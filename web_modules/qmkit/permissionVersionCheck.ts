/**
 * 权限版本检测工具
 * 用于检测后端返回的X-Permission-Version响应头，并在版本不一致时刷新页面
 */

class PermissionVersionCheck {
  // 防重复刷新的标志
  private isRefreshing = false;
  // 版本检测的防抖定时器
  private refreshTimer: any = null;
  // 防抖延迟时间（毫秒）
  private readonly debounceDelay = 1000;
  // 本地存储的key
  private readonly storageKey = 'permission-version';

  /**
   * 检查权限版本并处理刷新
   * @param newVersion - 从响应头获取的新版本号
   */
  checkPermissionVersionAndRefresh(newVersion: string | null): void {
    if (!newVersion) return;

    const localVersion = localStorage.getItem(this.storageKey);

    // 如果版本不同且当前没有在刷新中
    if (localVersion !== newVersion && !this.isRefreshing) {
      console.log(`检测到权限版本变化: ${localVersion} -> ${newVersion}`);

      // 设置刷新标志，防止重复刷新
      this.isRefreshing = true;

      // 更新本地存储的版本号
      localStorage.setItem(this.storageKey, newVersion);

      // 使用防抖机制，避免短时间内多次刷新
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
      }

      this.refreshTimer = setTimeout(() => {
        console.log('执行页面刷新');
        window.location.reload();
      }, this.debounceDelay);
    }
  }

  /**
   * 从响应头中提取权限版本信息
   * @param response - fetch响应对象
   */
  extractPermissionVersionFromResponse(response: Response): string | null {
    try {
      const version = response.headers.get('X-Permission-Version');
      return version;
    } catch (error) {
      console.warn('无法从响应头获取权限版本信息:', error);
      return null;
    }
  }

  /**
   * 初始化权限版本检测
   */
  init(): void {
    console.log('初始化权限版本检测');

    // 初始化本地版本号（如果不存在）
    if (!localStorage.getItem(this.storageKey)) {
      localStorage.setItem(this.storageKey, '');
    }
  }

  /**
   * 重置刷新状态（用于测试或手动重置）
   */
  resetRefreshState(): void {
    this.isRefreshing = false;
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * 获取当前权限版本信息
   */
  getCurrentVersion(): string {
    return localStorage.getItem(this.storageKey) || '';
  }

  /**
   * 手动设置权限版本（用于测试）
   */
  setVersion(version: string): void {
    localStorage.setItem(this.storageKey, version);
  }
}

// 创建单例实例
const permissionVersionCheck = new PermissionVersionCheck();

export default permissionVersionCheck;
