# web_modules/qmkit/fetch.ts 集成权限版本检测指南

## 修改步骤

### 1. 备份原文件
```bash
cp web_modules/qmkit/fetch.ts web_modules/qmkit/fetch.ts.backup
```

### 2. 在文件顶部添加导入
在 `web_modules/qmkit/fetch.ts` 文件的开头，在现有的导入语句之后添加：

```typescript
//ai生成
import permissionVersionCheck from '../../src/utils/permissionVersionCheck';
```

### 3. 在fetch函数中添加权限版本检测
找到这行代码：
```typescript
const res = await fetch(url, merge);
```

在这行代码之后添加：
```typescript
//ai生成
// 检查响应头中的权限版本信息
const newVersion = permissionVersionCheck.extractPermissionVersionFromResponse(res);
if (newVersion) {
  // 异步处理权限版本检查，不阻塞响应
  setTimeout(() => {
    permissionVersionCheck.checkPermissionVersionAndRefresh(newVersion);
  }, 0);
}
```

### 4. 在文件末尾添加初始化
在文件的最后，在 `trimValueDeep` 函数之后添加：

```typescript
//ai生成
// 初始化权限版本检测
permissionVersionCheck.init();
```

## 完整的修改示例

### 修改前的位置（第1行附近）：
```typescript
import 'whatwg-fetch';
import { message } from 'antd';
import * as _ from 'lodash';
import { util } from 'qmkit';
import { URL_PREFIX } from '@/services/config';
import Const from './config';
```

### 修改后：
```typescript
import 'whatwg-fetch';
import { message } from 'antd';
import * as _ from 'lodash';
import { util } from 'qmkit';
import { URL_PREFIX } from '@/services/config';
import Const from './config';

//ai生成
import permissionVersionCheck from '../../src/utils/permissionVersionCheck';
```

### 修改前的位置（fetch函数中）：
```typescript
const res = await fetch(url, merge);

const resJSON = await res.json();
```

### 修改后：
```typescript
const res = await fetch(url, merge);

//ai生成
// 检查响应头中的权限版本信息
const newVersion = permissionVersionCheck.extractPermissionVersionFromResponse(res);
if (newVersion) {
  // 异步处理权限版本检查，不阻塞响应
  setTimeout(() => {
    permissionVersionCheck.checkPermissionVersionAndRefresh(newVersion);
  }, 0);
}

const resJSON = await res.json();
```

### 修改前的位置（文件末尾）：
```typescript
function trimValueDeep(value) {
  return value && !_.isNumber(value) && !_.isBoolean(value) && !_.isDate(value)
    ? _.isString(value)
      ? _.trim(value)
      : _.isArray(value)
      ? _.map(value, trimValueDeep)
      : _.mapValues(value, trimValueDeep)
    : value;
}
```

### 修改后：
```typescript
function trimValueDeep(value) {
  return value && !_.isNumber(value) && !_.isBoolean(value) && !_.isDate(value)
    ? _.isString(value)
      ? _.trim(value)
      : _.isArray(value)
      ? _.map(value, trimValueDeep)
      : _.mapValues(value, trimValueDeep)
    : value;
}

//ai生成
// 初始化权限版本检测
permissionVersionCheck.init();
```

## 验证修改

修改完成后，您可以通过以下方式验证：

1. **检查控制台日志**：页面加载时应该看到 "初始化权限版本检测" 的日志
2. **测试版本变化**：在浏览器控制台中执行：
```javascript
import permissionVersionCheck from '@/utils/permissionVersionCheck';
permissionVersionCheck.setVersion('test-version-1');
permissionVersionCheck.checkPermissionVersionAndRefresh('test-version-2');
```

## 注意事项

1. **路径问题**：确保导入路径 `../../src/utils/permissionVersionCheck` 正确
2. **异步处理**：权限版本检测使用 `setTimeout` 异步处理，不会阻塞接口响应
3. **防重复刷新**：同时调用多个接口时，只会刷新一次页面
4. **防抖机制**：页面刷新有1秒延迟，避免频繁刷新

## 工作原理

1. 每次接口调用后，检查响应头中的 `X-Permission-Version`
2. 与本地存储的版本号比较
3. 如果版本不同且当前没有在刷新中，设置刷新标志
4. 1秒后执行页面刷新
5. 刷新标志防止重复刷新 