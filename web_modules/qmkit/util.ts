import { cache, history } from 'qmkit';

export function setLoginData(data: any) {
  localStorage.setItem(cache.LOGIN_DATA, JSON.stringify(data));
}

export function getLoginData() {
  return JSON.parse(localStorage.getItem(cache.LOGIN_DATA) || '{}');
}

export function isLogin() {
  let data = localStorage.getItem(cache.LOGIN_DATA);

  if (!data) {
    return false;
  }
  //解析数据
  // 全局保存token
  (window as any).token = JSON.parse(data).token;
  return (window as any).token != null;
}

/**
 * 清除缓存并跳转登录页
 */
export function logout(dispatch?: any) {
  localStorage.removeItem(cache.LOGIN_DATA);
  localStorage.removeItem(cache.LOGIN_FUNCTIONS);
  localStorage.removeItem(cache.MUNU_ACTIVE_INDEX);
  localStorage.removeItem(cache.FIRST_NOTICE);
  localStorage.removeItem(cache.LIVE_MONITORING_STATUS_DETAIL);
  localStorage.removeItem(cache.LIVE_MONITORING_STATUS_LIVE)
  localStorage.removeItem(cache.LOGIN_MENUS)
  localStorage.removeItem(cache.LOGIN_INFO)

  dispatch({
    type: 'SET_COLLAPSED',
    payload: { collapsed: false },
  });

  new Promise((resolved) => {
    dispatch({
      type: 'SET_ROUTE',
      payload: {
        value: [{
          pathName: 'BefriendsAI',
          isActive: true,
          path:"/friendSeek/chatMain"
        },
          { pathName: '首页', isActive: false, path: '/workbench' },
          ],
      },
    })

    resolved('')
  }).finally(() => {
    sessionStorage.removeItem(cache.ROUTE_VALUE)
  });

  history.push('/login');
}

// 当前菜单
export function setCurrentMenu(path: string) {
  localStorage.setItem(cache.MUNU_ACTIVE_INDEX, path);
}

export function getCurrentMenu() {
  return localStorage.getItem(cache.MUNU_ACTIVE_INDEX) || '';
}

// 当前菜单key
export function setKeyMenu(openKeys: string[]) {
  localStorage.setItem(cache.MUNU_KEY, JSON.stringify(openKeys));
}

export function getKeyMenu() {
  return localStorage.getItem(cache.MUNU_KEY) ? JSON.parse(localStorage.getItem(cache.MUNU_KEY)) : ['0'];
}

// 权限
export function setLocalFunctions(data: string[]) {
  localStorage.setItem(cache.LOGIN_FUNCTIONS, JSON.stringify(data));
}

export function getLocalFunctions() {
  return JSON.parse(localStorage.getItem(cache.LOGIN_FUNCTIONS) || '[]');
}
export function div(num1, num2) {
  const r1 = decimalLength(num1);
  const r2 = decimalLength(num2);

  const max = Math.max(r1, r2);

  const n1 = suffixInteger(num1, max);
  const n2 = suffixInteger(num2, max);

  return n1 / n2;
}
/**
 * 小数点后补齐0作为整数
 * <AUTHOR>
 * @param  {Number} num    数字
 * @param  {Number} length 补齐的长度
 * @return {Number}        整数
 */
function suffixInteger(num, length) {
  let str = num.toString();
  const decimalLen = decimalLength(num);
  str += Math.pow(10, length - decimalLen)
    .toString()
    .substr(1);
  return Number(str.replace('.', ''));
}
/**
 * 获取小数点后数字长度
 * <AUTHOR>
 * @param  {Number} num 数字
 * @return {Number}     长度
 */
function decimalLength(num) {
  const str = num.toString();
  const index = str.indexOf('.');
  return index == -1 ? 0 : str.substr(index + 1).length;
}

