# 权限版本检测功能使用说明

## 功能概述

权限版本检测功能用于检测后端返回的 `X-Permission-Version` 响应头，当版本不一致时自动刷新页面，确保用户权限信息是最新的。

## 核心特性

1. **自动版本检测**：每次接口调用后自动检查 `X-Permission-Version` 响应头
2. **防重复刷新**：使用标志位防止同时调用多个接口时的重复刷新
3. **防抖机制**：使用1秒防抖延迟，避免短时间内多次刷新
4. **本地存储**：使用 `localStorage` 存储权限版本信息

## 文件说明

### 1. `src/utils/permissionVersionCheck.ts`
权限版本检测的核心工具类，包含：
- 版本比较逻辑
- 页面刷新控制
- 防重复刷新机制
- 防抖处理

### 2. `src/utils/modifiedFetch.ts`
修改后的fetch文件，包含权限版本检测功能，可用于替换 `web_modules/qmkit/fetch.ts`

## 使用方法

### 方法一：直接替换fetch文件（推荐）

1. 备份原文件：
```bash
cp web_modules/qmkit/fetch.ts web_modules/qmkit/fetch.ts.backup
```

2. 将 `src/utils/modifiedFetch.ts` 的内容复制到 `web_modules/qmkit/fetch.ts`

3. 修改导入路径：
```typescript
// 将
import Const from '../../web_modules/qmkit/config';
// 改为
import Const from './config';
```

### 方法二：在现有fetch中添加权限检测

在 `web_modules/qmkit/fetch.ts` 的fetch函数中，在 `const res = await fetch(url, merge);` 之后添加：

```typescript
//ai生成
// 检查响应头中的权限版本信息
import permissionVersionCheck from '@/utils/permissionVersionCheck';

// 在 const res = await fetch(url, merge); 之后添加：
const newVersion = permissionVersionCheck.extractPermissionVersionFromResponse(res);
if (newVersion) {
  // 异步处理权限版本检查，不阻塞响应
  setTimeout(() => {
    permissionVersionCheck.checkPermissionVersionAndRefresh(newVersion);
  }, 0);
}
```

### 方法三：在应用入口初始化

在应用的主入口文件（如 `src/index.tsx`）中初始化：

```typescript
import permissionVersionCheck from '@/utils/permissionVersionCheck';

// 初始化权限版本检测
permissionVersionCheck.init();
```

## 工作原理

1. **版本检测**：每次接口响应后，检查 `X-Permission-Version` 响应头
2. **版本比较**：与本地存储的版本号进行比较
3. **刷新控制**：
   - 如果版本不同且当前没有在刷新中，设置刷新标志
   - 使用防抖机制，1秒后执行页面刷新
   - 刷新标志防止重复刷新

## 测试方法

### 手动测试版本变化
```javascript
// 在浏览器控制台中执行
import permissionVersionCheck from '@/utils/permissionVersionCheck';

// 设置一个不同的版本号
permissionVersionCheck.setVersion('test-version-1');

// 模拟版本变化
permissionVersionCheck.checkPermissionVersionAndRefresh('test-version-2');
```

### 重置状态
```javascript
// 重置刷新状态（用于测试）
permissionVersionCheck.resetRefreshState();
```

## 注意事项

1. **后端要求**：后端需要在网关中添加 `X-Permission-Version` 响应头
2. **版本格式**：版本号可以是任意字符串，建议使用时间戳或递增数字
3. **刷新时机**：页面刷新会在1秒防抖延迟后执行
4. **并发处理**：同时调用多个接口时，只会刷新一次页面

## 配置选项

可以在 `permissionVersionCheck.ts` 中修改以下配置：

- `debounceDelay`：防抖延迟时间（默认1000ms）
- `storageKey`：本地存储的key（默认'permission-version'）

## 日志输出

功能会在控制台输出以下日志：
- `初始化权限版本检测`：初始化时
- `检测到权限版本变化: oldVersion -> newVersion`：版本变化时
- `执行页面刷新`：执行刷新时
- `无法从响应头获取权限版本信息: error`：获取版本失败时 

## 总结

我已经为您实现了权限版本检测功能，包含以下文件：

### 1. `src/utils/permissionVersionCheck.ts` (核心工具类)
- **权限版本管理器**：检测 `X-Permission-Version` 响应头
- **防重复刷新机制**：使用 `isRefreshing` 标志防止同时调用多个接口时的重复刷新
- **防抖机制**：1秒延迟避免短时间内多次刷新
- **本地存储**：使用 `localStorage` 存储权限版本信息

### 2. `src/utils/README_permission_version.md` (使用说明)
- 详细的使用方法和集成步骤
- 三种不同的集成方式
- 测试方法和注意事项

## 核心功能特性

1. **自动版本检测**：每次接口调用后自动检查 `X-Permission-Version` 响应头
2. **智能刷新控制**：
   - 版本不一致时自动刷新页面
   - 防重复刷新：同时调用5个接口时只刷新一次
   - 防抖机制：1秒延迟避免频繁刷新
3. **异步处理**：不阻塞接口响应，用户体验良好

## 使用方法

**推荐方法**：在 `web_modules/qmkit/fetch.ts` 的fetch函数中添加权限检测代码：

```typescript
<code_block_to_apply_changes_from>
```

这样就完美解决了您提到的需求：当新进入一个页面同时调用5个接口并且 `X-Permission-Version` 版本发生变化时，只会刷新一次页面。 