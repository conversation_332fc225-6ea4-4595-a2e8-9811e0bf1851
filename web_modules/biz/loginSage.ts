import { LoginReulst } from '@/services/login';
import { getFunctions } from '@/services/user';
import { history, util, cache } from 'qmkit';
import { setLocalFunctions } from 'qmkit/util';

export default async (loginData: LoginReulst) => {
  util.setLoginData(loginData);

  window.token = loginData?.token;

  // 获取权限
  await getFunctions().then((response) => {
    const { res } = response;
    if (res?.success) {
      const functions = (res?.result || []).filter((item) => item?.authNm);
      setLocalFunctions(functions.map((item) => item?.authNm));
    }
  });
  const loaction =  JSON.parse(localStorage.getItem(cache.LOGIN_BACK) || '{}')
  if(loaction.pathname) {
    window.location.href = `${window.location.origin}${loaction.pathname}${loaction.search}`;
    localStorage.removeItem(cache.LOGIN_BACK)
    return
  } 
  const pathnameList=['/platform-supplier-newGoods', '/business-leads-open']
  if(!pathnameList.includes(window.location.pathname)){
    history.push('/')
  } 
};
