export enum PLATFORM_ENUM {
  DY = 'DY',
  TB = 'TB',
  TAOBAO = 'TAOBAO',
  WECHAT_VIDEO = 'WECHAT_VIDEO',
  BAIDU  = 'BAIDU',
  JD = 'JD',
  KS  = 'KS',
  RED = 'RED',
  ALL = 'ALL'
}

export const plantformList = [
  {
    label: '抖音',
    value: PLATFORM_ENUM.DY
  },
  {
    label: '淘宝',
    value: PLATFORM_ENUM.TB
  },
  // {
  //   label:'视频号',
  //   value: PLATFORM_ENUM.WECHAT_VIDEO
  // },
  // {
  //   label:'百度',
  //   value: PLATFORM_ENUM.BAIDU
  // },
  // {
  //   label:'京东',
  //   value: PLATFORM_ENUM.JD
  // },
  // {
  //   label:'快手',
  //   value: PLATFORM_ENUM.KS
  // },
  // {
  //   label:'小红书',
  //   value: PLATFORM_ENUM.RED
  // },
]

export const PlantformName = {
  [PLATFORM_ENUM.DY]: '抖音',
  [PLATFORM_ENUM.TB]: '淘宝',
  [PLATFORM_ENUM.TAOBAO]: '淘宝',
  [PLATFORM_ENUM.WECHAT_VIDEO]: '视频号',
  [PLATFORM_ENUM.BAIDU]: '百度',
  [PLATFORM_ENUM.JD]: '京东',
  [PLATFORM_ENUM.KS]: '快手',
  [PLATFORM_ENUM.RED]: '小红书',
  [PLATFORM_ENUM.ALL]: '全部',
}

export type PLATFORM_ENUM_KEY = keyof typeof PLATFORM_ENUM

export enum GOODS_SOURCE {
  HOME_BRED = 'HOME_BRED',
  IMPORT = 'IMPORT',
  CROSS_BORDER = 'CROSS_BORDER',
}

export const GOODS_SOURCE_NAME = {
  [GOODS_SOURCE.CROSS_BORDER]: '跨境',
  [GOODS_SOURCE.IMPORT]: '进口',
  [GOODS_SOURCE.HOME_BRED]: '国产',
}

export const GOODS_SOURCE_LIST = [
  {
    value: GOODS_SOURCE.HOME_BRED,
    label: GOODS_SOURCE_NAME[GOODS_SOURCE.HOME_BRED]
  },
  {
    value: GOODS_SOURCE.IMPORT,
    label: GOODS_SOURCE_NAME[GOODS_SOURCE.IMPORT]
  },
  {
    value: GOODS_SOURCE.CROSS_BORDER,
    label: GOODS_SOURCE_NAME[GOODS_SOURCE.CROSS_BORDER]
  },
]

export enum PROCESS_STATUS {
  BP_CONFIRMING = 'BP_CONFIRMING',
  WAIT_AUDIT = 'WAIT_AUDIT',
  WAIT_LIVE = 'WAIT_LIVE',
  ABORT_LIVE = 'ABORT_LIVE',
  ABORT_WAIT_LIVE = 'ABORT_WAIT_LIVE',
  COMPLETED_LIVE = 'COMPLETED_LIVE',
  CANCEL = 'CANCEL',
  INVITING = 'INVITING',
  LOSE_EFFICACY = 'LOSE_EFFICACY',
  TB_ORDERED = 'TB_ORDERED',
  // 链接确认状态
  WAIT_CHECK = 'WAIT_CHECK',
  NO_PROMOTION = 'NO_PROMOTION',
  CAN_PROMOTION = 'CAN_PROMOTION'
}

export const PROCESS_STATUS_NAME = {
  [PROCESS_STATUS.BP_CONFIRMING]: '待商务确认',
  [PROCESS_STATUS.WAIT_AUDIT]: '审核中',
  [PROCESS_STATUS.WAIT_LIVE]: '待直播',
  [PROCESS_STATUS.ABORT_LIVE]: '已掉品',
  [PROCESS_STATUS.ABORT_WAIT_LIVE]: '已掉品',
  [PROCESS_STATUS.COMPLETED_LIVE]: '已播',
  [PROCESS_STATUS.CANCEL]: '已取消',
  [PROCESS_STATUS.INVITING]: '邀约中',
  [PROCESS_STATUS.LOSE_EFFICACY]: '已失效',
  [PROCESS_STATUS.TB_ORDERED]: '定品',
  // 链接确认状态
  [PROCESS_STATUS.WAIT_CHECK]: '待确认',
  [PROCESS_STATUS.NO_PROMOTION]: '不可上播',
  [PROCESS_STATUS.CAN_PROMOTION]: '可上播',
};

export const PROCESS_STATUS_COLOR = {
  [PROCESS_STATUS.BP_CONFIRMING]: 'orange',
  [PROCESS_STATUS.WAIT_AUDIT]: 'blue',
  [PROCESS_STATUS.WAIT_LIVE]: 'green',
  [PROCESS_STATUS.ABORT_LIVE]: 'red',
  [PROCESS_STATUS.ABORT_WAIT_LIVE]: 'red',
  [PROCESS_STATUS.COMPLETED_LIVE]: 'purple',
  [PROCESS_STATUS.CANCEL]: 'red',
  [PROCESS_STATUS.INVITING]: 'orange',
  [PROCESS_STATUS.LOSE_EFFICACY]: 'black',
  [PROCESS_STATUS.TB_ORDERED]: 'green',
  // 链接确认状态
  [PROCESS_STATUS.WAIT_CHECK]: 'orange',
  [PROCESS_STATUS.NO_PROMOTION]: 'red',
  [PROCESS_STATUS.CAN_PROMOTION]: 'green',
}

export const PROCESS_STATUS_LIST = [
  {
  value: PROCESS_STATUS.BP_CONFIRMING,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.BP_CONFIRMING],
 },
  {
  value: PROCESS_STATUS.WAIT_AUDIT,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.WAIT_AUDIT],
 },
  {
  value: PROCESS_STATUS.WAIT_LIVE,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.WAIT_LIVE],
 },
  {
  value: PROCESS_STATUS.ABORT_LIVE,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.ABORT_LIVE],
 },
  {
  value: PROCESS_STATUS.COMPLETED_LIVE,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.COMPLETED_LIVE],
 },
  {
  value: PROCESS_STATUS.CANCEL,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.CANCEL],
 },
  {
  value: PROCESS_STATUS.INVITING,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.INVITING],
 },
  {
  value: PROCESS_STATUS.LOSE_EFFICACY,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.LOSE_EFFICACY],
 },
  {
  value: PROCESS_STATUS.TB_ORDERED,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.TB_ORDERED],
 },
];

export enum LEVEL {
  LOW = 'LOW',
  MIDDLe = 'MIDDLe',// 之前写错了  后面如果没有用到在删除掉
  MIDDLE = 'MIDDLE',
  HIGH = 'HIGH',
  PASS = 'PASS',
  AUTO_PASS = 'AUTO_PASS',
  SKIP = 'SKIP',
  HIGH_SPECIAL = 'HIGH_SPECIAL',
  NONE = "NONE"
}

export const LEVEL_NAME = {
  [LEVEL.PASS]: '通过',
  [LEVEL.LOW]: '低风险',
  [LEVEL.MIDDLE]: '中风险',
  [LEVEL.MIDDLe]: '中风险', // 之前写错了  后面如果没有用到在删除掉
  [LEVEL.HIGH]: '高风险',
  [LEVEL.AUTO_PASS]: '自动过审',
  [LEVEL.SKIP]: '跳过',
  [LEVEL.HIGH_SPECIAL]: '高风险-特',
  [LEVEL.NONE]: '待审核',
};

export const LEVEL_COLOR = {
  [LEVEL.NONE]: 'rgba(248, 122, 58, 1)',
  [LEVEL.HIGH]: 'rgba(255, 65, 46, 1)',
  [LEVEL.MIDDLE]: 'rgba(38, 194, 78, 1)',
  [LEVEL.MIDDLe]: 'rgba(38, 194, 78, 1)',// 之前写错了  后面如果没有用到在删除掉
  [LEVEL.HIGH_SPECIAL]: 'rgba(38, 194, 78, 1)',
  [LEVEL.LOW]: 'rgba(38, 194, 78, 1)',
  [LEVEL.PASS]: 'rgba(38, 194, 78, 1)',
  [LEVEL.AUTO_PASS]: 'rgba(38, 194, 78, 1)',
};

export enum STATUS_PERSON {
  INIT = 'INIT',
  PASS = 'PASS',
  AUTO_PASS = 'AUTO_PASS',
  REJECT = 'REJECT',
  SKIP = 'SKIP',
}

export const STATUS_PERSON_LIST = {
  [STATUS_PERSON.INIT]: '待审核',
  [STATUS_PERSON.PASS]: '通过',
  [STATUS_PERSON.AUTO_PASS]: '自动过审',
  [STATUS_PERSON.REJECT]: '驳回',
  [STATUS_PERSON.SKIP]: '跳过',
};

export const plantformListAll = [
  ...plantformList, 
  {
    label: '视频号',
    value: PLATFORM_ENUM.WECHAT_VIDEO
  },
  {
    label: '百度',
    value: PLATFORM_ENUM.BAIDU
  },
  {
    label: '京东',
    value: PLATFORM_ENUM.JD
  },
  {
    label: '快手',
    value: PLATFORM_ENUM.KS
  },
  {
    label: '小红书',
    value: PLATFORM_ENUM.RED
  },
]

export const OmitJDPlantformList = [
   {
    label: '抖音',
    value: PLATFORM_ENUM.DY
  },
  {
    label: '淘宝',
    value: PLATFORM_ENUM.TB
  },
  {
    label: '视频号',
    value: PLATFORM_ENUM.WECHAT_VIDEO
  },
  {
    label: '百度',
    value: PLATFORM_ENUM.BAIDU
  },
  {
    label: '京东',
    value: PLATFORM_ENUM.JD
  },
  {
    label: '快手',
    value: PLATFORM_ENUM.KS
  },
  {
    label: '小红书',
    value: PLATFORM_ENUM.RED
  },
]

export const OnlyTBPlantformList = [
  {
    label: '淘宝',
    value: PLATFORM_ENUM.TB
  },
]

export const OPERATION_STATIS_LIST = [
  {
  value: PROCESS_STATUS.WAIT_AUDIT,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.WAIT_AUDIT],
 },
  {
  value: PROCESS_STATUS.WAIT_LIVE,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.WAIT_LIVE],
 },
  {
  value: PROCESS_STATUS.ABORT_LIVE,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.ABORT_LIVE],
 },
  {
  value: PROCESS_STATUS.COMPLETED_LIVE,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.COMPLETED_LIVE],
 },
  {
  value: PROCESS_STATUS.LOSE_EFFICACY,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.LOSE_EFFICACY],
 },
  {
  value: PROCESS_STATUS.TB_ORDERED,
  label: PROCESS_STATUS_NAME[PROCESS_STATUS.TB_ORDERED],
 },
]

export enum QUALIFICATION_REPLENISHMENT_STATUS {
  'SUPPLEMENT_WAIT_AUDIT'="SUPPLEMENT_WAIT_AUDIT"
}

export const QUALIFICATION_REPLENISHMENT_STATUS_NAME = {
  [QUALIFICATION_REPLENISHMENT_STATUS.SUPPLEMENT_WAIT_AUDIT]: '补充待审核'
}

export const QUALIFICATION_REPLENISHMENT_STATUS_LIST = [
{
    value: QUALIFICATION_REPLENISHMENT_STATUS.SUPPLEMENT_WAIT_AUDIT,
    label: QUALIFICATION_REPLENISHMENT_STATUS_NAME[QUALIFICATION_REPLENISHMENT_STATUS.SUPPLEMENT_WAIT_AUDIT]
  }
]

export enum UALITY_INSPECTION_STATUS_ENUM {
  NO_NEED = 'NEEDLESS_INSPECTION',
  NO_INSPECTION = 'NON_INSPECTION',
  INSPECTIONING = "INSPECTING",
  INSPECTION_PASS = 'PASSED_INSPECTION',
  INSPECTION_FAIL = 'FAILED_INSPECTION',
}

export const UALITY_INSPECTION_STATUS_NAME = {
  [UALITY_INSPECTION_STATUS_ENUM.NO_NEED]: '无需质检',
  [UALITY_INSPECTION_STATUS_ENUM.NO_INSPECTION]: '需质检',
  [UALITY_INSPECTION_STATUS_ENUM.INSPECTIONING]: '待质检',
  [UALITY_INSPECTION_STATUS_ENUM.INSPECTION_PASS]: '质检通过',
  [UALITY_INSPECTION_STATUS_ENUM.INSPECTION_FAIL]: '质检不通过',
}

export const UALITY_INSPECTION_STATUS_LIST = [
  {
    value: UALITY_INSPECTION_STATUS_ENUM.NO_NEED,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.NO_NEED]
  },
  {
    value: UALITY_INSPECTION_STATUS_ENUM.NO_INSPECTION,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.NO_INSPECTION]
  },
  {
    value: UALITY_INSPECTION_STATUS_ENUM.INSPECTIONING,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.INSPECTIONING]
  },
  {
    value: UALITY_INSPECTION_STATUS_ENUM.INSPECTION_PASS,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.INSPECTION_PASS]
  },
  {
    value: UALITY_INSPECTION_STATUS_ENUM.INSPECTION_FAIL,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.INSPECTION_FAIL]
  },
]

export const UALITY_INSPECTION_STATUS_PART_LIST = [
  {
    value: UALITY_INSPECTION_STATUS_ENUM.INSPECTIONING,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.INSPECTIONING]
  },
  {
    value: UALITY_INSPECTION_STATUS_ENUM.INSPECTION_PASS,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.INSPECTION_PASS]
  },
  {
    value: UALITY_INSPECTION_STATUS_ENUM.INSPECTION_FAIL,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.INSPECTION_FAIL]
  },
  {
    value: UALITY_INSPECTION_STATUS_ENUM.NO_NEED,
    label: UALITY_INSPECTION_STATUS_NAME[UALITY_INSPECTION_STATUS_ENUM.NO_NEED]
  },
]

export const UALITY_INSPECTION_STATUS_COLOR = {
  [UALITY_INSPECTION_STATUS_ENUM.NO_NEED]: 'blue',
  [UALITY_INSPECTION_STATUS_ENUM.NO_INSPECTION]: '#FAAD14',
  [UALITY_INSPECTION_STATUS_ENUM.INSPECTIONING]: 'blue',
  [UALITY_INSPECTION_STATUS_ENUM.INSPECTION_PASS]: 'green',
  [UALITY_INSPECTION_STATUS_ENUM.INSPECTION_FAIL]: 'red',
}

export const plantformTBList = [
  {
    label: '抖音',
    value: PLATFORM_ENUM.DY
  },
  {
    label:'京东',
    value: PLATFORM_ENUM.JD
  },
  {
    label: '淘宝',
    value: PLATFORM_ENUM.TAOBAO
  }
]
