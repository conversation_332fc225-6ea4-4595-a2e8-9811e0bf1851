import { IRoute } from '../router';

export const businessCRM: IRoute[] = [
  {
    path: '/opportunity-management',
    name: '商机管理',
    asyncComponent: () => import('../pages/opportunity-management'),
  },
  {
    path: '/opportunity-management-form',
    name: '新增商机',
    asyncComponent: () => import('../pages/opportunity-management/FormPage'),
  },
  {
    path: '/opportunity-management-edit',
    name: '编辑商机',
    asyncComponent: () => import('../pages/opportunity-management/FormPage'),
  },
  {
    path: '/opportunity-management-detail',
    name: '商机详情',
    asyncComponent: () => import('../pages/opportunity-management/DetailPage'),
  },
  {
    path: '/business-leads',
    name: '商机线索',
    asyncComponent: () => import('../pages/business-leads'),
  },
  {
    path: '/business-leads-add',
    name: '新增商机线索',
    asyncComponent: () => import('../pages/business-leads/FormPage'),
  },
  {
    path: '/business-leads-edit',
    name: '编辑商机线索',
    asyncComponent: () => import('../pages/business-leads/FormPage'),
  },
  {
    path: '/business-leads-detail',
    name: '商机线索详情',
    asyncComponent: () => import('../pages/business-leads/DetailPage'),
  },
];
