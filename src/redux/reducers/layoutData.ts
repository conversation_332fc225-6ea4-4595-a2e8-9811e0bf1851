import { isArray } from 'lodash';
import { handleActions } from 'redux-actions';

const defaultValue = {
  collapsed: false,
};

const routeDefaultValue = {
  routePath: [
    // pathName就是路由的name属性，直接改router.ts文件中的路由name属性就行
    { pathName: 'BefriendsAI', isActive: true, path: '/friendSeek/chatMain' },
    { pathName: '首页', isActive: false, path: '/workbench' },
  ],
};

const keepAliveListDefatulValue = {
  keepAliveList: [],
};
export const setCollapsed = handleActions(
  {
    SET_COLLAPSED: (state: any, { payload: { collapsed } }: any) => {
      return { collapsed };
    },
  },
  defaultValue,
);

export const setRoute = handleActions(
  {
    SET_ROUTE: (state: any, { payload: { value } }: any) => {
      let newRouteArr = [];
      if (isArray(value)) {
        newRouteArr = value;
      } else {
        newRouteArr = [...state.routePath, value];
      }
      sessionStorage.setItem('routeValue', JSON.stringify(newRouteArr));

      return { routePath: newRouteArr };
    },
  },
  routeDefaultValue,
);

export const setKeepAliveList = handleActions(
  {
    SET_KEEP_ALIVE_LIST: (state: any, { payload: { keepAliveList } }: any) => {
      return { keepAliveList };
    },
  },
  keepAliveListDefatulValue,
);
