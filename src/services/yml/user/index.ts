import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type UpdateDataRoleApiRequest = {
  description?: string /*数据角色描述*/;
  id?: string /*数据权限id*/;
  name?: string /*数据角色名称*/;
  seeAnchorBuIdScopeList?: Array<string> /*主播管理事业部数据权限*/;
  seeAnchorScope?: number /*主播管理数据权限：0=全部，1=事业部，2=本人所负责的*/;
  seeBUList?: Array<string> /*查看事业部范围，ALL 表示全部，没有查看权限则返回空字符串，如果有具体的列表则返回具体的名字列表*/;
  seeBuIdList?: Array<string> /*交个朋友端生效, 查看事业部id范围，没有查看权限则返回空字符串，如果有具体的列表则返回具体的id列表*/;
  seeBusinessOpportunityScope?: number /*查看商机数据权限范围，0：全部；1:查看所属事业部；2：本人及下属；3：本人；4：商务助理*/;
  seeCategoryList?: Array<number> /*可以查看的行业大类列表，[-111] 表示没有类目选中，[]表示全选 [xxx,yyy]表示特定类目选中*/;
  seeCommissionScope?: number /*查看佣金权限范围，0：全部；1：本人及下属；2：本人；3：商务助理*/;
  seeCommodityRelatedScope?: number /*查看商品相关数据范围，0全部，1本人及下属，2本人*/;
  seeContact?: number /*能否查看联系方式，0禁止，1允许*/;
  seeCooperationGuaranteedScope?: number /*查看保量合同数据权限范围，0：全部；1：本人及下属；2：本人；3：商务助理*/;
  seeFinanceScope?: number /*只有业财端生效，0全部，1本人及下属，2本人 */;
  seeFinancialContractsScope?: number /*交个朋友端生效, 支出合同相关数据权限范围 0=全部，1=查看部门及下属部门所负责的，2=查看个人*/;
  seeFinancialSettlementScope?: number /*交个朋友端生效, 财务结算相关数据权限范围，0：全部；1：仅本人；2：事业部；*/;
  seeLiveRoomList?: Array<string> /*查看直播间范围，ALL 表示全部，没有查看权限则返回空字符串，如果有具体的列表则返回具体的名字列表*/;
  seeMarketingDataBuIdList?: Array<string> /*交个朋友端生效, 查看营销数据事业部id范围，没有查看权限则返回空，如果有具体的列表则返回具体的id列表*/;
  seeMarketingDataScope?: number /*交个朋友端生效, 营销相关数据权限范围，0：全部；1：仅本人；2：事业部；*/;
  seePhone?: number /*能否查看手机号，0禁止，1允许*/;
  seeProviderContact?: number /*能否查看合作方联系方式，0禁止，1允许*/;
  seeProviderScope?: number /*只有平台端生效,查看合作方范围，0全部，1本人及下属，2本人 */;
  seeSupplierContact?: number /*能否查看商家联系方式，0禁止，1允许*/;
  seeSupplierScope?: number /*只有平台端生效,查看商家范围，0全部，1本人及下属，2本人 */;
  seeTalentContact?: number /*能否查看达人联系方式，0禁止，1允许*/;
  seeTalentScope?: number /*只有平台端生效,查看达人范围，0全部，1本人及下属，2本人 */;
  seeWarehouseIdList?: Array<string> /*仓库数据权限：可查看的仓库ID集合*/;
  seeWorkScope?: number /*查看范围，0全部，1本人及下属，2本人 */;
  status?: number /*目标状态, 0启用， 1禁用*/;
  trafficConfiguration?: number /*流量投放配置数据权限：0=全部，1=事业部，2=本人所负责的*/;
  trafficConfigurationBuIds?: Array<string> /*流量投放配置事业部数据权限*/;
};

export type UpdateDataRoleApiResult = string;

/**
 *更新数据角色
 */
export const updateDataRoleApi = (params: UpdateDataRoleApiRequest) => {
  return Fetch<ResponseWithResult<UpdateDataRoleApiResult>>('/user/public/dataRole/update', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/user/public/dataRole/update') },
  });
};

export type GetDataRoleByIdRequest = {
  id?: string /*数据权限id*/;
};

export type GetDataRoleByIdResult = {
  companyInfoId?: string;
  createPerson?: string;
  createTime?: string;
  delFlag?: number;
  deletePerson?: string;
  deleteTime?: string;
  description?: string;
  id?: string;
  name?: string;
  seeAnchorBuIdScopeList?: Array<string> /*主播管理事业部数据权限*/;
  seeAnchorScope?: number /*主播管理数据权限：0=全部，1=事业部，2=本人所负责的*/;
  seeBUList?: Array<string>;
  seeBuIdList?: Array<string> /*交个朋友端生效, 查看事业部id范围，没有查看权限则返回空字符串，如果有具体的列表则返回具体的id列表*/;
  seeBusinessOpportunityScope?: number /*查看商机数据权限范围，0：全部；1:查看所属事业部；2：本人及下属；3：本人；4：商务助理*/;
  seeCategory?: string;
  seeCategoryList?: Array<string>;
  seeCommissionScope?: number;
  seeCommodityRelatedScope?: number;
  seeContact?: number;
  seeCooperationGuaranteedScope?: number;
  seeFinanceScope?: number;
  seeFinancialContractsScope?: number /*交个朋友端生效, 支出合同相关数据权限范围 0=全部，1=查看部门及下属部门所负责的，2=查看个人*/;
  seeFinancialSettlementScope?: number /*交个朋友端生效, 财务结算相关数据权限范围，0：全部；1：仅本人；2：事业部；*/;
  seeLiveRoomList?: Array<string>;
  seeMarketingDataBuIdList?: Array<string> /*交个朋友端生效, 查看营销数据事业部id范围，没有查看权限则返回空，如果有具体的列表则返回具体的id列表*/;
  seeMarketingDataScope?: number /*交个朋友端生效, 营销相关数据权限范围，0：全部；1：仅本人；2：事业部；*/;
  seePhone?: number;
  seeProviderContact?: number;
  seeProviderScope?: number;
  seeSupplierContact?: number;
  seeSupplierScope?: number;
  seeTalentContact?: number;
  seeTalentScope?: number;
  seeWarehouseIdList?: Array<string>;
  seeWorkScope?: number;
  status?: number;
  tenantId?: string;
  trafficConfiguration?: number /*流量投放配置数据权限：0=全部，1=事业部，2=本人所负责的*/;
  trafficConfigurationBuIds?: Array<string> /*流量投放配置事业部数据权限*/;
  updatePerson?: string;
  updateTime?: string;
};

/**
 *数据角色详情
 */
export const getDataRoleById = (params: GetDataRoleByIdRequest) => {
  return Fetch<ResponseWithResult<GetDataRoleByIdResult>>('/user/public/dataRole/getById', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/user/public/dataRole/getById') },
  });
};

export type GetLiveRoomRequest = any;

export type GetLiveRoomResult = {
  [key: string]: Array<{
    avatar?: string /*头像*/;
    brandFeeTechRate?: string /*品牌费分佣比例, 0.01表示1%*/;
    buId?: string /*事业部id*/;
    buName?: string /*事业部名称*/;
    buyinId?: string /*百应ID*/;
    canZeroCommission?: 'NO' | 'YES' /*是否允许0佣金, 0 否，1 是[YesOrNoEnum]*/;
    commissionTechRate?: string /*佣金率分佣比例, 0.01表示1%*/;
    cooperationMode?:
      | 'COLONEL'
      | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
    customCommissionRate?: 'NO' | 'YES' /*自定义分佣比例：0 否，1 是[YesOrNoEnum]*/;
    fansNum?: string /*粉丝数量*/;
    id?: string /*id*/;
    institutionId?: string /*达人所属机构ID*/;
    isPushMdy?: boolean /*是否推送明道云*/;
    name?: string /*直播间名称*/;
    openId?: string /*直播间openId*/;
    platform?: string /*平台*/;
    platformId?: string /*平台id*/;
    priceProtection?: 'NO' | 'YES' /*价格保护：0 否，1 是[YesOrNoEnum]*/;
    sortingNum?: number /*排序权重*/;
    talentId?: string /*达人id*/;
    talentNo?: string /*达人编号*/;
  }>;
};

/**
 *获取所有直播间列表
 */
export const getLiveRoom = (params: GetLiveRoomRequest) => {
  return Fetch<ResponseWithResult<GetLiveRoomResult>>('/user/open/dataRole/listLiveRoom', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/user/open/dataRole/listLiveRoom') },
  });
};

export type AddDataRoleApiRequest = {
  description?: string /*数据角色描述*/;
  name?: string /*数据角色名称*/;
  seeAnchorBuIdScopeList?: Array<string> /*主播管理事业部数据权限*/;
  seeAnchorScope?: number /*主播管理数据权限：0=全部，1=事业部，2=本人所负责的*/;
  seeBUList?: Array<string> /*查看事业部范围，ALL 表示全部，没有查看权限则返回空字符串，如果有具体的列表则返回具体的名字列表*/;
  seeBuIdList?: Array<string> /*交个朋友端生效, 查看事业部id范围，没有查看权限则返回空字符串，如果有具体的列表则返回具体的id列表*/;
  seeBusinessOpportunityScope?: number /*查看商机数据权限范围，0：全部；1:查看所属事业部；2：本人及下属；3：本人；4：商务助理*/;
  seeCategoryList?: Array<number> /*可以查看的行业大类列表，[-111] 表示没有类目选中，[]表示全选 [xxx,yyy]表示特定类目选中*/;
  seeCommissionScope?: number /*查看佣金权限范围，0：全部；1：本人及下属；2：本人；3：商务助理*/;
  seeCommodityRelatedScope?: number /*查看商品相关数据范围，0全部，1本人及下属，2本人*/;
  seeContact?: number /*能否查看联系方式，0禁止，1允许*/;
  seeCooperationGuaranteedScope?: number /*查看保量合同数据权限范围，0：全部；1：本人及下属；2：本人；3：商务助理*/;
  seeFinanceScope?: number /*只有业财端生效，0全部，1本人及下属，2本人 */;
  seeFinancialContractsScope?: number /*交个朋友端生效, 支出合同相关数据权限范围 0=全部，1=查看部门及下属部门所负责的，2=查看个人*/;
  seeFinancialSettlementScope?: number /*交个朋友端生效, 财务结算相关数据权限范围，0：全部；1：仅本人；2：事业部；*/;
  seeLiveRoomList?: Array<string> /*查看直播间范围，ALL 表示全部，没有查看权限则返回空字符串，如果有具体的列表则返回具体的名字列表*/;
  seeMarketingDataBuIdList?: Array<string> /*交个朋友端生效, 查看营销数据事业部id范围，没有查看权限则返回空，如果有具体的列表则返回具体的id列表*/;
  seeMarketingDataScope?: number /*交个朋友端生效, 营销相关数据权限范围，0：全部；1：仅本人；2：事业部；*/;
  seePhone?: number /*能否查看手机号，0禁止，1允许*/;
  seeProviderContact?: number /*能否查看合作方联系方式，0禁止，1允许*/;
  seeProviderScope?: number /*只有平台端生效,查看合作方范围，0全部，1本人及下属，2本人 */;
  seeSupplierContact?: number /*能否查看商家联系方式，0禁止，1允许*/;
  seeSupplierScope?: number /*只有平台端生效,查看商家范围，0全部，1本人及下属，2本人 */;
  seeTalentContact?: number /*能否查看达人联系方式，0禁止，1允许*/;
  seeTalentScope?: number /*只有平台端生效,查看达人范围，0全部，1本人及下属，2本人 */;
  seeWarehouseIdList?: Array<string> /*仓库数据权限：可查看的仓库ID集合*/;
  seeWorkScope?: number /*查看范围，0全部，1本人及下属，2本人 */;
  trafficConfiguration?: number /*流量投放配置数据权限：0=全部，1=事业部，2=本人所负责的*/;
  trafficConfigurationBuIds?: Array<string> /*流量投放配置事业部数据权限*/;
};

export type AddDataRoleApiResult = string;

/**
 *添加数据角色
 */
export const addDataRoleApi = (params: AddDataRoleApiRequest) => {
  return Fetch<ResponseWithResult<AddDataRoleApiResult>>('/user/public/dataRole/add', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/user/public/dataRole/add') },
  });
};
