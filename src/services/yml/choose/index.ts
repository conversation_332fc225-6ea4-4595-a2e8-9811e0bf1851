import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';
interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type CascadeListRequest = {
  deptId?: string /*事业部id*/;
  labelStatus?: 'ENABLE' | 'DISABLE' /*状态[LabelStatusEnum]*/;
  talentIdList?: Array<string> /*达人id*/;
};

export type CascadeListResult = {
  cascadeList?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*标签组id*/;
    labelGroupName?: string /*标签组名称*/;
    labelOptionList?: Array<{
      labelGroupId?: string /*父级id*/;
      labelOption?: string /*选项名称*/;
      optionId?: string /*id*/;
    }> /*二级目录*/;
    requiredFlag?: number /* 0-非必填 1-必填*/;
    status?: 'ENABLE' | 'DISABLE' /*ENABLE-启用 UNABLE-禁用[LabelStatusEnum]*/;
  }> /*级联数据*/;
};

/**
 *标签级联列表
 */
export const cascadeList = (params: CascadeListRequest) => {
  return Fetch<ResponseWithResult<CascadeListResult>>('/iasm/public/label/cascadeList', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/label/cascadeList') },
  });
};

export type BpEditRequest = {
  afterSaleContent?: {
    freshCompensation?: string /*（生鲜）赔付标准 文本 行业大类为生鲜时展示该字段；提示：请填写赔付标准和有效期，例如：确认收货后30个工作日内，坏果坏1赔1，超过50%全赔*/;
    freshIsCold?: boolean /* （生鲜）是否冷链发货 true：是，false：否*/;
    installationService?: string /* （数码家电）售后附加服务 文本 行业大类为数码家电时展示该字段；提示：免人工安装费用，额外收取xx元材料费，支持延迟发货*/;
    insuranceTime?: string /* （数码家电）质保时长  文本 行业大类为数码家电时展示该字段*/;
    modes?: Array<
      'NONE' | 'NO_REASON' | 'FREE_FREIGHT_INSURANCE' | 'OTHER'
    > /* 售后服务 多选数组 必填，选项：无、7天无理由、赠送运费险、其他[AfterSaleTypeEnum]*/;
    others?: string /* 其他售后服务文本 必填，售后服务选其他时，必填该字段*/;
    productionDate?: string /* 生产日期 文本 行业大类为食品饮料、美妆护肤则展示，例如：晚于2023年6月*/;
    shelfLife?: string /* 产品保质期 文本 行业大类为食品饮料、美妆护肤则展示，例如：6个月、避光保存7天*/;
  } /*售后信息*/;
  brandFee?: string /*基础服务费（基础服务费）*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  comment?: string /*备注*/;
  commissionRate?: string /*线上佣金比例*/;
  commissionRateOffline?: string /*线下佣金比例*/;
  discountContent?: {
    giftInfos?: Array<{
      condition?:
        | 'IMMEDIATE'
        | 'REQUIREMENTS' /*赠送条件，下单即送、指定条件赠送[GiftConditionTypeEnum]*/;
      conditionRemark?: string /* 赠送条件备注*/;
      giftType?: 'ALL' | 'PART' /*赠品生效规格[GiftTypeEnum]*/;
      image?: string /*赠品图片*/;
      link?: string /*赠品链接*/;
      name?: string /*赠品名称*/;
      onSale?: boolean /*是否在售*/;
      price?: string /*赠品价格*/;
      skuNoList?: Array<string> /*赠品生效的sku编号*/;
    }> /*赠品信息*/;
    remark?: string /* 优惠备注*/;
    type?: Array<
      'FULL_REDUCTION' | 'IMMEDIATE_REDUCTION' | 'COUPON' | 'OTHER' | 'NONE'
    > /* 优惠方式, 必填，选项：满减、立减、优惠券、其他、无（该选项和其他选项互斥，选了无则不允许选其他选项[DiscountTypeEnum]*/;
  } /*优惠信息*/;
  id?: string /*id*/;
  labelConfigInfo?: Array<{
    labelName?: string /*标签名称*/;
    labelOptionId?: string /*标签选项配置id*/;
    labelOptionName?: string /*标签选项名称*/;
  }> /*标签配置信息*/;
  labelOptionContent?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*id*/;
    labelGroupName?: string /*标签名称*/;
    labelOptionList?: Array<{
      id?: string /*id*/;
      labelOption?: string /*标签内容名称*/;
    }> /*标签选项*/;
    requiredFlag?: boolean /* false-非必填 true-必填*/;
  }> /*标签内容*/;
  liveServiceTypeId?: string /*直播服务类型id*/;
  logisticsContent?: {
    deliveryCycle?: string /* 发货周期, 必填，xx天内发货，默认2天*/;
    deliveryMode?: 'SPOT' | 'PRESALE' /* 发货模式, 必填，现货/预售[DeliveryModeEnum]*/;
    giftDeliveryMode?: boolean /* 主品赠品发货情况, 赠品是否随主品一起发货，true：是，false：否*/;
    giftLogisticsNo?: string /* 赠品寄样单号，如果选择 主赠分别发货 显示本字段*/;
    logisticsNo?: string /* 主品寄样单号*/;
  } /*物流信息*/;
  sectionFee?: string /*切片费*/;
  sellingPoints?: string /*商品主要卖点*/;
  skus?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
  }> /*sku信息*/;
  spuFocus?: string /*重点展示需求*/;
  version?: number /*版本号*/;
};

export type BpEditResult = boolean;

/**
 *商务编辑
 */
export const bpEdit = (params: BpEditRequest) => {
  return Fetch<ResponseWithResult<BpEditResult>>('/iasm/public/selection/bpEdit', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/bpEdit') },
  });
};

export type BrandListRequest = {
  name?: string /*品牌名称*/;
};

export type BrandListResult = Array<{
  code?: string /*品牌编号*/;
  englishName?: string /*英文名*/;
  id?: string /*品牌id*/;
  logo?: string /*logo*/;
  name?: string /*品牌名称*/;
  spuBrandLabelIdList?: string /*品牌标签id列表*/;
  spuBrandLabelList?: Array<{
    id?: string /*品牌标签id*/;
    name?: string /*品牌标签名称*/;
  }> /*品牌标签列表*/;
  status?: string /*品牌状态*/;
}>;

/**
 *品牌列表获取
 */
export const brandList = (params: BrandListRequest) => {
  return Fetch<ResponseWithResult<BrandListResult>>(
    '/befriend-service-goods/public/spuBrand/list',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/goods/public/spuBrand/list') },
    },
  );
};

export type BpEditAndConfirmRequest = {
  afterSaleContent?: {
    freshCompensation?: string /*（生鲜）赔付标准 文本 行业大类为生鲜时展示该字段；提示：请填写赔付标准和有效期，例如：确认收货后30个工作日内，坏果坏1赔1，超过50%全赔*/;
    freshIsCold?: boolean /* （生鲜）是否冷链发货 true：是，false：否*/;
    installationService?: string /* （数码家电）售后附加服务 文本 行业大类为数码家电时展示该字段；提示：免人工安装费用，额外收取xx元材料费，支持延迟发货*/;
    insuranceTime?: string /* （数码家电）质保时长  文本 行业大类为数码家电时展示该字段*/;
    modes?: Array<
      'NONE' | 'NO_REASON' | 'FREE_FREIGHT_INSURANCE' | 'OTHER'
    > /* 售后服务 多选数组 必填，选项：无、7天无理由、赠送运费险、其他[AfterSaleTypeEnum]*/;
    others?: string /* 其他售后服务文本 必填，售后服务选其他时，必填该字段*/;
    productionDate?: string /* 生产日期 文本 行业大类为食品饮料、美妆护肤则展示，例如：晚于2023年6月*/;
    shelfLife?: string /* 产品保质期 文本 行业大类为食品饮料、美妆护肤则展示，例如：6个月、避光保存7天*/;
  } /*售后信息*/;
  brandFee?: string /*基础服务费（基础服务费）*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  comment?: string /*备注*/;
  commissionRate?: string /*线上佣金比例*/;
  commissionRateOffline?: string /*线下佣金比例*/;
  discountContent?: {
    giftInfos?: Array<{
      condition?:
        | 'IMMEDIATE'
        | 'REQUIREMENTS' /*赠送条件，下单即送、指定条件赠送[GiftConditionTypeEnum]*/;
      conditionRemark?: string /* 赠送条件备注*/;
      giftType?: 'ALL' | 'PART' /*赠品生效规格[GiftTypeEnum]*/;
      image?: string /*赠品图片*/;
      link?: string /*赠品链接*/;
      name?: string /*赠品名称*/;
      onSale?: boolean /*是否在售*/;
      price?: string /*赠品价格*/;
      skuNoList?: Array<string> /*赠品生效的sku编号*/;
    }> /*赠品信息*/;
    remark?: string /* 优惠备注*/;
    type?: Array<
      'FULL_REDUCTION' | 'IMMEDIATE_REDUCTION' | 'COUPON' | 'OTHER' | 'NONE'
    > /* 优惠方式, 必填，选项：满减、立减、优惠券、其他、无（该选项和其他选项互斥，选了无则不允许选其他选项[DiscountTypeEnum]*/;
  } /*优惠信息*/;
  expireDays?: number /*失效期限*/;
  frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
  frameworkRoundFlag?: boolean /*是否计入年框场次*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  id?: string /*id*/;
  isLowCommission?: boolean /*是否低佣金*/;
  labelConfigInfo?: Array<{
    labelName?: string /*标签名称*/;
    labelOptionId?: string /*标签选项配置id*/;
    labelOptionName?: string /*标签选项名称*/;
  }> /*标签配置信息*/;
  labelOptionContent?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*id*/;
    labelGroupName?: string /*标签名称*/;
    labelOptionList?: Array<{
      id?: string /*id*/;
      labelOption?: string /*标签内容名称*/;
    }> /*标签选项*/;
    requiredFlag?: boolean /* false-非必填 true-必填*/;
  }> /*标签内容*/;
  liveServiceTypeId?: string /*直播服务类型id*/;
  logisticsContent?: {
    deliveryCycle?: string /* 发货周期, 必填，xx天内发货，默认2天*/;
    deliveryMode?: 'SPOT' | 'PRESALE' /* 发货模式, 必填，现货/预售[DeliveryModeEnum]*/;
    giftDeliveryMode?: boolean /* 主品赠品发货情况, 赠品是否随主品一起发货，true：是，false：否*/;
    giftLogisticsNo?: string /* 赠品寄样单号，如果选择 主赠分别发货 显示本字段*/;
    logisticsNo?: string /* 主品寄样单号*/;
  } /*物流信息*/;
  lowCommissionRequest?: {
    estimateGmv?: string /*本场预估支付GMV*/;
    finishedGmv?: string /*已完成gmv*/;
    guaranteedGmv?: string /*应保gmv*/;
    highAnchorName?: string /*直播间名称*/;
    highAuthorOpenId?: string /*高佣对应直播间ID*/;
    highBpName?: string /*高佣对应商务*/;
    highBrandFee?: string /*高佣对应基础服务费*/;
    highLiveDate?: string /*高佣对应直播日期*/;
    highPayGmv?: string /*高佣对应场次支付GMV*/;
    highSalePrice?: string /*高佣对应到手价*/;
    highTotalCommissionRate?: string /*高佣对应总佣金*/;
    quantityGuaranteedLiveRoundBrandFee?: string /*保量/专场基础服务费*/;
    quantityGuaranteedLiveRoundId?: string /*保量/专场场次*/;
    reasonDetail?: string /*详细原因*/;
    reasonType?:
      | 'BREAK_PRICE_REDUCE_COMMISSION'
      | 'BRAND_FEE_AFFECTS'
      | 'SUPPLEMENT_LIVE'
      | 'QUANTITY_GUARANTEED'
      | 'HIS_COMMERCIAL_CLAUSE_REPORTING_ERROR'
      | 'BRAND_REDUCE_COMMISSION'
      | 'FRAMEWORK_COOPERATION'
      | 'WELFARE_GOODS'
      | 'OTHER_LESS_THAN_LUO'
      | 'OTHER' /*低佣原因类型[LowCommissionReasonTypeEnum]*/;
    supplementLiveRoundBrandFee?: string /*补播场次对应的基础服务费*/;
    supplementLiveRoundId?: string /*补播场次*/;
  } /*低佣信息*/;
  roundTotalFees?: string /*年框佣金*/;
  sectionFee?: string /*切片费*/;
  sellingPoints?: string /*商品主要卖点*/;
  skus?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
  }> /*sku信息*/;
  spuFocus?: string /*重点展示需求*/;
  version?: number /*版本号*/;
};

export type BpEditAndConfirmResult = boolean;

/**
 *商务保存并确认
 */
export const bpEditAndConfirm = (params: BpEditAndConfirmRequest) => {
  return Fetch<ResponseWithResult<BpEditAndConfirmResult>>(
    '/iasm/public/selection/bpEditAndConfirm',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/bpEditAndConfirm') },
    },
  );
};

export type IsSignAgreementRequest = {
  id?: string /*业务ID*/;
};

export type IsSignAgreementResult = boolean;

/**
 *校验并保存服务协议
 */
export const isSignAgreement = (params: IsSignAgreementRequest) => {
  return Fetch<ResponseWithResult<IsSignAgreementResult>>(
    '/iasm/public/selection/checkAndSaveServiceAgreement',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/checkAndSaveServiceAgreement') },
    },
  );
};

export type GetServiceTypeRequest = {
  deptId?: string /*事业部id*/;
  flag?: boolean /*是否查全部 true:查全部 false:查非删除的*/;
  liveRoomId?: string /*直播间id*/;
  liveRoundId?: string /*场次ID*/;
  serviceTypeName?: string /*服务类型名称*/;
};

export type GetServiceTypeResult = {
  required?: number /*是否必填(0:非必填;1:必填)*/;
  serviceTypeOptions?: Array<{
    id?: string /*服务类型选项配置id*/;
    name?: string /*直播服务类型选项名称*/;
    sort?: number /*排序*/;
    status?: 'DISABLE' | 'ENABLE' /*状态(0:停用;1:启用)[ServiceTypeOptionStatus]*/;
  }> /*服务选项配置集合*/;
};

/**
 *检测是否是必填直播服务类型选项
 */
export const getServiceType = (params: GetServiceTypeRequest) => {
  return Fetch<ResponseWithResult<GetServiceTypeResult>>(
    '/iasm/public/LiveServiceType/getServiceType',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/LiveServiceType/getServiceType') },
    },
  );
};

export type GetServiceTypeBySelectionRoundIdsRequest = {
  ids?: Array<string> /*IDS*/;
};

export type GetServiceTypeBySelectionRoundIdsResult = {
  [key: string]: {
    required?: number /*是否必填(0:非必填;1:必填)*/;
    serviceTypeOptions?: Array<{
      id?: string /*服务类型选项配置id*/;
      name?: string /*直播服务类型选项名称*/;
      sort?: number /*排序*/;
      status?: 'DISABLE' | 'ENABLE' /*状态(0:停用;1:启用)[ServiceTypeOptionStatus]*/;
    }> /*服务选项配置集合*/;
  };
};

/**
 *检测是否是必填直播服务类型选项
 */
export const getServiceTypeBySelectionRoundIds = (
  params: GetServiceTypeBySelectionRoundIdsRequest,
) => {
  return Fetch<ResponseWithResult<GetServiceTypeBySelectionRoundIdsResult>>(
    '/iasm/public/LiveServiceType/getServiceTypeBySelectionRoundIds',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/iasm/public/LiveServiceType/getServiceTypeBySelectionRoundIds'),
      },
    },
  );
};
