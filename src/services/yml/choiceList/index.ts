import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';
interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type LiveGoodsListRequest = {
  agreementSignStatus?: boolean /*协议签署状态,已签署true,未签署false*/;
  bpId?: string /*商务ID*/;
  bpIds?: Array<string> /*商务ID*/;
  brandFeeMax?: string /*基础服务费查询区间 最大值*/;
  brandFeeMin?: string /*基础服务费查询区间 最小值*/;
  brandId?: string /*品牌id*/;
  current?: number /*当前页码,从1开始*/;
  endBrandFee?: string /*基础服务费基础服务费,结束*/;
  frameworkGoods?: boolean /*框架商品*/;
  groupIds?: Array<string> /*项目组ID*/;
  guaranteeQuantityGoods?: boolean /*保量商品*/;
  idList?: Array<string> /*场次货盘ID集合*/;
  labelId?: string /*标签*/;
  legalStatus?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*法务审核状态 「风险等级」[SelectionQualificationRiskLevel]*/;
  liveRoundId?: string /*场次ID*/;
  liveServiceTypeIds?: Array<string> /*直播服务类型IDs*/;
  livestreamLinkStatus?: 'GENERATED' | 'NOT_GENERATED' /*上播链接状态[LivestreamLinkStatus]*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxCommissionRate?: string /*线上佣金比例-最高值*/;
  maxCommissionRateOffline?: string /*线下佣金比例-最高值*/;
  maxSectionFee?: string /*切片费-最高值*/;
  minCommissionRate?: string /*线上佣金比例-最低值*/;
  minCommissionRateOffline?: string /*线下佣金比例-最低值*/;
  minSectionFee?: string /*切片费-最低值*/;
  notUploadFlag?: boolean /*是否只显示未上传付款凭证数据,true未上传,false已上传*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*运营审核状态[SelectionReviewStatus]*/;
  paymentVoucherStatus?:
    | 'WAIT_CONFIRM'
    | 'CONFIRMED'
    | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
  platformSpuId?: string /*平台商品ID*/;
  platformSpuIds?: Array<string> /*批量平台商品ID*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionNo?: string /*选品流程编号*/;
  selectionNos?: Array<string> /*批量选品流程编号*/;
  selectionStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*选品审核状态[SelectionReviewStatus]*/;
  shopName?: string /*店铺名称*/;
  size?: number /*分页大小*/;
  spokenIsComplete?: boolean /*口播稿是否完整*/;
  spuNameKeyword?: string /*商品关键词（商品名称）*/;
  spuNames?: Array<string> /*批量商品名称*/;
  spuNo?: Array<string> /*批量spu编号*/;
  startBrandFee?: string /*基础服务费基础服务费,开始*/;
  statusList?: Array<
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED'
  > /*状态[SelectionRoundStatus]*/;
  supplierConfirming?: boolean /*商家确认状态,true已确认,false未确认*/;
  supplierId?: string /*商家id*/;
};

export type LiveGoodsListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    advancePaymentOrderNo?: string /*预付款单编号*/;
    allowQualityScoreAudit?: boolean /*是否允许质量分OA审核 true 是 false 否*/;
    avgSales?: string /*场均(支付)*/;
    avgSalesForFifteenDays?: string /*场均(T15)*/;
    bestSignCompanyStatus?:
      | 'WAIT_CHECK'
      | 'CHECKING'
      | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
    bpCancelReason?: string /*商务取消原因*/;
    bpConfirmSwitch?: 'OFF' | 'ON' /*商务确认开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    bpId?: string /*商务ID*/;
    bpName?: string /*商务名称*/;
    bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[SelectionBpStatus]*/;
    brandFee?: string /*基础服务费，坑位费*/;
    brandName?: string /*品牌名称*/;
    captainPromotionLink?: {
      captainId?: string;
      creator?: string;
      doudianGoodsId?: string;
      doudianId?: string;
      doudianName?: string;
      effectEndTime?: string;
      effectStartTime?: string;
      errorDetail?: string;
      gmtCreated?: string;
      gmtModified?: string;
      id?: string;
      liveTime?: string;
      modifier?: string;
      operatorName?: string;
      promotionLink?: string;
      promotionLinkId?: string;
      reason?: number;
      sellPrice?: string;
      serviceFeeRate?: string;
      source?: number;
      spuId?: string;
      spuImg?: string;
      spuName?: string;
      spuNo?: string;
      status?: number;
      talentCommissionRate?: string;
      type?: number;
    } /*抖音推广链接*/;
    commissionRate?: string /*线上佣金比例*/;
    commissionRateOffline?: string /*线下佣金比例*/;
    complianceAuditor?: string /*合规审核人ID*/;
    complianceAuditorName?: string /*合规审核人名称*/;
    complianceStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    coopFrameworkId?: string /*合作框架id*/;
    coopFrameworkType?: number /*合作框架类型,1保gmv模式-按主体,2保gmv模式-指定品牌*/;
    cooperationGuaranteed?: {
      accountNo?: string /*机构银行账号*/;
      achievedCalculationType?:
        | 'BY_PAYMENT'
        | 'BY_SETTLEMENT' /*达成计算方式(按支付-BY_PAYMENT,按结算-BY_SETTLEMENT)[AchievedCalculationTypeEnum]*/;
      agreedRefundFlag?: number /*是否约定退款*/;
      appointReceiptInstitutionId?: string /*指定收款机构id*/;
      appointReceiptInstitutionName?: string /*指定收款机构名称*/;
      bankName?: string /*机构开户行*/;
      bestSignContractId?: string /*上上签合同ID*/;
      bpId?: string /*商务id*/;
      bpName?: string /*商务名称*/;
      brandFee?: string /*基础佣金（原固定服务费）*/;
      brandFeeLatestPaymentTime?: string /*基础佣金最晚支付日期*/;
      brandIds?: Array<string> /*品牌id*/;
      brandInfo?: string /*品牌信息*/;
      brandInfoDTOS?: Array<{
        brandId?: string /*品牌id*/;
        brandName?: string /*品牌名称*/;
      }> /*品牌信息*/;
      commissionRate?: string /*线上佣金比例*/;
      contractApproveStatus?:
        | 'INIT'
        | 'WAIT_PENDING'
        | 'PENDING'
        | 'PASS'
        | 'REJECTED' /*合同审批状态（WAIT_PENDING：待审批；PENDING：审批中；PASS：已通过；REJECTED：已驳回）[ContractApproveStatusEnum]*/;
      contractInfo?: string /*合同信息*/;
      contractInfoList?: Array<string> /*合同信息*/;
      contractStatus?:
        | 'WAIT_START'
        | 'SIGNING'
        | 'SIGNED'
        | 'WITHDRAWAL' /*合同状态（WAIT_START:待开始；SIGNING:签署中；SIGNED:已签署）[ContractStatusEnum]*/;
      contractType?:
        | 'ON_LINE'
        | 'OFF_LINE' /*合同类型（ON_LINE：线上合同；OFF_LINE：线下合同）[GuaranteedContractTypeEnum]*/;
      coopActualEndTime?: string /*合作实际结束时间*/;
      coopAdvanceEndTime?: string /*合作提前结束时间*/;
      coopEndTime?: string /*合作结束时间*/;
      coopExtendEndTime?: string /*保量延长期*/;
      coopManualEndTime?: string /*合作手动结束时间*/;
      coopStartTime?: string /*合作开始时间*/;
      creator?: string /*创建人*/;
      creatorName?: string /*创建人名称*/;
      delFlag?: number /*删除标记*/;
      deptId?: string /*事业部id(已废弃)*/;
      deptInfo?: string /*事业部信息*/;
      deptInfoDTOS?: Array<{
        deptId?: string /*事业部id*/;
        deptName?: string /*事业部名称*/;
      }> /*事业部信息*/;
      description?: string /*合同描述*/;
      estimatedSettlementGmv?: string /*保量实际GMV(原名：预估结算gmv)*/;
      estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
      finishedGmv?: string /*保量预估GMV(原名：已完成目标gmv（原名：已完成保量gmv）)*/;
      finishedRate?: string /*保量预估GMV比例*/;
      gmtCreated?: string /*创建时间*/;
      gmtModified?: string /*更新时间*/;
      goodsKeyWordInfoDTOS?: Array<{
        brandInfoDTOS?: Array<{
          brandId?: string /*品牌id*/;
          brandName?: string /*品牌名称*/;
        }> /*品牌信息*/;
        goodsKeyWord?: string /*商品关键词*/;
        shopInfoDTOS?: Array<{
          shopId?: string /*店铺id*/;
          shopName?: string /*店铺名称*/;
        }> /*店铺信息*/;
      }> /*商品关键字信息*/;
      goodsKeywordInfo?: string /*商品关键字信息*/;
      guaranteeBrandFeeRate?: string /*保量基础佣金*/;
      guaranteeQuantityId?: string /*保量合作id(已废弃)*/;
      guaranteedGmv?: string /*目标gmv（原保量gmv）*/;
      guaranteedGmvAdvanceAchievedRule?:
        | 'AUTO_FINISH'
        | 'GO_ON' /*目标gmv提前达成计算规则(合作自动终止；乙方继续为甲方提供营销服务，对超出目标GMV部分的销售额，由甲方向乙方支付额外的激励佣金，激励佣金=超出的销售额/（目标GMV/基础佣金总额）)[GuaranteedGmvAdvanceAchievedRuleEnum]*/;
      guaranteedGmvFailAchievedRule?:
        | 'B_REFUNDS_UNUSED_BASIC_COMMISSION_TO_A'
        | 'EXTEND_COOPERATION_PERIOD'
        | 'UNTIL_ACHIEVEMENT_GOAL_EXTEND_COOPERATION_PERIOD' /*目标gmv未能达成计算规则（乙方向甲方退还未消耗的基础佣金；延长合作期限；合作期限延长至目标gmv达成之日）[GuaranteedGmvFailAchievedRuleEnum]*/;
      id?: string /*保量合同id*/;
      institutionId?: string /*机构id（弃用）*/;
      institutionIds?: Array<string> /*机构id集合*/;
      institutionInfoDTOS?: Array<{
        institutionId?: string /*机构id*/;
        institutionName?: string /*机构名称*/;
        institutionOrganizationName?: string /*机构实名主体名称*/;
        institutionRelNameVersion?: string /*机构实名快照版本号*/;
      }> /*机构信息*/;
      institutionName?: string /*机构名称（弃用）*/;
      institutionOrganizationName?: string /*机构实名主体名称（弃用）*/;
      institutionRelNameVersion?: string /*机构实名快照版本号（弃用）*/;
      invoiceContent?:
        | 'MODERN_SERVICE_TECHNOLOGY_SERVICE_FEE'
        | 'MODERN_SERVICE_SERVICE_FEE' /*发票内容[InvoiceContentTypeEnum]*/;
      invoiceRate?: string /*发票税率*/;
      isWhitelist?: boolean /*是否白名单（0：否；1：是）*/;
      liveRoomInfo?: string /*直播间信息*/;
      liveRoomInfos?: Array<string> /*直播间id*/;
      liveRoomPossessType?:
        | 'ALL'
        | 'INCLUDE' /*直播间拥有类型（ALL:全部；INCLUDE：包含)[LiveRoomPossessTypeEnum]*/;
      mediaSigningStatus?:
        | 'SIGNED'
        | 'UNSIGNED' /*新媒体服务协议签署状态(SIGNED：已签署；UNSIGNED：未签署)；已废弃[MediaSigningStatusEnum]*/;
      modifier?: string /*更新人*/;
      modifierName?: string /*更新人名称*/;
      name?: string /*保量合同名称*/;
      no?: string /*保量合同编号*/;
      oaRequestId?: string /*oa请求ID「审批流」*/;
      originalCoopGuaranteedId?: string /*原保量合作记录主键ID*/;
      originalCoopGuaranteedNo?: string /*原保量合作记录编号*/;
      payDurationType?:
        | 'T_PAY'
        | 'T_ZERO'
        | 'T_TWO'
        | 'T_FIFTEEN'
        | 'T_SEVEN'
        | 'BY_SETTLEMENT' /*支付天数（T_PAY:支付时；T+0:支付24h内；T+2:支付48h内）[PayDurationTypeEnum]*/;
      paymentAmountTotal?: string /*累计回款金额*/;
      paymentType?:
        | 'SELF_FUNDED'
        | 'THIRD_PARTY' /*付款方式（自行支付/代付）[ContractPaymentTypeEnum]*/;
      platform?: string /*平台*/;
      relVOS?: Array<{
        brandId?: string /*品牌id*/;
        cooperationGuaranteedId?: string /*保量合作记录主键id*/;
        cooperationGuaranteedNo?: string /*保量合作记录编号*/;
        creator?: string /*创建人*/;
        gmtCreated?: string;
        gmtModified?: string;
        id?: string /*id*/;
        legalName?: string /*供应商主体名称*/;
        liveDate?: string /*直播时间*/;
        liveRoomId?: string /*直播间id*/;
        liveRoomName?: string /*直播间名称*/;
        modifier?: string /*更新人*/;
        openId?: string /*直播间open_id*/;
        platformId?: string /*平台id*/;
        platformSource?: string /*商品来源平台*/;
        platformSpuId?: string /*平台商品ID*/;
        relFlag?: number /*关联标记（是否关联， 0 否  1 是）*/;
        selectionNo?: string /*场次货盘编号*/;
        shopId?: string /*店铺id*/;
        spuName?: string /*spu名称*/;
        spuNo?: string /*spu编号*/;
        supplierId?: string /*商家id*/;
        talentId?: string /*达人id*/;
      }> /*关联关系*/;
      settlementIntervalType?:
        | 'MONTH'
        | 'QUARTER'
        | 'YEAR' /*结算周期[ContractSettlementIntervalTypeEnum]*/;
      shopInfo?: string /*店铺信息*/;
      shopInfoDTOS?: Array<{
        shopId?: string /*店铺id*/;
        shopName?: string /*店铺名称*/;
      }> /*店铺信息*/;
      status?:
        | 'DRAFT'
        | 'WAIT_START'
        | 'IN_COOPERATION'
        | 'FINISHED'
        | 'WITHDRAWAL'
        | 'TERMINATE' /*合作状态（DRAFT：暂存；WAIT_START:待开始；IN_COOPERATION:合作中；FINISHED:已结束）[GuaranteedStatusEnum]*/;
      stimulateCommission?: string /*激励佣金*/;
      stimulateCommissionRate?: string /*激励佣金比例*/;
      supplierCompanyNameInfo?: string /*商家主体名称信息*/;
      supplierCompanyNameInfos?: Array<string> /*商家主体名称信息*/;
      supplierInfo?: string /*商家信息*/;
      supplierInfoDTOS?: Array<{
        contactAddress?: string /*联系地址*/;
        contactMail?: string /*联系邮箱*/;
        contactMobile?: string /*联系电话*/;
        contactName?: string /*联系人*/;
        supplierCompanyName?: string /*商家主体名称*/;
        supplierId?: string /*商家id*/;
      }> /*商家信息*/;
      thirdPartyPayerName?: string /*代付方名称*/;
      thirdPartyPayerUscc?: string /*代付方统一社会信用代码*/;
      type?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'SHOP'
        | 'SHOP_BRAND'
        | 'GOODS_KEYWORD' /*保量类型（SUPPLIER：按商家主体；BRAND：按品牌；SHOP：按店铺；SHOP_BRAND：按店铺品牌；GOODS_KEYWORD：商品关键字）[GuaranteedTypeEnum]*/;
      version?: number /*版本号*/;
    } /*保量信息*/;
    cooperationOrderId?: string /*合作订单id*/;
    dropProductReason?: string /*掉品原因*/;
    dropProductReasonType?: string /*掉品原因类型*/;
    favorableRate?: string /*好评率*/;
    frameworkCoopModel?: Array<
      'GUARANTEED_GMV_MODE' | 'GUARANTEED_LIVE_ROUND_MODE' | 'GUARANTEED_SLICE_MODE'
    > /*合作模式多选：1保 GMV模式，2保场次排期模式，3前两种多选[CooperationFrameworkCoopModelEnum]*/;
    frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
    frameworkRoundFlag?: boolean /*是否计入年框场次*/;
    gmvCommissionRate?: string /*GMV模式会有：年框GMV总分佣比例*/;
    gmvPlatformCommissionRate?: string /*GMV模式会有：年框GMV平台分佣比例*/;
    goodsQualityScore?: string /*商品质量分*/;
    guaranteeQuantityFlag?: boolean /*是否保量*/;
    guaranteeQuantityId?: string /*保量id*/;
    hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
    historySumSales?: string /*历史累计(支付)*/;
    historySumSalesForFifteenDays?: string /*历史累计(T15)*/;
    id?: string /*id*/;
    image?: string /*商品主图链接*/;
    isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
    labelList?: Array<string> /*标签*/;
    legalAuditor?: string /*法务审核人ID*/;
    legalAuditorName?: string /*法务审核人名称*/;
    legalCheckSwitch?: 'OFF' | 'ON' /*法务审核开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    legalStatus?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*法务审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionQualificationRiskLevel]*/;
    linkValidityEndTime?: string /*上播链接结束时间*/;
    linkValidityStartTime?: string /*上播链接开始时间*/;
    livePlatformSpuId?: string /*上播商品id*/;
    liveServiceType?: string /*直播服务类型(讲解类型)*/;
    liveServiceTypeId?: string /*直播服务类型ID*/;
    lowCommissionAuditStatus?:
      | 'NONE'
      | 'INIT'
      | 'PENDING'
      | 'APPROVED'
      | 'REJECTED'
      | 'CANCELED'
      | 'DELETED'
      | 'REVERTED'
      | 'OVERTIME_CLOSE'
      | 'OVERTIME_RECOVER' /*低佣审核状态[LowCommissionAuditStatusEnum]*/;
    lowCommissionAuditor?: string /*低佣审核人*/;
    lowCommissionAuditorName?: string /*低佣审核姓名*/;
    lowCommissionFlowNo?: string /*低佣审核流程编号*/;
    luxuryReviewStatus?:
      | 'NO_NEED_REVIEW'
      | 'PENDING_REVIEW'
      | 'REJECTED'
      | 'APPROVED' /*高奢商品资质复查状态[LuxuryReviewStatusEnum]*/;
    maxPrice?: string /*最大价 */;
    minPrice?: string /*最小价*/;
    needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
    no?: string /*编号, CCHP+TB*/;
    operatorAuditor?: string /*运营审核人ID*/;
    operatorAuditorName?: string /*运营审核人名称*/;
    operatorCheckSwitch?: 'OFF' | 'ON' /*运营审核开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    operatorStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    paymentCooperationId?: string /*合作订单付款订单id*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentVoucherId?: string /*付款凭证id*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    platformSource?:
      | 'DY'
      | 'JD'
      | 'TB'
      | 'PDD'
      | 'KS'
      | 'WECHAT_VIDEO'
      | 'BAIDU'
      | 'RED' /*商品来源平台[PlatformEnum]*/;
    platformSpuId?: string /*平台商品ID*/;
    platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
    promotionLink?: string /*上播链接*/;
    qualifyInspectionStatus?:
      | 'NEEDLESS_INSPECTION'
      | 'NON_INSPECTION'
      | 'INSPECTING'
      | 'PASSED_INSPECTION'
      | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
    qualityScoreAuditStatus?:
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT' /*质量分审核状态[QualityScoreAuditStatusEnum]*/;
    roundTotalFees?: string /*保场次排期模式会有：场次费用*/;
    sectionFee?: string /*切片费*/;
    selectionAuditor?: string /*选品审核人ID*/;
    selectionAuditorName?: string /*选品审核人名称*/;
    selectionCheckSwitch?: 'OFF' | 'ON' /*选品审核开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    selectionStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    sellingPoints?: string /*商品主要卖点*/;
    serviceAgreementId?: string /*服务协议id*/;
    shopPoints?: string /*店铺分*/;
    sourceOrderNo?: string /*来源单据编码*/;
    specialAuditId?: string /*资质特批Id*/;
    specialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'WITHDRAW' /*资质特批状态[SpecialAuditStatus]*/;
    specialAuditType?: 'ONLINE' | 'OFFLINE' | 'HIGH_SPECIAL' /*特批类型[SpecialAuditTypeEnum]*/;
    spokenType?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
    spuFocus?: string /*重点展示需求*/;
    spuId?: string /*spu id*/;
    spuName?: string /*spu名称*/;
    spuNo?: string /*spu编号*/;
    standardFavorableRate?: string /*标准好评率*/;
    standardStore?: string /*标准店铺分*/;
    status?:
      | 'BP_CONFIRMING'
      | 'WAIT_AUDIT'
      | 'WAIT_LIVE'
      | 'ABORT_LIVE'
      | 'ABORT_WAIT_LIVE'
      | 'COMPLETED_LIVE'
      | 'CANCEL'
      | 'INVITING'
      | 'LOSE_EFFICACY'
      | 'TB_ORDERED' /*商务审核状态/场次货盘审核状态[SelectionRoundStatus]*/;
    supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
    supplierBodySpecialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'INVALID' /*主体特批状态[SupplierBodySpecialAuditStatusEnum]*/;
    supplierConfirmSwitch?: 'OFF' | 'ON' /*商家确认开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    supplierId?: string /*商家id*/;
    supplierOperatorId?: string /*商家员工ID*/;
    supplierOrgId?: string /*商家主体快照ID*/;
    supplierOrgName?: string /*商家主体名称*/;
    supplierReason?: string /*商家驳回原因*/;
    supplierStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    totalCommission?: string /*总佣金*/;
    totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
    version?: number /*版本号*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *场次货盘列表 按直播场次查询
 */
export const liveGoodsList = (params: LiveGoodsListRequest) => {
  return Fetch<ResponseWithResult<LiveGoodsListResult>>(
    '/iasm/public/selection/querySelectionRoundList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/querySelectionRoundList') },
    },
  );
};

export type NewLiveGoodsListRequest = {
  agreementSignStatus?: boolean /*协议签署状态,已签署true,未签署false*/;
  bpId?: string /*商务ID*/;
  bpIds?: Array<string> /*商务ID*/;
  brandFeeMax?: string /*基础服务费查询区间 最大值*/;
  brandFeeMin?: string /*基础服务费查询区间 最小值*/;
  brandId?: string /*品牌id*/;
  current?: number /*当前页码,从1开始*/;
  endBrandFee?: string /*基础服务费基础服务费,结束*/;
  frameworkGoods?: boolean /*框架商品*/;
  groupIds?: Array<string> /*项目组ID*/;
  guaranteeQuantityGoods?: boolean /*保量商品*/;
  idList?: Array<string> /*场次货盘ID集合*/;
  labelId?: string /*标签*/;
  legalStatus?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*法务审核状态 「风险等级」[SelectionQualificationRiskLevel]*/;
  liveRoundId?: string /*场次ID*/;
  liveServiceTypeIds?: Array<string> /*直播服务类型IDs*/;
  livestreamLinkStatus?: 'GENERATED' | 'NOT_GENERATED' /*上播链接状态[LivestreamLinkStatus]*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxCommissionRate?: string /*线上佣金比例-最高值*/;
  maxCommissionRateOffline?: string /*线下佣金比例-最高值*/;
  maxSectionFee?: string /*切片费-最高值*/;
  minCommissionRate?: string /*线上佣金比例-最低值*/;
  minCommissionRateOffline?: string /*线下佣金比例-最低值*/;
  minSectionFee?: string /*切片费-最低值*/;
  notUploadFlag?: boolean /*是否只显示未上传付款凭证数据,true未上传,false已上传*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*运营审核状态[SelectionReviewStatus]*/;
  paymentVoucherStatus?:
    | 'WAIT_CONFIRM'
    | 'CONFIRMED'
    | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
  platformSpuId?: string /*平台商品ID*/;
  platformSpuIds?: Array<string> /*批量平台商品ID*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionNo?: string /*选品流程编号*/;
  selectionNos?: Array<string> /*批量选品流程编号*/;
  selectionStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*选品审核状态[SelectionReviewStatus]*/;
  shopName?: string /*店铺名称*/;
  size?: number /*分页大小*/;
  spokenIsComplete?: boolean /*口播稿是否完整*/;
  spuNameKeyword?: string /*商品关键词（商品名称）*/;
  spuNames?: Array<string> /*批量商品名称*/;
  spuNo?: Array<string> /*批量spu编号*/;
  startBrandFee?: string /*基础服务费基础服务费,开始*/;
  statusList?: Array<
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED'
  > /*状态[SelectionRoundStatus]*/;
  supplierConfirming?: boolean /*商家确认状态,true已确认,false未确认*/;
  supplierId?: string /*商家id*/;
};

export type NewLiveGoodsListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    advancePaymentOrderNo?: string /*预付款单编号*/;
    allowQualityScoreAudit?: boolean /*是否允许质量分OA审核 true 是 false 否*/;
    avgSales?: string /*场均(支付)*/;
    avgSalesForFifteenDays?: string /*场均(T15)*/;
    bestSignCompanyStatus?:
      | 'WAIT_CHECK'
      | 'CHECKING'
      | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
    bpCancelReason?: string /*商务取消原因*/;
    bpConfirmSwitch?: 'OFF' | 'ON' /*商务确认开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    bpId?: string /*商务ID*/;
    bpName?: string /*商务名称*/;
    bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[SelectionBpStatus]*/;
    brandFee?: string /*基础服务费，坑位费*/;
    brandName?: string /*品牌名称*/;
    captainPromotionLink?: {
      captainId?: string;
      creator?: string;
      doudianGoodsId?: string;
      doudianId?: string;
      doudianName?: string;
      effectEndTime?: string;
      effectStartTime?: string;
      errorDetail?: string;
      gmtCreated?: string;
      gmtModified?: string;
      id?: string;
      liveTime?: string;
      modifier?: string;
      operatorName?: string;
      promotionLink?: string;
      promotionLinkId?: string;
      reason?: number;
      sellPrice?: string;
      serviceFeeRate?: string;
      source?: number;
      spuId?: string;
      spuImg?: string;
      spuName?: string;
      spuNo?: string;
      status?: number;
      talentCommissionRate?: string;
      type?: number;
    } /*抖音推广链接*/;
    commissionRate?: string /*线上佣金比例*/;
    commissionRateOffline?: string /*线下佣金比例*/;
    complianceAuditor?: string /*合规审核人ID*/;
    complianceAuditorName?: string /*合规审核人名称*/;
    complianceStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    coopFrameworkId?: string /*合作框架id*/;
    coopFrameworkType?: number /*合作框架类型,1保gmv模式-按主体,2保gmv模式-指定品牌*/;
    cooperationGuaranteed?: {
      accountNo?: string /*机构银行账号*/;
      achievedCalculationType?:
        | 'BY_PAYMENT'
        | 'BY_SETTLEMENT' /*达成计算方式(按支付-BY_PAYMENT,按结算-BY_SETTLEMENT)[AchievedCalculationTypeEnum]*/;
      agreedRefundFlag?: number /*是否约定退款*/;
      appointReceiptInstitutionId?: string /*指定收款机构id*/;
      appointReceiptInstitutionName?: string /*指定收款机构名称*/;
      bankName?: string /*机构开户行*/;
      bestSignContractId?: string /*上上签合同ID*/;
      bpId?: string /*商务id*/;
      bpName?: string /*商务名称*/;
      brandFee?: string /*基础佣金（原固定服务费）*/;
      brandFeeLatestPaymentTime?: string /*基础佣金最晚支付日期*/;
      brandIds?: Array<string> /*品牌id*/;
      brandInfo?: string /*品牌信息*/;
      brandInfoDTOS?: Array<{
        brandId?: string /*品牌id*/;
        brandName?: string /*品牌名称*/;
      }> /*品牌信息*/;
      commissionRate?: string /*线上佣金比例*/;
      contractApproveStatus?:
        | 'INIT'
        | 'WAIT_PENDING'
        | 'PENDING'
        | 'PASS'
        | 'REJECTED' /*合同审批状态（WAIT_PENDING：待审批；PENDING：审批中；PASS：已通过；REJECTED：已驳回）[ContractApproveStatusEnum]*/;
      contractInfo?: string /*合同信息*/;
      contractInfoList?: Array<string> /*合同信息*/;
      contractStatus?:
        | 'WAIT_START'
        | 'SIGNING'
        | 'SIGNED'
        | 'WITHDRAWAL' /*合同状态（WAIT_START:待开始；SIGNING:签署中；SIGNED:已签署）[ContractStatusEnum]*/;
      contractType?:
        | 'ON_LINE'
        | 'OFF_LINE' /*合同类型（ON_LINE：线上合同；OFF_LINE：线下合同）[GuaranteedContractTypeEnum]*/;
      coopActualEndTime?: string /*合作实际结束时间*/;
      coopAdvanceEndTime?: string /*合作提前结束时间*/;
      coopEndTime?: string /*合作结束时间*/;
      coopExtendEndTime?: string /*保量延长期*/;
      coopManualEndTime?: string /*合作手动结束时间*/;
      coopStartTime?: string /*合作开始时间*/;
      creator?: string /*创建人*/;
      creatorName?: string /*创建人名称*/;
      delFlag?: number /*删除标记*/;
      deptId?: string /*事业部id(已废弃)*/;
      deptInfo?: string /*事业部信息*/;
      deptInfoDTOS?: Array<{
        deptId?: string /*事业部id*/;
        deptName?: string /*事业部名称*/;
      }> /*事业部信息*/;
      description?: string /*合同描述*/;
      estimatedSettlementGmv?: string /*保量实际GMV(原名：预估结算gmv)*/;
      estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
      finishedGmv?: string /*保量预估GMV(原名：已完成目标gmv（原名：已完成保量gmv）)*/;
      finishedRate?: string /*保量预估GMV比例*/;
      gmtCreated?: string /*创建时间*/;
      gmtModified?: string /*更新时间*/;
      goodsKeyWordInfoDTOS?: Array<{
        brandInfoDTOS?: Array<{
          brandId?: string /*品牌id*/;
          brandName?: string /*品牌名称*/;
        }> /*品牌信息*/;
        goodsKeyWord?: string /*商品关键词*/;
        shopInfoDTOS?: Array<{
          shopId?: string /*店铺id*/;
          shopName?: string /*店铺名称*/;
        }> /*店铺信息*/;
      }> /*商品关键字信息*/;
      goodsKeywordInfo?: string /*商品关键字信息*/;
      guaranteeBrandFeeRate?: string /*保量基础佣金*/;
      guaranteeQuantityId?: string /*保量合作id(已废弃)*/;
      guaranteedGmv?: string /*目标gmv（原保量gmv）*/;
      guaranteedGmvAdvanceAchievedRule?:
        | 'AUTO_FINISH'
        | 'GO_ON' /*目标gmv提前达成计算规则(合作自动终止；乙方继续为甲方提供营销服务，对超出目标GMV部分的销售额，由甲方向乙方支付额外的激励佣金，激励佣金=超出的销售额/（目标GMV/基础佣金总额）)[GuaranteedGmvAdvanceAchievedRuleEnum]*/;
      guaranteedGmvFailAchievedRule?:
        | 'B_REFUNDS_UNUSED_BASIC_COMMISSION_TO_A'
        | 'EXTEND_COOPERATION_PERIOD'
        | 'UNTIL_ACHIEVEMENT_GOAL_EXTEND_COOPERATION_PERIOD' /*目标gmv未能达成计算规则（乙方向甲方退还未消耗的基础佣金；延长合作期限；合作期限延长至目标gmv达成之日）[GuaranteedGmvFailAchievedRuleEnum]*/;
      id?: string /*保量合同id*/;
      institutionId?: string /*机构id（弃用）*/;
      institutionIds?: Array<string> /*机构id集合*/;
      institutionInfoDTOS?: Array<{
        institutionId?: string /*机构id*/;
        institutionName?: string /*机构名称*/;
        institutionOrganizationName?: string /*机构实名主体名称*/;
        institutionRelNameVersion?: string /*机构实名快照版本号*/;
      }> /*机构信息*/;
      institutionName?: string /*机构名称（弃用）*/;
      institutionOrganizationName?: string /*机构实名主体名称（弃用）*/;
      institutionRelNameVersion?: string /*机构实名快照版本号（弃用）*/;
      invoiceContent?:
        | 'MODERN_SERVICE_TECHNOLOGY_SERVICE_FEE'
        | 'MODERN_SERVICE_SERVICE_FEE' /*发票内容[InvoiceContentTypeEnum]*/;
      invoiceRate?: string /*发票税率*/;
      isWhitelist?: boolean /*是否白名单（0：否；1：是）*/;
      liveRoomInfo?: string /*直播间信息*/;
      liveRoomInfos?: Array<string> /*直播间id*/;
      liveRoomPossessType?:
        | 'ALL'
        | 'INCLUDE' /*直播间拥有类型（ALL:全部；INCLUDE：包含)[LiveRoomPossessTypeEnum]*/;
      mediaSigningStatus?:
        | 'SIGNED'
        | 'UNSIGNED' /*新媒体服务协议签署状态(SIGNED：已签署；UNSIGNED：未签署)；已废弃[MediaSigningStatusEnum]*/;
      modifier?: string /*更新人*/;
      modifierName?: string /*更新人名称*/;
      name?: string /*保量合同名称*/;
      no?: string /*保量合同编号*/;
      oaRequestId?: string /*oa请求ID「审批流」*/;
      originalCoopGuaranteedId?: string /*原保量合作记录主键ID*/;
      originalCoopGuaranteedNo?: string /*原保量合作记录编号*/;
      payDurationType?:
        | 'T_PAY'
        | 'T_ZERO'
        | 'T_TWO'
        | 'T_FIFTEEN'
        | 'T_SEVEN'
        | 'BY_SETTLEMENT' /*支付天数（T_PAY:支付时；T+0:支付24h内；T+2:支付48h内）[PayDurationTypeEnum]*/;
      paymentAmountTotal?: string /*累计回款金额*/;
      paymentType?:
        | 'SELF_FUNDED'
        | 'THIRD_PARTY' /*付款方式（自行支付/代付）[ContractPaymentTypeEnum]*/;
      platform?: string /*平台*/;
      relVOS?: Array<{
        brandId?: string /*品牌id*/;
        cooperationGuaranteedId?: string /*保量合作记录主键id*/;
        cooperationGuaranteedNo?: string /*保量合作记录编号*/;
        creator?: string /*创建人*/;
        gmtCreated?: string;
        gmtModified?: string;
        id?: string /*id*/;
        legalName?: string /*供应商主体名称*/;
        liveDate?: string /*直播时间*/;
        liveRoomId?: string /*直播间id*/;
        liveRoomName?: string /*直播间名称*/;
        modifier?: string /*更新人*/;
        openId?: string /*直播间open_id*/;
        platformId?: string /*平台id*/;
        platformSource?: string /*商品来源平台*/;
        platformSpuId?: string /*平台商品ID*/;
        relFlag?: number /*关联标记（是否关联， 0 否  1 是）*/;
        selectionNo?: string /*场次货盘编号*/;
        shopId?: string /*店铺id*/;
        spuName?: string /*spu名称*/;
        spuNo?: string /*spu编号*/;
        supplierId?: string /*商家id*/;
        talentId?: string /*达人id*/;
      }> /*关联关系*/;
      settlementIntervalType?:
        | 'MONTH'
        | 'QUARTER'
        | 'YEAR' /*结算周期[ContractSettlementIntervalTypeEnum]*/;
      shopInfo?: string /*店铺信息*/;
      shopInfoDTOS?: Array<{
        shopId?: string /*店铺id*/;
        shopName?: string /*店铺名称*/;
      }> /*店铺信息*/;
      status?:
        | 'DRAFT'
        | 'WAIT_START'
        | 'IN_COOPERATION'
        | 'FINISHED'
        | 'WITHDRAWAL'
        | 'TERMINATE' /*合作状态（DRAFT：暂存；WAIT_START:待开始；IN_COOPERATION:合作中；FINISHED:已结束）[GuaranteedStatusEnum]*/;
      stimulateCommission?: string /*激励佣金*/;
      stimulateCommissionRate?: string /*激励佣金比例*/;
      supplierCompanyNameInfo?: string /*商家主体名称信息*/;
      supplierCompanyNameInfos?: Array<string> /*商家主体名称信息*/;
      supplierInfo?: string /*商家信息*/;
      supplierInfoDTOS?: Array<{
        contactAddress?: string /*联系地址*/;
        contactMail?: string /*联系邮箱*/;
        contactMobile?: string /*联系电话*/;
        contactName?: string /*联系人*/;
        supplierCompanyName?: string /*商家主体名称*/;
        supplierId?: string /*商家id*/;
      }> /*商家信息*/;
      thirdPartyPayerName?: string /*代付方名称*/;
      thirdPartyPayerUscc?: string /*代付方统一社会信用代码*/;
      type?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'SHOP'
        | 'SHOP_BRAND'
        | 'GOODS_KEYWORD' /*保量类型（SUPPLIER：按商家主体；BRAND：按品牌；SHOP：按店铺；SHOP_BRAND：按店铺品牌；GOODS_KEYWORD：商品关键字）[GuaranteedTypeEnum]*/;
      version?: number /*版本号*/;
    } /*保量信息*/;
    cooperationOrderId?: string /*合作订单id*/;
    dropProductReason?: string /*掉品原因*/;
    dropProductReasonType?: string /*掉品原因类型*/;
    favorableRate?: string /*好评率*/;
    frameworkCoopModel?: Array<
      'GUARANTEED_GMV_MODE' | 'GUARANTEED_LIVE_ROUND_MODE' | 'GUARANTEED_SLICE_MODE'
    > /*合作模式多选：1保 GMV模式，2保场次排期模式，3前两种多选[CooperationFrameworkCoopModelEnum]*/;
    frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
    frameworkRoundFlag?: boolean /*是否计入年框场次*/;
    gmvCommissionRate?: string /*GMV模式会有：年框GMV总分佣比例*/;
    gmvPlatformCommissionRate?: string /*GMV模式会有：年框GMV平台分佣比例*/;
    goodsQualityScore?: string /*商品质量分*/;
    guaranteeQuantityFlag?: boolean /*是否保量*/;
    guaranteeQuantityId?: string /*保量id*/;
    hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
    historySumSales?: string /*历史累计(支付)*/;
    historySumSalesForFifteenDays?: string /*历史累计(T15)*/;
    id?: string /*id*/;
    image?: string /*商品主图链接*/;
    isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
    labelList?: Array<string> /*标签*/;
    legalAuditor?: string /*法务审核人ID*/;
    legalAuditorName?: string /*法务审核人名称*/;
    legalCheckSwitch?: 'OFF' | 'ON' /*法务审核开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    legalStatus?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*法务审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionQualificationRiskLevel]*/;
    linkValidityEndTime?: string /*上播链接结束时间*/;
    linkValidityStartTime?: string /*上播链接开始时间*/;
    livePlatformSpuId?: string /*上播商品id*/;
    liveServiceType?: string /*直播服务类型(讲解类型)*/;
    liveServiceTypeId?: string /*直播服务类型ID*/;
    lowCommissionAuditStatus?:
      | 'NONE'
      | 'INIT'
      | 'PENDING'
      | 'APPROVED'
      | 'REJECTED'
      | 'CANCELED'
      | 'DELETED'
      | 'REVERTED'
      | 'OVERTIME_CLOSE'
      | 'OVERTIME_RECOVER' /*低佣审核状态[LowCommissionAuditStatusEnum]*/;
    lowCommissionAuditor?: string /*低佣审核人*/;
    lowCommissionAuditorName?: string /*低佣审核姓名*/;
    lowCommissionFlowNo?: string /*低佣审核流程编号*/;
    luxuryReviewStatus?:
      | 'NO_NEED_REVIEW'
      | 'PENDING_REVIEW'
      | 'REJECTED'
      | 'APPROVED' /*高奢商品资质复查状态[LuxuryReviewStatusEnum]*/;
    maxPrice?: string /*最大价 */;
    minPrice?: string /*最小价*/;
    needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
    no?: string /*编号, CCHP+TB*/;
    operatorAuditor?: string /*运营审核人ID*/;
    operatorAuditorName?: string /*运营审核人名称*/;
    operatorCheckSwitch?: 'OFF' | 'ON' /*运营审核开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    operatorStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    paymentCooperationId?: string /*合作订单付款订单id*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentVoucherId?: string /*付款凭证id*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    platformSource?:
      | 'DY'
      | 'JD'
      | 'TB'
      | 'PDD'
      | 'KS'
      | 'WECHAT_VIDEO'
      | 'BAIDU'
      | 'RED' /*商品来源平台[PlatformEnum]*/;
    platformSpuId?: string /*平台商品ID*/;
    platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
    promotionLink?: string /*上播链接*/;
    qualifyInspectionStatus?:
      | 'NEEDLESS_INSPECTION'
      | 'NON_INSPECTION'
      | 'INSPECTING'
      | 'PASSED_INSPECTION'
      | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
    qualityScoreAuditStatus?:
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT' /*质量分审核状态[QualityScoreAuditStatusEnum]*/;
    roundTotalFees?: string /*保场次排期模式会有：场次费用*/;
    sectionFee?: string /*切片费*/;
    selectionAuditor?: string /*选品审核人ID*/;
    selectionAuditorName?: string /*选品审核人名称*/;
    selectionCheckSwitch?: 'OFF' | 'ON' /*选品审核开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    selectionStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    sellingPoints?: string /*商品主要卖点*/;
    serviceAgreementId?: string /*服务协议id*/;
    shopPoints?: string /*店铺分*/;
    sourceOrderNo?: string /*来源单据编码*/;
    specialAuditId?: string /*资质特批Id*/;
    specialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'WITHDRAW' /*资质特批状态[SpecialAuditStatus]*/;
    specialAuditType?: 'ONLINE' | 'OFFLINE' | 'HIGH_SPECIAL' /*特批类型[SpecialAuditTypeEnum]*/;
    spokenType?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
    spuFocus?: string /*重点展示需求*/;
    spuId?: string /*spu id*/;
    spuName?: string /*spu名称*/;
    spuNo?: string /*spu编号*/;
    standardFavorableRate?: string /*标准好评率*/;
    standardStore?: string /*标准店铺分*/;
    status?:
      | 'BP_CONFIRMING'
      | 'WAIT_AUDIT'
      | 'WAIT_LIVE'
      | 'ABORT_LIVE'
      | 'ABORT_WAIT_LIVE'
      | 'COMPLETED_LIVE'
      | 'CANCEL'
      | 'INVITING'
      | 'LOSE_EFFICACY'
      | 'TB_ORDERED' /*商务审核状态/场次货盘审核状态[SelectionRoundStatus]*/;
    supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
    supplierBodySpecialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'INVALID' /*主体特批状态[SupplierBodySpecialAuditStatusEnum]*/;
    supplierConfirmSwitch?: 'OFF' | 'ON' /*商家确认开关(OFF：关闭；ON：开启)[SwitchStatus]*/;
    supplierId?: string /*商家id*/;
    supplierOperatorId?: string /*商家员工ID*/;
    supplierOrgId?: string /*商家主体快照ID*/;
    supplierOrgName?: string /*商家主体名称*/;
    supplierReason?: string /*商家驳回原因*/;
    supplierStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    totalCommission?: string /*总佣金*/;
    totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
    version?: number /*版本号*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *新的场次货盘列表 按直播场次查询
 */
export const newLiveGoodsList = (params: NewLiveGoodsListRequest) => {
  return Fetch<ResponseWithResult<NewLiveGoodsListResult>>(
    '/iasm/public/selection/newQuerySelectionRoundList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/newQuerySelectionRoundList') },
    },
  );
};

export type LiveTimeListRequest = {
  endLiveDate?: string /*直播开始日期*/;
  liveRoomIdList?: Array<string> /*直播间主键id 所属直播间*/;
  liveRoomName?: string /*直播间名称*/;
  liveRoundStatusList?: Array<
    'CANCEL' | 'PENDING' | 'LIVING' | 'END'
  > /*状态，全部不传 已取消：CANCEL、准备中：PENDING、直播中：LIVING 已结束：END[LiveRoundStatusEnum]*/;
  startLiveDate?: string /*直播开始日期*/;
};

export type LiveTimeListResult = Array<{
  anchorType?:
    | 'PRIMARY_ANCHOR'
    | 'SECONDARY_ANCHOR'
    | 'TALENT_ANCHOR' /*主播类型 主主播:primary_anchor  副主播:secondary_anchor[AnchorTypeEnum]*/;
  brandFeeGoal?: string /*基础服务费目标*/;
  brandFeeGoalTotal?: string /*总基础服务费目标*/;
  commissionGoal?: string /*佣金目标*/;
  commissionGoalTotal?: string /*总佣金目标*/;
  disposition?: boolean /*是否配置场次区间目标, 已配置:true*/;
  gmvGoal?: string /*总gmv目标*/;
  gmvGoalTotal?: string /*总gmv目标*/;
  holeNum?: number /*总坑位数*/;
  id?: string /*主键*/;
  incomeGoal?: string /*收入目标*/;
  linkNum?: string /*挂链数量*/;
  liveCalendarDetails?: Array<{ [key: string]: string }> /*直播日历详情*/;
  liveDate?: string /*直播日期*/;
  liveEndTime?: string /*直播结束时间*/;
  liveRoomId?: string /*达人账号 直播间Id*/;
  liveStartTime?: string /*直播开始时间*/;
  name?: string /*场次名称*/;
  personInChargeId?: string /*负责人id*/;
  personInChargeName?: string /*负责人*/;
  remark?: string /*备注*/;
  roundDesc?: string /*货盘简述*/;
  roundTag?: string /*场次标签*/;
  status?:
    | 'CANCEL'
    | 'PENDING'
    | 'LIVING'
    | 'END' /*状态 已取消：CANCEL、准备中：PENDING、直播中：LIVING 已结束：END[LiveRoundStatusEnum]*/;
  subject?: string /*场次主题*/;
  talkNum?: string /*讲解数量*/;
}>;

/**
 *直播场次查询
 */
export const liveTimeList = (params: LiveTimeListRequest) => {
  return Fetch<ResponseWithResult<LiveTimeListResult>>('/iasm/public/web/liveRound/search', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/web/liveRound/search') },
  });
};

export type LiveTimeCountListRequest = {
  liveRoundIdlist?: Array<string> /*直播间主键id 所属直播间*/;
};

export type LiveTimeCountListResult = Array<{
  anchorType?: string /*主播类型 主主播:primary_anchor  副主播:secondary_anchor*/;
  deptName?: string /*事业部名称*/;
  liveDate?: string /*直播日期*/;
  liveEndTime?: string /*直播结束时间*/;
  liveRoomId?: string /*达人账号 直播间Id*/;
  liveRoomName?: string /*直播间名称*/;
  liveRoundId?: string /*场次id*/;
  liveStartTime?: string /*直播开始时间*/;
  name?: string /*场次名称*/;
  remark?: string /*备注*/;
  statusCountMap?: {
    [key: string]: string;
  } /*按状态统计：key 状态枚举， value 状态对应的数量 BP_CONFIRMING:待商务确认 WAIT_AUDIT:审核中 WAIT_LIVE:待直播 ABORT_LIVE:掉品 CANCEL:已取消[SelectionRoundStatus]*/;
  subject?: string /*场次主题*/;
}>;

/**
 *场次货盘按直播场次统计
 */
export const liveTimeCountList = (params: LiveTimeCountListRequest) => {
  return Fetch<ResponseWithResult<LiveTimeCountListResult>>(
    '/iasm/public/selection/countByLiveRound',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/countByLiveRound') },
    },
  );
};

export type ApplyDeadlineNodeConfigRequest = {
  deptId?: string /*事业部id*/;
  liveRoomId?: string /*直播间id*/;
  platformSource?: string /*平台来源*/;
};

export type ApplyDeadlineNodeConfigResult = string;

/**
 *选品池报名开始时间节点
 */
export const applyDeadlineNodeConfig = (params: ApplyDeadlineNodeConfigRequest) => {
  return Fetch<ResponseWithResult<ApplyDeadlineNodeConfigResult>>(
    '/tools/public/systemParam/selectGoodsPool/applyDeadlineNodeConfig',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/tools/public/systemParam/selectGoodsPool/applyDeadlineNodeConfig'),
      },
    },
  );
};

export type LiveGoodsInfoRequest = {
  id?: string /*业务ID*/;
};

export type LiveGoodsInfoResult = {
  afterSaleContent?: string /*售后信息，json对象*/;
  applyBillType?:
    | 'FORWARD'
    | 'REVERSE'
    | 'PLATFORM_SUPPLIER_FORWARD' /*类型 正向 FORWARD 逆向 REVERSE 平台型商家(自动生成)报名单 PLATFORM_SUPPLIER_FORWARD[ApplyTypeEnum]*/;
  applyChannel?:
    | 'INVESTMENT_PLAN_DIRECT'
    | 'INVESTMENT_PLAN_INVITE'
    | 'INVESTMENT_PLAN_LIVE_ROUND_INVITE' /*报名渠道，枚举项：招商计划；货盘邀请链接；选品邀请链接[ApplyChannelEnum]*/;
  applyRecordId?: string /*报名记录ID*/;
  applyRecordNo?: string /*报名记录编号*/;
  auditDetailMap?: {
    [key: string]: {
      auditOpinion?: string /*审核意见*/;
      auditState?:
        | 'PASS'
        | 'NO_PASS'
        | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
      bizType?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'GOODS'
        | 'SHOP'
        | 'BP_BRAND'
        | 'BP_GOODS'
        | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
      expirationDate?: string /*法务审核有效期*/;
      isBrandWhitelisted?: boolean /*是否白名单B*/;
      itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
      remark?: string /*备注*/;
    };
  } /*法务审核明细, Key是资质项目版本ID[QualificationBizTypeEnum]*/;
  bestSignCompanyStatus?:
    | 'WAIT_CHECK'
    | 'CHECKING'
    | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
  bpAssistantIds?: string /*商务助理ID, 英文逗号分割*/;
  bpId?: string /*商务ID*/;
  bpName?: string /*商务名称*/;
  bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[SelectionBpStatus]*/;
  brandFee?: string /*基础服务费，坑位费*/;
  brandFeeTechRate?: string /*基础服务费平台服务费率*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  captainPromotionLink?: {
    captainId?: string;
    creator?: string;
    doudianGoodsId?: string;
    doudianId?: string;
    doudianName?: string;
    effectEndTime?: string;
    effectStartTime?: string;
    errorDetail?: string;
    gmtCreated?: string;
    gmtModified?: string;
    id?: string;
    liveTime?: string;
    modifier?: string;
    operatorName?: string;
    promotionLink?: string;
    promotionLinkId?: string;
    reason?: number;
    sellPrice?: string;
    serviceFeeRate?: string;
    source?: number;
    spuId?: string;
    spuImg?: string;
    spuName?: string;
    spuNo?: string;
    status?: number;
    talentCommissionRate?: string;
    type?: number;
  } /*抖音推广链接*/;
  cateId?: string /*类目ID*/;
  cateName?: string /*类目名称*/;
  cateNamePath?: string /*类目全路径名称*/;
  categoryCar?: string /*类目车*/;
  comment?: string /*备注*/;
  commentGood?: string /*商品好评数*/;
  commentTotal?: string /*商品评价数*/;
  commissionRate?: string /*线上佣金比例*/;
  commissionRateOffline?: string /*线下佣金比例*/;
  commissionTechRate?: string /*佣金平台服务费率*/;
  complianceAuditor?: string /*合规审核人ID*/;
  complianceAuditorName?: string /*合规审核人名称*/;
  complianceNo?: string /*合规审批编号*/;
  complianceStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  coopFrameworkId?: string /*合作框架id*/;
  coopMode?: string /*合作方式，枚举项：线上合作、线下合作*/;
  coopOrderNo?: string /*合作订单号*/;
  cooperationGuaranteed?: {
    accountNo?: string /*机构银行账号*/;
    achievedCalculationType?:
      | 'BY_PAYMENT'
      | 'BY_SETTLEMENT' /*达成计算方式(按支付-BY_PAYMENT,按结算-BY_SETTLEMENT)[AchievedCalculationTypeEnum]*/;
    agreedRefundFlag?: number /*是否约定退款*/;
    appointReceiptInstitutionId?: string /*指定收款机构id*/;
    appointReceiptInstitutionName?: string /*指定收款机构名称*/;
    bankName?: string /*机构开户行*/;
    bestSignContractId?: string /*上上签合同ID*/;
    bpId?: string /*商务id*/;
    bpName?: string /*商务名称*/;
    brandFee?: string /*基础佣金（原固定服务费）*/;
    brandFeeLatestPaymentTime?: string /*基础佣金最晚支付日期*/;
    brandIds?: Array<string> /*品牌id*/;
    brandInfo?: string /*品牌信息*/;
    brandInfoDTOS?: Array<{
      brandId?: string /*品牌id*/;
      brandName?: string /*品牌名称*/;
    }> /*品牌信息*/;
    commissionRate?: string /*线上佣金比例*/;
    contractApproveStatus?:
      | 'INIT'
      | 'WAIT_PENDING'
      | 'PENDING'
      | 'PASS'
      | 'REJECTED' /*合同审批状态（WAIT_PENDING：待审批；PENDING：审批中；PASS：已通过；REJECTED：已驳回）[ContractApproveStatusEnum]*/;
    contractInfo?: string /*合同信息*/;
    contractInfoList?: Array<string> /*合同信息*/;
    contractStatus?:
      | 'WAIT_START'
      | 'SIGNING'
      | 'SIGNED'
      | 'WITHDRAWAL' /*合同状态（WAIT_START:待开始；SIGNING:签署中；SIGNED:已签署）[ContractStatusEnum]*/;
    contractType?:
      | 'ON_LINE'
      | 'OFF_LINE' /*合同类型（ON_LINE：线上合同；OFF_LINE：线下合同）[GuaranteedContractTypeEnum]*/;
    coopActualEndTime?: string /*合作实际结束时间*/;
    coopAdvanceEndTime?: string /*合作提前结束时间*/;
    coopEndTime?: string /*合作结束时间*/;
    coopExtendEndTime?: string /*保量延长期*/;
    coopManualEndTime?: string /*合作手动结束时间*/;
    coopStartTime?: string /*合作开始时间*/;
    creator?: string /*创建人*/;
    creatorName?: string /*创建人名称*/;
    delFlag?: number /*删除标记*/;
    deptId?: string /*事业部id(已废弃)*/;
    deptInfo?: string /*事业部信息*/;
    deptInfoDTOS?: Array<{
      deptId?: string /*事业部id*/;
      deptName?: string /*事业部名称*/;
    }> /*事业部信息*/;
    description?: string /*合同描述*/;
    estimatedSettlementGmv?: string /*保量实际GMV(原名：预估结算gmv)*/;
    estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
    finishedGmv?: string /*保量预估GMV(原名：已完成目标gmv（原名：已完成保量gmv）)*/;
    finishedRate?: string /*保量预估GMV比例*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    goodsKeyWordInfoDTOS?: Array<{
      brandInfoDTOS?: Array<{
        brandId?: string /*品牌id*/;
        brandName?: string /*品牌名称*/;
      }> /*品牌信息*/;
      goodsKeyWord?: string /*商品关键词*/;
      shopInfoDTOS?: Array<{
        shopId?: string /*店铺id*/;
        shopName?: string /*店铺名称*/;
      }> /*店铺信息*/;
    }> /*商品关键字信息*/;
    goodsKeywordInfo?: string /*商品关键字信息*/;
    guaranteeBrandFeeRate?: string /*保量基础佣金*/;
    guaranteeQuantityId?: string /*保量合作id(已废弃)*/;
    guaranteedGmv?: string /*目标gmv（原保量gmv）*/;
    guaranteedGmvAdvanceAchievedRule?:
      | 'AUTO_FINISH'
      | 'GO_ON' /*目标gmv提前达成计算规则(合作自动终止；乙方继续为甲方提供营销服务，对超出目标GMV部分的销售额，由甲方向乙方支付额外的激励佣金，激励佣金=超出的销售额/（目标GMV/基础佣金总额）)[GuaranteedGmvAdvanceAchievedRuleEnum]*/;
    guaranteedGmvFailAchievedRule?:
      | 'B_REFUNDS_UNUSED_BASIC_COMMISSION_TO_A'
      | 'EXTEND_COOPERATION_PERIOD'
      | 'UNTIL_ACHIEVEMENT_GOAL_EXTEND_COOPERATION_PERIOD' /*目标gmv未能达成计算规则（乙方向甲方退还未消耗的基础佣金；延长合作期限；合作期限延长至目标gmv达成之日）[GuaranteedGmvFailAchievedRuleEnum]*/;
    id?: string /*保量合同id*/;
    institutionId?: string /*机构id（弃用）*/;
    institutionIds?: Array<string> /*机构id集合*/;
    institutionInfoDTOS?: Array<{
      institutionId?: string /*机构id*/;
      institutionName?: string /*机构名称*/;
      institutionOrganizationName?: string /*机构实名主体名称*/;
      institutionRelNameVersion?: string /*机构实名快照版本号*/;
    }> /*机构信息*/;
    institutionName?: string /*机构名称（弃用）*/;
    institutionOrganizationName?: string /*机构实名主体名称（弃用）*/;
    institutionRelNameVersion?: string /*机构实名快照版本号（弃用）*/;
    invoiceContent?:
      | 'MODERN_SERVICE_TECHNOLOGY_SERVICE_FEE'
      | 'MODERN_SERVICE_SERVICE_FEE' /*发票内容[InvoiceContentTypeEnum]*/;
    invoiceRate?: string /*发票税率*/;
    isWhitelist?: boolean /*是否白名单（0：否；1：是）*/;
    liveRoomInfo?: string /*直播间信息*/;
    liveRoomInfos?: Array<string> /*直播间id*/;
    liveRoomPossessType?:
      | 'ALL'
      | 'INCLUDE' /*直播间拥有类型（ALL:全部；INCLUDE：包含)[LiveRoomPossessTypeEnum]*/;
    mediaSigningStatus?:
      | 'SIGNED'
      | 'UNSIGNED' /*新媒体服务协议签署状态(SIGNED：已签署；UNSIGNED：未签署)；已废弃[MediaSigningStatusEnum]*/;
    modifier?: string /*更新人*/;
    modifierName?: string /*更新人名称*/;
    name?: string /*保量合同名称*/;
    no?: string /*保量合同编号*/;
    oaRequestId?: string /*oa请求ID「审批流」*/;
    originalCoopGuaranteedId?: string /*原保量合作记录主键ID*/;
    originalCoopGuaranteedNo?: string /*原保量合作记录编号*/;
    payDurationType?:
      | 'T_PAY'
      | 'T_ZERO'
      | 'T_TWO'
      | 'T_FIFTEEN'
      | 'T_SEVEN'
      | 'BY_SETTLEMENT' /*支付天数（T_PAY:支付时；T+0:支付24h内；T+2:支付48h内）[PayDurationTypeEnum]*/;
    paymentAmountTotal?: string /*累计回款金额*/;
    paymentType?:
      | 'SELF_FUNDED'
      | 'THIRD_PARTY' /*付款方式（自行支付/代付）[ContractPaymentTypeEnum]*/;
    platform?: string /*平台*/;
    relVOS?: Array<{
      brandId?: string /*品牌id*/;
      cooperationGuaranteedId?: string /*保量合作记录主键id*/;
      cooperationGuaranteedNo?: string /*保量合作记录编号*/;
      creator?: string /*创建人*/;
      gmtCreated?: string;
      gmtModified?: string;
      id?: string /*id*/;
      legalName?: string /*供应商主体名称*/;
      liveDate?: string /*直播时间*/;
      liveRoomId?: string /*直播间id*/;
      liveRoomName?: string /*直播间名称*/;
      modifier?: string /*更新人*/;
      openId?: string /*直播间open_id*/;
      platformId?: string /*平台id*/;
      platformSource?: string /*商品来源平台*/;
      platformSpuId?: string /*平台商品ID*/;
      relFlag?: number /*关联标记（是否关联， 0 否  1 是）*/;
      selectionNo?: string /*场次货盘编号*/;
      shopId?: string /*店铺id*/;
      spuName?: string /*spu名称*/;
      spuNo?: string /*spu编号*/;
      supplierId?: string /*商家id*/;
      talentId?: string /*达人id*/;
    }> /*关联关系*/;
    settlementIntervalType?:
      | 'MONTH'
      | 'QUARTER'
      | 'YEAR' /*结算周期[ContractSettlementIntervalTypeEnum]*/;
    shopInfo?: string /*店铺信息*/;
    shopInfoDTOS?: Array<{
      shopId?: string /*店铺id*/;
      shopName?: string /*店铺名称*/;
    }> /*店铺信息*/;
    status?:
      | 'DRAFT'
      | 'WAIT_START'
      | 'IN_COOPERATION'
      | 'FINISHED'
      | 'WITHDRAWAL'
      | 'TERMINATE' /*合作状态（DRAFT：暂存；WAIT_START:待开始；IN_COOPERATION:合作中；FINISHED:已结束）[GuaranteedStatusEnum]*/;
    stimulateCommission?: string /*激励佣金*/;
    stimulateCommissionRate?: string /*激励佣金比例*/;
    supplierCompanyNameInfo?: string /*商家主体名称信息*/;
    supplierCompanyNameInfos?: Array<string> /*商家主体名称信息*/;
    supplierInfo?: string /*商家信息*/;
    supplierInfoDTOS?: Array<{
      contactAddress?: string /*联系地址*/;
      contactMail?: string /*联系邮箱*/;
      contactMobile?: string /*联系电话*/;
      contactName?: string /*联系人*/;
      supplierCompanyName?: string /*商家主体名称*/;
      supplierId?: string /*商家id*/;
    }> /*商家信息*/;
    thirdPartyPayerName?: string /*代付方名称*/;
    thirdPartyPayerUscc?: string /*代付方统一社会信用代码*/;
    type?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'SHOP'
      | 'SHOP_BRAND'
      | 'GOODS_KEYWORD' /*保量类型（SUPPLIER：按商家主体；BRAND：按品牌；SHOP：按店铺；SHOP_BRAND：按店铺品牌；GOODS_KEYWORD：商品关键字）[GuaranteedTypeEnum]*/;
    version?: number /*版本号*/;
  } /*保量信息*/;
  cooperationMode?: string /*合作模式:COLONEL-团长,DIRECT-定向*/;
  cooperationOrderStatus?:
    | 'TO_BE_ADD'
    | 'TO_BE_FULFILL_AGREEMENT'
    | 'FULFILL_AGREEMENT'
    | 'CLOSED'
    | 'AGREEMENT_CONFIRMED' /*履约状态[CooperationOrderStatusEnum]*/;
  creator?: string /*创建人*/;
  depositAmount?: string /*定金金额*/;
  deptId?: string /*事业部ID*/;
  discountContent?: string /*优惠信息，json对象*/;
  dropProductReason?: string /*掉品原因*/;
  estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
  expireDays?: number /*失效期限*/;
  expireEndTime?: string /*有效期结束时间*/;
  expireStartTime?: string /*有效期开始时间*/;
  favorableRate?: string /*好评率*/;
  favorableRateRefreshTime?: string /*好评率刷新时间*/;
  finishedRate?: string /*保量预估GMV比例*/;
  firstCateParentId?: string /*一级类目id*/;
  frameworkCoopModel?: Array<
    'GUARANTEED_GMV_MODE' | 'GUARANTEED_LIVE_ROUND_MODE' | 'GUARANTEED_SLICE_MODE'
  > /*合作模式多选：1保 GMV模式，2保场次排期模式，3前两种多选[CooperationFrameworkCoopModelEnum]*/;
  frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
  frameworkRoundFlag?: boolean /*是否计入年框场次*/;
  generationMethod?: string /*生成方式*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*更新时间*/;
  gmvCommissionRate?: string /*年框GMV总分佣比例*/;
  gmvPlatformCommissionRate?: string /*年框GMV平台分佣比例*/;
  goodsQualityScore?: string /*商品质量分*/;
  goodsQualityScoreApprovalProcessNo?: string /*商品质量分审批流程编号*/;
  goodsQualityScoreProcessUid?: string /*商品质量分OA流程id*/;
  groupId?: string /*项目组ID*/;
  groupName?: string /*项目组名称*/;
  guaranteeBrandFeeRate?: string /*保量基础佣金*/;
  guaranteeProportionFlag?: boolean /*是否保比*/;
  guaranteeProportionId?: string /*保比记录id*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  guaranteeQuantityId?: string /*保量id*/;
  hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
  highRiskGrant?: boolean /*高风险特批 0：否 1：是*/;
  id?: string /*场次货盘ID*/;
  image?: string /*商品主图链接*/;
  institutionId?: string /*机构id*/;
  interestPoints?: string /*利益点*/;
  investmentId?: string /*招商计划ID*/;
  investmentNo?: string /*招商计划编号*/;
  investmentTitle?: string /*招商计划名称*/;
  isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
  isGuarantee?: boolean /*是否担保交易 false：不担保 true：担保*/;
  isPriceProtection?: boolean /*商品价格保护 0：否 1：是*/;
  isSampleProvided?: boolean /*样品提供*/;
  labelConfigInfo?: Array<{
    labelName?: string /*标签名称*/;
    labelOptionId?: string /*标签选项配置id*/;
    labelOptionName?: string /*标签选项名称*/;
  }> /*标签配置信息*/;
  labelList?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*id*/;
    labelGroupName?: string /*标签名称*/;
    labelOptionList?: Array<{
      id?: string /*id*/;
      labelOption?: string /*标签内容名称*/;
    }> /*标签内容*/;
    requiredFlag?: number /* 0-非必填 1-必填*/;
    status?: 'ENABLE' | 'DISABLE' /*ENABLE-启用 UNABLE-禁用[LabelStatusEnum]*/;
  }> /*标签*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalAuditorName?: string /*法务审核人名称*/;
  legalReason?: string /*法务驳回原因*/;
  legalStatus?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*法务审核状态[SelectionQualificationRiskLevel]*/;
  link?: string /*商品参考链接*/;
  linkBak?: string /*商品参考链接（手动补充）*/;
  linkCheckRemark?: string /*链接确认备注*/;
  linkCheckStatus?:
    | 'WAIT_CHECK'
    | 'NO_PROMOTION'
    | 'CAN_PROMOTION' /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  liveDate?: string /*场次ID*/;
  liveMaxPrice?: string /*最大价 */;
  liveMinPrice?: string /*最小价*/;
  livePlatformSpuId?: string /*上播商品id*/;
  liveRoomId?: string /*直播间id*/;
  liveRoundId?: string /*场次ID*/;
  liveRoundInfo?: {
    anchorType?:
      | 'PRIMARY_ANCHOR'
      | 'SECONDARY_ANCHOR'
      | 'TALENT_ANCHOR' /*主播类型[AnchorTypeEnum]*/;
    liveDate?: string /*直播日期*/;
    liveEndTime?: string /*直播结束时间*/;
    liveRoomId?: string /*直播间Id*/;
    liveRoomImg?: string /*直播间头像*/;
    liveRoomName?: string /*直播间名称*/;
    liveRoundId?: string /*直播场次ID*/;
    liveRoundName?: string /*直播场次名称*/;
    liveStartTime?: string /*直播开始时间*/;
    subject?: string /*场次主题*/;
  } /*场次信息*/;
  liveServiceType?: string /*直播服务类型(讲解类型)*/;
  liveServiceTypeId?: string /*直播服务类型ID*/;
  logisticsContent?: string /*物流信息，json对象*/;
  lowCommissionAuditStatus?:
    | 'NONE'
    | 'INIT'
    | 'PENDING'
    | 'APPROVED'
    | 'REJECTED'
    | 'CANCELED'
    | 'DELETED'
    | 'REVERTED'
    | 'OVERTIME_CLOSE'
    | 'OVERTIME_RECOVER' /*低佣审核状态[LowCommissionAuditStatusEnum]*/;
  lowCommissionAuditTime?: string /*低佣审核时间*/;
  lowCommissionAuditor?: string /*低佣审核人*/;
  lowCommissionAuditorName?: string /*低佣审核姓名*/;
  lowCommissionFlowNo?: string /*低佣审核流程编号*/;
  lowCommissionUniqueCode?: string /*低佣请求id*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxPrice?: string /*最大价 */;
  minPrice?: string /*最小价*/;
  needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
  ninetyDayLiveCount?: string /*90天已播场次数*/;
  ninetyDayLiveZeroCount?: string /*90天内销量=0的场次数*/;
  ninetyDayLiveZeroRate?: string /*低效品占比*/;
  no?: string /*编号, CCHP+TB*/;
  noQualificationRequired?: string /*是否免资质, 0不免除，1免除*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorAuditorName?: string /*运营审核人名称*/;
  operatorReason?: string /*运营驳回原因*/;
  operatorStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  orderProcessStatus?: string /*单据处理状态 生成中： Generating ，已生成： generated ，待关闭 Wait_Close */;
  paymentOrderStatus?:
    | 'NO_PAYMENT_REQUIRED'
    | 'PENDING_PAYMENT'
    | 'PAYING'
    | 'PARTIAL_PAYMENT'
    | 'PAYMENT_FAIL'
    | 'FULL_PAYMENT'
    | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
  paymentVoucher?: {
    comment?: string /*备注*/;
    confirmedDate?: string /*确认时间*/;
    confirmedName?: string /*财务名称*/;
    confirmedPerson?: string /*确认人*/;
    cooperationOrderId?: string /*合作订单id*/;
    gmtCreated?: string /*创建时间*/;
    id?: string;
    ourMainCompany?: string /*我方收款主体*/;
    paymentAmount?: string /*回款总金额*/;
    paymentDate?: string /*付款日期*/;
    paymentFileList?: Array<string> /*付款凭证文件*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderId?: string /*合作订单付款订单id*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentReason?: string /*付款凭证问题原因*/;
    paymentStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态 待确认 已确认 有问题[PaymentVoucherStatus]*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    supplierMainCompany?: string /*商家付款主体*/;
  } /*付款凭证信息*/;
  platformShopId?: string /*平台店铺ID(抖店/快手等店铺ID)*/;
  platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*商品来源平台[PlatformEnum]*/;
  platformSpuId?: string /*平台商品ID*/;
  platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
  playBackStatus?: 'PLAYED' | 'NOT_PLAYER' /*上播状态[PlayBackStatusEnum]*/;
  preCreateCoopOrder?: boolean /*是否需要前置生成合作订单*/;
  preSaleStock?: number /*预售库存*/;
  price?: string /*日常价*/;
  promotionLink?: string /*推广链接*/;
  promotionLinkBak?: string /*推广链接(手动补充)*/;
  promotionLinkId?: string /*推广链接id*/;
  qualityScoreAuditStatus?:
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT' /*质量分审核状态[QualityScoreAuditStatusEnum]*/;
  qualityScoreAuditor?: string /*商品质量分审批人*/;
  reasonDetail?: string /*详细原因*/;
  reasonType?:
    | 'BREAK_PRICE_REDUCE_COMMISSION'
    | 'BRAND_FEE_AFFECTS'
    | 'SUPPLEMENT_LIVE'
    | 'QUANTITY_GUARANTEED'
    | 'HIS_COMMERCIAL_CLAUSE_REPORTING_ERROR'
    | 'BRAND_REDUCE_COMMISSION'
    | 'FRAMEWORK_COOPERATION'
    | 'WELFARE_GOODS'
    | 'OTHER_LESS_THAN_LUO'
    | 'OTHER' /*低佣原因[LowCommissionReasonTypeEnum]*/;
  receiveSampleRegister?: string /*收样登记*/;
  resourceUrls?: Array<{
    resourceId?: string /*链接附件id*/;
    resourceUrl?: string /*链接附件url*/;
  }> /*链接附件列表*/;
  returnSampleRegister?: string /*退样登记*/;
  reverseDemandId?: string /*反需人员Id*/;
  reverseDemandName?: string /*反需人员姓名*/;
  reverseDemandTag?: boolean /*反需标签,0:否,1:是*/;
  roundTotalFees?: string /*场次费用*/;
  salePrice?: string /*到手价 */;
  sectionFee?: string /*切片费*/;
  selectGoodsPoolNo?: string /*选品池编号*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionAuditorName?: string /*选品审核人名称*/;
  selectionLabel?: string /*选品标签*/;
  selectionReason?: string /*选品驳回原因*/;
  selectionRemark?: string /*选品备注*/;
  selectionRoundAdjustRecordList?: Array<{
    adjustReason?: string /*调整原因*/;
    adjustSource?: 'COOP_ORDER_ADJUST' /*记录来源：COOP_ORDER_ADJUST:合作调整单[SelectionRoundAdjustRecordAdjustSource]*/;
    adjustType?:
      | 'ADJUST_SERVICE_FEE'
      | 'ADJUST_CHANGE_BINDING' /*调整类型ADJUST_SERVICE_FEE：调整基础服务费，ADJUST_CHANGE_BINDING：调整解绑支付单[SelectionRoundAdjustRecordAdjustType]*/;
    afterBrandFee?: string /*调整后基础服务费*/;
    approvalProcessNo?: string /*审批流程编号*/;
    beforeBrandFee?: string /*调整前基础服务费*/;
    bizOrderId?: string /*来源单据ID*/;
    coopOrderId?: string /*合作订单主键*/;
    coopOrderNo?: string /*合作订单号*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键ID*/;
    modifier?: string /*修改人*/;
    requestUniqueCode?: string /*请求唯一编号*/;
    selectionRoundId?: string /*场次货盘ID*/;
    selectionRoundNo?: string /*场次货盘编号*/;
    status?:
      | 'INIT'
      | 'PASS'
      | 'REJECT' /*审核状态，INIT：待审核、PASS：通过、REJECT：驳回[SelectionRoundAdjustRecordStatus]*/;
    targetCoopOrderId?: string /*目标合作订单id*/;
    targetCoopOrderNo?: string /*目标合作订单号*/;
    targetSelectionRoundId?: string /*目标场次货盘ID*/;
    targetSelectionRoundNo?: string /*目标场次货盘编号*/;
  }> /*商务条款变更信息*/;
  selectionStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  sellingPoints?: string /*商品主要卖点*/;
  serviceAgreementId?: string /*服务协议id*/;
  shelfLife?: string /*食品保质期*/;
  shopId?: string /*店铺ID*/;
  shopPoints?: string /*店铺分*/;
  shopPointsRefreshTime?: string /*店铺分刷新时间*/;
  skus?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    purchasePrice?: string /*采购价（含税）*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
    taxRate?: string /*税率（%）*/;
  }> /*sku信息*/;
  soldSalesVolume?: string /*已售销量*/;
  sourceOrderNo?: string /*来源单据编码*/;
  sourceOrderType?: string /*来源单据类型*/;
  specQualificationVersion?: string /*采买链路资质版本ID, 无论是否采买均绑定一个采买链路版本, 用于品牌类型切换处理*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  specialAuditApprovalProcessNo?: string /*资质特批-审批流程编号*/;
  specialAuditId?: string /*资质特批Id*/;
  specialAuditProcessUid?: string /*资质特批Uid*/;
  specialAuditStatus?:
    | 'WAIT_AUDIT'
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT'
    | 'WITHDRAW' /*资质特批状态[SpecialAuditStatus]*/;
  specialAuditType?: 'ONLINE' | 'OFFLINE' | 'HIGH_SPECIAL' /*特批类型[SpecialAuditTypeEnum]*/;
  specialAuditorRecordList?: Array<{
    auditTime?: string /*审批时间*/;
    auditor?: string /*审批人名称*/;
    auditorId?: string /*审批人ID*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    level?: string /*审批级别*/;
    modifier?: string /*修改人*/;
    rejectReason?: string /*驳回原因*/;
    roleType?:
      | 'BUSINESS'
      | 'SELECTION'
      | 'OPERATE'
      | 'MIDDLE_GROUND'
      | 'LEGAL'
      | 'ANCHOR' /*角色类型[RoleTypeEnum]*/;
    status?: string /*状态*/;
  }> /*资质特批审核人记录*/;
  spuFocus?: string /*重点展示需求*/;
  spuFocusResources?: Array<{
    resourceId?: string /*链接附件id*/;
    resourceUrl?: string /*链接附件url*/;
  }> /*重点展示需求附件*/;
  spuId?: string /*spu id*/;
  spuName?: string /*spu名称*/;
  spuNo?: string /*spu编号*/;
  spuSource?: string /*商品来源：SUPPLIER_SPU、CAPTAIN_ACTIVITY_SPU、KUAISHOU_CAPTAIN_ACTIVITY_SPU ...*/;
  standardCateId?: string /*行业大类ID*/;
  standardCateName?: string /*行业大类名称*/;
  standardFavorableRate?: string /*标准好评率*/;
  standardStore?: string /*标准店铺分*/;
  status?:
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED' /*场次货盘状态[SelectionRoundStatus]*/;
  suggestCommission?: string /*建议佣金*/;
  supplierBodySpecialAuditApprovalProcessNo?: string /*主体特批-审批流程编号*/;
  supplierBodySpecialAuditId?: string /*主体特批Id*/;
  supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
  supplierBodySpecialAuditStatus?:
    | 'WAIT_AUDIT'
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT'
    | 'INVALID' /*主体特批状态[SupplierBodySpecialAuditStatusEnum]*/;
  supplierBodySpecialAuditorRecordList?: Array<{
    auditTime?: string /*审批时间*/;
    auditor?: string /*审批人名称*/;
    auditorId?: string /*审批人ID*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    level?: string /*审批级别*/;
    modifier?: string /*修改人*/;
    rejectReason?: string /*驳回原因*/;
    roleType?:
      | 'BUSINESS'
      | 'SELECTION'
      | 'OPERATE'
      | 'MIDDLE_GROUND'
      | 'LEGAL'
      | 'ANCHOR' /*角色类型[RoleTypeEnum]*/;
    status?: string /*状态*/;
  }> /*主体特批审核人记录*/;
  supplierBodySpecialProcessUid?: string /*主体特批流程id*/;
  supplierId?: string /*商家ID*/;
  supplierOrgId?: string /*商家主体快照ID*/;
  supplierOrgName?: string /*商家主体名称*/;
  supplierStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  supplySource?:
    | 'HOME_BRED'
    | 'CROSS_BORDER'
    | 'IMPORT' /*来源，国产、跨境、进口[SupplySourceEnum]*/;
  talentId?: string /*达人ID*/;
  talentNo?: string /*达人编号*/;
  totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
  totalCommissionRate?: string /*总佣金*/;
  version?: number /*版本号*/;
};

/**
 *场次货盘详情
 */
export const liveGoodsInfo = (params: LiveGoodsInfoRequest) => {
  return Fetch<ResponseWithResult<LiveGoodsInfoResult>>('/iasm/public/selection/detail', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/detail') },
  });
};

export type GetXpRefreshFavorableRateRequest = {
  id?: string /*业务ID*/;
};

export type GetXpRefreshFavorableRateResult = {
  favorableRate?: string /*好评率*/;
  favorableRateRefreshTime?: string /*好评率刷新时间*/;
  shopPoints?: string /*店铺分*/;
  shopPointsRefreshTime?: string /*店铺分刷新时间*/;
};

/**
 *选品池-刷新好评率
 */
export const getXpRefreshFavorableRate = (params: GetXpRefreshFavorableRateRequest) => {
  return Fetch<ResponseWithResult<GetXpRefreshFavorableRateResult>>(
    '/pim/public/selectGoodsPool/refreshFavorableRate',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/selectGoodsPool/refreshFavorableRate') },
    },
  );
};

export type GetXpRefreshShopPointsRequest = {
  id?: string /*业务ID*/;
};

export type GetXpRefreshShopPointsResult = {
  favorableRate?: string /*好评率*/;
  favorableRateRefreshTime?: string /*好评率刷新时间*/;
  shopPoints?: string /*店铺分*/;
  shopPointsRefreshTime?: string /*店铺分刷新时间*/;
};

/**
 *选品池-刷新店铺体验分
 */
export const getXpRefreshShopPoints = (params: GetXpRefreshShopPointsRequest) => {
  return Fetch<ResponseWithResult<GetXpRefreshShopPointsResult>>(
    '/pim/public/selectGoodsPool/refreshShopPoints',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/selectGoodsPool/refreshShopPoints') },
    },
  );
};

export type GetCcRefreshFavorableRateRequest = {
  id?: string /*业务ID*/;
};

export type GetCcRefreshFavorableRateResult = {
  favorableRate?: string /*好评率*/;
  favorableRateRefreshTime?: string /*好评率刷新时间*/;
  shopPoints?: string /*店铺分*/;
  shopPointsRefreshTime?: string /*店铺分刷新时间*/;
};

/**
 *场次货盘-刷新好评率
 */
export const getCcRefreshFavorableRate = (params: GetCcRefreshFavorableRateRequest) => {
  return Fetch<ResponseWithResult<GetCcRefreshFavorableRateResult>>(
    '/iasm/public/selection/refreshFavorableRate',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/refreshFavorableRate') },
    },
  );
};

export type GetCcRefreshShopPointsRequest = {
  id?: string /*业务ID*/;
};

export type GetCcRefreshShopPointsResult = {
  favorableRate?: string /*好评率*/;
  favorableRateRefreshTime?: string /*好评率刷新时间*/;
  shopPoints?: string /*店铺分*/;
  shopPointsRefreshTime?: string /*店铺分刷新时间*/;
};

/**
 *场次货盘-刷新店铺体验分
 */
export const getCcRefreshShopPoints = (params: GetCcRefreshShopPointsRequest) => {
  return Fetch<ResponseWithResult<GetCcRefreshShopPointsResult>>(
    '/iasm/public/selection/refreshShopPoints',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/refreshShopPoints') },
    },
  );
};

export type ListAllLiveRoomRequest = {
  deptPlatformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台 DY:抖音 TB：淘宝 JD:京东 PDD:拼多多 KS:快手[PlatformEnum]*/;
  isVisible?: boolean /*是否需要权限*/;
  liveRoomName?: string /*直播间名称*/;
};

export type ListAllLiveRoomResult = {
  treeDataList?: Array<{
    deptId?: string /*事业部id*/;
    deptName?: string /*事业部名称*/;
    liveRoomList?: Array<{
      liveRoomId?: string /*直播间id*/;
      liveRoomName?: string /*直播间名称*/;
      platform?: string /*平台*/;
    }> /*直播间列表*/;
    platform?: string /*平台*/;
  }> /*数据*/;
};

/**
 *获取事业部、直播间二级关联
 */
export const listAllLiveRoom = (params: ListAllLiveRoomRequest) => {
  return Fetch<ResponseWithResult<ListAllLiveRoomResult>>('/iasm/public/dept/getDeptTree', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/dept/getDeptTree') },
  });
};

export type CascadeListRequest = {
  deptId?: string /*事业部id*/;
  labelStatus?: 'ENABLE' | 'DISABLE' /*状态[LabelStatusEnum]*/;
  talentIdList?: Array<string> /*达人id*/;
};

export type CascadeListResult = {
  cascadeList?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*标签组id*/;
    labelGroupName?: string /*标签组名称*/;
    labelOptionList?: Array<{
      labelGroupId?: string /*父级id*/;
      labelOption?: string /*选项名称*/;
      optionId?: string /*id*/;
    }> /*二级目录*/;
    requiredFlag?: number /* 0-非必填 1-必填*/;
    status?: 'ENABLE' | 'DISABLE' /*ENABLE-启用 UNABLE-禁用[LabelStatusEnum]*/;
  }> /*级联数据*/;
};

/**
 *标签级联列表
 */
export const cascadeList = (params: CascadeListRequest) => {
  return Fetch<ResponseWithResult<CascadeListResult>>('/iasm/public/label/cascadeList', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/label/cascadeList') },
  });
};

export type BpConfirmRequest = {
  frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
  frameworkRoundFlag?: boolean /*是否计入年框场次*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  id?: string /*id*/;
  liveServiceTypeId?: string /*直播服务类型id*/;
  roundTotalFees?: string /*年框佣金*/;
  version?: number /*版本号*/;
};

export type BpConfirmResult = boolean;

/**
 *商务确认
 */
export const bpConfirm = (params: BpConfirmRequest) => {
  return Fetch<ResponseWithResult<BpConfirmResult>>('/iasm/public/selection/bpConfirm', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/bpConfirm') },
  });
};

export type BpCancleRequest = {
  id?: string /*id*/;
  reason?: string /*原因*/;
  version?: number /*版本号*/;
};

export type BpCancleResult = boolean;

/**
 *商务取消
 */
export const bpCancle = (params: BpCancleRequest) => {
  return Fetch<ResponseWithResult<BpCancleResult>>('/iasm/public/selection/bpCancel', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/bpCancel') },
  });
};

export type BpEditRequest = {
  afterSaleContent?: {
    freshCompensation?: string /*（生鲜）赔付标准 文本 行业大类为生鲜时展示该字段；提示：请填写赔付标准和有效期，例如：确认收货后30个工作日内，坏果坏1赔1，超过50%全赔*/;
    freshIsCold?: boolean /* （生鲜）是否冷链发货 true：是，false：否*/;
    installationService?: string /* （数码家电）售后附加服务 文本 行业大类为数码家电时展示该字段；提示：免人工安装费用，额外收取xx元材料费，支持延迟发货*/;
    insuranceTime?: string /* （数码家电）质保时长  文本 行业大类为数码家电时展示该字段*/;
    modes?: Array<
      'NONE' | 'NO_REASON' | 'FREE_FREIGHT_INSURANCE' | 'OTHER'
    > /* 售后服务 多选数组 必填，选项：无、7天无理由、赠送运费险、其他[AfterSaleTypeEnum]*/;
    others?: string /* 其他售后服务文本 必填，售后服务选其他时，必填该字段*/;
    productionDate?: string /* 生产日期 文本 行业大类为食品饮料、美妆护肤则展示，例如：晚于2023年6月*/;
    shelfLife?: string /* 产品保质期 文本 行业大类为食品饮料、美妆护肤则展示，例如：6个月、避光保存7天*/;
  } /*售后信息*/;
  brandFee?: string /*基础服务费（基础服务费）*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  comment?: string /*备注*/;
  commissionRate?: string /*线上佣金比例*/;
  commissionRateOffline?: string /*线下佣金比例*/;
  discountContent?: {
    giftInfos?: Array<{
      condition?:
        | 'IMMEDIATE'
        | 'REQUIREMENTS' /*赠送条件，下单即送、指定条件赠送[GiftConditionTypeEnum]*/;
      conditionRemark?: string /* 赠送条件备注*/;
      giftType?: 'ALL' | 'PART' /*赠品生效规格[GiftTypeEnum]*/;
      image?: string /*赠品图片*/;
      link?: string /*赠品链接*/;
      name?: string /*赠品名称*/;
      onSale?: boolean /*是否在售*/;
      price?: string /*赠品价格*/;
      skuNoList?: Array<string> /*赠品生效的sku编号*/;
    }> /*赠品信息*/;
    remark?: string /* 优惠备注*/;
    type?: Array<
      'FULL_REDUCTION' | 'IMMEDIATE_REDUCTION' | 'COUPON' | 'OTHER' | 'NONE'
    > /* 优惠方式, 必填，选项：满减、立减、优惠券、其他、无（该选项和其他选项互斥，选了无则不允许选其他选项[DiscountTypeEnum]*/;
  } /*优惠信息*/;
  id?: string /*id*/;
  labelConfigInfo?: Array<{
    labelName?: string /*标签名称*/;
    labelOptionId?: string /*标签选项配置id*/;
    labelOptionName?: string /*标签选项名称*/;
  }> /*标签配置信息*/;
  labelOptionContent?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*id*/;
    labelGroupName?: string /*标签名称*/;
    labelOptionList?: Array<{
      id?: string /*id*/;
      labelOption?: string /*标签内容名称*/;
    }> /*标签选项*/;
    requiredFlag?: boolean /* false-非必填 true-必填*/;
  }> /*标签内容*/;
  liveServiceTypeId?: string /*直播服务类型id*/;
  logisticsContent?: {
    deliveryCycle?: string /* 发货周期, 必填，xx天内发货，默认2天*/;
    deliveryMode?: 'SPOT' | 'PRESALE' /* 发货模式, 必填，现货/预售[DeliveryModeEnum]*/;
    giftDeliveryMode?: boolean /* 主品赠品发货情况, 赠品是否随主品一起发货，true：是，false：否*/;
    giftLogisticsNo?: string /* 赠品寄样单号，如果选择 主赠分别发货 显示本字段*/;
    logisticsNo?: string /* 主品寄样单号*/;
  } /*物流信息*/;
  sectionFee?: string /*切片费*/;
  sellingPoints?: string /*商品主要卖点*/;
  skus?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
  }> /*sku信息*/;
  spuFocus?: string /*重点展示需求*/;
  version?: number /*版本号*/;
};

export type BpEditResult = boolean;

/**
 *商务编辑
 */
export const bpEdit = (params: BpEditRequest) => {
  return Fetch<ResponseWithResult<BpEditResult>>('/iasm/public/selection/bpEdit', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/bpEdit') },
  });
};

export type RoleDelRequest = {
  dropProductReasonType?: string /*掉品原因类型*/;
  id?: string /*id*/;
  reason?: string /*原因*/;
  version?: number /*版本号*/;
};

export type RoleDelResult = boolean;

/**
 *运营选品商务总监掉品
 */
export const roleDel = (params: RoleDelRequest) => {
  return Fetch<ResponseWithResult<RoleDelResult>>('/iasm/public/selection/droppedProduct', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/droppedProduct') },
  });
};

export type OpConfirmRequest = {
  id?: string /*id*/;
  version?: number /*版本号*/;
};

export type OpConfirmResult = boolean;

/**
 *运营确认
 */
export const opConfirm = (params: OpConfirmRequest) => {
  return Fetch<ResponseWithResult<OpConfirmResult>>('/iasm/public/selection/operatorConfirm', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/operatorConfirm') },
  });
};

export type ChConfirmRequest = {
  id?: string /*id*/;
  version?: number /*版本号*/;
};

export type ChConfirmResult = boolean;

/**
 *选品确认
 */
export const chConfirm = (params: ChConfirmRequest) => {
  return Fetch<ResponseWithResult<ChConfirmResult>>('/iasm/public/selection/selectionConfirm', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/selectionConfirm') },
  });
};

export type ChEditRequest = {
  depositAmount?: string /*定金金额*/;
  id?: string /*id*/;
  labelConfigInfo?: Array<{
    labelName?: string /*标签名称*/;
    labelOptionId?: string /*标签选项配置id*/;
    labelOptionName?: string /*标签选项名称*/;
  }> /*标签配置信息*/;
  labelOptionContent?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*id*/;
    labelGroupName?: string /*标签名称*/;
    labelOptionList?: Array<{
      id?: string /*id*/;
      labelOption?: string /*标签内容名称*/;
    }> /*标签选项*/;
    requiredFlag?: boolean /* false-非必填 true-必填*/;
  }> /*标签内容*/;
  liveServiceTypeId?: string /*直播服务类型id*/;
  receiveSampleRegister?: string /*收样登记*/;
  reverseDemandId?: string /*反需人员*/;
  reverseDemandTag?: boolean /*反需标签,false:否,true:是*/;
  selectionLabel?: string /*选品标签*/;
  selectionRemark?: string /*选品备注*/;
  sellingPoints?: string /*商品主要卖点*/;
  spuFocus?: string /*重点展示需求*/;
  spuFocusResourceList?: Array<string> /*重点展示需求附件*/;
  version?: number /*版本号*/;
};

export type ChEditResult = boolean;

/**
 *选品编辑
 */
export const chEdit = (params: ChEditRequest) => {
  return Fetch<ResponseWithResult<ChEditResult>>('/iasm/public/selection/selectionEdit', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/selectionEdit') },
  });
};

export type ApproveConfirmRequest = {
  id?: string /*场次货盘ID*/;
};

export type ApproveConfirmResult = boolean;

/**
 *高风险特批
 */
export const approveConfirm = (params: ApproveConfirmRequest) => {
  return Fetch<ResponseWithResult<ApproveConfirmResult>>('/iasm/public/selection/highRiskGrant', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/highRiskGrant') },
  });
};

export type AddSuppLinkRequest = {
  id?: string /*场次货盘ID*/;
  ids?: Array<string> /*场次货盘ID*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  livePlatformSpuId?: string /*上播商品id*/;
  livestreamLink?: string /*上播链接*/;
};

export type AddSuppLinkResult = boolean;

/**
 *补充链接
 */
export const addSuppLink = (params: AddSuppLinkRequest) => {
  return Fetch<ResponseWithResult<AddSuppLinkResult>>('/iasm/public/selection/addLinks', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/addLinks') },
  });
};

export type GetBatchrequestsRequest = {
  ids?: Array<string> /*场次货盘id集合*/;
};

export type GetBatchrequestsResult = boolean;

/**
 *批量自动获取上播链接
 */
export const getBatchrequests = (params: GetBatchrequestsRequest) => {
  return Fetch<ResponseWithResult<GetBatchrequestsResult>>(
    '/iasm/public/selection/batchAutoGetLinks',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/batchAutoGetLinks') },
    },
  );
};

export type GetOneLinkRequest = {
  id?: string /*业务ID*/;
};

export type GetOneLinkResult = {
  autoGetLinks?: boolean /*调用淘宝接口是否调用成功*/;
  msg?: string /*单个获取上播链接返回结果*/;
};

/**
 *请求链接
 */
export const getOneLink = (params: GetOneLinkRequest) => {
  return Fetch<ResponseWithResult<GetOneLinkResult>>('/iasm/public/selection/getOneLink', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/getOneLink') },
  });
};

export type GetGoodsLiveRoomRequest = {
  deptId?: string /*事业部id*/;
  platformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*类型[PlatformEnum]*/;
  selectGoodsPoolId?: string /*选品池id*/;
};

export type GetGoodsLiveRoomResult = Array<{
  avatar?: string /*头像*/;
  buId?: string /*事业部id*/;
  buName?: string /*事业部名称*/;
  id?: string /*id*/;
  name?: string /*直播间名称*/;
  openId?: string /*直播间openId*/;
  platform?: string /*平台*/;
  talentId?: string /*达人id*/;
}>;

/**
 *直播间列表(权限)-交集
 */
export const getGoodsLiveRoom = (params: GetGoodsLiveRoomRequest) => {
  return Fetch<ResponseWithResult<GetGoodsLiveRoomResult>>(
    '/iasm/public/web/liveRound/listIntersectionRoom',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/web/liveRound/listIntersectionRoom') },
    },
  );
};

export type CreateBySelectionRoundRequest = {
  selectionRoundId?: string /*场次货盘ID*/;
};

export type CreateBySelectionRoundResult = boolean;

/**
 *业务中台端创建请款单
 */
export const createBySelectionRound = (params: CreateBySelectionRoundRequest) => {
  return Fetch<ResponseWithResult<CreateBySelectionRoundResult>>(
    '/agreement/public/advancePaymentOrder/befriends/createBySelectionRound',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign(
          '/agreement/public/advancePaymentOrder/befriends/createBySelectionRound',
        ),
      },
    },
  );
};

export type QueryBusinessInfoRequest = {
  ids?: Array<string>;
};

export type QueryBusinessInfoResult = {
  resultList?: Array<{
    bpName?: string /* 商务名称*/;
    id?: string /* 选品池id*/;
  }> /* 商务信息列表*/;
};

/**
 *选品池查询商务信息
 */
export const queryBusinessInfo = (params: QueryBusinessInfoRequest) => {
  return Fetch<ResponseWithResult<QueryBusinessInfoResult>>(
    '/pim/public/selectGoodsPool/queryBusinessInfo',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/selectGoodsPool/queryBusinessInfo') },
    },
  );
};

export type SaveBpClaimsRequest = {
  bpId?: string;
  ids?: Array<string> /*选品池id集合*/;
};

export type SaveBpClaimsResult = Array<{
  failReason?: string /*失败原因*/;
  platformSpuId?: string /*平台商品ID*/;
  spuName?: string /*商品名称*/;
  spuNo?: string /*商品编号*/;
}>;

/**
 *商务认领
 */
export const saveBpClaims = (params: SaveBpClaimsRequest) => {
  return Fetch<ResponseWithResult<SaveBpClaimsResult>>('/pim/public/selectGoodsPool/bpClaims', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/pim/public/selectGoodsPool/bpClaims') },
  });
};

export type ActionLogListRequest = {
  bizOrderId?: string /*业务单据id*/;
  bizOrderNo?: string /*业务单据编号*/;
  bizOrderType?: string /*业务单据类型*/;
  bizOrderTypeList?: Array<string> /*业务单据类型*/;
  bizTypeModels?: Array<{
    bizOrderType?: string /*业务单据类型*/;
    secondBizTypes?: Array<string> /*二级业务单据类型*/;
  }> /*一二级业务单据类型集合*/;
  current?: number /*当前页码,从1开始*/;
  employeeIdList?: Array<string> /*用户id*/;
  operateDesc?: string /*操作类型描述*/;
  operateEndTime?: string /*操作结束时间*/;
  operateStartTime?: string /*操作开始时间*/;
  operateType?: string /*操作类型*/;
  operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
  operatorName?: string /*操作人名称*/;
  size?: number /*分页大小*/;
};

export type ActionLogListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    bizOrderId?: string /*业务单据id*/;
    bizOrderNo?: string /*业务单据编号*/;
    bizOrderType?: string /*业务单据类型*/;
    contentSnapshot?: string /*操作之前的快照*/;
    id?: string /*主键*/;
    operateDesc?: string /*操作类型*/;
    operateTime?: string /*操作时间*/;
    operateType?: string /*操作类型*/;
    operatorAccount?: string /*操作人账号*/;
    operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
    operatorContent?: string /*操作内容*/;
    operatorId?: string /*操作人id*/;
    operatorName?: string /*操作人姓名*/;
    secondBizType?: string /*二级业务单据类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *场次货盘详情内的相关日志分页查询操作
 */
export const actionLogList = (params: ActionLogListRequest) => {
  return Fetch<ResponseWithResult<ActionLogListResult>>(
    '/tools/public/operation/querySelectionRoundDetailLogList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/operation/querySelectionRoundDetailLogList') },
    },
  );
};

export type FwActionLogListRequest = {
  auditTypes?: Array<
    'MANUAL' | 'AUTO' | 'WHITE_LIST' | 'NON_LIVE_JOIN'
  > /*过审类型, MANUAL:人工审核, AUTO:自动过审, WHITE_LIST:白名单过审[QualificationAuditTypeEnum]*/;
  auditorList?: Array<string> /*审核人*/;
  bizTypeList?: Array<
    'LIVE' | 'SHOP_WINDOW' | 'VIDEO_CLIP' | 'SLICE'
  > /*业务类型[ServiceTypeEnum]*/;
  bpId?: string /*商务ID*/;
  brandId?: string /*品牌id*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部id*/;
  endAuditTime?: string /*法务审核时间(结束)*/;
  liveEndDate?: string /*直播日期区间-截止时间*/;
  liveRoomIds?: Array<string> /*直播间ID*/;
  liveStartDate?: string /*直播日期区间-开始时间*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  orderBy?: string /*排序字段*/;
  platformSpuIds?: Array<string> /*平台商品ID*/;
  riskLevels?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待审核-NONE[QualificationRiskLevelEnum]*/;
  selectionRoundNos?: Array<string> /*场次货盘编号*/;
  size?: number /*分页大小*/;
  spuNames?: Array<string> /*商品名称*/;
  spuNos?: Array<string> /*商品编号*/;
  startAuditTime?: string /*法务审核时间(开始)*/;
  supplierIds?: Array<string> /*商家ID*/;
};

export type FwActionLogListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    auditTime?: string /*法务审核时间*/;
    auditType?:
      | 'MANUAL'
      | 'AUTO'
      | 'WHITE_LIST'
      | 'NON_LIVE_JOIN' /*过审类型, MANUAL:人工审核, AUTO:自动过审, WHITE_LIST:白名单过审[QualificationAuditTypeEnum]*/;
    auditorName?: string /*审核人姓名*/;
    bizType?: 'LIVE' | 'SHOP_WINDOW' | 'VIDEO_CLIP' | 'SLICE' /*业务类型[ServiceTypeEnum]*/;
    bpName?: string /*商务姓名*/;
    brandName?: string /*品牌名称*/;
    brandQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*店铺品牌资质审核状态[QualificationAuditStateEnum]*/;
    gmtCreated?: string /*创建时间*/;
    id?: string /*主键*/;
    platformSpuId?: string /*平台商品ID*/;
    riskLevel?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待审核-NONE[QualificationRiskLevelEnum]*/;
    selectionRound?: {
      anchorType?:
        | 'PRIMARY_ANCHOR'
        | 'SECONDARY_ANCHOR'
        | 'TALENT_ANCHOR' /*直播间类型[AnchorTypeEnum]*/;
      applyChannel?:
        | 'INVESTMENT_PLAN_DIRECT'
        | 'INVESTMENT_PLAN_INVITE'
        | 'INVESTMENT_PLAN_LIVE_ROUND_INVITE' /*报名渠道，枚举项：招商计划；货盘邀请链接；选品邀请链接[ApplyChannelEnum]*/;
      bizType?: 'LIVE' | 'SHOP_WINDOW' | 'VIDEO_CLIP' | 'SLICE' /*业务类型[ServiceTypeEnum]*/;
      bpName?: string /*商务姓名*/;
      hasBrandFee?: boolean /*有无基础服务费*/;
      hasGifts?: boolean /*是否有赠品*/;
      id?: string /*关联记录主键*/;
      isSpec?: boolean /*是否采买*/;
      liveDateEnd?: string /*合作结束日期*/;
      liveDateStart?: string /*合作开始日期*/;
      liveRoomId?: string /*直播间Id*/;
      liveRoomImg?: string /*直播间头像*/;
      liveRoomName?: string /*直播间名称*/;
      liveRoundId?: string /*直播场次ID*/;
      liveRoundName?: string /*直播场次名称*/;
      luckyProductFlag?: boolean /*是否福袋商品*/;
      passRate?: string /*审核通过率-商务*/;
      promotionLink?: string /*上播链接*/;
      selectionRoundNo?: string /*场次货盘编号*/;
      serviceType?: string /*直播服务类型(讲解类型)*/;
      spuNo?: string /*商品编号*/;
    } /*合作信息*/;
    spuImage?: string /*商品图片*/;
    spuName?: string /*商品名称*/;
    spuNo?: string /*商品编号*/;
    spuQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*商品资质审核状态[QualificationAuditStateEnum]*/;
    supplierQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*商家资质审核状态[QualificationAuditStateEnum]*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *交个朋友端-法务审核记录分页查询（场次货盘维度）
 */
export const fwActionLogList = (params: FwActionLogListRequest) => {
  return Fetch<ResponseWithResult<FwActionLogListResult>>(
    '/pim/public/qualificationAudit/pageRecordForInst',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationAudit/pageRecordForInst') },
    },
  );
};

export type GoodsActionLogListRequest = {
  selectionRoundId?: string /*场次货盘Id*/;
};

export type GoodsActionLogListResult = Array<{
  operatorContent?: string /*操作内容*/;
  operatorNameInfo?: string /*操作人信息*/;
  operatorTime?: string /*创建时间*/;
  qualificationItemVersionId?: string /*资质项版本表主键ID*/;
}>;

/**
 *场次货盘详情内的相关资质历史版本分页查询操作
 */
export const goodsActionLogList = (params: GoodsActionLogListRequest) => {
  return Fetch<ResponseWithResult<GoodsActionLogListResult>>(
    '/pim/public/operation/querySelectionRoundDetailQualificationItemVersion',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign(
          '/pim/public/operation/querySelectionRoundDetailQualificationItemVersion',
        ),
      },
    },
  );
};

export type SelectionBatchAddRequest = {
  ids?: Array<string> /*场次货盘ID*/;
  liveDate?: string /*直播日期*/;
  liveRoomId?: string /*直播间ID*/;
  liveRoundId?: string /*直播场次*/;
  whetherIncludeBrandFee?: boolean /*是否包含基础服务费流程(大于0), true:包含(全部), false:不包含(纯佣)*/;
};

export type SelectionBatchAddResult = boolean;

/**
 *场次货盘批量加入到其他场次
 */
export const selectionBatchAdd = (params: SelectionBatchAddRequest) => {
  return Fetch<ResponseWithResult<SelectionBatchAddResult>>('/iasm/public/selection/batchAdd', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/batchAdd') },
  });
};

export type CheckBatchCopyAddRequest = {
  liveRoundId?: string /*场次id*/;
};

export type CheckBatchCopyAddResult = boolean;

/**
 *根据直播场次查询是否能复制本场次
 */
export const checkBatchCopyAdd = (params: CheckBatchCopyAddRequest) => {
  return Fetch<ResponseWithResult<CheckBatchCopyAddResult>>(
    '/iasm/public/selection/checkBatchCopyAdd',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/checkBatchCopyAdd') },
    },
  );
};

export type CheckBatchCopyGoodsListRequest = {
  liveRoundId?: string /*场次id*/;
};

export type CheckBatchCopyGoodsListResult = Array<{
  bpId?: string /*商务id*/;
  brandFee?: string /*基础服务费*/;
  hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
  id?: string /*场次货盘id*/;
  platformSpuId?: string /*平台商品id*/;
  spuName?: string /*商品名称*/;
}>;

/**
 *复制场次货盘-校验含基础服务费流程接口
 */
export const checkBatchCopyGoodsList = (params: CheckBatchCopyGoodsListRequest) => {
  return Fetch<ResponseWithResult<CheckBatchCopyGoodsListResult>>(
    '/iasm/public/selection/checkWhetherIncludeBrandFee',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/checkWhetherIncludeBrandFee') },
    },
  );
};

export type QueryBySelectionIdListRequest = {
  requestList?: Array<{
    id?: string /*IDS*/;
    needQualificationCompletion?: string /*是否后续补足资质 1 是，0 否*/;
    specialMaerial?: string /*是否特殊材质（1:是，0:否）*/;
    type?: string /*资质特批方式：线上特批（ONLINE），线下特批（OFFLINE）*/;
  }> /*查询场次货盘的类目特批有效期请求集合*/;
};

export type QueryBySelectionIdListResult = Array<{
  days?: string /*有效期天数*/;
  selectionRoundId?: string /*场次货盘id*/;
}>;

/**
 *查询场次货盘的类目特批有效期
 */
export const queryBySelectionIdList = (params: QueryBySelectionIdListRequest) => {
  return Fetch<ResponseWithResult<QueryBySelectionIdListResult>>(
    '/iasm/public/specialAuditCateExpireDate/queryBySelectionIdList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/iasm/public/specialAuditCateExpireDate/queryBySelectionIdList'),
      },
    },
  );
};

export type SelectionRoundListExportRequest = {
  agreementSignStatus?: boolean /*协议签署状态,已签署true,未签署false*/;
  bpId?: string /*商务ID*/;
  brandId?: string /*品牌id*/;
  endBrandFee?: string /*基础服务费基础服务费,结束*/;
  groupIds?: Array<string> /*项目组ID*/;
  legalStatus?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*法务审核状态 「风险等级」[SelectionQualificationRiskLevel]*/;
  liveRoundId?: string /*场次ID*/;
  liveServiceTypeIds?: Array<string> /*直播服务类型IDs*/;
  notUploadFlag?: boolean /*是否只显示未上传付款凭证数据,true未上传,false已上传*/;
  operatorAuditor?: string /*运营审核人ID*/;
  paymentVoucherStatus?:
    | 'WAIT_CONFIRM'
    | 'CONFIRMED'
    | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
  platformSpuIds?: Array<string> /*批量平台商品ID*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionNos?: Array<string> /*批量选品流程编号*/;
  shopName?: string /*店铺名称*/;
  spuNames?: Array<string> /*批量商品名称*/;
  spuNo?: Array<string> /*批量spu编号*/;
  startBrandFee?: string /*基础服务费基础服务费,开始*/;
  statusList?: Array<
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED'
  > /*状态[SelectionRoundStatus]*/;
  supplierConfirming?: boolean /*商家确认状态,true已确认,false未确认*/;
  supplierId?: string /*商家id*/;
};

export type SelectionRoundListExportResult = string;

/**
 *场次货盘列表导出 按直播场次查询
 */
export const selectionRoundListExport = (params: SelectionRoundListExportRequest) => {
  return Fetch<ResponseWithResult<SelectionRoundListExportResult>>(
    '/iasm/public/selection/selectionRoundListExport',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/selectionRoundListExport') },
    },
  );
};

export type DroppedProductRepeatRequest = {
  id?: string /*id*/;
};

export type DroppedProductRepeatResult = boolean;

/**
 *掉品复播
 */
export const droppedProductRepeat = (params: DroppedProductRepeatRequest) => {
  return Fetch<ResponseWithResult<DroppedProductRepeatResult>>(
    '/iasm/public/selection/droppedProductRepeat',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/droppedProductRepeat') },
    },
  );
};

export type PaymentVoucherSaveRequest = {
  cooperationOrderId?: string /*合作订单id*/;
  id?: string;
  paymentFileList?: Array<string> /*付款凭证文件*/;
  paymentOrderId?: string /*合作订单付款订单id*/;
  selectRoundId?: string /*场次货盘id*/;
};

export type PaymentVoucherSaveResult = boolean;

/**
 *上传付款凭证保存
 */
export const paymentVoucherSave = (params: PaymentVoucherSaveRequest) => {
  return Fetch<ResponseWithResult<PaymentVoucherSaveResult>>('/iasm/public/paymentVoucher/save', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/paymentVoucher/save') },
  });
};

export type PaymentVoucherConfirmedRequest = {
  paymentVoucherId?: string /*id*/;
  reason?: string /*问题原因*/;
};

export type PaymentVoucherConfirmedResult = boolean;

/**
 *财务确认问题
 */
export const paymentVoucherConfirmed = (params: PaymentVoucherConfirmedRequest) => {
  return Fetch<ResponseWithResult<PaymentVoucherConfirmedResult>>(
    '/iasm/public/paymentVoucher/confirmedProblem',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/paymentVoucher/confirmedProblem') },
    },
  );
};

export type PaymentVoucherConfirmRequest = {
  comment?: string /*备注*/;
  id?: string;
  ourMainCompany?: string /*我方收款主体*/;
  paymentAmount?: string /*回款总金额*/;
  paymentDate?: string /*付款日期*/;
  supplierMainCompany?: string /*商家付款主体*/;
};

export type PaymentVoucherConfirmResult = boolean;

/**
 *确认付款凭证
 */
export const paymentVoucherConfirm = (params: PaymentVoucherConfirmRequest) => {
  return Fetch<ResponseWithResult<PaymentVoucherConfirmResult>>(
    '/iasm/public/paymentVoucher/confirm',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/paymentVoucher/confirm') },
    },
  );
};
