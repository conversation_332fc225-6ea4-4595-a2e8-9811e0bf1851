import React, { useEffect, useState, useImperativeHandle } from 'react';
import { Button, Modal, Spin, Card, Avatar, message, Icon } from 'antd';
import { Document, Page } from 'react-pdf';

import { getUrlList, signAgreement } from 'web-common-modules/yml/pdf/index';
type PropsType = {
  id?: boolean;
  type?: string; //查看设置为look,签署设置为sign
  onRef?: any;
  serviceAgreementId?: string;
  institutionName?: string;
  promptText?: string;
  refresh?: any;
};
const { Meta } = Card;
const { confirm } = Modal;
const PreviewPDF: React.FC<PropsType> = React.forwardRef((props) => {
  const { id, type, onRef, institutionName, promptText, serviceAgreementId, ...rest } = props;
  // console.log(props);
  const [visible, setVisible] = useState(false);
  const [scrollBottom, setScrollBottom] = useState(false);
  const [numPages, setNumPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(true);
  const getUrl = () => {
    const params = { accessTerm: 'FRONT', resourceIds: [id] };
    setLoading(true);
    getUrlList({ ...params })
      .then((res) => {
        console.log(res.res);
        if (res?.res?.code === '200') {
          setUrl(res?.res?.result[id]);
          setTimeout(() => {
            setLoading(false);
          }, 200);
        }
      })
      .catch((err) => {
        setLoading(false);
      });
  };
  useEffect(() => {
    getUrl();
    setSignLoading(false);
  }, [id]);
  useImperativeHandle(onRef, () => {
    return {
      onOpen,
    };
  });
  const onOpen = () => {
    setVisible(true);
    // getUrl();
  };

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // console.dir(e.target);
    // console.log(scrollTop, scrollHeight, clientHeight, scrollTop + clientHeight - scrollHeight);
    if (Math.abs(scrollTop + clientHeight - scrollHeight) < 200) {
      setScrollBottom(true);
    }
  };

  const closeModal = () => {
    setVisible(false);
    // props.onClose && props.onClose();
  };
  const [signLoading, setSignLoading] = useState(false);
  const handleConfirm = () => {
    // 处理用户点击 "我已阅读" 的逻辑
    // 可以在这里触发相关操作，如保存用户阅读状态等
    console.log(serviceAgreementId);
    setSignLoading(true);
    signAgreement({ serviceAgreementId })
      .then((res) => {
        setSignLoading(false);
        // console.log(res?.res);
        if (res?.res?.code === '200') {
          message.success('文件签署成功');

          setVisible(false);
          props?.refresh && props?.refresh();
        } else if (res?.res?.code === 'SUPPLIER_NOT_REAL_NAME') {
          confirm({
            icon: <Icon type="exclamation-circle"></Icon>, // 使用自定义样式的图标
            content: (
              <div>
                <p>还未进行实名认证，请进行实名认证后在报名。</p>
              </div>
            ),
            onOk() {
              gotoSign();
            },

            okText: '去认证',
          });
        } else {
          message.warn(res?.res?.message);
        }
      })
      .catch((err) => {
        setSignLoading(false);
        console.log(err);
      });
    // closeModal();
  };
  const gotoSign = () => {
    const loginInfo =
      localStorage.getItem('s2b-supplier@login') &&
      JSON.parse(localStorage.getItem('s2b-supplier@login'));
    loginInfo.auditState = 0;
    localStorage.setItem('s2b-supplier@login', JSON.stringify(loginInfo));
    history.push('/shop-process');
    setTimeout(() => {
      location.reload();
    }, 500);
  };
  const onDocumentLoadSuccess = ({ numPages }) => {
    console.log(numPages);
    setNumPages(numPages);
  };
  const downLoad = () => {
    downloadFile(url, '协议');
  };
  const downloadFile = (url, fileName) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const blobURL = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobURL;
        link.download = fileName;
        link.click();
        URL.revokeObjectURL(blobURL);
      })
      .catch((error) => {
        console.error('下载文件时出错：', error);
      });
  };
  useEffect(() => {
    if (type === 'look') {
      setScrollBottom(true);
    } else if (type === 'sign') {
    }
  }, [type]);
  return (
    <>
      <Modal visible={visible} onCancel={closeModal} footer={null} width={1100} {...rest}>
        <Spin spinning={loading}>
          {type === 'sign' && (
            <div
              style={{
                marginTop: '10px',
                background: '#f0f0f0',
                padding: '8px 20px',
              }}
            >
              <h3>{`请确认与达人机构${institutionName}的合作合同`}</h3>
              <p>{promptText}</p>
            </div>
          )}
          <div
            style={{
              height: '600px',
              overflowY: 'scroll',
              marginTop: '10px',
              display: 'flex',
              justifyContent: ' space-around',
            }}
            id="scrollDiv"
            onScroll={handleScroll}
          >
            <Document file={url} onLoadSuccess={onDocumentLoadSuccess}>
              {Array.from(new Array(numPages), (el, index) => (
                <Page
                  key={`page_${index + 1}`}
                  pageNumber={index + 1}
                  renderTextLayer={false} // 禁用文本层以提高性能
                />
              ))}
            </Document>
          </div>
        </Spin>
        {scrollBottom && (
          <div style={{ marginTop: '16px', textAlign: 'center' }}>
            {type === 'sign' && scrollBottom && (
              <Button onClick={handleConfirm} type="primary" loading={signLoading}>
                我已阅读并同意该协议
              </Button>
            )}
            <Button onClick={() => downLoad()} style={{ marginLeft: '8px' }}>
              下载PDF
            </Button>
          </div>
        )}
      </Modal>
    </>
  );
});
export default PreviewPDF;
