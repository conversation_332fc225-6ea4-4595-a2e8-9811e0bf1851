import React, { useState, forwardRef, useImperative<PERSON><PERSON><PERSON>, useEffect } from 'react';
import { Radio, Button, Card, Form, message, Tag, Icon, Modal, Input, Select } from 'antd';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { promiseRatioEdit } from '../GoodsInfoEdit/services/yml/index';
import { FormComponentProps } from 'antd/lib/form';
interface IPROPS extends FormComponentProps {
  setBaobiMarkVisible: (value: boolean) => void;
  visible: boolean;
  info?: any;
  onDetail?: (value: any, type: string) => void;
}

const BaobiMarkModal: React.FC<IPROPS> = (props) => {
  const { setBaobiMarkVisible, visible, info, form, onDetail } = props;

  const onCancel = () => {
    setBaobiMarkVisible(false);
  };
  const handleOk = () => {
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      //
      const params = {
        id: info?.id,
        isPromiseRatio: values.isPromiseRatio,
        promiseRatioValue: values.promiseRatioValue || '',
      };
      promiseRatioEdit(params).then((res) => {
        if (res?.res?.code === '200') {
          message.success('操作成功');
          setBaobiMarkVisible(false);
          onDetail({ id: info?.id }, 'info');
        } else {
          message.warn(res?.res?.message);
        }
      });
    });
  };
  const radioChange = (e: any) => {
    // 是否保比改变时清空ROI
    form.setFieldsValue({ isPromiseRatio: '' });
  };

  useEffect(() => {
    form.resetFields();
  }, [visible]);
  return (
    <Modal
      title={`保比标记`}
      visible={visible}
      onCancel={() => {
        onCancel();
      }}
      onOk={handleOk}
      width={400}
    >
      <Form labelAlign="right" labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
        <Form.Item label={`是否保比`}>
          {form.getFieldDecorator('isPromiseRatio', {
            rules: [{ required: true, message: `请选择是否保比` }],
          })(
            <Radio.Group onChange={radioChange}>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>,
          )}
        </Form.Item>
        {form.getFieldsValue()?.isPromiseRatio && (
          <Form.Item label="保比(ROI)">
            {form.getFieldDecorator('promiseRatioValue', {
              rules: [{ required: true, message: `请填写保比ROI` }],
            })(<Input style={{ width: 260 }} placeholder="请输入" maxLength={100} />)}
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};
export default Form.create<IPROPS>()(BaobiMarkModal);
