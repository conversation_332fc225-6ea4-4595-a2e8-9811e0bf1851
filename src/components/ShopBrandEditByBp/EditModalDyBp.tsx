import React, { useEffect, useState, useImperativeHandle } from 'react';
import { Form, Input, Icon, Radio, message, Button } from 'antd';
import { Modal, Popover } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { FormComponentProps } from 'antd/es/form';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import './index.less';

import { shopQuaInfo, shopQuaEdit, refreshSpecQualification } from './services/index';

import FileUpload from '../FileUpload';
interface PRPOS extends ModalProps, FormComponentProps {
  id?: number | string;
  info?: object;
  onRef?: any;
  reset: () => void;
  onComplete?: any;
}

export const styles = {
  box: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  } as any,
  plus: {
    color: '#999',
    fontSize: '28px',
  },
};

const EditModal: React.FC<PRPOS & WithToggleModalProps> = (props) => {
  const { id, reset, onOk, onComplete, onRef, form, info, ...other } = props;
  const [shopQualificationImg, setImgList] = useState([]);
  const [visible, setVisible] = useState(false);
  const { getFieldDecorator, getFieldValue } = form;

  const [formData, setFormData] = useState({
    id: '',
    shopName: '',
    doudianId: '',
    platformShopOrgName: '',
    shopQualificationImg: [],
  });
  const [companyName, setCompanyName] = useState();
  useEffect(() => {}, []);
  const [shopInfoRes, setShopInfoRes] = useState();
  const getShop = async (value) => {
    const res = await shopQuaInfo({ shopId: value.shopId, supplierId: value.supplierId });
    // console.log('店铺详情001res', res);
    if (res?.res?.code == 200) {
      const info = res.res.result;
      // console.log(info);

      form.setFieldsValue({
        // shopQualificationImg: info?.shopQualificationImg && changeImg(info?.shopQualificationImg),
        shopQualificationImg: info?.shopQualificationImg,
      });

      setShopInfoRes(info);
      // setImgList(info?.shopQualificationImg && changeImg(info?.shopQualificationImg));
      // console.log(formData);
    } else {
      message.error(res.res.message);
    }
  };

  const onOka = () => {
    try {
      props.form.validateFields(async (err, values) => {
        console.log('编辑参数001', err, values);
        if (!err) {
          const params = {
            shopId: shopInfo.shopId,
            supplierId: shopInfo?.supplierId,
            shopMainName: values.shopMainName,
            shopQualificationImg: values.shopQualificationImg,
            isConsistent: values.isConsistent,
          };

          const res = await shopQuaEdit(params);
          if (res?.res?.code === '200') {
            if (info?.specVersion) {
              // 为true时保存成功，需要调刷新接口
              refreshSpecQualification({ specVersionId: info?.versionId });
            }
            message.success('操作成功');
            form.resetFields();
            setVisible(false);
            onComplete && onComplete();
          } else {
            message.error(res.res.message);
          }
        } else {
          return err;
        }
      });
    } catch (err) {
      // console.log(err);
    }
  };
  const cancelBtn = () => {
    setVisible(false);
  };
  const getUrl = (list: Array<any>) => {
    list.forEach((i) => {
      if (Array.isArray(i.url)) {
        i.url = i.url[0];
      }
    });
    return list.map((i) => i.url);
  };
  const changeImg = (list: Array<any>) => {
    if (list?.length) {
      return list.map((item: string, index: number) => ({
        uid: index,
        name: item,
        status: 'done',
        url: item,
      }));
    }
  };
  const [shopInfo, setShopInfo] = useState(undefined);
  const showModal = (value) => {
    // console.log('value', value);
    setShopInfo(value);
    getShop(value);
    setVisible(true);
  };
  useImperativeHandle(onRef, () => ({
    open: showModal,
  }));
  return (
    <Modal
      title="店铺信息"
      onOk={() => {
        onOka();
      }}
      width={500}
      onCancel={cancelBtn}
      visible={visible}
    >
      <div className="goodInfo-contain">
        <Form labelAlign="left" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item className="mb-16" label="店铺名称:">
            {getFieldDecorator('shopName', {
              rules: [{ required: false, message: '请填写店铺名称' }],
              initialValue: shopInfo?.shopName,
            })(<Input disabled placeholder="" maxLength={30} />)}
          </Form.Item>
          <Form.Item className="mb-16" label="店铺ID:">
            {getFieldDecorator('doudianId', {
              rules: [{ required: false, message: '请填写店铺ID' }],
              initialValue: shopInfo?.platformShopCode,
            })(<Input disabled placeholder="" maxLength={30} />)}
          </Form.Item>
          <Form.Item className="mb-16" label="店铺截图:">
            {getFieldDecorator('shopQualificationImg', {
              rules: [{ required: false, message: '请选择店铺截图' }],
            })(
              <FileUpload
                maxLen={5}
                multiple
                isImage
                typeCode="SPU_IMG"
                maxSize={20 * 1024 * 1024}
                // multiple
                accept={'.jpg,.jpeg,.png'}
              />,
            )}
            <div style={{ marginTop: '6px' }}>
              <Popover
                placement="right"
                title={<></>}
                content={
                  <img
                    className="imgSize"
                    title=""
                    src="https://befriend-rss-static-test.oss-cn-hangzhou.aliyuncs.com/goods/spuImg/83af25bd995d430a84bbdf18df93c701/12333.jpg"
                  ></img>
                }
              >
                <a title="">
                  示例图 <Icon type="right" />
                </a>
              </Popover>
              {/*  */}
              {/* </Preview> */}
              <p style={{ fontSize: '12px', color: 'rgba(46, 52, 66, 0.45)', marginTop: '4px' }}>
                请上传体现<span style={{ color: 'rgba(46, 52, 66, 1' }}>开店公司主体名称</span>
                和店铺名称的后台截图；
              </p>
              <p style={{ fontSize: '12px', color: 'rgba(46, 52, 66, 0.45)', marginTop: '4px' }}>
                支持PNG、JPG或JPEG格式，单张不超过20MB，最多5张
              </p>
            </div>
          </Form.Item>
        </Form>
        <Form labelAlign="left" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
          <Form.Item className="mb-16" label="开店主体与入驻主体是否一致:">
            {getFieldDecorator('isConsistent', {
              initialValue: shopInfoRes?.isConsistent,
              rules: [{ required: false, message: '请选择' }],
            })(
              <Radio.Group name="radiogroup" style={{ marginTop: '10px' }}>
                <Radio value={true}>一致</Radio>
                <Radio value={false}>不一致</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
        </Form>{' '}
        <Form labelAlign="left" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          {form.getFieldsValue().isConsistent === false && (
            <Form.Item className="mb-16" label="开店主体名称:">
              {getFieldDecorator('shopMainName', {
                initialValue: shopInfoRes?.shopMainName,
                rules: [{ required: false, message: '开店主体名称' }],
              })(<Input allowClear placeholder="开店主体名称" maxLength={30} />)}
            </Form.Item>
          )}
        </Form>
      </div>
    </Modal>
  );
};

export default Form.create()(EditModal);
