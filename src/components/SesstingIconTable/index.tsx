import React, { useEffect, useState, useImperativeHandle } from 'react';
import { Popover, Checkbox, Button, Icon, message } from 'antd';
import { setConfig, getConfig } from '@/services/yml/common';
import './index.less';
interface PropsType {
  bizType?: string;
}
const SesstingIconTable = (props: PropsType) => {
  const [visible, setVisible] = useState(false);
  const { bizType, onRef } = props;
  const handleVisibleChange = (visible) => {
    setVisible(visible);
  };

  const [items, setItems] = useState([]);

  const handleDragStart = (e, index) => {
    e.dataTransfer.setData('index', index);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    const disabledArr = items.filter((i) => !i.draggable)?.map((i) => String(i?.index));
    const draggedIndex = e.dataTransfer.getData('index');
    const droppedIndex = e.currentTarget.getAttribute('data-index');
    console.log(disabledArr, draggedIndex, droppedIndex);
    if (disabledArr.indexOf(droppedIndex) > -1) {
      return;
    }
    const newItems = [...items];
    const [draggedItem] = newItems.splice(draggedIndex, 1);
    newItems.splice(droppedIndex, 0, draggedItem);

    setItems(
      newItems.map((item, key) => {
        return { ...item, index: key };
      }),
    );
  };

  const setConfigInfo = () => {
    const arr = items.map((i) => {
      return {
        name: i?.name,
        key: i?.key,
        index: i?.index,
        isShow: i?.isShow,
        draggable: i?.draggable,
      };
    });
    console.log('arr', arr);
    setConfig({ bizType: bizType, extConfig: JSON.stringify(arr) }).then(({ res }) => {
      if (res?.code === '200') {
        message.success('保存成功');
        setVisible(false);
        history.go(0);
      }
    });
  };

  const getConfigInfo = () => {
    getConfig({ bizType: bizType }).then(({ res }) => {
      if (res.code === '200') {
        if (res.result) {
          const arr = JSON.parse(res.result);

          setItems(arr);
        }
      }
    });
  };
  useEffect(() => {
    getConfigInfo();
  }, [visible]);
  return (
    <div className="settingIcon">
      <Popover
        trigger="click"
        visible={visible}
        onVisibleChange={handleVisibleChange}
        content={
          <div>
            <div className="contentSetting unselectable">
              {items.map((item, index) => {
                return (
                  <div
                    key={index}
                    data-index={index}
                    draggable={item?.draggable}
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    className="contentLine"
                    style={{ cursor: item?.draggable ? 'grab' : '' }}
                  >
                    <span>
                      <Checkbox
                        checked={item?.isShow}
                        disabled={!item?.draggable}
                        onChange={(value) => {
                          // console.log(value.target.checked, item);
                          const arr = [...items];
                          arr.forEach((i) => {
                            if (i.key === item.key) {
                              i.isShow = !item.isShow;
                            }
                          });
                          setItems([...arr]);
                        }}
                      />
                      {/* <span style={{ flex: 1, margin: '0 8px' }}>{item.name}</span> */}
                      {item.name === '#' ? (
                        <span className="iconfont icon-shezhi setIcon"></span>
                      ) : (
                        <span style={{ flex: 1, margin: '0 8px' }}>{item.name}</span>
                      )}
                    </span>
                    <Icon type="drag" />
                  </div>
                );
              })}
            </div>
            <div className="settingBtnGroup">
              <Button
                onClick={() => {
                  setVisible(false);
                }}
                type="default"
                style={{ marginRight: '8px' }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setConfigInfo();
                }}
                style={{ marginRight: '16px' }}
              >
                确定
              </Button>
            </div>
          </div>
        }
      >
        <span className="iconfont icon-shezhi setIcon"></span>
      </Popover>
    </div>
  );
};

export default SesstingIconTable;
