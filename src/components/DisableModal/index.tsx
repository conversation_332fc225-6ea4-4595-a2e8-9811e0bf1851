import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Drawer, Button, Card, Form, message, Tag, Icon, Modal, Input, Select } from 'antd';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { getGoodsDisable, getGoodsEnable } from '../GoodsInfoEdit/services/yml/index';
import { FormComponentProps } from 'antd/lib/form';
interface IPROPS extends FormComponentProps {
  setIsDisabledVisible: (value: boolean) => void;
  isDisabledVisible: boolean;
  isDisabledTag: boolean;
  info?: any;
  onSearch: () => void;
  setVisible: (value: boolean) => void;
}

const DisableModal: React.FC<IPROPS> = (props) => {
  const {
    setIsDisabledVisible,
    isDisabledVisible,
    isDisabledTag,
    info,
    form,
    onSearch,
    setVisible,
  } = props;

  // 获取系统代码
  const { codeList, getEnumList } = useCode(
    isDisabledTag
      ? CODE_ENUM.GOODS_ASSORTING_DISABLE_REASON
      : CODE_ENUM.GOODS_ASSORTING_ENABLE_REASON,
    {
      wait: true,
      able: true,
    },
  );

  const disabledHandleCancel = () => {
    setIsDisabledVisible(false);
  };
  const handleDisabledOk = () => {
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      // 禁用：getGoodsDisable，解禁：getGoodsEnable
      const disabledApiUlr = isDisabledTag ? getGoodsDisable : getGoodsEnable;
      const params = isDisabledTag
        ? {
            id: info?.id,
            disableReason: values.disableReason,
            remark: values.remark || '',
          }
        : {
            id: info?.id,
            enableReason: values.disableReason,
            remark: values.remark || '',
          };
      disabledApiUlr(params).then((res) => {
        if (res?.res?.code === '200') {
          message.success('操作成功');
          setVisible(false);
          setIsDisabledVisible(false);
          // 此处必须加延时，不然接口不会更新数据
          setTimeout(() => {
            onSearch();
          }, 1000);
        } else {
          message.warn(res?.res?.message);
        }
      });
    });
  };

  useEffect(() => {
    getEnumList();
    form.resetFields();
  }, [isDisabledTag, isDisabledVisible]);
  return (
    <Modal
      title={`商品${isDisabledTag ? '禁用' : '解禁'}`}
      visible={isDisabledVisible}
      onCancel={() => {
        disabledHandleCancel();
      }}
      onOk={handleDisabledOk}
      width={400}
    >
      <Form labelAlign="right" labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
        <Form.Item label={`${isDisabledTag ? '禁用' : '解禁'}原因`}>
          {form.getFieldDecorator('disableReason', {
            rules: [{ required: true, message: `请选择${isDisabledTag ? '禁用' : '解禁'}原因` }],
          })(
            <Select
              allowClear={true}
              // maxTagCount={1}
              optionFilterProp="children"
              showSearch
              filterOption={true}
              placeholder="请选择"
              style={{ width: 260 }}
              // labelInValue
            >
              {codeList.map((i, index) => {
                return (
                  <Select.Option value={i.label} key={index}>
                    {i.label}
                  </Select.Option>
                );
              })}
            </Select>,
          )}
        </Form.Item>
        <Form.Item label="备注">
          {form.getFieldDecorator('remark')(
            <Input style={{ width: 260 }} placeholder="请输入" maxLength={100} />,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default Form.create<IPROPS>()(DisableModal);
