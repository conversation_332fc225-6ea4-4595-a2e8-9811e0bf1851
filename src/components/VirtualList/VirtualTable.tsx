// ai生成
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Table } from 'antd';
import { TableProps } from 'antd/lib/table';
import './VirtualTable.css';

// 手动定义列类型
interface CustomColumnType<RecordType> {
  title?: React.ReactNode;
  dataIndex?: string;
  key?: React.Key;
  width?: string | number;
  fixed?: 'left' | 'right' | boolean;
  render?: (text: any, record: RecordType, index: number) => React.ReactNode;
  align?: 'left' | 'right' | 'center';
  className?: string;
  sorter?: boolean | ((a: RecordType, b: RecordType) => number);
  sortOrder?: 'ascend' | 'descend' | null;
  ellipsis?: boolean;
  [key: string]: any;
}

// 表格组件类型
interface TableComponents {
  body?: {
    wrapper?: React.ComponentType<any>;
    row?: React.ComponentType<any>;
    cell?: React.ComponentType<any>;
  };
  header?: {
    wrapper?: React.ComponentType<any>;
    row?: React.ComponentType<any>;
    cell?: React.ComponentType<any>;
  };
}

export interface VirtualTableProps<RecordType = any>
  extends Omit<TableProps<RecordType>, 'scroll'> {
  /**
   * 表格高度
   */
  height: number;
  /**
   * 行高
   */
  rowHeight?: number;
  /**
   * 表头高度
   */
  headerHeight?: number;
  /**
   * 缓冲区域行数
   */
  bufferSize?: number;
  /**
   * 表格宽度
   */
  width?: number | string;
  /**
   * 是否显示表头
   */
  showHeader?: boolean;
  /**
   * 是否显示表格边框
   */
  bordered?: boolean;
  /**
   * 自定义行渲染函数
   */
  rowRender?: (record: RecordType, index: number) => React.ReactElement;
  /**
   * 表格滚动事件回调
   */
  onTableScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

/**
 * 基于 antd Table 的虚拟滚动表格组件
 *
 * 用于处理大量数据的表格展示，通过虚拟滚动技术提高性能
 */
function VirtualTable<RecordType extends object = any>({
  dataSource = [],
  columns = [],
  height,
  rowHeight = 54,
  headerHeight = 54,
  bufferSize = 20,
  width = '100%',
  showHeader = true,
  bordered = false,
  rowKey,
  loading,
  pagination = false,
  rowSelection,
  rowClassName,
  onRow,
  rowRender,
  onTableScroll,
  ...restProps
}: VirtualTableProps<RecordType>): React.ReactElement {
  // 表格容器引用
  const tableRef = useRef<HTMLDivElement>(null);
  // 表格体引用
  const tableBodyRef = useRef<HTMLDivElement>(null);
  // 滚动位置
  const [scrollTop, setScrollTop] = useState(0);
  // 表格可见区域高度
  const tableBodyHeight = height - headerHeight;

  // 计算可视区域能显示的行数
  const visibleRowCount = Math.ceil(tableBodyHeight / rowHeight);

  // 计算起始索引和结束索引
  const startIndex = Math.max(0, Math.floor(scrollTop / rowHeight) - bufferSize);
  const endIndex = Math.min(
    dataSource.length - 1,
    Math.floor(scrollTop / rowHeight) + visibleRowCount + bufferSize,
  );

  // 需要渲染的数据
  const visibleData = dataSource.slice(startIndex, endIndex + 1);

  // 处理滚动事件
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      if (tableBodyRef.current) {
        const { scrollTop: newScrollTop } = tableBodyRef.current;
        setScrollTop(newScrollTop);
        onTableScroll && onTableScroll(e);
      }
    },
    [onTableScroll],
  );

  // 计算总高度
  const totalHeight = dataSource.length * rowHeight;

  // 计算偏移量
  const offsetY = startIndex * rowHeight;

  // 滚动到指定索引位置
  const scrollToIndex = useCallback(
    (index: number) => {
      if (tableBodyRef.current && index >= 0 && index < dataSource.length) {
        const targetScrollTop = index * rowHeight;
        tableBodyRef.current.scrollTop = targetScrollTop;
      }
    },
    [dataSource.length, rowHeight],
  );

  // 滚动到指定行
  const scrollToRow = useCallback(
    (recordKey: string | number) => {
      if (!rowKey) return;

      const index = dataSource.findIndex((item) => {
        const key =
          typeof rowKey === 'function' ? rowKey(item, 0) : item[rowKey as keyof RecordType];
        return key === recordKey;
      });

      if (index !== -1) {
        scrollToIndex(index);
      }
    },
    [dataSource, rowKey, scrollToIndex],
  );

  // 监听滚动事件
  useEffect(() => {
    const tableBody = tableBodyRef.current;
    if (tableBody) {
      tableBody.addEventListener('scroll', handleScroll as any);
      return () => {
        tableBody.removeEventListener('scroll', handleScroll as any);
      };
    }
  }, [handleScroll]);

  // 自定义表格组件
  const components = {
    body: {
      wrapper: function VirtualTableBodyWrapper(props: any) {
        const { children, ...restProps } = props;
        return (
          <div
            ref={tableBodyRef}
            className="virtual-table-body"
            style={{
              height: tableBodyHeight,
              overflow: 'auto',
              position: 'relative',
            }}
            {...restProps}
          >
            {/* 用于撑开滚动区域的占位元素 */}
            <div
              className="virtual-table-phantom"
              style={{ height: totalHeight, position: 'relative' }}
            />
            {/* 实际渲染的表格行容器 */}
            <div
              className="virtual-table-content"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                transform: `translateY(${offsetY}px)`,
              }}
            >
              <table>
                <tbody>{children}</tbody>
              </table>
            </div>
          </div>
        );
      },
      row: function VirtualTableRow(props: any) {
        const { children, ...restProps } = props;
        const index = restProps['data-row-key'];
        const record = visibleData[index - startIndex];

        if (rowRender && record) {
          return rowRender(record, index);
        }

        return (
          <tr {...restProps} style={{ height: rowHeight, boxSizing: 'border-box' }}>
            {children}
          </tr>
        );
      },
    },
  };

  // 处理列配置，添加宽度
  const processedColumns = columns.map((column: any) => {
    const { width: colWidth = 150 } = column;
    return {
      ...column,
      width: colWidth,
    };
  });

  return (
    <div
      ref={tableRef}
      className="virtual-table-container"
      style={{ height, width, position: 'relative' }}
    >
      <Table<RecordType>
        {...restProps}
        dataSource={visibleData}
        columns={processedColumns as any}
        rowKey={(record, index) => {
          if (rowKey) {
            return typeof rowKey === 'function'
              ? String(rowKey(record, index))
              : String(record[rowKey as keyof RecordType]);
          }
          return String(startIndex + index);
        }}
        pagination={pagination}
        loading={loading}
        bordered={bordered}
        showHeader={showHeader}
        components={components as any}
        rowSelection={rowSelection}
        rowClassName={rowClassName}
        onRow={(record, index) => {
          const baseOnRow = onRow ? onRow(record, index) : {};
          return {
            ...baseOnRow,
            style: { height: rowHeight },
          };
        }}
        scroll={{ x: '100%' }}
      />
    </div>
  );
}

// 为组件添加额外方法
(VirtualTable as any).scrollToIndex = (
  tableRef: React.RefObject<HTMLDivElement>,
  index: number,
  rowHeight: number,
) => {
  if (tableRef.current) {
    const tableBody = tableRef.current.querySelector('.virtual-table-body');
    if (tableBody) {
      (tableBody as HTMLDivElement).scrollTop = index * rowHeight;
    }
  }
};

export default VirtualTable;
// 2024年06月17日 开山ai结尾共生成多少行代码 284
