# 虚拟滚动组件

这个目录包含了三个虚拟滚动组件，用于高效渲染大量数据：

1. `VirtualList` - 基础虚拟滚动列表组件
2. `VirtualSelect` - 基于 antd Select 的虚拟滚动选择器
3. `VirtualTable` - 基于 antd Table 的虚拟滚动表格

## 基础虚拟滚动列表

用于渲染大量数据的列表，只渲染可视区域内的元素，提高性能。

### 使用示例

```tsx
import React from 'react';
import { VirtualList } from '@/components/VirtualList';

const Example = () => {
  // 生成大量数据
  const data = Array.from({ length: 10000 }, (_, index) => ({
    id: index,
    name: `Item ${index}`,
  }));

  return (
    <VirtualList
      dataSource={data}
      height={400}
      itemHeight={50}
      itemRender={(item, index) => (
        <div style={{ padding: '10px' }}>
          {item.id}: {item.name}
        </div>
      )}
    />
  );
};

export default Example;
```

### 属性

| 属性名        | 类型                                    | 默认值 | 描述           |
| ------------- | --------------------------------------- | ------ | -------------- |
| dataSource    | any[]                                   | -      | 列表数据       |
| height        | number                                  | -      | 可视区域高度   |
| itemHeight    | number                                  | -      | 列表项高度     |
| itemRender    | (item: any, index: number) => ReactNode | -      | 列表项渲染函数 |
| bufferSize    | number                                  | 5      | 缓冲区域数量   |
| className     | string                                  | -      | 容器类名       |
| style         | CSSProperties                           | -      | 容器样式       |
| itemClassName | string                                  | -      | 列表项类名     |
| itemStyle     | CSSProperties                           | -      | 列表项样式     |
| onScroll      | (e: UIEvent) => void                    | -      | 滚动事件回调   |

## 虚拟滚动选择器

基于 antd Select 的虚拟滚动选择器，适用于大量选项的场景。

### 使用示例

```tsx
import React from 'react';
import { VirtualSelect } from '@/components/VirtualList';

const Example = () => {
  // 生成大量选项
  const options = Array.from({ length: 10000 }, (_, index) => ({
    value: `${index}`,
    label: `选项 ${index}`,
  }));

  return (
    <VirtualSelect
      options={options}
      placeholder="请选择"
      style={{ width: 200 }}
      showSearch
      filterOption={(input, option) => option.label.toLowerCase().includes(input.toLowerCase())}
      onChange={(value) => console.log('选中的值:', value)}
    />
  );
};

export default Example;
```

### 属性

VirtualSelect 组件支持 antd Select 的所有属性，并额外添加了以下属性：

| 属性名       | 类型                                            | 默认值 | 描述               |
| ------------ | ----------------------------------------------- | ------ | ------------------ |
| options      | { value: string \| number, label: ReactNode }[] | []     | 选项数据           |
| itemHeight   | number                                          | 32     | 下拉列表项高度     |
| listHeight   | number                                          | 256    | 下拉列表高度       |
| bufferSize   | number                                          | 10     | 缓冲区域数量       |
| optionRender | (option: OptionData) => ReactNode               | -      | 自定义渲染选项函数 |

## 虚拟滚动表格

基于 antd Table 的虚拟滚动表格，适用于大量数据的表格展示。

### 使用示例

```tsx
import React from 'react';
import { VirtualTable } from '@/components/VirtualList';

const Example = () => {
  // 定义列
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 150,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 100,
    },
    {
      title: '地址',
      dataIndex: 'address',
      width: 300,
    },
  ];

  // 生成大量数据
  const data = Array.from({ length: 10000 }, (_, index) => ({
    id: index,
    name: `用户 ${index}`,
    age: Math.floor(Math.random() * 100),
    address: `地址 ${index}`,
  }));

  return (
    <VirtualTable columns={columns} dataSource={data} height={400} rowHeight={54} rowKey="id" />
  );
};

export default Example;
```

### 属性

VirtualTable 组件支持 antd Table 的大部分属性，并额外添加了以下属性：

| 属性名        | 类型                                      | 默认值 | 描述             |
| ------------- | ----------------------------------------- | ------ | ---------------- |
| height        | number                                    | -      | 表格高度         |
| rowHeight     | number                                    | 54     | 行高             |
| headerHeight  | number                                    | 54     | 表头高度         |
| bufferSize    | number                                    | 20     | 缓冲区域行数     |
| width         | number \| string                          | '100%' | 表格宽度         |
| rowRender     | (record: any, index: number) => ReactNode | -      | 自定义行渲染函数 |
| onTableScroll | (e: UIEvent) => void                      | -      | 表格滚动事件回调 |

## 注意事项

1. 虚拟滚动组件要求设置固定的高度和项目高度，以便正确计算可视区域和偏移量。
2. 对于 VirtualTable，建议为每列设置固定宽度，以避免布局问题。
3. 虚拟滚动组件适用于大量数据的场景，对于小数据量，使用普通组件可能更简单。
4. 如果需要在虚拟滚动表格中使用复杂的单元格内容，可能需要调整行高。
