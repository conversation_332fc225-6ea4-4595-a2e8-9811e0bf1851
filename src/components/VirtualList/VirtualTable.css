/* ai生成 */
.virtual-table-container {
  position: relative;
  overflow: hidden;
}

.virtual-table-body {
  overflow: auto;
  position: relative;
}

.virtual-table-phantom {
  position: relative;
}

.virtual-table-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.virtual-table-content table {
  table-layout: fixed;
  width: 100%;
}

.virtual-table-content tr {
  box-sizing: border-box;
}

/* 修复 antd 表格样式冲突 */
.virtual-table-container .ant-table-body {
  overflow: visible !important;
}

.virtual-table-container .ant-table-header {
  overflow: hidden !important;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.virtual-table-container .ant-table-thead > tr > th {
  background: #fafafa;
  position: sticky;
  top: 0;
  z-index: 2;
}
/* 2024年06月17日 开山ai结尾共生成多少行代码 36 */ 