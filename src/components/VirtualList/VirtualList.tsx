// ai生成
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';

export interface VirtualListProps<T = any> {
  /**
   * 列表数据
   */
  dataSource: T[];
  /**
   * 可视区域高度
   */
  height: number;
  /**
   * 列表项高度
   */
  itemHeight: number;
  /**
   * 列表项渲染函数
   */
  itemRender: (item: T, index: number) => React.ReactNode;
  /**
   * 缓冲区域数量，默认为5
   */
  bufferSize?: number;
  /**
   * 容器类名
   */
  className?: string;
  /**
   * 容器样式
   */
  style?: React.CSSProperties;
  /**
   * 列表项类名
   */
  itemClassName?: string;
  /**
   * 列表项样式
   */
  itemStyle?: React.CSSProperties;
  /**
   * 滚动事件回调
   */
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
}

function VirtualList<T = any>({
  dataSource,
  height,
  itemHeight,
  itemRender,
  bufferSize = 5,
  className = '',
  style = {},
  itemClassName = '',
  itemStyle = {},
  onScroll,
}: VirtualListProps<T>): React.ReactElement {
  // 容器引用
  const containerRef = useRef<HTMLDivElement>(null);
  // 滚动位置
  const [scrollTop, setScrollTop] = useState(0);

  // 计算可视区域能显示的列表项数量
  const visibleCount = Math.ceil(height / itemHeight);

  // 计算起始索引和结束索引
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
  const endIndex = Math.min(
    dataSource.length - 1,
    Math.floor(scrollTop / itemHeight) + visibleCount + bufferSize,
  );

  // 需要渲染的数据
  const visibleData = useMemo(() => {
    return dataSource.slice(startIndex, endIndex + 1);
  }, [dataSource, startIndex, endIndex]);

  // 处理滚动事件
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop: newScrollTop } = e.currentTarget;
      setScrollTop(newScrollTop);
      onScroll && onScroll(e);
    },
    [onScroll],
  );

  // 计算总高度
  const totalHeight = dataSource.length * itemHeight;

  // 计算偏移量
  const offsetY = startIndex * itemHeight;

  return (
    <div
      ref={containerRef}
      className={`virtual-list-container ${className}`}
      style={{
        height,
        overflow: 'auto',
        position: 'relative',
        ...style,
      }}
      onScroll={handleScroll}
    >
      {/* 用于撑开滚动区域的占位元素 */}
      <div className="virtual-list-phantom" style={{ height: totalHeight, position: 'relative' }} />
      {/* 实际渲染的列表项容器 */}
      <div
        className="virtual-list-content"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          transform: `translateY(${offsetY}px)`,
        }}
      >
        {visibleData.map((item, index) => (
          <div
            key={startIndex + index}
            className={`virtual-list-item ${itemClassName}`}
            style={{
              height: itemHeight,
              boxSizing: 'border-box',
              ...itemStyle,
            }}
          >
            {itemRender(item, startIndex + index)}
          </div>
        ))}
      </div>
    </div>
  );
}

export default VirtualList;
// 2024年06月17日 开山ai结尾共生成多少行代码 129
