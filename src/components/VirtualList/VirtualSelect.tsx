import React, { useCallback, useMemo, useRef } from 'react';
import { Select } from 'antd';
import VirtualList from './VirtualList';
import { SelectProps } from 'antd/lib/select';

const { Option } = Select;

export interface OptionData {
  /**
   * 选项值
   */
  value: string | number;
  /**
   * 选项显示文本
   */
  label: React.ReactNode;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 其他自定义属性
   */
  [key: string]: any;
}

export interface VirtualSelectProps {
  /**
   * 选项数据
   */
  options: OptionData[];
  /**
   * 选中的值
   */
  value?: string | string[] | number | number[];
  /**
   * 默认选中的值
   */
  defaultValue?: string | string[] | number | number[];
  /**
   * 是否多选
   */
  mode?: 'multiple' | 'tags';
  /**
   * 选择框大小
   */
  size?: 'large' | 'default' | 'small';
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 是否显示清除按钮
   */
  allowClear?: boolean;
  /**
   * 选择框默认文本
   */
  placeholder?: string;
  /**
   * 下拉菜单和选择器同宽
   */
  dropdownMatchSelectWidth?: boolean;
  /**
   * 自定义样式类名
   */
  className?: string;
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 下拉菜单的样式
   */
  dropdownStyle?: React.CSSProperties;
  /**
   * 下拉菜单的类名
   */
  dropdownClassName?: string;
  /**
   * 是否在选中项后清空搜索框
   */
  autoClearSearchValue?: boolean;
  /**
   * 是否默认高亮第一个选项
   */
  defaultActiveFirstOption?: boolean;
  /**
   * 是否可以搜索
   */
  showSearch?: boolean;
  /**
   * 搜索时过滤选项的方法
   */
  filterOption?: boolean | ((inputValue: string, option: any) => boolean);
  /**
   * 选中 option 时调用
   */
  onChange?: (value: any, option: any) => void;
  /**
   * 失去焦点时回调
   */
  onBlur?: (value: any) => void;
  /**
   * 获得焦点时回调
   */
  onFocus?: () => void;
  /**
   * 文本框值变化时回调
   */
  onSearch?: (value: string) => void;
  /**
   * 下拉列表项高度
   */
  itemHeight?: number;
  /**
   * 下拉列表高度
   */
  listHeight?: number;
  /**
   * 缓冲区域数量
   */
  bufferSize?: number;
  /**
   * 自定义渲染选项函数
   */
  optionRender?: (option: OptionData) => React.ReactNode;
}

/**
 * 基于 antd Select 的虚拟滚动选择器
 *
 * 用于处理大量选项数据的场景，通过虚拟滚动技术提高性能
 */
const VirtualSelect: React.FC<VirtualSelectProps> = ({
  options = [],
  value,
  defaultValue,
  mode,
  size,
  disabled = false,
  allowClear = false,
  placeholder,
  dropdownMatchSelectWidth = true,
  className,
  style,
  dropdownStyle,
  dropdownClassName,
  autoClearSearchValue = true,
  defaultActiveFirstOption = true,
  showSearch = false,
  filterOption,
  onChange,
  onBlur,
  onFocus,
  onSearch,
  itemHeight = 32,
  listHeight = 256,
  bufferSize = 10,
  optionRender,
}) => {
  // 下拉菜单容器引用
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 自定义下拉菜单渲染函数
  const dropdownRender = useCallback(
    (menu: React.ReactElement) => {
      return (
        <div ref={dropdownRef} style={{ ...dropdownStyle }}>
          {menu}
        </div>
      );
    },
    [dropdownStyle],
  );

  // 自定义选项渲染函数
  const renderOption = useCallback(
    (option: OptionData) => {
      if (optionRender) {
        return optionRender(option);
      }
      return option.label;
    },
    [optionRender],
  );

  // 虚拟列表渲染函数
  const itemRender = useCallback(
    (item: OptionData, index: number) => {
      return (
        <Option key={item.value} value={item.value} disabled={item.disabled}>
          {renderOption(item)}
        </Option>
      );
    },
    [renderOption],
  );

  // 下拉菜单内容
  const dropdownContent = useMemo(() => {
    return (
      <VirtualList
        dataSource={options}
        height={listHeight}
        itemHeight={itemHeight}
        itemRender={itemRender}
        bufferSize={bufferSize}
      />
    );
  }, [options, listHeight, itemHeight, itemRender, bufferSize]);

  // ai生成
  const handleChange = (val: any, opt: any) => {
    onChange?.(val, opt);
  };

  const handleFilterOption = (input: string, option: any) => {
    if (typeof filterOption === 'function') {
      return filterOption(input, option);
    }
    return filterOption !== false;
  };
  // 2024年7月12日 开山ai结尾共生成8行代码

  return (
    <Select
      value={value}
      defaultValue={defaultValue}
      mode={mode}
      size={size as SelectProps['size']}
      disabled={disabled}
      allowClear={allowClear}
      placeholder={placeholder}
      dropdownMatchSelectWidth={dropdownMatchSelectWidth}
      className={className}
      style={style}
      dropdownClassName={dropdownClassName}
      autoClearSearchValue={autoClearSearchValue}
      defaultActiveFirstOption={defaultActiveFirstOption}
      showSearch={showSearch}
      filterOption={filterOption !== undefined ? handleFilterOption : undefined}
      onChange={handleChange}
      onBlur={onBlur}
      onFocus={onFocus}
      onSearch={onSearch}
      dropdownRender={() => dropdownContent}
    >
      {/* 这里的 Option 不会被渲染，但需要保留以兼容 antd Select 的 API */}
      {options.map((option) => (
        <Option key={option.value} value={option.value} disabled={option.disabled}>
          {option.label}
        </Option>
      ))}
    </Select>
  );
};

export default VirtualSelect;
