import React, { useEffect, useState } from 'react';
import { Popover, Checkbox, Button, Icon, message } from 'antd';
import { setConfig } from '@/services/yml/common';
import './index.less';
interface PropsType {
  options?: Array<any>;
  bizType?: string;
}
const SettingIcon = (props: PropsType) => {
  const [visible, setVisible] = useState(false);
  const { options, bizType } = props;
  const handleVisibleChange = (visible) => {
    setVisible(visible);
  };

  const [items, setItems] = useState([]);

  const handleDragStart = (e, index) => {
    e.dataTransfer.setData('index', index);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    const index = items.filter((i) => !i.draggable).length - 1;
    const draggedIndex = e.dataTransfer.getData('index');
    const droppedIndex = e.currentTarget.getAttribute('data-index');
    // console.log(index, draggedIndex, droppedIndex);
    if (droppedIndex <= index) {
      return;
    }
    const newItems = [...items];
    const [draggedItem] = newItems.splice(draggedIndex, 1);
    newItems.splice(droppedIndex, 0, draggedItem);

    setItems(
      newItems.map((item, key) => {
        return { ...item, index: key };
      }),
    );
  };
  useEffect(() => {
    // console.log(options);
    options.length && setItems([...options]);
  }, [options]);
  const setConfigInfo = () => {
    const arr = items.map((i) => {
      return {
        name: i?.name,
        key: i?.key,
        index: i?.index,
        isShow: i?.isShow,
        draggable: i?.draggable,
      };
    });
    setConfig({ bizType: bizType, extConfig: JSON.stringify(arr) }).then(({ res }) => {
      if (res?.code === '200') {
        message.success('保存成功');
        setVisible(false);
        history.go(0);
      }
    });
  };
  return (
    <div className="settingIcon">
      <Popover
        trigger="click"
        visible={visible}
        onVisibleChange={handleVisibleChange}
        content={
          <div>
            <div className="contentSetting unselectable">
              {items.map((item, index) => {
                return (
                  <div
                    key={index}
                    data-index={index}
                    draggable={item?.draggable}
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    className="contentLine"
                    style={{ cursor: item?.draggable ? 'grab' : '' }}
                  >
                    <span>
                      <Checkbox
                        checked={item?.isShow}
                        disabled={!item?.draggable}
                        onChange={(value) => {
                          // console.log(value.target.checked, item);
                          const arr = [...items];
                          arr.forEach((i) => {
                            if (i.key === item.key) {
                              i.isShow = !item.isShow;
                            }
                          });
                          setItems([...arr]);
                        }}
                      />
                      <span style={{ flex: 1, margin: '0 8px' }}>{item.name}</span>
                    </span>
                    <Icon type="drag" />
                  </div>
                );
              })}
            </div>
            <div className="settingBtnGroup">
              <Button
                onClick={() => {
                  setVisible(false);
                }}
                type="default"
                style={{ marginRight: '8px' }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setConfigInfo();
                }}
                style={{ marginRight: '16px' }}
              >
                确定
              </Button>
            </div>
          </div>
        }
      >
        <span className="iconfont icon-shezhi setIcon"></span>
      </Popover>
    </div>
  );
};

export default SettingIcon;
