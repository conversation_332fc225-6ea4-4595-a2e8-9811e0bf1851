.file-preview {
  &-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    &.vertical {
      flex-direction: column;
      gap: 4px;
    }
  }

  &-item {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fafafa;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    max-width: 200px;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
      color: #1890ff;
    }

    &.clickable {
      cursor: pointer;

      &:hover {
        border-color: #1890ff;
        background: #f0f8ff;
        color: #1890ff;
      }
    }

    &.not-clickable {
      cursor: default;

      &:hover {
        border-color: #d9d9d9;
        background: #fafafa;
        color: #666;
      }
    }
  }

  &-icon {
    font-size: 14px;
    flex-shrink: 0;
  }

  &-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }

  &-size {
    font-size: 11px;
    color: #999;
    margin-left: 4px;
  }

  &-actions {
    display: flex;
    gap: 2px;
    margin-left: 4px;
  }

  &-action {
    padding: 2px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 12px;
    color: #666;

    &:hover {
      background: rgba(0, 0, 0, 0.06);
      color: #1890ff;
    }
  }

  &-more {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    background: transparent;
    font-size: 12px;
    color: #999;
    cursor: pointer;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }
}

// 图片预览Modal样式
.file-preview-modal {
  .ant-modal-body {
    padding: 0;
  }

  .image-preview {
    text-align: center;

    img {
      max-width: 100%;
      max-height: 70vh;
      object-fit: contain;
    }
  }

  .pdf-preview {
    height: 70vh;
    overflow: auto;

    canvas {
      display: block;
      margin: 0 auto;
    }
  }

  .video-preview {
    text-align: center;

    video {
      max-width: 100%;
      max-height: 70vh;
    }
  }

  .audio-preview {
    text-align: center;
    padding: 20px;

    audio {
      width: 100%;
      max-width: 400px;
    }
  }

  .file-info {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;

    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 500;
        color: #666;
      }

      .value {
        color: #333;
      }
    }
  }
}

// 文件列表弹窗样式
.file-list-modal {
  .file-list {
    max-height: 400px;
    overflow-y: auto;

    .file-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      transition: background 0.2s ease;

      &:hover {
        background: #f5f5f5;
      }

      .file-icon {
        font-size: 16px;
        flex-shrink: 0;
      }

      .file-info {
        flex: 1;
        overflow: hidden;

        .file-name {
          font-weight: 500;
          color: #333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          font-size: 12px;
          color: #999;
          margin-top: 2px;
        }
      }

      .file-actions {
        display: flex;
        gap: 4px;

        .action-btn {
          padding: 4px 8px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          background: #fff;
          cursor: pointer;
          font-size: 12px;
          color: #666;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }
    }
  }
}
