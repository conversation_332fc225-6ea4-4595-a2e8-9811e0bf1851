// 文件预览组件类型定义

export interface FileItem {
  key?: string; // 唯一标识符（可选）
  fileName: string;
  fileUrl: string;
  fileSize?: string | number; // 文件大小（可选）
  uploadTime?: string; // 上传时间（可选）
}

export interface FilePreviewProps {
  files: FileItem[];
  maxDisplayCount?: number; // 最大显示数量，超出显示 +N
  showDownload?: boolean; // 是否显示下载按钮
  showFileSize?: boolean; // 是否显示文件大小
  layout?: 'horizontal' | 'vertical'; // 布局方式
  className?: string;
  style?: React.CSSProperties;
}

export type FileType = 'image' | 'pdf' | 'excel' | 'word' | 'video' | 'audio' | 'zip' | 'other';

export interface FileTypeInfo {
  type: FileType;
  icon: string;
  color: string;
  supportPreview: boolean;
  previewMethod: 'modal' | 'newWindow' | 'inline';
}
