import React, { useState, useCallback, useMemo } from 'react';
import { Modal, message } from 'antd';
import { Preview } from 'web-common-modules/antd-pro-components';
import { FileItem, FilePreviewProps } from './types';
import { getFileType, formatFileSize, downloadFile, isValidUrl, getPreviewUrl } from './utils';
import './index.less';

const FilePreview: React.FC<FilePreviewProps> = ({
  files = [],
  maxDisplayCount = 10,
  showDownload = true,
  showFileSize = false,
  layout = 'horizontal',
  className = '',
  style = {},
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentFile, setCurrentFile] = useState<FileItem | null>(null);
  const [fileListVisible, setFileListVisible] = useState(false);

  // 有效文件过滤
  const validFiles = useMemo(() => {
    return files.filter((file) => file.fileName && file.fileUrl && isValidUrl(file.fileUrl));
  }, [files]);

  // 显示的文件和剩余文件
  const displayFiles = validFiles.slice(0, maxDisplayCount);
  const remainingCount = validFiles.length - maxDisplayCount;

  // 处理文件预览
  const handlePreview = useCallback(
    (file: FileItem) => {
      const fileTypeInfo = getFileType(file.fileName);

      if (!fileTypeInfo.supportPreview) {
        message.info('该文件类型不支持预览，已为您打开下载');
        downloadFile(file.fileUrl, file.fileName);
        return;
      }

      if (fileTypeInfo.previewMethod === 'newWindow' || fileTypeInfo.type === 'pdf') {
        // Excel/Word文档和PDF通过新窗口预览
        const previewUrl =
          fileTypeInfo.type === 'pdf'
            ? file.fileUrl
            : getPreviewUrl(file.fileUrl, fileTypeInfo.type);
        window.open(previewUrl, '_blank');
      } else {
        // 视频、音频等通过Modal预览
        setCurrentFile(file);
        setPreviewVisible(true);
      }
    },
    [validFiles],
  );

  // 处理文件下载
  const handleDownload = useCallback((file: FileItem, e: React.MouseEvent) => {
    e.stopPropagation();
    downloadFile(file.fileUrl, file.fileName);
  }, []);

  // 处理查看更多文件
  const handleShowMore = useCallback(() => {
    setFileListVisible(true);
  }, []);

  // 关闭预览
  const closePreview = useCallback(() => {
    setPreviewVisible(false);
    setCurrentFile(null);
  }, []);

  // 关闭文件列表
  const closeFileList = useCallback(() => {
    setFileListVisible(false);
  }, []);

  // 渲染预览内容
  const renderPreviewContent = () => {
    if (!currentFile) return null;

    const fileTypeInfo = getFileType(currentFile.fileName);

    switch (fileTypeInfo.type) {
      case 'video':
        return (
          <div className="video-preview">
            <video controls>
              <source src={currentFile.fileUrl} type="video/mp4" />
              您的浏览器不支持视频播放
            </video>
          </div>
        );

      case 'audio':
        return (
          <div className="audio-preview">
            <audio controls>
              <source src={currentFile.fileUrl} type="audio/mpeg" />
              您的浏览器不支持音频播放
            </audio>
          </div>
        );

      default:
        return (
          <div style={{ padding: '20px', textAlign: 'center' }}>
            <p>暂不支持该文件类型的预览</p>
          </div>
        );
    }
  };

  // 渲染文件项
  const renderFileItem = (file: FileItem, index: number) => {
    const fileTypeInfo = getFileType(file.fileName);
    const isClickable = fileTypeInfo.supportPreview || fileTypeInfo.type === 'image';
    const key = file.key || `file_${index}`;

    // 如果是图片，使用Preview组件包装
    if (fileTypeInfo.type === 'image') {
      const imageFiles = validFiles.filter((f) => getFileType(f.fileName).type === 'image');
      const imageUrls = imageFiles.map((f, idx) => ({
        url: f.fileUrl,
        desc: f.fileName,
        key: f.key || `img_${idx}`,
      }));

      return (
        <Preview key={key} url={file.fileUrl} urlList={imageUrls}>
          <span
            className="file-preview-simple clickable"
            style={{
              color: '#1890ff',
              cursor: 'pointer',
              marginRight: '8px',
            }}
          >
            {file.fileName}
          </span>
        </Preview>
      );
    }

    return (
      <span
        key={key}
        className={`file-preview-simple ${isClickable ? 'clickable' : 'not-clickable'}`}
        onClick={() => isClickable && handlePreview(file)}
        style={{
          color: isClickable ? '#1890ff' : '#333',
          cursor: isClickable ? 'pointer' : 'default',
          marginRight: '8px',
        }}
      >
        {file.fileName}
      </span>
    );
  };

  if (validFiles.length === 0) {
    return <span>-</span>;
  }

  return (
    <div className={`file-preview ${className}`} style={style}>
      <div className="file-preview-simple-container">
        {displayFiles.map((file, index) => renderFileItem(file, index))}
        {remainingCount > 0 && (
          <span
            className="file-preview-more-text"
            onClick={handleShowMore}
            style={{
              color: '#1890ff',
              cursor: 'pointer',
            }}
          >
            +{remainingCount}个文件
          </span>
        )}
      </div>

      {/* 文件预览Modal */}
      <Modal
        visible={previewVisible}
        onCancel={closePreview}
        footer={null}
        width={900}
        className="file-preview-modal"
        title={currentFile?.fileName}
      >
        {renderPreviewContent()}
        {currentFile && (
          <div className="file-info">
            <div className="info-item">
              <span className="label">文件名称:</span>
              <span className="value">{currentFile.fileName}</span>
            </div>
            {currentFile.fileSize && (
              <div className="info-item">
                <span className="label">文件大小:</span>
                <span className="value">{formatFileSize(currentFile.fileSize)}</span>
              </div>
            )}
            {currentFile.uploadTime && (
              <div className="info-item">
                <span className="label">上传时间:</span>
                <span className="value">{currentFile.uploadTime}</span>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 文件列表Modal */}
      <Modal
        visible={fileListVisible}
        onCancel={closeFileList}
        footer={null}
        width={600}
        className="file-list-modal"
        title="全部文件"
      >
        <div className="file-list">
          {validFiles.map((file, index) => {
            const fileTypeInfo = getFileType(file.fileName);
            const isClickable = fileTypeInfo.supportPreview || fileTypeInfo.type === 'image';
            const key = file.key || `file_${index}`;

            // 如果是图片，整个文件项用Preview组件包装
            if (fileTypeInfo.type === 'image') {
              const imageFiles = validFiles.filter((f) => getFileType(f.fileName).type === 'image');
              const imageUrls = imageFiles.map((f, idx) => ({
                url: f.fileUrl,
                desc: f.fileName,
                key: f.key || `img_${idx}`,
              }));

              return (
                <Preview key={key} url={file.fileUrl} urlList={imageUrls}>
                  <div className="file-item" style={{ cursor: 'pointer' }}>
                    <div className="file-info">
                      <div className="file-name">{file.fileName}</div>
                      {file.fileSize && (
                        <div className="file-size">{formatFileSize(file.fileSize)}</div>
                      )}
                    </div>
                    <div className="file-actions">
                      <button className="action-btn">预览</button>
                      <button
                        className="action-btn"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(file, e);
                        }}
                      >
                        下载
                      </button>
                    </div>
                  </div>
                </Preview>
              );
            }

            // 非图片文件的处理
            return (
              <div
                key={key}
                className="file-item"
                onClick={() => isClickable && handlePreview(file)}
              >
                <div className="file-info">
                  <div className="file-name">{file.fileName}</div>
                  {file.fileSize && (
                    <div className="file-size">{formatFileSize(file.fileSize)}</div>
                  )}
                </div>
                <div className="file-actions">
                  {isClickable && (
                    <button
                      className="action-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePreview(file);
                      }}
                    >
                      预览
                    </button>
                  )}
                  <button
                    className="action-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(file, e);
                    }}
                  >
                    下载
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </Modal>
    </div>
  );
};

export default FilePreview;
