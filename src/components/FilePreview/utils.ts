import { FileType, FileTypeInfo } from './types';

// 文件类型映射
const FILE_TYPE_MAP: Record<FileType, FileTypeInfo> = {
  image: {
    type: 'image',
    icon: 'file-image',
    color: '#52c41a',
    supportPreview: true,
    previewMethod: 'modal',
  },
  pdf: {
    type: 'pdf',
    icon: 'file-pdf',
    color: '#ff4d4f',
    supportPreview: true,
    previewMethod: 'modal',
  },
  excel: {
    type: 'excel',
    icon: 'file-excel',
    color: '#52c41a',
    supportPreview: true,
    previewMethod: 'newWindow',
  },
  word: {
    type: 'word',
    icon: 'file-word',
    color: '#1890ff',
    supportPreview: true,
    previewMethod: 'newWindow',
  },
  video: {
    type: 'video',
    icon: 'video-camera',
    color: '#722ed1',
    supportPreview: true,
    previewMethod: 'modal',
  },
  audio: {
    type: 'audio',
    icon: 'audio',
    color: '#fa8c16',
    supportPreview: true,
    previewMethod: 'modal',
  },
  zip: {
    type: 'zip',
    icon: 'file-zip',
    color: '#faad14',
    supportPreview: false,
    previewMethod: 'newWindow',
  },
  other: {
    type: 'other',
    icon: 'file',
    color: '#8c8c8c',
    supportPreview: false,
    previewMethod: 'newWindow',
  },
};

// 文件扩展名映射
const EXTENSION_MAP: Record<string, FileType> = {
  // 图片格式
  jpg: 'image',
  jpeg: 'image',
  png: 'image',
  gif: 'image',
  bmp: 'image',
  webp: 'image',
  svg: 'image',
  ico: 'image',

  // PDF格式
  pdf: 'pdf',

  // Excel格式
  xls: 'excel',
  xlsx: 'excel',
  csv: 'excel',

  // Word格式
  doc: 'word',
  docx: 'word',

  // 视频格式
  mp4: 'video',
  avi: 'video',
  mov: 'video',
  wmv: 'video',
  flv: 'video',
  mkv: 'video',

  // 音频格式
  mp3: 'audio',
  wav: 'audio',
  flac: 'audio',
  aac: 'audio',

  // 压缩格式
  zip: 'zip',
  rar: 'zip',
  '7z': 'zip',
  tar: 'zip',
  gz: 'zip',
};

/**
 * 根据文件名获取文件类型
 * @param fileName 文件名
 * @returns 文件类型信息
 */
export const getFileType = (fileName: string): FileTypeInfo => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const fileType = extension ? EXTENSION_MAP[extension] || 'other' : 'other';
  return FILE_TYPE_MAP[fileType];
};

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的大小字符串
 */
export const formatFileSize = (size: string | number): string => {
  if (!size) return '';

  const sizeNum = typeof size === 'string' ? parseFloat(size) : size;
  if (isNaN(sizeNum)) return '';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = sizeNum;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(1)} ${units[index]}`;
};

/**
 * 下载文件
 * @param fileUrl 文件URL
 * @param fileName 文件名
 */
export const downloadFile = (fileUrl: string, fileName: string): void => {
  const link = document.createElement('a');
  link.href = fileUrl;
  link.download = fileName;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/**
 * 检查URL是否有效
 * @param url
 * @returns
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * 获取文件预览URL（针对Office文件）
 * @param fileUrl 原始文件URL
 * @param fileType 文件类型
 * @returns 预览URL
 */
export const getPreviewUrl = (fileUrl: string, fileType: FileType): string => {
  if (fileType === 'excel' || fileType === 'word') {
    // 使用微软Office在线预览服务
    return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrl)}`;
  }
  return fileUrl;
};
