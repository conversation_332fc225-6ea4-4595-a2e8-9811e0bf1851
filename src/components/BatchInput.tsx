import React, { useEffect, useState } from 'react';
import { Input, Modal, Icon, message } from 'antd';
interface BatchInputProps {
  label: string;
  value?: string;
  onChange?: (value?: string) => void;
  maxLength?: number;
  clearable?: boolean;
  maxRow?: number;
  style?: React.CSSProperties;
}
const BatchInput: React.FC<BatchInputProps> = ({
  value,
  onChange,
  label,
  maxLength,
  clearable,
  style,
  maxRow,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [textValue, setTextValue] = useState(value);
  const handleModalOk = () => {
    if (textValue && maxRow && textValue?.split('\n')?.length > maxRow) {
      message.error('最多输入' + maxRow + '行');
      return;
    }
    setModalVisible(false);
    const value = textValue
      ?.split('\n')
      .filter((i) => i)
      .join(' ');
    setInputValue(value);
    onChange?.(value);
  };
  useEffect(() => {
    console.log('选品池', value);

    setInputValue(value || undefined);
  }, [value]);

  return (
    <div>
      <Input
        value={inputValue}
        onChange={(e) => {
          const value = e.target.value;
          if (clearable) {
            onChange?.(value === '' ? undefined : value);
            setInputValue(value === '' ? undefined : value);
            return;
          }
          if (maxRow && e.target?.value?.split(' ')?.length > maxRow) {
            const list = e.target?.value?.split(' ');
            e.target.value = list.slice(0, 20).join(' ');
            onChange && onChange(e.target.value);
            setInputValue(e.target.value);
            return;
          }
          onChange?.(value);
          setInputValue(value);
        }}
        allowClear
        placeholder={'请输入' + label}
        // addonAfter={
        // onFocus={() => setModalVisible(true)}
        suffix={
          <Icon
            style={{ marginRight: '-2px' }}
            type="double-right"
            onClick={() => {
              const value = inputValue
                ?.split(' ')
                .filter((i) => i)
                .join('\n');
              setTextValue(value);
              setModalVisible(true);
            }}
          />
        }
      />
      <Modal
        title={label}
        visible={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
      >
        <p
          style={{
            height: '20px',
            fontWeight: '400',
            fontSize: '12px',
            color: '#666666',
            lineHeight: '20px',
            textAlign: 'left',
            marginTop: '-12px',
            marginBottom: '6px',
          }}
        >
          多个{label}换行输入即可
        </p>
        <Input.TextArea
          rows={6}
          allowClear
          value={textValue}
          onChange={(e) => setTextValue(e.target.value)}
          maxLength={maxLength}
        />
      </Modal>
    </div>
  );
};

export default BatchInput;
