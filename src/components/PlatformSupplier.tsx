import React, { useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import { getSupplierList } from '@/services/gql/supplier-list/list';
const { Option } = Select;

const PlatformSupplier = (props) => {
  const { onChange, value } = props;
  const [options, setOptions] = useState([]);
  const [fetching, setFetching] = useState(false);
  const [list, setList] = useState([]);
  const onSearch = (value?: string) => {
    getSupplierList({
      providerType: props?.providerType,
      supplierName: value,
      current: 1,
      size: 50,
    }).then((res) => {
      // console.log('res', res);
      if (res?.code === '200') {
        const arr = res.result?.supplierByPage?.records?.map((i) => {
          return {
            value: i.id,
            label: i.providerName,
          };
        });
        setList([...arr]);
      }
    });
  };
  const handleSearch = debounce((value) => {
    onSearch(value);

    //   setOptions(results);
    //   setFetching(false);
  }, 300);
  useEffect(() => {
    onSearch();
  }, []);
  useEffect(() => {
    list.length > 0 && setOptions([...list]);
  }, [list]);
  return (
    <Select
      allowClear
      showSearch
      placeholder="请选择"
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      value={value}
      labelInValue={props?.labelInValue}
      onSearch={handleSearch}
      {...props}
      onChange={(e) => {
        console.log('e', e);
        onChange(e);
      }}
      notFoundContent={fetching ? <Spin size="small" /> : null}
    >
      {options.map((item, index) => (
        <Option key={index} value={item.value}>
          {item.label}
        </Option>
      ))}
    </Select>
  );
};

export default PlatformSupplier;
