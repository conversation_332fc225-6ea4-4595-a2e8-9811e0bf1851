// /befriend-service-goods/open/goods/cate/queryForTalentGoods
import React, { useEffect, useRef, useState } from 'react';
import { Button, Row, Col, Input, Select } from 'antd';
import { getCateTree } from '@/services/goods/index';
interface Props {
  onChange?: (value: any) => void;
  value?: any;
  mode?: 'multiple' | 'tags' | null;
}
const CateSelect: React.FC<Props> = (props) => {
  const { value, onChange, mode } = props;
  const [dataList, setDataList] = useState([]); //直播间列表
  const getList = () => {
    getCateTree().then((res) => {
      console.log(res);
      if (res?.res?.code === '200') {
        const resData = res?.res?.result;
        resData?.length > 0 ? setDataList([...resData]) : setDataList([]);
      } else {
        setDataList([]);
      }
    });
  };
  useEffect(() => {
    getList();
  }, []);

  return (
    <Select
      value={value}
      mode={mode}
      allowClear
      onChange={onChange}
      filterOption={true}
      optionFilterProp="children"
      placeholder="请选择"
    >
      {dataList?.map((i, index) => {
        return (
          <Option value={i?.cateId} key={index}>
            {i?.cateName}
          </Option>
        );
      })}
    </Select>
  );
};
export default CateSelect;
