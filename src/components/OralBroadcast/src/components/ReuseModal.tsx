import React, { useEffect, useState } from 'react';
import { Form, Input, message, Modal, Table, InputNumber } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import styles from '@/styles/index.module.less';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { useTable, useRowSelection } from '../hooks';
import PaginationProxy from '@/common/constants/Pagination';
import DesensitizationOral from './DesensitizationOral';
import { useRequest } from 'ahooks';
import { spokenScriptInfoReuse } from '../services';
import { usePoint } from '../hooks';

interface IProps extends ModalProps {
  platformSpuId: string;
  form: WrappedFormUtils;
  handleSetValue: (value: string) => void;
  spokenScriptInfoId: any;
  onRefresh: any;
}

const ReuseModal = (props: IProps) => {
  const { visible, form, platformSpuId, handleSetValue, spokenScriptInfoId, onRefresh, ...rest } =
    props;
  const [saveSpuId, setSaveSpuId] = useState<string>('');

  const { openGoodsDetailReuse } = usePoint();

  const {
    columns,
    pagination,
    preview,
    closePreview,
    dataSource,
    onPageChange,
    listLoading,
    getList,
    setCondition,
  } = useTable(saveSpuId);
  const { rowSelection, selectedRows, clearSelection } = useRowSelection();

  const { run: reuseRun, loading: reuseLoading } = useRequest(spokenScriptInfoReuse, {
    manual: true,
    onSuccess({ res }) {
      openGoodsDetailReuse();
      if (!res?.success) {
        message.error(res?.message || '网络异常');
        return;
      }
      onRefresh();
      rest?.onCancel();
    },
  });

  const handleSearch = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }
      const { platformSpuId: curPlatformSpuId, ...values } = form.getFieldsValue();
      setSaveSpuId(curPlatformSpuId);
      setCondition(values);
      getList({
        platformSpuId: curPlatformSpuId,
        current: pagination.current,
        size: pagination.size,
        ...values,
      });
    });
  };

  const handelReset = () => {
    form.resetFields();
    setSaveSpuId('');
    getList({
      current: pagination.current,
      size: pagination.size,
    });
  };

  useEffect(() => {
    if (!visible) {
      clearSelection();
      return;
    }
    if (visible && platformSpuId) {
      getList({
        platformSpuId,
        current: pagination.current,
        size: pagination.size,
      });
      setSaveSpuId(platformSpuId);
      // setTimeout(() => {
      //   form.setFieldsValue({
      //     platformSpuId,
      //   });
      // }, 100);
    }
  }, [visible, platformSpuId]);

  const handleComfim = () => {
    if (!selectedRows?.length) {
      message.warning('请选择要复用的口播稿');
      return;
    }
    handleSetValue(selectedRows[0]?.spuSpokenScriptInfo || null);
    reuseRun({
      spokenScriptInfoId: selectedRows[0]?.id,
      currSpokenScriptInfoId: spokenScriptInfoId,
    });
  };

  const handleNumber = (rule: any, value: any, callback: any) => {
    const reg = /^\d*$/;
    if (reg.test(value)) {
      callback();
      return;
    }
    callback('只能输入数字');
  };

  return (
    <Modal
      title="相关口播稿"
      {...rest}
      visible={visible}
      width={1200}
      className={styles['modal-sty']}
      maskClosable={false}
      onOk={handleComfim}
      style={{ top: '20px' }}
      okButtonProps={{
        loading: reuseLoading,
      }}
    >
      <div className={styles.publishFeeContainer} style={{ padding: 0 }}>
        <SearchFormComponent
          form={form}
          rowShowNum={3}
          options={{
            platformSpuId: {
              label: '平台商品ID',
              renderNode: <Input placeholder="请输入" maxLength={20} />,
              hocOptions: {
                initialValue: saveSpuId,
                rules: [
                  {
                    validator: handleNumber,
                  },
                ],
              },
            },
            spuName: {
              label: '商品名称',
              renderNode: <Input placeholder="请输入" maxLength={30} />,
            },
          }}
          loading={false}
          onSearch={handleSearch}
          onReset={handelReset}
        />
        <Table
          columns={columns as any}
          dataSource={dataSource}
          pagination={false}
          rowSelection={rowSelection}
          rowKey={'id'}
          scroll={{ y: 600, x: '100%' }}
          loading={listLoading || reuseLoading}
        />
        <div style={{ marginTop: '8px' }}>
          {/* @ts-ignore */}
          <PaginationProxy
            {...pagination}
            onChange={({ current, size }: any) => {
              onPageChange(current, size);
            }}
            valueType="merge"
            showQuickJumper={false}
            pageSizeOptions={['2', '5', '10']}
          />
        </div>
      </div>
      <Modal
        title="预览"
        width={888}
        footer={null}
        className={styles['modal-sty']}
        maskClosable={false}
        visible={preview.visible}
        onCancel={closePreview}
      >
        <DesensitizationOral detail={preview.detail} />
      </Modal>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(ReuseModal));
