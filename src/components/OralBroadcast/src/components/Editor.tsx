import React, { useRef, useCallback, useState, useEffect } from 'react';
import BraftEditor from 'braft-editor';
import { ContentUtils } from 'braft-utils/dist';
import 'braft-editor/dist/index.css';
import { ControlType, ExtendControlType, ImageControlType } from 'braft-editor/index.d';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Form, Upload, Button, Icon, message } from 'antd';
import styles from '../index.module.less';
import { debounce } from 'lodash';
import { notification } from 'antd';

interface IProps {
  form: WrappedFormUtils;
  labelKey: string;
  initialValue: string;
  isControl?: boolean;
}

const Editor = (props: IProps) => {
  const { form, labelKey, initialValue, isControl = false } = props;
  const { getFieldValue, getFieldDecorator } = form;
  const editorRef = useRef(null);
  const [isScroll, setIsScroll] = useState<boolean>(false);
  const controls = [
    // 'undo',
    // 'redo',
    // 'separator',
    'font-size',
    'line-height',
    // 'letter-spacing',
    'separator',
    'text-color',
    'bold',
    // 'italic',
    // 'underline',
    'separator',
    'text-align',
    'separator',
    // 'list-ul',
    // 'headings',

    // 'blockquote',
    // 'separator',
  ] as ControlType[];

  const imageControls = [
    // 'float-left', // 设置图片左浮动
    // 'float-right', // 设置图片右浮动
    'align-left', // 设置图片居左
    'align-center', // 设置图片居中
    'align-right', // 设置图片居右
    // 'link', // 设置图片超链接
    'size', // 设置图片尺寸
    'remove', // 删除图片
  ] as ImageControlType[];
  const editorState = getFieldValue(labelKey);
  const onFilesChange = ({ file }: any) => {
    // 兼容旧接口
    const response = file.response;
    if (Number(response.code) == 200) {
      const fileUrl = response?.result;
      const newState = ContentUtils.insertMedias(editorState, [
        {
          type: 'IMAGE',
          url: fileUrl,
        },
      ]);
      form.setFieldsValue({ [labelKey]: newState });
    } else {
      message.error(file.name + ' 上传失败！');
    }
  };
  const extendControls = [
    {
      key: 'antd-uploader',
      type: 'component',
      component: (
        <Upload
          accept="image/*"
          headers={{
            Accept: 'application/json',
            Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
          }}
          // action={uploadUrl}
          action={'https://api-prod.jinweihaowu.com/rss/open/resource/uploadFile'} // 统一用线上的公用文件上传接口
          name="file"
          data={{ typeCode: 'TASK_POSTER' }}
          showUploadList={false}
          onChange={onFilesChange}
        >
          <Button type="link">
            <Icon type="picture" theme="filled" />
          </Button>
        </Upload>
      ),
    },
  ] as ExtendControlType[];

  const args = {
    message: '提示',
    description: '当前口播稿存在分页，请留意。',
    duration: 0,
    onClose: useCallback(() => {
      console.log('------->');
      setIsScroll(false);
    }, [setIsScroll]),
  };
  const checkScrollbarDebounced = useCallback(
    debounce(() => {
      // notification.destroy();
      if (editorRef.current) {
        const editorContainer = editorRef.current.querySelector('.public-DraftEditor-content');
        if (editorContainer) {
          // console.log(editorContainer.scrollHeight, editorContainer.clientHeight);
          const firstChildDiv = editorContainer.firstChild;
          if (!firstChildDiv) {
            return;
          }
          console.log(firstChildDiv.clientHeight, isScroll);
          const hasScrollbar = firstChildDiv.clientHeight > 400;
          if (!isScroll && hasScrollbar) {
            console.log('滚动条状态:', hasScrollbar);
            setIsScroll(true);

            notification.warning(args);
          }

          if (isScroll && !hasScrollbar) {
            console.log('无滚动条状态:', hasScrollbar);
            setIsScroll(false);
            notification.destroy();
          }
        }
      }
    }, 300), // 300ms防抖间隔
    [isScroll], // 依赖数组为空表示只创建一次
  );

  const handleChange = () => {
    checkScrollbarDebounced(); // 触发防抖检查
    // 其他需要即时处理的变化逻辑...
  };

  useEffect(() => {
    return () => {
      notification.destroy();
    };
  }, []);
  return (
    <div
      style={{ width: '100%', height: '100%', overflow: 'hidden' }}
      className={styles['editor-box']}
      ref={editorRef}
    >
      <Form.Item>
        {getFieldDecorator(labelKey, {
          initialValue: BraftEditor.createEditorState(initialValue || null),
        })(
          <BraftEditor
            controls={controls}
            extendControls={extendControls}
            imageControls={imageControls}
            colors={[
              '#000000',
              '#333333',
              '#2c3e50',
              '#666666',
              '#7f8c8d',
              '#999999',
              '#cccccc',
              '#ffffff',
              '#00ff00',
              '#61a951',
              '#0000ff',
              '#16a085',
              '#ff0000',
              '#07a9fe',
              '#003ba5',
              '#8e44ad',
              '#f32784',
              '#c0392b',
              '#d35400',
              '#f39c12',
              '#fdda00',
            ]}
            textAligns={['left', 'center', 'right']}
            // readOnly={disabled || view}
            onChange={isControl ? handleChange : undefined}
          />,
        )}
      </Form.Item>
    </div>
  );
};

export default Editor;
