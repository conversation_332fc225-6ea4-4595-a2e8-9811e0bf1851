import React, { useState } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Modal, Form, message, Input } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import styles from '@/styles/index.module.less';
import { spokenScriptInfoSaveNewVersion, spokenScriptInfoSave } from '../services';
import { useRequest } from 'ahooks';
import { usePoint } from '../hooks';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  spokenScriptInfoId: string;
  getParams: any;
  isDetail?: boolean;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const AddVersion = (props: IProps) => {
  const {
    form,
    visible,
    onRefresh,
    size,
    spokenScriptInfoId,
    getParams,
    isDetail = false,
    ...rest
  } = props;

  const { getFieldDecorator } = form;

  const [spokenScriptInfoSaveLoading, setSpokenScriptInfoSaveLoading] = useState<boolean>(false);

  const { openGoodsDetailSaveNewVersion } = usePoint();

  const { run, loading } = useRequest(spokenScriptInfoSaveNewVersion, {
    manual: true,
    onSuccess({ res }) {
      openGoodsDetailSaveNewVersion();
      if (!res?.success) {
        message.warning(res?.message || '');
        return;
      }
      message.success('添加成功');
      onRefresh?.();
      rest?.onCancel();
    },
  });

  const handleSubmit = async (value: any) => {
    setSpokenScriptInfoSaveLoading(true);
    try {
      const params = await getParams();
      const { res } = await spokenScriptInfoSave(params);
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        setSpokenScriptInfoSaveLoading(false);
        return;
      }
      setSpokenScriptInfoSaveLoading(false);
      run({
        spokenScriptInfoId,
        versionRemark: value?.versionRemark,
      });
    } catch (error) {
      console.log('🚀 ~ handleSubmit ~ error:', error);
      setSpokenScriptInfoSaveLoading(false);
    }
    // getParams().then((res: any) => {
    //   console.log('🚀 ~ getParams ~ res:', res, value);
    // });
  };

  const handleOk = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }

      if (isDetail) {
        run({
          spokenScriptInfoId,
          versionRemark: value?.versionRemark,
        });
        return;
      }
      handleSubmit(value);
    });
  };

  return (
    <Modal
      title="版本号"
      {...rest}
      visible={visible}
      width={500}
      className={styles['modal-sty']}
      maskClosable={false}
      okButtonProps={{
        loading: loading || spokenScriptInfoSaveLoading,
      }}
      onOk={handleOk}
    >
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} label="版本号" style={{ margin: '0' }}>
          V{size || 1}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="版本备注" style={{ margin: '0' }}>
          {getFieldDecorator('versionRemark', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(<Input placeholder="请输入" maxLength={100} />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(AddVersion));
