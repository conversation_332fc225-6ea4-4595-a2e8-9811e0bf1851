const signMap = {
  '/pim/public/spokenScriptInfo/detail': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptInfo/save': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptInfo/saveAndSubmit': 'basic.x1.xxxx1',
  '/tools/public/operation/queryOperationLogList': 'basic.x1.xxxx1',
  '/pim/public/algorithmExchange/createPpt': 'basic.x1.xxxx1',
  '/pim/public/algorithmExchange/getPpt': 'basic.x1.xxxx1',
  '/pim/public/algorithmExchange/createScriptHtml': 'basic.x1.xxxx1',
  '/pim/public/algorithmExchange/getScriptHtml': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptAudit/auditResultPage': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptInfo/list': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptInfo/saveNewVersion': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptInfo/page': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptInfo/reuse': 'basic.x1.xxxx1',
  '/pim/public/spokenScriptInfo/checkSensitive': 'basic.x1.xxxx1',
  '/goods/public/supplierSpu/checkGenerateScriptHtml': 'basic.x1.xxxx1',
  '/tools/public/dataBuriedPoint/add': 'basic.x1.xxxx1',
};

export function getSign(path: string): string {
  return signMap[path];
}
