import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type SpokenScriptInfoDetailRequest = {
  spokenScriptInfoId?: string /*口播稿id*/;
};

export type SpokenScriptInfoDetailResult = {
  cateName?: string /*类目名称*/;
  discount?: string /*折扣*/;
  id?: string /*id*/;
  logisticsInfo?: string /*物流信息*/;
  mechanism?: string /*机制*/;
  platformSpuId?: string /*平台商品id*/;
  retailPriceStr?: string /*零售价*/;
  salePriceStr?: string /*直播价*/;
  selectGoodsPoolId?: string /*选品池id*/;
  selectGoodsPoolNo?: string /*选品池编号*/;
  spuBaseInfo?: string /*商品基础信息*/;
  spuId?: string /*商品id*/;
  spuImg?: string /*商品图片*/;
  spuProductName?: string /*产品信息*/;
  spuSpokenScriptInfo?: string /*商品口播稿数据*/;
  taskId?: string /*任务ID*/;
  taskStatus?:
    | 'WAIT_PROCESS'
    | 'PROCESSING'
    | 'COMPLETED'
    | 'FAILED' /*任务状态: 0-待处理 1-处理中 2-已完成 3-失败[SpokenScriptTaskStatusEnum]*/;
  type?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
};

/**
 *根据口播稿id查询口播稿详情
 */
export const spokenScriptInfoDetail = (params: SpokenScriptInfoDetailRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptInfoDetailResult>>(
    '/pim/public/spokenScriptInfo/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/detail') },
    },
  );
};

export type SpokenScriptInfoSaveRequest = {
  cateName?: string /*类目名称*/;
  discount?: string /*折扣*/;
  id?: string /*id*/;
  logisticsInfo?: string /*物流信息*/;
  mechanism?: string /*机制*/;
  retailPriceStr?: string /*零售价*/;
  salePriceStr?: string /*直播价*/;
  spuBaseInfo?: string /*商品基础信息*/;
  spuImg?: string /*商品图片*/;
  spuProductName?: string /*产品信息*/;
  spuSpokenScriptInfo?: string /*商品口播稿数据*/;
  type?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
};

export type SpokenScriptInfoSaveResult = boolean;

/**
 *口播搞保存
 */
export const spokenScriptInfoSave = (params: SpokenScriptInfoSaveRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptInfoSaveResult>>(
    '/pim/public/spokenScriptInfo/save',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/save') },
    },
  );
};

export type SpokenScriptInfoSaveAndSubmitRequest = {
  cateName?: string /*类目名称*/;
  discount?: string /*折扣*/;
  id?: string /*id*/;
  logisticsInfo?: string /*物流信息*/;
  mechanism?: string /*机制*/;
  retailPriceStr?: string /*零售价*/;
  salePriceStr?: string /*直播价*/;
  spokenScriptImg?: string /*口播稿图片*/;
  spuBaseInfo?: string /*商品基础信息*/;
  spuImg?: string /*商品图片*/;
  spuProductName?: string /*产品信息*/;
  spuSpokenScriptInfo?: string /*商品口播稿数据*/;
  type?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
};

export type SpokenScriptInfoSaveAndSubmitResult = boolean;

/**
 *口播搞保存并提交
 */
export const spokenScriptInfoSaveAndSubmit = (params: SpokenScriptInfoSaveAndSubmitRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptInfoSaveAndSubmitResult>>(
    '/pim/public/spokenScriptInfo/saveAndSubmit',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/saveAndSubmit') },
    },
  );
};

export type QueryOperationLogListForPlatformRequest = {
  bizOrderId?: string /*业务单据id*/;
  bizOrderNo?: string /*业务单据编号*/;
  bizOrderType?: string /*业务单据类型*/;
  bizOrderTypeList?: Array<string> /*业务单据类型*/;
  bizTypeModels?: Array<{
    bizOrderType?: string /*业务单据类型*/;
    secondBizTypes?: Array<string> /*二级业务单据类型*/;
  }> /*一二级业务单据类型集合*/;
  current?: number /*当前页码,从1开始*/;
  employeeIdList?: Array<string> /*用户id*/;
  operateDesc?: string /*操作类型描述*/;
  operateEndTime?: string /*操作结束时间*/;
  operateStartTime?: string /*操作开始时间*/;
  operateType?: string /*操作类型*/;
  operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
  operatorName?: string /*操作人名称*/;
  size?: number /*分页大小*/;
};

export type QueryOperationLogListForPlatformResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    bizOrderId?: string /*业务单据id*/;
    bizOrderNo?: string /*业务单据编号*/;
    bizOrderType?: string /*业务单据类型*/;
    contentSnapshot?: string /*操作之前的快照*/;
    id?: string /*主键*/;
    operateDesc?: string /*操作类型*/;
    operateTime?: string /*操作时间*/;
    operateType?: string /*操作类型*/;
    operatorAccount?: string /*操作人账号*/;
    operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
    operatorContent?: string /*操作内容*/;
    operatorId?: string /*操作人id*/;
    operatorName?: string /*操作人姓名*/;
    secondBizType?: string /*二级业务单据类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询操作日志
 */
export const queryOperationLogListForPlatform = (
  params: QueryOperationLogListForPlatformRequest,
) => {
  return Fetch<ResponseWithResult<QueryOperationLogListForPlatformResult>>(
    '/tools/public/operation/queryOperationLogList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/operation/queryOperationLogList') },
    },
  );
};

export type AlgorithmExchangeCreatePptRequest = {
  id?: string /*id*/;
  supplementaryCueWords?: string /*补充提示词*/;
};

export type AlgorithmExchangeCreatePptResult = string;

/**
 *根据口播稿id创建口播稿ppt
 */
export const algorithmExchangeCreatePpt = (params: AlgorithmExchangeCreatePptRequest) => {
  return Fetch<ResponseWithResult<AlgorithmExchangeCreatePptResult>>(
    '/pim/public/algorithmExchange/createPpt',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/algorithmExchange/createPpt') },
    },
  );
};

export type AlgorithmExchangeGetPptRequest = {
  companyInfoId?: string;
  operatorId?: string;
  platform?: boolean;
  spokenScriptInfoId?: string /*口播稿id*/;
  supplier?: boolean;
  supplierId?: string;
  talentId?: string;
  taskId?: string /*任务id*/;
};

export type AlgorithmExchangeGetPptResult = string;

/**
 *根据任务id查询口播稿ppt
 */
export const algorithmExchangeGetPpt = (params: AlgorithmExchangeGetPptRequest) => {
  return Fetch<ResponseWithResult<AlgorithmExchangeGetPptResult>>(
    '/pim/public/algorithmExchange/getPpt',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/algorithmExchange/getPpt') },
    },
  );
};

export type AlgorithmExchangeCreateScriptHtmlRequest = {
  id?: string /*id*/;
  supplementaryCueWords?: string /*补充提示词*/;
};

export type AlgorithmExchangeCreateScriptHtmlResult = string;

/**
 *根据口播稿id创建口播稿html
 */
export const algorithmExchangeCreateScriptHtml = (
  params: AlgorithmExchangeCreateScriptHtmlRequest,
) => {
  return Fetch<ResponseWithResult<AlgorithmExchangeCreateScriptHtmlResult>>(
    '/pim/public/algorithmExchange/createScriptHtml',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/algorithmExchange/createScriptHtml') },
    },
  );
};

export type AlgorithmExchangeGetScriptHtmlRequest = {
  companyInfoId?: string;
  operatorId?: string;
  platform?: boolean;
  spokenScriptInfoId?: string /*口播稿id*/;
  supplier?: boolean;
  supplierId?: string;
  talentId?: string;
  taskId?: string /*任务id*/;
};

export type AlgorithmExchangeGetScriptHtmlResult = string;

/**
 *根据任务id查询口播稿html
 */
export const algorithmExchangeGetScriptHtml = (params: AlgorithmExchangeGetScriptHtmlRequest) => {
  return Fetch<ResponseWithResult<AlgorithmExchangeGetScriptHtmlResult>>(
    '/pim/public/algorithmExchange/getScriptHtml',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/algorithmExchange/getScriptHtml') },
    },
  );
};

export type SpokenScriptAuditResultPageRequest = {
  current?: number /*当前页码,从1开始*/;
  idList?: Array<string> /*口播稿审核id列表*/;
  size?: number /*分页大小*/;
  spokenScriptInfoId?: string /*口播稿记录id*/;
};

export type SpokenScriptAuditResultPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    auditResultImg?: string /*法务反馈结果图片列表*/;
    auditResultText?: string /*法务反馈结果文字描述*/;
    auditTime?: string /*审核时间*/;
    auditor?: string /*审核人id*/;
    auditorName?: string /*审核人名称*/;
    id?: string /*id*/;
    spokenScriptInfoId?: string /*口播稿记录id*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询口播稿素材反馈意见
 */
export const spokenScriptAuditResultPage = (params: SpokenScriptAuditResultPageRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptAuditResultPageResult>>(
    '/pim/public/spokenScriptAudit/auditResultPage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptAudit/auditResultPage') },
    },
  );
};

export type SpokenScriptInfoListRequest = {
  selectGoodsPoolId?: string /*商品id*/;
  selectGoodsPoolNo?: string /*选品池编号*/;
};

export type SpokenScriptInfoListResult = Array<{
  spokenScriptInfoId?: string /*口播稿id*/;
  type?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
  versionId?: number /*版本*/;
  versionRemark?: string /*版本备注*/;
}>;

/**
 *根据选品池编号查询口播稿列表
 */
export const spokenScriptInfoList = (params: SpokenScriptInfoListRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptInfoListResult>>(
    '/pim/public/spokenScriptInfo/list',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/list') },
    },
  );
};

export type SpokenScriptInfoSaveNewVersionRequest = {
  spokenScriptInfoId?: string /*旧版口播稿id*/;
  versionRemark?: string /*版本号备注*/;
};

export type SpokenScriptInfoSaveNewVersionResult = boolean;

/**
 *存为新版
 */
export const spokenScriptInfoSaveNewVersion = (params: SpokenScriptInfoSaveNewVersionRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptInfoSaveNewVersionResult>>(
    '/pim/public/spokenScriptInfo/saveNewVersion',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/saveNewVersion') },
    },
  );
};

export type SpokenScriptInfoPageRequest = {
  current?: number /*当前页码,从1开始*/;
  platformSpuId?: string /*平台商品id*/;
  size?: number /*分页大小*/;
  spuName?: string /*商品名称*/;
};

export type SpokenScriptInfoPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    deptName?: string /*事业部名称*/;
    gmtModified?: string /*更新时间*/;
    historySumSales?: string /*历史累计销售额*/;
    id?: string /*口播稿id*/;
    selectGoodsPoolId?: string /*选品池id*/;
    selectGoodsPoolNo?: string /*选品池编号*/;
    spuImg?: string /*商品图片*/;
    spuName?: string /*商品名称*/;
    spuSpokenScriptInfo?: string /*商品口播稿数据*/;
    type?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
    versionId?: number /*版本ID*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *口播稿列表
 */
export const spokenScriptInfoPage = (params: SpokenScriptInfoPageRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptInfoPageResult>>(
    '/pim/public/spokenScriptInfo/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/page') },
    },
  );
};

export type SpokenScriptInfoReuseRequest = {
  currSpokenScriptInfoId?: string /*当前口播稿id*/;
  spokenScriptInfoId?: string /*口播稿id*/;
};

export type SpokenScriptInfoReuseResult = boolean;

/**
 *确认复用
 */
export const spokenScriptInfoReuse = (params: SpokenScriptInfoReuseRequest) => {
  return Fetch<ResponseWithResult<SpokenScriptInfoReuseResult>>(
    '/pim/public/spokenScriptInfo/reuse',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/reuse') },
    },
  );
};

export type CheckSensitiveRequest = {
  id?: string /*id*/;
  spuSpokenScriptInfo?: string /*商品口播稿数据*/;
};

export type CheckSensitiveResult = string;

/**
 *口播搞校验敏感词
 */
export const checkSensitive = (params: CheckSensitiveRequest) => {
  return Fetch<ResponseWithResult<CheckSensitiveResult>>(
    '/pim/public/spokenScriptInfo/checkSensitive',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/spokenScriptInfo/checkSensitive') },
    },
  );
};

export type CheckGenerateScriptHtmlRequest = {
  id?: string /*业务ID*/;
};

export type CheckGenerateScriptHtmlResult = boolean;

/**
 *校验是否能生成口播稿
 */
export const checkGenerateScriptHtml = (params: CheckGenerateScriptHtmlRequest) => {
  return Fetch<ResponseWithResult<CheckGenerateScriptHtmlResult>>(
    '/befriend-service-goods/public/supplierSpu/checkGenerateScriptHtml',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/goods/public/supplierSpu/checkGenerateScriptHtml') },
    },
  );
};

export type DataBuriedPointAddRequest = {
  dataType?: string /*二级类型：CLICK-点击记录，DURATION-停留时长，COUNT-数量统计，UNKNOWN-未定义*/;
  endTime?: string /*结束时间（停留时长场景）*/;
  operationType?: string /*操作类型*/;
  originType?: 'PC' | 'MOBILE' /*来源类型[OriginTypeEnum]*/;
  quantity?: string /*数量（token使用量等）*/;
  startTime?: string /*开始时间（停留时长场景）*/;
  timeDuration?: string /*时间段*/;
};

export type DataBuriedPointAddResult = any;

/**
 *新增数据
 */
export const dataBuriedPointAdd = (params: DataBuriedPointAddRequest) => {
  return Fetch<ResponseWithResult<DataBuriedPointAddResult>>('/tools/public/dataBuriedPoint/add', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/tools/public/dataBuriedPoint/add') },
  });
};
