.oral-box {
  padding-bottom: 16px;
  width: 100%;
  box-sizing: border-box;
  padding-right: 16px;
  &-version_btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -16px;
    padding-bottom: 8px;
  }

  .version-line {
    width: 50%;
    :global {
      .ant-tabs-nav .ant-tabs-tab {
        margin-right: 16px;
      }
      .ant-tabs-nav .ant-tabs-tab-disabled,
      .ant-tabs-nav .ant-tabs-tab-disabled:hover {
        color: rgba(0, 0, 0, 0.2) !important;
      }
    }
  }

  &-btns {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    position: sticky;
    top: -1px;
    background: #ffffff;
    z-index: 10;
  }
}

.tabs_popover {
  :global {
    .ant-popover-inner {
      background: rgba(255, 255, 255, 0.8) !important;
    }
  }
}

.tabs_content_box {
  width: 300px;
  display: flex;
  align-items: flex-start;
  word-break: break-all;
}

.tabs_content {
  font-size: 14px;
  font-weight: bold;
  color: #111111;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.main-oral-audio {
  width: 860px;
  overflow: hidden;

  border: 2px solid #666666;
  // margin-top: 8px;
  display: flex;

  &-left {
    width: 29%;
    flex-shrink: 0;
    border-right: 1px solid #666666;
    display: flex;
    flex-direction: column;
  }

  &-right {
    // flex: 1;
    width: calc(100% - 29%);
    display: flex;
    flex-direction: column;
  }

  .goods-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #666666;
    min-height: 300px;
    max-height: 310px;
    flex: 1;

    .class-box {
      position: absolute;
      top: 0;
      left: 0;
      width: 40%;
      min-height: 40px;
      background: #666666;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      word-break: break-all;
      box-sizing: border-box;
      padding: 8px;
      z-index: 5;
    }

    .draw-image-mark {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }

    > img {
      width: 90%;
      height: 90%;
      object-fit: fill;
    }
  }

  .attribute-box {
    height: 500px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    flex-shrink: 0;
    word-break: break-all;
  }

  .des-attribute-box {
    height: 300px;
    width: 100%;
    overflow-y: hidden;
    overflow-x: hidden;
    flex-shrink: 0;
    word-break: break-all;
    padding: 8px;
  }

  .content-box {
    max-height: 620px;
    min-height: 500px;
    flex: 1;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .des-content-box {
    height: 420px;
    // flex: 1;
    width: 100%;
    overflow-x: hidden;
  }
}

.main-oral {
  width: 100%;
  overflow: hidden;
  // height: 790px;
  border: 2px solid #666666;
  // margin-top: 8px;
  display: flex;
  &-left {
    width: 29%;
    flex-shrink: 0;
    border-right: 1px solid #666666;
    display: flex;
    flex-direction: column;
  }
  &-right {
    // flex: 1;
    width: calc(100% - 29%);
    display: flex;
    flex-direction: column;
  }
  .goods-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #666666;
    min-height: 300px;
    max-height: 310px;
    flex: 1;
    .class-box {
      position: absolute;
      top: 0;
      left: 0;
      width: 40%;
      min-height: 40px;
      background: #666666;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      word-break: break-all;
      box-sizing: border-box;
      padding: 8px;
      z-index: 5;
    }
    .draw-image-mark {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }
    > img {
      width: 90%;
      height: 90%;
      object-fit: fill;
    }
  }
  .attribute-box {
    height: 500px;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    flex-shrink: 0;
    word-break: break-all;
  }
  .des-attribute-box {
    height: 300px;
    width: 100%;
    overflow-y: hidden;
    overflow-x: hidden;
    flex-shrink: 0;
    word-break: break-all;
    padding: 8px;
  }
  .content-box {
    max-height: 620px;
    min-height: 500px;
    flex: 1;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .des-content-box {
    height: 420px;
    // flex: 1;
    width: 100%;
    overflow-x: hidden;
  }
}

.mes-layout {
  border-top: 1px solid #666666;
  display: flex;
  margin-top: -1px;
  &-main {
    flex: 1 1 0;
    flex-shrink: 0;
  }
  &-assist {
    flex: 1 1 0;
    border-left: 1px solid #666666;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
  }
  textarea {
    resize: none !important;
  }
}

.mes-line {
  display: flex;
  border-bottom: 1px solid #666666;
  &-title_reuse {
    background: #666666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 12px;
    word-break: break-all;
    box-sizing: border-box;
    // min-height: 60px;
    padding: 8px;
    flex-shrink: 0;
    border-bottom: 1px solid #cccccc;
  }
  &-title {
    background: #666666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 16px;
    word-break: break-all;
    box-sizing: border-box;
    // min-height: 60px;
    padding: 8px;
    flex-shrink: 0;
    border-bottom: 1px solid #cccccc;
  }
  &-leftTitle {
    width: 50px;
  }
  &-leftTitle_reuse {
    width: 40px;
  }
  &-rightTitle {
    width: 80px;
  }
  &-rightTitle_reuse {
    width: 40px;
  }
  &-content {
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    word-break: break-all;
    white-space: pre-wrap;
  }
}

.editor-box {
  :global {
    .ant-form-item {
      height: 100%;
    }
    .ant-form-item-control-wrapper {
      height: 100%;
    }
    .ant-form-item-control {
      height: 100%;
    }
    .ant-form-item-children {
      height: 100%;
    }
    .ant-btn-link {
      color: #6a6f7b;
      padding: 0 8px;
      margin-top: 6px;
    }
    .bf-container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .bf-content {
      flex: 1 !important;
    }
    .bf-line-height-dropdown {
      min-width: unset;
    }
  }
}

.screen-shot-modal {
  :global {
    .ant-modal-confirm-btns {
      display: none !important;
    }
  }
}

.service-form {
  :global {
    .ant-col-8 {
      width: 96px !important;
    }
  }
}

.ppt-modal {
  display: flex;
  font-size: 16px;
  // height: 60px;
  font-weight: 500;
  align-items: flex-start;
  margin-bottom: 12px;
  justify-content: center;
  color: #111111;
}

.dots-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding-bottom: 12px;
}

.dot {
  height: 10px;
  width: 10px;
  margin-right: 10px;
  border-radius: 10px;
  background-color: #b3d4fc;
  animation: pulse 1.5s infinite ease-in-out;
}

.dot:last-child {
  margin-right: 0;
}

.dot:nth-child(1) {
  animation-delay: -0.3s;
}

.dot:nth-child(2) {
  animation-delay: -0.1s;
}

.dot:nth-child(3) {
  animation-delay: 0.1s;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    background-color: #b3d4fc;
    box-shadow: 0 0 0 0 rgba(178, 212, 252, 0.7);
  }

  50% {
    transform: scale(1.2);
    background-color: #0d6dea;
    box-shadow: 0 0 0 10px rgba(178, 212, 252, 0);
  }

  100% {
    transform: scale(0.8);
    background-color: #b3d4fc;
    box-shadow: 0 0 0 0 rgba(178, 212, 252, 0.7);
  }
}

.ai-line {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #111111;
  margin-bottom: 12px;
}

.btn-or {
  background: #faad14;
  color: #ffffff;
  border: none;
  &:hover {
    background: #faad14 !important;
    color: #ffffff;
  }
  &:focus {
    background: #faad14 !important;
    color: #ffffff;
  }
  &:active {
    background: #faad14 !important;
    color: #ffffff;
  }
}

.btn-or[disabled] {
  background: #f5f5f5 !important;
  color: #cccccc !important;
}

.create-ai-modal {
  &-content {
    display: flex;
    align-items: flex-start;
  }
}
