import { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { algorithmExchangeCreateScriptHtml, algorithmExchangeGetScriptHtml } from '../services';
import { message } from 'antd';
import { usePoint } from './usePoint';

export const useAICreate = (detail: any, getDetail: any) => {
  const [progressValue, setProgressValue] = useState<number>(0);
  const [aiVisible, setAIVisible] = useState<boolean>(false);
  const [createAIVisible, setCreateAIVisible] = useState<boolean>(false); // AI生成口播稿弹窗
  // const [aiText, setAIText] = useState<string>('');

  const { openGoodsDetailAICreate } = usePoint();

  const taskId = useRef<string>('');

  const { run: createHtmlRun, loading: createHtmlLoading } = useRequest(
    algorithmExchangeCreateScriptHtml,
    {
      manual: true,
      onSuccess({ res }) {
        setCreateAIVisible(false);
        if (res?.success) {
          getDetail();
          // taskId.current = res?.result;
          // getHtmlRun({
          //   taskId: res?.result,
          //   spokenScriptInfoId: detail?.id,
          // });
        } else {
          message.warning(res?.message || '网络异常');
          // setAIVisible(false);
          // setProgressValue(0);
        }
        openGoodsDetailAICreate();
      },
    },
  );

  const { cancel: getHtmlCancel, run: getHtmlRun } = useRequest(algorithmExchangeGetScriptHtml, {
    manual: true,
    pollingInterval: 20000,
    onSuccess({ res }) {
      if (res?.success) {
        const data = res?.result;
        if (data) {
          // setAIText(data || '');
          // cb?.();
          // setProgressValue(100);
          // setAIVisible(false);
          getHtmlCancel();
          // @TODO: 再次调用详情接口
        }
      } else {
        message.warning(res?.message || '网络异常');
        getHtmlCancel();
        setAIVisible(false);
        setProgressValue(0);
      }
    },
  });

  const cancelCreate = () => {
    getHtmlCancel?.();
    setAIVisible(false);
    setProgressValue(0);
  };

  // useEffect(() => {
  //   return getHtmlCancel?.();
  // }, []);

  return {
    createHtmlRun,
    createHtmlLoading,
    getHtmlCancel,
    progressValue,
    aiVisible,
    // aiText,
    setAIVisible,
    setProgressValue,
    cancelCreate,
    createAIVisible,
    setCreateAIVisible,
  };
};
