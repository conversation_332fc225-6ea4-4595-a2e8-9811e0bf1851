import React from 'react';
import { useRequest } from 'ahooks';
import { dataBuriedPointAdd } from '../services';

export const usePoint = () => {
  const { run: addPointRun, loading: addPointLoading } = useRequest(dataBuriedPointAdd, {
    manual: true,
  });

  // 用户打开商品详情-口播稿点击次数统计
  const openGoodsDetail = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_SCRIPT_CLICK_COUNT',
      originType: 'PC',
    });
  };

  // 用户打开商品详情-AI生成口播稿按钮
  const openGoodsDetailAICreate = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_AI_GENERATE_SCRIPT',
      originType: 'PC',
    });
  };

  //用户打开商品详情-点击编辑按钮
  const openGoodsDetailEdit = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_EDIT_BUTTON',
      originType: 'PC',
    });
  };

  // 用户打开商品详情-点击下载按钮
  const openGoodsDetailDownload = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_DOWNLOAD_BUTTON',
      originType: 'PC',
    });
  };

  // 用户打开商品详情-从用户点击口播稿页面到离开该页面
  const openGoodsDetailLeave = (params: any) => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_SCRIPT_PAGE_DURATION',
      originType: 'PC',
      ...params,
    });
  };

  // 用户打开商品详情-点击保存按钮
  const openGoodsDetailSave = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_SAVE_BUTTON',
      originType: 'PC',
    });
  };

  // 用户打开商品详情-点击存为新版按钮-点击版本命名且保存后记录
  const openGoodsDetailSaveNewVersion = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_SAVE_AS_NEW_VERSION',
      originType: 'PC',
    });
  };

  // 用户打开商品详情-点击复用按钮-用户选中数据且确定后的记录
  const openGoodsDetailReuse = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_REUSE_BUTTON',
      originType: 'PC',
    });
  };

  //用户打开商品详情-点击保存并提交按钮
  const openGoodsDetailSaveAndSubmit = () => {
    addPointRun({
      operationType: 'PRODUCT_DETAIL_SAVE_AND_SUBMIT',
      originType: 'PC',
    });
  };

  // 用户打开场次货盘-点击批量下载口播稿按钮
  const openGoodsDetailBatchDownload = () => {
    addPointRun({
      operationType: 'SESSION_GOODS_BATCH_DOWNLOAD_SCRIPT',
      originType: 'PC',
    });
  };

  // 用户打开选品池/场次货盘-点击AI生成口播稿
  const goodsAICreate = () => {
    addPointRun({
      operationType: 'SELECTION_POOL_AI_GENERATE_SCRIPT',
      originType: 'PC',
    });
  };

  return {
    addPointRun,
    openGoodsDetail,
    openGoodsDetailAICreate,
    openGoodsDetailEdit,
    openGoodsDetailDownload,
    openGoodsDetailLeave,
    openGoodsDetailSave,
    openGoodsDetailSaveNewVersion,
    openGoodsDetailReuse,
    openGoodsDetailSaveAndSubmit,
    openGoodsDetailBatchDownload,
    goodsAICreate,
  };
};
