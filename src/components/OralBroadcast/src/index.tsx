import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import styles from './index.module.less';
import { Button, message, Modal, Spin, Icon, Progress, Tabs, Popover, Input } from 'antd';
import { MainOral, OtherInfo, MyLoading, AddVersion, ReuseModal } from './components';
import html2canvas from 'html2canvas';
import { base64ToFile } from './utils';
import { debounce } from 'lodash';
import { useDetail, useApi, useDownload, useAICreate, usePoint } from './hooks';
import { TYPES } from './types';
import { AuthWrapper } from 'qmkit';
import { SpokenScriptInfoListResult, checkGenerateScriptHtml } from './services';
import { useRequest } from 'ahooks';
import moment from 'moment';

interface IProps {
  isDetail?: boolean;
  spokenScriptInfoId?: string | undefined;
  onRef?: any;
  // setSpokenScriptInfoId: any;
  currentVersionValue: number;
  versionList: SpokenScriptInfoListResult;
  handleTabsChange: any;
  handleAddVersionRefresh: any;
  platformSpuId: any;
  handleGetVersionList: any;
  refreshOralLog: () => void;
}

const { TabPane } = Tabs;

const OralBroadcast = (props: IProps) => {
  const {
    isDetail = false,
    spokenScriptInfoId,
    onRef,
    // setSpokenScriptInfoId,
    versionList,
    currentVersionValue,
    handleTabsChange,
    handleAddVersionRefresh,
    platformSpuId,
    handleGetVersionList,
    refreshOralLog,
  } = props;
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [saveSubmit, setSaveSubmit] = useState<boolean>(false); // 点击保存并提交

  const [createAIPoint, setCreateAIPoint] = useState<string>(''); // AI生成口播稿提示词

  const MainOralRef = useRef<{
    validate: () => void;
    setFormValue: (params: any) => void;
    reuseSpuSpokenScriptInfo: (value: string | null) => void;
  }>(null);
  const OtherInfoRef = useRef<{
    validate: () => void;
    setFormValue: (params: any) => void;
  }>();

  const { downloadVisible, createPptLoading, createPptRun, setDownloadVisible } =
    useDownload(refreshOralLog);

  const {
    detail,
    detailLoading,
    detailRun,
    setDetail,
    spuImg,
    downloadIamge,
    typeSaveSubmit,
    setTypeSaveSubmit,
    getHtmlCancel,
  } = useDetail(
    // selectGoodsPoolNo,
    spokenScriptInfoId,
    (detail: any) => {
      MainOralRef.current?.setFormValue(detail);
      OtherInfoRef.current?.setFormValue(detail?.type);
    },
    isEdit,
  );

  const {
    openGoodsDetail,
    openGoodsDetailLeave,
    openGoodsDetailEdit,
    openGoodsDetailDownload,
    openGoodsDetailSave,
    openGoodsDetailSaveAndSubmit,
  } = usePoint();

  const onRefresh = () => {
    setIsEdit(false);
    detailRun({ spokenScriptInfoId });
    refreshOralLog();
  };

  const onErrorRefresh = () => {
    setIsEdit(true);
  };

  const {
    spokenScriptInfoSaveLoading,
    spokenScriptInfoSaveRun,
    uploadPic,
    uploadLoading,
    saveParamsRef,
    spokenScriptInfoSaveAndSubmitLoading,
    spokenScriptInfoSaveAndSubmitRun,
    sensitiveWordsVisible,
    setSensitiveWordsVisible,
    checkSensitiveRun,
    checkSensitiveLoading,
    sensitiveWordsString,
  } = useApi({ onRefresh, onErrorRefresh });

  const handleEdit = () => {
    // console.log(typeSaveSubmit, '----------->');
    setIsEdit(true);
    openGoodsDetailEdit();
  };

  // 取消
  const handleCance = () => {
    setIsEdit(false);
    setTimeout(() => {
      downloadIamge(detail?.spuImg as string);
    }, 100);
  };

  const globalGetValue = async (isSubmit = false) => {
    const list = [MainOralRef.current?.validate(), OtherInfoRef.current?.validate()];
    try {
      const res = await Promise.all(list);
      // if (isSubmit) {
      //   setIsEdit(false);
      // }
      const [basic, other] = res || [];
      const params = {
        ...(basic as any),
        ...(other as any),
        // selectGoodsPoolNo,
        id: detail?.id,
      };
      // console.log('🚀 ~ globalGetValue ~ params:', params);
      /**
       * @details 无论是什么情况下的编辑点击保存或者保存并提交 是要type 是 终版要提示 已按照法务审核意见进行相关修改
       */
      if (params?.type === TYPES.END || detail?.type === TYPES.END) {
        Modal.confirm({
          title: '已按照法务审核意见进行相关修改',
          maskClosable: false,
          keyboard: false,
          okText: '确定',
          cancelText: '取消',
          onOk() {
            // 保存
            if (!isSubmit) {
              spokenScriptInfoSaveRun(!params?.type ? { ...params, type: TYPES.END } : params);
              return;
            }
            //
            /**
             * @details  【终版】 口播稿保存或者保存并提交(包含 终版的编辑)按钮，若商品命中敏感词，仍然允许保存成功，但不会触发提交，保存成功
             */
            spokenScriptInfoSaveAndSubmitRun(params);
          },
          onCancel() {
            setSaveSubmit(false);
          },
        });
        return;
      }
      //
      /**
       * @details type 是初版的操作
       */
      // spokenScriptInfoSaveRun(params);
      // 保存
      if (!isSubmit) {
        spokenScriptInfoSaveRun(params);
        return;
      }
      // isSubmit ? (saveParamsRef.current = params) : spokenScriptInfoSaveRun(params);
      // if (params.type === TYPES.END) {
      //   spokenScriptInfoSaveAndSubmitRun(params);
      //   return;
      // }
      /**
       * @details 初版保存并提交 若商品命中敏感词，将对应的敏感词展示在弹窗内确认
       */
      saveParamsRef.current = params;
      // if (isSubmit) {
      //   setDetail({
      //     // spuImg: detail?.spuImg,
      //     ...params,
      //   });
      //   setTimeout(() => {
      //     setIsEdit(false);
      //   }, 500);
      // }
      /**
       * @details 先调用判断敏感词接口 有提示  没有
       */
      checkSensitiveRun(
        { id: detail.id, spuSpokenScriptInfo: params?.spuSpokenScriptInfo },
        params,
      );
    } catch (error) {
      console.log('🚀 ~ handleSave ~ error:', error);
      isSubmit && setSaveSubmit(false);
    }
  };

  const handleSave = debounce(() => {
    globalGetValue();
    openGoodsDetailSave();
  }, 300);

  // 保存并提交
  const handleSaveSubmit = debounce(() => {
    // setSaveSubmit(true);
    globalGetValue(true);
    openGoodsDetailSaveAndSubmit();
  }, 500);

  // 截图
  const handleScreenShot = () => {
    const canvasEle = document.getElementById('_oral-broadcast-main');
    html2canvas(canvasEle as HTMLElement, {
      useCORS: true,
      allowTaint: true,
      logging: true,
      scale: 3,
      scrollX: 0,
      scrollY: 0,
      // foreignObjectRendering: true,
    })
      .then((canvas) => {
        const data = canvas.toDataURL();
        const file = base64ToFile(data);
        const formData = new FormData();
        formData.append('file', file);
        formData.append('typeCode', 'TASK_POSTER');
        uploadPic(formData);
      })
      .catch(() => {
        message.warning('截图失败');
      })
      .finally(() => {
        setSaveSubmit(false);
      });
  };

  const {
    createHtmlRun,
    createHtmlLoading,
    // getHtmlCancel,
    progressValue,
    aiVisible,
    setAIVisible,
    setProgressValue,
    cancelCreate,
    createAIVisible,
    setCreateAIVisible,
  } = useAICreate(detail, () => {
    detailRun({
      spokenScriptInfoId,
    });
  });

  const handleConfirmCreate = () => {
    setAIVisible(false);

    detailRun({
      spokenScriptInfoId,
    });
  };

  const { run: checkGenerateScriptHtmlRun, loading: checkGenerateScriptHtmlLoading } = useRequest(
    checkGenerateScriptHtml,
    {
      manual: true,
      onSuccess({ res }) {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        if (!res?.result) {
          message.warning(
            '当前商品属性及详情缺失，暂不支持生成，请相关人员补全商家端该商品信息后重试。',
          );
          return;
        }
        setCreateAIVisible(true);

        // Modal.confirm({
        //   title: '请确认是否通过AI生成口播稿?',
        //   content:
        //     'AI生成的口播稿信息仅供参考，并且会覆盖该口播稿已填写的卖点信息，请确认是否执行该操作。',
        //   onOk() {
        //     // setAIVisible(true);
        //     createHtmlRun({
        //       id: detail?.id,
        //     });
        //     // setTimeout(() => {
        //     //   setProgressValue(90);
        //     // }, 200);
        //     // 直接调用详情接口
        //     // detailRun({
        //     //   spokenScriptInfoId,
        //     // });
        //   },
        // });
      },
    },
  );

  const handleAICreatePoint = () => {
    // checkGenerateScriptHtmlRun({ id: detail?.spuId });
    setCreateAIVisible(true);
  };

  const handleWordsSubmit = () => {
    spokenScriptInfoSaveAndSubmitRun(saveParamsRef.current);
  };

  // useEffect(() => {
  //   if (saveSubmit && !isEdit) {
  //     setTimeout(() => {
  //       handleScreenShot();
  //     }, 100);
  //   }
  // }, [isEdit, saveSubmit]);

  useEffect(() => {
    const startTime = moment().format('YYYY-MM-DD HH:mm:ss');
    openGoodsDetail();
    return () => {
      Modal.destroyAll();
      const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
      openGoodsDetailLeave({
        startTime: startTime,
        endTime: endTime,
      });
    };
  }, []);

  useEffect(() => {
    if (isEdit) {
      // 点击编辑的时候掉一下版本接口获取最新的类型状态
      handleGetVersionList?.();
    }
  }, [isEdit]);

  useImperativeHandle(onRef, () => {
    return {
      detailSetValue(value: any) {
        setDetail(value);
        if (value?.spuImg) {
          downloadIamge(value?.spuImg);
        }
      },
    };
  });

  // 另存为逻辑 先保存之前版本的内容
  const getParams = async () => {
    const list = [MainOralRef.current?.validate(), OtherInfoRef.current?.validate()];
    const res = await Promise.all(list);
    // if (isSubmit) {
    //   setIsEdit(false);
    // }
    const [basic, other] = res || [];
    /**
     * @details 在编辑并且当前版本是终版的情况下点击存为行版本的时候需要将detail的信息回填到params中 因为MainOralRef和OtherInfoRef部分字段没有在form表单里面
     */
    if (!other?.type && detail?.type === TYPES.END) {
      const params = {
        ...(basic as any),
        id: detail?.id,
        cateName: detail?.cateName,
        spuImg: detail?.spuImg,
        spuSpokenScriptInfo: detail?.spuSpokenScriptInfo,
        type: detail?.type,
      };
      return params;
    }
    const params = {
      ...(basic as any),
      ...(other as any),
      // selectGoodsPoolNo,
      id: detail?.id,
    };
    return params;
  };

  const handleBeforeClick = (cb: any) => {
    getParams()
      .then((res) => {
        if (res?.type === TYPES.END) {
          Modal.confirm({
            title: '已按照法务审核意见进行相关修改',
            maskClosable: false,
            keyboard: false,
            okText: '确定',
            cancelText: '取消',
            onOk() {
              cb();
            },
          });
          return;
        }
        cb();
      })
      .catch(() => {
        message.warning('请将必填项填写完整~');
      });
  };

  const handleSetReuseValue = (value: string) => {
    MainOralRef.current?.reuseSpuSpokenScriptInfo(value);
  };

  const handleCreateAIVisible = () => {
    // console.log(createAIPoint, 'createAIPoint');
    createHtmlRun({
      id: detail?.id,
      supplementaryCueWords: createAIPoint,
      // createAIPoint,
    });
  };

  return (
    <div className={styles['oral-box']}>
      {/* 加上版本 */}
      <div className={styles['oral-box-version_btns']}>
        <div className={styles['version-line']}>
          <Tabs
            size="small"
            activeKey={String(currentVersionValue)}
            onChange={(activeKey: any) => {
              handleTabsChange(activeKey);
              // 存在轮询查询接口的时候要取消
              getHtmlCancel?.();
            }}
            // defaultActiveKey={1}
          >
            {versionList?.map((item, index) => (
              <TabPane
                key={String(item.versionId)}
                tab={
                  <Popover
                    placement="bottom"
                    overlayClassName={styles['tabs_popover']}
                    content={
                      <div className={styles['tabs_content_box']}>
                        <Icon
                          type="check-circle"
                          theme="filled"
                          style={{
                            fontSize: '16px',
                            color: '#52c41a',
                            paddingRight: '8px',
                            marginTop: '2px',
                          }}
                        />
                        <div>
                          <p className={styles['tabs_content']}>版本号: V{item?.versionId}</p>
                          <p style={{ width: '100%', wordBreak: 'break-all' }}>
                            版本备注: {item?.versionRemark || '-'}
                          </p>
                        </div>
                      </div>
                    }
                  >
                    V{item?.versionId}
                  </Popover>
                }
                // disabled={isEdit && item?.type === TYPES.END}
              />
            ))}
          </Tabs>
        </div>
        {/* 按钮部分 */}
        {isDetail ? (
          <></>
        ) : (
          <div className={styles['oral-box-btns']}>
            {isEdit ? (
              <>
                <Button
                  onClick={handleCance}
                  loading={
                    saveSubmit ||
                    spokenScriptInfoSaveLoading ||
                    uploadLoading ||
                    spokenScriptInfoSaveAndSubmitLoading ||
                    checkSensitiveLoading
                  }
                  style={{ marginRight: '8px' }}
                >
                  取消
                </Button>
                {detail?.type !== TYPES.END ? (
                  <AuthWrapper functionName="f_oral_broadcast_reuse">
                    <ReuseModal
                      platformSpuId={platformSpuId}
                      handleSetValue={handleSetReuseValue}
                      spokenScriptInfoId={spokenScriptInfoId}
                      onRefresh={() => {
                        refreshOralLog();
                      }}
                    >
                      <Button className={styles['btn-or']} style={{ marginRight: '8px' }}>
                        复用
                      </Button>
                    </ReuseModal>
                  </AuthWrapper>
                ) : null}

                <AuthWrapper functionName="f_oral_broadcast_version">
                  <AddVersion
                    size={versionList?.length + 1}
                    onRefresh={() => {
                      handleAddVersionRefresh();
                      setIsEdit(false);
                      refreshOralLog();
                    }}
                    spokenScriptInfoId={spokenScriptInfoId}
                    getParams={getParams}
                    beforeClick={handleBeforeClick}
                  >
                    <Button icon="plus" type="primary" style={{ marginRight: '8px' }}>
                      存为新版
                    </Button>
                  </AddVersion>
                </AuthWrapper>
                <Button
                  type="primary"
                  style={{ marginRight: '8px' }}
                  onClick={handleSave}
                  loading={
                    saveSubmit ||
                    spokenScriptInfoSaveLoading ||
                    uploadLoading ||
                    spokenScriptInfoSaveAndSubmitLoading ||
                    checkSensitiveLoading
                  }
                >
                  保存
                </Button>
                {typeSaveSubmit !== TYPES.END ? (
                  <Button
                    type="primary"
                    onClick={handleSaveSubmit}
                    loading={
                      saveSubmit ||
                      spokenScriptInfoSaveLoading ||
                      uploadLoading ||
                      spokenScriptInfoSaveAndSubmitLoading ||
                      checkSensitiveLoading
                    }
                  >
                    保存并提交
                  </Button>
                ) : null}
              </>
            ) : (
              <>
                {detail?.type === TYPES.FIRST || !detail?.type ? (
                  detail?.taskStatus !== 1 ? (
                    <AuthWrapper functionName="f_oral_broadcast_ai">
                      <Button
                        type="primary"
                        loading={
                          saveSubmit ||
                          uploadLoading ||
                          spokenScriptInfoSaveAndSubmitLoading ||
                          createHtmlLoading ||
                          checkSensitiveLoading ||
                          checkGenerateScriptHtmlLoading
                        }
                        onClick={handleAICreatePoint}
                        style={{ marginRight: '8px' }}
                      >
                        AI生成口播稿
                      </Button>
                    </AuthWrapper>
                  ) : (
                    <Button
                      type="primary"
                      loading={true}
                      // onClick={handleAICreatePoint}
                      style={{ marginRight: '8px' }}
                    >
                      口播稿生成中
                    </Button>
                  )
                ) : (
                  <></>
                )}

                <AuthWrapper functionName="f_oral_broadcast_edit">
                  <Button
                    type="primary"
                    style={{ marginRight: '8px' }}
                    onClick={handleEdit}
                    loading={
                      saveSubmit ||
                      uploadLoading ||
                      spokenScriptInfoSaveAndSubmitLoading ||
                      checkSensitiveLoading
                    }
                    disabled={detail?.taskStatus === 1}
                  >
                    编辑
                  </Button>
                </AuthWrapper>
                {/* {detail?.type === TYPES.FIRST || !detail?.type ? (
                  <AuthWrapper functionName="f_oral_broadcast_edit">
                    <Button
                      type="primary"
                      style={{ marginRight: '8px' }}
                      onClick={handleEdit}
                      loading={saveSubmit || uploadLoading || spokenScriptInfoSaveAndSubmitLoading}
                    >
                      编辑
                    </Button>
                  </AuthWrapper>
                ) : (
                  <></>
                )} */}
                {detail?.type === TYPES.END ? (
                  <AuthWrapper functionName="f_oral_broadcast_version">
                    <AddVersion
                      size={versionList?.length + 1}
                      onRefresh={() => {
                        handleAddVersionRefresh();
                        refreshOralLog();
                      }}
                      spokenScriptInfoId={spokenScriptInfoId}
                      getParams={getParams}
                      // beforeClick={handleBeforeClick}
                      isDetail={true}
                    >
                      <Button
                        icon="plus"
                        type="primary"
                        style={{ marginRight: '8px' }}
                        disabled={detail?.taskStatus === 1}
                      >
                        存为新版
                      </Button>
                    </AddVersion>
                  </AuthWrapper>
                ) : (
                  <></>
                )}

                <AuthWrapper functionName="f_oral_broadcast_downLoad">
                  <Button
                    className={styles['btn-or']}
                    loading={
                      saveSubmit ||
                      uploadLoading ||
                      spokenScriptInfoSaveAndSubmitLoading ||
                      createPptLoading ||
                      checkSensitiveLoading
                    }
                    onClick={() => {
                      setDownloadVisible(true);
                      createPptRun({
                        id: detail?.id,
                      });
                      openGoodsDetailDownload();
                    }}
                    disabled={detail?.taskStatus === 1}
                  >
                    下载
                  </Button>
                </AuthWrapper>
              </>
            )}
          </div>
        )}
      </div>

      <Spin
        spinning={
          saveSubmit ||
          detailLoading ||
          spokenScriptInfoSaveLoading ||
          uploadLoading ||
          spokenScriptInfoSaveAndSubmitLoading ||
          checkSensitiveLoading
        }
      >
        {/* 操作区域 */}
        <MainOral
          detail={detail}
          isEdit={isEdit}
          onRef={MainOralRef}
          spuImg={spuImg}
          downloadIamge={downloadIamge}
        ></MainOral>
        {/* 直播间, 直播日期, 类型Form */}
        {isDetail ? (
          <></>
        ) : (
          <OtherInfo
            isEdit={isEdit}
            onRef={OtherInfoRef}
            detail={detail}
            handleRadioChange={(value: TYPES) => {
              // 方便后续扩展
              setTypeSaveSubmit(value);
            }}
          ></OtherInfo>
        )}
      </Spin>
      {/* 下载的loading弹窗 */}
      <Modal
        visible={downloadVisible}
        footer={null}
        maskClosable={false}
        keyboard={false}
        closable={false}
        width={500}
      >
        <div className={styles['ppt-modal']}>ppt生成中请稍等~</div>
        <MyLoading />
      </Modal>
      {/* ai生成 */}
      <Modal
        visible={aiVisible}
        footer={
          <>
            {progressValue === 100 ? (
              <Button type="primary" onClick={handleConfirmCreate}>
                确定
              </Button>
            ) : (
              <Button className={styles['btn-or']} onClick={cancelCreate}>
                取消生成
              </Button>
            )}
          </>
        }
        maskClosable={false}
        keyboard={false}
        closable={false}
        width={500}
      >
        <div className={styles['ai-line']}>
          {progressValue === 100 ? (
            <>
              <Icon
                type="check-circle"
                theme="filled"
                style={{ color: '#52C41A', fontSize: '18px', marginRight: '8px' }}
              />
              商品卖点生成完毕，请查阅。
            </>
          ) : (
            <>
              <Icon
                type="exclamation-circle"
                theme="filled"
                style={{ color: '#FAAD14', fontSize: '18px', marginRight: '8px' }}
              />
              商品卖点正在生成，请稍作等待...
            </>
          )}
        </div>
        <Progress percent={progressValue} status="active" />
      </Modal>
      {/* 敏感词提示 */}
      <Modal
        visible={sensitiveWordsVisible}
        closable={false}
        width={500}
        maskClosable={false}
        keyboard={false}
        wrapClassName={styles['sensitive-words-modal']}
        footer={
          <>
            <Button
              onClick={() => {
                setSensitiveWordsVisible(false);
              }}
              loading={spokenScriptInfoSaveAndSubmitLoading}
            >
              修改
            </Button>
            <Button
              type="primary"
              onClick={handleWordsSubmit}
              loading={spokenScriptInfoSaveAndSubmitLoading}
            >
              提交
            </Button>
          </>
        }
      >
        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
          <Icon
            type="exclamation-circle"
            theme="filled"
            style={{ color: 'red', fontSize: '16px', marginRight: '8px', marginTop: '4px' }}
          />
          <div>
            <p
              style={{ fontSize: '16px', fontWeight: '500', color: '#444444', marginBottom: '8px' }}
            >
              当前口播稿内存在敏感词, 确认提交吗?
            </p>
            <p style={{ fontSize: '14px', color: '#666666' }}>{sensitiveWordsString}</p>
          </div>
        </div>
      </Modal>
      {/* AI生成口播稿弹窗 */}
      <Modal
        visible={createAIVisible}
        maskClosable={false}
        keyboard={false}
        width={500}
        closable={false}
        wrapClassName={styles['create-ai-modal']}
        onCancel={() => {
          setCreateAIVisible(false);
        }}
        onOk={() => {
          handleCreateAIVisible();
        }}
        okButtonProps={{
          loading: createHtmlLoading,
        }}
      >
        <div className={styles['create-ai-modal-content']}>
          <Icon
            type="exclamation-circle"
            theme="filled"
            style={{ color: '#FAAD14', fontSize: '16px', marginRight: '8px', marginTop: '4px' }}
          />
          <div>
            <p
              style={{ fontSize: '14px', color: '#444444', fontWeight: '500', marginBottom: '8px' }}
            >
              请确认是否通过AI生成卖点?
            </p>
            <p>
              1. AI生成的卖点信息仅供参考,并且会覆盖该口播稿已填写的卖点信息,请确认是否执行该操作。
            </p>
            <p style={{ marginBottom: '8px' }}>
              2. 若需要新增商品补充内容,可在下方方框内输入,将会作为用户提示词一并给到LLM。
            </p>
            <Input.TextArea
              placeholder="请输入补充的提示词"
              style={{ marginBottom: '8px' }}
              rows={4}
              maxLength={512}
              value={createAIPoint}
              onChange={(e) => {
                setCreateAIPoint(e.target.value);
              }}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default OralBroadcast;
