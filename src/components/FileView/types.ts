// ai生成
export interface FileInfo {
  url: string;
  name: string;
  size?: number;
  type?: string;
  lastModified?: number;
}

export interface PreviewConfig {
  width?: number;
  height?: number;
  maxWidth?: string;
  maxHeight?: string;
  showToolbar?: boolean;
  showFileName?: boolean;
  showFileSize?: boolean;
  allowDownload?: boolean;
  allowFullscreen?: boolean;
}

export interface FileViewProps {
  file: FileInfo;
  config?: PreviewConfig;
  className?: string;
  style?: React.CSSProperties;
  onError?: (error: Error) => void;
  onLoad?: () => void;
}

export type FileType =
  | 'image'
  | 'document'
  | 'video'
  | 'audio'
  | 'archive'
  | 'code'
  | 'pdf'
  | 'unknown';

export interface FileTypeConfig {
  type: FileType;
  extensions: string[];
  mimeTypes: string[];
  icon: string;
  color: string;
  supportPreview: boolean;
}

export interface PreviewComponentProps {
  file: FileInfo;
  config: PreviewConfig;
  onError?: (error: Error) => void;
  onLoad?: () => void;
}
// 2024年12月23日 开山ai结尾共生成37行代码
