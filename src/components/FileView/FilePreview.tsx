// ai生成
import React from 'react';
import { FileViewProps } from './types';
import { getFileType, isValidUrl } from './utils/fileUtils';
import ImagePreview from './components/ImagePreview';
import DocumentPreview from './components/DocumentPreview';
import VideoPreview from './components/VideoPreview';
import AudioPreview from './components/AudioPreview';
import ArchivePreview from './components/ArchivePreview';
import CodePreview from './components/CodePreview';
import DefaultPreview from './components/DefaultPreview';

const FilePreview: React.FC<FileViewProps> = ({
  file,
  config = {},
  className = '',
  style = {},
  onError,
  onLoad,
}) => {
  // 默认配置
  const defaultConfig = {
    showToolbar: true,
    showFileName: true,
    showFileSize: true,
    allowDownload: true,
    allowFullscreen: true,
    maxWidth: '100%',
    maxHeight: '600px',
    ...config,
  };

  // 验证文件URL
  if (!file.url || !isValidUrl(file.url)) {
    const error = new Error('无效的文件URL');
    onError?.(error);
    return (
      <div
        className={`file-preview-error ${className}`}
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: config.height || 300,
          backgroundColor: '#fafafa',
          border: '1px solid #e8e8e8',
          borderRadius: 4,
          ...style,
        }}
      >
        <div style={{ fontSize: 48, color: '#ccc', marginBottom: 16 }}>⚠️</div>
        <p style={{ color: '#666' }}>无效的文件URL</p>
      </div>
    );
  }

  // 验证文件名
  if (!file.name) {
    const error = new Error('缺少文件名');
    onError?.(error);
    return (
      <div
        className={`file-preview-error ${className}`}
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: config.height || 300,
          backgroundColor: '#fafafa',
          border: '1px solid #e8e8e8',
          borderRadius: 4,
          ...style,
        }}
      >
        <div style={{ fontSize: 48, color: '#ccc', marginBottom: 16 }}>📄</div>
        <p style={{ color: '#666' }}>缺少文件名信息</p>
      </div>
    );
  }

  // 获取文件类型
  const fileType = getFileType(file.name, file.type);

  // 公共属性
  const commonProps = {
    file,
    config: defaultConfig,
    onError,
    onLoad,
  };

  // 根据文件类型渲染对应的预览组件
  const renderPreviewComponent = () => {
    switch (fileType) {
      case 'image':
        return <ImagePreview {...commonProps} />;

      case 'document':
      case 'pdf':
        return <DocumentPreview {...commonProps} />;

      case 'video':
        return <VideoPreview {...commonProps} />;

      case 'audio':
        return <AudioPreview {...commonProps} />;

      case 'archive':
        return <ArchivePreview {...commonProps} />;

      case 'code':
        return <CodePreview {...commonProps} />;

      case 'unknown':
      default:
        return <DefaultPreview {...commonProps} />;
    }
  };

  return (
    <div
      className={`file-preview-container ${className}`}
      style={{
        position: 'relative',
        width: '100%',
        ...style,
      }}
    >
      {renderPreviewComponent()}
    </div>
  );
};

export default FilePreview;
// 2024年12月23日 开山ai结尾共生成98行代码
