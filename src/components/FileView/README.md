# FileView 文件预览组件

一个功能强大、支持多种文件格式的 React 文件预览组件。

## 功能特性

- 🖼️ **图片预览** - 支持 jpg、png、gif、svg 等格式，支持缩放、旋转、全屏
- 📄 **文档预览** - 支持 PDF、Word、Excel、PowerPoint 等 Office 文档
- 🎥 **视频预览** - 支持 mp4、avi、mov 等格式，内置播放控制器
- 🎵 **音频预览** - 支持 mp3、wav、flac 等格式，可视化播放界面
- 📦 **压缩文件** - 显示压缩包信息和缩略图
- 💻 **代码文件** - 支持语法高亮的代码预览
- 🔧 **强大配置** - 丰富的自定义选项和回调函数
- 📱 **响应式** - 适配不同屏幕尺寸

## 安装使用

```tsx
import FileView, { QuickPreview, FileListPreview } from '@/components/FileView';

// 基础使用
const file = {
  url: 'https://example.com/image.jpg',
  name: 'example.jpg',
  size: 1024000,
};

<FileView file={file} />;
```

## API

### FileView Props

| 属性      | 类型                   | 默认值 | 说明            |
| --------- | ---------------------- | ------ | --------------- |
| file      | FileInfo               | -      | 文件信息对象    |
| config    | PreviewConfig          | {}     | 预览配置        |
| className | string                 | ''     | 自定义 CSS 类名 |
| style     | CSSProperties          | {}     | 自定义样式      |
| onError   | (error: Error) => void | -      | 错误回调        |
| onLoad    | () => void             | -      | 加载完成回调    |

### FileInfo

```tsx
interface FileInfo {
  url: string; // 文件URL
  name: string; // 文件名
  size?: number; // 文件大小（字节）
  type?: string; // MIME类型
  lastModified?: number; // 最后修改时间
}
```

### PreviewConfig

```tsx
interface PreviewConfig {
  width?: number; // 宽度
  height?: number; // 高度
  maxWidth?: string; // 最大宽度
  maxHeight?: string; // 最大高度
  showToolbar?: boolean; // 显示工具栏
  showFileName?: boolean; // 显示文件名
  showFileSize?: boolean; // 显示文件大小
  allowDownload?: boolean; // 允许下载
  allowFullscreen?: boolean; // 允许全屏
}
```

## 使用示例

### 基础预览

```tsx
import FileView from '@/components/FileView';

const BasicExample = () => {
  const file = {
    url: 'https://example.com/document.pdf',
    name: 'example.pdf',
    size: 2048000,
  };

  return (
    <FileView
      file={file}
      config={{
        height: 600,
        showToolbar: true,
        allowDownload: true,
      }}
      onError={(error) => console.error('预览失败:', error)}
      onLoad={() => console.log('预览加载完成')}
    />
  );
};
```

### 快速预览

```tsx
import { QuickPreview } from '@/components/FileView';

const QuickExample = () => {
  return (
    <QuickPreview
      url="https://example.com/image.jpg"
      fileName="example.jpg"
      fileSize={1024000}
      width={800}
      height={600}
    />
  );
};
```

### 文件列表预览

```tsx
import { FileListPreview } from '@/components/FileView';

const ListExample = () => {
  const files = [
    {
      url: 'https://example.com/file1.jpg',
      name: 'file1.jpg',
      size: 1024000,
    },
    {
      url: 'https://example.com/file2.pdf',
      name: 'file2.pdf',
      size: 2048000,
    },
  ];

  return (
    <FileListPreview
      files={files}
      selectedIndex={0}
      onFileSelect={(index) => console.log('选择文件:', index)}
      previewConfig={{
        height: 600,
        showToolbar: true,
      }}
    />
  );
};
```

### 图片预览配置

```tsx
const ImageExample = () => {
  const imageFile = {
    url: 'https://example.com/photo.jpg',
    name: 'photo.jpg',
    size: 5120000,
  };

  return (
    <FileView
      file={imageFile}
      config={{
        height: 500,
        showToolbar: true,
        allowFullscreen: true,
        allowDownload: true,
        showFileName: true,
        showFileSize: true,
      }}
    />
  );
};
```

### 视频预览配置

```tsx
const VideoExample = () => {
  const videoFile = {
    url: 'https://example.com/video.mp4',
    name: 'video.mp4',
    size: 52428800,
  };

  return (
    <FileView
      file={videoFile}
      config={{
        width: 800,
        height: 450,
        showToolbar: true,
        allowFullscreen: true,
        allowDownload: true,
      }}
    />
  );
};
```

### 代码预览配置

```tsx
const CodeExample = () => {
  const codeFile = {
    url: 'https://example.com/script.js',
    name: 'script.js',
    size: 4096,
  };

  return (
    <FileView
      file={codeFile}
      config={{
        height: 500,
        showToolbar: true,
        allowDownload: true,
        showFileName: true,
      }}
    />
  );
};
```

## 工具函数

```tsx
import { FileViewUtils } from '@/components/FileView';

// 获取文件类型
const fileType = FileViewUtils.getFileType('example.jpg');

// 检查是否支持预览
const canPreview = FileViewUtils.isSupportPreview('example.pdf');

// 格式化文件大小
const sizeText = FileViewUtils.formatFileSize(1024000); // "1.0 MB"

// 获取文件图标
const icon = FileViewUtils.getFileIcon('example.zip');

// 下载文件
FileViewUtils.downloadFile('https://example.com/file.zip', 'file.zip');
```

## 支持的文件类型

### 图片格式

jpg, jpeg, png, gif, bmp, webp, svg, ico, tiff

### 文档格式

pdf, doc, docx, xls, xlsx, ppt, pptx, txt, rtf

### 视频格式

mp4, avi, mkv, mov, wmv, flv, webm, m4v

### 音频格式

mp3, wav, flac, aac, ogg, m4a, wma

### 压缩格式

zip, rar, 7z, tar, gz, bz2, xz

### 代码格式

js, ts, jsx, tsx, css, less, scss, html, xml, json, py, java, cpp, c, php, go, rs, vue, sql, md

## 注意事项

1. **跨域问题**: 确保文件 URL 支持跨域访问
2. **文件大小**: 大文件可能需要较长加载时间
3. **浏览器兼容**: 部分功能需要现代浏览器支持
4. **Office 文档**: 使用微软 Office Online 服务预览，需要网络连接
5. **视频/音频**: 依赖浏览器原生支持的格式

## 常见问题

### Q: 如何自定义样式？

A: 可以通过 className 和 style 属性传入自定义样式，或者覆盖组件的 CSS 类。

### Q: 如何处理预览失败？

A: 使用 onError 回调函数捕获错误，可以显示自定义错误信息或降级处理。

### Q: 如何优化大文件预览性能？

A: 可以通过 config 配置限制预览尺寸，或者对大文件进行预处理。

### Q: 是否支持自定义预览组件？

A: 当前版本提供了完整的预览组件，如需深度定制，可以参考源码实现自定义组件。
