// ai生成
/* 文件预览组件样式 */

.file-preview-container {
  position: relative;
  width: 100%;
  background-color: #fff;

  /* 错误状态样式 */
  .file-preview-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    background-color: #fafafa;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    color: #666;

    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .error-message {
      font-size: 14px;
      text-align: center;
    }
  }
}

/* 图片预览样式 */
.image-preview {
  .image-toolbar {
    display: flex;
    gap: 8px;
    padding: 8px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 4px;

    .ant-btn {
      color: #fff;
      border-color: #fff;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .image-container {
    position: relative;
    overflow: hidden;

    img {
      transition: transform 0.3s ease;
      user-select: none;
    }
  }
}

/* 文档预览样式 */
.document-preview {
  .document-toolbar {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px;
  }

  .document-header {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;

    .document-icon {
      margin-right: 12px;
      font-size: 24px;
    }
  }

  .document-loading,
  .document-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background-color: #fafafa;
  }
}

/* 视频预览样式 */
.video-preview {
  position: relative;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;

  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    padding: 20px 16px 16px;
    transition: opacity 0.3s;

    .control-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #fff;
    }
  }

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.3s;

    .ant-btn {
      width: 64px;
      height: 64px;
      font-size: 24px;
      background-color: rgba(255, 255, 255, 0.9);
      color: #000;
      border: none;
    }
  }
}

/* 音频预览样式 */
.audio-preview {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;

  .audio-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;

    .audio-icon {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 16px;
    }
  }

  .audio-controls {
    padding: 20px;

    .progress-display {
      text-align: center;
      margin-bottom: 16px;
    }

    .control-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
    }

    .volume-control {
      display: flex;
      align-items: center;
      gap: 12px;
      max-width: 200px;
      margin: 0 auto;
    }
  }
}

/* 压缩文件预览样式 */
.archive-preview {
  .archive-content {
    text-align: center;
    padding: 40px;

    .archive-icon-wrapper {
      position: relative;
      margin: 0 auto 24px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .archive-decoration {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-bottom: 24px;

      .pulse-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        animation: pulse 1s infinite;

        @keyframes pulse {
          0% {
            transform: scale(1);
            opacity: 0.3;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.6;
          }
          100% {
            transform: scale(1);
            opacity: 0.3;
          }
        }
      }
    }

    .archive-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 24px;
    }
  }
}

/* 代码预览样式 */
.code-preview {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;

  .code-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
  }

  .code-header {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;

    .language-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      margin-right: 12px;
    }
  }

  .code-wrapper {
    display: flex;
    overflow: hidden;

    .line-numbers {
      min-width: 40px;
      padding: 12px 8px;
      background-color: #f8f8f8;
      border-right: 1px solid #e8e8e8;
      font-size: 12px;
      line-height: 1.6;
      color: #999;
      text-align: right;
      user-select: none;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }

    .code-content {
      flex: 1;
      padding: 12px;
      font-size: 13px;
      line-height: 1.6;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      white-space: pre;
      overflow: auto;
      background-color: #fff;

      .code-line {
        min-height: 1.6em;
      }
    }
  }
}

/* 默认预览样式 */
.default-preview {
  .default-content {
    text-align: center;
    padding: 60px;
    position: relative;

    .file-icon-wrapper {
      position: relative;
      margin: 0 auto 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .file-attributes {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 32px;

      .attribute-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 8px;
        min-width: 80px;
      }
    }

    .file-actions {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      justify-content: center;
      margin-bottom: 24px;
    }

    .decoration {
      position: absolute;
      top: 20px;
      right: 20px;
      opacity: 0.1;
      font-size: 100px;
      pointer-events: none;
    }
  }
}

/* 文件列表预览样式 */
.file-list-preview {
  .file-list {
    display: flex;
    gap: 8px;
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    margin-bottom: 16px;
    flex-wrap: wrap;

    .file-item {
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid #e8e8e8;
      font-size: 12px;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
      }

      &.active {
        background-color: #1890ff;
        color: #fff;
        border-color: #1890ff;
      }
    }
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .file-preview-container {
    .code-wrapper {
      .line-numbers {
        min-width: 30px;
        padding: 8px 4px;
        font-size: 10px;
      }

      .code-content {
        padding: 8px;
        font-size: 11px;
      }
    }

    .archive-content,
    .default-content {
      padding: 20px;
    }

    .video-controls {
      padding: 10px 8px 8px;
    }
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-preview-container {
  animation: fadeIn 0.3s ease-out;
}

/* 加载状态样式 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
}
// 2024年12月23日 开山ai结尾共生成293行代码
