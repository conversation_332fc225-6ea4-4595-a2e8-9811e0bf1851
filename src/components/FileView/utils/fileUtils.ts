// ai生成
import { FileType, FileTypeConfig } from '../types';

// 文件类型配置
export const FILE_TYPE_CONFIGS: Record<FileType, FileTypeConfig> = {
  image: {
    type: 'image',
    extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml'],
    icon: 'file-image',
    color: '#52c41a',
    supportPreview: true,
  },
  document: {
    type: 'document',
    extensions: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'],
    mimeTypes: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
    ],
    icon: 'file-text',
    color: '#1890ff',
    supportPreview: true,
  },
  pdf: {
    type: 'pdf',
    extensions: ['pdf'],
    mimeTypes: ['application/pdf'],
    icon: 'file-pdf',
    color: '#f5222d',
    supportPreview: true,
  },
  video: {
    type: 'video',
    extensions: ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'],
    mimeTypes: ['video/mp4', 'video/avi', 'video/quicktime', 'video/webm'],
    icon: 'video-camera',
    color: '#722ed1',
    supportPreview: true,
  },
  audio: {
    type: 'audio',
    extensions: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a', 'wma'],
    mimeTypes: ['audio/mpeg', 'audio/wav', 'audio/flac', 'audio/aac', 'audio/ogg'],
    icon: 'audio',
    color: '#fa8c16',
    supportPreview: true,
  },
  archive: {
    type: 'archive',
    extensions: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'],
    mimeTypes: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
    icon: 'folder-zip',
    color: '#13c2c2',
    supportPreview: false,
  },
  code: {
    type: 'code',
    extensions: [
      'js',
      'ts',
      'jsx',
      'tsx',
      'css',
      'less',
      'scss',
      'html',
      'xml',
      'json',
      'py',
      'java',
      'cpp',
      'c',
      'php',
      'go',
      'rs',
      'vue',
      'sql',
      'md',
    ],
    mimeTypes: [
      'text/javascript',
      'application/javascript',
      'text/typescript',
      'text/css',
      'text/html',
      'application/json',
      'text/markdown',
    ],
    icon: 'code',
    color: '#faad14',
    supportPreview: true,
  },
  unknown: {
    type: 'unknown',
    extensions: [],
    mimeTypes: [],
    icon: 'file',
    color: '#d9d9d9',
    supportPreview: false,
  },
};

/**
 * 根据文件名获取文件扩展名
 */
export const getFileExtension = (fileName: string): string => {
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return fileName.substring(lastDotIndex + 1).toLowerCase();
};

/**
 * 根据文件名或MIME类型判断文件类型
 */
export const getFileType = (fileName: string, mimeType?: string): FileType => {
  const extension = getFileExtension(fileName);

  // 优先根据MIME类型判断
  if (mimeType) {
    for (const [type, config] of Object.entries(FILE_TYPE_CONFIGS)) {
      if (config.mimeTypes.includes(mimeType)) {
        return type as FileType;
      }
    }
  }

  // 根据扩展名判断
  for (const [type, config] of Object.entries(FILE_TYPE_CONFIGS)) {
    if (config.extensions.includes(extension)) {
      return type as FileType;
    }
  }

  return 'unknown';
};

/**
 * 获取文件类型配置
 */
export const getFileTypeConfig = (fileType: FileType): FileTypeConfig => {
  return FILE_TYPE_CONFIGS[fileType];
};

/**
 * 判断文件是否支持预览
 */
export const isSupportPreview = (fileName: string, mimeType?: string): boolean => {
  const fileType = getFileType(fileName, mimeType);
  return getFileTypeConfig(fileType).supportPreview;
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
};

/**
 * 获取文件图标
 */
export const getFileIcon = (fileName: string, mimeType?: string): string => {
  const fileType = getFileType(fileName, mimeType);
  return getFileTypeConfig(fileType).icon;
};

/**
 * 获取文件颜色
 */
export const getFileColor = (fileName: string, mimeType?: string): string => {
  const fileType = getFileType(fileName, mimeType);
  return getFileTypeConfig(fileType).color;
};

/**
 * 检查URL是否有效
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * 下载文件
 */
export const downloadFile = (url: string, fileName: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
// 2024年12月23日 开山ai结尾共生成144行代码
