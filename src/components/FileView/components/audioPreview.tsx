// ai生成
import React, { useState, useRef, useEffect } from 'react';
import { Icon, Button, Slider, Progress, message } from 'antd';
import { PreviewComponentProps } from '../types';
import { downloadFile, formatFileSize, getFileExtension } from '../utils/fileUtils';

interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  loading: boolean;
  error: boolean;
  buffered: number;
}

const AudioPreview: React.FC<PreviewComponentProps> = ({ file, config, onError, onLoad }) => {
  const [audioState, setAudioState] = useState<AudioState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    loading: true,
    error: false,
    buffered: 0,
  });

  const audioRef = useRef<HTMLAudioElement>(null);
  const extension = getFileExtension(file.name);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setAudioState((prev) => ({
        ...prev,
        duration: audio.duration,
        loading: false,
        error: false,
      }));
      onLoad?.();
    };

    const handleTimeUpdate = () => {
      setAudioState((prev) => ({
        ...prev,
        currentTime: audio.currentTime,
      }));
    };

    const handleProgress = () => {
      if (audio.buffered.length > 0) {
        const buffered = (audio.buffered.end(0) / audio.duration) * 100;
        setAudioState((prev) => ({ ...prev, buffered }));
      }
    };

    const handleError = () => {
      setAudioState((prev) => ({ ...prev, error: true, loading: false }));
      onError?.(new Error('音频加载失败'));
    };

    const handleLoadStart = () => {
      setAudioState((prev) => ({ ...prev, loading: true }));
    };

    const handleEnded = () => {
      setAudioState((prev) => ({ ...prev, isPlaying: false, currentTime: 0 }));
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('progress', handleProgress);
    audio.addEventListener('error', handleError);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('progress', handleProgress);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [onLoad, onError]);

  const handlePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (audioState.isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }

    setAudioState((prev) => ({ ...prev, isPlaying: !prev.isPlaying }));
  };

  const handleSeek = (value: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = value;
    setAudioState((prev) => ({ ...prev, currentTime: value }));
  };

  const handleVolumeChange = (value: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = value;
    setAudioState((prev) => ({ ...prev, volume: value }));
  };

  const handleDownload = () => {
    try {
      downloadFile(file.url, file.name);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getAudioIcon = () => {
    const iconMap: Record<string, string> = {
      mp3: 'audio',
      wav: 'audio',
      flac: 'audio',
      aac: 'audio',
      ogg: 'audio',
      m4a: 'audio',
    };
    return iconMap[extension] || 'audio';
  };

  const progressPercent = audioState.duration
    ? (audioState.currentTime / audioState.duration) * 100
    : 0;

  if (audioState.error) {
    return (
      <div
        className="audio-error"
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: 200,
          backgroundColor: '#fafafa',
          border: '1px solid #e8e8e8',
          borderRadius: 4,
        }}
      >
        <Icon type="audio" style={{ fontSize: 48, color: '#ccc' }} />
        <p style={{ marginTop: 16, color: '#666' }}>音频加载失败</p>
        <div style={{ marginTop: 16, display: 'flex', gap: 8 }}>
          <Button size="small" onClick={() => window.open(file.url, '_blank')}>
            新窗口打开
          </Button>
          {config.allowDownload && (
            <Button size="small" onClick={handleDownload}>
              下载
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      className="audio-preview"
      style={{
        width: config.width || '100%',
        border: '1px solid #e8e8e8',
        borderRadius: 4,
        overflow: 'hidden',
        backgroundColor: '#fff',
      }}
    >
      <audio ref={audioRef} src={file.url} preload="metadata" />

      {/* 工具栏 */}
      {config.showToolbar && (
        <div
          className="audio-toolbar"
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 8,
            padding: 8,
            backgroundColor: '#f5f5f5',
            borderBottom: '1px solid #e8e8e8',
          }}
        >
          {config.allowDownload && (
            <Button size="small" icon="download" onClick={handleDownload}>
              下载
            </Button>
          )}
        </div>
      )}

      {/* 音频信息头部 */}
      <div
        className="audio-header"
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: 16,
          backgroundColor: '#fafafa',
          borderBottom: '1px solid #e8e8e8',
        }}
      >
        <div
          className="audio-icon"
          style={{
            width: 60,
            height: 60,
            backgroundColor: '#fa8c16',
            borderRadius: 8,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
          }}
        >
          <Icon type={getAudioIcon()} style={{ fontSize: 32, color: '#fff' }} />
        </div>

        <div className="audio-info" style={{ flex: 1 }}>
          <div style={{ fontWeight: 500, fontSize: 16, marginBottom: 4 }}>{file.name}</div>
          <div style={{ fontSize: 12, color: '#666' }}>
            {extension.toUpperCase()} 音频文件
            {file.size && ` • ${formatFileSize(file.size)}`}
            {audioState.duration > 0 && ` • ${formatTime(audioState.duration)}`}
          </div>
        </div>
      </div>

      {/* 播放控制区域 */}
      <div
        className="audio-controls"
        style={{
          padding: 20,
        }}
      >
        {/* 进度显示 */}
        <div style={{ marginBottom: 16 }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 8,
            }}
          >
            <span style={{ fontSize: 24, fontWeight: 500, color: '#1890ff' }}>
              {formatTime(audioState.currentTime)}
            </span>
            <span style={{ margin: '0 8px', color: '#ccc' }}>/</span>
            <span style={{ fontSize: 16, color: '#666' }}>{formatTime(audioState.duration)}</span>
          </div>

          {/* 可视化进度条 */}
          <div
            style={{
              height: 60,
              backgroundColor: '#f5f5f5',
              borderRadius: 4,
              position: 'relative',
              overflow: 'hidden',
              marginBottom: 12,
            }}
          >
            {/* 缓冲进度 */}
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                height: '100%',
                width: `${audioState.buffered}%`,
                backgroundColor: '#e6f7ff',
                transition: 'width 0.3s',
              }}
            />

            {/* 播放进度 */}
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                height: '100%',
                width: `${progressPercent}%`,
                backgroundColor: '#1890ff',
                transition: 'width 0.1s',
              }}
            />

            {/* 音频波形效果 */}
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                display: 'flex',
                alignItems: 'center',
                gap: 2,
              }}
            >
              {audioState.isPlaying &&
                [...Array(20)].map((_, i) => (
                  <div
                    key={i}
                    style={{
                      width: 2,
                      height: Math.random() * 30 + 10,
                      backgroundColor: audioState.isPlaying ? '#1890ff' : '#ccc',
                      borderRadius: 1,
                      animation: audioState.isPlaying
                        ? `wave 0.${i + 5}s infinite alternate`
                        : 'none',
                    }}
                  />
                ))}
            </div>
          </div>

          {/* 进度条滑块 */}
          <Slider
            min={0}
            max={audioState.duration}
            value={audioState.currentTime}
            onChange={(value) => handleSeek(Array.isArray(value) ? value[0] : value)}
            tipFormatter={(value) => formatTime(value || 0)}
            style={{ margin: 0 }}
            disabled={audioState.loading}
          />
        </div>

        {/* 控制按钮 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 16,
            marginBottom: 16,
          }}
        >
          <Button
            type="primary"
            shape="circle"
            size="large"
            icon={audioState.isPlaying ? 'pause' : 'caret-right'}
            onClick={handlePlayPause}
            loading={audioState.loading}
            style={{
              width: 56,
              height: 56,
              fontSize: 20,
            }}
          />
        </div>

        {/* 音量控制 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: 12,
            maxWidth: 200,
            margin: '0 auto',
          }}
        >
          <Icon type="sound" style={{ color: '#666', fontSize: 16 }} />
          <div style={{ flex: 1 }}>
            <Slider
              min={0}
              max={1}
              step={0.1}
              value={audioState.volume}
              onChange={(value) => handleVolumeChange(Array.isArray(value) ? value[0] : value)}
              style={{ margin: 0 }}
            />
          </div>
          <span style={{ fontSize: 12, color: '#666', minWidth: 30 }}>
            {Math.round(audioState.volume * 100)}%
          </span>
        </div>
      </div>

      <style>{`
        @keyframes wave {
          0% { height: 10px; }
          100% { height: 30px; }
        }
      `}</style>
    </div>
  );
};

export default AudioPreview;
// 2024年12月23日 开山ai结尾共生成234行代码
