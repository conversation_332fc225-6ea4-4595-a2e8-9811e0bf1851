// ai生成
import React, { useState, useEffect } from 'react';
import { Icon, Button, Spin, message } from 'antd';
import { PreviewComponentProps } from '../types';
import { downloadFile, getFileExtension } from '../utils/fileUtils';

interface CodeState {
  content: string;
  loading: boolean;
  error: boolean;
  lineNumbers: boolean;
}

const CodePreview: React.FC<PreviewComponentProps> = ({ file, config, onError, onLoad }) => {
  const [codeState, setCodeState] = useState<CodeState>({
    content: '',
    loading: true,
    error: false,
    lineNumbers: true,
  });

  const extension = getFileExtension(file.name);

  useEffect(() => {
    loadCodeContent();
  }, [file.url]);

  const loadCodeContent = async () => {
    setCodeState((prev) => ({ ...prev, loading: true, error: false }));

    try {
      const response = await fetch(file.url);
      if (!response.ok) {
        throw new Error('无法加载代码文件');
      }

      const text = await response.text();
      setCodeState((prev) => ({
        ...prev,
        content: text,
        loading: false,
        error: false,
      }));
      onLoad?.();
    } catch (error) {
      setCodeState((prev) => ({
        ...prev,
        loading: false,
        error: true,
      }));
      onError?.(error instanceof Error ? error : new Error('代码加载失败'));
    }
  };

  const handleDownload = () => {
    try {
      downloadFile(file.url, file.name);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const handleCopy = () => {
    navigator.clipboard
      .writeText(codeState.content)
      .then(() => {
        message.success('代码已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败');
      });
  };

  const toggleLineNumbers = () => {
    setCodeState((prev) => ({
      ...prev,
      lineNumbers: !prev.lineNumbers,
    }));
  };

  const getLanguageName = () => {
    const langMap: Record<string, string> = {
      js: 'JavaScript',
      ts: 'TypeScript',
      jsx: 'JSX',
      tsx: 'TSX',
      css: 'CSS',
      less: 'Less',
      scss: 'SCSS',
      html: 'HTML',
      xml: 'XML',
      json: 'JSON',
      py: 'Python',
      java: 'Java',
      cpp: 'C++',
      c: 'C',
      php: 'PHP',
      go: 'Go',
      rs: 'Rust',
      vue: 'Vue',
      sql: 'SQL',
      md: 'Markdown',
    };
    return langMap[extension] || extension.toUpperCase();
  };

  const getLanguageColor = () => {
    const colorMap: Record<string, string> = {
      js: '#f7df1e',
      ts: '#3178c6',
      jsx: '#61dafb',
      tsx: '#61dafb',
      css: '#1572b6',
      less: '#1d365d',
      scss: '#cf649a',
      html: '#e34f26',
      xml: '#e34f26',
      json: '#000000',
      py: '#3776ab',
      java: '#ed8b00',
      cpp: '#00599c',
      c: '#a8b9cc',
      php: '#777bb4',
      go: '#00add8',
      rs: '#000000',
      vue: '#4fc08d',
      sql: '#336791',
      md: '#083fa1',
    };
    return colorMap[extension] || '#faad14';
  };

  const renderLineNumbers = () => {
    if (!codeState.lineNumbers) return null;

    const lines = codeState.content.split('\n');
    return (
      <div
        className="line-numbers"
        style={{
          minWidth: 40,
          padding: '12px 8px',
          backgroundColor: '#f8f8f8',
          borderRight: '1px solid #e8e8e8',
          fontSize: 12,
          lineHeight: 1.6,
          color: '#999',
          textAlign: 'right',
          userSelect: 'none',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        }}
      >
        {lines.map((_, index) => (
          <div key={index}>{index + 1}</div>
        ))}
      </div>
    );
  };

  const renderCodeContent = () => {
    const lines = codeState.content.split('\n');

    return (
      <div
        className="code-content"
        style={{
          flex: 1,
          padding: 12,
          fontSize: 13,
          lineHeight: 1.6,
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          whiteSpace: 'pre',
          overflow: 'auto',
          backgroundColor: '#fff',
        }}
      >
        {lines.map((line, index) => (
          <div key={index} className="code-line">
            {line || ' '}
          </div>
        ))}
      </div>
    );
  };

  if (codeState.loading) {
    return (
      <div
        className="code-loading"
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: config.height || 400,
          backgroundColor: '#fafafa',
          border: '1px solid #e8e8e8',
          borderRadius: 4,
        }}
      >
        <Spin size="large" />
        <p style={{ marginTop: 16, color: '#666' }}>正在加载代码...</p>
      </div>
    );
  }

  if (codeState.error) {
    return (
      <div
        className="code-error"
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: config.height || 400,
          backgroundColor: '#fafafa',
          border: '1px solid #e8e8e8',
          borderRadius: 4,
        }}
      >
        <Icon type="code" style={{ fontSize: 48, color: '#ccc' }} />
        <p style={{ marginTop: 16, color: '#666' }}>代码加载失败</p>
        <div style={{ marginTop: 16, display: 'flex', gap: 8 }}>
          <Button size="small" onClick={loadCodeContent}>
            重试
          </Button>
          <Button size="small" onClick={() => window.open(file.url, '_blank')}>
            新窗口打开
          </Button>
          {config.allowDownload && (
            <Button size="small" onClick={handleDownload}>
              下载
            </Button>
          )}
        </div>
      </div>
    );
  }

  const totalLines = codeState.content.split('\n').length;
  const fileSize = new Blob([codeState.content]).size;

  return (
    <div
      className="code-preview"
      style={{
        width: config.width || '100%',
        border: '1px solid #e8e8e8',
        borderRadius: 4,
        overflow: 'hidden',
        backgroundColor: '#fff',
      }}
    >
      {/* 工具栏 */}
      {config.showToolbar && (
        <div
          className="code-toolbar"
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: 8,
            backgroundColor: '#f5f5f5',
            borderBottom: '1px solid #e8e8e8',
          }}
        >
          <div style={{ display: 'flex', gap: 8 }}>
            <Button size="small" icon="copy" onClick={handleCopy}>
              复制
            </Button>
            <Button
              size="small"
              icon="ordered-list"
              onClick={toggleLineNumbers}
              type={codeState.lineNumbers ? 'primary' : 'default'}
            >
              行号
            </Button>
          </div>

          <div style={{ display: 'flex', gap: 8 }}>
            {config.allowDownload && (
              <Button size="small" icon="download" onClick={handleDownload}>
                下载
              </Button>
            )}
            <Button
              size="small"
              icon="external-link"
              onClick={() => window.open(file.url, '_blank')}
            >
              新窗口打开
            </Button>
          </div>
        </div>
      )}

      {/* 代码信息头部 */}
      <div
        className="code-header"
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: 12,
          backgroundColor: '#fafafa',
          borderBottom: '1px solid #e8e8e8',
        }}
      >
        <div
          className="language-badge"
          style={{
            backgroundColor: getLanguageColor(),
            color: '#fff',
            padding: '4px 8px',
            borderRadius: 4,
            fontSize: 12,
            fontWeight: 500,
            marginRight: 12,
          }}
        >
          {getLanguageName()}
        </div>

        <div className="file-info" style={{ flex: 1 }}>
          <div style={{ fontWeight: 500, fontSize: 14 }}>{file.name}</div>
          <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
            {totalLines} 行 • {Math.round(fileSize / 1024)} KB
          </div>
        </div>
      </div>

      {/* 代码内容 */}
      <div
        className="code-wrapper"
        style={{
          display: 'flex',
          height: config.height || 400,
          overflow: 'hidden',
        }}
      >
        {renderLineNumbers()}
        {renderCodeContent()}
      </div>

      {/* 文件信息 */}
      {config.showFileName && (
        <div
          className="code-footer"
          style={{
            padding: 8,
            backgroundColor: '#f5f5f5',
            borderTop: '1px solid #e8e8e8',
            fontSize: 12,
            color: '#666',
            textAlign: 'center',
          }}
        >
          {file.name} • {getLanguageName()} • {totalLines} 行
        </div>
      )}
    </div>
  );
};

export default CodePreview;
// 2024年12月23日 开山ai结尾共生成243行代码
