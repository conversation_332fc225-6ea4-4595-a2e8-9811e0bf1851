// ai生成
import React, { useState, useRef, useEffect } from 'react';
import { Icon, Button, Slider, message } from 'antd';
import { PreviewComponentProps } from '../types';
import { downloadFile, formatFileSize } from '../utils/fileUtils';

interface VideoState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isFullscreen: boolean;
  showControls: boolean;
  loading: boolean;
  error: boolean;
}

const VideoPreview: React.FC<PreviewComponentProps> = ({ file, config, onError, onLoad }) => {
  const [videoState, setVideoState] = useState<VideoState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isFullscreen: false,
    showControls: true,
    loading: true,
    error: false,
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const controlsTimerRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setVideoState((prev) => ({
        ...prev,
        duration: video.duration,
        loading: false,
        error: false,
      }));
      onLoad?.();
    };

    const handleTimeUpdate = () => {
      setVideoState((prev) => ({
        ...prev,
        currentTime: video.currentTime,
      }));
    };

    const handleError = () => {
      setVideoState((prev) => ({ ...prev, error: true, loading: false }));
      onError?.(new Error('视频加载失败'));
    };

    const handleLoadStart = () => {
      setVideoState((prev) => ({ ...prev, loading: true }));
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('error', handleError);
    video.addEventListener('loadstart', handleLoadStart);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('error', handleError);
      video.removeEventListener('loadstart', handleLoadStart);
    };
  }, [onLoad, onError]);

  const handlePlayPause = () => {
    const video = videoRef.current;
    if (!video) return;

    if (videoState.isPlaying) {
      video.pause();
    } else {
      video.play();
    }

    setVideoState((prev) => ({ ...prev, isPlaying: !prev.isPlaying }));
  };

  const handleSeek = (value: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = value;
    setVideoState((prev) => ({ ...prev, currentTime: value }));
  };

  const handleVolumeChange = (value: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.volume = value;
    setVideoState((prev) => ({ ...prev, volume: value }));
  };

  const handleFullscreen = () => {
    const container = containerRef.current;
    if (!container) return;

    if (!videoState.isFullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  const handleDownload = () => {
    try {
      downloadFile(file.url, file.name);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const showControlsTemporarily = () => {
    setVideoState((prev) => ({ ...prev, showControls: true }));

    if (controlsTimerRef.current) {
      clearTimeout(controlsTimerRef.current);
    }

    controlsTimerRef.current = setTimeout(() => {
      setVideoState((prev) => ({ ...prev, showControls: false }));
    }, 3000);
  };

  const handleMouseMove = () => {
    showControlsTemporarily();
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setVideoState((prev) => ({
        ...prev,
        isFullscreen: !!document.fullscreenElement,
      }));
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      if (controlsTimerRef.current) {
        clearTimeout(controlsTimerRef.current);
      }
    };
  }, []);

  if (videoState.error) {
    return (
      <div
        className="video-error"
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: 300,
          backgroundColor: '#000',
          color: '#fff',
          borderRadius: 4,
        }}
      >
        <Icon type="video-camera" style={{ fontSize: 48, color: '#666' }} />
        <p style={{ marginTop: 16 }}>视频加载失败</p>
        <div style={{ marginTop: 16, display: 'flex', gap: 8 }}>
          <Button size="small" onClick={() => window.open(file.url, '_blank')}>
            新窗口打开
          </Button>
          {config.allowDownload && (
            <Button size="small" onClick={handleDownload}>
              下载
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="video-preview"
      style={{
        position: 'relative',
        width: config.width || '100%',
        height: config.height || 400,
        backgroundColor: '#000',
        borderRadius: 4,
        overflow: 'hidden',
        cursor: videoState.showControls ? 'default' : 'none',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => setVideoState((prev) => ({ ...prev, showControls: true }))}
    >
      <video
        ref={videoRef}
        src={file.url}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
        }}
        onClick={handlePlayPause}
      />

      {videoState.loading && (
        <div
          className="video-loading"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
          }}
        >
          <Icon type="loading" spin style={{ fontSize: 48, color: '#fff' }} />
        </div>
      )}

      {/* 播放/暂停按钮 */}
      <div
        className="play-button"
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          opacity: videoState.isPlaying ? 0 : 1,
          transition: 'opacity 0.3s',
          pointerEvents: videoState.isPlaying ? 'none' : 'auto',
        }}
        onClick={handlePlayPause}
      >
        <Button
          type="primary"
          shape="circle"
          size="large"
          icon="caret-right"
          style={{
            width: 64,
            height: 64,
            fontSize: 24,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            color: '#000',
            border: 'none',
          }}
        />
      </div>

      {/* 控制栏 */}
      <div
        className="video-controls"
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(to top, rgba(0,0,0,0.8), transparent)',
          padding: '20px 16px 16px',
          opacity: videoState.showControls ? 1 : 0,
          transition: 'opacity 0.3s',
        }}
      >
        {/* 进度条 */}
        <div style={{ marginBottom: 12 }}>
          <Slider
            min={0}
            max={videoState.duration}
            value={videoState.currentTime}
            onChange={handleSeek}
            tipFormatter={(value) => formatTime(value || 0)}
            style={{
              margin: 0,
            }}
          />
        </div>

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            color: '#fff',
          }}
        >
          {/* 左侧控制 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <Button
              type="link"
              icon={videoState.isPlaying ? 'pause' : 'caret-right'}
              onClick={handlePlayPause}
              style={{ color: '#fff', padding: 0 }}
            />

            <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginLeft: 8 }}>
              <Icon type="sound" style={{ color: '#fff' }} />
              <div style={{ width: 80 }}>
                <Slider
                  min={0}
                  max={1}
                  step={0.1}
                  value={videoState.volume}
                  onChange={handleVolumeChange}
                  style={{ margin: 0 }}
                />
              </div>
            </div>

            <span style={{ fontSize: 12, marginLeft: 16 }}>
              {formatTime(videoState.currentTime)} / {formatTime(videoState.duration)}
            </span>
          </div>

          {/* 右侧控制 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {config.allowDownload && (
              <Button
                type="link"
                icon="download"
                onClick={handleDownload}
                style={{ color: '#fff', padding: 0 }}
              />
            )}

            {config.allowFullscreen && (
              <Button
                type="link"
                icon={videoState.isFullscreen ? 'fullscreen-exit' : 'fullscreen'}
                onClick={handleFullscreen}
                style={{ color: '#fff', padding: 0 }}
              />
            )}
          </div>
        </div>
      </div>

      {/* 文件信息 */}
      {config.showFileName && (
        <div
          className="video-info"
          style={{
            position: 'absolute',
            top: 16,
            left: 16,
            right: 16,
            background: 'rgba(0, 0, 0, 0.7)',
            color: '#fff',
            padding: 8,
            borderRadius: 4,
            fontSize: 12,
            opacity: videoState.showControls ? 1 : 0,
            transition: 'opacity 0.3s',
          }}
        >
          <div style={{ fontWeight: 500 }}>{file.name}</div>
          {config.showFileSize && file.size && (
            <div style={{ marginTop: 2, opacity: 0.8 }}>{formatFileSize(file.size)}</div>
          )}
        </div>
      )}
    </div>
  );
};

export default VideoPreview;
// 2024年12月23日 开山ai结尾共生成243行代码
