// ai生成
import React from 'react';
import { Icon, But<PERSON>, message } from 'antd';
import { PreviewComponentProps } from '../types';
import { downloadFile, formatFileSize, getFileExtension } from '../utils/fileUtils';

const ArchivePreview: React.FC<PreviewComponentProps> = ({ file, config, onError, onLoad }) => {
  const extension = getFileExtension(file.name);

  React.useEffect(() => {
    // 压缩文件没有真正的"加载"过程，直接触发onLoad
    onLoad?.();
  }, [onLoad]);

  const handleDownload = () => {
    try {
      downloadFile(file.url, file.name);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const handleOpenExternal = () => {
    window.open(file.url, '_blank');
  };

  const getArchiveIcon = () => {
    const iconMap: Record<string, string> = {
      zip: 'folder-zip',
      rar: 'folder-zip',
      '7z': 'folder-zip',
      tar: 'folder-zip',
      gz: 'folder-zip',
      bz2: 'folder-zip',
      xz: 'folder-zip',
    };
    return iconMap[extension] || 'folder-zip';
  };

  const getArchiveColor = () => {
    const colorMap: Record<string, string> = {
      zip: '#13c2c2',
      rar: '#722ed1',
      '7z': '#52c41a',
      tar: '#fa8c16',
      gz: '#1890ff',
      bz2: '#f5222d',
      xz: '#faad14',
    };
    return colorMap[extension] || '#13c2c2';
  };

  const getArchiveDescription = () => {
    const descMap: Record<string, string> = {
      zip: 'ZIP 压缩包',
      rar: 'RAR 压缩包',
      '7z': '7-Zip 压缩包',
      tar: 'TAR 归档文件',
      gz: 'GZip 压缩文件',
      bz2: 'BZip2 压缩文件',
      xz: 'XZ 压缩文件',
    };
    return descMap[extension] || '压缩文件';
  };

  return (
    <div
      className="archive-preview"
      style={{
        width: config.width || '100%',
        border: '1px solid #e8e8e8',
        borderRadius: 4,
        overflow: 'hidden',
        backgroundColor: '#fff',
      }}
    >
      {/* 工具栏 */}
      {config.showToolbar && (
        <div
          className="archive-toolbar"
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 8,
            padding: 8,
            backgroundColor: '#f5f5f5',
            borderBottom: '1px solid #e8e8e8',
          }}
        >
          <Button size="small" icon="external-link" onClick={handleOpenExternal}>
            新窗口打开
          </Button>
          {config.allowDownload && (
            <Button size="small" icon="download" onClick={handleDownload}>
              下载
            </Button>
          )}
        </div>
      )}

      {/* 压缩包图标和信息 */}
      <div
        className="archive-content"
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: 40,
          textAlign: 'center',
          minHeight: config.height || 300,
          justifyContent: 'center',
        }}
      >
        {/* 大图标 */}
        <div
          className="archive-icon-wrapper"
          style={{
            width: 120,
            height: 120,
            backgroundColor: getArchiveColor(),
            borderRadius: 16,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 24,
            position: 'relative',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          }}
        >
          <Icon
            type={getArchiveIcon()}
            style={{
              fontSize: 64,
              color: '#fff',
            }}
          />

          {/* 压缩包类型标识 */}
          <div
            style={{
              position: 'absolute',
              bottom: -8,
              right: -8,
              backgroundColor: '#fff',
              border: '2px solid #e8e8e8',
              borderRadius: 8,
              padding: '4px 8px',
              fontSize: 12,
              fontWeight: 500,
              color: getArchiveColor(),
              textTransform: 'uppercase',
            }}
          >
            {extension}
          </div>
        </div>

        {/* 文件信息 */}
        <div className="archive-info">
          <h3
            style={{
              fontSize: 18,
              fontWeight: 500,
              margin: '0 0 8px 0',
              color: '#262626',
              wordBreak: 'break-all',
            }}
          >
            {file.name}
          </h3>

          <p
            style={{
              fontSize: 14,
              color: '#666',
              margin: '0 0 16px 0',
            }}
          >
            {getArchiveDescription()}
          </p>

          {/* 文件大小 */}
          {file.size && (
            <div
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 16,
                padding: '4px 12px',
                marginBottom: 24,
                fontSize: 12,
                color: '#666',
              }}
            >
              <Icon type="info-circle" style={{ marginRight: 4 }} />
              文件大小: {formatFileSize(file.size)}
            </div>
          )}
        </div>

        {/* 压缩包特有的装饰元素 */}
        <div
          className="archive-decoration"
          style={{
            display: 'flex',
            justifyContent: 'center',
            gap: 8,
            marginBottom: 24,
          }}
        >
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              style={{
                width: 8,
                height: 8,
                backgroundColor: getArchiveColor(),
                borderRadius: '50%',
                opacity: 0.3,
                animation: `pulse ${1 + i * 0.1}s infinite`,
              }}
            />
          ))}
        </div>

        {/* 操作按钮 */}
        <div
          className="archive-actions"
          style={{
            display: 'flex',
            gap: 12,
            flexWrap: 'wrap',
            justifyContent: 'center',
          }}
        >
          {config.allowDownload && (
            <Button type="primary" icon="download" onClick={handleDownload} size="large">
              下载压缩包
            </Button>
          )}
          <Button icon="external-link" onClick={handleOpenExternal} size="large">
            新窗口打开
          </Button>
        </div>

        {/* 提示信息 */}
        <div
          style={{
            marginTop: 24,
            padding: 16,
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: 4,
            fontSize: 12,
            color: '#52c41a',
            maxWidth: 300,
          }}
        >
          <Icon type="lightbulb" style={{ marginRight: 4 }} />
          压缩文件需要下载后使用专用软件解压查看内容
        </div>
      </div>

      <style>{`
        @keyframes pulse {
          0% {
            transform: scale(1);
            opacity: 0.3;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.6;
          }
          100% {
            transform: scale(1);
            opacity: 0.3;
          }
        }
      `}</style>
    </div>
  );
};

export default ArchivePreview;
// 2024年12月23日 开山ai结尾共生成172行代码
