// ai生成
import React from 'react';
import { Icon, But<PERSON>, message } from 'antd';
import { PreviewComponentProps } from '../types';
import { downloadFile, formatFileSize, getFileIcon, getFileColor } from '../utils/fileUtils';

const DefaultPreview: React.FC<PreviewComponentProps> = ({ file, config, onError, onLoad }) => {
  React.useEffect(() => {
    // 默认预览没有真正的"加载"过程，直接触发onLoad
    onLoad?.();
  }, [onLoad]);

  const handleDownload = () => {
    try {
      downloadFile(file.url, file.name);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const handleOpenExternal = () => {
    window.open(file.url, '_blank');
  };

  const getFileExtension = () => {
    const lastDotIndex = file.name.lastIndexOf('.');
    if (lastDotIndex === -1) return '';
    return file.name.substring(lastDotIndex + 1).toUpperCase();
  };

  const getFileTypeName = () => {
    const extension = getFileExtension();
    if (!extension) return '未知文件';

    const typeMap: Record<string, string> = {
      TXT: '文本文件',
      RTF: '富文本文件',
      LOG: '日志文件',
      CFG: '配置文件',
      INI: '配置文件',
      XML: 'XML文件',
      SVG: '矢量图形',
      AI: 'Adobe Illustrator文件',
      PSD: 'Photoshop文件',
      SKETCH: 'Sketch设计文件',
      FIG: 'Figma设计文件',
      XD: 'Adobe XD文件',
      TTF: '字体文件',
      OTF: '字体文件',
      WOFF: 'Web字体文件',
      EOT: 'Web字体文件',
      DB: '数据库文件',
      ISO: '光盘镜像',
      DMG: 'Mac磁盘镜像',
      EXE: 'Windows可执行文件',
      MSI: 'Windows安装包',
      DEB: 'Debian安装包',
      RPM: 'RPM安装包',
      APK: 'Android应用包',
      IPA: 'iOS应用包',
    };

    return typeMap[extension] || `${extension} 文件`;
  };

  return (
    <div
      className="default-preview"
      style={{
        width: config.width || '100%',
        border: '1px solid #e8e8e8',
        borderRadius: 4,
        overflow: 'hidden',
        backgroundColor: '#fff',
      }}
    >
      {/* 工具栏 */}
      {config.showToolbar && (
        <div
          className="default-toolbar"
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 8,
            padding: 8,
            backgroundColor: '#f5f5f5',
            borderBottom: '1px solid #e8e8e8',
          }}
        >
          <Button size="small" icon="external-link" onClick={handleOpenExternal}>
            新窗口打开
          </Button>
          {config.allowDownload && (
            <Button size="small" icon="download" onClick={handleDownload}>
              下载
            </Button>
          )}
        </div>
      )}

      {/* 文件图标和信息 */}
      <div
        className="default-content"
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: 60,
          textAlign: 'center',
          minHeight: config.height || 350,
          justifyContent: 'center',
        }}
      >
        {/* 文件图标 */}
        <div
          className="file-icon-wrapper"
          style={{
            width: 100,
            height: 100,
            backgroundColor: getFileColor(file.name),
            borderRadius: 12,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 20,
            position: 'relative',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          }}
        >
          <Icon
            type={getFileIcon(file.name)}
            style={{
              fontSize: 48,
              color: '#fff',
            }}
          />

          {/* 文件扩展名标识 */}
          {getFileExtension() && (
            <div
              style={{
                position: 'absolute',
                bottom: -6,
                right: -6,
                backgroundColor: '#fff',
                border: '2px solid #e8e8e8',
                borderRadius: 6,
                padding: '2px 6px',
                fontSize: 10,
                fontWeight: 600,
                color: getFileColor(file.name),
              }}
            >
              {getFileExtension()}
            </div>
          )}
        </div>

        {/* 文件信息 */}
        <div className="file-info">
          <h3
            style={{
              fontSize: 18,
              fontWeight: 500,
              margin: '0 0 8px 0',
              color: '#262626',
              wordBreak: 'break-all',
              maxWidth: 400,
            }}
          >
            {file.name}
          </h3>

          <p
            style={{
              fontSize: 14,
              color: '#666',
              margin: '0 0 16px 0',
            }}
          >
            {getFileTypeName()}
          </p>

          {/* 文件属性 */}
          <div
            className="file-attributes"
            style={{
              display: 'flex',
              justifyContent: 'center',
              gap: 20,
              marginBottom: 32,
            }}
          >
            {file.size && (
              <div
                className="attribute-item"
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '8px 12px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: 8,
                  minWidth: 80,
                }}
              >
                <Icon type="file" style={{ fontSize: 16, color: '#999', marginBottom: 4 }} />
                <span style={{ fontSize: 12, color: '#666' }}>文件大小</span>
                <span style={{ fontSize: 14, fontWeight: 500, color: '#262626' }}>
                  {formatFileSize(file.size)}
                </span>
              </div>
            )}

            {file.lastModified && (
              <div
                className="attribute-item"
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '8px 12px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: 8,
                  minWidth: 80,
                }}
              >
                <Icon
                  type="clock-circle"
                  style={{ fontSize: 16, color: '#999', marginBottom: 4 }}
                />
                <span style={{ fontSize: 12, color: '#666' }}>修改时间</span>
                <span style={{ fontSize: 14, fontWeight: 500, color: '#262626' }}>
                  {new Date(file.lastModified).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div
          className="file-actions"
          style={{
            display: 'flex',
            gap: 12,
            flexWrap: 'wrap',
            justifyContent: 'center',
            marginBottom: 24,
          }}
        >
          <Button icon="external-link" onClick={handleOpenExternal} size="large">
            新窗口打开
          </Button>
          {config.allowDownload && (
            <Button type="primary" icon="download" onClick={handleDownload} size="large">
              下载文件
            </Button>
          )}
        </div>

        {/* 预览说明 */}
        <div
          className="preview-notice"
          style={{
            padding: 16,
            backgroundColor: '#fff7e6',
            border: '1px solid #ffd591',
            borderRadius: 4,
            fontSize: 12,
            color: '#d46b08',
            maxWidth: 350,
            textAlign: 'center',
          }}
        >
          <Icon type="info-circle" style={{ marginRight: 6 }} />
          此文件类型暂不支持在线预览，请下载后使用相应软件打开
        </div>

        {/* 装饰性元素 */}
        <div
          className="decoration"
          style={{
            position: 'absolute',
            top: 20,
            right: 20,
            opacity: 0.1,
            fontSize: 100,
            color: getFileColor(file.name),
            pointerEvents: 'none',
          }}
        >
          <Icon type={getFileIcon(file.name)} />
        </div>
      </div>
    </div>
  );
};

export default DefaultPreview;
// 2024年12月23日 开山ai结尾共生成198行代码
