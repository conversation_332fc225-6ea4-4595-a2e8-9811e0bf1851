// ai生成
import React, { useState, useRef, useEffect } from 'react';
import { Icon, Button, Spin, message } from 'antd';
import { PreviewComponentProps } from '../types';
import { downloadFile } from '../utils/fileUtils';

interface ImageState {
  scale: number;
  rotation: number;
  loaded: boolean;
  error: boolean;
}

const ImagePreview: React.FC<PreviewComponentProps> = ({ file, config, onError, onLoad }) => {
  const [imageState, setImageState] = useState<ImageState>({
    scale: 1,
    rotation: 0,
    loaded: false,
    error: false,
  });
  const [fullscreen, setFullscreen] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleImageLoad = () => {
    setImageState((prev) => ({ ...prev, loaded: true, error: false }));
    onLoad?.();
  };

  const handleImageError = () => {
    const error = new Error('图片加载失败');
    setImageState((prev) => ({ ...prev, error: true, loaded: false }));
    onError?.(error);
  };

  const handleZoomIn = () => {
    setImageState((prev) => ({
      ...prev,
      scale: Math.min(prev.scale * 1.2, 5),
    }));
  };

  const handleZoomOut = () => {
    setImageState((prev) => ({
      ...prev,
      scale: Math.max(prev.scale / 1.2, 0.1),
    }));
  };

  const handleRotate = () => {
    setImageState((prev) => ({
      ...prev,
      rotation: (prev.rotation + 90) % 360,
    }));
  };

  const handleReset = () => {
    setImageState((prev) => ({
      ...prev,
      scale: 1,
      rotation: 0,
    }));
  };

  const handleDownload = () => {
    try {
      downloadFile(file.url, file.name);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const handleFullscreen = () => {
    setFullscreen(!fullscreen);
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (e.deltaY > 0) {
      handleZoomOut();
    } else {
      handleZoomIn();
    }
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!fullscreen) return;

      switch (e.key) {
        case 'Escape':
          setFullscreen(false);
          break;
        case '+':
        case '=':
          handleZoomIn();
          break;
        case '-':
          handleZoomOut();
          break;
        case 'r':
        case 'R':
          handleRotate();
          break;
        case '0':
          handleReset();
          break;
      }
    };

    if (fullscreen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [fullscreen]);

  const imageStyle: React.CSSProperties = {
    transform: `scale(${imageState.scale}) rotate(${imageState.rotation}deg)`,
    transition: 'transform 0.3s ease',
    maxWidth: config.maxWidth || '100%',
    maxHeight: config.maxHeight || '100%',
    cursor: imageState.scale > 1 ? 'move' : 'zoom-in',
  };

  const containerStyle: React.CSSProperties = {
    position: fullscreen ? 'fixed' : 'relative',
    top: fullscreen ? 0 : 'auto',
    left: fullscreen ? 0 : 'auto',
    width: fullscreen ? '100vw' : config.width || 'auto',
    height: fullscreen ? '100vh' : config.height || 'auto',
    backgroundColor: fullscreen ? 'rgba(0, 0, 0, 0.9)' : 'transparent',
    zIndex: fullscreen ? 1000 : 'auto',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  };

  if (imageState.error) {
    return (
      <div className="file-preview-error">
        <Icon type="picture" style={{ fontSize: 48, color: '#ccc' }} />
        <p>图片加载失败</p>
      </div>
    );
  }

  return (
    <div ref={containerRef} style={containerStyle}>
      {config.showToolbar && (
        <div
          className="image-toolbar"
          style={{
            position: fullscreen ? 'absolute' : 'relative',
            top: fullscreen ? 20 : 0,
            left: fullscreen ? 20 : 0,
            zIndex: 1001,
            display: 'flex',
            gap: 8,
            padding: 8,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderRadius: 4,
            marginBottom: fullscreen ? 0 : 8,
          }}
        >
          <Button size="small" icon="zoom-in" onClick={handleZoomIn} />
          <Button size="small" icon="zoom-out" onClick={handleZoomOut} />
          <Button size="small" icon="redo" onClick={handleRotate} />
          <Button size="small" icon="reload" onClick={handleReset} />
          {config.allowFullscreen && (
            <Button
              size="small"
              icon={fullscreen ? 'fullscreen-exit' : 'fullscreen'}
              onClick={handleFullscreen}
            />
          )}
          {config.allowDownload && <Button size="small" icon="download" onClick={handleDownload} />}
          {fullscreen && <Button size="small" icon="close" onClick={() => setFullscreen(false)} />}
        </div>
      )}

      <div
        className="image-container"
        style={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'hidden',
          position: 'relative',
        }}
        onWheel={handleWheel}
      >
        {!imageState.loaded && <Spin size="large" style={{ position: 'absolute', zIndex: 1 }} />}
        <img
          ref={imageRef}
          src={file.url}
          alt={file.name}
          style={imageStyle}
          onLoad={handleImageLoad}
          onError={handleImageError}
          draggable={false}
        />
      </div>

      {config.showFileName && (
        <div
          className="file-info"
          style={{
            position: fullscreen ? 'absolute' : 'relative',
            bottom: fullscreen ? 20 : 0,
            left: fullscreen ? 20 : 0,
            right: fullscreen ? 20 : 0,
            padding: 8,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            borderRadius: 4,
            textAlign: 'center',
            marginTop: fullscreen ? 0 : 8,
          }}
        >
          <div>{file.name}</div>
          {config.showFileSize && file.size && (
            <div style={{ fontSize: 12, opacity: 0.8 }}>{Math.round(file.size / 1024)} KB</div>
          )}
          <div style={{ fontSize: 12, opacity: 0.8 }}>
            缩放: {Math.round(imageState.scale * 100)}% | 旋转: {imageState.rotation}°
          </div>
        </div>
      )}
    </div>
  );
};

export default ImagePreview;
// 2024年12月23日 开山ai结尾共生成200行代码
