// ai生成
import React, { useState, useEffect } from 'react';
import { Icon, Button, Spin, message } from 'antd';
import { PreviewComponentProps } from '../types';
import { getFileExtension, downloadFile } from '../utils/fileUtils';

interface DocumentState {
  loading: boolean;
  error: boolean;
  previewUrl: string;
  useIframe: boolean;
}

const DocumentPreview: React.FC<PreviewComponentProps> = ({ file, config, onError, onLoad }) => {
  const [docState, setDocState] = useState<DocumentState>({
    loading: true,
    error: false,
    previewUrl: '',
    useIframe: false,
  });

  const extension = getFileExtension(file.name);

  useEffect(() => {
    initPreview();
  }, [file.url]);

  const initPreview = async () => {
    setDocState((prev) => ({ ...prev, loading: true, error: false }));

    try {
      let previewUrl = file.url;
      let useIframe = false;

      // 根据文件类型选择预览方式
      if (extension === 'pdf') {
        // PDF 可以直接在iframe中预览
        useIframe = true;
      } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension)) {
        // Office 文档使用在线预览服务
        // 可以选择微软的 Office Online 或者其他第三方服务
        const encodedUrl = encodeURIComponent(file.url);

        // 使用微软 Office Online Viewer
        if (['doc', 'docx'].includes(extension)) {
          previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
        } else if (['xls', 'xlsx'].includes(extension)) {
          previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
        } else if (['ppt', 'pptx'].includes(extension)) {
          previewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
        }

        useIframe = true;
      } else if (extension === 'txt') {
        // 文本文件直接获取内容
        try {
          const response = await fetch(file.url);
          const text = await response.text();
          setDocState({
            loading: false,
            error: false,
            previewUrl: text,
            useIframe: false,
          });
          onLoad?.();
          return;
        } catch (error) {
          throw new Error('无法加载文本文件');
        }
      } else {
        throw new Error('不支持的文档类型');
      }

      setDocState({
        loading: false,
        error: false,
        previewUrl,
        useIframe,
      });
      onLoad?.();
    } catch (error) {
      setDocState((prev) => ({
        ...prev,
        loading: false,
        error: true,
      }));
      onError?.(error instanceof Error ? error : new Error('文档预览失败'));
    }
  };

  const handleDownload = () => {
    try {
      downloadFile(file.url, file.name);
    } catch (error) {
      message.error('下载失败');
    }
  };

  const handleRetry = () => {
    initPreview();
  };

  const handleOpenExternal = () => {
    window.open(file.url, '_blank');
  };

  const renderToolbar = () => {
    if (!config.showToolbar) return null;

    return (
      <div
        className="document-toolbar"
        style={{
          display: 'flex',
          gap: 8,
          padding: 8,
          backgroundColor: '#f5f5f5',
          borderBottom: '1px solid #e8e8e8',
          borderRadius: '4px 4px 0 0',
        }}
      >
        <Button size="small" icon="reload" onClick={handleRetry}>
          刷新
        </Button>
        <Button size="small" icon="external-link" onClick={handleOpenExternal}>
          新窗口打开
        </Button>
        {config.allowDownload && (
          <Button size="small" icon="download" onClick={handleDownload}>
            下载
          </Button>
        )}
      </div>
    );
  };

  const renderPreview = () => {
    if (docState.loading) {
      return (
        <div
          className="document-loading"
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: 300,
            backgroundColor: '#fafafa',
          }}
        >
          <Spin size="large" />
          <p style={{ marginTop: 16, color: '#666' }}>正在加载文档...</p>
        </div>
      );
    }

    if (docState.error) {
      return (
        <div
          className="document-error"
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: 300,
            backgroundColor: '#fafafa',
          }}
        >
          <Icon type="file-exclamation" style={{ fontSize: 48, color: '#ccc' }} />
          <p style={{ marginTop: 16, color: '#666' }}>文档预览失败</p>
          <div style={{ marginTop: 16, display: 'flex', gap: 8 }}>
            <Button size="small" onClick={handleRetry}>
              重试
            </Button>
            <Button size="small" onClick={handleOpenExternal}>
              新窗口打开
            </Button>
            {config.allowDownload && (
              <Button size="small" onClick={handleDownload}>
                下载
              </Button>
            )}
          </div>
        </div>
      );
    }

    if (docState.useIframe) {
      return (
        <iframe
          src={docState.previewUrl}
          style={{
            width: '100%',
            height: config.height || 600,
            border: 'none',
            backgroundColor: '#fff',
          }}
          onLoad={() => onLoad?.()}
          onError={() => {
            setDocState((prev) => ({ ...prev, error: true }));
            onError?.(new Error('文档加载失败'));
          }}
        />
      );
    }

    // 文本文件预览
    if (extension === 'txt') {
      return (
        <div
          className="text-preview"
          style={{
            padding: 16,
            backgroundColor: '#fff',
            border: '1px solid #e8e8e8',
            borderRadius: '0 0 4px 4px',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            fontSize: 14,
            lineHeight: 1.6,
            whiteSpace: 'pre-wrap',
            overflow: 'auto',
            maxHeight: config.height || 600,
          }}
        >
          {docState.previewUrl}
        </div>
      );
    }

    return null;
  };

  const renderFileInfo = () => {
    if (!config.showFileName) return null;

    return (
      <div
        className="document-info"
        style={{
          padding: 8,
          backgroundColor: '#f5f5f5',
          borderTop: '1px solid #e8e8e8',
          borderRadius: '0 0 4px 4px',
          fontSize: 12,
          color: '#666',
        }}
      >
        <div style={{ fontWeight: 500 }}>{file.name}</div>
        {config.showFileSize && file.size && (
          <div style={{ marginTop: 4 }}>文件大小: {Math.round(file.size / 1024)} KB</div>
        )}
        <div style={{ marginTop: 4 }}>文件类型: {extension.toUpperCase()}</div>
      </div>
    );
  };

  const getDocumentIcon = () => {
    const iconMap: Record<string, string> = {
      pdf: 'file-pdf',
      doc: 'file-word',
      docx: 'file-word',
      xls: 'file-excel',
      xlsx: 'file-excel',
      ppt: 'file-ppt',
      pptx: 'file-ppt',
      txt: 'file-text',
    };
    return iconMap[extension] || 'file';
  };

  return (
    <div
      className="document-preview"
      style={{
        width: config.width || '100%',
        border: '1px solid #e8e8e8',
        borderRadius: 4,
        overflow: 'hidden',
        backgroundColor: '#fff',
      }}
    >
      {renderToolbar()}

      <div
        className="document-header"
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: 12,
          backgroundColor: '#fafafa',
          borderBottom: '1px solid #e8e8e8',
        }}
      >
        <Icon
          type={getDocumentIcon()}
          style={{
            fontSize: 24,
            color: '#1890ff',
            marginRight: 12,
          }}
        />
        <div>
          <div style={{ fontWeight: 500, fontSize: 14 }}>{file.name}</div>
          <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
            {extension.toUpperCase()} 文档
            {file.size && ` • ${Math.round(file.size / 1024)} KB`}
          </div>
        </div>
      </div>

      {renderPreview()}
      {renderFileInfo()}
    </div>
  );
};

export default DocumentPreview;
// 2024年12月23日 开山ai结尾共生成198行代码
