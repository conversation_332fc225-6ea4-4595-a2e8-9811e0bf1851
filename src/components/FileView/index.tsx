// ai生成
import React from 'react';
import FilePreview from './FilePreview';
import { FileViewProps, FileInfo, PreviewConfig } from './types';
import {
  getFileType,
  getFileTypeConfig,
  isSupportPreview,
  formatFileSize,
  getFileIcon,
  getFileColor,
  downloadFile,
} from './utils/fileUtils';
import './index.module.less';

// 导出类型
export type { FileViewProps, FileInfo, PreviewConfig };

// 导出工具函数
export const FileViewUtils = {
  getFileType,
  getFileTypeConfig,
  isSupportPreview,
  formatFileSize,
  getFileIcon,
  getFileColor,
  downloadFile,
};

// 主组件
const FileView: React.FC<FileViewProps> = (props) => {
  return <FilePreview {...props} />;
};

// 便捷的预览组件
export const QuickPreview: React.FC<{
  url: string;
  fileName: string;
  fileSize?: number;
  width?: number;
  height?: number;
  showToolbar?: boolean;
}> = ({ url, fileName, fileSize, width, height, showToolbar = true }) => {
  const file: FileInfo = {
    url,
    name: fileName,
    size: fileSize,
  };

  const config: PreviewConfig = {
    width,
    height,
    showToolbar,
    showFileName: true,
    showFileSize: true,
    allowDownload: true,
    allowFullscreen: true,
  };

  return <FileView file={file} config={config} />;
};

// 文件列表预览组件
export const FileListPreview: React.FC<{
  files: FileInfo[];
  selectedIndex?: number;
  onFileSelect?: (index: number) => void;
  previewConfig?: PreviewConfig;
}> = ({ files, selectedIndex = 0, onFileSelect, previewConfig }) => {
  const [currentIndex, setCurrentIndex] = React.useState(selectedIndex);

  React.useEffect(() => {
    setCurrentIndex(selectedIndex);
  }, [selectedIndex]);

  const handleFileSelect = (index: number) => {
    setCurrentIndex(index);
    onFileSelect?.(index);
  };

  if (!files.length) {
    return <div>没有文件可预览</div>;
  }

  const currentFile = files[currentIndex];

  return (
    <div className="file-list-preview">
      {files.length > 1 && (
        <div
          className="file-list"
          style={{
            display: 'flex',
            gap: 8,
            padding: 8,
            backgroundColor: '#f5f5f5',
            borderRadius: 4,
            marginBottom: 16,
            flexWrap: 'wrap',
          }}
        >
          {files.map((file, index) => (
            <div
              key={index}
              className={`file-item ${index === currentIndex ? 'active' : ''}`}
              style={{
                padding: 8,
                borderRadius: 4,
                cursor: 'pointer',
                backgroundColor: index === currentIndex ? '#1890ff' : '#fff',
                color: index === currentIndex ? '#fff' : '#262626',
                border: '1px solid #e8e8e8',
                fontSize: 12,
                maxWidth: 150,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
              onClick={() => handleFileSelect(index)}
              title={file.name}
            >
              {file.name}
            </div>
          ))}
        </div>
      )}

      <FileView file={currentFile} config={previewConfig} />
    </div>
  );
};

export default FileView;
// 2024年12月23日 开山ai结尾共生成102行代码
