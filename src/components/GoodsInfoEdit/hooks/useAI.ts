// ai生成
import React, { useState, useRef } from 'react';
import {
  createInterestPoints,
  nonLiveSelectionCreateInterestPoints,
  selectionInterestPoints,
  nonLiveSelectionQueryInterestPoints,
} from '../services/point';
import { useRequest } from 'ahooks';
import { message } from 'antd';

export const useAI = (cb: any) => {
  // const [loading, setLoading] = useState<boolean>(false);
  const [createAIVisible, setCreateAIVisible] = useState<boolean>(false);
  // ai生成 - 添加轮询loading状态
  const [pollingLoading, setPollingLoading] = useState<boolean>(false);
  // ai生成结尾
  const pollingTimerRef = useRef<any>(null);
  const pollingCountRef = useRef<number>(0);

  const openAIModal = () => {
    setCreateAIVisible(true);
  };

  const closeAIModal = () => {
    setCreateAIVisible(false);
    // ai生成 - 关闭时清理轮询loading状态
    setPollingLoading(false);
    // ai生成结尾
    // 清理轮询定时器
    if (pollingTimerRef.current) {
      clearTimeout(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }
    pollingCountRef.current = 0;
  };

  // 轮询 selectionInterestPoints 接口
  const pollSelectionInterestPoints = (params: { spuId?: string; taskId?: string }) => {
    // ai生成 - 开始轮询时设置loading状态
    setPollingLoading(true);
    // ai生成结尾

    const poll = () => {
      pollingCountRef.current += 1;

      // 最多轮询10分钟，20秒一次，即30次
      if (pollingCountRef.current > 30) {
        message.error('轮询超时，请稍后重试');
        closeAIModal();
        return;
      }

      selectionInterestPoints(params)
        .then(({ res }) => {
          if (!res?.success) {
            message.error(res?.message || '轮询失败');
            closeAIModal();
            return;
          }

          // 如果有结果了，调用回调并关闭
          if (res.result === true) {
            cb?.();
            closeAIModal();
          } else {
            // 继续轮询
            pollingTimerRef.current = setTimeout(poll, 20000);
          }
        })
        .catch(() => {
          message.error('轮询请求失败');
          closeAIModal();
        });
    };

    // ai生成 - 第一次轮询立即开始
    poll();
    // ai生成结尾
  };

  // 轮询 nonLiveSelectionQueryInterestPoints 接口
  const pollNonLiveSelectionQueryInterestPoints = (params: { id?: string; taskId?: string }) => {
    // ai生成 - 开始轮询时设置loading状态
    setPollingLoading(true);
    // ai生成结尾

    const poll = () => {
      pollingCountRef.current += 1;

      // 最多轮询10分钟，20秒一次，即30次
      if (pollingCountRef.current > 30) {
        message.error('轮询超时，请稍后重试');
        closeAIModal();
        return;
      }

      nonLiveSelectionQueryInterestPoints(params)
        .then(({ res }) => {
          if (!res?.success) {
            message.error(res?.message || '轮询失败');
            closeAIModal();
            return;
          }

          // 如果有结果了，调用回调并关闭
          if (res.result === true) {
            cb?.();
            closeAIModal();
          } else {
            // 继续轮询
            pollingTimerRef.current = setTimeout(poll, 20000);
          }
        })
        .catch(() => {
          message.error('轮询请求失败');
          closeAIModal();
        });
    };

    // ai生成 - 第一次轮询立即开始
    poll();
    // ai生成结尾
  };

  const { run, loading } = useRequest(createInterestPoints, {
    manual: true,
    onSuccess({ res }, params) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }

      // 执行成功后开始轮询 selectionInterestPoints
      pollingCountRef.current = 0;
      pollSelectionInterestPoints({
        spuId: params[0]?.spuId,
        taskId: res?.result, // 假设返回的数据包含taskId
      });
    },
  });

  const {
    run: nonLiveSelectionCreateInterestPointsRun,
    loading: nonLiveSelectionCreateInterestPointsLoading,
  } = useRequest(nonLiveSelectionCreateInterestPoints, {
    manual: true,
    onSuccess({ res }, params) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      message.success('操作成功');

      // 执行成功后开始轮询 nonLiveSelectionQueryInterestPoints
      // pollingCountRef.current = 0;
      // pollNonLiveSelectionQueryInterestPoints({
      //   id: params[0]?.id,
      //   taskId: res?.result, // 假设返回的数据包含taskId
      // });
    },
  });

  // const run = (params: any) => {
  //   setLoading(true);
  //   return (resolve: any, reject: any) => {
  //     createInterestPoints(params)
  //       .then(({ res }) => {
  //         if (!res?.success) {
  //           message.warning(res?.message || '网络异常');
  //           return;
  //         }
  //         cb?.();
  //         resolve();
  //       })
  //       .catch(() => {
  //         resolve();
  //       })
  //       .finally(() => {
  //         setLoading(false);
  //       });
  //   };
  // };

  return {
    createAIRun: run,
    createAILoading: loading,
    createAIVisible,
    openAIModal,
    closeAIModal,
    nonLiveSelectionCreateInterestPointsRun,
    nonLiveSelectionCreateInterestPointsLoading,
    // ai生成 - 导出轮询loading状态
    pollingLoading,
    // ai生成结尾
  };
};
// 2024年12月31日 开山ai结尾共生成11行代码
