import { message } from 'antd';
import { nonLiveSelectionEditInterestPoints, updateInterestPointsData } from '../services/point';
import { useRequest } from 'ahooks';

export const useEdit = (cb: any) => {
  const { run: editRun, loading: editLoading } = useRequest(updateInterestPointsData, {
    manual: true,
    onSuccess({ res }) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      message.success('操作成功');
      cb?.();
    },
  });
  const {
    run: nonLiveSelectionEditInterestPointsRun,
    loading: nonLiveSelectionEditInterestPointsLoading,
  } = useRequest(nonLiveSelectionEditInterestPoints, {
    manual: true,
    onSuccess({ res }) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      message.success('操作成功');
      cb?.();
    },
  });
  return {
    editLoading,
    editRun,
    nonLiveSelectionEditInterestPointsRun,
    nonLiveSelectionEditInterestPointsLoading,
  };
};
