/**
 *
 * @param props 接受 type和info 因为这个组件只有新增和编辑
 * @returns 抛出onOpen  onClose onComplet作为完成表单，传输数据方法
 *
 */

import React, { useState, forwardRef, useImperativeHandle, useEffect, useMemo } from 'react';
import { Drawer, Button, Card, Form, message, Tag, Icon, Modal, Input, Select } from 'antd';
import LeftCard from './leftCard';
import RightCard from './RightCard';
import { FlowStatus, FlowStatusColor, FlowStatusEnumAll } from '@/common/constants/moduleConstant';
import { copyText } from '@/utils/moduleUtils';
import {
  cascadeList,
  bpEditAndConfirm,
  bpEdit,
  isSignAgreement,
  getServiceType,
} from '@/services/yml/choose/index';
import './index.less';
import { AuthWrapper } from 'qmkit';
import { lowCommissionJudge, LowCommissionJudgeResult } from './services/yml/index';
import styles from './index.module.less';
import { LiveGoodsInfoResult } from '@/services/yml/choiceList';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { LegalCheckDrawer } from '@/pages/choice-list-new/components';
import { getLabelOptionList } from './component/LabelOptionFormItems';
import DisableModal from '../DisableModal';
import { ExplosiveAuditStatusAll, ExplosiveAuditStatusColor } from './component/type';
import HotGoodsApply from './component/HotGoodsApply';
import BaobiMarkModal from '../BaobiMarkModal';
import CreateEditRatioModal from '@/pages/guaranteed-ratio/modules/CreateEditRatioModal';
import { useLocation } from 'react-router-dom';
import {
  UN_SELECT_STEP_NAME,
  UN_SELECT_STEP_COLOR,
  UN_FlowStatus_ChoiceList,
} from '@/pages/selection-flow-board/types';
interface GoodsInfoEditProps {
  type?: string;
  info?: LiveGoodsInfoResult;
  form?: WrappedFormUtils;
  onRef?: any;
  isSupplier?: boolean;
  entry?: 'changci' | 'xuanpin';
  recordInfo?: any;
  childrenMsg?: any;
  confirmHandle?: any;
  liveRoundId?: any;
  tabsValue?: string;
  onSearch?: () => void;
  getInfo?: () => void;
  onDetail?: (value: any, type: string) => void;
  pageTag?: string; // 保比标记按钮只有选品看板才有
  search?: () => void;
}
const GoodsInfoEdit = forwardRef((props: GoodsInfoEditProps, ref) => {
  const {
    type,
    info,
    form,
    onRef,
    isSupplier,
    entry,
    recordInfo,
    confirmHandle,
    childrenMsg,
    liveRoundId,
    getInfo,
    tabsValue = 'live',
    pageTag,
  } = props;

  useEffect(() => {
    form.onOpen = onOpen;
    form.onClose = onOpen;
    const userName =
      localStorage.getItem('jgpy-crm@login') &&
      JSON.parse(localStorage.getItem('jgpy-crm@login'))?.employeeName;
    userName && setActionName(userName);
    // handleClickEdit();
  }, []);

  const [actionName, setActionName] = useState('');
  const [visible, setVisible] = useState(false);
  const [opType, setOptype] = useState<'edit' | 'info'>();
  const [cascadeArr, setCascadeArr] = useState([]);
  const [childMsg, setChildMsg] = useState();
  const handleClickEdit = () => {
    getServiceType({ liveRoundId: liveRoundId }).then((res) => {
      if (res?.res?.code === '200') {
        setChildMsg(res?.res?.result);
      }
    });
  };
  const onOpen = (type: any) => {
    // console.log('info', info);
    if (type === 'edit') {
      handleClickEdit();
      setOptype('edit');
    } else {
      setOptype('info');
    }
    setVisible(true);
  };
  useEffect(() => {
    getCascadeList(info);
  }, [info]);

  const getCascadeList = (value) => {
    if (value.talentId) {
      cascadeList({
        deptId: value.deptId,
        labelStatus: 'ENABLE',
        talentIdList: [value.talentId],
      }).then((res) => {
        if (res?.res?.code === '200') {
          const list = res.res.result.cascadeList;
          setCascadeArr(list);
        }
      });
    }
  };
  const onClose = () => {
    setVisible(false);
  };
  const [confirmVisible, setconFirmVisible] = useState(false);
  const [isSignFile, setIsSignFile] = useState(false);
  const handleOk = () => {
    bpEditAndConfirm({ ...saveParams })
      .then((res) => {
        // console.log(res);
        if (res?.res?.code === '200') {
          message.success('操作成功');
          props?.search && props?.search();
          onClose();
        } else {
          message.warn(res?.res?.message);
        }
        setconFirmVisible(false);
      })
      .catch((err) => {
        setconFirmVisible(false);
      });
  };
  const handleCancel = () => {
    setconFirmVisible(false);
  };
  const [saveParams, setSaveParams] = useState({});
  const save = (exist?: boolean) => {
    form.validateFields((err, values) => {
      if (values && values?.liveServiceTypeId === info?.liveServiceType) {
        values.liveServiceTypeId = info?.liveServiceTypeId;
      }
      if (values.commissionRate + values.commissionRateOffline > 100) {
        message.warn('总佣金不能超过100，请修改线上佣金/线下佣金');
        return;
      }
      const labelOptionContent = [];
      cascadeArr?.forEach((item) => {
        if (values[item.id]) {
          const obj = {
            chooseMethod: item?.chooseMethod,
            id: item?.id,
            labelGroupName: item?.labelGroupName,
            requiredFlag: item?.requiredFlag,
            labelOptionList: [],
          };
          item?.labelOptionList?.forEach((i) => {
            if (Array.isArray(values[item?.id])) {
              if (values[item?.id].indexOf(i.labelOption) > -1) {
                obj.labelOptionList.push({
                  id: i.optionId,
                  labelOption: i.labelOption,
                });
              }
            } else {
              if (values[item?.id] === i.labelOption) {
                obj.labelOptionList.push({
                  id: i.optionId,
                  labelOption: i.labelOption,
                });
              }
            }
          });
          labelOptionContent.push(obj);
        }
      });
      // console.log('Received values of form: ', values);
      let isComplete = false;
      values.skus?.forEach((i) => {
        if ((!i.stock && i.stock !== 0) || !i.salePrice) {
          isComplete = true;
        }
      });
      if (isComplete) {
        message.warn('sku信息未填写完整，请检查');
        return;
      }
      if (!err) {
        const isLowCommissionParam = (lowCommissionInfo as LowCommissionJudgeResult)
          ?.isLowCommission
          ? {
              isLowCommission: lowCommissionInfo?.isLowCommission,
              lowCommissionRequest: {
                estimateGmv: values?.estimateGmv ?? undefined,
                finishedGmv: values?.finishedGmv ?? undefined,
                guaranteedGmv: values?.guaranteedGmv ?? undefined,
                highAnchorName:
                  values?.highAnchorName ??
                  lowCommissionInfo?.highCommissionModel?.highAnchorName ??
                  undefined,
                highAuthorOpenId:
                  values?.highAuthorOpenId ??
                  lowCommissionInfo?.highCommissionModel?.highAuthorOpenId ??
                  undefined,
                highBpName:
                  values?.highBpName ??
                  lowCommissionInfo?.highCommissionModel?.highBpName ??
                  undefined,
                highBrandFee:
                  // values?.highBrandFee ??
                  lowCommissionInfo?.highCommissionModel?.highBrandFee ?? undefined,
                highLiveDate:
                  values?.highLiveDate ??
                  lowCommissionInfo?.highCommissionModel?.highLiveDate ??
                  undefined,
                highPayGmv:
                  values?.highPayGmv ??
                  lowCommissionInfo?.highCommissionModel?.highPayGmv ??
                  undefined,
                highSalePrice:
                  values?.highSalePrice ??
                  lowCommissionInfo?.highCommissionModel?.highSalePrice ??
                  undefined,
                highTotalCommissionRate:
                  values?.highTotalCommissionRate ??
                  lowCommissionInfo?.highCommissionModel?.highTotalCommissionRate ??
                  undefined,
                quantityGuaranteedLiveRoundBrandFee:
                  values?.quantityGuaranteedLiveRoundBrandFee ?? undefined,
                quantityGuaranteedLiveRoundId:
                  values?.quantityGuaranteedLiveRoundId?.key ?? undefined,
                reasonDetail: values?.reasonDetail ?? undefined,
                reasonType: values?.reasonType ?? undefined,
                supplementLiveRoundBrandFee: values?.supplementLiveRoundBrandFee ?? undefined,
                supplementLiveRoundId: values?.supplementLiveRoundId?.key ?? undefined,
                brandFee: values?.brandFee ?? info?.brandFee ?? undefined,
              },
            }
          : {};
        const params = {
          labelOptionContent,
          id: info?.id,
          ...values,
          quantityGuaranteedLiveRoundId: undefined,
          afterSaleContent: {
            ...values.afterSaleContent,
          },
          brandName: info?.brandName || values?.brandId?.label,
          brandId: info?.brandId || values?.brandId?.key,
          version: info?.version,
          commissionRate: (values?.commissionRate / 100).toFixed(4),
          commissionRateOffline: (values?.commissionRateOffline / 100).toFixed(4),
          ...isLowCommissionParam,
          labelConfigInfo: JSON.parse(
            JSON.stringify(getLabelOptionList(form?.getFieldsValue() ?? {})),
          ),
          expireStartTime: info?.expireStartTime,
          expireEndTime: info?.expireEndTime,
        };
        Object.keys(params).forEach((key: string) => {
          if (key.includes('labelOpTion_label_value')) {
            delete params[key];
          }
        });
        setSaveParams(params);

        getIsSign();
        // console.log('params', params);
      }
    });
  };
  const getIsSign = () => {
    isSignAgreement({ id: info?.id })
      .then((res) => {
        // console.log(res);
        // return res?.result;

        if (res?.res?.result === true) {
          setIsSignFile(false);
        } else {
          setIsSignFile(true);
        }
        setconFirmVisible(true);
      })
      .catch((err) => {
        return false;
      });
  };
  const [lowCommissionInfo, seLowCommissionInfo] = useState<LowCommissionJudgeResult>();
  const getlowCommissionJudge = async (params?: {
    initCommissionRate?: string;
    frameworkRoundFlag?: string;
  }) => {
    // console.log(info);
    const { initCommissionRate, frameworkRoundFlag } = params ?? {};
    const {
      brandId,
      commissionRate,
      commissionRateOffline,
      deptId,
      no,
      platformSource,
      platformSpuId,
      spuName,
      supplierId,
    } = info;
    try {
      const { res } = await lowCommissionJudge({
        brandId,
        commissionRate: initCommissionRate ?? commissionRate,
        commissionRateOffline,
        deptId,
        no,
        platformSource,
        platformSpuId,
        spuName,
        supplierId,
        lowCommissionGuaranteeQuantityFlag: frameworkRoundFlag,
      });
      if (res.code !== '200') {
        message.error(res.message || '网络错误');
        return;
      }
      const { result } = res;
      seLowCommissionInfo(result);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    visible && entry !== 'xuanpin' && tabsValue === 'live' && getlowCommissionJudge();
  }, [visible]);
  React.useImperativeHandle(onRef, () => ({
    onOpen,
    onClose,
  }));
  const [legalInfoVisible, setLegalInfoVisible] = useState(false);
  const handleOpenGoodsQua = () => {
    setLegalInfoVisible(true);
  };
  // 禁用解禁相关
  const [isDisabledVisible, setIsDisabledVisible] = useState(false); // true: 打开，false: 关闭弹框
  const [isDisabledTag, setIsDisabledTag] = useState(true); // true: 禁用，false: 解禁
  const handleOpenDisModal = (type: boolean) => {
    setIsDisabledTag(type);
    setIsDisabledVisible(true);
  };
  // 保比标记相关
  const [baobiMarkVisible, setBaobiMarkVisible] = useState(false);
  const pathname = useMemo(() => useLocation().pathname, [useLocation()]);
  return (
    <Drawer
      title={
        <div style={{ height: '22px' }}>
          {entry === 'xuanpin' ? (
            '商品详情'
          ) : (
            <div style={{ float: 'left', color: '#111111', fontSize: '14px', fontWeight: 500 }}>
              选品编号:{info?.no}
            </div>
          )}
          <Icon
            onClick={() => {
              copyText(info?.no || '');
            }}
            style={{ marginLeft: '8px', color: '#204EFF' }}
            type="copy"
          />
          {tabsValue === 'live' && (
            <Tag style={{ marginLeft: '8px' }} color={FlowStatusColor[info?.status]}>
              {FlowStatusEnumAll[info?.status]}
            </Tag>
          )}
          {tabsValue === 'unlive' && (
            <Tag style={{ marginLeft: '8px' }} color={UN_SELECT_STEP_COLOR[info?.status]}>
              {UN_SELECT_STEP_NAME[info?.status]}
            </Tag>
          )}
          {info?.explosiveAuditStatus && (
            <Tag
              style={{ marginLeft: '8px' }}
              color={ExplosiveAuditStatusColor[info?.explosiveAuditStatus]}
            >
              {ExplosiveAuditStatusAll[info?.explosiveAuditStatus]}
            </Tag>
          )}
          <div
            className="footerBox"
            style={entry === 'workbench' ? { marginTop: '-5px' } : { marginTop: '-13px' }}
          >
            <div className="footerButton">
              {/* {info?.legalStatus && info?.legalStatus !== 'NONE' && (
                <AuditDetail id={info?.id} source="choiceList">
                  <Button type="primary" style={{ marginRight: '10px' }}>
                    法务审核结果
                  </Button>
                </AuditDetail>
              )} */}
              {(recordInfo?.operatorStatus === 'PASS' ||
                recordInfo?.operatorStatus === 'AUTO_PASS' ||
                recordInfo?.operatorStatus === 'SKIP') &&
                (recordInfo?.selectionStatus === 'PASS' ||
                  recordInfo?.selectionStatus === 'AUTO_PASS' ||
                  recordInfo?.selectionStatus === 'SKIP') &&
                recordInfo?.status === 'WAIT_AUDIT' &&
                recordInfo?.supplierStatus === 'INIT' && (
                  <Button style={{ marginRight: 10 }} type="primary" onClick={props.onsure}>
                    确认 {/* 商家工作台的确认按钮 */}
                  </Button>
                )}
              {opType === 'edit' ? (
                // 同时拥有商务保存和确认才出现按钮
                <AuthWrapper functionName="f_choose_bp_edit">
                  <AuthWrapper functionName="f_choose_bp_confirm">
                    {
                      <Button
                        className="save"
                        type="primary"
                        onClick={() => {
                          save();
                        }}
                      >
                        保存并确认
                      </Button>
                    }
                  </AuthWrapper>
                </AuthWrapper>
              ) : (
                info?.status === 'BP_CONFIRMING' && (
                  <AuthWrapper functionName="f_choose_bp_edit">
                    <Button
                      type="primary"
                      style={{ marginRight: '10px' }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClickEdit();
                        setOptype('edit');
                      }}
                    >
                      编辑
                    </Button>
                  </AuthWrapper>
                )
              )}
              {/* @TODO: 选品池详情抽屉顶部右侧按钮 */}
              {entry === 'xuanpin' ? (
                <>
                  {info?.explosiveSourceFlag === true &&
                    info?.explosiveType === 'PRE_EXPLOSIVE' &&
                    info?.explosiveAuditStatus === null && (
                      <AuthWrapper functionName="f_goods_assorting_add_explosive_apply">
                        <HotGoodsApply
                          info={info}
                          onRefresh={() => {
                            getInfo && getInfo();
                          }}
                        >
                          <Button type="primary" style={{ marginRight: '10px' }}>
                            提交到爆品池
                          </Button>
                        </HotGoodsApply>
                      </AuthWrapper>
                    )}
                  {(info?.status === 'SELECTING' || info?.status === 'INVALID') && (
                    <AuthWrapper functionName="f_goods_assorting_goods_disable">
                      <Button
                        type="danger"
                        style={{ marginRight: '10px' }}
                        onClick={() => handleOpenDisModal(true)}
                      >
                        禁用
                      </Button>
                    </AuthWrapper>
                  )}
                  {info?.status === 'DISABLED' && (
                    <AuthWrapper functionName="f_goods_assorting_goods_enable">
                      <Button
                        type="primary"
                        style={{
                          backgroundColor: '#19BE6B',
                          borderColor: '#19BE6B',
                          marginRight: '10px',
                        }}
                        onClick={() => handleOpenDisModal(false)}
                      >
                        解禁
                      </Button>
                    </AuthWrapper>
                  )}
                  <AuthWrapper functionName="f_goods_assorting_look_goods_qua">
                    <Button style={{ marginRight: '10px' }} onClick={handleOpenGoodsQua}>
                      查看商品资质
                    </Button>
                  </AuthWrapper>
                </>
              ) : (
                <></>
              )}
              {/* 保比弹窗 */}
              {!!(
                !info?.guaranteeProportionId &&
                info?.brandFee &&
                Number(info.brandFee) > 0 &&
                info?.status !== 'CANCEL' &&
                info?.status !== 'ABORT_LIVE' &&
                info?.status !== 'LOSE_EFFICACY' &&
                info?.status !== 'ABORT_WAIT_LIVE' &&
                opType !== 'edit' &&
                tabsValue === 'live' &&
                (pathname === '/choice-list' || pathname === '/selection-flow-board')
              ) && (
                <CreateEditRatioModal
                  type="create"
                  onRefresh={() => {
                    setVisible(false);
                    props?.search && props.search();
                  }}
                  info={info}
                />
              )}
              {/* {pageTag === 'xuanpinkanban' && opType !== 'edit' && tabsValue === 'live' ? (
                <AuthWrapper functionName="f_choose_baobi_mark">
                  <Button
                    style={{ marginRight: '10px' }}
                    onClick={() => {
                      setBaobiMarkVisible(true);
                    }}
                  >
                    保比标记
                  </Button>
                </AuthWrapper>
              ) : (
                <></>
              )} */}

              <Button
                className="cancel"
                onClick={() => {
                  setVisible(false);
                }}
              >
                取消
              </Button>
            </div>
          </div>
        </div>
      }
      placement="right"
      closable={false}
      width={1350}
      onClose={onClose}
      visible={visible}
      style={{ transform: 'translateX(0)' }}
    >
      {visible && (
        <div className={`${styles.boxForm}` + ' box'}>
          <div className="content">
            <LeftCard
              entry={entry!}
              type={opType}
              cascadeArr={cascadeArr}
              isSupplier={isSupplier}
              info={info}
              form={form}
              lowCommissionInfo={lowCommissionInfo}
              childrenMsg={childrenMsg}
              liveRoundId={liveRoundId}
              childMsg={childMsg}
              lowCommissionInfo={lowCommissionInfo}
              getlowCommissionJudge={getlowCommissionJudge}
              tabsValue={tabsValue}
            />
            <RightCard
              entry={entry}
              type={opType}
              info={info}
              form={form}
              tabsValue={tabsValue}
              onRefresh={() => {
                getInfo && getInfo();
                props?.onDetail?.(info, 'info');
              }}
              visible={visible}
            ></RightCard>
          </div>
        </div>
      )}
      <Modal
        width={424}
        visible={confirmVisible}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <div
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          style={{ display: 'flex' }}
        >
          <Icon
            type="exclamation-circle"
            className="iconStyle"
            style={{ color: '#3772ff', marginRight: 16 }}
          />
          <div>
            <h3> {'确认提交选品信息?'}</h3> <br />
            {isSignFile &&
              '当前商家未确认《新媒体营销协议》，请引导商家在商家端-合同-合同管理中进行确认.'}
          </div>
        </div>
      </Modal>
      <LegalCheckDrawer
        info={info}
        visible={legalInfoVisible}
        handleClose={() => {
          setLegalInfoVisible(false);
        }}
        isGoodsAssorting={true}
      />
      <DisableModal
        setIsDisabledVisible={setIsDisabledVisible}
        isDisabledVisible={isDisabledVisible}
        isDisabledTag={isDisabledTag}
        info={info}
        onSearch={props.onSearch}
        setVisible={setVisible}
      ></DisableModal>
      <BaobiMarkModal
        setBaobiMarkVisible={setBaobiMarkVisible}
        visible={baobiMarkVisible}
        info={info}
        onDetail={props.onDetail}
      ></BaobiMarkModal>
    </Drawer>
  );
});

export default Form.create({ name: 'editForm' })(GoodsInfoEdit);
