const signMap = {
  '/iasm/public/web/liveRound/listAllLiveRoom': 'basic.x1.xxxx1',
  '/pim/public/selectGoodsPool/createInterestPoints': 'basic.x1.xxxx1',
  '/pim/public/selectGoodsPool/updateInterestPointsData': 'basic.x1.xxxx1',
  '/tools/public/operation/queryOperationLogList': 'basic.x1.xxxx1',
  '/iasm/public/selection/redEnvelopeDetail': 'basic.x1.xxxx1',
  '/iasm/public/selection/editRedEnvelope': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/listLiveRoomSortByDept': 'basic.x1.xxxx1',
  '/iasm/public/nonLiveSelection/createInterestPoints': 'basic.x1.xxxx1',
  '/iasm/public/nonLiveSelection/editInterestPoints': 'basic.x1.xxxx1',
  '/iasm/public/nonLiveSelection/queryInterestPoints': 'basic.x1.xxxx1',
  '/pim/public/selectGoodsPool/interestPointsGet': 'basic.x1.xxxx1',
};

export function getSign(path: string): string {
  return signMap[path];
}
