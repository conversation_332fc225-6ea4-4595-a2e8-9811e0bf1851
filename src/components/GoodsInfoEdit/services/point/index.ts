import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type ListAllLiveRoomRequest = {
  deptId?: string /*事业部id*/;
  platformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*类型[PlatformEnum]*/;
};

export type ListAllLiveRoomResult = Array<{
  avatar?: string /*头像*/;
  buId?: string /*事业部id*/;
  buName?: string /*事业部名称*/;
  id?: string /*id*/;
  institutionId?: string /*达人所属机构ID*/;
  name?: string /*直播间名称*/;
  openId?: string /*直播间openId*/;
  platform?: string /*平台*/;
  sortingNum?: number /*排序权重*/;
  talentId?: string /*达人id*/;
  talentNo?: string /*达人编号*/;
}>;

/**
 *所有直播间列表
 */
export const listAllLiveRoom = (params: ListAllLiveRoomRequest) => {
  return Fetch<ResponseWithResult<ListAllLiveRoomResult>>(
    '/iasm/public/web/liveRound/listAllLiveRoom',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/web/liveRound/listAllLiveRoom') },
    },
  );
};

export type CreateInterestPointsRequest = {
  spuId?: string /*商品ID*/;
  whetherSingle?: boolean /*是否属于单个操作*/;
};

export type CreateInterestPointsResult = string;

/**
 *根据平台商品ID生成利益点
 */
export const createInterestPoints = (params: CreateInterestPointsRequest) => {
  return Fetch<ResponseWithResult<CreateInterestPointsResult>>(
    '/pim/public/selectGoodsPool/createInterestPoints',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/selectGoodsPool/createInterestPoints') },
    },
  );
};

export type UpdateInterestPointsDataRequest = {
  interestPoints?: string /*利益点*/;
  liveEndDate?: string /*直播结束日期*/;
  liveRoomIdList?: Array<string> /*直播间id*/;
  liveStartDate?: string /*直播开始日期*/;
  spuId?: string /*商品id*/;
};

export type UpdateInterestPointsDataResult = boolean;

/**
 *编辑利益点
 */
export const updateInterestPointsData = (params: UpdateInterestPointsDataRequest) => {
  return Fetch<ResponseWithResult<UpdateInterestPointsDataResult>>(
    '/pim/public/selectGoodsPool/updateInterestPointsData',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/selectGoodsPool/updateInterestPointsData') },
    },
  );
};

export type QueryOperationLogListRequest = {
  bizOrderId?: string /*业务单据id*/;
  bizOrderNo?: string /*业务单据编号*/;
  bizOrderType?: string /*业务单据类型*/;
  bizOrderTypeList?: Array<string> /*业务单据类型*/;
  bizTypeModels?: Array<{
    bizOrderType?: string /*业务单据类型*/;
    secondBizTypes?: Array<string> /*二级业务单据类型*/;
  }> /*一二级业务单据类型集合*/;
  current?: number /*当前页码,从1开始*/;
  employeeIdList?: Array<string> /*用户id*/;
  operateDesc?: string /*操作类型描述*/;
  operateEndTime?: string /*操作结束时间*/;
  operateStartTime?: string /*操作开始时间*/;
  operateType?: string /*操作类型*/;
  operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
  operatorName?: string /*操作人名称*/;
  size?: number /*分页大小*/;
};

export type QueryOperationLogListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    bizOrderId?: string /*业务单据id*/;
    bizOrderNo?: string /*业务单据编号*/;
    bizOrderType?: string /*业务单据类型*/;
    contentSnapshot?: string /*操作之前的快照*/;
    id?: string /*主键*/;
    operateDesc?: string /*操作类型*/;
    operateTime?: string /*操作时间*/;
    operateType?: string /*操作类型*/;
    operatorAccount?: string /*操作人账号*/;
    operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
    operatorContent?: string /*操作内容*/;
    operatorId?: string /*操作人id*/;
    operatorName?: string /*操作人姓名*/;
    secondBizType?: string /*二级业务单据类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询操作日志
 */
export const queryOperationLogList = (params: QueryOperationLogListRequest) => {
  return Fetch<ResponseWithResult<QueryOperationLogListResult>>(
    '/tools/public/operation/queryOperationLogList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/operation/queryOperationLogList') },
    },
  );
};

export type RedEnvelopeDetailRequest = {
  id?: string /*业务ID*/;
};

export type RedEnvelopeDetailResult = {
  buyReturnRedEnvelopeFlag?: boolean /*是否买返红包*/;
  fundingParty?: string /*出资方*/;
  id?: string /*场次货盘id*/;
  lossAmount?: string /*亏损金额*/;
  lossFlag?: boolean /*是否亏损*/;
  redEnvelopeAmount?: number /*红包金额*/;
  remark?: string /*备注*/;
};

/**
 *买返红包详情
 */
export const redEnvelopeDetail = (params: RedEnvelopeDetailRequest) => {
  return Fetch<ResponseWithResult<RedEnvelopeDetailResult>>(
    '/iasm/public/selection/redEnvelopeDetail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/redEnvelopeDetail') },
    },
  );
};

export type EditRedEnvelopeRequest = {
  buyReturnRedEnvelopeFlag?: boolean /*是否买返红包*/;
  fundingParty?: string /*出资方*/;
  id?: string /*场次货盘id*/;
  redEnvelopeAmount?: number /*红包金额*/;
  remark?: string /*备注*/;
};

export type EditRedEnvelopeResult = boolean;

/**
 *编辑买返红包详情
 */
export const editRedEnvelope = (params: EditRedEnvelopeRequest) => {
  return Fetch<ResponseWithResult<EditRedEnvelopeResult>>(
    '/iasm/public/selection/editRedEnvelope',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/editRedEnvelope') },
    },
  );
};

export type ListLiveRoomSortByDeptRequest = {
  deptId?: string /*事业部id*/;
  platformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*类型[PlatformEnum]*/;
  sort?: boolean /*排序 true 升序 false 降序 默认降序*/;
};

export type ListLiveRoomSortByDeptResult = Array<{
  avatar?: string /*头像*/;
  buId?: string /*事业部id*/;
  buName?: string /*事业部名称*/;
  id?: string /*id*/;
  institutionId?: string /*达人所属机构ID*/;
  name?: string /*直播间名称*/;
  openId?: string /*直播间openId*/;
  platform?: string /*平台*/;
  sortingNum?: number /*排序权重*/;
  talentId?: string /*达人id*/;
  talentNo?: string /*达人编号*/;
}>;

/**
 *获取当前事业部+平台排序的直播间列表
 */
export const listLiveRoomSortByDept = (params: ListLiveRoomSortByDeptRequest) => {
  return Fetch<ResponseWithResult<ListLiveRoomSortByDeptResult>>(
    '/iasm/public/liveRoom/listLiveRoomSortByDept',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/liveRoom/listLiveRoomSortByDept') },
    },
  );
};

export type NonLiveSelectionCreateInterestPointsRequest = {
  id?: string /*id*/;
};

export type NonLiveSelectionCreateInterestPointsResult = string;

/**
 *非直播流程-生成利益点
 */
export const nonLiveSelectionCreateInterestPoints = (
  params: NonLiveSelectionCreateInterestPointsRequest,
) => {
  return Fetch<ResponseWithResult<NonLiveSelectionCreateInterestPointsResult>>(
    '/iasm/public/nonLiveSelection/createInterestPoints',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/createInterestPoints') },
    },
  );
};

export type NonLiveSelectionEditInterestPointsRequest = {
  id?: string /*id*/;
  interestPoints?: string /*利益点内容*/;
};

export type NonLiveSelectionEditInterestPointsResult = boolean;

/**
 *非直播流程-编辑利益点
 */
export const nonLiveSelectionEditInterestPoints = (
  params: NonLiveSelectionEditInterestPointsRequest,
) => {
  return Fetch<ResponseWithResult<NonLiveSelectionEditInterestPointsResult>>(
    '/iasm/public/nonLiveSelection/editInterestPoints',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/editInterestPoints') },
    },
  );
};

export type NonLiveSelectionQueryInterestPointsRequest = {
  id?: string /*ID*/;
  taskId?: string /*任务id*/;
};

export type NonLiveSelectionQueryInterestPointsResult = boolean;

/**
 *非直播流程-根据任务id生成利益点
 */
export const nonLiveSelectionQueryInterestPoints = (
  params: NonLiveSelectionQueryInterestPointsRequest,
) => {
  return Fetch<ResponseWithResult<NonLiveSelectionQueryInterestPointsResult>>(
    '/iasm/public/nonLiveSelection/queryInterestPoints',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/queryInterestPoints') },
    },
  );
};

export type SelectionInterestPointsRequest = {
  spuId?: string /*商品ID*/;
  taskId?: string /*任务id*/;
};

export type SelectionInterestPointsResult = boolean;

/**
 *根据任务id查询利益点
 */
export const selectionInterestPoints = (params: SelectionInterestPointsRequest) => {
  return Fetch<ResponseWithResult<SelectionInterestPointsResult>>(
    '/pim/public/selectGoodsPool/interestPointsGet',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/selectGoodsPool/interestPointsGet') },
    },
  );
};
