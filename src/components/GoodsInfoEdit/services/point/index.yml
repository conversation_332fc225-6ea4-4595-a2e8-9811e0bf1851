basic:
  listAllLiveRoom: /iasm/public/web/liveRound/listAllLiveRoom
  createInterestPoints: /pim/public/selectGoodsPool/createInterestPoints
  updateInterestPointsData: /pim/public/selectGoodsPool/updateInterestPointsData
  queryOperationLogList: /tools/public/operation/queryOperationLogList
  redEnvelopeDetail: /iasm/public/selection/redEnvelopeDetail
  editRedEnvelope: /iasm/public/selection/editRedEnvelope
  listLiveRoomSortByDept: /iasm/public/liveRoom/listLiveRoomSortByDept
  nonLiveSelectionCreateInterestPoints: /iasm/public/nonLiveSelection/createInterestPoints
  nonLiveSelectionEditInterestPoints: /iasm/public/nonLiveSelection/editInterestPoints
  nonLiveSelectionQueryInterestPoints: /iasm/public/nonLiveSelection/queryInterestPoints
  selectionInterestPoints: /pim/public/selectGoodsPool/interestPointsGet

