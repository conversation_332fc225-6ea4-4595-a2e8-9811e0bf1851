.ant-drawer-body {
  padding: 8px;
}
.goodsCrads {
  width: 278px;
  font-size: 10px;
  //   font-size: 16em;
  //   width: 220px;
  padding: 0;
  .topCard {
    width: 100%;
    position: relative;
    padding-top: 100%;
    .goodsImg {
      border-radius: 12px;
      width: 278px;
      height: 278px;
      position: absolute;
      top: 0;
      left: 0;
    }
    .rightTip {
      position: absolute;
      top: -1px;
      right: -9px;
    }
  }
  .bottomCard {
    font-size: 12px;
    padding: 8px 0px 0px 0px;
    .priceRole {
      margin-bottom: 8px;
      height: 20px;
      display: flex;
      // justify-content: space-between;
      align-items: center;
      .price {
        font-weight: 500;
        font-size: 20px;
        color: #e02020;
      }
      .role {
        font-weight: 200;
        font-size: 12px;
        color: #bababb;
        line-height: 20px;
      }
      .priceLine {
        text-decoration: line-through;
        margin-left: 10px;
        color: #bababb;
        font-size: 15px;
      }
    }
    .product {
      position: relative;
      max-height: 36px;
      margin-bottom: 8px;
      //   margin-right: 40px;
      .product-name {
        font-weight: 500;
        font-size: 12px;
        color: #111111;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
      .product-name-overflow {
        font-weight: 500;
        font-size: 12px;
        color: #111111;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .copyBtnHide {
        display: none;
      }
      .copyBtn {
        position: absolute;
        bottom: 0;
        right: 0;
        padding: 0 4px;
        background-color: white;
        display: flex;
        align-items: center;
        border-radius: 5px;
      }
    }
    .tagGourp {
      margin-bottom: 10px;
      width: 100%;
      height: 15px;
      display: flex;
      //   justify-content: space-around;
      //   flex
      overflow: hidden;
      .ant-tag {
        border-radius: 8px;
        height: 15px;
        line-height: 15px;
        font-size: 12px;
        .tagText {
          text-shadow: 0px 2px 20px rgba(0, 0, 0, 0.2);
          display: inline-block;
          height: 14px;
          font-size: 12px;
          font-weight: 400;
          line-height: 14px;
        }
      }
    }
    .priceGroup {
      margin-bottom: 4px;
      width: 278px;
      padding: 4px;
      background-color: #f8f8f9;
      color: #666;
      line-height: 20px;
      .ant-row {
        display: flex;
      }
      .auditStyle {
        text-align: justify;
        margin: 2px 0;
        .leftCol {
          float: left;
        }
        .leftSTR {
          font-size: 12px;
          font-weight: 500;
          color: #444444;
          line-height: 20px;
        }
        .rightSTR {
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          line-height: 17px;
          word-break: break-all;
        }
        .rightCol {
          float: right;
          text-align: right;
          padding-right: 4px;
        }
        .auditFw {
          margin-right: 3px;
        }
      }
    }
    .iconGroup {
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      background-color: rgb(243, 241, 241);
      padding: 2px 0;
    }
  }
}
.ant-card-body {
  padding: 0;
}
.product {
  display: flex;
  align-items: center;
}
.dot {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  display: inline-block;
  margin-right: 5px;
}
