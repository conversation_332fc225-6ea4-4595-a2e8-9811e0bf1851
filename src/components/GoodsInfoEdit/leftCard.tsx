import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import {
  Card,
  Tag,
  Button,
  Row,
  Col,
  Popover,
  Icon,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Spin,
  Tooltip,
  Descriptions,
} from 'antd';
import './leftCard.less';
import {
  BpEditAndConfirmRequest,
  cascadeList,
  getServiceType,
  GetServiceTypeResult,
} from '@/services/yml/choose/index';
import { AuthWrapper, checkAuth } from 'qmkit';
import styles from './index.module.less';
import {
  lowCommissionConfigByTypeDeptId,
  LowCommissionConfigByTypeDeptIdRequest,
  LowCommissionConfigByTypeDeptIdResult,
} from '@/services/yml/low-commission-config';
import {
  LowCommissionColumInputType,
  LowCommissionColumnEnum,
  LowCommissionColumnFormNameEnum,
  LowCommissionColumSelectType,
  LowCommissionReasonTypeEnum,
} from '@/pages/setting/low-commission-type-config/utils';
import {
  existIdenticalSpuIdJudge,
  getLowCommissionDetail,
  getSelectionRoundByPlatformSpuId,
  LowCommissionJudgeResult,
} from './services/yml';
import {
  getLiveRoundList,
  GetLiveRoundListRequest,
  GetLiveRoundListResult,
} from '@/services/yml/live-room-manage';
import { getSpuSelectNum } from '@/pages/live-room-operation/live-broadcast/yml';
import NumberSessions from '@/pages/choice-list-new/components/NumberSessions';
import { LiveGoodsInfoResult } from '@/services/yml/choiceList';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import LeftCardLeftForm, { responseWithResultAsync } from './component/LeftCardLeftForm';
import moment from 'moment';
import {
  QUALIFICATION_AUDIT_STATE_COLOR,
  QUALIFICATION_AUDIT_STATE_ICON,
  RISK_LEVEL_TEXT,
  allRiskLevelColor,
} from '@/pages/audit/legal-audit-queue/utils/getRiskLevel';
import { QUALIFICATION_AUDIT_STATE, RISK_LEVEL } from '@/pages/audit/legal-audit-queue/types';
import { ApproveIconStatus } from '@/pages/choice-list-new/components/choiceListRight/List/goodsCard';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import LabelOptionFormItem from './component/LabelOptionFormItems';
import {
  GoodsAssortingListInfo,
  ExplosiveTypeAll,
  ExplosiveTypeColor,
} from '@/pages/goods-assorting/list/type';
import { nodeInfoList, NodeInfoListResult } from './services/api';
import { LEVEL_COLOR, LEVEL_NAME } from '../../../web_modules/types';
import { newRenderPlatformSourceLogo } from '@/common/constants/platform';
import {
  GOODS_GRADES_STATE,
  GOODS_GRADES_STATE_ENUM,
  GOODS_GRADES_STATE_NAME,
  GOODS_GRADES_STATE_COLOR,
  COMPLIANCESTATUS_NAME,
  COMPLIANCESTATUS_COLOR,
  COMPLIANCESTATUS_ICON,
  COMPLIANCESTATUS_COLOR_NAME,
} from '@/pages/choice-list-new/tools';
import useLowCommissionTypeConfig from '@/pages/setting/low-commission-type-config/hook';

const { Option } = Select;
const CompanyStatus = {
  WAIT_CHECK: '待认证',
  CHECKING: '认证中',
  CHECKED: '已认证',
};
export const lowCommissionReasonTypeList = [
  { label: '破价降佣', value: 'BREAK_PRICE_REDUCE_COMMISSION' },
  { label: '本场品牌费影响', value: 'BRAND_FEE_AFFECTS' },
  { label: '补播', value: 'SUPPLEMENT_LIVE' },
  { label: '保量/专场', value: 'QUANTITY_GUARANTEED' },
  {
    label: '历史商务条款提报错误',
    value: 'HIS_COMMERCIAL_CLAUSE_REPORTING_ERROR',
  },
  { label: '品牌方降佣', value: 'BRAND_REDUCE_COMMISSION' },
  { label: '框架合作', value: 'FRAMEWORK_COOPERATION' },
  { label: '福利品/货补', value: 'WELFARE_GOODS' },
  { label: '其他主播低于罗场', value: 'OTHER_LESS_THAN_LUO' },
  { label: '其他', value: 'OTHER' },
];

export const LowCommissionAuditStatusEnum = {
  REJECTED: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        驳回
      </span>
    ),
  },
  CANCELED: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>取消
      </span>
    ),
  },
  REVERTED: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>还原
      </span>
    ),
  },
  DELETED: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>删除
      </span>
    ),
  },
  PENDING: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>
        审核中
      </span>
    ),
  },
  APPROVED: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  INIT: {
    icon: (
      <Icon style={{ color: 'gray', marginRight: '5px' }} theme="outlined" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#FAAD14' }}>
        <span className="dot" style={{ backgroundColor: '#FAAD14' }}></span>
        未开始
      </span>
    ),
  },
};

export const IconStatus = {
  //通过
  PASS: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  //待审核
  INIT: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>
        待审核
      </span>
    ),
  },
  //没通过
  REJECT: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        没通过
      </span>
    ),
  },
  //没通过
  NO_PASS: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        没通过
      </span>
    ),
  },
  //未开始
  null: {
    icon: (
      <Icon style={{ color: 'gray', marginRight: '5px' }} theme="outlined" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#FAAD14' }}>
        <span className="dot" style={{ backgroundColor: '#FAAD14' }}></span>
        未开始
      </span>
    ),
  },
  //邀约中
  INVITING: {
    icon: (
      <Icon style={{ color: 'gray', marginRight: '5px' }} theme="outlined" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#FAAD14' }}>
        <span className="dot" style={{ backgroundColor: '#FAAD14' }}></span>
        未开始
      </span>
    ),
  },
  //商务审核中是通过
  WAIT_AUDIT: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  //商务待直播是通过
  WAIT_LIVE: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  //商务一通过是通过
  COMPLETED_LIVE: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  //商务已掉品是没通过
  ABORT_LIVE: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#f50' }}>
        <span className="dot" style={{ backgroundColor: '#f50' }}></span>不通过
      </span>
    ),
  },
  ABORT_WAIT_LIVE: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#f50' }}>
        <span className="dot" style={{ backgroundColor: '#f50' }}></span>不通过
      </span>
    ),
  },
  //商务已掉品是没通过
  CANCEL: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        已取消
      </span>
    ),
  },
  //商务待确认就是待审核
  BP_CONFIRMING: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>
        待审核
      </span>
    ),
  },
  // 法务高风险 没通过
  HIGH: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        已取消
      </span>
    ),
  },
  // 法务中风险就是已通过
  MIDDLE: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  // 法务低风险就是已通过
  LOW: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  // 法务待审核
  NONE: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>
        待审核
      </span>
    ),
  },
};
const LegalStaus = {
  // 法务高风险 没通过
  NO_PASS: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        没通过
      </span>
    ),
  },
  HIGH: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        高风险
      </span>
    ),
  },
  // 法务中风险就是已通过
  MIDDLE: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        中风险
      </span>
    ),
  },
  // 法务低风险就是已通过
  LOW: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        低风险
      </span>
    ),
  },
  // 法务待审核
  NONE: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>
        待审核
      </span>
    ),
  },
  // 法务待审核
  PASS: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>
        已通过
      </span>
    ),
  },
  //未开始
  null: {
    icon: (
      <Icon style={{ color: 'gray', marginRight: '5px' }} theme="outlined" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#FAAD14' }}>
        <span className="dot" style={{ backgroundColor: '#FAAD14' }}></span>
        未开始
      </span>
    ),
  },
  HIGH_SPECIAL: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#E90000' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>
        高风险-特
      </span>
    ),
  },
};
const tagColor = ['#108ee9', '#87d068', '#f50', '#2db7f5'];
enum FlowTagColor {
  '讲解' = 'green',
  '预售' = 'blue',
  '福利' = 'red',
  '现货' = 'orange',
  '直播' = 'pink',
}
function validatorTalentCommission(
  value: number,
  callback: (val?: any) => void,
  rate: number,
  platformSource: string,
  tabsValue: string,
) {
  // console.log('🚀 ~ file: CommercialCondition.tsx:24 ~ validatorTalentCommission ~ rate:', rate);
  // const maxValue = 100 - (rate || 0); // 如果是JD，则最大为80
  const maxValue = 100;
  const minValue = 0;
  const precision = platformSource === 'TB' ? 1 : 2;

  if (!value && value != 0) {
    return callback();
  }

  if (Number.isNaN(Number(value))) {
    return callback(`请输入0-${maxValue} 支持${precision}位小数`);
  }

  if (value > maxValue || value < minValue) {
    return callback(`总佣金可设范围${minValue}-${maxValue}%`);
  }
  return callback();

  // if (value > maxValue || value < 1) {
  //   return callback(`总佣金可设范围1-${maxValue}%`);
  // }
  // return callback();
}
const GoodsCard: React.FC<{
  info: LiveGoodsInfoResult;
  getlowCommissionJudge: (params?: {
    initCommissionRate?: string;
    frameworkRoundFlag?: string;
  }) => void;
  form: WrappedFormUtils;
  entry: 'changci' | 'xuanpin';
  type?: 'edit' | 'info';
}> = (props) => {
  const {
    info,
    type,
    form,
    cascadeArr,
    isSupplier,
    entry,
    liveRoundId,
    lowCommissionInfo,
    childMsg,
    getlowCommissionJudge,
    tabsValue,
  } = props;
  const { getFieldDecorator, setFieldsValue, getFieldsValue } = form;
  const [option, setOptions] = useState([]);
  const [goodsInfo, setGoodsInfo] = useState({});
  const [labelArr, setlabelArr] = useState([]);
  const labelObj = useRef({});
  const [childrenMsg, setChildMsg] = useState<GetServiceTypeResult>();
  const [editTotalCommission, setEditTotalCommission] = useState(0);
  const { list: lowCommissionTypeList, loading: lowCommissionTypeLoading } =
    useLowCommissionTypeConfig();
  // 低佣生效期限数据字典
  const { codeList: LOW_COMMISSION_PERIOD_LIST } = useCode(CODE_ENUM.VALIDITY_PERIOD, {
    able: true,
  });

  useEffect(() => {
    labelObj.current = {};
    const arr: Array<any> = [];
    info.labelList?.forEach((i) => {
      labelObj.current[i.id] = [];
      i.labelOptionList?.forEach((t) => {
        arr.push(t.labelOption);
        labelObj.current[i.id].push(t.labelOption);
      });
    });
    // console.log(labelObj.current);
    setlabelArr(arr);
    getServiceType({ liveRoundId: liveRoundId }).then((res) => {
      if (res?.res?.code === '200') {
        setChildMsg(res?.res?.result);
      }
    });
  }, [info]);
  const [lowCommissionConfig, setLowCommissionConfig] =
    useState<LowCommissionConfigByTypeDeptIdResult>();
  const [lowReason, setLowReason] =
    useState<NonNullable<BpEditAndConfirmRequest['lowCommissionRequest']>['reasonType']>();
  const [brandFee, setBrandFee] = useState<number>();
  // 控制低佣生效期限字段显示
  const [showPeriodField, setShowPeriodField] = useState<boolean>(false);
  const handleGetConfig = async (params: LowCommissionConfigByTypeDeptIdRequest) => {
    try {
      const { res } = await lowCommissionConfigByTypeDeptId(params);
      if (res.code !== '200') {
        message.error(res.message || '网络错误');
        return;
      }
      const { result } = res;
      setLowCommissionConfig(result);
      setTimeout(() => {
        setIsShow(true);
      });
    } finally {
    }
  };
  // 低佣详细原因和低佣原因回显
  const setLowCommissionsReasonDetail = async (type?: boolean) => {
    const result = await responseWithResultAsync({
      request: getLowCommissionDetail,
      params: { deptId: info?.deptId, platformSpuId: info?.platformSpuId },
    });
    setFieldsValue({
      reasonDetail: result?.reasonDetail,
      ...(type ? { reasonType: result?.reasonType } : {}),
    });
    if (type && result?.reasonType) {
      handleReasonChange(result?.reasonType);
    }
  };
  useEffect(() => {
    type === 'edit' &&
      entry === 'changci' &&
      lowCommissionConfig &&
      setLowCommissionsReasonDetail();
    // setFieldsValue({
    //   reasonDetail: info?.reasonDetail,
    //   ...(type ? { reasonType: info?.reasonType } : {}),
    // });
  }, [lowCommissionConfig]);

  // 监听 lowReason、lowCommissionTypeList 和 lowCommissionTypeLoading 的变化，处理 showPeriodField 的设置
  useEffect(() => {
    // 只有当数据加载完成且有选中的低佣原因时才处理
    if (!lowCommissionTypeLoading && lowCommissionTypeList && lowReason && info?.deptId) {
      const selectedConfig = lowCommissionTypeList?.find(
        (item) => item?.type === lowReason && item?.deptId === info?.deptId,
      );
      const newShowPeriodField = selectedConfig?.needAudit ?? false;

      // 如果生效期限字段显示条件发生改变，清空该字段的值
      if (showPeriodField !== newShowPeriodField) {
        form.setFieldsValue({
          expireDays: null,
        });
      }

      setShowPeriodField(newShowPeriodField);
    }
  }, [lowReason, lowCommissionTypeList, lowCommissionTypeLoading, info?.deptId]);

  const [isShow, setIsShow] = useState(false);
  const handleReasonChange = (value: LowCommissionConfigByTypeDeptIdRequest['type']) => {
    setIsShow(false);
    setLowReason(value);
    handleGetConfig({ type: value, deptId: info?.deptId });
  };
  const [existIdenticalSpuIdJudgeLoading, setExistIdenticalSpuIdJudgeLoading] = useState(false);
  const handleExistIdenticalSpuIdJudge = async (value: { key: string; label: any }) => {
    setExistIdenticalSpuIdJudgeLoading(true);
    try {
      if (lowReason === 'SUPPLEMENT_LIVE') {
        const { res: feeRes } = await getSelectionRoundByPlatformSpuId({
          liveRoundId: value.key,
          platformSpuId: info?.platformSpuId as string,
        });
        if (feeRes.code !== '200') {
          message.error(feeRes.message || '网络错误');
        } else {
          const { result: feeResult } = feeRes;
          setBrandFee(feeResult.brandFee);
          form.setFieldsValue({
            supplementLiveRoundBrandFee: feeResult.brandFee,
          });
        }
      }
      const { res } = await existIdenticalSpuIdJudge({
        selectionNo: info?.no!,
        liveRoundId: value.key,
      });
      if (res.code !== '200') {
        message.error(res.message || '网络错误');
        return;
      }
      const { result } = res;
      if (!result.isExist) {
        message.warning(
          `该场次系统中未检测出有${result.spuName}商品，请确认是否真实上播过，如仍要选择请自行承担风险`,
        );
      }
    } finally {
      setExistIdenticalSpuIdJudgeLoading(false);
    }
  };
  const handleSetEditTotalCommission = async () => {
    const values = getFieldsValue();
    const totalCommission =
      ((values.commissionRate ?? 0) + (values.commissionRateOffline ?? 0)) / 100;
    setEditTotalCommission(totalCommission);
    getlowCommissionJudge({
      initCommissionRate: totalCommission,
      frameworkRoundFlag: info?.guaranteeQuantityFlag,
    });
  };
  const showTotalCommission = useMemo(() => {
    const frameworkRoundFlag = form?.getFieldsValue()?.frameworkRoundFlag;
    if (type === 'edit') {
      return `${Math.round(editTotalCommission * 10000) / 100}%`;
    }
    {
      return `${Math.round(info?.totalCommissionContainGuaranteed * 10000) / 100}%`;
    }
  }, [
    type,
    editTotalCommission,
    info?.totalCommissionContainGuaranteed,
    form?.getFieldsValue()?.frameworkRoundFlag,
  ]);
  useEffect(() => {
    if (lowCommissionInfo?.isLowCommission) {
      const obj = { ...lowCommissionInfo?.highCommissionModel };
      delete obj?.reasonDetail;
      setFieldsValue(obj ?? {});
      // type === 'edit' && setLowCommissionsReasonDetail(true);
    }
  }, [lowCommissionInfo?.isLowCommission]);
  useEffect(() => {
    type === 'edit' && handleSetEditTotalCommission();
    type === 'edit' && setLowCommissionsReasonDetail(true);
  }, [type]);
  const Status = useCallback(() => {
    const {
      riskLevel,
      brandQualificationAuditState,
      spuQualificationAuditState,
      supplierQualificationAuditState,
    } = info?.qualificationAuditLatestVO ?? {};
    return (
      <div className={styles['table-level']}>
        <div className={styles['level-box']}>
          <div className={styles['level-box-item']}>
            <Icon
              style={{
                color:
                  QUALIFICATION_AUDIT_STATE_COLOR[
                    supplierQualificationAuditState as QUALIFICATION_AUDIT_STATE
                  ],
              }}
              className={styles['icon']}
              type={
                QUALIFICATION_AUDIT_STATE_ICON[
                  supplierQualificationAuditState as QUALIFICATION_AUDIT_STATE
                ]
              }
              theme="filled"
            />
            <span>商家</span>
          </div>
          <div className={styles['level-box-item']}>
            <Icon
              style={{
                color:
                  QUALIFICATION_AUDIT_STATE_COLOR[
                    brandQualificationAuditState as QUALIFICATION_AUDIT_STATE
                  ],
              }}
              className={styles['icon']}
              type={
                QUALIFICATION_AUDIT_STATE_ICON[
                  brandQualificationAuditState as QUALIFICATION_AUDIT_STATE
                ]
              }
              theme="filled"
            />
            <span>品牌</span>
          </div>
        </div>
        <div className={styles['level-box']}>
          <div className={styles['level-box-item']}>
            <Icon
              style={{
                color:
                  QUALIFICATION_AUDIT_STATE_COLOR[
                    spuQualificationAuditState as QUALIFICATION_AUDIT_STATE
                  ],
              }}
              className={styles['icon']}
              type={
                QUALIFICATION_AUDIT_STATE_ICON[
                  spuQualificationAuditState as QUALIFICATION_AUDIT_STATE
                ]
              }
              theme="filled"
            />
            <span>商品</span>
          </div>
        </div>
        <p style={{ color: LEVEL_COLOR[riskLevel as 'NONE'], marginRight: '8px' }}>
          {LEVEL_NAME[riskLevel as 'NONE'] || '-'}
        </p>
      </div>
    );
  }, [info]);
  const [nodeSepList, setSepNodeList] = useState<NodeInfoListResult>();
  const [nodeBodyList, setBodyNodeList] = useState<NodeInfoListResult>();
  const [nodeLowList, setLowNodeList] = useState<NodeInfoListResult>();
  const getNodeList = async (type: 'body' | 'sep' | 'low', requestId?: string) => {
    const result = await responseWithResultAsync({
      request: nodeInfoList,
      params: { requestId },
    });
    if (type === 'body') {
      setBodyNodeList(result?.filter((item) => [0, 1, 2].includes(item.isRemark as number)) ?? []);
    }
    if (type === 'sep') {
      setSepNodeList(result?.filter((item) => [0, 1, 2].includes(item.isRemark as number)) ?? []);
    }
    if (type === 'low') {
      setLowNodeList(result?.filter((item) => [0, 1, 2].includes(item.isRemark as number)) ?? []);
    }
  };
  useEffect(() => {
    if (
      !!info?.specialAuditProcessUid &&
      !isNaN(Number(info.specialAuditProcessUid)) &&
      info?.specialAuditStatus === 'CONFIRMING'
    ) {
      getNodeList('sep', info?.specialAuditProcessUid);
    }
    if (
      !!info?.supplierBodySpecialProcessUid &&
      !isNaN(Number(info.supplierBodySpecialProcessUid)) &&
      info?.supplierBodySpecialAuditStatus === 'CONFIRMING'
    ) {
      getNodeList('body', info?.supplierBodySpecialProcessUid);
    }
    if (
      !!info?.lowCommissionUniqueCode &&
      !isNaN(Number(info.lowCommissionUniqueCode)) &&
      info?.lowCommissionAuditStatus === 'PENDING'
    ) {
      getNodeList('low', info?.lowCommissionUniqueCode);
    }
  }, [
    info?.specialAuditProcessUid,
    info?.supplierBodySpecialProcessUid,
    info?.lowCommissionUniqueCode,
  ]);

  const [liveServiceTypeName, setLiveServiceTypeName] = useState<string>(info?.liveServiceType);
  return (
    <div className={`${styles.leftForm} +' left'`}>
      <div className="goodsCrads">
        <div className="topCard">
          <img src={info?.image} alt="" className="goodsImg" />
        </div>
        <div className="bottomCard">
          <div className="priceRole">
            {entry === 'xuanpin' ? (
              <span className="price">
                {`${
                  info?.minSalePrice === info?.maxSalePrice
                    ? '¥' + info?.minSalePrice
                    : '¥' + info?.minSalePrice + '-' + info?.maxSalePrice
                }`}{' '}
                {/* <span className="priceLine">{`${
                  info?.minPrice === info?.maxPrice
                    ? '¥' + info?.minPrice
                    : '¥' + info?.minPrice + '-' + info?.maxPrice
                }`}</span> */}
              </span>
            ) : (
              <span className="price">
                {tabsValue === 'live' ? (
                  <>
                    {`${
                      info?.liveMinPrice === info?.liveMaxPrice
                        ? '¥' + info?.liveMinPrice
                        : '¥' + info?.liveMinPrice + '-' + info?.liveMaxPrice
                    }`}{' '}
                    <span className="priceLine">{`${
                      info?.minPrice === info?.maxPrice
                        ? '¥' + info?.minPrice
                        : '¥' + info?.minPrice + '-' + info?.maxPrice
                    }`}</span>
                  </>
                ) : (
                  <>{`${
                    info?.minPrice === info?.maxPrice
                      ? '¥' + info?.minPrice
                      : '¥' + info?.minPrice + '-' + info?.maxPrice
                  }`}</>
                )}
              </span>
            )}
          </div>
          {/* 商品标题 */}
          <div className="product" style={{ display: 'flex', height: '32px' }}>
            <Popover content={info?.spuName || '-'}>
              <div className="product-name-overflow">{info?.spuName}</div>
            </Popover>

            {info?.explosiveType ? (
              <Tag
                style={{
                  marginLeft: '8px',
                  lineHeight: '18px',
                  borderRadius: '9px',
                  position: 'relative',
                  top: '-2px',
                }}
                color={ExplosiveTypeColor[info?.explosiveType]}
              >
                {ExplosiveTypeAll[info?.explosiveType]}
              </Tag>
            ) : (
              <></>
            )}
            {info?.luckyProductFlag && entry === 'xuanpin' ? (
              <Tag
                style={{
                  background: '#a5e1a4',
                  color: '#4f8644',
                  borderColor: '#4f8644',
                  marginLeft: '4px',
                  borderRadius: '9px',
                }}
              >
                福袋
              </Tag>
            ) : (
              <></>
            )}
          </div>
          {entry === 'changci' && (
            <div style={{ margin: '-6px 0 12px 0' }}>
              {newRenderPlatformSourceLogo({
                platform: info?.platformSource as any,
                width: 14,
              })}
              <span style={{ marginLeft: '4px' }}>{info?.liveRoundInfo?.liveRoundName}</span>
            </div>
          )}
          {/* //直播服务类型  标签列表 */}
          {type === 'edit' ? (
            <div style={{ marginLeft: 8 }}>
              {!!childrenMsg && (
                <Form.Item label="直播服务类型">
                  {getFieldDecorator('liveServiceTypeId', {
                    initialValue: childrenMsg?.serviceTypeOptions?.find(
                      (item) => item.id == info?.liveServiceTypeId,
                    )
                      ? info?.liveServiceTypeId
                      : info?.liveServiceType,
                    rules: [
                      {
                        required: !!childMsg?.required,
                        message: '请选择直播服务类型',
                      },
                    ],
                  })(
                    <Select
                      style={{ width: 200 }}
                      value={info?.liveServiceType}
                      onChange={(value, option) => {
                        const { props } = option || {};
                        const name = props?.children;
                        console.log('🚀 ~ name:', name);
                        setLiveServiceTypeName(name);
                      }}
                    >
                      {childrenMsg?.serviceTypeOptions?.map((item) => {
                        return (
                          <Option value={item.id} key={item.id}>
                            {item.name}
                          </Option>
                        );
                      })}
                    </Select>,
                  )}
                </Form.Item>
              )}
              {['预售挂链', '预售讲解', '预热挂链', '预热讲解'].includes(liveServiceTypeName) ? (
                <Form.Item label="定金金额">
                  {getFieldDecorator('depositAmount', {
                    initialValue: info?.depositAmount,
                    rules: [{ required: true, message: '定金金额' }],
                  })(
                    <InputNumber
                      min={0}
                      max={9999999.99}
                      placeholder="定金金额"
                      formatter={(value) => `¥${value}`}
                      parser={(value) => value!.replace('¥', '')}
                      precision={2}
                    />,
                  )}
                </Form.Item>
              ) : (
                <></>
              )}

              {cascadeArr?.map((i) => {
                return (
                  // <div key={i.id}>
                  <Form.Item label={i.labelGroupName}>
                    {getFieldDecorator(String(i.id), {
                      initialValue:
                        i.chooseMethod === 'RADIO'
                          ? labelObj.current?.[i.id]?.[0]
                          : labelObj.current?.[i.id],
                      rules: [
                        {
                          required: i.requiredFlag === 1 ? true : false,
                          message: '请选择标签',
                        },
                      ],
                    })(
                      <Select
                        mode={i.chooseMethod === 'RADIO' ? '' : 'multiple'}
                        style={{ width: 200 }}
                      >
                        {i?.labelOptionList?.map((t) => {
                          return (
                            <Option key={t.labelOption} value={t.labelOption}>
                              {t.labelOption}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                  // <br />
                  // </div>
                );
              })}
            </div>
          ) : entry === 'xuanpin' ? null : (
            labelArr?.length > 0 && (
              <div className="tagGourp">
                {labelArr?.map((i, index) => {
                  <Tag color={FlowTagColor[i]} key={index}>
                    <span className="tagText">{i}</span>
                  </Tag>;
                })}
              </div>
            )
          )}
          {type === 'edit' ? (
            <div style={{ marginLeft: 8 }}>
              <LabelOptionFormItem
                form={form}
                deptId={info?.deptId}
                liveRoomId={info?.liveRoundInfo?.liveRoomId}
                type={type}
                info={info}
              />
            </div>
          ) : null}
          {tabsValue === 'live' ? (
            <>
              <div className={styles['priceGroup-box'] + ' priceGroup'}>
                {entry === 'xuanpin' ? (
                  <Row className="auditStyle">
                    <Col>
                      <span className="leftSTR">总佣金：</span>
                      <span className="rightSTR">
                        {info?.hideCommissionFlag ? (
                          ' *** %'
                        ) : (
                          <>{Math.round(info?.totalCommission * 10000) / 100}%</>
                        )}
                      </span>
                    </Col>
                  </Row>
                ) : (
                  <Row className="auditStyle">
                    {entry === 'xuanpin' ? (
                      <Col>
                        <span className="leftSTR">总佣金：</span>
                        <span className="rightSTR">
                          {info?.hideCommissionFlag ? (
                            ' *** %'
                          ) : (
                            <>{Math.round(info?.totalCommission * 10000) / 100}%</>
                          )}
                        </span>
                      </Col>
                    ) : (
                      <Col>
                        <span className="leftSTR">总佣金(含保量)：</span>
                        <span className="rightSTR">
                          {info?.hideCommissionFlag ? ' *** %' : <>{showTotalCommission}</>}
                        </span>
                        {(lowCommissionInfo?.isLowCommission || !!info?.suggestCommission) && (
                          <>
                            {info?.status === 'BP_CONFIRMING' || info?.status === 'CANCEL' ? (
                              <span style={{ color: '#ee0000', marginLeft: '8px' }}>
                                建议总佣金比例
                                {Math.round(
                                  (info?.suggestCommission
                                    ? Number(info?.suggestCommission)
                                    : lowCommissionInfo?.highCommissionModel
                                        ?.highTotalCommissionRate) * 10000,
                                ) / 100}
                                %
                              </span>
                            ) : (
                              <Popover
                                title="低佣信息"
                                content={
                                  <section style={{ minWidth: '100px', maxWidth: '300px' }}>
                                    <div>
                                      <span>低佣原因：</span>
                                      <span>
                                        {info?.reasonType
                                          ? LowCommissionReasonTypeEnum[info.reasonType]
                                          : '-'}
                                      </span>
                                    </div>
                                    <div>
                                      <span>详细原因：</span>
                                      <span>{info?.reasonDetail ?? '-'}</span>
                                    </div>
                                  </section>
                                }
                              >
                                <span style={{ color: '#ee0000', marginLeft: '8px' }}>
                                  建议总佣金比例
                                  {Math.round(
                                    (info?.suggestCommission
                                      ? Number(info?.suggestCommission)
                                      : lowCommissionInfo?.highCommissionModel
                                          ?.highTotalCommissionRate) * 10000,
                                  ) / 100}
                                  %
                                </span>
                              </Popover>
                            )}
                          </>
                        )}
                      </Col>
                    )}
                  </Row>
                )}
                <Row className="auditStyle">
                  <Col>
                    <span className="leftSTR">线上佣金：</span>{' '}
                    {type === 'edit' ? (
                      <Form.Item>
                        {getFieldDecorator('commissionRate', {
                          initialValue: Math.round(info?.commissionRate * 10000) / 100,
                          rules: [
                            { required: true, message: '线上佣金' },
                            {
                              validator: (_, value, callback) => {
                                return validatorTalentCommission(
                                  value,
                                  callback,
                                  0,
                                  info?.platformSource,
                                );
                              },
                            },
                          ],
                        })(
                          <InputNumber
                            formatter={(value) => `${value}%`}
                            parser={(value) => value.replace('%', '')}
                            placeholder="%"
                            precision={2}
                            step={0.01}
                            style={{
                              width: '190px',
                              marginTop: 4,
                              marginBottom: 8,
                            }}
                            onBlur={handleSetEditTotalCommission}
                            disabled={
                              !checkAuth('f_choose_bp_edit') ||
                              !checkAuth('f_choose_bp_mechanism_edit')
                            }
                          />,
                        )}
                      </Form.Item>
                    ) : (
                      <span className="rightSTR">
                        {info?.hideCommissionFlag ? (
                          ' *** %'
                        ) : (
                          <>{Math.round(info?.commissionRate * 10000) / 100 + '%'}</>
                        )}
                      </span>
                    )}
                  </Col>
                </Row>

                {entry === 'xuanpin' ? (
                  <Row className="auditStyle">
                    <Col>
                      <span className="leftSTR">线下佣金：</span>
                      <span className="rightSTR">
                        {info?.hideCommissionFlag ? ( // 根据返回字段判断有无查看权限
                          ' *** %'
                        ) : (
                          <>{Math.round(info?.commissionRateOffline * 10000) / 100 + '%'}</>
                        )}
                      </span>
                    </Col>
                  </Row>
                ) : (
                  <Row className="auditStyle">
                    <Col>
                      <span className="leftSTR">线下佣金：</span>
                      {type === 'edit' ? (
                        <Form.Item>
                          {getFieldDecorator('commissionRateOffline', {
                            initialValue: Math.round(info?.commissionRateOffline * 10000) / 100,
                            rules: [
                              { required: true, message: '线下佣金' },
                              {
                                validator: (_, value, callback) => {
                                  return validatorTalentCommission(
                                    value,
                                    callback,
                                    0,
                                    info?.platformSource,
                                  );
                                },
                              },
                            ],
                          })(
                            <InputNumber
                              formatter={(value) => `${value}%`}
                              parser={(value) => value.replace('%', '')}
                              placeholder="%"
                              precision={2}
                              step={0.01}
                              style={{
                                width: '190px',
                                marginTop: 4,
                                marginBottom: 8,
                              }}
                              onBlur={handleSetEditTotalCommission}
                            />,
                          )}
                        </Form.Item>
                      ) : (
                        <span className="rightSTR">
                          {info?.hideCommissionFlag ? (
                            ' *** %'
                          ) : (
                            <>{Math.round(info?.commissionRateOffline * 10000) / 100 + '%'}</>
                          )}
                        </span>
                      )}
                    </Col>
                  </Row>
                )}
                {entry === 'changci' ? (
                  <Row className="auditStyle">
                    <Col>
                      <span className="leftSTR">保量基础佣金：</span>
                      <span className="rightSTR">
                        {info?.hideCommissionFlag ? ( // 根绝返回字段判断有无查看权限
                          ' *** %'
                        ) : (
                          <>{info?.guaranteeBrandFeeRate || '-'}</>
                        )}
                      </span>
                    </Col>
                  </Row>
                ) : (
                  <></>
                )}

                <Row className="auditStyle">
                  <Col>
                    <span className="leftSTR">基础服务费：</span>
                    {type === 'edit' ? (
                      <Form.Item>
                        {getFieldDecorator('brandFee', {
                          initialValue: info?.brandFee || 0,
                          rules: [{ required: false, message: '基础服务费' }],
                        })(
                          <InputNumber
                            min={0}
                            max={9999999.99}
                            formatter={(value) => `¥${value}`}
                            parser={(value) => value.replace('¥', '')}
                            placeholder="基础服务费"
                            // maxLength={11}
                            style={{
                              width: '190px',
                              marginTop: 4,
                              marginBottom: 8,
                            }}
                            disabled={
                              !checkAuth('f_choose_bp_edit') ||
                              !checkAuth('f_choose_bp_mechanism_edit')
                            }
                          />,
                        )}
                      </Form.Item>
                    ) : (
                      <span className="rightSTR">
                        {info?.hideCommissionFlag ? ' *** ' : <>￥{info?.brandFee || 0}</>}
                      </span>
                    )}
                  </Col>
                </Row>
                {entry === 'xuanpin' ? null : (
                  <Row className="auditStyle">
                    <Col>
                      <span className="leftSTR">切片费：</span>
                      {type === 'edit' ? (
                        <Form.Item>
                          {getFieldDecorator('sectionFee', {
                            initialValue: info?.sectionFee || 0,
                            rules: [{ required: false, message: '切片费' }],
                          })(
                            <InputNumber
                              min={0}
                              max={9999999.99}
                              formatter={(value) => `¥${value}`}
                              parser={(value) => value!.replace('¥', '')}
                              placeholder="切片费"
                              // maxLength={11}
                              style={{
                                width: '190px',
                                marginTop: 4,
                                marginBottom: 8,
                              }}
                            />,
                          )}
                        </Form.Item>
                      ) : (
                        <span className="rightSTR">
                          {info?.hideCommissionFlag ? ' *** ' : <>￥{info?.sectionFee || 0}</>}
                        </span>
                      )}
                    </Col>
                  </Row>
                )}
                {entry === 'xuanpin' ? null : (
                  <Row className="auditStyle">
                    <Col>
                      <span className="leftSTR">资源位费用：</span>
                      {type === 'edit' ? (
                        <Form.Item>
                          {getFieldDecorator('resourceBitCost', {
                            initialValue: info?.resourceBitCost || 0,
                            // rules: [{ required: false, message: '资源位费用' }],
                          })(
                            <InputNumber
                              min={0}
                              max={9999999.99}
                              formatter={(value) => `¥${value}`}
                              parser={(value) => value!.replace('¥', '')}
                              placeholder="资源位费用"
                              precision={2}
                              // maxLength={11}
                              style={{
                                width: '190px',
                                marginTop: 4,
                                marginBottom: 8,
                              }}
                            />,
                          )}
                        </Form.Item>
                      ) : (
                        <span className="rightSTR">{<>￥{info?.resourceBitCost || 0}</>}</span>
                      )}
                    </Col>
                  </Row>
                )}
                {type !== 'edit' && entry === 'changci' && (
                  <Row className="auditStyle">
                    <Col>
                      <span className="leftSTR">直播服务类型：</span>
                      <span className="rightSTR">{info?.liveServiceType ?? '-'}</span>
                    </Col>
                  </Row>
                )}

                {type !== 'edit' &&
                  entry === 'changci' &&
                  info?.depositAmount !== null &&
                  info?.depositAmount !== undefined && (
                    <Row className="auditStyle">
                      <Col>
                        <span className="leftSTR">定金金额：</span>
                        <span className="rightSTR">￥{info?.depositAmount}</span>
                      </Col>
                    </Row>
                  )}
                {type !== 'edit' && entry === 'changci' && (
                  <>
                    {info?.labelConfigInfo?.map((item) => (
                      <Row className="auditStyle">
                        <Col>
                          <span className="leftSTR">{item?.labelName}：</span>
                          <span className="rightSTR">{item?.labelOptionName ?? '-'}</span>
                        </Col>
                      </Row>
                    ))}
                  </>
                )}
                <LeftCardLeftForm
                  form={form}
                  entry={entry}
                  type={type}
                  info={info}
                  getlowCommissionJudge={getlowCommissionJudge}
                />
              </div>
              {lowCommissionInfo?.isLowCommission && entry !== 'xuanpin' && type === 'edit' && (
                <section style={{ marginTop: '5px', paddingBottom: '20px' }}>
                  <article
                    style={{
                      borderRadius: '10px',
                      border: '1px solid #ccc',
                      padding: '5px',
                    }}
                  >
                    <p style={{ color: '#ee0000' }}>低佣提示：</p>
                    <p>
                      {'当前商品佣金低于【' +
                        (lowCommissionInfo?.highCommissionModel?.highAnchorName ?? '-') +
                        '】商务「' +
                        (lowCommissionInfo?.highCommissionModel?.highBpName ?? '-') +
                        '」在【' +
                        `${
                          lowCommissionInfo?.highCommissionModel?.highLiveDate
                            ? moment(lowCommissionInfo?.highCommissionModel?.highLiveDate).format(
                                'YYYY-MM-DD',
                              )
                            : '-'
                        }` +
                        '】上播的佣金比例，该上播商品总佣金比例为【' +
                        Math.round(
                          lowCommissionInfo?.highCommissionModel?.highTotalCommissionRate * 10000,
                        ) /
                          100 +
                        '%】。请联系商家重新沟通商务机制，并按照建议佣金比例提报商品。' +
                        '如坚持按照当前商务条件上播，请填写【低佣原因】及对应字段。发起选品流程后会自动提交飞书流程供对应商务负责人审核。'}
                    </p>
                  </article>
                  <Spin spinning={existIdenticalSpuIdJudgeLoading}>
                    <div style={{ marginLeft: 8 }}>
                      <Form.Item
                        label={
                          <span>
                            <span>低佣原因</span>
                            <Popover content="无法修改字段可在详细原因中补充">
                              <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                            </Popover>
                          </span>
                        }
                        required={true}
                      >
                        {getFieldDecorator('reasonType', {
                          initialValue: lowCommissionConfig?.type,
                          rules: [{ required: true, message: '低佣原因' }],
                        })(
                          <Select
                            placeholder="请选择"
                            allowClear
                            style={{ width: '190px' }}
                            onChange={handleReasonChange}
                            loading={lowCommissionTypeLoading}
                          >
                            {lowCommissionReasonTypeList
                              ?.filter((item) =>
                                lowCommissionTypeList
                                  ?.filter((typeItem) => typeItem?.deptId === info?.deptId)
                                  ?.find((typeItem) => typeItem?.type === item?.value),
                              )
                              ?.map((item) => (
                                <Select.Option key={item.value} value={item.value}>
                                  {item.label}
                                </Select.Option>
                              ))}
                          </Select>,
                        )}
                      </Form.Item>
                      {showPeriodField && (
                        <Form.Item
                          label={
                            <span>
                              <span>生效期限</span>
                              <Popover content="因品牌费、保量等需长期履约导致的低佣,发起审批时可选择生效期限,审批通过后,生效期限内无需重复发起。">
                                <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                              </Popover>
                            </span>
                          }
                          required={true}
                        >
                          {getFieldDecorator('expireDays', {
                            initialValue: info?.expireDays,
                            rules: [{ required: true, message: '生效期限' }],
                          })(
                            <Select placeholder="请选择" allowClear style={{ width: '190px' }}>
                              {LOW_COMMISSION_PERIOD_LIST?.map((i, index) => {
                                return (
                                  <Select.Option value={i?.value} key={index}>
                                    {i?.label}
                                  </Select.Option>
                                );
                              })}
                            </Select>,
                          )}
                        </Form.Item>
                      )}
                      {lowCommissionConfig?.content?.map((item) => {
                        return item.column ? (
                          LowCommissionColumInputType.includes(item.column) ? (
                            <Form.Item label={LowCommissionColumnEnum[item.column]} required={true}>
                              {getFieldDecorator(LowCommissionColumnFormNameEnum[item.column], {
                                rules: [
                                  {
                                    required: true,
                                    message: LowCommissionColumnEnum[item.column],
                                  },
                                ],
                              })(
                                item.column === 'SUPPLEMENT_LIVE_ROUND_BRAND_FEE' &&
                                  lowReason === 'SUPPLEMENT_LIVE' ? (
                                  <p>{brandFee ?? '-'}</p>
                                ) : item.column !== 'SUPPLEMENT_LIVE_ROUND_ID' ? (
                                  <InputNumber
                                    formatter={(value) => `¥${value}`}
                                    parser={(value) => value!.replace('¥', '')}
                                    maxLength={11}
                                    style={{ width: '190px' }}
                                  />
                                ) : (
                                  <Input placeholder="请输入" style={{ width: '190px' }} />
                                ),
                              )}
                            </Form.Item>
                          ) : LowCommissionColumSelectType.includes(item.column) ? (
                            isShow && (
                              <Form.Item
                                label={LowCommissionColumnEnum[item.column]}
                                required={true}
                              >
                                {getFieldDecorator(LowCommissionColumnFormNameEnum[item.column], {
                                  rules: [
                                    {
                                      required: true,
                                      message: LowCommissionColumnEnum[item.column],
                                    },
                                  ],
                                })(
                                  <NumberSessions
                                    noMultiple={true}
                                    style={{ width: '190px' }}
                                    liveRoomOpenId={info?.liveRoundInfo?.liveRoomId}
                                    onChange={handleExistIdenticalSpuIdJudge}
                                    isFilter={true}
                                    isHover={true}
                                    allPart={true}
                                  ></NumberSessions>,
                                )}
                              </Form.Item>
                            )
                          ) : item.column === 'HIGH_BRAND_FEE' ||
                            item.column === 'HIGH_PAY_GMV' ||
                            item.column === 'HIGH_SALE_PRICE' ? (
                            <Form.Item label={LowCommissionColumnEnum[item.column]} required={true}>
                              {getFieldDecorator(
                                LowCommissionColumnFormNameEnum[item.column],
                                {},
                              )(
                                <p style={{ width: '190px' }}>
                                  {item.column === 'HIGH_PAY_GMV'
                                    ? lowCommissionInfo?.highCommissionModel?.highPayGmv ?? '-'
                                    : item.column === 'HIGH_SALE_PRICE'
                                    ? lowCommissionInfo?.highCommissionModel?.highSalePrice ?? '-'
                                    : lowCommissionInfo?.highCommissionModel?.highBrandFee ?? '-'}
                                </p>,
                              )}
                            </Form.Item>
                          ) : (
                            <Form.Item
                              label={
                                <span>
                                  <span>{LowCommissionColumnEnum[item.column]}</span>
                                  {lowCommissionConfig?.exampleContent && (
                                    <Popover
                                      content={
                                        <div style={{ maxWidth: '300px' }}>
                                          {lowCommissionConfig?.exampleContent}
                                        </div>
                                      }
                                    >
                                      <Icon type="question-circle" style={{ marginLeft: '2px' }} />
                                    </Popover>
                                  )}
                                </span>
                              }
                              required={true}
                            >
                              {getFieldDecorator(LowCommissionColumnFormNameEnum[item.column], {
                                initialValue: lowCommissionConfig.exampleContent,
                                rules: [
                                  {
                                    required: true,
                                    message: LowCommissionColumnEnum[item.column],
                                  },
                                ],
                              })(
                                <Input.TextArea
                                  placeholder="请输入"
                                  style={{ width: '190px' }}
                                  maxLength={500}
                                />,
                              )}
                            </Form.Item>
                          )
                        ) : null;
                      })}
                    </div>
                  </Spin>
                  {/* <button onClick={() => {
                console.log(form.getFieldsValue())
              }}>click</button> */}
                </section>
              )}
              {type === 'edit' || isSupplier ? (
                ''
              ) : (
                <div className="priceGroup" style={{ backgroundColor: '#F8F8F9' }}>
                  {entry === 'xuanpin' ? (
                    <Row className="auditStyle">
                      <Col span={24}>
                        <span className="leftSTR">事业部：</span>
                        <span className="rightSTR">{info?.deptName}</span>
                      </Col>
                    </Row>
                  ) : null}
                  {/* 高风险-特 */}
                  {entry === 'xuanpin' || info?.specialAuditType !== 'HIGH_SPECIAL' ? (
                    <Row className="auditStyle">
                      <Col span={24}>
                        <span className="leftSTR">项目组：</span>
                        <span className="rightSTR">{info?.groupName}</span>
                      </Col>
                    </Row>
                  ) : (
                    <Row className="auditStyle">
                      <Col span={16}>
                        <span className="leftSTR">项目组：</span>
                        <span className="rightSTR">{info?.groupName}</span>
                      </Col>
                      <Col span={8}>
                        <Tag
                          color="#FFFBE6"
                          className="rightCol"
                          style={{ marginRight: '0px', borderColor: '#FAAD14' }}
                        >
                          <span style={{ color: '#FAAD14', marginRight: '0px' }}>高风险-特</span>
                        </Tag>
                      </Col>
                    </Row>
                  )}

                  <Row className="auditStyle">
                    <Col span={12} className="leftCol" style={{ display: 'flex' }}>
                      <span className="leftSTR">商务：</span>
                      {entry === 'xuanpin' ? (
                        <Popover
                          title="商务"
                          content={<>{info?.bpNames?.length ? info?.bpNames?.join('/') : '-'}</>}
                        >
                          <span
                            className="rightSTR"
                            style={{
                              width: '90px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              wordBreak: 'break-all',
                              display: 'inline-block',
                            }}
                          >
                            {info?.bpNames?.length ? info?.bpNames?.join('/') : '-'}
                          </span>
                        </Popover>
                      ) : (
                        <span className="rightSTR">{info?.bpName}</span>
                      )}
                    </Col>
                    <Col span={12} className="rightCol">
                      {/* {IconStatus[info?.status].icon} */}
                      {IconStatus[info?.bpStatus]?.name}
                    </Col>
                  </Row>
                  {entry === 'xuanpin'
                    ? null
                    : info?.selectionStatus !== 'SKIP' &&
                      info?.selectionStatus !== 'AUTO_PASS' && (
                        <Row className="auditStyle">
                          <Col span={12} className="leftCol">
                            <span className="leftSTR">选品：</span>
                            <span className="rightSTR">{info?.selectionAuditorName}</span>
                          </Col>
                          <Col span={12} className="rightCol">
                            {/* {IconStatus[info?.selectionStatus].icon} */}
                            {IconStatus[info?.selectionStatus]?.name}
                          </Col>
                        </Row>
                      )}
                  {entry === 'xuanpin'
                    ? null
                    : info?.operatorStatus !== 'SKIP' &&
                      info?.operatorStatus !== 'AUTO_PASS' && (
                        <Row className="auditStyle">
                          <Col span={12} className="leftCol">
                            <span className="leftSTR">运营：</span>
                            <span className="rightSTR">{info?.operatorAuditorName}</span>
                          </Col>
                          <Col span={12} className="rightCol">
                            {/* {IconStatus[info?.operatorStatus].icon} */}
                            {IconStatus[info?.operatorStatus]?.name}
                          </Col>
                        </Row>
                      )}
                  {entry === 'xuanpin' ? null : (
                    <Row className="auditStyle">
                      <Col span={12} className="leftCol">
                        <span className="leftSTR">法务：</span>
                        <span className="rightSTR">{info?.legalAuditorName}</span>
                      </Col>
                      <Col span={12} className="rightCol">
                        <span style={{ color: 'black' }}>
                          {LegalStaus[info?.legalStatus]?.name}
                        </span>
                      </Col>
                    </Row>
                  )}
                  {entry === 'xuanpin' ? null : (
                    <Row className="auditStyle">
                      <Col span={24} className="leftCol" style={{ marginLeft: '35px' }}>
                        <span className="auditFw">
                          {IconStatus[info?.auditDetailMap?.SUPPLIER?.auditState]?.icon}
                          商家
                        </span>
                        <span className="auditFw">
                          {IconStatus[info?.auditDetailMap?.BRAND?.auditState]?.icon}
                          品牌
                        </span>
                        <span>
                          {IconStatus[info?.auditDetailMap?.GOODS?.auditState]?.icon}
                          商品
                        </span>
                      </Col>
                    </Row>
                  )}
                  {entry === 'xuanpin' ? (
                    <Row className="auditStyle">
                      <Col span={24}>
                        <span className="leftSTR">法务：</span>
                        <span className="rightSTR">
                          {info?.qualificationAuditLatestVO?.auditorName}
                        </span>
                      </Col>
                    </Row>
                  ) : null}
                  {entry === 'xuanpin' && (
                    <Row className="auditStyle">
                      <Col span={24} className="leftCol" style={{ marginLeft: '35px' }}>
                        <Status />
                      </Col>
                    </Row>
                  )}
                  {entry === 'xuanpin' ? (
                    <Row className="auditStyle">
                      <Col span={24}>
                        <span className="leftSTR">资质有效期：</span>
                        <span className="rightSTR">
                          {info?.qualificationAuditLatestVO?.expirationDate
                            ? moment(info?.qualificationAuditLatestVO?.expirationDate).format(
                                'YYYY-MM-DD',
                              )
                            : '-'}
                        </span>
                      </Col>
                    </Row>
                  ) : null}
                  {entry === 'xuanpin'
                    ? null
                    : info?.lowCommissionAuditStatus !== 'NONE' && (
                        <>
                          <Row className="auditStyle">
                            <Col span={24} className="leftCol">
                              <span className="leftSTR">低佣：</span>
                              <span className="rightSTR">
                                {info?.lowCommissionFlowNo && `${info?.lowCommissionFlowNo}`}
                              </span>
                            </Col>
                          </Row>
                          <Row className="auditStyle">
                            <Col span={18} className="leftCol" style={{ paddingLeft: '60px' }}>
                              <div className="rightSTR">
                                {!!info?.lowCommissionUniqueCode &&
                                !isNaN(Number(info.lowCommissionUniqueCode)) &&
                                info?.lowCommissionAuditStatus === 'PENDING' ? (
                                  <span className="rightSTR">
                                    {nodeLowList?.[nodeLowList?.length - 1]?.isRemark === 0
                                      ? nodeLowList?.filter((item, index) => {
                                          return (
                                            item.isRemark === 0 &&
                                            nodeLowList?.[index - 1]?.isRemark === 2
                                          );
                                        })?.[0]?.userName
                                      : nodeLowList?.[nodeLowList?.length - 1]?.userName}
                                  </span>
                                ) : (
                                  <span className="leftSTR">{info?.lowCommissionAuditorName}</span>
                                )}
                              </div>
                            </Col>
                            {/* {!(
                              !!info?.lowCommissionUniqueCode &&
                              !isNaN(Number(info.lowCommissionUniqueCode)) &&
                              info?.lowCommissionAuditStatus === 'PENDING'
                            ) && ( */}
                            <Col span={6} className="rightCol" style={{ marginLeft: '8px' }}>
                              {
                                LowCommissionAuditStatusEnum[
                                  info?.lowCommissionAuditStatus as keyof typeof LowCommissionAuditStatusEnum
                                ]?.name
                              }
                            </Col>
                            {/* )} */}
                          </Row>
                        </>
                      )}
                  {info?.highRiskGrant &&
                    (!lowCommissionInfo?.isLowCommission ||
                      info?.lowCommissionAuditStatus === 'APPROVED') && (
                      <div
                        style={{
                          marginLeft: '30px',
                          marginTop: '4px',
                          marginBottom: '4px',
                        }}
                      >
                        <Tag className="ml-4" style={{ fontWeight: 700 }} type="line" color="green">
                          特批通过
                        </Tag>
                      </div>
                    )}
                  {entry === 'changci' && info?.complianceStatus ? (
                    <>
                      <Row className="auditStyle">
                        <Col
                          span={12}
                          className="leftCol"
                          style={{ display: 'flex', alignItems: 'top' }}
                        >
                          <span className="leftSTR" style={{ flexShrink: 0 }}>
                            合规：
                          </span>
                          <span
                            style={{ display: 'flex', flexDirection: 'column', marginTop: '2px' }}
                          >
                            <span className="rightSTR">{info?.complianceNo || '-'}</span>
                            <span>{info?.complianceAuditorName || '-'}</span>
                          </span>
                        </Col>
                        <Col span={12} className="rightCol">
                          {info?.complianceStatus &&
                            COMPLIANCESTATUS_COLOR_NAME[
                              info?.complianceStatus as COMPLIANCESTATUS_ENUM
                            ]}
                        </Col>
                      </Row>
                    </>
                  ) : null}
                  {entry === 'changci' && (
                    <Row className="auditStyle">
                      <Col
                        span={24}
                        // onClick={(e) => {
                        //   e.stopPropagation();
                        //   e.preventDefault();
                        //   window.open(`/provider-detail/${info?.supplierId}`);
                        // }}
                        style={{ display: 'flex' }}
                      >
                        <span className="leftSTR">商品分：</span>
                        <div style={{ display: 'flex', flexDirection: 'column', flex: 1 }}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                            }}
                          >
                            <span className="rightSTR">
                              {info?.goodsQualityScoreApprovalProcessNo}
                            </span>
                            {info?.qualityScoreAuditStatus && (
                              <span>
                                {
                                  GOODS_GRADES_STATE[
                                    info?.qualityScoreAuditStatus as GOODS_GRADES_STATE_ENUM
                                  ].icon
                                }
                                <span
                                  style={{
                                    color:
                                      GOODS_GRADES_STATE_COLOR[
                                        info?.qualityScoreAuditStatus as GOODS_GRADES_STATE_ENUM
                                      ],
                                  }}
                                >
                                  {
                                    GOODS_GRADES_STATE_NAME[
                                      info?.qualityScoreAuditStatus as GOODS_GRADES_STATE_ENUM
                                    ]
                                  }
                                </span>
                              </span>
                            )}
                          </div>

                          <span className="rightSTR">{info?.qualityScoreAuditor || '-'}</span>
                        </div>
                      </Col>
                    </Row>
                  )}

                  <Row className="auditStyle" style={{ position: 'relative' }}>
                    <Col
                      span={22}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        window.open(`/provider-detail/${info?.supplierId}`);
                      }}
                    >
                      <span className="leftSTR">商家：</span>
                      <span className="rightSTR">{info?.supplierOrgName}</span>
                    </Col>
                    <Col span={2}>
                      {info?.serviceAgreementId !== '-1' && (
                        <Popover content={info?.serviceAgreementId ? '已签署' : '未签署'}>
                          {info?.serviceAgreementId ? (
                            <span
                              className="iconfont icon-yiqianshu"
                              style={{
                                fontSize: '16px',
                                bottom: 4,
                                position: 'relative',
                                color: '#52C41A',
                              }}
                            ></span>
                          ) : (
                            ''
                          )}
                        </Popover>
                      )}
                    </Col>
                  </Row>
                  {info?.legalStatus === 'HIGH' && info?.specialAuditStatus && (
                    <>
                      <Row className="auditStyle">
                        <Col span={24} className="leftCol">
                          <span className="leftSTR">资质特批：</span>
                          <span className="rightSTR">{info?.specialAuditApprovalProcessNo}</span>
                        </Col>
                      </Row>
                      <Row className="auditStyle">
                        <Col span={18} className="leftCol" style={{ paddingLeft: '60px' }}>
                          {!!info?.specialAuditProcessUid &&
                          !isNaN(Number(info.specialAuditProcessUid)) &&
                          info?.specialAuditStatus === 'CONFIRMING' ? (
                            <div className="rightSTR">
                              {nodeSepList?.[nodeSepList?.length - 1]?.isRemark === 0
                                ? nodeSepList?.filter((item, index) => {
                                    return (
                                      item.isRemark === 0 &&
                                      nodeSepList?.[index - 1]?.isRemark === 2
                                    );
                                  })?.[0]?.userName
                                : nodeSepList?.[nodeSepList?.length - 1]?.userName}
                            </div>
                          ) : (
                            info?.specialAuditorRecordList?.map((item) => (
                              <div className="rightSTR"> {item.auditor} </div>
                            ))
                          )}
                        </Col>
                        {!(
                          !!info?.specialAuditProcessUid &&
                          !isNaN(Number(info.specialAuditProcessUid)) &&
                          info?.specialAuditStatus === 'CONFIRMING'
                        ) && (
                          <Col span={6} className="rightCol" style={{ marginLeft: '8px' }}>
                            {ApproveIconStatus[info.specialAuditStatus].name}
                          </Col>
                        )}
                      </Row>
                    </>
                  )}
                  {(!!info?.supplierBodySpecialAuditId ||
                    !!info?.supplierBodySpecialAuditStatus) && (
                    <>
                      <Row className="auditStyle">
                        <Col span={24} className="leftCol">
                          <span className="leftSTR">主体特批：</span>
                          <span className="rightSTR">
                            {info?.supplierBodySpecialAuditApprovalProcessNo}
                          </span>
                        </Col>
                      </Row>
                      <Row className="auditStyle">
                        <Col span={18} className="leftCol" style={{ paddingLeft: '60px' }}>
                          {/* {info?.specialAuditorRecordList?.map((item) => (
                          <div className="rightSTR"> {item.auditor} </div>
                        ))} */}
                          <div className="rightSTR">
                            {!!info?.supplierBodySpecialProcessUid &&
                            !isNaN(Number(info.supplierBodySpecialProcessUid)) &&
                            info?.supplierBodySpecialAuditStatus === 'CONFIRMING' ? (
                              <div className="rightSTR">
                                {nodeBodyList?.[nodeBodyList?.length - 1]?.isRemark === 0
                                  ? nodeBodyList?.filter((item, index) => {
                                      return (
                                        item.isRemark === 0 &&
                                        nodeBodyList?.[index - 1]?.isRemark === 2
                                      );
                                    })?.[0]?.userName
                                  : nodeBodyList?.[nodeBodyList?.length - 1]?.userName}
                              </div>
                            ) : (
                              info?.supplierBodySpecialAuditorRecordList
                                ?.map((item) => {
                                  // 确保 item 有 auditor 属性且不为 undefined
                                  const auditor = item && item.auditor;
                                  return auditor || ''; // 如果 auditor 不存在，则返回空字符串
                                })
                                .join(',')
                            )}
                          </div>
                        </Col>
                        {!(
                          !!info?.supplierBodySpecialProcessUid &&
                          !isNaN(Number(info.supplierBodySpecialProcessUid)) &&
                          info?.supplierBodySpecialAuditStatus === 'CONFIRMING'
                        ) && (
                          <Col span={6} className="rightCol" style={{ marginLeft: '8px' }}>
                            {info?.supplierBodySpecialAuditStatus
                              ? ApproveIconStatus[
                                  info?.supplierBodySpecialAuditStatus === 'WAIT_AUDIT'
                                    ? 'CONFIRMING'
                                    : info?.supplierBodySpecialAuditStatus
                                ].name
                              : ''}
                          </Col>
                        )}
                      </Row>
                    </>
                  )}
                </div>
              )}
              {/* 爆品和预爆品显示下边爆品相关信息 */}
              {(info?.explosiveType === 'PRE_EXPLOSIVE' || info?.explosiveType === 'EXPLOSIVE') && (
                <div className="priceGroup" style={{ backgroundColor: '#F8F8F9' }}>
                  <Row className="auditStyle">
                    <Col span={24}>
                      <span className="leftSTR">爆品来源事业部：</span>
                      <span className="rightSTR">{info?.explosiveDeptName}</span>
                    </Col>
                  </Row>
                  <Row className="auditStyle">
                    <Col span={24}>
                      <span className="leftSTR">爆品来源直播间：</span>
                      <span className="rightSTR">{info?.explosiveLiveRoomName}</span>
                    </Col>
                  </Row>
                  <Row className="auditStyle">
                    <Col span={24}>
                      <span className="leftSTR">爆品来源场次：</span>
                      <span className="rightSTR">{info?.explosiveLiveRoundName}</span>
                    </Col>
                  </Row>
                  <Row className="auditStyle">
                    <Col span={24}>
                      <span className="leftSTR">爆品归属商务：</span>
                      <span className="rightSTR">{info?.explosiveBpName}</span>
                    </Col>
                  </Row>
                  <Row className="auditStyle">
                    <Col span={24}>
                      <span className="leftSTR">爆品归属运营：</span>
                      <span className="rightSTR">{info?.explosiveOperatorAuditorName}</span>
                    </Col>
                  </Row>
                </div>
              )}
            </>
          ) : (
            <>
              <div className={styles['priceGroup-box'] + ' priceGroup'}>
                <Row className="auditStyle">
                  <Col>
                    <span className="leftSTR">线上佣金：</span>{' '}
                    {type === 'edit' ? (
                      <Form.Item>
                        {getFieldDecorator('commissionRate', {
                          initialValue: Math.round(info?.commissionRate * 10000) / 100,
                          rules: [
                            { required: true, message: '线上佣金' },
                            {
                              validator: (_, value, callback) => {
                                return validatorTalentCommission(
                                  value,
                                  callback,
                                  0,
                                  info?.platformSource,
                                );
                              },
                            },
                          ],
                        })(
                          <InputNumber
                            formatter={(value) => `${value}%`}
                            parser={(value) => value.replace('%', '')}
                            placeholder="%"
                            precision={2}
                            step={0.01}
                            style={{ width: '190px', marginTop: 4, marginBottom: 8 }}
                            onBlur={handleSetEditTotalCommission}
                          />,
                        )}
                      </Form.Item>
                    ) : (
                      <span className="rightSTR">
                        {info?.hideCommissionFlag ? (
                          ' *** %'
                        ) : (
                          <>{Math.round(info?.commissionRate * 10000) / 100 + '%'}</>
                        )}
                      </span>
                    )}
                  </Col>
                </Row>
                <Row className="auditStyle">
                  <Col>
                    <span className="leftSTR">线下佣金：</span>{' '}
                    <span className="rightSTR">
                      {info?.hideCommissionFlag ? (
                        ' *** %'
                      ) : (
                        <>
                          {info?.commissionRateOffline
                            ? Math.round(info?.commissionRateOffline * 10000) / 100 + '%'
                            : '0%'}
                        </>
                      )}
                    </span>
                  </Col>
                </Row>
              </div>
              {type === 'edit' || isSupplier ? (
                ''
              ) : (
                <div className="priceGroup" style={{ backgroundColor: '#F8F8F9' }}>
                  {entry === 'xuanpin' ? (
                    <Row className="auditStyle">
                      <Col span={24}>
                        <span className="leftSTR">事业部：</span>
                        <span className="rightSTR">{info?.deptName}</span>
                      </Col>
                    </Row>
                  ) : null}
                  <Row className="auditStyle">
                    <Col span={24}>
                      <span className="leftSTR">项目组：</span>
                      <span className="rightSTR">{info?.groupName}</span>
                    </Col>
                  </Row>
                  <Row className="auditStyle">
                    <Col span={12} className="leftCol" style={{ display: 'flex' }}>
                      <span className="leftSTR">商务：</span>
                      {entry === 'xuanpin' ? (
                        <Popover
                          title="商务"
                          content={<>{info?.bpNames?.length ? info?.bpNames?.join('/') : '-'}</>}
                        >
                          <span
                            className="rightSTR"
                            style={{
                              width: '90px',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              wordBreak: 'break-all',
                              display: 'inline-block',
                            }}
                          >
                            {info?.bpNames?.length ? info?.bpNames?.join('/') : '-'}
                          </span>
                        </Popover>
                      ) : (
                        <span className="rightSTR">{info?.bpName}</span>
                      )}
                    </Col>
                    <Col span={12} className="rightCol">
                      {/* {IconStatus[info?.status].icon} */}
                      {IconStatus[info?.bpStatus]?.name}
                    </Col>
                  </Row>
                  {entry === 'xuanpin' ? null : (
                    <Row className="auditStyle">
                      <Col span={12} className="leftCol">
                        <span className="leftSTR">法务：</span>
                        <span className="rightSTR">{info?.legalAuditorName}</span>
                      </Col>
                      <Col span={12} className="rightCol">
                        <span style={{ color: 'black' }}>
                          {LegalStaus[info?.legalStatus]?.name}
                        </span>
                      </Col>
                    </Row>
                  )}
                  {entry === 'xuanpin' ? null : (
                    <Row className="auditStyle">
                      <Col span={24} className="leftCol" style={{ marginLeft: '35px' }}>
                        <span className="auditFw">
                          {IconStatus[info?.auditDetailMap?.SUPPLIER?.auditState]?.icon}
                          商家
                        </span>
                        <span className="auditFw">
                          {IconStatus[info?.auditDetailMap?.BRAND?.auditState]?.icon}
                          品牌
                        </span>
                        <span>
                          {IconStatus[info?.auditDetailMap?.GOODS?.auditState]?.icon}
                          商品
                        </span>
                      </Col>
                    </Row>
                  )}
                  {entry === 'xuanpin' ? (
                    <Row className="auditStyle">
                      <Col span={24}>
                        <span className="leftSTR">法务：</span>
                        <span className="rightSTR">
                          {info?.qualificationAuditLatestVO?.auditorName}
                        </span>
                      </Col>
                    </Row>
                  ) : null}
                  {entry === 'xuanpin' && (
                    <Row className="auditStyle">
                      <Col span={24} className="leftCol" style={{ marginLeft: '35px' }}>
                        <Status />
                      </Col>
                    </Row>
                  )}

                  {entry === 'xuanpin' ? (
                    <Row className="auditStyle">
                      <Col span={24}>
                        <span className="leftSTR">资质有效期：</span>
                        <span className="rightSTR">
                          {info?.qualificationAuditLatestVO?.expirationDate
                            ? moment(info?.qualificationAuditLatestVO?.expirationDate).format(
                                'YYYY-MM-DD',
                              )
                            : '-'}
                        </span>
                      </Col>
                    </Row>
                  ) : null}
                  {info?.highRiskGrant &&
                    (!lowCommissionInfo?.isLowCommission ||
                      info?.lowCommissionAuditStatus === 'APPROVED') && (
                      <div style={{ marginLeft: '30px', marginTop: '4px', marginBottom: '4px' }}>
                        <Tag className="ml-4" style={{ fontWeight: 700 }} type="line" color="green">
                          特批通过
                        </Tag>
                      </div>
                    )}
                  <Row className="auditStyle" style={{ position: 'relative' }}>
                    <Col
                      span={22}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        window.open(`/provider-detail/${info?.supplierId}`);
                      }}
                    >
                      <span className="leftSTR">商家：</span>
                      <span className="rightSTR">{info?.supplierOrgName}</span>
                    </Col>
                    <Col span={2}>
                      {info?.serviceAgreementId !== '-1' && (
                        <Popover content={info?.serviceAgreementId ? '已签署' : '未签署'}>
                          {info?.serviceAgreementId ? (
                            <span
                              className="iconfont icon-yiqianshu"
                              style={{
                                fontSize: '16px',
                                bottom: 4,
                                position: 'relative',
                                color: '#52C41A',
                              }}
                            ></span>
                          ) : (
                            ''
                          )}
                        </Popover>
                      )}
                    </Col>
                  </Row>
                  {info?.legalStatus === 'HIGH' && info?.specialAuditStatus && (
                    <Row className="auditStyle">
                      <Col span={14} className="leftCol">
                        <span className="leftSTR">资质特批：</span>
                        <span className="rightSTR">{info?.specialAuditApprovalProcessNo}</span>
                      </Col>
                      <Col span={4} className="leftCol">
                        {info?.specialAuditorRecordList?.map((item) => (
                          <div className="rightSTR"> {item.auditor} </div>
                        ))}
                      </Col>
                      <Col span={6} className="rightCol">
                        {ApproveIconStatus[info.specialAuditStatus].name}
                      </Col>
                    </Row>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
export default GoodsCard;
