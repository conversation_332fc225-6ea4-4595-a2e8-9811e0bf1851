import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Table, Input, Form, Popover } from 'antd';
import { fwActionLogList } from '@/services/yml/choiceList/index';
import Title from './Title';
import AuditDetail from '@/pages/audit/legal-audit-records/components/NewAduitDetail/index';
type propsType = {
  form: any;
  info?: any;
  type?: string;
};
import '../index.less';
import moment from 'moment';
const riskLevelTxt = {
  PASS: '通过',
  LOW: '低风险',
  MIDDLE: '中风险',
  HIGH: '高风险',
  NONE: '待审核',
};
const Gift: React.FC<propsType> = (props) => {
  const { type, info, form, entry } = props;
  const [dataSource, setDataSource] = useState([]);

  useEffect(() => {
    if (info?.id) {
      fwActionLogList({
        current: 1,
        size: 1000,
        orderBy: 'audit_time',
        // bizOrderTypeList: ['SELECTION_ROUND_BL_FW_CHANGE', 'BLM_MANUAL_UNBIND', 'BLM_MANUAL_BIND'],
        selectionRoundNos: [info?.no],
      })
        .then(({ res }) => {
          // console.log(res);
          if (res.code === '200') {
            if (res.result.records && res.result.records.length) {
              const arr = res.result.records || [];
              arr?.forEach((item, index) => {
                item.index = index + 1;
              });
              setDataSource(arr);
            } else {
              setDataSource([]);
            }
          } else {
            setDataSource([]);
          }
        })
        .catch(() => {
          setDataSource([]);
        });
      // setDataSource(arr.length ? [...arr] : []);
    }
  }, [info?.id]);
  const adjustTypeEnum = {
    ADJUST_SERVICE_FEE: '调整基础服务费',
    ADJUST_CHANGE_BINDING: '调整解绑支付单',
  };
  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (t) => t || '-',
      width: 60,
    },
    {
      title: '审核结果',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      render: (t) => {
        return (
          <Popover content={t ? t : '-'}>
            <p className="two-line-ellipsis" style={{ width: '100%' }}>
              {t ? riskLevelTxt[t] : '-'}
            </p>
          </Popover>
        );
      },
      // width: 300,
    },
    {
      title: '审核人',
      dataIndex: 'auditorName',
      key: 'auditorName',
      align: 'right',
      render: (t) => t || '-',
      width: 150,
    },

    {
      title: '审核时间',
      dataIndex: 'auditTime',
      key: 'auditTime',
      align: 'right',
      width: 200,
      render: (t) => moment(t).format('YYYY-MM-DD HH:mm:ss') || '-',
    },
    {
      title: '操作',
      dataIndex: 'operateTime',
      key: 'operateTime',
      align: 'right',
      width: 200,
      render: (t, r) => {
        return (
          <AuditDetail id={r.id} bpName={r.bpName}>
            <a>查看详情</a>
          </AuditDetail>
        );
      },
    },
  ];
  return (
    <Descriptions title="法务审核记录" style={{ marginBottom: 16 }}>
      <Descriptions.Item label="" span={3}>
        <Table
          rowClassName="skuTable-row"
          // style={
          //   dataSource?.length > 5
          //     ? { height: '440px', overflowY: 'scroll', textAlign: 'center' }
          //     : { textAlign: 'center' }
          // }
          scroll={{ y: 500 }}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </Descriptions.Item>
    </Descriptions>
  );
};
export default Gift;
