import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Table, Input, Form, Popover } from 'antd';
import Title from './Title';
type propsType = {
  form: any;
  info?: any;
  type?: string;
};
import '../index.less';
import { checkAuth } from 'qmkit';
const Gift: React.FC<propsType> = (props) => {
  const { type, info, form, entry } = props;
  const [dataSource, setDataSource] = useState();
  useEffect(() => {
    if (info?.skuList !== null && info?.skus !== null) {
      const arr = entry === 'xuanpin' ? [...info?.skuList] : [...info?.skus];
      arr.length > 0 &&
        arr.forEach((item, index) => {
          item.index = index;
        });
      setDataSource([...arr]);
      form.setFieldsValue({ skus: [...arr] });
    }
  }, [info?.id, type]);
  useEffect(() => {
    form.setFieldsValue({ skus: dataSource });
  }, [dataSource]);

  const columns = [
    {
      title: '商品图',
      dataIndex: 'image',
      key: 'image',
      width: '80px',
      render: (t, records) => (
        <div
          style={{
            width: '30px',
            height: '30px',
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            marginRight: '5px',
          }}
        >
          {' '}
          <img width={30} height={30} src={records.image} alt="" />
        </div>
      ),
    },
    {
      title: 'SKU',
      dataIndex: 'name',
      key: 'name',
      width: '250px',
      render: (t, records) => (
        <Popover key={t} content={t}>
          <p
            style={{
              textAlign: 'left',
              textOverflow: 'ellipsis',
              width: 100,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              display: 'inline-block',
            }}
            key="t"
          >
            {t}
          </p>
        </Popover>
      ),
    },
    {
      title: '日常价',
      dataIndex: 'price',
      key: 'price',
      align: 'right',
      render: (t, records) => <p style={{ textAlign: 'right' }}>{t}</p>,
    },
    {
      title: <Title title={'直播价'} />,
      dataIndex: 'salePrice',
      key: 'salePrice',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.salePrice}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].salePrice = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
              disabled={!checkAuth('f_choose_bp_edit') || !checkAuth('f_choose_bp_mechanism_edit')}
            ></Input>
          ) : (
            <span style={{ color: 'red' }}>{t}</span>
          )}
        </p>
      ),
    },
    {
      title: <Title title={'库存(件)'} />,
      dataIndex: 'stock',
      key: 'stock',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.stock}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].stock = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
              disabled={!checkAuth('f_choose_bp_edit') || !checkAuth('f_choose_bp_mechanism_edit')}
            ></Input>
          ) : (
            t
          )}
        </p>
      ),
    },
    {
      title: '历史最低价',
      dataIndex: 'hisLowestPrice',
      key: 'hisLowestPrice',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.hisLowestPrice}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].hisLowestPrice = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
            ></Input>
          ) : (
            t
          )}
        </p>
      ),
    },
    {
      title: '最高售卖价',
      dataIndex: 'hisHighestPrice',
      key: 'hisHighestPrice',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.hisHighestPrice}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].hisHighestPrice = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
            ></Input>
          ) : (
            t
          )}
        </p>
      ),
    },
    {
      title: '返现金额',
      dataIndex: 'cashAmount',
      key: 'cashAmount',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.cashAmount}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].cashAmount = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
            ></Input>
          ) : (
            t?.toFixed(2)
          )}
        </p>
      ),
    },
  ];
  const columnsGift = [
    {
      title: '商品图',
      dataIndex: 'image',
      key: 'image',
      width: '80px',
      render: (t, records) => (
        <div
          style={{
            width: '30px',
            height: '30px',
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            marginRight: '5px',
          }}
        >
          {' '}
          <img width={30} height={30} src={records.image} alt="" />
        </div>
      ),
    },
    {
      title: 'SKU',
      dataIndex: 'name',
      key: 'name',
      width: '250px',
      render: (t, records) => (
        <Popover key={t} content={t}>
          <p
            style={{
              textAlign: 'left',
              textOverflow: 'ellipsis',
              width: 100,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              display: 'inline-block',
            }}
            key="t"
          >
            {t}
          </p>
        </Popover>
      ),
    },
    {
      title: '日常价',
      dataIndex: 'price',
      key: 'price',
      align: 'right',
      render: (t, records) => <p style={{ textAlign: 'right' }}>{t}</p>,
    },
    {
      title: <Title title={'直播价'} />,
      dataIndex: 'salePrice',
      key: 'salePrice',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.salePrice}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].salePrice = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
              disabled={!checkAuth('f_choose_bp_edit') || !checkAuth('f_choose_bp_mechanism_edit')}
            ></Input>
          ) : (
            <span style={{ color: 'red' }}>{t}</span>
          )}
        </p>
      ),
    },
    {
      title: <Title title={'采购价(含税)'} />,
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>{type === 'info' && <span>{t}</span>}</p>
      ),
    },
    {
      title: <Title title={'税率(%)'} />,
      dataIndex: 'taxRate',
      key: 'taxRate',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>{type === 'info' && <span>{t}</span>}</p>
      ),
    },
    {
      title: <Title title={'库存(件)'} />,
      dataIndex: 'stock',
      key: 'stock',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.stock}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].stock = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
              disabled={!checkAuth('f_choose_bp_edit') || !checkAuth('f_choose_bp_mechanism_edit')}
            ></Input>
          ) : (
            t
          )}
        </p>
      ),
    },
    {
      title: '历史最低价',
      dataIndex: 'hisLowestPrice',
      key: 'hisLowestPrice',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.hisLowestPrice}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].hisLowestPrice = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
            ></Input>
          ) : (
            t
          )}
        </p>
      ),
    },
    {
      title: '最高售卖价',
      dataIndex: 'hisHighestPrice',
      key: 'hisHighestPrice',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.hisHighestPrice}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].hisHighestPrice = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
            ></Input>
          ) : (
            t
          )}
        </p>
      ),
    },
    {
      title: '返现金额',
      dataIndex: 'cashAmount',
      key: 'cashAmount',
      align: 'right',
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.cashAmount}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].cashAmount = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
            ></Input>
          ) : (
            t?.toFixed(2)
          )}
        </p>
      ),
    },
  ];
  return (
    <Descriptions title={type === 'edit' ? 'SKU信息' : ''} style={{ marginBottom: 16 }}>
      <Descriptions.Item label="" span={3}>
        {type === 'edit' ? (
          <Form.Item>
            {form.getFieldDecorator(entry === 'xuanpin' ? 'skuList' : 'skus', {
              rules: [{ required: false, message: '' }],
            })(
              <Table
                rowClassName="skuTable-row"
                style={
                  dataSource?.length > 5
                    ? { height: '440px', overflowY: 'scroll', textAlign: 'center' }
                    : { textAlign: 'center' }
                }
                dataSource={dataSource}
                columns={columns}
                pagination={false}
              />,
            )}
          </Form.Item>
        ) : (
          <Table
            rowClassName="skuTable-row"
            style={
              dataSource?.length > 5
                ? { height: '440px', overflowY: 'scroll', textAlign: 'center' }
                : { textAlign: 'center' }
            }
            dataSource={dataSource}
            columns={
              (entry === 'xuanpin' || entry === 'changci') && info?.luckyProductFlag
                ? columnsGift
                : columns
            }
            pagination={false}
          />
        )}
      </Descriptions.Item>
    </Descriptions>
  );
};
export default Gift;
