import React, { useState } from 'react';
import { Card, Descriptions, Table, Form, Tabs } from 'antd';
import './index.less';
import Service from './service';
import Gift from './gift';
import Goods from './goods';
import SkuList from './skuList';
import Investment from './investment';
import Contract from './contract';
import BpInfo from './BpInfo';
import ActionLog from './ActionLog';
import FwActionLog from './fwActionLog';
import GoodsActionLog from './goodsActionLog';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { LiveGoodsInfoResult } from '@/services/yml/choiceList';
import BpClaimsLog from './BpClaimsLog';
import styles from './index.module.less';
import OralBox from './OralBox';
import PointLog from './PointLog';

import MaterialFeedback from './MaterialFeedback';
type PropsType = {
  entry?: 'xuanpin' | 'changci';
  type: 'info' | 'edit';
  form?: WrappedFormUtils;
  info?: LiveGoodsInfoResult;
  tabsValue: string;
  onRefresh: any;
  visible: boolean;
};

const RightCard: React.FC<PropsType> = (props) => {
  const { form, type, info, entry, tabsValue, onRefresh, visible } = props;
  // console.log('rightform', form, type);

  const [tabValue, setTabValue] = useState<'sku' | 'oral'>('sku');

  const [spokenScriptInfoId, setSpokenScriptInfoId] = useState<string>(''); // 口播稿id

  return (
    <div className="right">
      {/* 商品信息模块 */}
      <Goods
        info={info}
        type={type}
        form={form}
        entry={entry}
        tabsValue={tabsValue}
        onRefresh={onRefresh}
      />
      {/* sku模块 */}
      {type === 'edit' ? (
        <>
          <SkuList info={info} type={type} form={form} entry={entry} />
          {/* 优惠信息模块 */}
          <Gift info={info} type={type} form={form} entry={entry} visible={visible} />
          {/* 服务模块 */}
          <Service info={info} type={type} form={form} entry={entry} />
          {/* 招商信息 */}
          {entry === 'xuanpin' ? <Investment info={info} type={type} form={form} /> : null}
          {/* 合同/财务结算模块 */}
          {entry === 'changci' ? (
            <Contract info={info} type={type} form={form} tabsValue={tabsValue} />
          ) : null}
          {entry === 'changci' ? <BpInfo info={info} type={type} form={form} /> : null}
          {entry === 'changci' ? (
            <ActionLog info={info} type={type} form={form} tabsValue={tabsValue} />
          ) : null}
          {entry === 'changci' ? <FwActionLog info={info} type={type} form={form} /> : null}
          {entry === 'changci' ? <GoodsActionLog info={info} type={type} form={form} /> : null}
          {entry === 'xuanpin' ? <BpClaimsLog info={info} type={type} form={form} /> : null}
        </>
      ) : (
        <div className={styles['edit-tab']}>
          <Tabs
            onChange={(activeKey: 'sku' | 'oral') => {
              setTabValue(activeKey);
            }}
            style={{ marginBottom: '16px', marginTop: '-10px' }}
          >
            <Tabs.TabPane tab="SKU信息" key="sku"></Tabs.TabPane>
            <Tabs.TabPane tab="口播稿信息" key="oral"></Tabs.TabPane>
          </Tabs>
          {tabValue === 'sku' ? (
            <>
              <SkuList info={info} type={type} form={form} entry={entry} />
              {/* 优惠信息模块 */}
              <Gift info={info} type={type} form={form} entry={entry} />
              {/* 服务模块 */}
              <Service info={info} type={type} form={form} entry={entry} />
              {/* 招商信息 */}
              {entry === 'xuanpin' ? <Investment info={info} type={type} form={form} /> : null}
              {/* 合同/财务结算模块 */}
              {entry === 'changci' ? (
                <Contract info={info} type={type} form={form} tabsValue={tabsValue} />
              ) : null}
              {entry === 'changci' ? <BpInfo info={info} type={type} form={form} /> : null}
              {entry === 'changci' ? (
                <ActionLog info={info} type={type} form={form} tabsValue={tabsValue} />
              ) : null}
              {entry === 'changci' ? <FwActionLog info={info} type={type} form={form} /> : null}
              {entry === 'changci' ? <GoodsActionLog info={info} type={type} form={form} /> : null}
              {entry === 'xuanpin' ? <BpClaimsLog info={info} type={type} form={form} /> : null}
              <PointLog info={info} type={type} form={form} entry={entry} tabsValue={tabsValue} />
            </>
          ) : (
            <OralBox info={info} />
          )}
        </div>
      )}
    </div>
  );
};
export default RightCard;
