import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Table, Input, Form, Popover } from 'antd';
import Title from './Title';
type propsType = {
  form: any;
  info?: any;
  type?: string;
};
import '../index.less';
const Gift: React.FC<propsType> = (props) => {
  const { type, info, form, entry } = props;
  const [dataSource, setDataSource] = useState();
  useEffect(() => {
    if (info?.selectionRoundAdjustRecordList !== null) {
      const arr =
        (info?.selectionRoundAdjustRecordList &&
          info?.selectionRoundAdjustRecordList.length && [
            ...info?.selectionRoundAdjustRecordList,
          ]) ||
        [];
      arr.length > 0 &&
        arr.forEach((item, index) => {
          item.index = index;
        });
      setDataSource(arr.length ? [...arr] : []);
    }
  }, [info?.id]);
  const adjustTypeEnum = {
    ADJUST_SERVICE_FEE: '调整基础服务费',
    ADJUST_CHANGE_BINDING: '调整解绑支付单',
  };
  const columns = [
    {
      title: '审批流程编号',
      dataIndex: 'approvalProcessNo',
      key: 'approvalProcessNo',
      render: (t) => t || '-',
      width: 120,
    },
    {
      title: '调整类型',
      dataIndex: 'adjustType',
      key: 'adjustType',
      render: (t) => (t ? adjustTypeEnum[t] : '-'),
      width: 100,
    },
    {
      title: '调整原因',
      dataIndex: 'adjustReason',
      key: 'adjustReason',
      align: 'right',
      render: (t) => t || '-',
      width: 100,
    },

    {
      title: '调整前基础服务费',
      dataIndex: 'beforeBrandFee',
      key: 'beforeBrandFee',
      align: 'right',
      width: 130,
      render: (t) => t || '-',
    },
    {
      title: '调整后基础服务费',
      dataIndex: 'afterBrandFee',
      key: 'afterBrandFee',
      align: 'right',
      width: 130,
      render: (t, records) => (
        <p style={{ textAlign: 'right' }}>
          {type === 'edit' ? (
            <Input
              style={{ width: 80 }}
              value={records.hisLowestPrice}
              onChange={(e) => {
                const arr = [...dataSource];
                arr[records.index].hisLowestPrice = e.target.value;
                // console.log(arr);
                setDataSource([...arr]);
              }}
            ></Input>
          ) : (
            t
          )}
        </p>
      ),
    },
    {
      title: '调整金额',
      dataIndex: 'hisHighestPrice',
      key: 'hisHighestPrice',
      align: 'right',
      width: 100,
      render: (t, r) => {
        return Math.abs(r.beforeBrandFee - r.afterBrandFee) || '-';
      },
    },
    {
      title: '关联的新合作订单号',
      dataIndex: 'targetCoopOrderNo',
      key: 'targetCoopOrderNo',
      align: 'right',
      render: (t) => t || '-',
      width: 120,
    },
    {
      title: '关联的新场次货盘选品编号',
      dataIndex: 'targetSelectionRoundNo',
      key: 'targetSelectionRoundNo',
      align: 'right',
      width: 120,
    },
  ];
  return (
    <Descriptions title="商务条款变更信息" style={{ marginBottom: 16 }}>
      <Descriptions.Item label="" span={3}>
        <Table
          rowClassName="skuTable-row"
          style={
            dataSource?.length > 5
              ? { height: '440px', overflowY: 'scroll', textAlign: 'center' }
              : { textAlign: 'center' }
          }
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </Descriptions.Item>
    </Descriptions>
  );
};
export default Gift;
