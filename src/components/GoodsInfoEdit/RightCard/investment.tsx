import React from 'react';
import { Card, Descriptions, Table, Input, Form, Popover } from 'antd';
const { TextArea } = Input;
import BrandList from '../component/brandList';
type propsType = {
  type: string;
  info?: any;
  form: any;
};
const Investment: React.FC<propsType> = (props) => {
  const { type, info, form } = props;
  const { getFieldDecorator } = form;
  const onChange = () => {};
  return (
    <Form layout="inline">
      <Descriptions title="招商信息" column={1}>
        <Descriptions.Item label="有效期">
          {info?.investmentPlanStartTime + '-' + info?.investmentPlanEndTime}
        </Descriptions.Item>
        <Descriptions.Item label="报名直播间">
          {info?.liveRoomMaps?.map((item, index) => {
            const flag = info?.liveRoomMaps?.length - 1;
            if (flag === index) {
              return item.liveRoomName;
            } else {
              return item.liveRoomName + ',';
            }
          })}
        </Descriptions.Item>
        <Descriptions.Item label="来源报名单号">
          <div
            style={{
              height: '15px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              width: '890px',
            }}
          >
            <Popover placement="topLeft" content={info?.bjdNos}>
              {info?.bjdNos}
            </Popover>
          </div>
        </Descriptions.Item>
      </Descriptions>
    </Form>
  );
};
export default Investment;
