import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Table, Input, Form, Popover } from 'antd';
import { queryOperationLogList } from '../services/point';
import PopoverRowText from '@/components/PopoverRowText/index';

import Title from './Title';
type propsType = {
  form: any;
  info?: any;
  type?: string;
  entry: any;
  tabsValue: string;
};
import '../index.less';
import moment from 'moment';
const Gift: React.FC<propsType> = (props) => {
  const { type, info, form, entry, tabsValue } = props;
  const [dataSource, setDataSource] = useState([]);
  useEffect(() => {
    if (info?.id) {
      queryOperationLogList({
        // current: 1,
        // size: 1000,
        // // bizOrderTypeList: ['SELECTION_ROUND_BL_FW_CHANGE', 'BLM_MANUAL_UNBIND', 'BLM_MANUAL_BIND'],
        // bizOrderNo: info?.no,
        bizOrderId: info?.id,
        bizOrderTypeList:
          entry === 'changci'
            ? tabsValue === 'live'
              ? ['SELECTION_ROUND']
              : ['NON_LIVE_SELECTION_ROUND_POINT']
            : ['SELECT_GOODS_POOL_LOG'],
        bizTypeModels:
          entry === 'changci'
            ? tabsValue === 'live'
              ? [{ bizOrderType: 'SELECTION_ROUND', secondBizTypes: ['INTEREST_POINT_UPDATE'] }]
              : [
                  {
                    bizOrderType: 'NON_LIVE_SELECTION_ROUND_POINT',
                    secondBizTypes: ['NON_LIVE_SELECTION_ROUND_EDIT_INTEREST_POINTS'],
                  },
                ]
            : [
                {
                  bizOrderType: 'SELECT_GOODS_POOL_LOG',
                  secondBizTypes: ['INTEREST_POINT_UPDATE'],
                },
              ],
      })
        .then(({ res }) => {
          // console.log(res);
          if (res.code === '200') {
            if (res.result.records && res.result.records.length) {
              const arr = res.result.records || [];
              arr?.forEach((item, index) => {
                item.index = index + 1;
              });
              setDataSource(arr);
            } else {
              setDataSource([]);
            }
          } else {
            setDataSource([]);
          }
        })
        .catch(() => {
          setDataSource([]);
        });
      // setDataSource(arr.length ? [...arr] : []);
    }
  }, [info?.id]);
  const adjustTypeEnum = {
    ADJUST_SERVICE_FEE: '调整基础服务费',
    ADJUST_CHANGE_BINDING: '调整解绑支付单',
  };
  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (t) => t || '-',
      width: 50,
    },
    {
      title: '操作内容',
      dataIndex: 'operatorContent',
      key: 'operatorContent',
      render: (t) => {
        return <PopoverRowText text={t} />;
      },
      width: 300,
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      align: 'right',
      render: (t) => t || '-',
      width: 150,
    },

    {
      title: '操作时间',
      dataIndex: 'operateTime',
      key: 'operateTime',
      align: 'right',
      width: 150,
      render: (t) => moment(t).format('YYYY-MM-DD HH:mm:ss') || '-',
    },
  ];
  return (
    <Descriptions title="利益点编辑记录" style={{ marginBottom: 16 }}>
      <Descriptions.Item label="" span={3}>
        <Table
          rowClassName="skuTable-row"
          // style={
          //   dataSource?.length > 5
          //     ? { height: '440px', overflowY: 'scroll', textAlign: 'center' }
          //     : { textAlign: 'center' }
          // }
          scroll={{ y: 500 }}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </Descriptions.Item>
    </Descriptions>
  );
};
export default Gift;
