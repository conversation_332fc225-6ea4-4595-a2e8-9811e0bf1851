const toList = (list) => {
  const arr = [];
  Object.entries(list).forEach((i) => {
    arr.push({
      value: i[0],
      name: i[1],
    });
  });
  return arr;
};
export enum discountType {
  FULL_REDUCTION = '满减',
  IMMEDIATE_REDUCTION = '立减',
  COUPON = '优惠券',
  OTHER = '其他',
  NONE = '无',
}
export const skuType = {
  ALL: '全部规格',
  PART: '指定规格',
};
export enum giveType {
  IMMEDIATE = '下单即送',
  REQUIREMENTS = '指定条件赠送',
}
export enum deliveryMode {
  SPOT = '现货',
  PRESALE = '预售',
}
export enum booleanType {
  true = '是',
  false = '否',
}
export enum aftersaleType {
  NONE = '无',
  NO_REASON = '7天无理由',
  FREE_FREIGHT_INSURANCE = '赠送运费险',
  OTHER = '其他',
}
export enum ActionType {
  LINK = '挂链',
  PRIMARY_EXTRA_LINK = '罗场补坑挂链',
  SECONDARY_EXTRA_LINK = '副主播补坑挂链',
  EXPLAIN = '讲解',
  PRIMARY_EXTRA_EXPLAIN = '罗场补坑讲解',
  SECONDARY_EXTRA_EXPLAIN = '副主播补坑讲解',
}
export const deliveryModeList = toList(deliveryMode);
export const booleanTypeList = [
  {
    value: 'true',
    name: '是',
  },
  {
    value: 'false',
    name: '否',
  },
];
export const aftersaleTypeList = toList(aftersaleType);
export enum FlowStatus {
  BP_CONFIRMING = '待商务确认',
  WAIT_AUDIT = '审核中',
  WAIT_LIVE = '待直播',
  ABORT_LIVE = '已掉品',
  CANCEL = '已取消',
}
export enum FlowStatusColor {
  BP_CONFIRMING = 'orange',
  WAIT_AUDIT = '#108ee9',
  WAIT_LIVE = '#87d068',
  ABORT_LIVE = '#f50',
  CANCEL = 'gray',
}
