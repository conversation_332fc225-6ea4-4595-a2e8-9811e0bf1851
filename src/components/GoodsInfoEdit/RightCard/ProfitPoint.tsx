import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, DatePicker } from 'antd';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import WithToggleModal from '@/components/WithToggleModal';
import style from '@/styles/index.module.less';
import { ModalProps } from 'antd/lib/modal';
import { useLive, useEdit } from '../hooks';
import moment from 'moment';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  deptId: string;
  id?: string;
  [key: string]: any;
  tabsValue?: string;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const { RangePicker } = DatePicker;

const ProfitPoint = (props: IProps) => {
  const {
    form,
    visible,
    onRefresh,
    deptId,
    spu,
    interestPoints,
    platformEnum,
    tabsValue,
    id,
    ...rest
  } = props;
  console.log('🚀 ~ ProfitPoint ~ props:', props);
  const { getFieldDecorator } = form;
  const { liveRun, liveList, liveLoading } = useLive((value) => {
    form.setFieldsValue({
      liveRoomIdList: value ? [value?.id] : undefined,
    });
  });

  const {
    editLoading,
    editRun,
    nonLiveSelectionEditInterestPointsRun,
    nonLiveSelectionEditInterestPointsLoading,
  } = useEdit(() => {
    // @ts-ignore
    rest?.onCancel?.();
    onRefresh?.();
  });

  const handleOk = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }
      const { liveDate, ...otherValue } = value ?? {};
      const [liveStartDate, liveEndDate] = liveDate ?? [];
      if (tabsValue === 'live') {
        editRun({
          ...otherValue,
          liveStartDate: moment(liveStartDate).format('YYYY-MM-DD'),
          liveEndDate: moment(liveEndDate).format('YYYY-MM-DD'),
          spuId: spu,
        });
      } else {
        nonLiveSelectionEditInterestPointsRun({
          id: id,
          interestPoints: otherValue?.interestPoints,
        });
      }
    });
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
    console.log('🚀 ~ useEffect ~ visible:', visible, deptId, platformEnum);
    if (visible && deptId && platformEnum) {
      liveRun({ deptId, platformEnum });
    }
  }, [visible, deptId, platformEnum]);

  return (
    <Modal
      title="编辑利益点"
      {...rest}
      visible={visible}
      width={500}
      className={style['modal-sty']}
      maskClosable={false}
      onOk={handleOk}
      confirmLoading={editLoading || nonLiveSelectionEditInterestPointsLoading}
    >
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} label="利益点" style={{ marginBottom: '10px' }}>
          {getFieldDecorator('interestPoints', {
            // rules: [{ required: true, message: '请输入利益点' }],
            initialValue: interestPoints,
          })(<Input placeholder="请输入利益点" />)}
        </Form.Item>
        {tabsValue === 'live' && (
          <Form.Item
            {...formItemLayout}
            label="直播间"
            required={false}
            style={{ marginBottom: '10px' }}
          >
            {getFieldDecorator('liveRoomIdList', {
              rules: [{ required: true, message: '请选择直播间' }],
            })(
              <Select
                loading={liveLoading}
                placeholder="请选择直播间"
                mode="multiple"
                maxTagCount={2}
                filterOption={false}
              >
                {liveList.map((item) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.name}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        )}
        {tabsValue === 'live' && (
          <Form.Item
            {...formItemLayout}
            required={false}
            label="直播日期"
            style={{ marginBottom: '10px' }}
          >
            {getFieldDecorator('liveDate', {
              rules: [{ required: true, message: '请选择日期' }],
              initialValue: [moment(), moment().add(30, 'd')],
            })(<RangePicker style={{ width: '100%' }} allowClear={false} />)}
            <div
              style={{ fontSize: '12px', lineHeight: '18px', color: '#999999', marginTop: '8px' }}
            >
              填写直播间&直播日期范围后，朋友云将会更新同商品ID所选范围内的利益点。
            </div>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(ProfitPoint));
