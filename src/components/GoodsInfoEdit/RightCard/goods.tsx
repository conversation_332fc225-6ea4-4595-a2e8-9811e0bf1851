import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Table,
  Input,
  Form,
  Popover,
  message,
  Tooltip,
  Modal,
  Icon,
} from 'antd';
const { TextArea } = Input;
import BrandList from '../component/brandList';
import { copyText } from '@/utils/moduleUtils';
import styles from '../index.module.less';
import moment from 'moment';
import ImportantIcon from './ImportantIcon';
import { LinkPromotionCheckStatusEnum } from '@/pages/selection-flow-board/hooks';
import ImageItem from '@/components/FileUploadSouceId/components/ImageItem';
import {
  LiveGoodsInfoResult,
  getXpRefreshFavorableRate,
  getXpRefreshShopPoints,
  getCcRefreshFavorableRate,
  getCcRefreshShopPoints,
} from '@/services/yml/choiceList';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import style from './goods.module.less';
import { numberContrast } from '@/utils/formatTime';
import { debounce, throttle } from 'lodash';
import { AuthWrapper, Const } from 'qmkit';
import { refreshZeroRate } from '../services/api';
import { responseWithResultAsync } from '../component/LeftCardLeftForm';
import { refreshSoldSalesVolume } from '../services/yml';
import { numberToColor } from '@/pages/selection-flow-board/utils';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import { useRefreshGoodsQualityScore } from '../hook';
import ProfitPoint from './ProfitPoint';
import { useAI } from '../hooks';
import AICreate from '@/pages/selection-flow-board/components/AICategory/AICreate';
import CategoryVehicle from './CategoryVehicle';
import {
  getCategoryCartBySelectionId,
  GetCategoryCartBySelectionIdResult,
} from '@/pages/selection-flow-board/services/aiCategoryCar';
import { useRequest } from 'ahooks';

type propsType = {
  type: string;
  info?: LiveGoodsInfoResult;
  form: WrappedFormUtils;
  entry?: 'xuanpin' | 'changci';
  tabsValue?: string;
  onRefresh: any;
};
const format = 'YYYY-MM-DD HH:mm:ss';
const Goods: React.FC<propsType> = (props) => {
  const { type, info, form, entry, tabsValue, onRefresh } = props;
  // const { favorableRate,favorableRateRefreshTime} = info;
  const { getFieldDecorator } = form;
  // const onChange = () => {};
  // 好评率相关
  const [favorableRate, setFavorableRate] = useState<string>();
  const [favorableRateRefreshTime, setFavorableRateRefreshTime] = useState<string>();
  const [transform, setTransform] = useState(false);
  // 店铺分相关
  const [shopPoints, setShopPoints] = useState<string>();
  const [shopPointsRefreshTime, setShopPointsRefreshTime] = useState<string>();
  const [transformShop, setTransformShop] = useState(false);
  const [transformNinet, setTransformNinet] = useState(false);
  const [ninetyDayMap, setNinetyDayMap] = useState<{
    ninetyDayLiveCount?: number | string;
    ninetyDayLiveZeroCount?: number | string;
    ninetyDayLiveZeroRate?: number | string;
  }>();
  const [goodsQualityScore, setGoodsQualityScore] = useState<any>();

  const [categoryCar, setCategoryCar] = useState<GetCategoryCartBySelectionIdResult>({});

  const { run, loading } = useRequest(getCategoryCartBySelectionId, {
    onSuccess: ({ res }) => {
      if (!res?.success) {
        message.error(res?.message);
        return;
      }
      setCategoryCar(res?.result || {});
    },
  });

  const content = (
    <div>
      <p>推广链接ID：{info?.promotionLinkId}</p>
      <p>
        链接有效期：
        {info?.linkValidityStartTime
          ? moment(info?.linkValidityStartTime).format(format) +
            '~' +
            moment(info?.linkValidityEndTime).format(format)
          : '-'}
      </p>
      {info?.platformSource === 'TB' && (
        <p>
          链接确认状态：
          {info?.linkCheckStatus ? LinkPromotionCheckStatusEnum[info.linkCheckStatus] : '-'}
        </p>
      )}
      {!!(info?.linkCheckRemark && info?.platformSource === 'TB') && (
        <p>备注：{info?.linkCheckRemark}</p>
      )}
    </div>
  );
  // 刷新好评率
  const getRefreshFavorableRate = debounce(() => {
    const urlApi = entry === 'xuanpin' ? getXpRefreshFavorableRate : getCcRefreshFavorableRate; // 选品池和其他详情页调的接口不同
    setTransform(true);
    urlApi({ id: info?.id })
      .then((res) => {
        if (res.res.code === '200') {
          setFavorableRate(res.res.result.favorableRate);
          setFavorableRateRefreshTime(res.res.result.favorableRateRefreshTime);
        } else {
          message.error(res.res.message);
        }
      })
      .finally(() => {
        setTransform(false);
      });
  }, 300);
  // 刷新店铺分
  const getRefreshShopPoints = debounce(() => {
    const urlApi = entry === 'xuanpin' ? getXpRefreshShopPoints : getCcRefreshShopPoints; // 选品池和其他详情页调的接口不同
    setTransformShop(true);
    urlApi({ id: info?.id })
      .then((res) => {
        if (res.res.code === '200') {
          setShopPoints(res.res.result.shopPoints);
          setShopPointsRefreshTime(res.res.result.shopPointsRefreshTime);
        } else {
          message.error(res.res.message);
        }
      })
      .finally(() => {
        setTransformShop(false);
      });
  }, 300);

  const { refreshGoodsQualityScoreRun, refreshGoodsQualityScoreLoading } =
    useRefreshGoodsQualityScore({
      entry: entry as 'changci',
      cb: (value) => {
        setGoodsQualityScore(value);
      },
    });

  const refreshNinetDayMap = async () => {
    setTransformNinet(true);
    const result = await responseWithResultAsync({
      request: refreshZeroRate,
      params: { id: info?.id },
    });
    setTransformNinet(false);
    setNinetyDayMap({
      ninetyDayLiveCount: result?.ninetyDayLiveCount,
      ninetyDayLiveZeroCount: result?.ninetyDayLiveZeroCount,
      ninetyDayLiveZeroRate: result?.ninetyDayLiveZeroRate,
    });
  };
  const [soldSalesVolume, setSoldSalesVolume] = useState<number | string>();
  const [transformSold, setTransformSold] = useState(false);
  const getSoldSalesVolume = async () => {
    setTransformSold(true);
    const result = await responseWithResultAsync({
      request: refreshSoldSalesVolume,
      params: { id: info?.id },
    });
    setTransformSold(false);
    if (!result) return;
    if (!result?.result && result) {
      message.error('更新失败');
      return;
    }
    setSoldSalesVolume(result?.soldSalesVolume);
  };

  useEffect(() => {
    // 在组件挂载后设置字段值
    setFavorableRate(info?.favorableRate || '');
    setFavorableRateRefreshTime(info?.favorableRateRefreshTime || '');
    setShopPoints(info?.shopPoints || '');
    setShopPointsRefreshTime(info?.shopPointsRefreshTime || '');
    setNinetyDayMap({
      ninetyDayLiveCount: info?.ninetyDayLiveCount,
      ninetyDayLiveZeroCount: info?.ninetyDayLiveZeroCount,
      ninetyDayLiveZeroRate: info?.ninetyDayLiveZeroRate,
    });
    setSoldSalesVolume(info?.soldSalesVolume);
    setGoodsQualityScore(info?.goodsQualityScore);
    info?.id && run({ id: info?.id });
  }, [info]);
  const DEFAULT_DEPTID = Const.NODE_SERVER_ENV === 'test' ? '35' : '1';

  const {
    createAIRun,
    createAILoading,
    createAIVisible,
    openAIModal,
    closeAIModal,
    nonLiveSelectionCreateInterestPointsRun,
    nonLiveSelectionCreateInterestPointsLoading,
    pollingLoading,
  } = useAI(() => {
    onRefresh?.();
  });

  // Implement the handleAICreate function
  const handleAICreate = () => {
    if (tabsValue === 'live') {
      openAIModal();
    } else {
      nonLiveSelectionCreateInterestPointsRun({
        id: info?.id,
      });
    }
  };

  return (
    <>
      <Form layout="inline" className={styles['goods-form']}>
        <Descriptions title="商品信息" className={styles['goods-descriptions']}>
          <Descriptions.Item label="商品编号">{info?.spuNo}</Descriptions.Item>
          <Descriptions.Item label="平台商品ID">
            {info?.platformSpuId || info?.platformSpuIdBak}
            {(info?.platformSpuId || info?.platformSpuIdBak) && (
              <a
                onClick={() => {
                  copyText((info?.platformSpuId || info?.platformSpuIdBak)!);
                }}
              >
                复制
              </a>
            )}
          </Descriptions.Item>
          {entry === 'xuanpin' && (
            <Descriptions.Item label="选品池商品ID">{info?.id}</Descriptions.Item>
          )}
          <Descriptions.Item label="商品参考链接">
            {(info?.link || info?.linkBak) && (
              <div style={{ display: 'flex' }}>
                <a
                  style={{
                    textOverflow: 'ellipsis',
                    width: 60,
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    display: 'inline-block',
                  }}
                  target="_blank"
                  href={info?.link || info?.linkBak}
                >
                  {info?.link || info?.linkBak}
                </a>
                <a
                  onClick={() => {
                    copyText(info?.link ?? info?.linkBak!);
                  }}
                >
                  复制
                </a>
              </div>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="行业大类">{info?.standardCateName}</Descriptions.Item>
          <Descriptions.Item label="类目">{info?.cateNamePath}</Descriptions.Item>
          <Descriptions.Item label="品牌">
            {type === 'edit' && !info?.brandName ? (
              <Form.Item>
                {getFieldDecorator('brandName', {
                  initialValue: { label: info?.brandName, key: info?.brandId },
                  rules: [{ required: true, message: '品牌' }],
                })(<BrandList></BrandList>)}
              </Form.Item>
            ) : (
              info?.brandName
            )}
          </Descriptions.Item>
          <Descriptions.Item label="店铺">{info?.platformShopName}</Descriptions.Item>
          <Descriptions.Item label="店铺ID">{info?.platformShopId}</Descriptions.Item>
          <Descriptions.Item label="上播链接">
            {info?.platformSource !== 'TB' ||
            info?.linkCheckStatus === 'CAN_PROMOTION' ||
            entry === 'changci' ? (
              <Popover content={content} title="上播链接信息" trigger="hover">
                {info?.promotionLink &&
                (info?.platformSource !== 'TB' ||
                  info?.linkCheckStatus === 'CAN_PROMOTION' ||
                  entry === 'changci') ? (
                  <div style={{ display: 'flex' }}>
                    <a
                      style={{
                        textOverflow: 'ellipsis',
                        width: 60,
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        display: 'inline-block',
                      }}
                      target="_blank"
                      href={info?.promotionLink}
                    >
                      {info?.promotionLink}
                    </a>
                    {!!(
                      info?.linkCheckStatus === 'CAN_PROMOTION' ||
                      info?.platformSource !== 'TB' ||
                      entry === 'changci'
                    ) && (
                      <a
                        onClick={() => {
                          copyText(info?.promotionLink!);
                        }}
                      >
                        复制
                      </a>
                    )}
                  </div>
                ) : (
                  '-'
                )}
              </Popover>
            ) : (
              '-'
            )}
          </Descriptions.Item>
          {entry === 'changci' && typeof info?.luckyProductFlag === 'boolean' ? (
            <Descriptions.Item label="是否福袋商品">
              {info?.luckyProductFlag ? '是' : '否'}
            </Descriptions.Item>
          ) : (
            <></>
          )}

          {entry === 'xuanpin' ? (
            <>
              <Descriptions.Item label="商品好评率">
                <span
                  className={
                    numberContrast({ num1: info?.standardFavorableRate, num2: info?.favorableRate })
                      ? style['error-number']
                      : ''
                  }
                >
                  {favorableRateRefreshTime ? (
                    <Popover
                      content={`更新时间：${moment(favorableRateRefreshTime).format(format)}`}
                    >
                      {favorableRate ? `${(Number(favorableRate) * 100).toFixed(2)}%` : 0}
                    </Popover>
                  ) : (
                    <>{favorableRate ? `${(Number(favorableRate) * 100).toFixed(2)}%` : 0}</>
                  )}
                </span>
                {info?.platformSource === 'DY' ? (
                  <Popover content={'更新评分'}>
                    <span
                      className={`iconfont icon-jiazai ${
                        transform ? style['icon-transform'] : null
                      }`}
                      style={{
                        color: '#204EFF',
                        marginLeft: '4px',
                        position: 'relative',
                        top: '1px',
                        cursor: 'pointer',
                      }}
                      onClick={getRefreshFavorableRate}
                    ></span>
                  </Popover>
                ) : (
                  <></>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="店铺体验分" span={1}>
                <span
                  className={
                    numberContrast({ num1: info?.standardStore, num2: info?.shopPoints })
                      ? style['error-number']
                      : ''
                  }
                >
                  {shopPointsRefreshTime ? (
                    <Popover content={`更新时间：${moment(shopPointsRefreshTime).format(format)}`}>
                      {shopPoints || 0}
                    </Popover>
                  ) : (
                    <>{shopPoints || 0}</>
                  )}
                </span>
                {info?.platformSource === 'DY' ? (
                  <Popover content={'更新评分'}>
                    <span
                      className={`iconfont icon-jiazai ${
                        transformShop ? style['icon-transform-shopPoints'] : null
                      }`}
                      style={{
                        color: '#204EFF',
                        marginLeft: '4px',
                        position: 'relative',
                        top: '1px',
                        cursor: 'pointer',
                      }}
                      onClick={getRefreshShopPoints}
                    ></span>
                  </Popover>
                ) : (
                  <></>
                )}
              </Descriptions.Item>
            </>
          ) : (
            <>
              {info?.platformSource === 'DY' ? (
                <>
                  <Descriptions.Item label="商品好评率">
                    <span
                      className={
                        numberContrast({
                          num1: info?.standardFavorableRate,
                          num2: info?.favorableRate,
                        })
                          ? style['error-number']
                          : ''
                      }
                    >
                      {favorableRateRefreshTime ? (
                        <Popover
                          content={`更新时间：${moment(favorableRateRefreshTime).format(format)}`}
                        >
                          {favorableRate ? `${(Number(favorableRate) * 100).toFixed(2)}%` : 0}
                        </Popover>
                      ) : (
                        <>{favorableRate ? `${(Number(favorableRate) * 100).toFixed(2)}%` : 0}</>
                      )}
                    </span>
                    {info?.platformSource === 'DY' ? (
                      <Popover content={'更新评分'}>
                        <span
                          className={`iconfont icon-jiazai ${
                            transform ? style['icon-transform'] : null
                          }`}
                          style={{
                            color: '#204EFF',
                            marginLeft: '4px',
                            position: 'relative',
                            top: '1px',
                            cursor: 'pointer',
                          }}
                          onClick={getRefreshFavorableRate}
                        ></span>
                      </Popover>
                    ) : (
                      <></>
                    )}
                  </Descriptions.Item>
                  <Descriptions.Item label="店铺体验分" span={1}>
                    <span
                      className={
                        numberContrast({ num1: info?.standardStore, num2: info?.shopPoints })
                          ? style['error-number']
                          : ''
                      }
                    >
                      {shopPointsRefreshTime ? (
                        <Popover
                          content={`更新时间：${moment(shopPointsRefreshTime).format(format)}`}
                        >
                          {shopPoints || 0}
                        </Popover>
                      ) : (
                        <>{shopPoints || 0}</>
                      )}
                    </span>
                    {info?.platformSource === 'DY' ? (
                      <Popover content={'更新评分'}>
                        <span
                          className={`iconfont icon-jiazai ${
                            transformShop ? style['icon-transform-shopPoints'] : null
                          }`}
                          style={{
                            color: '#204EFF',
                            marginLeft: '4px',
                            position: 'relative',
                            top: '1px',
                            cursor: 'pointer',
                          }}
                          onClick={getRefreshShopPoints}
                        ></span>
                      </Popover>
                    ) : (
                      <></>
                    )}
                  </Descriptions.Item>
                </>
              ) : (
                <></>
              )}
              <Descriptions.Item label="限制库存" span={1}>
                {info?.preSaleStock}
              </Descriptions.Item>
            </>
          )}
          {info?.isDisplayQualityScore && (
            <Descriptions.Item label="商品质量分" span={1}>
              <span style={{ color: numberToColor(goodsQualityScore as number) }}>
                {goodsQualityScore}
              </span>
              <Popover content={'更新商品质量分'}>
                <span
                  className={`iconfont icon-jiazai ${
                    transformShop ? style['icon-transform-shopPoints'] : null
                  }`}
                  style={{
                    color: '#204EFF',
                    marginLeft: '4px',
                    position: 'relative',
                    top: '1px',
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    refreshGoodsQualityScoreRun(info?.id);
                  }}
                ></span>
              </Popover>
            </Descriptions.Item>
          )}
          {entry === 'changci' && tabsValue === 'live' ? (
            <Descriptions.Item label="类目车" span={1}>
              <div style={{ display: 'flex', alignItems: 'center', wordBreak: 'break-all' }}>
                <Popover
                  title="类目车"
                  content={
                    <div style={{ width: '300px', wordBreak: 'break-all', overflow: 'hidden' }}>
                      {categoryCar?.content || '-'}
                    </div>
                  }
                >
                  <p
                    style={{
                      wordBreak: 'break-all',
                      width: '200px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {categoryCar?.content || '-'}
                  </p>
                </Popover>

                <AuthWrapper functionName="f_selection_flow_board_category_vehicle_create_id">
                  <AICreate
                    platformSpuId={info?.platformSpuId}
                    type="AI_RESET_SAME_SPU_ID"
                    onRefresh={() => {
                      run({ id: info?.id });
                    }}
                    currentSelectionId={info?.id}
                  >
                    <img
                      src="https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images/icon/Ai.png"
                      alt=""
                      style={{
                        width: '16px',
                        height: '16px',
                        marginLeft: '4px',
                        marginRight: '4px',
                        borderRadius: '2px',
                        cursor: 'pointer',
                      }}
                    />
                  </AICreate>
                </AuthWrapper>

                <AuthWrapper functionName="f_selection_flow_board_category_vehicle_detail">
                  <CategoryVehicle
                    platformSpuId={info?.platformSpuId}
                    id={info?.id}
                    type="AI_RESET_SAME_SPU_ID"
                    onRefresh={() => {
                      run({ id: info?.id });
                    }}
                  >
                    <Tooltip title="编辑AI类目车">
                      <span
                        className="icon iconfont icon-bianji1"
                        style={{ cursor: 'pointer' }}
                      ></span>
                    </Tooltip>
                  </CategoryVehicle>
                </AuthWrapper>
              </div>
            </Descriptions.Item>
          ) : (
            <></>
          )}
        </Descriptions>
        {info?.deptId == DEFAULT_DEPTID && info?.platformSource === 'DY' && entry === 'changci' && (
          <Descriptions>
            <Descriptions.Item label="低效品占比" span={1}>
              <span>
                {ninetyDayMap?.ninetyDayLiveZeroRate !== null
                  ? `${(Number(ninetyDayMap?.ninetyDayLiveZeroRate) * 100).toFixed(2)}%`
                  : '-'}
              </span>
              {/* {entry === 'xuanpin' && (
              <span
                className={`iconfont icon-jiazai ${
                  transformNinet ? style['icon-transform-shopPoints'] : null
                }`}
                style={{
                  color: '#204EFF',
                  marginLeft: '4px',
                  position: 'relative',
                  top: '1px',
                  cursor: 'pointer',
                }}
                onClick={refreshNinetDayMap}
              ></span>
            )} */}
            </Descriptions.Item>
            <Descriptions.Item label="已播次数" span={1}>
              {ninetyDayMap?.ninetyDayLiveCount ?? '-'}
            </Descriptions.Item>
            <Descriptions.Item label="销量为0场次数" span={1}>
              {ninetyDayMap?.ninetyDayLiveZeroCount ?? '-'}
            </Descriptions.Item>
          </Descriptions>
        )}
        {/* 单独一行Descriptions */}
        <Descriptions>
          {entry === 'xuanpin' ? (
            <></>
          ) : (
            <>
              {info?.platformSource === 'DY' &&
                typeof info?.luckyProductFlag !== 'boolean' && ( // 当直播平台为抖音时，上方会显示商品好评率及店铺体验分，上播商品ID需要强制换行
                  <div></div>
                )}
              <Descriptions.Item label="上播商品ID" span={1}>
                {info?.livePlatformSpuId}
                {info?.livePlatformSpuId && (
                  <a
                    onClick={() => {
                      copyText(info!.livePlatformSpuId!);
                    }}
                  >
                    复制
                  </a>
                )}
              </Descriptions.Item>
            </>
          )}
          {entry === 'changci' && info?.platformSource === 'TB' && tabsValue === 'live' && (
            <Descriptions.Item label="已售" span={1}>
              <span>{soldSalesVolume ?? 0}</span>
              <span
                className={`iconfont icon-jiazai ${
                  transformSold ? style['icon-transform-shopPoints'] : null
                }`}
                style={{
                  color: '#204EFF',
                  marginLeft: '4px',
                  position: 'relative',
                  top: '1px',
                  cursor: 'pointer',
                }}
                onClick={getSoldSalesVolume}
              ></span>
            </Descriptions.Item>
          )}
        </Descriptions>

        {/* 单独一行Descriptions */}
        <Descriptions style={{ marginBottom: 16 }}>
          <Descriptions.Item
            label={type === 'edit' ? '' : <ImportantIcon></ImportantIcon>}
            span={1}
          >
            {type === 'edit' ? (
              <Form.Item label={'商品主要卖点'}>
                {getFieldDecorator('sellingPoints', {
                  initialValue:
                    entry === 'xuanpin' ? info?.selectionContent?.advantages : info?.sellingPoints,
                  rules: [{ required: false, message: '商品主要卖点' }],
                })(
                  <TextArea
                    style={{ width: '800px' }}
                    maxLength={2000}
                    placeholder="商品主要卖点"
                    allowClear
                    autoSize={{ minRows: 3 }}
                  />,
                )}
              </Form.Item>
            ) : entry === 'xuanpin' ? (
              info?.selectionContent?.advantages
            ) : (
              info?.sellingPoints
            )}
          </Descriptions.Item>
          {entry === 'xuanpin' && (
            <Descriptions.Item label="利益点" span={2}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {info?.interestPoints || '-'}
                <AuthWrapper functionName="f_consumer_benefit_ai_create">
                  <Tooltip title="AI生成利益点">
                    <img
                      src="https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images/icon/Ai.png"
                      alt=""
                      style={{
                        width: '16px',
                        height: '16px',
                        marginLeft: '4px',
                        marginRight: '4px',
                        borderRadius: '2px',
                        cursor: 'pointer',
                      }}
                      onClick={handleAICreate}
                    />
                  </Tooltip>
                </AuthWrapper>
                <AuthWrapper functionName="f_consumer_benefit_edit">
                  <ProfitPoint
                    deptId={info?.deptId}
                    onRefresh={onRefresh}
                    spu={info?.spuId}
                    interestPoints={info?.interestPoints}
                    platformEnum={info?.platformSource}
                    tabsValue={tabsValue}
                  >
                    <Tooltip title="编辑利益点">
                      <span
                        className="icon iconfont icon-bianji1"
                        style={{ cursor: 'pointer' }}
                      ></span>
                    </Tooltip>
                  </ProfitPoint>
                </AuthWrapper>
              </div>
            </Descriptions.Item>
          )}
          <Descriptions.Item label={type === 'edit' ? '' : '重点展示需求'} span={3}>
            {type === 'edit' ? (
              <Form.Item label="重点展示需求">
                {getFieldDecorator('spuFocus', {
                  initialValue:
                    entry === 'xuanpin' ? info?.selectionContent?.highlights : info?.spuFocus,
                  rules: [{ required: false, message: '重点展示需求' }],
                })(
                  <TextArea
                    style={{ width: '800px' }}
                    maxLength={500}
                    placeholder="重点展示需求"
                    allowClear
                    autoSize={{ minRows: 3 }}
                  />,
                )}
              </Form.Item>
            ) : entry === 'xuanpin' ? (
              info?.selectionContent?.highlights
            ) : (
              info?.spuFocus
            )}
          </Descriptions.Item>
          {entry === 'changci' && (
            <Descriptions.Item label="重点展示需求（附件）" span={3}>
              {
                <section style={{ display: 'flex', flexWrap: 'wrap' }}>
                  {info?.spuFocusResources?.map((item) => {
                    return (
                      <ImageItem src={{ url: item?.resourceUrl, resourceId: item?.resourceId }} />
                    );
                  })}
                </section>
              }
            </Descriptions.Item>
          )}
        </Descriptions>
        {entry === 'changci' && (
          <Descriptions
            title="选品信息"
            style={{ marginBottom: 16 }}
            className={styles['goods-descriptions']}
          >
            <Descriptions.Item label="是否为反需">
              {info?.reverseDemandTag ? '是' : '否'}
            </Descriptions.Item>
            <Descriptions.Item label="反需人员" span={2}>
              {info?.reverseDemandName || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="选品备注">{info?.selectionRemark}</Descriptions.Item>
            <Descriptions.Item label="利益点">
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {info?.interestPoints || '-'}
                <AuthWrapper functionName="f_consumer_benefit_ai_create">
                  <Tooltip title="AI生成利益点">
                    <img
                      src="https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images/icon/Ai.png"
                      alt=""
                      style={{
                        width: '16px',
                        height: '16px',
                        marginLeft: '4px',
                        marginRight: '4px',
                        borderRadius: '2px',
                        cursor: 'pointer',
                      }}
                      onClick={handleAICreate}
                    />
                  </Tooltip>
                </AuthWrapper>
                <AuthWrapper functionName="f_consumer_benefit_edit">
                  <ProfitPoint
                    deptId={info?.deptId}
                    onRefresh={onRefresh}
                    spu={info?.spuId}
                    interestPoints={info?.interestPoints}
                    platformEnum={info?.platformSource}
                    tabsValue={tabsValue}
                    id={info?.id}
                  >
                    <Tooltip title="编辑利益点">
                      <span
                        className="icon iconfont icon-bianji1"
                        style={{ cursor: 'pointer' }}
                      ></span>
                    </Tooltip>
                  </ProfitPoint>
                </AuthWrapper>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label={type === 'edit' ? '' : '备注'} span={1}>
              {type === 'edit' && info?.status === 'BP_CONFIRMING' ? (
                <Form.Item label="备注">
                  {getFieldDecorator('comment', {
                    initialValue: info?.comment,
                    // rules: [{ required: false, message: '备注' }],
                  })(<Input style={{ width: '200px' }} maxLength={100} placeholder="备注" />)}
                </Form.Item>
              ) : (
                <p
                  style={{
                    width: '200px',
                    wordBreak: 'break-all',
                    display: 'flex',
                    flexWrap: 'wrap',
                  }}
                >
                  {info?.comment}
                </p>
              )}
            </Descriptions.Item>
            {/* <Descriptions.Item label="是否保比">
            {info?.isPromiseRatio === true ? '是' : info?.isPromiseRatio === false ? '否' : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="保比(ROI)">
            <p
              style={{
                width: '200px',
                wordBreak: 'break-all',
                display: 'flex',
                flexWrap: 'wrap',
              }}
            >
              {info?.promiseRatioValue || '-'}
            </p>
          </Descriptions.Item> */}
            <Descriptions.Item label="收样登记">
              <p
                style={{
                  width: '200px',
                  wordBreak: 'break-all',
                  display: 'flex',
                  flexWrap: 'wrap',
                }}
              >
                {info?.receiveSampleRegister || '-'}
              </p>
            </Descriptions.Item>
            <Descriptions.Item label="退样登记">
              <p
                style={{
                  width: '200px',
                  wordBreak: 'break-all',
                  display: 'flex',
                  flexWrap: 'wrap',
                }}
              >
                {info?.returnSampleRegister || '-'}
              </p>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Form>
      <Modal
        visible={createAIVisible}
        width="416px"
        closable={false}
        wrapClassName={`${styles['ai-Modal']}`}
        onOk={() => {
          if (tabsValue === 'live') {
            createAIRun({
              // @ts-ignore
              source: info?.platformSource,
              spuId: info?.spuId,
            });
          }
        }}
        okButtonProps={{
          loading: createAILoading || nonLiveSelectionCreateInterestPointsLoading || pollingLoading,
        }}
        onCancel={closeAIModal}
      >
        <div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Icon
              style={{ fontSize: '22px', color: '#FAAD14', marginRight: '16px' }}
              type="question-circle"
            />
            <span style={{ color: '#444', font: '15px', fontWeight: '500' }}>
              请确认是否通过AI生成利益点?
            </span>
          </div>
          <div
            style={{
              marginTop: '8px',
              marginLeft: '38px',
              fontSize: '13px',
              color: 'rgba(0,0,0,0.6)',
            }}
          >
            AI生成的利益点信息仅供参考，并且会覆盖同商品ID的选品池和30天内的场次货盘中已填写的利益点信息,请确认是否执行该操作。
          </div>
        </div>
      </Modal>
    </>
  );
};
export default Goods;
