import React, { useEffect, useState } from 'react';
import { ActionType } from './type';
import { Modal, Form, Input, Drawer, Button, message, Select } from 'antd';
const { Option } = Select;
const choiceList = (props) => {
  const [option, setOptions] = useState([]);
  useEffect(() => {
    const arr = Object.entries(ActionType).map((i) => {
      return { value: i[0], label: i[1] };
    });
    setOptions(arr);
  }, []);
  return (
    <Select style={{ width: 190 }} {...props}>
      {option.map((i) => {
        return (
          <Option value={i.value} key={i.value}>
            {i.label}
          </Option>
        );
      })}
    </Select>
  );
};
export default choiceList;
