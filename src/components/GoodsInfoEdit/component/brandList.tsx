import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import { brandList } from '@/services/yml/choose/index';
const { Option } = Select;

function BrandSelect(props) {
  const [options, setOptions] = useState([]);

  const handleSearch = (value) => {
    brandList({ name: value }).then((res) => {
      if (res?.res?.code === '200') {
        const list = res?.res?.result;
        // console.log(list);
        setOptions(list);
      } else {
        setOptions([]);
      }
      // const data = response.data;
      // const options = data.map((brand) => ({
      //   value: brand.id,
      //   label: brand.name,
      // }));
    });
  };
  useEffect(() => {
    handleSearch();
  }, []);

  return (
    <Select
      showSearch
      allowClear
      placeholder="请选择品牌"
      onSearch={handleSearch}
      filterOption={false}
      labelInValue
      style={{ width: '100px' }}
      //   onChange={(e) => {
      //     console.log(e);
      //     props.onChange && props.onChange(Option);
      //   }}
      {...props}
    >
      {options.map((option) => (
        <Option key={option.id} value={option.id}>
          {option.name}
        </Option>
      ))}
    </Select>
  );
}

export default BrandSelect;
