import React, { useEffect, useState } from 'react';

import { Modal, Form, Input, Drawer, Button, message, Select } from 'antd';
const { Option } = Select;
const cascadeList = (props) => {
  const [option, setOptions] = useState([]);
  useEffect(() => {
    const arr = [];
    setOptions(arr);
  }, []);
  return (
    <Select style={{ width: 120 }}>
      {option.map((i) => {
        return <Option value={i.value}>{i.label}</Option>;
      })}
    </Select>
  );
};
export default cascadeList;
