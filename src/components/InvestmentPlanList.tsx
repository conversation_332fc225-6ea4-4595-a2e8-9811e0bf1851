import React, { useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';
import { iasmInvestmentPlanPageAuth } from '@/services/yml/investment-plan-list';

const { Option } = Select;
interface UserListProps {
  onChange?: (value: any) => void; // onChange 是一个函数，接受一个参数并且没有返回值
  value?: any; // value 可以是任意类型
  labelInValue?: boolean; // value 可以是任意类型
  valueIsInfo?: boolean; // value 可以是任意类型
  buIds: string[]; // buIds 是一个字符串数组
  platform?: string;
}
const UserList = (props: UserListProps) => {
  const { onChange, value, buIds, platform, labelInValue, valueIsInfo } = props;
  const [options, setOptions] = useState([]);
  const [fetching, setFetching] = useState(false);
  const [list, setList] = useState([]);
  const onSearch = (value?: string) => {
    iasmInvestmentPlanPageAuth({
      buIds: buIds,
      statusSet: ['STARTED'],
      typeSet: ['LIMIT_TIME'],
      keyword: value,
      platformSet: platform ? [platform] : [],
      current: 1,
      size: 50,
    }).then(({ res }) => {
      // console.log(res);
      if (res?.code === '200') {
        const arr = res.result?.map((i) => {
          return {
            value: valueIsInfo ? JSON.stringify(i) : i.id,
            label: i.title,
          };
        });
        setList([...arr]);
      }
    });
  };
  const handleSearch = debounce((value) => {
    onSearch(value);
  }, 300);

  useEffect(() => {
    if (props?.buIds) {
      onSearch();
    }
  }, [buIds]);
  useEffect(() => {
    list.length > 0 && setOptions([...list]);
  }, [list]);
  return (
    <Select
      allowClear
      showSearch
      placeholder="请选择"
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      labelInValue={labelInValue}
      value={value}
      onSearch={handleSearch}
      {...props}
      onChange={onChange}
      notFoundContent={fetching ? <Spin size="small" /> : null}
    >
      {options.map((item, index) => (
        <Option key={index} value={item.value}>
          {item.label}
        </Option>
      ))}
    </Select>
  );
};

export default UserList;
