import { message } from 'antd';
import React, { ReactChild, useState } from 'react';
import { AuthWrapper } from 'qmkit';
import { EncryptTypeEnum, EncryptTypeObj } from './utils';
import { getMarkInfo } from './services/index';
import PopverContent from '@/components/PopverContent';

interface IProps {
  data: string;
  type: EncryptTypeEnum;
  id: string;
}

const Encrypt = (props: IProps) => {
  const { type, id, data } = props;
  const [decryptData, setDecryptData] = useState({
    contactEmail: null,
    contactMobile: null,
    contactWxNo: null,
    employeeMobile: null,
    qualificationMobile: null,
  });
  const handleDecrypt = () => {
    getMarkInfo({
      tenantId: id,
      maskFiled: type,
    }).then(({ res }) => {
      if (res.success) {
        const key = EncryptTypeObj[type];
        setDecryptData({ ...decryptData, [key]: res.result[key] });
        return;
      }
      message.error(res.message || '查看失败');
    });
  };

  if (decryptData[EncryptTypeObj[type]]) {
    return <PopverContent title={decryptData[EncryptTypeObj[type]]}></PopverContent> || '-';
  }
  return (
    <>
      {props?.data ? (
        <>
          <div style={{ display: 'flex' }}>
            <PopverContent title={props.data}></PopverContent>
            {
              <AuthWrapper functionName="f_supplier-view-mark">
                <a onClick={handleDecrypt} className="ml-4">
                  查看
                </a>
              </AuthWrapper>
            }
          </div>
        </>
      ) : (
        '-'
      )}
    </>
  );
};

export default Encrypt;
