import React, { useEffect, useState, useImperativeHandle } from 'react';
import { Form, Input, Icon, Radio, message, Tooltip } from 'antd';
import { Modal, Popover, InputNumber } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { FormComponentProps } from 'antd/es/form';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import '../index.less';
import { getShopInfo, addShop, editShopBrandInfo } from '@/services/yml/shop/index';
interface PRPOS extends ModalProps, FormComponentProps {
  id?: number | string;
  info?: object;
  reset: () => void;
  onSearch?: any;
  onRef?: any;
  supplierId?: string | number;
}
export const styles = {
  box: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  } as any,
  plus: {
    color: '#999',
    fontSize: '28px',
  },
};

const ModalDy: React.FC<PRPOS & WithToggleModalProps> = (props) => {
  const { id, onSearch, form, onRef, supplierId } = props;
  const { getFieldDecorator, getFieldValue } = form;
  const [visible, setVisible] = useState(false);
  useEffect(() => {
    console.log(props);
    id && getShop();
  }, []);
  const [shopInfo, setShopInfo] = useState({ id: '' });
  const getShop = async () => {
    const res = await getShopInfo({ id: id });
    console.log(res);
    if (res?.res?.code === '200') {
      const info = res.res.result;
      setShopInfo({ ...info });
    } else {
      message.error(res.res.message);
    }
  };
  const onConfirm = () => {
    form.validateFields(async (err, values) => {
      if (!err) {
        const params = {
          ...values,
          supplierId,
        };
        shopInfo.id ? (params.id = shopInfo.id) : '';
        const res = await (shopInfo.id ? editShopBrandInfo(params) : addShop(params));
        if (res?.res?.code == '200') {
          message.success('操作成功');
          onCancel?.();
          onSearch && onSearch({});
        } else {
          message.error(res.res.message);
        }
      }
    });
  };
  const onCancel = () => {
    setVisible(false);
  };
  const showModal = () => {
    setVisible(true);
  };
  useImperativeHandle(onRef, () => ({
    open: showModal,
  }));

  return (
    <Modal
      title={
        <div>
          {id ? '编辑店铺' : '新建店铺'}
          <Tooltip title="若您选择新建抖音商品需要先新建抖音店铺">
            <Icon type="question-circle" />
          </Tooltip>
        </div>
      }
      onOk={() => {
        onConfirm();
      }}
      onCancel={onCancel}
      visible={visible}
    >
      <div className="goodInfo-contain">
        <Form labelAlign="left" labelCol={{ span: 4 }} wrapperCol={{ span: 17 }}>
          <Form.Item label="平台:">
            {getFieldDecorator('shopSourceTypeEnum', {
              initialValue: 'DY_SELF',
              rules: [{ required: true, message: '请选项一致性' }],
            })(
              <Radio.Group name="radiogroup" style={{ marginTop: '10px' }}>
                <Radio value={'DY_SELF'}>抖音手动</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          <Form.Item label="店铺名称:">
            {getFieldDecorator('shopName', {
              rules: [{ required: true, message: '请填写店铺名称' }],
            })(<Input placeholder="完整店铺名称" maxLength={30} />)}
          </Form.Item>
          <Form.Item label="店铺ID:">
            {getFieldDecorator('doudianId', {
              rules: [{ required: true, message: '请填写店铺ID' }],
            })(
              <InputNumber
                style={{ width: '200px' }}
                precision={0}
                min={0}
                disabled={id ? true : false}
                placeholder="完整店铺ID"
                maxLength={30}
              />,
            )}
            <Popover
              placement="right"
              title={<></>}
              content={
                <img
                  className="imgSize"
                  title=""
                  src="https://befriend-rss-static-dev.oss-cn-hangzhou.aliyuncs.com/goods/spuImg/20e232dd2bd8464394a7b64adb88d4b8/WechatIMG18.jpg"
                ></img>
              }
            >
              <a style={{ marginLeft: '8px' }}>
                示例图 <Icon type="right" />
              </a>
            </Popover>
            <p style={{ fontSize: '12px', color: 'rgba(46, 52, 66, 0.45)', marginTop: '4px' }}>
              请在抖店后台店铺信息设置中查看
              <span style={{ color: 'rgba(46, 52, 66, 1' }}>店铺ID</span>;
            </p>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default Form.create()(ModalDy);
