// ai生成
import { SelectProps } from 'antd/es/select';
import {
  GetQueryByParentIdRequest,
  GetQueryByParentIdResult,
  GetSearchByNameRequest,
  GetSearchByNameResult,
} from './yml/index';

export enum PLACEMENT {
  'bottomLeft' = 'bottomLeft',
}

export type PlatformSource =
  | 'SELF'
  | 'DY'
  | 'KSXD'
  | 'TAOBAO'
  | 'JD'
  | 'PDD'
  | 'WECHAT_VIDEO'
  | 'BAIDU'
  | 'RED'
  | 'TREND_PREDICTION';

export interface CategoryItem {
  cateName: string;
  cateId: string;
  hasChildren?: number;
  cateGrade?: number;
  cateParentId?: string;
}

export interface SelectedCategory {
  cateId: string;
  cateName: string;
  hasChildren?: number;
  level: number;
  cateGrade?: number;
  cateParentId?: string;
}

export interface State {
  categoryList: CategoryItem[][];
  optionList: CategoryItem[];
  selectedCategories: SelectedCategory[];
  searchContent: string;
  isSearching: boolean;
  loading: boolean;
  fetching: boolean;
  isDropdownOpen: boolean;
  isInitialized: boolean;
}

export type Action =
  | { type: 'SET_CATEGORY_LIST'; payload: CategoryItem[][] }
  | { type: 'SET_OPTION_LIST'; payload: CategoryItem[] }
  | { type: 'SET_SELECTED_CATEGORIES'; payload: SelectedCategory[] }
  | { type: 'SET_SEARCH_CONTENT'; payload: string }
  | { type: 'SET_IS_SEARCHING'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_FETCHING'; payload: boolean }
  | { type: 'SET_DROPDOWN_OPEN'; payload: boolean }
  | { type: 'SET_INITIALIZED'; payload: boolean }
  | { type: 'ADD_CATEGORY_LEVEL'; payload: { level: number; categories: CategoryItem[] } }
  | { type: 'TOGGLE_CATEGORY_SELECTION'; payload: { category: CategoryItem; level: number } }
  | { type: 'REMOVE_CATEGORY_SELECTION'; payload: string }
  | { type: 'CLEAR_SELECTED_CATEGORIES' }
  | { type: 'RESET_STATE' };

export interface IProps extends Omit<SelectProps, 'onChange' | 'value' | 'mode'> {
  getQueryByParentId?: (params: GetQueryByParentIdRequest) => Promise<any>;
  getSearchByName?: (params: GetSearchByNameRequest) => Promise<any>;
  source?: PlatformSource;
  value?: string[];
  onChange?: (value: string[]) => void;
  showType?: 'light' | 'normal';
  width?: 'auto' | number;
  mustLastLevel?: boolean;
  status?: number;
  getSelectList?: (categories: SelectedCategory[]) => void;
  placement?: PLACEMENT;
  defaultApi?: boolean;
  disabled?: boolean;
  maxCount?: number;
  placeholder?: string;
  allowClear?: boolean;
}

export interface CategoryRef {
  clearSelection: () => void;
  getSelectedCategories: () => SelectedCategory[];
  setSelectedCategories: (categories: SelectedCategory[]) => void;
}

// 导出API类型
export type {
  GetQueryByParentIdRequest,
  GetQueryByParentIdResult,
  GetSearchByNameRequest,
  GetSearchByNameResult,
};
// 2024年12月27日 开山ai结尾共生成83行代码
