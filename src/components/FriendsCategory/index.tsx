// ai生成
import React, { useImperativeHandle, forwardRef, useCallback, useMemo } from 'react';
import { Spin, Select } from 'antd';
import { classNames } from 'web-common-modules/utils';
import {
  getQueryByParentId as defaultQueryByParentId,
  getSearchByName as defaultSearchByName,
} from './yml/index';
import { useCategoryHooks } from './hooks';
import { SearchOptions, CategoryList, FooterActions } from './components';
import { renderSelectedTags, hasParentCategory, getSelectedCategoryIds } from './utils';
import { IProps, CategoryRef, SelectedCategory } from './types';
import './index.less';

function FriendsCategory(props: IProps, ref: React.Ref<CategoryRef>): React.ReactElement {
  const {
    getQueryByParentId = defaultQueryByParentId,
    getSearchByName = defaultSearchByName,
    source,
    value = [],
    onChange,
    placeholder = '请选择类目',
    className = '',
    showType = 'normal',
    width = 200,
    mustLastLevel = false,
    status,
    getSelectList,
    placement,
    defaultApi = true,
    disabled = false,
    maxCount,
    allowClear = true,
    ...restProps
  } = props;

  // 使用自定义hook管理状态和逻辑
  const { state, dispatch, handleSearch, handleCategoryClick, handleSearchSelect, handleClearAll } =
    useCategoryHooks({
      getQueryByParentId,
      getSearchByName,
      source,
      value,
      status,
      defaultApi,
      disabled,
      maxCount,
      getSelectList,
    });

  // 确认选择
  const handleConfirm = useCallback(() => {
    if (mustLastLevel) {
      // 检查是否所有选中的类目都是最后一级
      if (hasParentCategory(state.selectedCategories)) {
        return;
      }
    }

    const selectedIds = getSelectedCategoryIds(state.selectedCategories);
    onChange?.(selectedIds);
    dispatch({ type: 'SET_DROPDOWN_OPEN', payload: false });
  }, [state.selectedCategories, mustLastLevel, onChange, dispatch]);

  // 暴露给外部的方法
  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      dispatch({ type: 'CLEAR_SELECTED_CATEGORIES' });
    },
    getSelectedCategories: () => state.selectedCategories,
    setSelectedCategories: (categories: SelectedCategory[]) => {
      dispatch({ type: 'SET_SELECTED_CATEGORIES', payload: categories });
    },
  }));

  // 渲染选中的标签
  const selectedTagsText = useMemo(() => {
    return renderSelectedTags(state.selectedCategories);
  }, [state.selectedCategories]);

  return (
    <div
      style={{ width }}
      className={classNames({
        'category-l': true,
        [className]: true,
        'selected-light': showType === 'light',
      })}
    >
      <Select
        {...restProps}
        style={{ width: '100%' }}
        mode="multiple"
        showSearch
        defaultActiveFirstOption={false}
        placeholder={placeholder}
        notFoundContent={state.fetching ? <Spin size="small" /> : null}
        filterOption={false}
        onSearch={handleSearch}
        onDropdownVisibleChange={(open) => {
          dispatch({ type: 'SET_DROPDOWN_OPEN', payload: open });
        }}
        value={selectedTagsText}
        allowClear={allowClear}
        disabled={disabled}
        maxTagCount={3}
        maxTagTextLength={10}
        dropdownRender={() => (
          <div>
            <SearchOptions state={state} onSearchSelect={handleSearchSelect} />
            <CategoryList
              state={state}
              placement={placement}
              onCategoryClick={handleCategoryClick}
            />
            <FooterActions
              state={state}
              maxCount={maxCount}
              onClearAll={handleClearAll}
              onConfirm={handleConfirm}
            />
          </div>
        )}
      />
    </div>
  );
}

export { getQueryByParentId, getSearchByName } from './yml/index';
export type { CategoryItem, SelectedCategory, PlatformSource } from './types';

export default forwardRef<CategoryRef, IProps>(FriendsCategory);
// 2024年12月27日 开山ai结尾共生成112行代码
