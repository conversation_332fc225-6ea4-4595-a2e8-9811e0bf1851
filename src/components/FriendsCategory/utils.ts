// ai生成
import { SelectedCategory } from './types';

/**
 * 渲染选中的标签文本
 * @param selectedCategories 选中的类目数组
 * @returns 标签文本字符串或undefined
 */
export const renderSelectedTags = (selectedCategories: SelectedCategory[]): string | undefined => {
  if (selectedCategories.length === 0) {
    return undefined;
  }

  return selectedCategories.map((item) => item.cateName).join(', ');
};

/**
 * 检查是否所有选中项都是最后一级
 * @param selectedCategories 选中的类目数组
 * @returns 是否有父级类目
 */
export const hasParentCategory = (selectedCategories: SelectedCategory[]): boolean => {
  return selectedCategories.some((item) => item.hasChildren === 1);
};

/**
 * 获取选中的类目ID数组
 * @param selectedCategories 选中的类目数组
 * @returns 类目ID数组
 */
export const getSelectedCategoryIds = (selectedCategories: SelectedCategory[]): string[] => {
  return selectedCategories.map((item) => item.cateId);
};

/**
 * 检查是否可以选择更多项目
 * @param selectedCategories 已选中的类目数组
 * @param maxCount 最大选择数量
 * @param targetCategoryId 目标类目ID
 * @returns 是否可以选择
 */
export const canSelectMore = (
  selectedCategories: SelectedCategory[],
  maxCount?: number,
  targetCategoryId?: string,
): boolean => {
  if (!maxCount) return true;

  if (selectedCategories.length >= maxCount) {
    // 如果已经选中了目标类目，则可以取消选择
    const isAlreadySelected = selectedCategories.some((item) => item.cateId === targetCategoryId);
    return isAlreadySelected;
  }

  return true;
};

/**
 * 根据搜索内容高亮显示文本
 * @param text 原文本
 * @param searchContent 搜索内容
 * @returns 高亮后的文本片段
 */
export const highlightText = (text: string, searchContent: string) => {
  if (!searchContent) return [text];

  const searchIndex = text.toLowerCase().indexOf(searchContent.toLowerCase());
  if (searchIndex === -1) return [text];

  return [
    text.substring(0, searchIndex),
    text.substring(searchIndex, searchIndex + searchContent.length),
    text.substring(searchIndex + searchContent.length),
  ];
};

/**
 * 获取级别名称
 * @param level 级别索引
 * @returns 级别名称
 */
export const getLevelName = (level: number): string => {
  const levelNames = ['一级类目', '二级类目', '三级类目'];
  return levelNames[level] || `${level + 1}级类目`;
};
// 2024年12月27日 开山ai结尾共生成74行代码
