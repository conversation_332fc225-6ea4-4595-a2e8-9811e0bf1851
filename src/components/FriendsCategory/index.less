.category-l {
  position: relative;
  z-index: 99;
}

.selected-light .ant-select-open {
  .ant-select-selection-selected-value {
    opacity: 0.9 !important;
  }
}

.find-ul {
  width: fit-content;
  min-width: 200px;
  max-height: 200px;
  overflow-y: auto;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .find-li {
    line-height: 22px;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background: #e6f7ff;
      color: #1890ff;
    }

    &.selected {
      background: #f6ffed;
      color: #52c41a;
    }

    i {
      font-size: 10px;
      margin-left: 5px;
    }
  }
}

.category-box {
  display: flex;
  align-items: flex-start;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #fff;
  border-radius: 4px;
  min-width: 200px;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .category-item {
    border-right: 1px solid #eee;
    width: 220px;
    position: relative;

    &:last-child {
      border-right: none;
    }

    .category-header {
      padding: 8px 12px;
      background: #fafafa;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 13px;
      color: #666;
    }

    ul {
      max-height: 200px;
      overflow-y: auto;
      margin: 0;
      padding: 0;

      li {
        line-height: 22px;
        padding: 8px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #e6f7ff;
          color: #1890ff;
        }

        .category-item-content {
          display: flex;
          align-items: center;
          flex: 1;

          .ant-checkbox-wrapper {
            margin-right: 8px;
          }

          .category-name {
            font-size: 14px;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        i {
          font-size: 10px;
          color: #999;
        }
      }

      .selected {
        background: #f6ffed;
        color: #52c41a;
        font-weight: 500;

        &:hover {
          background: #f6ffed;
          color: #52c41a;
        }
      }
    }
  }
}

.category-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #eee;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  z-index: 1001;

  .selected-count {
    color: #666;
    font-weight: 500;
  }

  .footer-actions {
    display: flex;
    gap: 8px;

    .ant-btn {
      height: 24px;
      padding: 0 8px;
      font-size: 12px;
      border-radius: 2px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-box {
    flex-direction: column;
    max-width: 100%;

    .category-item {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// 滚动条样式
.category-box ul::-webkit-scrollbar,
.find-ul::-webkit-scrollbar {
  width: 4px;
}

.category-box ul::-webkit-scrollbar-track,
.find-ul::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.category-box ul::-webkit-scrollbar-thumb,
.find-ul::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.category-box ul::-webkit-scrollbar-thumb:hover,
.find-ul::-webkit-scrollbar-thumb:hover {
  background: #999;
}
// 2024年12月27日 开山ai结尾共生成150行代码
