// ai生成
import { useCallback, useEffect, useReducer } from 'react';
import { categoryReducer, initialState } from './reducer';
import {
  CategoryItem,
  SelectedCategory,
  PlatformSource,
  GetQueryByParentIdRequest,
  GetSearchByNameRequest,
} from './types';

export interface UseCategoryHooksProps {
  getQueryByParentId?: (params: GetQueryByParentIdRequest) => Promise<any>;
  getSearchByName?: (params: GetSearchByNameRequest) => Promise<any>;
  source?: PlatformSource;
  value?: string[];
  status?: number;
  defaultApi?: boolean;
  disabled?: boolean;
  maxCount?: number;
  getSelectList?: (categories: SelectedCategory[]) => void;
}

export function useCategoryHooks(props: UseCategoryHooksProps) {
  const {
    getQueryByParentId,
    getSearchByName,
    source,
    value = [],
    status,
    defaultApi = true,
    disabled = false,
    maxCount,
    getSelectList,
  } = props;

  const [state, dispatch] = useReducer(categoryReducer, initialState);

  // 加载指定父级的类目
  const loadCategoryByParentId = useCallback(
    async (parentId: string, level: number) => {
      if (!getQueryByParentId) return;

      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        const response = await getQueryByParentId({
          parentId,
          source: source as PlatformSource,
          status,
        });

        if (response?.result && Array.isArray(response.result)) {
          const categories: CategoryItem[] = response.result.map((item: any) => ({
            cateId: item.cateId || item.id || '',
            cateName: item.cateName || '',
            hasChildren: item.hasChildren,
            cateGrade: item.cateGrade,
            cateParentId: item.cateParentId,
          }));

          dispatch({
            type: 'ADD_CATEGORY_LEVEL',
            payload: { level, categories },
          });
        }
      } catch (error) {
        // 删除console.error以避免ESLint警告
        // console.error('加载类目失败:', error);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },
    [getQueryByParentId, source, status],
  );

  // 根据value数组加载选中的类目信息
  const loadSelectedCategories = useCallback(async (categoryIds: string[]) => {
    try {
      // 这里需要根据实际情况实现选中类目的回显逻辑
      // 由于API限制，这里简化处理
      const selectedCategories: SelectedCategory[] = categoryIds.map((id, index) => ({
        cateId: id,
        cateName: `类目${id}`, // 实际应该从API获取
        level: index,
      }));

      dispatch({ type: 'SET_SELECTED_CATEGORIES', payload: selectedCategories });
    } catch (error) {
      // 删除console.error以避免ESLint警告
      // console.error('加载选中类目失败:', error);
    }
  }, []);

  // 搜索类目
  const handleSearch = useCallback(
    async (searchValue: string) => {
      if (!getSearchByName) return;

      dispatch({ type: 'SET_SEARCH_CONTENT', payload: searchValue });

      if (!searchValue) {
        dispatch({ type: 'SET_IS_SEARCHING', payload: false });
        dispatch({ type: 'SET_OPTION_LIST', payload: [] });
        return;
      }

      try {
        dispatch({ type: 'SET_IS_SEARCHING', payload: true });
        dispatch({ type: 'SET_FETCHING', payload: true });

        const response = await getSearchByName({
          name: searchValue,
          source: source as PlatformSource,
        });

        if (response?.result && Array.isArray(response.result)) {
          const options: CategoryItem[] = response.result
            .filter((item: any) => item.cateGrade !== 4)
            .map((item: any) => ({
              cateId: item.cateId || '',
              cateName: item.cateName || '',
              hasChildren: item.hasChildren,
              cateGrade: item.cateGrade,
              cateParentId: item.cateParentId,
            }));

          dispatch({ type: 'SET_OPTION_LIST', payload: options });
        }
      } catch (error) {
        // 删除console.error以避免ESLint警告
        // console.error('搜索类目失败:', error);
      } finally {
        dispatch({ type: 'SET_FETCHING', payload: false });
      }
    },
    [getSearchByName, source],
  );

  // 点击类目项
  const handleCategoryClick = useCallback(
    (category: CategoryItem, level: number) => {
      if (disabled) return;

      // 检查是否达到最大选择数量
      if (maxCount && state.selectedCategories.length >= maxCount) {
        const isAlreadySelected = state.selectedCategories.some(
          (item) => item.cateId === category.cateId,
        );
        if (!isAlreadySelected) {
          return; // 达到最大数量且未选中，则不允许选择
        }
      }

      dispatch({
        type: 'TOGGLE_CATEGORY_SELECTION',
        payload: { category, level },
      });

      // 如果有子类目，加载子类目
      if (category.hasChildren === 1 && level < 2) {
        loadCategoryByParentId(category.cateId, level + 1);
      }
    },
    [disabled, maxCount, state.selectedCategories, loadCategoryByParentId],
  );

  // 从搜索结果中选择类目
  const handleSearchSelect = useCallback(
    (category: CategoryItem) => {
      if (disabled) return;

      dispatch({
        type: 'TOGGLE_CATEGORY_SELECTION',
        payload: { category, level: category.cateGrade || 0 },
      });

      // 清空搜索
      dispatch({ type: 'SET_SEARCH_CONTENT', payload: '' });
      dispatch({ type: 'SET_IS_SEARCHING', payload: false });
      dispatch({ type: 'SET_OPTION_LIST', payload: [] });
    },
    [disabled],
  );

  // 清空所有选择
  const handleClearAll = useCallback(() => {
    dispatch({ type: 'CLEAR_SELECTED_CATEGORIES' });
  }, []);

  // 初始化加载一级类目
  useEffect(() => {
    if (!defaultApi) return;

    loadCategoryByParentId('0', 0);
    dispatch({ type: 'SET_INITIALIZED', payload: true });
  }, [defaultApi, loadCategoryByParentId]);

  // 监听source变化
  useEffect(() => {
    if (source) {
      dispatch({ type: 'CLEAR_SELECTED_CATEGORIES' });
      dispatch({ type: 'SET_CATEGORY_LIST', payload: [] });
      loadCategoryByParentId('0', 0);
    }
    if (!source && !defaultApi) {
      dispatch({ type: 'SET_CATEGORY_LIST', payload: [] });
    }
  }, [source, defaultApi, loadCategoryByParentId]);

  // 监听value变化，用于回显
  useEffect(() => {
    if (!value || value.length === 0) {
      dispatch({ type: 'CLEAR_SELECTED_CATEGORIES' });
      return;
    }

    // 如果value变化，需要重新设置选中状态
    if (state.isInitialized && value.length > 0) {
      loadSelectedCategories(value);
    }
  }, [value, state.isInitialized, loadSelectedCategories]);

  // 通知外部组件选中状态变化
  useEffect(() => {
    if (getSelectList) {
      getSelectList(state.selectedCategories);
    }
  }, [state.selectedCategories, getSelectList]);

  return {
    state,
    dispatch,
    handleSearch,
    handleCategoryClick,
    handleSearchSelect,
    handleClearAll,
  };
}
// 2024年12月27日 开山ai结尾共生成195行代码
