// ai生成
import { State, Action, SelectedCategory } from './types';

export const initialState: State = {
  categoryList: [],
  optionList: [],
  selectedCategories: [],
  searchContent: '',
  isSearching: false,
  loading: false,
  fetching: false,
  isDropdownOpen: false,
  isInitialized: false,
};

export function categoryReducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_CATEGORY_LIST':
      return { ...state, categoryList: action.payload };
    case 'SET_OPTION_LIST':
      return { ...state, optionList: action.payload };
    case 'SET_SELECTED_CATEGORIES':
      return { ...state, selectedCategories: action.payload };
    case 'SET_SEARCH_CONTENT':
      return { ...state, searchContent: action.payload };
    case 'SET_IS_SEARCHING':
      return { ...state, isSearching: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_FETCHING':
      return { ...state, fetching: action.payload };
    case 'SET_DROPDOWN_OPEN':
      return { ...state, isDropdownOpen: action.payload };
    case 'SET_INITIALIZED':
      return { ...state, isInitialized: action.payload };
    case 'ADD_CATEGORY_LEVEL': {
      const newCategoryList = [...state.categoryList];
      newCategoryList[action.payload.level] = action.payload.categories;
      return { ...state, categoryList: newCategoryList };
    }
    case 'TOGGLE_CATEGORY_SELECTION': {
      const { category, level } = action.payload;
      const existingIndex = state.selectedCategories.findIndex(
        (item) => item.cateId === category.cateId,
      );

      if (existingIndex !== -1) {
        // 如果已选中，则取消选择
        const updatedCategories = state.selectedCategories.filter(
          (item) => item.cateId !== category.cateId,
        );
        return { ...state, selectedCategories: updatedCategories };
      } else {
        // 如果未选中，则添加选择
        const newSelectedCategory: SelectedCategory = {
          cateId: category.cateId,
          cateName: category.cateName,
          hasChildren: category.hasChildren,
          level,
          cateGrade: category.cateGrade,
          cateParentId: category.cateParentId,
        };
        return {
          ...state,
          selectedCategories: [...state.selectedCategories, newSelectedCategory],
        };
      }
    }
    case 'REMOVE_CATEGORY_SELECTION':
      return {
        ...state,
        selectedCategories: state.selectedCategories.filter(
          (item) => item.cateId !== action.payload,
        ),
      };
    case 'CLEAR_SELECTED_CATEGORIES':
      return { ...state, selectedCategories: [] };
    case 'RESET_STATE':
      return initialState;
    default:
      return state;
  }
}
// 2024年12月27日 开山ai结尾共生成72行代码
