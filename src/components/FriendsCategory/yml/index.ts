import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type GetQueryByParentIdRequest = {
  hasStandardGoodsCateId?: boolean /*是否关联行业大类*/;
  isFilterStopChildren?: boolean /*计算是否有子节点时，是不是需要剔除已停用的子类目*/;
  parentId?: string /*父级类目id*/;
  source?:
    | 'SELF'
    | 'DY'
    | 'KSXD'
    | 'TAOBAO'
    | 'JD'
    | 'PDD'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED'
    | 'TREND_PREDICTION' /*商品类目来源(快手/抖音)[PlatformSourceEnum]*/;
  standardCateId?: string /*行业大类id*/;
  status?: number /*类目状态，0：停用，1：启用*/;
};

export type GetQueryByParentIdResult = Array<{
  cateGrade?: number /*分类层级*/;
  cateId?: string /*前商品分类主键，理清影响范围后删除*/;
  cateImg?: string /*分类图片*/;
  cateName?: string /*分类名称*/;
  cateNamePath?: string /*分类名称层次路径,例类目1|类目2|类目3*/;
  cateNo?: string /*分类编码SPC00000026*/;
  cateParentId?: string /*父分类ID*/;
  catePath?: string /*分类层次路径,例1|01|001|*/;
  cateRate?: string /*分类扣率*/;
  commissionRate?: string /*佣金比例*/;
  createTime?: string /*创建时间*/;
  delFlag?: number /*删除标识,0:未删除1:已删除*/;
  firstCateParentId?: string /*一级类目id*/;
  growthValueRate?: string /*成长值获取比例*/;
  hasChildren?: number /*是否有下一级： 0 没有，1有*/;
  id?: string /*主键，与cate_id值相同，主要与现有表设计保持统一，后续整理完cate_id影响范围后删除cate_id*/;
  isDefault?: number /*是否默认,0:否1:是*/;
  isParentCateRate?: number /*是否使用上级类目扣率 0 否  1 是*/;
  isParentGrowthValueRate?: number /*是否使用上级类目成长值获取比例 0 否  1 是*/;
  isParentPointsRate?: number /*是否使用上级类目积分获取比例 0 否  1 是*/;
  mappingCateId?: string /*映射默认类目id*/;
  mappingCateName?: string /*映射默认类目名称*/;
  pinYin?: string /*拼音*/;
  platformCateId?: string /*电商平台类目主键*/;
  pointsRate?: string /*积分获取比例*/;
  sort?: number /*排序*/;
  source?: string /*平台来源 DY 抖音*/;
  spinYin?: string;
  standardGoodsCateId?: string /*平台商品类目表id*/;
  status?: number /*状态： 0 停用，1启用*/;
  updateTime?: string /*更新时间*/;
}>;

/**
 *按类目ID查询下级类目，ID为0时，查询一级类目
 */
export const getQueryByParentId = (params: GetQueryByParentIdRequest) => {
  return Fetch<ResponseWithResult<GetQueryByParentIdResult>>(
    '/befriend-service-goods/public/goods/cate/queryByParentId',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/goods/public/goods/cate/queryByParentId') },
    },
  );
};

export type GetSearchByNameRequest = {
  isFilterStopChildren?: boolean /*计算是否有子节点时，是不是需要剔除已停用的子类目*/;
  name?: string /*类目名称*/;
  source?:
    | 'SELF'
    | 'DY'
    | 'KSXD'
    | 'TAOBAO'
    | 'JD'
    | 'PDD'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED'
    | 'TREND_PREDICTION' /*商品类目来源[PlatformSourceEnum]*/;
  status?: number /*类目状态，0：停用，1：启用*/;
};

export type GetSearchByNameResult = Array<{
  cateGrade?: number /*分类层级*/;
  cateId?: string /*商品分类主键*/;
  cateImg?: string /*分类图片*/;
  cateName?: string /*商品分类主键*/;
  cateNo?: string /*分类编码SPC00000026*/;
  cateParentId?: string /*父分类ID*/;
  catePath?: string /*分类层次路径,例1|01|001*/;
  catePathList?: Array<{
    cateId?: string /*商品分类主键*/;
    cateName?: string /*商品分类名称*/;
    cateParentId?: string /*商品分类父级ID*/;
    hasChildren?: number /*是否有下一级： 0 没有，1有*/;
  }> /*分类层次路径*/;
  hasChildren?: number /*是否有下一级： 0 没有，1有*/;
  standardGoodsCateId?: string /*平台商品类目表id*/;
  status?: number /*状态： 0 停用，1启用*/;
}>;

/**
 *按类目名称前后模糊搜索类目
 */
export const getSearchByName = (params: GetSearchByNameRequest) => {
  return Fetch<ResponseWithResult<GetSearchByNameResult>>(
    '/befriend-service-goods/public/goods/cate/searchByName',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/goods/public/goods/cate/searchByName') },
    },
  );
};
