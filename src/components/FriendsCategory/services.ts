import { Fetch } from 'qmkit';
export { getQueryByParentId, getSearchByName } from './yml/index';

// // 类目
// export const getQueryByParentId = (params: object) => {
//   return Fetch('/befriend-service-goods/public/goods/cate/queryByParentId', {
//     method: 'POST',
//     body: JSON.stringify(params),
//   });
// };

// // 搜索类目
// export const getSearchByName = (params: object) => {
//   return Fetch('/befriend-service-goods/public/goods/cate/searchByName', {
//     method: 'POST',
//     body: JSON.stringify(params),
//   });
// };
