// ai生成
import React from 'react';
import { Button } from 'antd';
import { State } from '../types';

interface FooterActionsProps {
  state: State;
  maxCount?: number;
  onClearAll: () => void;
  onConfirm: () => void;
}

export const FooterActions: React.FC<FooterActionsProps> = ({
  state,
  maxCount,
  onClearAll,
  onConfirm,
}) => {
  if (!state.isDropdownOpen) {
    return null;
  }

  return (
    <div className="category-footer">
      <div className="selected-count">
        已选择 {state.selectedCategories.length} 项{maxCount && ` / ${maxCount}`}
      </div>
      <div className="footer-actions">
        <Button size="small" onClick={onClearAll}>
          清空
        </Button>
        <Button size="small" type="primary" onClick={onConfirm}>
          确定
        </Button>
      </div>
    </div>
  );
};
// 2024年12月27日 开山ai结尾共生成33行代码
