// ai生成
import React from 'react';
import { Icon, Checkbox } from 'antd';
import { CategoryItem, State, PLACEMENT } from '../types';
import styles from '../index.module.less';

interface CategoryListProps {
  state: State;
  placement?: PLACEMENT;
  onCategoryClick: (category: CategoryItem, level: number) => void;
}

export const CategoryList: React.FC<CategoryListProps> = ({
  state,
  placement,
  onCategoryClick,
}) => {
  if (state.isSearching) {
    return null;
  }

  return (
    <div className={`category-box ${placement ? styles[placement] : ''}`}>
      {state.categoryList.map((level, levelIndex) => {
        if (levelIndex > 2 || !level || level.length === 0) {
          return null;
        }

        const selectedCount = level.filter((item) =>
          state.selectedCategories.some((selected) => selected.cateId === item.cateId),
        ).length;

        return (
          <div className="category-item" key={levelIndex}>
            <div className="category-header">
              <span>
                {levelIndex === 0 && '一级类目'}
                {levelIndex === 1 && '二级类目'}
                {levelIndex === 2 && '三级类目'}
              </span>
              <span>
                ({selectedCount}/{level.length})
              </span>
            </div>
            <ul>
              {level.map((category) => {
                const isSelected = state.selectedCategories.some(
                  (selected) => selected.cateId === category.cateId,
                );

                return (
                  <li
                    key={category.cateId}
                    className={`flex-between ${isSelected ? 'selected' : ''}`}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      onCategoryClick(category, levelIndex);
                    }}
                  >
                    <div className="category-item-content">
                      <Checkbox
                        checked={isSelected}
                        onChange={() => onCategoryClick(category, levelIndex)}
                      />
                      <span className="category-name">{category.cateName}</span>
                    </div>
                    {category.hasChildren === 1 && levelIndex < 2 && <Icon type="right" />}
                  </li>
                );
              })}
            </ul>
          </div>
        );
      })}
    </div>
  );
};
// 2024年12月27日 开山ai结尾共生成71行代码
