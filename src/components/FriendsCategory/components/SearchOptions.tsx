// ai生成
import React from 'react';
import { Icon } from 'antd';
import { CategoryItem, State } from '../types';

interface SearchOptionsProps {
  state: State;
  onSearchSelect: (category: CategoryItem) => void;
}

export const SearchOptions: React.FC<SearchOptionsProps> = ({ state, onSearchSelect }) => {
  if (!state.isSearching || state.optionList.length === 0) {
    return null;
  }

  return (
    <div className="find-ul">
      {state.optionList.map((item) => {
        const isSelected = state.selectedCategories.some(
          (selected) => selected.cateId === item.cateId,
        );
        const displayText = item.cateName;
        const searchIndex = displayText.toLowerCase().indexOf(state.searchContent.toLowerCase());

        return (
          <div
            key={item.cateId}
            className={`find-li ${isSelected ? 'selected' : ''}`}
            onMouseDown={(e) => {
              e.preventDefault();
              onSearchSelect(item);
            }}
          >
            <div>
              {searchIndex > -1 ? (
                <>
                  <span>{displayText.substring(0, searchIndex)}</span>
                  <span style={{ color: 'blue' }}>
                    {displayText.substring(searchIndex, searchIndex + state.searchContent.length)}
                  </span>
                  <span>{displayText.substring(searchIndex + state.searchContent.length)}</span>
                </>
              ) : (
                <span>{displayText}</span>
              )}
              {isSelected && <Icon type="check" style={{ color: '#1890ff', marginLeft: 8 }} />}
            </div>
            <Icon type="right" />
          </div>
        );
      })}
    </div>
  );
};
// 2024年12月27日 开山ai结尾共生成47行代码
