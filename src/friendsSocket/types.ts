// ai生成
/**
 * WebSocket类型定义文件
 */

/**
 * WebSocket连接状态枚举
 */
export enum WebSocketStatus {
  CONNECTING = 0, // 连接中
  OPEN = 1, // 已连接
  CLOSING = 2, // 关闭中
  CLOSED = 3, // 已关闭
  RECONNECTING = 4, // 重连中
}

/**
 * WebSocket配置接口
 */
export interface WebSocketOptions {
  url: string; // WebSocket服务器地址
  protocols?: string | string[]; // WebSocket协议
  autoReconnect?: boolean; // 是否自动重连
  reconnectInterval?: number; // 重连间隔(ms)
  reconnectAttempts?: number; // 最大重连次数
  heartbeatInterval?: number; // 心跳间隔(ms)
  heartbeatMessage?: string | object; // 心跳消息内容
  debug?: boolean; // 是否开启调试模式
  onOpen?: (event: Event) => void; // 连接建立回调
  onClose?: (event: CloseEvent) => void; // 连接关闭回调
  onError?: (event: Event) => void; // 错误回调
  onMessage?: (data: any) => void; // 消息接收回调
  onReconnect?: (attempt: number) => void; // 重连尝试回调
}

/**
 * WebSocket事件类型
 */
export type WebSocketEventType = 'open' | 'close' | 'error' | 'message' | 'reconnect';

/**
 * WebSocket事件监听器
 */
export type WebSocketEventListener = (event: Event | MessageEvent | CloseEvent | number) => void;

/**
 * WebSocket消息队列项
 */
export interface WebSocketQueueItem {
  data: any;
  resolve: (value: void | PromiseLike<void>) => void;
  reject: (reason?: any) => void;
}

/**
 * WebSocket消息格式
 */
export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

/**
 * 心跳消息格式
 */
export interface HeartbeatMessage {
  type: 'ping' | 'pong';
  timestamp: number;
}

/**
 * WebSocket Hook返回值类型
 */
export interface WebSocketHookResult {
  status: WebSocketStatus;
  lastMessage: any;
  error: Error | null;
  sendMessage: (data: any) => Promise<void>;
  reconnect: () => Promise<Event>;
  closeConnection: (code?: number, reason?: string) => Promise<CloseEvent>;
  addEventListener: (type: WebSocketEventType, listener: WebSocketEventListener) => void;
  removeEventListener: (type: WebSocketEventType, listener: WebSocketEventListener) => void;
  instance: any;
}

/**
 * WebSocket实例接口
 */
export interface IWebSocket {
  getStatus(): WebSocketStatus;
  connect(): Promise<Event>;
  close(code?: number, reason?: string): Promise<CloseEvent>;
  send(data: any, useQueue?: boolean): Promise<void>;
  addEventListener(type: WebSocketEventType, listener: WebSocketEventListener): void;
  removeEventListener(type: WebSocketEventType, listener: WebSocketEventListener): void;
}
// 2023年7月15日 开山ai结尾共生成多少行代码 84
