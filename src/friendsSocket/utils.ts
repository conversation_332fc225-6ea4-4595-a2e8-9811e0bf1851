/**
 * WebSocket工具函数
 */

/**
 * 检查浏览器是否支持WebSocket
 */
export function isWebSocketSupported(): boolean {
  return typeof WebSocket !== 'undefined';
}

/**
 * 检查URL是否是有效的WebSocket URL
 * @param url 要检查的URL
 */
export function isValidWebSocketUrl(url: string): boolean {
  return /^(wss?):\/\/\S+/.test(url);
}

/**
 * 格式化WebSocket URL
 * @param url 原始URL
 * @param secure 是否使用安全连接(wss)
 */
export function formatWebSocketUrl(url: string, secure = true): string {
  // 如果已经是WebSocket URL，直接返回
  if (isValidWebSocketUrl(url)) {
    return url;
  }

  // 移除可能存在的协议前缀
  const cleanUrl = url.replace(/^(https?|wss?):\/\//, '');

  // 添加WebSocket协议前缀
  return `${secure ? 'wss' : 'ws'}://${cleanUrl}`;
}

/**
 * 解析WebSocket消息
 * @param data 收到的消息数据
 */
export function parseWebSocketMessage(data: any): any {
  if (typeof data !== 'string') {
    return data;
  }

  try {
    return JSON.parse(data);
  } catch (e) {
    return data;
  }
}

/**
 * 准备要发送的WebSocket消息
 * @param data 要发送的数据
 */
export function prepareWebSocketMessage(data: any): string | ArrayBuffer | Blob {
  if (data === null || data === undefined) {
    return '';
  }

  if (typeof data === 'string') {
    return data;
  }

  if (data instanceof ArrayBuffer || data instanceof Blob) {
    return data;
  }

  // 对象转JSON字符串
  return JSON.stringify(data);
}

/**
 * 计算指数退避重连延迟
 * @param attempt 当前尝试次数
 * @param baseDelay 基础延迟(毫秒)
 * @param maxDelay 最大延迟(毫秒)
 */
export function calculateBackoff(attempt: number, baseDelay = 1000, maxDelay = 30000): number {
  // 指数退避算法: min(baseDelay * 2^attempt, maxDelay)
  const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);

  // 添加随机抖动以避免多个客户端同时重连
  const jitter = Math.random() * 0.5 + 0.75; // 0.75-1.25之间的随机数

  return Math.floor(delay * jitter);
}

/**
 * 创建Ping消息
 */
export function createPingMessage(): string {
  return JSON.stringify({ type: 'ping', timestamp: Date.now() });
}

/**
 * 创建Pong消息
 */
export function createPongMessage(): string {
  return JSON.stringify({ type: 'pong', timestamp: Date.now() });
}

/**
 * WebSocket关闭代码解释
 * @param code WebSocket关闭代码
 */
export function getCloseReasonText(code: number): string {
  const reasons: Record<number, string> = {
    1000: '正常关闭',
    1001: '离开',
    1002: '协议错误',
    1003: '不可接受的数据类型',
    1004: '保留',
    1005: '没有提供状态码',
    1006: '异常关闭',
    1007: '数据类型不一致',
    1008: '违反策略',
    1009: '消息太大',
    1010: '需要扩展',
    1011: '意外情况',
    1012: '服务重启',
    1013: '服务过载',
    1014: '网关错误',
    1015: 'TLS握手失败',
  };

  return reasons[code] || `未知关闭代码: ${code}`;
}

/**
 * 检查WebSocket是否需要重连
 * @param code WebSocket关闭代码
 */
export function shouldReconnect(code: number): boolean {
  // 不需要重连的关闭代码
  const nonRecoverableCodes = [1000, 1001, 1002, 1003, 1007, 1008, 1009];

  return !nonRecoverableCodes.includes(code);
}

export default {
  isWebSocketSupported,
  isValidWebSocketUrl,
  formatWebSocketUrl,
  parseWebSocketMessage,
  prepareWebSocketMessage,
  calculateBackoff,
  createPingMessage,
  createPongMessage,
  getCloseReasonText,
  shouldReconnect,
};
// 2023年7月15日 开山ai结尾共生成多少行代码 118
