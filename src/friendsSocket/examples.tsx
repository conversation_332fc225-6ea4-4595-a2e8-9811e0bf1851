// ai生成
import React, { useState, useEffect } from 'react';
import { FriendsWebSocket, useFriendsWebSocket, WebSocketStatus, createWebSocket } from './index';

/**
 * 基本使用示例 - 直接使用WebSocket类
 */
export const BasicExample: React.FC = () => {
  const [message, setMessage] = useState<string>('');
  const [messages, setMessages] = useState<Array<{ content: string; isSent: boolean }>>([]);
  const [status, setStatus] = useState<string>('未连接');
  const [ws, setWs] = useState<FriendsWebSocket | null>(null);

  // 初始化WebSocket
  useEffect(() => {
    // 创建WebSocket实例
    const websocket = new FriendsWebSocket({
      url: 'wss://echo.websocket.events', // 回显服务器，用于测试
      debug: true,
      onOpen: () => {
        setStatus('已连接');
      },
      onClose: () => {
        setStatus('已关闭');
      },
      onError: () => {
        setStatus('连接错误');
      },
      onMessage: (data) => {
        setMessages((prev) => [
          ...prev,
          { content: typeof data === 'string' ? data : JSON.stringify(data), isSent: false },
        ]);
      },
    });

    // 保存实例
    setWs(websocket);

    // 连接WebSocket
    websocket.connect().catch((error) => {
      console.error('连接失败:', error);
    });

    // 组件卸载时关闭连接
    return () => {
      websocket.close();
    };
  }, []);

  // 发送消息
  const handleSend = () => {
    if (ws && message.trim()) {
      ws.send(message)
        .then(() => {
          setMessages((prev) => [...prev, { content: message, isSent: true }]);
          setMessage('');
        })
        .catch((error) => {
          console.error('发送失败:', error);
        });
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>基本使用示例</h2>
      <div>状态: {status}</div>
      <div style={{ marginTop: '10px' }}>
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="输入消息"
          style={{ width: '300px', marginRight: '10px', padding: '5px' }}
        />
        <button onClick={handleSend}>发送</button>
      </div>
      <div
        style={{
          marginTop: '20px',
          border: '1px solid #ccc',
          padding: '10px',
          height: '300px',
          overflowY: 'auto',
        }}
      >
        {messages.map((msg, index) => (
          <div
            key={index}
            style={{
              textAlign: msg.isSent ? 'right' : 'left',
              margin: '5px 0',
              padding: '5px 10px',
              backgroundColor: msg.isSent ? '#e6f7ff' : '#f0f0f0',
              borderRadius: '5px',
              display: 'inline-block',
              maxWidth: '80%',
              wordBreak: 'break-word',
            }}
          >
            {msg.content}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * React Hook使用示例 - 使用useFriendsWebSocket Hook
 */
export const HookExample: React.FC = () => {
  const [message, setMessage] = useState<string>('');
  const [messages, setMessages] = useState<Array<{ content: string; isSent: boolean }>>([]);

  // 使用WebSocket Hook
  const { status, lastMessage, error, sendMessage, reconnect, closeConnection } =
    useFriendsWebSocket({
      url: 'wss://echo.websocket.events',
      debug: true,
    });

  // 监听消息
  useEffect(() => {
    if (lastMessage) {
      setMessages((prev) => [
        ...prev,
        {
          content: typeof lastMessage === 'string' ? lastMessage : JSON.stringify(lastMessage),
          isSent: false,
        },
      ]);
    }
  }, [lastMessage]);

  // 发送消息
  const handleSend = () => {
    if (message.trim()) {
      sendMessage(message)
        .then(() => {
          setMessages((prev) => [...prev, { content: message, isSent: true }]);
          setMessage('');
        })
        .catch((error) => {
          console.error('发送失败:', error);
        });
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>React Hook使用示例</h2>
      <div>状态: {WebSocketStatus[status]}</div>
      {error && <div style={{ color: 'red' }}>错误: {error.message}</div>}
      <div style={{ marginTop: '10px' }}>
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="输入消息"
          style={{ width: '300px', marginRight: '10px', padding: '5px' }}
          disabled={status !== WebSocketStatus.OPEN}
        />
        <button onClick={handleSend} disabled={status !== WebSocketStatus.OPEN}>
          发送
        </button>
        <button
          onClick={reconnect}
          disabled={status === WebSocketStatus.OPEN}
          style={{ marginLeft: '10px' }}
        >
          重新连接
        </button>
        <button
          onClick={() => closeConnection()}
          disabled={status !== WebSocketStatus.OPEN}
          style={{ marginLeft: '10px' }}
        >
          关闭连接
        </button>
      </div>
      <div
        style={{
          marginTop: '20px',
          border: '1px solid #ccc',
          padding: '10px',
          height: '300px',
          overflowY: 'auto',
        }}
      >
        {messages.map((msg, index) => (
          <div
            key={index}
            style={{
              textAlign: msg.isSent ? 'right' : 'left',
              margin: '5px 0',
              padding: '5px 10px',
              backgroundColor: msg.isSent ? '#e6f7ff' : '#f0f0f0',
              borderRadius: '5px',
              display: 'inline-block',
              maxWidth: '80%',
              wordBreak: 'break-word',
            }}
          >
            {msg.content}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * 高级使用示例 - 事件监听和心跳检测
 */
export const AdvancedExample: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [ws, setWs] = useState<FriendsWebSocket | null>(null);

  // 添加日志
  const addLog = (message: string) => {
    setLogs((prev) => [...prev, `${new Date().toLocaleTimeString()} - ${message}`]);
  };

  // 初始化WebSocket
  useEffect(() => {
    // 创建WebSocket实例
    const websocket = createWebSocket({
      url: 'wss://echo.websocket.events',
      heartbeatInterval: 10000, // 10秒发送一次心跳
      heartbeatMessage: { type: 'ping', timestamp: Date.now() },
      debug: true,
    });

    // 添加事件监听器
    websocket.addEventListener('open', () => {
      addLog('连接已打开');
      setIsConnected(true);
    });

    websocket.addEventListener('close', (event) => {
      if (event instanceof CloseEvent) {
        addLog(`连接已关闭，代码: ${event.code}, 原因: ${event.reason || '未提供'}`);
      } else {
        addLog('连接已关闭');
      }
      setIsConnected(false);
    });

    websocket.addEventListener('error', () => {
      addLog('连接错误');
    });

    websocket.addEventListener('message', (event) => {
      if (event instanceof MessageEvent) {
        addLog(`收到消息: ${event.data}`);
      }
    });

    websocket.addEventListener('reconnect', (attempt) => {
      if (typeof attempt === 'number') {
        addLog(`尝试重连 (${attempt})`);
      }
    });

    // 保存实例
    setWs(websocket);

    // 连接WebSocket
    websocket.connect().catch((error) => {
      addLog(`连接失败: ${error.message}`);
    });

    // 组件卸载时关闭连接
    return () => {
      websocket.close();
    };
  }, []);

  // 发送自定义消息
  const sendCustomMessage = () => {
    if (ws && isConnected) {
      const customMessage = {
        type: 'custom',
        content: '这是一条自定义消息',
        timestamp: Date.now(),
      };

      ws.send(customMessage)
        .then(() => {
          addLog(`已发送自定义消息: ${JSON.stringify(customMessage)}`);
        })
        .catch((error) => {
          addLog(`发送失败: ${error.message}`);
        });
    }
  };

  // 模拟断开连接
  const simulateDisconnect = () => {
    if (ws) {
      // 关闭连接但不设置forceClosed标志，允许自动重连
      ws.close(1000, '手动断开连接').then(() => {
        addLog('已手动断开连接，等待自动重连');
      });
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>高级使用示例</h2>
      <div>状态: {isConnected ? '已连接' : '未连接'}</div>
      <div style={{ marginTop: '10px' }}>
        <button onClick={sendCustomMessage} disabled={!isConnected}>
          发送自定义消息
        </button>
        <button onClick={simulateDisconnect} disabled={!isConnected} style={{ marginLeft: '10px' }}>
          模拟断开连接
        </button>
      </div>
      <div
        style={{
          marginTop: '20px',
          border: '1px solid #ccc',
          padding: '10px',
          height: '300px',
          overflowY: 'auto',
        }}
      >
        {logs.map((log, index) => (
          <div key={index} style={{ margin: '2px 0', fontFamily: 'monospace' }}>
            {log}
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * 示例页面 - 包含所有示例
 */
const ExamplesPage: React.FC = () => {
  return (
    <div>
      <h1>FriendsWebSocket 示例</h1>
      <BasicExample />
      <hr style={{ margin: '30px 0' }} />
      <HookExample />
      <hr style={{ margin: '30px 0' }} />
      <AdvancedExample />
    </div>
  );
};

export default ExamplesPage;
// 2023年7月15日 开山ai结尾共生成多少行代码 273
