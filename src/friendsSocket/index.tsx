import { useEffect, useRef, useState, useCallback } from 'react';
import {
  WebSocketStatus,
  WebSocketOptions,
  WebSocketEventType,
  WebSocketEventListener,
  WebSocketQueueItem,
  IWebSocket,
  WebSocketHookResult,
} from './types';
import * as Utils from './utils';

/**
 * FriendsWebSocket类 - 核心WebSocket管理类
 */
export class FriendsWebSocket implements IWebSocket {
  private ws: WebSocket | null = null;
  private options: WebSocketOptions;
  private status: WebSocketStatus = WebSocketStatus.CLOSED;
  private reconnectAttempts = 0;
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  private heartbeatTimer: ReturnType<typeof setTimeout> | null = null;
  private messageQueue: WebSocketQueueItem[] = [];
  private eventListeners: Map<WebSocketEventType, Set<WebSocketEventListener>> = new Map();
  private forceClosed = false;

  /**
   * 构造函数
   * @param options WebSocket配置选项
   */
  constructor(options: WebSocketOptions) {
    // 默认配置
    const defaultOptions: Partial<WebSocketOptions> = {
      autoReconnect: true,
      reconnectInterval: 3000,
      reconnectAttempts: 10,
      heartbeatInterval: 30000,
      heartbeatMessage: 'ping',
      debug: false,
    };

    this.options = { ...defaultOptions, ...options };

    // 初始化事件监听器Map
    this.eventListeners.set('open', new Set());
    this.eventListeners.set('close', new Set());
    this.eventListeners.set('error', new Set());
    this.eventListeners.set('message', new Set());
    this.eventListeners.set('reconnect', new Set());
  }

  /**
   * 获取当前连接状态
   */
  public getStatus(): WebSocketStatus {
    return this.status;
  }

  /**
   * 建立WebSocket连接
   */
  public connect(): Promise<Event> {
    return new Promise((resolve, reject) => {
      if (
        this.ws &&
        (this.status === WebSocketStatus.OPEN || this.status === WebSocketStatus.CONNECTING)
      ) {
        this.log('WebSocket已经处于连接状态');
        return resolve(new Event('open'));
      }

      this.forceClosed = false;
      this.status = WebSocketStatus.CONNECTING;

      try {
        // 检查URL是否有效
        if (!Utils.isValidWebSocketUrl(this.options.url)) {
          this.options.url = Utils.formatWebSocketUrl(this.options.url);
        }

        this.ws = new WebSocket(this.options.url, this.options.protocols);

        // 设置二进制类型为Blob
        this.ws.binaryType = 'blob';

        // 连接打开
        this.ws.onopen = (event: Event) => {
          this.status = WebSocketStatus.OPEN;
          this.reconnectAttempts = 0;
          this.log('WebSocket连接已打开');

          // 处理消息队列
          this.processMessageQueue();

          // 启动心跳
          this.startHeartbeat();

          // 触发回调和事件
          if (this.options.onOpen) this.options.onOpen(event);
          this.dispatchEvent('open', event);

          resolve(event);
        };

        // 接收消息
        this.ws.onmessage = (event: MessageEvent) => {
          this.log('收到消息:', event.data);

          // 解析消息
          const data = Utils.parseWebSocketMessage(event.data);

          // 触发回调和事件
          if (this.options.onMessage) this.options.onMessage(data);
          this.dispatchEvent('message', event);
        };

        // 连接关闭
        this.ws.onclose = (event: CloseEvent) => {
          this.status = WebSocketStatus.CLOSED;
          this.log(
            'WebSocket连接已关闭, 代码:',
            event.code,
            '原因:',
            Utils.getCloseReasonText(event.code),
          );

          // 清除心跳定时器
          this.stopHeartbeat();

          // 触发回调和事件
          if (this.options.onClose) this.options.onClose(event);
          this.dispatchEvent('close', event);

          // 如果不是主动关闭且配置了自动重连，则尝试重连
          if (
            !this.forceClosed &&
            this.options.autoReconnect &&
            Utils.shouldReconnect(event.code)
          ) {
            this.reconnect();
          }

          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            reject(new Error('WebSocket连接失败'));
          }
        };

        // 连接错误
        this.ws.onerror = (event: Event) => {
          this.log('WebSocket连接错误');

          // 触发回调和事件
          if (this.options.onError) this.options.onError(event);
          this.dispatchEvent('error', event);

          reject(new Error('WebSocket连接错误'));
        };
      } catch (error) {
        this.status = WebSocketStatus.CLOSED;
        this.log('WebSocket创建失败:', error);
        reject(error);

        // 尝试重连
        if (this.options.autoReconnect) {
          this.reconnect();
        }
      }
    });
  }

  /**
   * 重新连接
   */
  private reconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.reconnectAttempts >= (this.options.reconnectAttempts || 0)) {
      this.log('已达到最大重连次数');
      return;
    }

    this.status = WebSocketStatus.RECONNECTING;
    this.reconnectAttempts++;

    // 使用指数退避算法计算重连延迟
    const delay = Utils.calculateBackoff(
      this.reconnectAttempts,
      this.options.reconnectInterval,
      this.options.reconnectInterval ? this.options.reconnectInterval * 10 : 30000,
    );

    this.log(
      `尝试重连 (${this.reconnectAttempts}/${this.options.reconnectAttempts}), ${delay}ms后重试`,
    );

    // 触发重连事件
    if (this.options.onReconnect) this.options.onReconnect(this.reconnectAttempts);
    this.dispatchEvent('reconnect', this.reconnectAttempts);

    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(() => {
        // 连接失败，继续尝试重连
      });
    }, delay);
  }

  /**
   * 关闭WebSocket连接
   */
  public close(code?: number, reason?: string): Promise<CloseEvent> {
    return new Promise((resolve) => {
      if (!this.ws || this.status === WebSocketStatus.CLOSED) {
        this.log('WebSocket已经关闭');
        return resolve(new CloseEvent('close'));
      }

      this.forceClosed = true;
      this.status = WebSocketStatus.CLOSING;

      // 清除定时器
      this.stopHeartbeat();
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      // 设置一次性关闭事件监听器
      const onCloseOnce = (event: CloseEvent) => {
        resolve(event);
      };

      this.addEventListener('close', onCloseOnce as WebSocketEventListener);

      // 关闭连接
      try {
        this.ws.close(code, reason);
      } catch (error) {
        this.log('关闭WebSocket时出错:', error);
        this.status = WebSocketStatus.CLOSED;
        this.removeEventListener('close', onCloseOnce as WebSocketEventListener);
        resolve(new CloseEvent('close'));
      }
    });
  }

  /**
   * 发送消息
   * @param data 要发送的数据
   * @param useQueue 如果连接未就绪，是否将消息加入队列
   */
  public send(data: any, useQueue = true): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果连接已就绪，直接发送
      if (this.ws && this.status === WebSocketStatus.OPEN) {
        try {
          // 准备消息
          const message = Utils.prepareWebSocketMessage(data);
          this.ws.send(message);
          this.log('已发送消息:', message);
          resolve();
        } catch (error) {
          this.log('发送消息失败:', error);
          reject(error);
        }
      }
      // 如果连接未就绪且允许使用队列
      else if (useQueue) {
        this.messageQueue.push({ data, resolve, reject });
        this.log('消息已加入队列');

        // 如果连接未建立，尝试连接
        if (this.status === WebSocketStatus.CLOSED) {
          this.connect().catch(() => {
            // 连接失败，消息将保留在队列中
          });
        }
      }
      // 连接未就绪且不使用队列
      else {
        reject(new Error('WebSocket未连接'));
      }
    });
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    if (this.messageQueue.length === 0) return;

    this.log(`处理消息队列，共${this.messageQueue.length}条消息`);

    // 复制队列并清空原队列
    const queue = [...this.messageQueue];
    this.messageQueue = [];

    // 处理每条消息
    queue.forEach(({ data, resolve, reject }) => {
      this.send(data, false).then(resolve).catch(reject);
    });
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    if (!this.options.heartbeatInterval || this.options.heartbeatInterval <= 0) return;

    this.stopHeartbeat();

    this.heartbeatTimer = setInterval(() => {
      if (this.status === WebSocketStatus.OPEN) {
        this.log('发送心跳');
        // 如果未指定心跳消息，使用默认的ping消息
        const heartbeatMsg = this.options.heartbeatMessage || Utils.createPingMessage();
        this.send(heartbeatMsg, false).catch(() => {
          this.log('心跳发送失败');
        });
      }
    }, this.options.heartbeatInterval);
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(type: WebSocketEventType, listener: WebSocketEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.add(listener);
    }
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(type: WebSocketEventType, listener: WebSocketEventListener): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  /**
   * 触发事件
   */
  private dispatchEvent(
    type: WebSocketEventType,
    event: Event | MessageEvent | CloseEvent | number,
  ): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach((listener) => {
        try {
          listener(event);
        } catch (error) {
          this.log(`事件监听器错误 (${type}):`, error);
        }
      });
    }
  }

  /**
   * 输出调试日志
   */
  private log(...args: any[]): void {
    if (this.options.debug) {
      console.log('[FriendsWebSocket]', ...args);
    }
  }
}

/**
 * React Hook - 使用WebSocket
 */
export function useFriendsWebSocket(options: WebSocketOptions): WebSocketHookResult {
  const wsRef = useRef<FriendsWebSocket | null>(null);
  const [status, setStatus] = useState<WebSocketStatus>(WebSocketStatus.CLOSED);
  const [lastMessage, setLastMessage] = useState<any>(null);
  const [error, setError] = useState<Error | null>(null);

  // 初始化WebSocket
  useEffect(() => {
    // 检查浏览器支持
    if (!Utils.isWebSocketSupported()) {
      setError(new Error('当前浏览器不支持WebSocket'));
      return;
    }

    // 创建WebSocket实例
    const ws = new FriendsWebSocket({
      ...options,
      onOpen: (event) => {
        setStatus(WebSocketStatus.OPEN);
        setError(null);
        if (options.onOpen) options.onOpen(event);
      },
      onClose: (event) => {
        setStatus(WebSocketStatus.CLOSED);
        if (options.onClose) options.onClose(event);
      },
      onError: (event) => {
        setError(new Error('WebSocket连接错误'));
        if (options.onError) options.onError(event);
      },
      onMessage: (data) => {
        setLastMessage(data);
        if (options.onMessage) options.onMessage(data);
      },
      onReconnect: (attempt) => {
        setStatus(WebSocketStatus.RECONNECTING);
        if (options.onReconnect) options.onReconnect(attempt);
      },
    });

    wsRef.current = ws;

    // 建立连接
    ws.connect().catch((err) => {
      setError(err);
    });

    // 组件卸载时关闭连接
    return () => {
      ws.close();
    };
  }, [options.url]); // 仅在URL变化时重新创建WebSocket

  // 发送消息的回调函数
  const sendMessage = useCallback((data: any) => {
    if (wsRef.current) {
      return wsRef.current.send(data);
    }
    return Promise.reject(new Error('WebSocket未初始化'));
  }, []);

  // 手动重连的回调函数
  const reconnect = useCallback(() => {
    if (wsRef.current) {
      return wsRef.current.connect();
    }
    return Promise.reject(new Error('WebSocket未初始化'));
  }, []);

  // 手动关闭连接的回调函数
  const closeConnection = useCallback((code?: number, reason?: string) => {
    if (wsRef.current) {
      return wsRef.current.close(code, reason);
    }
    return Promise.resolve(new CloseEvent('close'));
  }, []);

  // 添加事件监听器的回调函数
  const addEventListener = useCallback(
    (type: WebSocketEventType, listener: WebSocketEventListener) => {
      if (wsRef.current) {
        wsRef.current.addEventListener(type, listener);
      }
    },
    [],
  );

  // 移除事件监听器的回调函数
  const removeEventListener = useCallback(
    (type: WebSocketEventType, listener: WebSocketEventListener) => {
      if (wsRef.current) {
        wsRef.current.removeEventListener(type, listener);
      }
    },
    [],
  );

  return {
    status,
    lastMessage,
    error,
    sendMessage,
    reconnect,
    closeConnection,
    addEventListener,
    removeEventListener,
    instance: wsRef.current,
  };
}

/**
 * 创建WebSocket实例的工厂函数
 */
export function createWebSocket(options: WebSocketOptions): FriendsWebSocket {
  return new FriendsWebSocket(options);
}

// 导出类型和工具函数
export * from './types';
export * from './utils';

export default {
  FriendsWebSocket,
  useFriendsWebSocket,
  createWebSocket,
  WebSocketStatus,
};
