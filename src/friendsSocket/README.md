# FriendsWebSocket

一个功能全面、健壮、高性能的 WebSocket 客户端库，支持自动重连、心跳检测、消息队列等功能。

## 特性

- ✅ 自动重连机制，支持指数退避算法
- ✅ 心跳检测，保持连接活跃
- ✅ 消息队列，确保消息不丢失
- ✅ 完整的事件系统
- ✅ 支持 React Hooks
- ✅ TypeScript 类型支持
- ✅ 丰富的配置选项
- ✅ 调试模式支持

## 安装

```bash
# 项目内已集成，无需额外安装
```

## 基本使用

### 直接使用 WebSocket 类

```typescript
import { FriendsWebSocket } from '../friendsSocket';

// 创建WebSocket实例
const ws = new FriendsWebSocket({
  url: 'wss://example.com/socket',
  autoReconnect: true,
  heartbeatInterval: 30000,
  debug: true,
  onOpen: (event) => {
    console.log('连接已打开');
  },
  onMessage: (data) => {
    console.log('收到消息:', data);
  },
  onClose: (event) => {
    console.log('连接已关闭');
  },
  onError: (event) => {
    console.log('连接错误');
  },
});

// 连接WebSocket
ws.connect()
  .then(() => {
    console.log('连接成功');

    // 发送消息
    ws.send({ type: 'greeting', content: 'Hello Server!' });
  })
  .catch((error) => {
    console.error('连接失败:', error);
  });

// 关闭连接
ws.close();
```

### 使用 React Hook

```tsx
import React, { useEffect } from 'react';
import { useFriendsWebSocket, WebSocketStatus } from '../friendsSocket';

const WebSocketComponent: React.FC = () => {
  const { status, lastMessage, error, sendMessage, reconnect, closeConnection } =
    useFriendsWebSocket({
      url: 'wss://example.com/socket',
      autoReconnect: true,
      heartbeatInterval: 30000,
    });

  useEffect(() => {
    if (lastMessage) {
      console.log('收到新消息:', lastMessage);
    }
  }, [lastMessage]);

  const handleSendMessage = () => {
    sendMessage({ type: 'greeting', content: 'Hello from React!' });
  };

  return (
    <div>
      <div>状态: {WebSocketStatus[status]}</div>
      {error && <div>错误: {error.message}</div>}
      <div>
        <button onClick={handleSendMessage} disabled={status !== WebSocketStatus.OPEN}>
          发送消息
        </button>
        <button onClick={reconnect} disabled={status === WebSocketStatus.OPEN}>
          重新连接
        </button>
        <button onClick={closeConnection} disabled={status !== WebSocketStatus.OPEN}>
          关闭连接
        </button>
      </div>
      {lastMessage && (
        <div>
          <h3>最新消息:</h3>
          <pre>{JSON.stringify(lastMessage, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default WebSocketComponent;
```

### 使用工厂函数

```typescript
import { createWebSocket } from '../friendsSocket';

// 创建WebSocket实例
const ws = createWebSocket({
  url: 'wss://example.com/socket',
  debug: true,
});

// 使用实例
ws.connect();
```

## API 参考

### WebSocketOptions

WebSocket 配置选项接口。

| 参数名            | 类型                        | 默认值    | 描述                 |
| ----------------- | --------------------------- | --------- | -------------------- |
| url               | string                      | -         | WebSocket 服务器地址 |
| protocols         | string \| string[]          | undefined | WebSocket 协议       |
| autoReconnect     | boolean                     | true      | 是否自动重连         |
| reconnectInterval | number                      | 3000      | 重连间隔(ms)         |
| reconnectAttempts | number                      | 10        | 最大重连次数         |
| heartbeatInterval | number                      | 30000     | 心跳间隔(ms)         |
| heartbeatMessage  | string \| object            | 'ping'    | 心跳消息内容         |
| debug             | boolean                     | false     | 是否开启调试模式     |
| onOpen            | (event: Event) => void      | undefined | 连接建立回调         |
| onClose           | (event: CloseEvent) => void | undefined | 连接关闭回调         |
| onError           | (event: Event) => void      | undefined | 错误回调             |
| onMessage         | (data: any) => void         | undefined | 消息接收回调         |
| onReconnect       | (attempt: number) => void   | undefined | 重连尝试回调         |

### WebSocketStatus

WebSocket 连接状态枚举。

```typescript
enum WebSocketStatus {
  CONNECTING = 0, // 连接中
  OPEN = 1, // 已连接
  CLOSING = 2, // 关闭中
  CLOSED = 3, // 已关闭
  RECONNECTING = 4, // 重连中
}
```

### FriendsWebSocket 类

核心 WebSocket 管理类。

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
| --- | --- | --- | --- |
| constructor | options: WebSocketOptions | - | 构造函数 |
| getStatus | - | WebSocketStatus | 获取当前连接状态 |
| connect | - | Promise\<Event\> | 建立 WebSocket 连接 |
| close | code?: number, reason?: string | Promise\<CloseEvent\> | 关闭 WebSocket 连接 |
| send | data: any, useQueue?: boolean | Promise\<void\> | 发送消息 |
| addEventListener | type: WebSocketEventType, listener: WebSocketEventListener | void | 添加事件监听器 |
| removeEventListener | type: WebSocketEventType, listener: WebSocketEventListener | void | 移除事件监听器 |

### useFriendsWebSocket Hook

React Hook，用于在 React 组件中使用 WebSocket。

#### 参数

| 参数名  | 类型             | 描述               |
| ------- | ---------------- | ------------------ |
| options | WebSocketOptions | WebSocket 配置选项 |

#### 返回值

| 属性名 | 类型 | 描述 |
| --- | --- | --- |
| status | WebSocketStatus | 当前连接状态 |
| lastMessage | any | 最后收到的消息 |
| error | Error \| null | 错误信息 |
| sendMessage | (data: any) => Promise\<void\> | 发送消息函数 |
| reconnect | () => Promise\<Event\> | 手动重连函数 |
| closeConnection | (code?: number, reason?: string) => Promise\<CloseEvent\> | 关闭连接函数 |
| addEventListener | (type: WebSocketEventType, listener: WebSocketEventListener) => void | 添加事件监听器 |
| removeEventListener | (type: WebSocketEventType, listener: WebSocketEventListener) => void | 移除事件监听器 |
| instance | FriendsWebSocket \| null | WebSocket 实例 |

### 工具函数

`utils.ts`中提供了一系列辅助函数：

| 函数名                  | 描述                                |
| ----------------------- | ----------------------------------- |
| isWebSocketSupported    | 检查浏览器是否支持 WebSocket        |
| isValidWebSocketUrl     | 检查 URL 是否是有效的 WebSocket URL |
| formatWebSocketUrl      | 格式化 WebSocket URL                |
| parseWebSocketMessage   | 解析 WebSocket 消息                 |
| prepareWebSocketMessage | 准备要发送的 WebSocket 消息         |
| calculateBackoff        | 计算指数退避重连延迟                |
| createPingMessage       | 创建 Ping 消息                      |
| createPongMessage       | 创建 Pong 消息                      |
| getCloseReasonText      | WebSocket 关闭代码解释              |
| shouldReconnect         | 检查 WebSocket 是否需要重连         |

## 高级用法

### 自定义心跳消息

```typescript
const ws = new FriendsWebSocket({
  url: 'wss://example.com/socket',
  heartbeatInterval: 15000,
  heartbeatMessage: { type: 'heartbeat', timestamp: Date.now() },
});
```

### 事件监听

```typescript
const ws = new FriendsWebSocket({
  url: 'wss://example.com/socket',
});

// 添加事件监听器
ws.addEventListener('open', (event) => {
  console.log('连接已打开');
});

ws.addEventListener('message', (event) => {
  if (event instanceof MessageEvent) {
    console.log('收到消息:', event.data);
  }
});

// 移除事件监听器
const closeHandler = (event: CloseEvent) => {
  console.log('连接已关闭');
};
ws.addEventListener('close', closeHandler);
// 稍后移除
ws.removeEventListener('close', closeHandler);
```

### 消息队列

当 WebSocket 连接未就绪时，发送的消息会被加入队列，连接建立后自动发送。

```typescript
const ws = new FriendsWebSocket({
  url: 'wss://example.com/socket',
});

// 即使连接尚未建立，消息也会被加入队列
ws.send({ type: 'init', data: 'Hello' });

// 如果不希望使用队列，可以设置useQueue为false
ws.send({ type: 'immediate', data: 'Urgent' }, false)
  .then(() => console.log('消息已发送'))
  .catch((error) => console.error('发送失败:', error));
```

## 最佳实践

1. **始终处理错误**：使用 Promise 的 catch 或 try-catch 处理可能的错误。
2. **设置合理的重连参数**：根据应用需求设置重连间隔和最大重连次数。
3. **使用心跳保持连接**：在长连接场景中，设置合理的心跳间隔。
4. **关闭不再使用的连接**：在组件卸载或不再需要连接时，调用 close 方法关闭连接。
5. **使用调试模式排查问题**：开发阶段设置`debug: true`以获取详细日志。

## 常见问题

### Q: 如何处理断线重连？

A: 库已内置自动重连机制，可通过`autoReconnect`、`reconnectInterval`和`reconnectAttempts`参数配置。

### Q: 如何确保消息不丢失？

A: 使用消息队列功能，当连接未就绪时，消息会被加入队列，连接建立后自动发送。

### Q: 如何处理大量数据？

A: 对于大量数据，考虑使用二进制格式（ArrayBuffer 或 Blob）而非 JSON 字符串。

### Q: 如何处理认证？

A: 可在连接建立后立即发送认证消息，或在 URL 中包含认证信息。

### Q: 浏览器兼容性如何？

A: 支持所有现代浏览器，包括移动端浏览器。IE11 需要 polyfill 支持。
