import { MENU_TYPE } from '@/common/constants/moduleConstant';
import { Tile } from 'web-common-modules/utils/menu';
import { TOP_HEADER_TYPE } from './common/constants/menu';
import {
  settingRoutes,
  systemConfigRoutes,
  legalAudit,
  liveMan,
  selectionMenu,
  supplier,
  goodsCenter,
  businessCRM,
  webiboRoutes,
  warehouseManagementRoutes,
  companyManage,
} from './routers';

export interface IRoute {
  path: string;
  name: string;
  exact?: boolean;
  menu?: MENU_TYPE;
  topHeaderType?: TOP_HEADER_TYPE;
  asyncComponent: () => void;
  children?: IRoute[];
  parent?: string;
  url?: string;
}
const routesTree: IRoute[] = [
  // 工作台
  {
    path: '/workbench',
    name: '工作台',
    exact: true,
    asyncComponent: () => import(/* webpackChunkName: "pages_workbench" */ './pages/workbench'),
  },
  // 邀请入驻
  {
    path: '/invite-in',
    name: '邀请入驻',
    exact: true,
    asyncComponent: () => import(/* webpackChunkName: "invite_in" */ './pages/invite-in'),
  },

  // 招商计划
  {
    path: '/investment-plan-list',
    name: '招商计划',
    exact: true,
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_supplier" */ './pages/investment-plan-list/List'),
  },
  {
    path: '/investment-plan-add',
    name: '招商计划操作',
    exact: true,
    asyncComponent: () => import('./pages/investment-plan-list/Add'),
  },
  {
    path: '/investment-plan-detail',
    name: '详情',
    exact: true,
    asyncComponent: () => import('./pages/investment-plan-list/Detail'),
  },
  //招商链接管理
  {
    path: '/invitation-link-manage',
    name: '邀请链接管理',
    exact: true,
    asyncComponent: () => import('./pages/invitation-link'),
  },
  {
    path: '/report-sheet',
    name: '商家报名单',
    asyncComponent: () => import('./pages/report-sheet'),
  },
  {
    path: '/report-sheet-detail',
    name: '商家报名单详情',
    asyncComponent: () => import('./pages/report-sheet/Detail'),
  },
  // 商家管理-新-商家列表
  {
    path: '/supplier-list-provider',
    name: '客商管理',
    exact: true,
    asyncComponent: () => import('./pages/supplier-list/List'),
  },
  {
    path: '/add-supplier',
    name: '新建客商',
    exact: true,
    asyncComponent: () => import('./pages/supplier-list/AddSupplier/index'),
  },
  {
    path: '/edit-supplier/:sid',
    name: '编辑客商',
    exact: true,
    asyncComponent: () => import('./pages/supplier-list/AddSupplier/index'),
  },
  {
    path: '/apply-edit-supplier/:sid',
    name: '编辑客商',
    exact: true,
    asyncComponent: () => import('./pages/supplier-list/AddSupplier/index'),
  },
  // 商家详情
  {
    path: '/provider-detail/:sid',
    name: '客商详情',
    exact: true,
    asyncComponent: () => import('./pages/supplier-list/Detail/index'),
  },
  // 框架合作管理
  {
    path: '/business-cooperation/list',
    name: '框架合同管理',
    exact: true,
    asyncComponent: () => import('./pages/business-cooperation/List'),
  },
  // 新建年框合作
  {
    path: '/business-cooperation/add',
    name: '新建年框合作',
    exact: true,
    asyncComponent: () => import('./pages/business-cooperation/Add'),
  },
  // 框架合作详情
  {
    path: '/business-cooperation/detail',
    name: '框架合作详情',
    exact: true,
    asyncComponent: () => import('./pages/business-cooperation/View'),
  },
  // 框架合作账单
  {
    path: '/publish-fee-manage',
    name: '框架账单',
    exact: true,
    asyncComponent: () => import('./pages/gmvCommissionManage'),
  },
  // 主播合同管理
  {
    path: '/anchor-contract',
    name: '主播合同管理',
    asyncComponent: () =>
      import(/* webpackChunkName: "anchor-information" */ './pages/anchor-contract/index'),
  },
  // 账单导出记录
  {
    path: '/billExport-list',
    name: '账单导出记录',
    exact: true,
    asyncComponent: () => import('./pages/export-list'),
  },
  // 框架项目维护
  {
    path: '/framework-item',
    name: '框架项目维护',
    exact: true,
    asyncComponent: () => import('./pages/framework-item'),
  },
  // 框架合作报表
  {
    path: '/business-cooperation/report',
    name: '框架合作报表',
    exact: true,
    asyncComponent: () => import('./pages/report/business-cooperation'),
  },
  // 原榜单
  // {
  //   path: '/rank',
  //   name: '交友商品榜',
  //   exact: true,
  //   // menu: MENU_TYPE.NONE,
  //   topHeaderType: TOP_HEADER_TYPE.RANK,
  //   asyncComponent: () => import(/* webpackChunkName: "pages_supplier" */ './pages/rank'),
  // },

  {
    path: '/live-list',
    name: '直播监控',
    exact: true,
    topHeaderType: TOP_HEADER_TYPE.MONITORING,
    asyncComponent: () => import(/* webpackChunkName: "pages_supplier" */ './pages/dy-live/index'),
  },
  // 核链
  {
    path: '/check-link',
    name: '核链工具',
    exact: true,
    // menu: MENU_TYPE.NONE,
    topHeaderType: TOP_HEADER_TYPE.CHECK_LINK,
    asyncComponent: () => import(/* webpackChunkName: "pages_check-link" */ './pages/check-link'),
  },
  {
    path: '/label-management',
    name: '标签管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_label-management" */ './pages/live-room-manage/label-management'
      ),
    children: [],
  },
  {
    path: '/operation/live-broadcast',
    name: '直播场次',
    asyncComponent: () => import('./pages/live-room-operation/live-broadcast/Index'),
  },
  {
    path: '/live-calendar',
    name: '场次日历',
    asyncComponent: () => import('./pages/live-calendar'),
  },

  {
    path: '/legal-audit/queues',
    name: '审核队列',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_legal-audit-queue" */ './pages/audit/legal-audit-queue/List'
      ),
  },
  {
    path: '/legal-audit/workbench',
    name: '法务审核',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_legal-audit-workbench" */ './pages/audit/legal-audit-workbench/Index'
      ),
  },
  {
    path: '/legal-audit/records',
    name: '审核记录',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_legal-audit-records" */ './pages/audit/legal-audit-records/List'
      ),
  },
  {
    path: '/export-list-legal-audit-all',
    name: '审核记录导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/specil-approval-record',
    name: '特批日志',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_high-risk-auditor" */ './pages/specil-approval-record'),
    children: [
      {
        path: '/export-list-highrisk-approval-records',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/legal-audit/type',
    name: '资质类型',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_legal-audit-records" */ './pages/audit/legal-audit-type/List'
      ),
  },
  {
    path: '/project-team/manage',
    name: '项目组管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_legal-audit-records" */ './pages/live-room-manage/project-team-manage'
      ),
  },
  {
    path: '/project-team-detail',
    name: '项目组详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_legal-audit-records" */ './pages/live-room-manage/project-team-manage/components/Detail'
      ),
  },
  {
    path: '/business-map/assistant',
    name: '商务映射助理',
    asyncComponent: () => import('./pages/live-room-manage/business-map-assistant'),
  },
  {
    path: '/business-map-assistant-detail',
    name: '商务映射助理详情',
    asyncComponent: () =>
      import('./pages/live-room-manage/business-map-assistant/components/Detail'),
  },
  {
    path: '/agreement-management/agreement',
    name: '协议管理',
    asyncComponent: () => import('./pages/live-room-manage/agreement-management'),
  },
  {
    path: '/financialSettlemen/advancePayment',
    name: '预付款申请单',
    asyncComponent: () => import('./pages/qiankun/advancePayment'),
  },
  {
    path: '/financialSettlemen/advancePaymentAdd',
    name: '付款申请操作',
    asyncComponent: () => import('./pages/qiankun/index'),
  },
  {
    path: '/financialSettlemen/mcnExpense',
    name: '成本管理',
    asyncComponent: () => import('./pages/qiankun/mcnExpense'),
  },
  {
    path: '/financialSettlemen/addMcnExpense',
    name: '新建成本',
    asyncComponent: () => import('./pages/qiankun/index'),
  },
  {
    path: '/financialSettlemen/addMcnExpenseDetail',
    name: '成本详情',
    asyncComponent: () => import('./pages/qiankun/index'),
  },
  {
    path: '/financialSettlemen/invoiceManage',
    name: '发票管理',
    asyncComponent: () => import('./pages/qiankun/invoiceManage'),
  },
  {
    path: '/financialSettlemen/addInvoiceManage',
    name: '新增发票',
    asyncComponent: () => import('./pages/qiankun/index'),
  },
  {
    path: '/financialSettlemen/mcnAccountant',
    name: '付款结算管理',
    asyncComponent: () => import('./pages/qiankun/mcnAccountant'),
  },
  {
    path: '/financialSettlemen/mcnAccountantAdd',
    name: '付款结算操作',
    asyncComponent: () => import('./pages/qiankun/index'),
  },
  {
    path: '/financialSettlemen/exportRecordList',
    name: '导出记录',
    asyncComponent: () => import('./pages/qiankun/index'),
  },
  //
  {
    path: '/financialSettlemen/cashierWorkbench',
    name: '出纳工作台',
    asyncComponent: () => import('./pages/qiankun/cashierWorkbench'),
  },
  {
    path: '/financialSettlemen/cashierWorkbenchDetail',
    name: '出纳工作台详情',
    asyncComponent: () => import('./pages/qiankun/index'),
  },
  // voucherManagement 付款凭证管理
  {
    path: '/financialSettlemen/voucherManagement',
    name: '付款凭证管理',
    asyncComponent: () => import('./pages/qiankun/voucherManagement'),
  },
  // incomeStatement 对账单
  {
    path: '/financialSettlemen/incomeStatement',
    name: '对账单',
    asyncComponent: () => import('./pages/qiankun/index'),
  },

  // marketingManage
  // marketing-details-data 营销管理-营销明细数据
  // marketing-business-data 营销管理-营销业务数据
  {
    path: '/marketing-details-data',
    name: '营销明细数据',
    asyncComponent: () => import('./pages/live-room-manage/approval-item-manage'),
  },
  {
    path: '/marketing-business-data',
    name: '营销业务数据',
    asyncComponent: () => import('./pages/live-room-manage/approval-item-manage'),
  },

  {
    path: '/approval-item/manage',
    name: '审批项配置',
    asyncComponent: () => import('./pages/live-room-manage/approval-item-manage'),
  },
  {
    path: '/legal-audit/rules',
    name: '资质审核规则',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_legal-audit-records" */ './pages/audit/legal-audit-rules'),
  },
  {
    path: '/choice-list',
    name: '场次货盘',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_pallet-management" */ './pages/choice-list-new/index'),
    children: [
      {
        path: '/export-list-selected-goods-all',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
      {
        path: '/export-list-selected-goods-all-oral',
        name: '口播稿导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  //
  {
    path: '/order-cooperation',
    name: '合作订单管理',
    exact: true,
    asyncComponent: () => import('./pages/order-cooperation/index'),
  },
  {
    path: '/order-export-list',
    name: '导出记录',
    exact: true,
    asyncComponent: () => import('./pages/export-list'),
  },
  {
    path: '/export-list',
    name: '导出记录',
    exact: true,
    asyncComponent: () => import('./pages/export-list'),
  },
  {
    path: '/order-cooperation/:coopOrderNo',
    name: '订单详情',
    exact: true,
    asyncComponent: () => import('./pages/order-cooperation/detail'),
  },
  {
    path: '/non-live-order-cooperation/:coopOrderNo',
    name: '订单详情',
    exact: true,
    asyncComponent: () =>
      import('../web-common-modules/biz/Order/OrderCooperationDetail/NonLiveCooperation'),
  },

  {
    path: '/cooperation-adjust-order/:id',
    name: '订单详情',
    exact: true,
    asyncComponent: () => import('./pages/order-cooperation/cooperation-adjust-order/Detail'),
  },

  {
    path: '/choiceList-data-board',
    name: '数据看板',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_pallet-management" */ './pages/choiceList-data-board/index'
      ),
    children: [
      {
        path: '/export-list-selected-goods-all-board',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/estimatedSettlementRate',
    name: '预估结算率',
    asyncComponent: () => import('./pages/estimatedSettlementRate/index'),
    // children: [
    //   {
    //     path: '/export-list-selected-goods-all-board',
    //     name: '导出记录',
    //     asyncComponent: () =>
    //       import('./pages/export-list'),
    //   },
    // ],
  },
  {
    path: '/hot-goods',
    name: '爆品池',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_hot-goods" */ './pages/hot-goods/index'),
  },
  {
    path: '/goods-assorting',
    name: '选品池',
    asyncComponent: () => import('./pages/goods-assorting/index'),
    children: [
      {
        path: '/export-list-goods-assorting',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/goods-assorting-newGoods',
    name: '新建商品',
    asyncComponent: () => import('./pages/goods-assorting/newGoods/index'),
  },

  {
    path: '/tbLink-management',
    name: '淘宝链接管理',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_pallet-management" */ './pages/tbLink-management/index'),
  },
  // 部门管理
  {
    path: '/setting/department-mangement',
    name: '部门管理',
    exact: true,
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_department-mangement" */ './pages/setting/department-mangement'
      ),
  },
  {
    path: '/setting/employee-list',
    name: '员工列表',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_employee-list" */ './pages/setting/employee-list/List'),
  },
  {
    path: '/setting/employee-detail/:id',
    name: '员工详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_employee-list" */ './pages/setting/employee-list/user-detail/index'
      ),
  },
  {
    path: '/setting/employee-edit/:id',
    name: '编辑员工',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_employee-list" */ './pages/setting/employee-list/user-create/index'
      ),
  },
  {
    path: '/setting/employee-create',
    name: '新建员工',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_employee-list" */ './pages/setting/employee-list/user-create/index'
      ),
  },
  {
    path: '/setting/role-management',
    name: '角色权限',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_role-management" */ './pages/setting/role-management/List'
      ),
  },
  {
    path: '/setting/role-add',
    name: '新增角色权限',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_role-management_AddRole" */ './pages/setting/role-management/AddRole'
      ),
  },
  {
    path: '/setting/role-edit/:id',
    name: '编辑角色权限',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_role-management_AddRole" */ './pages/setting/role-management/AddRole'
      ),
  },
  {
    path: '/setting/data-management',
    name: '数据权限',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management" */ './pages/setting/data-management/List'
      ),
  },
  {
    path: '/setting/data-role-add',
    name: '新增数据权限',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management_AddDataRole" */ './pages/setting/data-management/AddDataRole'
      ),
  },
  {
    path: '/setting/data-role-edit/:id',
    name: '编辑数据权限',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management_AddDataRole" */ './pages/setting/data-management/AddDataRole'
      ),
  },
  {
    path: '/job-authorization-configuration',
    name: '职务权限配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "job-authorization-configuration" */ './pages/job-authorization-configuration'
      ),
  },
  {
    path: '/system-setting/low-commission-config',
    name: '低佣类型配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management" */ './pages/setting/low-commission-type-config'
      ),
  },
  {
    path: '/system-setting/low-commission-config-add',
    name: '新增低佣类型配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "low-commission-config-add" */ './pages/setting/low-commission-type-config/components/Add/index'
      ),
  },
  {
    path: '/system-setting/low-commission-config-edit/:id',
    name: '编辑低佣类型配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-low-commission-config-edit" */ './pages/setting/low-commission-type-config/components/Add/index'
      ),
  },
  {
    path: '/system-setting/low-commission-config-info/:id',
    name: '编辑低佣类型配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management" */ './pages/setting/low-commission-type-config/components/Info/index'
      ),
  },
  {
    path: '/system-setting/low-commission-whitelist',
    name: '低佣白名单',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management" */ './pages/setting/low-commission-whitelist'
      ),
  },
  {
    path: '/system-setting/black-list',
    name: '黑名单配置',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_data-management" */ './pages/black-list/index'),
  },
  {
    path: '/system-setting/selection-flow-config',
    name: '选品流程配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management" */ './pages/setting/selection-flow-management'
      ),
  },
  {
    path: '/system-setting/selection-config-create',
    name: '新增选品流程配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management_AddDataRole" */ './pages/setting/selection-flow-management/selection-create'
      ),
  },
  {
    path: '/system-setting/selection-config-edit',
    name: '编辑选品流程配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management_AddDataRole" */ './pages/setting/selection-flow-management/selection-create'
      ),
  },
  {
    path: '/system-setting/selection-config-detail',
    name: '选品流程配置详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management_AddDataRole" */ './pages/setting/selection-flow-management/selection-detail'
      ),
  },
  {
    path: '/system-setting/live-service-type-configuration',
    name: '直播服务类型配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_employee-list" */ './pages/setting/live-service-type-configuration'
      ),
  },
  {
    path: '/live-service-type-configuration/add',
    name: '新建直播服务类型',
    exact: true,
    asyncComponent: () => import('./pages/setting/live-service-type-configuration/Add'),
  },
  {
    path: '/live-service-type-configuration/edit',
    name: '编辑直播服务类型',
    exact: true,
    asyncComponent: () => import('./pages/setting/live-service-type-configuration/Add'),
  },
  {
    path: '/live-service-type-configuration/detail',
    name: '直播服务类型详情',
    exact: true,
    asyncComponent: () => import('./pages/setting/live-service-type-configuration/detail'),
  },
  // 聚合商品库
  // 原商品字典
  // {
  //   path: '/polymerization-goods',
  //   name: '交友商品池',
  //   // menu: MENU_TYPE.NONE,
  //   topHeaderType: TOP_HEADER_TYPE.DICTIONARY,
  //   asyncComponent: () =>
  //     import(/* webpackChunkName: "polymerization_goods" */ './pages/polymerization-goods/List'),
  //   children: [
  //     {
  //       path: '/polymerization-goods-detail',
  //       name: '聚合商品详情',
  //       // menu: MENU_TYPE.NONE,
  //       topHeaderType: TOP_HEADER_TYPE.DICTIONARY,
  //       asyncComponent: () =>
  //         import(
  //           /* webpackChunkName: "polymerization-goods-detail" */ './pages/polymerization-goods-detail'
  //         ),
  //     },
  //   ],
  // },
  {
    path: '/selection-flow-board',
    name: '选品看板',
    asyncComponent: () => import('./pages/selection-flow-board'),
    children: [
      {
        path: '/export-list-selected-goods-all-flow-board',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },

  {
    path: '/live-monitoring',
    name: '抖音直播间',
    topHeaderType: TOP_HEADER_TYPE.MONITORING,
    exact: true,
    asyncComponent: () =>
      import(/* webpackChunkName: "polymerization_goods" */ './pages/dy-live/index'),
    // children: [
    //   {
    //     path: '/session-details',
    //     name: '场次详情',
    //     menu: MENU_TYPE.NONE,
    //     topHeaderType: TOP_HEADER_TYPE.MONITORING,
    //     asyncComponent: () =>
    //       import(/* webpackChunkName: "polymerization-goods-detail" */ './pages/session-details'),
    //   },
    // ],
  },

  {
    path: '/open-off-live',
    name: '开关直播间',
    asyncComponent: () => import('./pages/open-off-live/index'),
    children: [
      {
        path: '/export-list-open-off-live',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/open-off-live-detail',
    name: '开关直播间详情',
    asyncComponent: () => import('./pages/open-off-live/detail/index'),
  },
  {
    path: '/dy-head-plan-manage',
    name: '抖音团长计划管理',
    asyncComponent: () => import('./pages/dy-head-plan'),
  },
  {
    path: '/dy-head-plan-detail/:cId',
    name: '抖音团长计划管理详情',
    asyncComponent: () => import('./pages/dy-head-plan/Detail'),
  },
  {
    path: '/dy-head-link-manage',
    name: '抖音团长链接管理',
    asyncComponent: () => import('./pages/dy-head-link'),
  },
  {
    path: '/dy-promotion-strategy-manage',
    name: '抖音推广策略管理',
    asyncComponent: () => import('./pages/dy-promotion-strategy'),
  },
  {
    path: '/operation-process-board',
    name: '运营看板',
    asyncComponent: () => import('./pages/operation-process-board'),
  },
  {
    path: '/quality-assurance-cooperation',
    name: '保量合同管理',
    asyncComponent: () => import('./pages/quality-assurance-cooperation'),
    children: [
      {
        path: '/quality-assurance-cooperation-list/edit',
        name: '保量合同管理编辑',
        asyncComponent: () => import('./pages/quality-assurance-cooperation/InfoEdit/index'),
      },
    ],
  },
  {
    path: '/quality-assurance-cooperation-export',
    name: '保量合同导出记录',
    exact: true,
    asyncComponent: () => import('./pages/export-list'),
  },
  {
    path: '/quality-assurance-cooperation-list/supplierMainEdit',
    name: '保量合同详情',
    asyncComponent: () => import('./pages/quality-assurance-cooperation/supplierMainEdit/index'),
  },
  {
    path: '/quality-assurance-cooperation-list/supplier-export',
    name: '保量合同导出记录',
    asyncComponent: () => import('./pages/export-list'),
  },
  {
    path: '/quality-assurance-cooperation-list/info',
    name: '保量合同管理详情',
    asyncComponent: () => import('./pages/quality-assurance-cooperation/InfoEdit/index'),
  },
  {
    path: '/quality-assurance-cooperation-list/form-edit',
    name: '保量合同管理编辑',
    asyncComponent: () => import('./pages/quality-assurance-cooperation/formPage'),
  },
  {
    path: '/quality-assurance-cooperation-list/form',
    name: '保量合同管理新增',
    asyncComponent: () => import('./pages/quality-assurance-cooperation/formPage'),
  },
  {
    path: '/system-code',
    name: '系统代码',
    asyncComponent: () => import('./pages/system-code'),
  },
  {
    path: '/system-code-add',
    name: '系统代码操作',
    asyncComponent: () => import('./pages/system-code/add-page'),
  },
  {
    path: '/system-code-detail',
    name: '系统代码详情',
    asyncComponent: () => import('./pages/system-code/detail'),
  },
  {
    path: '/notice-list',
    name: '消息列表',
    asyncComponent: () => import('./pages/notice-list'),
  },
  {
    path: '/paas-notice-list',
    name: '审批中心',
    asyncComponent: () => import('./pages/paas-notice-list'),
  },
  {
    path: '/paas-jiaopiao-page',
    name: '交票页面',
    asyncComponent: () => import('./pages/paas-jiaopiao-page/index'),
  },
  {
    path: '/system-parameter',
    name: '系统参数',
    asyncComponent: () => import('./pages/system-parameter'),
  },
  {
    path: '/system-parameter-add',
    name: '系统参数操作',
    asyncComponent: () => import('./pages/system-parameter/add-page'),
  },
  {
    path: '/system-parameter-detail',
    name: '系统参数详情',
    asyncComponent: () => import('./pages/system-parameter/detail'),
  },
  {
    path: '/session-targets',
    name: '场次目标',
    asyncComponent: () => import('./pages/session-targets'),
  },
  {
    path: '/session-targets-add',
    name: '新建场次目标',
    asyncComponent: () => import('./pages/session-targets/Add/index'),
  },
  {
    path: '/session-targets-edit',
    name: '编辑场次目标',
    asyncComponent: () => import('./pages/session-targets/Add/index'),
  },
  {
    path: '/system-setting/selection-tag-configuration',
    name: '选品流程标签配置',
    asyncComponent: () =>
      /* webpackChunkName: "pages_employee-list" */
      import('./pages/setting/selection-tag-configuration'),
  },
  {
    path: '/selection-tag-configuration/add',
    name: '新建选品流程标签配置',
    exact: true,
    asyncComponent: () => import('./pages/setting/selection-tag-configuration/Add'),
  },
  {
    path: '/selection-tag-configuration/update',
    name: '编辑选品流程标签配置',
    exact: true,
    asyncComponent: () => import('./pages/setting/selection-tag-configuration/Add'),
  },
  {
    path: '/selection-tag-configuration/detail',
    name: '选品流程标签配置详情',
    exact: true,
    asyncComponent: () => import('./pages/setting/selection-tag-configuration/detail'),
  },
  {
    path: '/system-setting/especially-approval-lifespan',
    name: '特批有效期配置',
    asyncComponent: () =>
      /* webpackChunkName: "pages_employee-list" */
      import('./pages/setting/especially-approval-lifespan'),
  },
  {
    path: '/system-setting/especially-approval-lifespan-form',
    name: '特批有效期配置添加配置',
    asyncComponent: () =>
      /* webpackChunkName: "pages_employee-list" */
      import('./pages/setting/especially-approval-lifespan/FormPage'),
  },
  {
    path: '/system-setting/special-approval-quota',
    name: '特批额度管理',
    asyncComponent: () =>
      /* webpackChunkName: "pages_employee-list" */
      import('./pages/setting/special-approval-quota'),
  },
  {
    path: '/system-setting/special-approval-quota-detail',
    name: '特批额度日志',
    asyncComponent: () =>
      /* webpackChunkName: "pages_employee-list" */
      import('./pages/setting/special-approval-quota/components/detail'),
  },
  {
    path: '/anchor-information',
    name: '主播信息',
    asyncComponent: () =>
      import(/* webpackChunkName: "anchor-information" */ './pages/anchor-information/index'),
    children: [
      {
        path: '/export-list-anchor-information-records',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/anchor-liveDate',
    name: '直播日历',
    asyncComponent: () =>
      import(/* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-schedule/index'),
  },
  {
    path: '/anchor-information-add',
    name: '添加主播',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-information" */ './pages/anchor-information/addAnchorInfo/index'
      ),
  },
  {
    path: '/anchor-information-edit',
    name: '编辑主播',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-information" */ './pages/anchor-information/addAnchorInfo/index'
      ),
  },
  {
    path: '/anchor-information-detail',
    name: '主播详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-information" */ './pages/anchor-information/anchorInfoDetail/index'
      ),
  },
  {
    path: '/anchor-information-salary',
    name: '主播薪酬',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-information" */ './pages/anchor-information/anchorInfoDetail/index'
      ),
  },
  {
    path: '/anchor-duration-statistics',
    name: '主播时长管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-duration-statistics/index'
      ),
    children: [
      {
        path: '/export-list-anchor-duration-statistics',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/anchor-duration-statistics-record',
    name: '记录/编辑主播时长',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-duration-statistics/anchor-duration-statistics-record/index'
      ),
  },
  {
    path: '/anchor-duration-statistics-create',
    name: '添加/编辑主播排期',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-duration-statistics/anchor-duration-statistics-create/index'
      ),
  },
  {
    path: '/anchor-duration-statistics-detail',
    name: '主播时长详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-duration-statistics/anchor-duration-statistics-detail/index'
      ),
  },
  {
    path: '/anchor-performance',
    name: '主播奖惩管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-performance/index'
      ),
  },
  {
    path: '/export-list-anchor-performance',
    name: '主播奖惩导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/go-on-board',
    name: '上播看板',
    asyncComponent: () => import('./pages/goOnBoard'),
  },
  {
    path: '/product-delisting',
    name: '未提报下架商品',
    asyncComponent: () => import('./pages/productDelisting'),
  },
  {
    path: '/product-delisting-detail',
    name: '未提报下架商品详情',
    asyncComponent: () => import('./pages/productDelisting/detail'),
  },
  {
    path: '/qualification-whitelist',
    name: '资质白名单管理',
    asyncComponent: () => import('./pages/qualification-whitelist/index'),
  },
  {
    path: '/export-list-qualification-white',
    name: '资质白名单管理导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/qualification-whitelist-add',
    name: '资质白名单管理新增',
    asyncComponent: () => import('./pages/qualification-whitelist/FormPage/index'),
  },
  {
    path: '/qualification-whitelist-edit',
    name: '资质白名单管理编辑',
    asyncComponent: () => import('./pages/qualification-whitelist/FormPage/index'),
  },
  {
    path: '/qualification-whitelist-detail',
    name: '资质白名单管理详情',
    asyncComponent: () => import('./pages/qualification-whitelist/DetailPage/index'),
  },

  {
    path: '/certification-audit',
    name: '资质审核',
    asyncComponent: () => import('./pages/certification-audit'),
  },
  {
    path: '/anchor-performance-create',
    name: '添加/编辑奖惩',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-performance/anchor-performance-create/index'
      ),
  },
  {
    path: '/anchor-performance-detail',
    name: '奖惩详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-performance/anchor-performance-detail/index'
      ),
  },
  {
    path: '/business-opportunity',
    name: '商机管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-business-opportunity/index'
      ),
  },
  {
    path: '/business-opportunity-info',
    name: '商机详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-business-opportunity/anchor-business-opportunity-detail/index'
      ),
  },
  {
    path: '/anchor-commercial-order',
    name: '主播商单管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-commercial-order/index'
      ),
  },
  {
    path: '/export-list-anchor-commercial-order',
    name: '主播商单导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/anchor-commercial-order-add',
    name: '创建主播商单管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-information" */ './pages/anchor-commercial-order/add-commercial-order/index'
      ),
  },
  {
    path: '/anchor-commercial-order-edit',
    name: '编辑主播商单管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-information" */ './pages/anchor-commercial-order/add-commercial-order/index'
      ),
  },
  {
    path: '/anchor-commercial-order-detail',
    name: '主播商单管理详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-information" */ './pages/anchor-commercial-order/anchor-commercial-order-detail/index'
      ),
  },
  {
    path: '/artist-sharing-cost',
    name: '艺人分成成本',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/artist-sharing-cost/index'
      ),
  },
  {
    path: '/anchor-confirm',
    name: '主播结算确认',
    asyncComponent: () =>
      import(/* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-confirm/index'),
  },
  {
    path: '/anchor-holiday',
    name: '主播报休管理',
    asyncComponent: () =>
      import(/* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-holiday/index'),
  },
  {
    path: '/anchor-schedule',
    name: '主播排期管理',
    asyncComponent: () =>
      import(/* webpackChunkName: "anchor-duration-statistics" */ './pages/anchor-schedule/index'),
  },
  {
    path: '/anchor-personal-income-tax',
    name: '主播个人所得税',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-personal-income-tax" */ './pages/anchor-personal-income-tax/index'
      ),
    children: [
      {
        path: '/export-list-anchor-personal-income-tax',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/company-bank-account',
    name: '公司管理',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/company-bank-account/index'
      ),
  },
  {
    path: '/company-bank-account-detail/:sid',
    name: '公司管理详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/company-bank-account/components/detailModal/index'
      ),
  },
  {
    path: '/company-bank-account-create',
    name: '新建公司',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/company-bank-account/components/createModal/index'
      ),
  },
  {
    path: '/company-bank-account-update',
    name: '编辑公司',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "anchor-duration-statistics" */ './pages/company-bank-account/components/createModal/index'
      ),
  },
  {
    path: '/automated-audit-rules',
    name: '自动化审核规则',
    asyncComponent: () => import('./pages/qualification-audit-result/automated-audit-rules'),
    children: [],
  },
  {
    path: '/business-automated-audit-rules-add',
    name: '商家自动化审核规则新增',
    asyncComponent: () =>
      import(
        './pages/qualification-audit-result/automated-audit-rules/business-automated-audit-rules/AddPage'
      ),
  },

  {
    path: '/business-automated-audit-rules-edit',
    name: '商家自动化审核规则编辑',
    asyncComponent: () =>
      import(
        './pages/qualification-audit-result/automated-audit-rules/business-automated-audit-rules/AddPage'
      ),
  },
  {
    path: '/business-automated-audit-rules-detail',
    name: '商家自动化审核规则详情',
    asyncComponent: () =>
      import(
        './pages/qualification-audit-result/automated-audit-rules/business-automated-audit-rules/DetailPage'
      ),
  },
  {
    path: '/brand-automated-audit-rules-add',
    name: '品牌自动化审核规则新增',
    asyncComponent: () =>
      import(
        './pages/qualification-audit-result/automated-audit-rules/brand-automated-audit-rules/AddPage'
      ),
  },
  {
    path: '/brand-automated-audit-rules-edit',
    name: '品牌自动化审核规则编辑',
    asyncComponent: () =>
      import(
        './pages/qualification-audit-result/automated-audit-rules/brand-automated-audit-rules/AddPage'
      ),
  },
  {
    path: '/brand-automated-audit-rules-detail',
    name: '品牌自动化审核规则详情',
    asyncComponent: () =>
      import(
        './pages/qualification-audit-result/automated-audit-rules/brand-automated-audit-rules/DetailPage'
      ),
  },
  {
    path: '/quality-test-rules-list',
    name: '质检规则',
    asyncComponent: () => import('./pages/quality-test-rules/index'),
  },
  {
    path: '/quality-test-rules-form',
    name: '质检规则新增/编辑',
    asyncComponent: () => import('./pages/quality-test-rules/formPage'),
  },
  {
    path: '/quality-test-rules-edit',
    name: '质检规则详情',
    asyncComponent: () => import('./pages/quality-test-rules/detailPage'),
  },
  {
    path: '/quality-inspection-task-list',
    name: '质检任务',
    asyncComponent: () => import('./pages/quality-inspection-task/index'),
  },
  {
    path: '/quality-inspection-task-form',
    name: '质检任务登记/编辑',
    asyncComponent: () => import('./pages/quality-inspection-task/formPage'),
  },
  {
    path: '/quality-inspection-task-detail',
    name: '质检任务详情',
    asyncComponent: () => import('./pages/quality-inspection-task/detail'),
  },
  {
    path: '/guaranteed-ratio',
    name: '保比合作管理',
    asyncComponent: () =>
      import(/* webpackChunkName: "guaranteed-ratio" */ './pages/guaranteed-ratio/index'),
  },
  {
    path: '/expend-contract',
    name: '支出合同管理',
    asyncComponent: () => import('./pages/expend-contract/index'),
  },
  {
    path: '/expend-contract-form',
    name: '支出合同管理新增/编辑',
    asyncComponent: () => import('./pages/expend-contract/FormPage/index'),
  },
  {
    path: '/expend-contract-detail',
    name: '支出合同管理详情',
    asyncComponent: () => import('./pages/expend-contract/detailPage'),
  },
  {
    path: '/goods-brand-store',
    name: '品牌管理',
    asyncComponent: () =>
      import(/* webpackChunkName: "guaranteed-ratio" */ './pages/goods-brand-store/index'),
  },
  {
    path: '/qualification-whitelist-category-rules',
    name: '资质白名单规则配置',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "pages_data-management" */ './pages/setting/qualification-whitelist-category-rules'
      ),
  },
  {
    path: '/queues-review-task',
    name: '资质复查任务',
    asyncComponent: () =>
      import(/* webpackChunkName: "guaranteed-ratio" */ './pages/review-task/index'),
  },
  {
    path: '/marketing-data',
    name: '营销数据',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-data'
      ),
    children: [
      {
        path: '/export-list-marketing-data-records',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/marketing-data-edit',
    name: '编辑营销数据',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-data/addMarketingData'
      ),
  },
  {
    path: '/marketing-data-create',
    name: '新建营销数据',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-data/addMarketingData'
      ),
  },
  {
    path: '/marketing-data-detail',
    name: '营销数据详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-data/marketingDataDetail'
      ),
  },
  {
    path: '/marketing-expenses',
    name: '营销费用',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-expenses'
      ),
    children: [
      {
        path: '/export-list-marketing-expenses-records',
        name: '导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/marketing-expenses-create',
    name: '新建营销费用',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-expenses/MarketingExpenses/addMarketingExpenses'
      ),
  },
  {
    path: '/marketing-expenses-edit',
    name: '编辑营销费用',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-expenses/MarketingExpenses/addMarketingExpenses'
      ),
  },
  {
    path: '/marketing-expenses-detail',
    name: '营销费用详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-expenses/MarketingExpenses/DetailPage'
      ),
  },
  {
    path: '/marketing-expenses-business-detail',
    name: '费用业务详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "marketing-data" */ './pages/marketing-management/marketing-expenses/MarketingExpenses/ExpensesBusinessDetailPage'
      ),
  },
  {
    path: '/session-limit-config',
    name: '场次额度配置',
    asyncComponent: () => import('./pages/session-limit-config/index'),
  },
  {
    path: '/shop-brand-qualification',
    name: '店铺品牌资质',
    asyncComponent: () => import('./pages/shop-brand-qualification/index'),
  },
  {
    path: '/shop-brand-qualification-detail',
    name: '店铺品牌资质详情',
    asyncComponent: () => import('./pages/shop-brand-qualification/detail'),
  },

  {
    path: '/cost-settlement',
    name: '成本结账',
    asyncComponent: () =>
      import(/* webpackChunkName: "guaranteed-ratio" */ './pages/cost-settlement'),
  },
  {
    path: '/cost-settlement-detail',
    name: '成本结账详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "guaranteed-ratio" */ './pages/cost-settlement/costSettlementDetail'
      ),
  },
  {
    path: '/export-list-settlement-summary-records',
    name: '事业部-直播间汇总导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/export-list-settlement-list-records',
    name: '结账明细导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/mouth-audit',
    name: '口播审核',
    asyncComponent: () => import('./pages/mouth-audit/index'),
  },
  {
    path: '/high-risk-limit',
    name: '特批额度看板',
    asyncComponent: () =>
      import(/* webpackChunkName: "guaranteed-ratio" */ './pages/high-risk-limit'),
  },
  {
    path: '/high-risk-limit-detail',
    name: '特批额度看板详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "guaranteed-ratio" */ './pages/high-risk-limit/high-risk-limit-detail'
      ),
  },
  {
    path: '/quality-assurance-plan-statistics',
    name: '保量计划统计',
    asyncComponent: () => import('./pages/quality-assurance-plan-statistics/index'),
    children: [],
  },
  {
    path: '/quality-assurance-plan-statistics-total/export',
    name: '保量计划汇总导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/quality-assurance-plan-statistics-detail/export',
    name: '保量计划明细导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/export-list-selected-goods-all-board-group',
    name: '项目组服务类型导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  ...settingRoutes,
  {
    path: '/high-risk-limit',
    name: '特批额度看板',
    asyncComponent: () =>
      import(/* webpackChunkName: "guaranteed-ratio" */ './pages/high-risk-limit'),
  },
  {
    path: '/high-risk-limit-detail',
    name: '特批额度看板详情',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "guaranteed-ratio" */ './pages/high-risk-limit/high-risk-limit-detail'
      ),
  },
  {
    path: '/summary-costs',
    name: '成本汇总表',
    asyncComponent: () => import('./pages/summary-costs/index'),
    children: [],
  },
  {
    path: '/summary-costs-total/export',
    name: '成本汇总表导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/workOrder-manager',
    name: '工单管理',
    asyncComponent: () => import('./pages/work-order-manager/index'),
    children: [],
  },
  {
    path: '/workOrder-manager-add',
    name: '新建工单',
    asyncComponent: () => import('./pages/work-order-manager/addWorkOrder/index'),
    children: [],
  },
  {
    path: '/workOrder-manager-edit',
    name: '编辑工单',
    asyncComponent: () => import('./pages/work-order-manager/addWorkOrder/edit'),
    children: [],
  },
  {
    path: '/workOrder-manager-detail',
    name: '工单详情',
    asyncComponent: () => import('./pages/work-order-manager/workOrderDetail/index'),
    children: [],
  },
  {
    path: '/export-list-workOrder',
    name: '工单导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/userProfile-manager',
    name: '用户档案',
    asyncComponent: () => import('./pages/user-profile-manager/index'),
    children: [],
  },
  {
    path: '/userProfile-add',
    name: '新建真实信息档案',
    asyncComponent: () => import('./pages/user-profile-manager/UserProfile/addUserProfile/index'),
    children: [],
  },
  {
    path: '/userProfile-edit',
    name: '编辑真实信息档案',
    asyncComponent: () => import('./pages/user-profile-manager/UserProfile/addUserProfile/edit'),
    children: [],
  },
  {
    path: '/userProfile-info',
    name: '真实信息档案详情',
    asyncComponent: () =>
      import('./pages/user-profile-manager/UserProfile/UserProfileDetail/index'),
    children: [],
  },
  {
    path: '/userProfile-export',
    name: '用户信息导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/speUserProfile-add',
    name: '新建特殊用户档案',
    asyncComponent: () =>
      import('./pages/user-profile-manager/SpecialUserFile/addUserProfile/index'),
    children: [],
  },
  {
    path: '/speUserProfile-edit',
    name: '编辑特殊用户档案',
    asyncComponent: () =>
      import('./pages/user-profile-manager/SpecialUserFile/addUserProfile/edit'),
    children: [],
  },
  {
    path: '/speUserProfile-info',
    name: '特殊用户档案详情',
    asyncComponent: () =>
      import('./pages/user-profile-manager/SpecialUserFile/UserProfileDetail/index'),
    children: [],
  },
  {
    path: '/speUserProfile-export',
    name: '特殊用户信息导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/userBlack-add',
    name: '新建用户黑名单',
    asyncComponent: () => import('./pages/user-profile-manager/UserBlackList/addUserProfile/index'),
    children: [],
  },
  {
    path: '/userBlack-edit',
    name: '编辑用户黑名单',
    asyncComponent: () => import('./pages/user-profile-manager/UserBlackList/addUserProfile/edit'),
    children: [],
  },
  {
    path: '/userBlack-info',
    name: '用户黑名单详情',
    asyncComponent: () =>
      import('./pages/user-profile-manager/UserBlackList/UserProfileDetail/index'),
    children: [],
  },
  {
    path: '/userBlack-export',
    name: '用户黑名单导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/userWhite-add',
    name: '新建用户白名单',
    asyncComponent: () => import('./pages/user-profile-manager/UserWhiteList/addUserProfile/index'),
    children: [],
  },
  {
    path: '/userWhite-edit',
    name: '编辑用户白名单',
    asyncComponent: () => import('./pages/user-profile-manager/UserWhiteList/addUserProfile/edit'),
    children: [],
  },
  {
    path: '/userWhite-info',
    name: '用户白名单详情',
    asyncComponent: () =>
      import('./pages/user-profile-manager/UserWhiteList/UserProfileDetail/index'),
    children: [],
  },
  {
    path: '/userWhite-export',
    name: '用户白名单导出记录',
    asyncComponent: () => import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
  },
  {
    path: '/friendSeek',
    name: 'BefriendsAI',
    asyncComponent: () => import('./pages/qiankun/ai'),
  },
  {
    path: '/traffic-delivery-config',
    name: '流量投放映射配置',
    asyncComponent: () => import('./pages/setting/traffic-delivery-config'),
  },
  {
    path: '/explosive-product-trend-prediction',
    name: '爆品趋势预测',
    asyncComponent: () => import('./pages/explosive-product-trend-prediction'),
    children: [
      {
        path: '/export-list-explosive-product-trend-prediction',
        name: '爆品趋势预测导出记录',
        asyncComponent: () =>
          import(/* webpackChunkName: "pages_export-list" */ './pages/export-list'),
      },
    ],
  },
  {
    path: '/paas-detail-page',
    name: '审批详情',
    exact: true,
    asyncComponent: () => import('./pages/paas-notice-list/compents/detailPage'),
  },
  {
    path: '/trend-hit-rules',
    name: '趋势命中规则配置',
    exact: true,
    asyncComponent: () => import('./pages/trend-hit-rules/index'),
  },
  {
    path: '/trend-hit-rules-add',
    name: '趋势命中规则配置新建',
    exact: true,
    asyncComponent: () => import('./pages/trend-hit-rules/add-page/index'),
  },
  {
    path: '/live-video-surveillance',
    name: '直播视频监控',
    exact: true,
    asyncComponent: () => import('./pages/live-video-surveillance/index'),
  },
];
const routes = Tile([
  ...routesTree,
  ...systemConfigRoutes,
  ...legalAudit,
  ...liveMan,
  ...supplier,
  ...goodsCenter,
  ...businessCRM,
  ...selectionMenu,
  ...warehouseManagementRoutes,
  ...webiboRoutes,
  ...companyManage,
]);

const homeRoutes = [
  {
    path: '/login',
    asyncComponent: () => import(/* webpackChunkName: "pages_login" */ './pages/login'),
  },
  {
    path: '/find-password',
    asyncComponent: () =>
      import(/* webpackChunkName: "pages_changePassword" */ './pages/change-password'),
  },
  {
    path: '/403',
    asyncComponent: () => import(/* webpackChunkName: "pages_403" */ './pages/403'),
  },
  {
    path: '/auth',
    asyncComponent: () => import(/* webpackChunkName: "auth" */ './pages/auth'),
  },
  {
    path: '/platform-supplier-newGoods',
    name: '新建商品',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "platform-supplier-newGoods" */ './pages/goods-assorting/newGoods/index'
      ),
  },
  {
    path: '/authorize/crm',
    name: '授权',
    asyncComponent: () =>
      import(/* webpackChunkName: "platform-supplier-newGoods" */ './pages/authorize-crm/index'),
  },
  {
    path: '/business-leads-open',
    name: '新增商机线索',
    asyncComponent: () =>
      import(
        /* webpackChunkName: "platform-supplier-newGoods" */ './pages/business-leads/FormPage'
      ),
  },
];
// 审核未通过下的, 包括未开店
const auditDidNotPass: any[] = [];

const allRoutes = [...routes, ...homeRoutes, ...auditDidNotPass];

const allRoutePath = allRoutes.map((item) => item.path);

export { routesTree, routes, homeRoutes, auditDidNotPass, allRoutePath, allRoutes };
