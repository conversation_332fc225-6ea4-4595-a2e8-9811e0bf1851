@import '../../web-common-modules/styles/token.less';

.web-crm-jgpy {
  .ant-table-tbody > tr > td,
  .ant-table-thead > tr > th {
    // border-bottom: 1px solid #ebedf0;
  }

  .ant-table-wrapper .ant-table-thead > tr > th {
    background-color: #f0f4f7 !important;
    color: #444444 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 10px 8px;
    line-height: 19px;
  }

  .ant-popover {
    .ant-popover-inner {
      border-radius: 4px;
      box-shadow: @shadows-down-2;

      .ant-popover-inner-content {
        padding: 10px 12px;
      }
    }
  }
}
.ant-modal {
  .ant-table-wrapper .ant-table-thead > tr > th {
    background-color: #f0f4f7 !important;
    color: #444444 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 10px 8px;
    line-height: 19px;
  }
}
.ant-tabs-tab {
  font-size: 13px !important;
  font-weight: 400 !important;
}
.ant-tabs-tab-active {
  font-weight: 500 !important;
}
.ant-tabs-ink-bar {
  height: 3px;
  border-radius: 5px;
  background: #204eff;
}
//全局修改背景色
.ant-table-row:nth-child(even) {
  background-color: #f8f8f9; /* 设置偶数行背景色 */
}
.ant-table-thead > tr > th {
  background-color: #f0f4f7 !important; /* 设置表头背景色为#F0F4F7 */
}
.ant-popover {
  .ant-popover-title {
    padding: 6px 8px;
    color: #111111;
    font-size: 14px;
    line-height: 22px;
  }
  .ant-popover-inner-content {
    padding: 8px;
  }
}
.ant-empty-image {
  svg {
    display: none;
  }
}
/* 在全局CSS文件中定义显示图片的样式 */
.ant-empty-image::before {
  content: url('../assets/fail-left.png'); /* 替换为你想要显示的图片路径 */
  display: block;
  width: 100px; /* 设置图片宽度 */
  height: 100px; /* 设置图片高度 */
  margin: 0 auto; /* 居中显示 */
}
.ant-empty-description {
  position: relative;
  top: 40px;
  color: #666;
  font-size: 14px;
}
//全局日期选择框的弹出框下移40px
.ant-calendar {
  top: 40px !important;
}

.ant-modal {
  .ant-modal-header {
    padding: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #111111;
  }
  .ant-modal-body {
    padding: 16px !important;
    .ant-form-item {
      margin-bottom: 12px;
    }
  }
}
.ant-table-fixed-header .ant-table-scroll .ant-table-header {
  height: 40px !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}
.ant-table-header .ant-table-hide-scrollbar {
  padding: 0;
}
.ant-upload.ant-upload-select-picture-card {
  width: 60px;
  height: 60px;
}
.ant-btn-sm {
  padding: 0 12px;
}
.ant-tag {
  border-radius: 4px;
}
.ant-table-fixed-right {
  z-index: 0;
}
#notice {
  .ant-popover {
    top: 20px !important;
  }
}

.ant-message {
  z-index: 9999;
}
