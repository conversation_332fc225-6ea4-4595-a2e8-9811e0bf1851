import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnProps } from 'antd/es/table/interface';
import styles from '@/styles/index.module.less';
import style from '../index.module.less';
import { Popover, Icon, Tag, Input, Popconfirm } from 'antd';
import { copyText } from 'web-common-modules/utils/copy';
import { newRenderPlatformSourceLogo } from 'web-common-modules/utils/newplatform';
import moment from 'moment';
import { LEVEL_NAME, LEVEL } from '../../../../../web_modules/types';
import { IconStatus } from '@/components/GoodsInfoEdit/leftCard';
import { DetailRefType } from '../components/SubjectDetail';
import { checkAuth } from 'qmkit';
import { ListInfoType } from './useList';
import { ApproveType } from './useSearch';
import { SupplierBodySpecialAuditInvalidRequest } from '../../services';
export enum QualificationAuditStateEnum {
  PASS = '合格',
  NO_PASS = '不合格',
  NONE = '未处理',
}

export enum QualificationRiskLevelEnum {
  'HIGH' = '高风险',
  'MIDDLE' = '中风险',
  'LOW' = '低风险',
  'PASS' = '通过',
  'NONE' = '待审核',
}
export enum ApproveStatus {
  WAIT_AUDIT = '待审核',
  CONFIRMING = '审核中',
  PASS = '通过',
  REJECT = '驳回',
  INVALID = '已失效',
}
export enum SubjectApproveStatus {
  WAIT_AUDIT = '审核中',
  CONFIRMING = '审核中',
  PASS = '通过',
  REJECT = '驳回',
  INVALID = '已失效',
}
export enum QualificationBizTypeEnum {
  SUPPLIER = '商家',
  BRAND = '品牌',
  SHOP = '店铺',
  GOODS = '商品',
  BP_BRAND = '品牌',
  BP_GOODS = '商品',
  BP_SHOP = '店铺',
}
export enum SpecialAuditStatus {
  WAIT_AUDIT = '待审核',
  CONFIRMING = '审核中',
  PASS = '通过',
  REJECT = '驳回',
}
export const useTable = (
  detailRef: React.MutableRefObject<DetailRefType | undefined>,
  auditInvalid: (params: SupplierBodySpecialAuditInvalidRequest) => void,
) => {
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState<ListInfoType[]>([]);
  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: ListInfoType[]) => {
      console.log(selectedRowKeys);
      setSelectedKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
  };

  const columns = useMemo(() => {
    return [
      {
        title: '#',
        align: 'center',
        key: 'key',
        dataIndex: 'key',
        className: styles['table-number'],
        render: (key, records, index) => index + 1,
        width: 40,
      },
      {
        title: '商家名称',
        key: 'supplierOrgName',
        dataIndex: 'supplierOrgName',
        width: 150,
        render: (_, records) => {
          return records?.supplierOrgName ? (
            <p
              className={style['goods-name-line']}
              style={{ marginRight: '4px' }}
              onClick={() => {
                checkAuth('f_subject_records_detail') &&
                  detailRef?.current?.onOpen({
                    id: records.id!,
                  });
              }}
            >
              {records?.supplierOrgName}
            </p>
          ) : (
            '-'
          );
        },
      },
      {
        title: '事业部',
        key: 'deptName',
        dataIndex: 'deptName',
        width: 100,
        render: (val) => val ?? '-',
      },
      {
        title: '直播间',
        key: 'liveRoomName',
        dataIndex: 'liveRoomName',
        width: 100,
        render: (val) => val ?? '-',
      },
      {
        title: '申请人',
        kay: 'creatorName',
        dataIndex: 'creatorName',
        render: (creatorName: string, records) => {
          return (
            <>
              <p>{creatorName ?? '-'} </p>
              <p>
                {' '}
                {records?.gmtCreated
                  ? moment(records?.gmtCreated).format('YYYY-MM-DD HH:mm:ss')
                  : '-'}
              </p>
            </>
          );
        },
        width: 100,
      },
      {
        title: '审批状态',
        kay: 'status',
        dataIndex: 'status',
        render: (status: 'WAIT_AUDIT' | 'CONFIRMING' | 'PASS' | 'REJECT' | 'INVALID') => {
          return <>{status ? SubjectApproveStatus[status] : '-'}</>;
        },
        width: 100,
      },
      {
        title: '审批时间',
        kay: 'auditTime',
        dataIndex: 'auditTime',
        render: (auditTime: ListInfoType['auditTime']) => {
          return <>{auditTime ? moment(auditTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</>;
        },
        width: 100,
      },

      {
        title: '审批用时',
        key: 'time',
        dataIndex: 'time',
        width: 100,
        render: (time, records) => {
          const startTime = records?.gmtCreated ? new Date(records?.gmtCreated).getTime() : null;
          const endTime = records?.auditTime ? new Date(records?.auditTime).getTime() : null;
          const dateTime = endTime && startTime ? endTime - startTime : null;
          const timestampToHours = (timestamp: number) => {
            const seconds = timestamp / 1000;
            const hours = seconds / 3600;
            return hours.toFixed(2);
          };
          return <p>{dateTime ? timestampToHours(dateTime) + '小时' : '-'}</p>;
        },
      },
      {
        title: '操作',
        key: 'id',
        dataIndex: 'id',
        width: 150,
        fixed: 'right',
        render: (id, records) =>
          checkAuth('f_subject_cancel_approvel') && (
            <Popconfirm
              getPopupContainer={() => document.body}
              title="确定要作废特批吗？"
              onConfirm={() => {
                auditInvalid({ id: records?.id });
              }}
            >
              {records?.status === 'PASS' && <a>作废特批</a>}
            </Popconfirm>
          ),
      },
    ] as ColumnProps<ListInfoType>[];
  }, [detailRef]);

  return {
    columns,
    rowSelection,
    selectedKeys,
    selectedRows,
  };
};
