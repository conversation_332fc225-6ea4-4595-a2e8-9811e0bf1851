import React, { useMemo, useState } from 'react';
import { ColumnProps } from 'antd/es/table/interface';
import styles from '@/styles/index.module.less';
import style from '../index.module.less';
import { Popover, Icon, Tag, Input } from 'antd';
import { copyText } from '@/utils/moduleUtils';
import { newRenderPlatformSourceLogo } from '@/common/constants/platform';
import moment from 'moment';
import { IconStatus } from '@/components/GoodsInfoEdit/leftCard';
import { DetailRefType } from '../components/HighRiskDetail';
import { checkAuth } from 'qmkit';
import { ListInfoType } from './useList';
import { ApproveType } from './useSearch';
export enum QualificationAuditStateEnum {
  PASS = '合格',
  NO_PASS = '不合格',
  NONE = '未处理',
}

export enum QualificationRiskLevelEnum {
  'HIGH' = '高风险',
  'MIDDLE' = '中风险',
  'LOW' = '低风险',
  'PASS' = '通过',
  'NONE' = '待审核',
  'HIGH_SPECIAL' = '高风险-特',
}
export enum ApproveStatus {
  WAIT_AUDIT = '待审核',
  CONFIRMING = '审核中',
  PASS = '通过',
  REJECT = '驳回',
  WITHDRAW = '撤回',
}
export enum QualificationBizTypeEnum {
  SUPPLIER = '商家',
  BRAND = '品牌',
  SHOP = '店铺',
  GOODS = '商品',
  BP_BRAND = '品牌',
  BP_GOODS = '商品',
  BP_SHOP = '店铺',
}
export enum SpecialAuditStatus {
  WAIT_AUDIT = '待审核',
  CONFIRMING = '审核中',
  PASS = '通过',
  REJECT = '驳回',
}
enum QualificationReviewTaskStatusEnum {
  FINISH = '已完成',
  WAIT_REVIEW = '待复查',
  WAIT_CHECK = '待确认',
}
enum QualificationReviewTaskStatusColorEnum {
  FINISH = 'green',
  WAIT_REVIEW = 'orange',
  WAIT_CHECK = 'blue',
}
export const useTable = (HighRiskDetailRef: React.MutableRefObject<DetailRefType | undefined>) => {
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState<ListInfoType[]>([]);
  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: ListInfoType[]) => {
      console.log(selectedRowKeys);
      setSelectedKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
  };

  const columns = useMemo(() => {
    return [
      {
        title: '#',
        align: 'center',
        key: 'key',
        dataIndex: 'key',
        className: styles['table-number'],
        render: (key, records, index) => index + 1,
        width: 40,
      },
      {
        title: '图片',
        key: 'spuImage',
        dataIndex: 'spuImage',
        width: 80,
        render: (spuImage: string) => (
          <>
            {spuImage ? (
              <img src={spuImage} className={style['table-img']} style={{ objectFit: 'cover' }} />
            ) : (
              '-'
            )}
          </>
        ),
      },
      {
        title: '商品信息',
        key: 'goodsMsg',
        dataIndex: 'goodsMsg',
        width: 204,
        render: (_, records) => {
          return (
            <div>
              <div className={style['goods-name']}>
                {/* 显示详情 */}
                <Popover title="商品名称" content={records?.spuName}>
                  <p
                    className={style['goods-name-line']}
                    style={{ marginRight: '4px' }}
                    onClick={() => {
                      checkAuth('f_high_risk_records_detail') &&
                        HighRiskDetailRef?.current?.onOpen({
                          id: records.id!,
                        });
                    }}
                  >
                    【{records?.brandName || '-'}】{records?.spuName || '-'}
                  </p>
                </Popover>
                <Popover
                  title="商品信息"
                  content={
                    <div>
                      <p>
                        商品编号:{records?.spuNo || '-'}
                        <Icon
                          onClick={() => {
                            copyText(records?.spuNo || '');
                          }}
                          type="copy"
                          style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                        />
                      </p>
                      <p>
                        平台商品ID: {records?.platformSpuId || '-'}
                        <Icon
                          onClick={() => {
                            copyText(records?.platformSpuId || '');
                          }}
                          type="copy"
                          style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                        />
                      </p>
                      <p>
                        选品编号: {records?.selectionRoundNo || '-'}
                        <Icon
                          onClick={() => {
                            copyText(records?.selectionRoundNo || '');
                          }}
                          type="copy"
                          style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                        />
                      </p>
                    </div>
                  }
                >
                  <img
                    src={
                      'https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images/icon/id.png'
                    }
                    style={{ width: '16px', height: '16px', marginLeft: '4px' }}
                    alt=""
                  />
                </Popover>
                {/* <div style={{ color: '#EE0000' }}>
                  {records?.minPrice === records?.maxPrice
                    ? `¥${records?.minPrice}`
                    : `¥${records?.minPrice} - ${records?.maxPrice}`}
                </div> */}
              </div>
              <div className={style['company']}>
                {records?.supplierOrgName ? (
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      window.open(`/provider-detail/${records?.supplierId}`);
                    }}
                    style={{ cursor: 'pointer' }}
                  >
                    {records?.supplierOrgName}
                  </div>
                ) : (
                  '-'
                )}
              </div>
            </div>
          );
        },
      },
      {
        title: '场次信息',
        key: 'session',
        dataIndex: 'session',
        width: 140,
        render: (_, records) => {
          return (
            <div>
              <div style={{ marginBottom: '8px' }}>
                {newRenderPlatformSourceLogo({ platform: records?.platform as any, width: 14 })}
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '12px',
                  lineHeight: '20px',
                }}
              >
                <span>
                  {records?.liveDateEnd ? moment(records?.liveDateEnd).format('YYYY-MM-DD') : '-'}
                </span>
              </div>
              <div>{records?.liveRoomName || '-'}</div>
            </div>
          );
        },
      },
      {
        title: '申请人',
        kay: 'creatorName',
        dataIndex: 'creatorName',
        render: (creatorName: string, records) => {
          return (
            <>
              <p>{creatorName ?? '-'} </p>
              <p>
                {' '}
                {records?.startAuditTime
                  ? moment(records?.startAuditTime).format('YYYY-MM-DD HH:mm:ss')
                  : '-'}
              </p>
            </>
          );
        },
        width: 100,
      },
      {
        title: '资质结果',
        kay: 'result',
        dataIndex: 'result',
        render: (_, records) => {
          return (
            <>
              <article style={{ color: 'black' }}>
                {records?.riskLevel
                  ? QualificationRiskLevelEnum[
                      records?.riskLevel as keyof typeof QualificationRiskLevelEnum
                    ]
                  : '-'}
              </article>
              {records?.qualificationItemAudit?.map((item) => (
                <article className="auditFw">
                  <span>{item?.bizType && QualificationBizTypeEnum[item?.bizType]}</span>
                  {item?.auditState && IconStatus[item?.auditState]?.icon}
                </article>
              ))}
            </>
          );
        },
        width: 100,
      },
      {
        title: '最新资质结果',
        kay: 'latestRiskLevel',
        dataIndex: 'latestRiskLevel',
        render: (_, records) => {
          return (
            <>
              <article style={{ color: 'black' }}>
                {records?.latestRiskLevel
                  ? QualificationRiskLevelEnum[
                      records?.latestRiskLevel as keyof typeof QualificationRiskLevelEnum
                    ]
                  : '-'}
              </article>
              {records?.latestQualificationItemAudit?.map((item) => (
                <article className="auditFw">
                  <span>{item?.bizType && QualificationBizTypeEnum[item?.bizType]}</span>
                  {item?.auditState && IconStatus[item?.auditState]?.icon}
                </article>
              ))}
            </>
          );
        },
        width: 100,
      },
      {
        title: '是否特殊材质',
        kay: 'specialMaerial',
        dataIndex: 'specialMaerial',
        render: (specialMaerial: 0 | 1) => {
          return <>{specialMaerial ? '是' : '否'}</>;
        },
        width: 100,
      },
      {
        title: '复查任务状态',
        kay: 'qualificationReviewTaskStatusEnum',
        dataIndex: 'qualificationReviewTaskStatusEnum',
        render: (val: ListInfoType['qualificationReviewTaskStatusEnum']) =>
          val ? (
            <Tag color={QualificationReviewTaskStatusColorEnum[val]}>
              {QualificationReviewTaskStatusEnum[val]}
            </Tag>
          ) : (
            '-'
          ),
        width: 100,
      },
      {
        title: '特批方式',
        kay: 'type',
        dataIndex: 'type',
        render: (type: ListInfoType['type']) => {
          return <>{type ? ApproveType[type] : '-'}</>;
        },
        width: 100,
      },
      {
        title: '审批状态',
        kay: 'status',
        dataIndex: 'status',
        render: (status: 'WAIT_AUDIT' | 'CONFIRMING' | 'PASS' | 'REJECT') => {
          return <>{status ? ApproveStatus[status] : '-'}</>;
        },
        width: 100,
      },
      {
        title: '审批时间',
        kay: 'auditTime',
        dataIndex: 'auditTime',
        render: (auditTime: ListInfoType['auditTime']) => {
          return <>{auditTime ? moment(auditTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</>;
        },
        width: 100,
      },

      {
        title: '审批用时',
        key: 'time',
        dataIndex: 'time',
        width: 100,
        render: (time, records) => {
          const startTime = records?.gmtCreated ? new Date(records?.gmtCreated).getTime() : null;
          const endTime = records?.auditTime ? new Date(records?.auditTime).getTime() : null;
          const dateTime = endTime && startTime ? endTime - startTime : null;
          const timestampToHours = (timestamp: number) => {
            const seconds = timestamp / 1000;
            const hours = seconds / 3600;
            return hours.toFixed(2);
          };
          return <p>{dateTime ? timestampToHours(dateTime) + '小时' : '-'}</p>;
        },
      },
    ] as ColumnProps<ListInfoType>[];
  }, [HighRiskDetailRef]);

  return {
    columns,
    rowSelection,
    selectedKeys,
    selectedRows,
    setSelectedKeys,
    setSelectedRows,
  };
};
