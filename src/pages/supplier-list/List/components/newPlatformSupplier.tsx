import React, { useImperative<PERSON><PERSON><PERSON>, useState } from 'react';
import { Form, Select, Input, Modal, Button, message } from 'antd';
import UserList from '@/components/UserList';
import OSSUpload from '@/components/OSSUploadV2';
import { newPlatformSupplier } from '@/services/yml/supplier/index';
import '../index.module.less';
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 5 },
};
interface FormProps {
  form: any; // 根据实际情况替换为正确的类型
  onRef: any; // 根据实际情况替换为正确的类型
  onSearch?: () => void;
}
const NewPlatformSupplier: React.FC<FormProps> = (props) => {
  const { form, onRef, onSearch } = props;
  const { getFieldDecorator, getFieldsValue } = form;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    form.validateFields((errors, values) => {
      if (!errors) {
        setLoading(true);
        const params = {
          ...values,
          workLicenseResourceId: values.workLicenseResourceId[0].resourceId,
        };
        // console.log(params);
        newPlatformSupplier({ ...params }).then(({ res }) => {
          //   console.log(res);
          if (res.code === '200') {
            message.success('新建成功');
            setIsModalOpen(false);
            onSearch();
          } else {
            message.error(res.message);
          }
          setLoading(false);
        });
      }
    });
    // setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const [images, setImages] = useState(null);

  useImperativeHandle(onRef, () => ({
    open: showModal,
  }));

  return (
    <div>
      <Modal
        title="新建商家"
        visible={isModalOpen}
        width={500}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
            确定
          </Button>,
        ]}
      >
        <Form.Item {...formItemLayout} label="商家主体名称">
          {getFieldDecorator('supplierCompanyName', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(<Input maxLength={50} className="modalInput300" placeholder="请输入" />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="商家类型">
          {getFieldDecorator('supplierType', {
            initialValue: 3,
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(
            <Select disabled className="modalInput300" allowClear>
              <Select.Option key={3} value={3}>
                平台型商家
              </Select.Option>
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="统一社会信用代码">
          {getFieldDecorator('uniformSocialCreditCode', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(<Input maxLength={50} className="modalInput300" placeholder="请输入" />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="邀请人">
          {getFieldDecorator('inviterId', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(<UserList className="modalInput300" />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="营业执照">
          {getFieldDecorator('workLicenseResourceId', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(
            <OSSUpload.Custom
              className="uploadNewStyle"
              maxLen={1}
              isImage
              width={50}
              height={50}
              size="small"
              typeCode="SPU_IMG"
              dataSource={images}
              maxSize={20 * 1024 * 1024}
              onChange={(list) => {
                setImages?.(list as DataSourceItem[]);
              }}
              accept={'.png,.jpg,.jpeg'}
            />,
          )}
          <p style={{ width: '300px' }}>支持扩展名为: .png .jpg .jpeg</p>
        </Form.Item>
      </Modal>
    </div>
  );
};

export default Form.create({ name: 'new_supplier' })(NewPlatformSupplier);
