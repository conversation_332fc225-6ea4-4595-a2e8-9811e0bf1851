import React from 'react';
import { Modal, Button, Input, Icon, Form, DatePicker, Upload, message, Radio, Alert } from 'antd';
import { noop, UploadFile, Const } from 'qmkit';
import PropTypes from 'prop-types';

import Store from '../../store';
const confirm = Modal.confirm;
const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    span: 2,
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    span: 24,
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
export default class BondReject extends React.Component<any, any> {
  _store: Store;
  props: {
    form: any;
    rejectVisable: boolean;
    cancel: Function;
    choseModal: Function;
    voucherData: any;
  };

  //声明上下文依赖
  static contextTypes = {
    _plume$Store: PropTypes.object,
  };

  constructor(props, ctx) {
    super(props);
    this._store = ctx['_plume$Store'];
    this.state = {
      length: 0,
    };
  }
  render() {
    const { rejectVisable, cancel, choseModal } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { length } = this.state;
    const { form } = this.props;
    const { bondLoading } = this._store.state().toJS();
    return (
      <Modal
        title="驳回原因"
        visible={rejectVisable}
        okButtonProps={{ loading: bondLoading }}
        onOk={this._onOK}
        width={700}
        onCancel={() => {
          cancel();
          choseModal();
          this.setState({ length: 0 });
          form.resetFields();
        }}
      >
        <Form {...formItemLayout}>
          <FormItem label="描述" required>
            {getFieldDecorator('rejectReason', {
              rules: [
                {
                  validator: (_rule, value, callback) => {
                    if (!value) {
                      callback('请填写驳回原因');
                      return;
                    }
                    if (value.length > 200) {
                      callback('驳回原因最多200个字符');
                      return;
                    }
                    callback();
                  },
                },
              ],
              initialValue: '',
              onChange: (e) => {
                this.setState({ length: e.target.value.length });
              },
            })(<TextArea placeholder="请输入" style={styles.height} />)}
            <div style={styles.right}>{length} / 200</div>
          </FormItem>
        </Form>
      </Modal>
    );
  }
  _onOK = () => {
    const { form, voucherData, cancel, choseModal } = this.props;
    const { rejectBond } = this._store;
    form.validateFields(null, (errs) => {
      //如果校验通过
      if (!errs) {
        const query = { id: voucherData.id, status: 3, ...form.getFieldsValue() };
        rejectBond(query).then((res) => {
          if (res === 'success') cancel();
        });
      }
    });
  };
}
const styles = {
  right: {
    textAlign: 'right',
  },
  height: {
    height: '120px',
  },
};
