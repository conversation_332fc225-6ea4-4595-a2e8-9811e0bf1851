import React from 'react';
import { Modal, Button, Form, Alert } from 'antd';
import { noop, QMShowUpload, Const, util } from 'qmkit';
import PropTypes from 'prop-types';
import moment from 'moment';

import Store from '../../store';
const confirm = Modal.confirm;
const FormItem = Form.Item;
const formItemLayout = {
  labelCol: {
    span: 2,
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    span: 24,
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
export default class BondPass extends React.Component<any, any> {
  _store: Store;
  props: {
    passVisable: boolean;
    cancel: Function;
    voucherData: any;
    close: Function;
    choseModal: Function;
  };

  //声明上下文依赖
  static contextTypes = {
    _plume$Store: PropTypes.object,
  };

  constructor(props, ctx) {
    super(props);
    this._store = ctx['_plume$Store'];
    this.state = {};
  }
  render() {
    const { passVisable, cancel, voucherData, close } = this.props;
    const voucherImgs = [];
    if (voucherData) {
      voucherData?.picUrl?.split('|').map((img, i) => {
        voucherImgs.push({ status: 'done', uid: i, url: img });
      });
    }

    return (
      <Modal
        title="核验缴款凭证"
        visible={passVisable}
        okText="通过"
        cancelText="驳回"
        // okButtonProps={{loading:loading}}
        width={700}
        footer={
          <div>
            <Button
              type="danger"
              ghost
              onClick={() => {
                cancel();
              }}
            >
              驳回
            </Button>
            <Button type="primary" onClick={this._onOK}>
              通过
            </Button>
          </div>
        }
        onCancel={() => {
          close();
        }}
      >
        <Form {...formItemLayout}>
          <Alert
            message="请注意审核商家上传的缴款凭证，此凭证一旦通过具有法律效应"
            type="warning"
            showIcon
          />
          <FormItem label="商家名称" style={{ marginTop: 30 }}>
            {voucherData?.storeName ?? '-'}
          </FormItem>
          <FormItem label="设置时间">
            {moment(voucherData?.setTime).format('YYYY-MM-DD HH:mm')}
          </FormItem>
          <FormItem label="备注">{voucherData?.remarks ?? '-'}</FormItem>
          <FormItem label="金额（元）">
            {util.numTofixed(voucherData?.securityDeposit) ?? '-'}
          </FormItem>
          <FormItem label="缴款凭证">
            {/* {voucherImgs.map((img)=>{
            return(<img src={img} style={{width:'80px',height:'80px'}}/>)
            })} */}
            <QMShowUpload
              name="uploadFile"
              fileList={voucherImgs}
              listType="picture-card"
              accept={'.jpg,.jpeg,.png,.gif'}
              showUploadList={{
                showRemoveIcon: false,
                showPreviewIcon: true,
              }}
            />
          </FormItem>
        </Form>
      </Modal>
    );
  }
  _onOK = () => {
    const { rejectBond } = this._store;
    const { voucherData, close, choseModal } = this.props;
    close();
    confirm({
      title: '通过保证金核验',
      content: '确定通过该商家的保证金核验吗？',
      onOk: () => {
        rejectBond({ id: voucherData.id, status: 4 }).then((res) => {
          // if (res === 'success') close();
        });
      },
      onCancel: () => {
        choseModal();
      },
    });
  };
}
