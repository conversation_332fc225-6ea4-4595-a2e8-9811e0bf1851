import React from 'react';
import { Modal, Button, Input, Icon, Form, DatePicker, Upload, message, Radio } from 'antd';
import { noop, UploadFile, Const } from 'qmkit';
import PropTypes from 'prop-types';

import Store from '../../store';
const confirm = Modal.confirm;
const FormItem = Form.Item;
const { TextArea } = Input;
const formItemLayout = {
  labelCol: {
    span: 2,
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    span: 24,
    xs: { span: 24 },
    sm: { span: 14 },
  },
};
export default class BondSettingForm extends React.Component<any, any> {
  _store: Store;
  props: {
    form: any;
    setVisable: boolean;
    cancel: Function;
    editBond: {};
  };

  //声明上下文依赖
  static contextTypes = {
    _plume$Store: PropTypes.object,
  };

  constructor(props, ctx) {
    super(props);
    this._store = ctx['_plume$Store'];
    this.state = {};
  }
  render() {
    const { getFieldDecorator, getFieldValue } = this.props.form;
    const { setVisable, cancel, form, editBond } = this.props;
    const { bondLoading } = this._store.state().toJS();

    return (
      <Modal
        title={editBond ? '编辑保证金' : '设置保证金'}
        visible={setVisable}
        okText="确定"
        okButtonProps={{ loading: bondLoading }}
        onOk={this._onOK}
        width={700}
        onCancel={() => {
          form.resetFields();
          cancel();
        }}
      >
        <Form {...formItemLayout}>
          <FormItem label="保证金金额" required={true}>
            {getFieldDecorator('securityDeposit', {
              rules: [
                {
                  validator: (_rule, value, callback) => {
                    const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
                    if (!value) {
                      callback('请填写保证金金额');
                      return;
                    }
                    if (value * 1 > 99999999.99) {
                      callback('金额最大为99,999,999.99');
                      return;
                    }
                    if (!reg.test(value)) {
                      callback('最多两位数小数');
                      return;
                    }
                    callback();
                  },
                },
              ],
              initialValue: editBond?.securityDeposit ?? '',
            })(
              <Input
                addonBefore="￥"
                // prefix="￥"
                //  suffix="RMB"
                placeholder="请输入"
              />,
            )}
          </FormItem>
          <FormItem label="备注">
            {getFieldDecorator('remarks', {
              initialValue: editBond?.remarks ?? '',
              rules: [
                {
                  validator: (_rule, value, callback) => {
                    if (value.length > 200) {
                      callback('最多输入不超过200字符');
                      return;
                    }
                    callback();
                  },
                },
              ],
            })(<TextArea placeholder="请输入" style={styles.height} />)}
            <div style={{ textAlign: 'right' }}>{getFieldValue('remarks')?.length} / 200</div>
          </FormItem>
        </Form>
      </Modal>
    );
  }
  _onOK = () => {
    const { form, cancel, editBond } = this.props;
    const { addBond } = this._store;
    form.validateFields(null, (errs) => {
      //如果校验通过
      if (!errs) {
        const query = { ...form.getFieldsValue() };
        if (editBond) {
          query.id = editBond.id;
          query.status = editBond.status;
        }
        addBond(query).then((res) => {
          if (res === 'success') {
            form.resetFields();
            cancel();
          }
        });
      }
    });
  };
}
const styles = {
  height: {
    height: '120px',
  },
};
