import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import styles from './index.module.less';
import { SearchForm, AddEditModal, AuthorizeModal } from './components';
import { useSearch } from './hooks';
import { Button, Popconfirm, Table, message } from 'antd';
import { columns } from './dataSource';
import {
  liveRoomInfoChangeExport,
  LiveRoomListRequest,
  LiveRoomListResult,
  liveRoomInfoExport,
} from './Services/yml';
import { useList, APIKEY } from '../live-room-manage/hooks';
import style from '../gmvCommissionManage/components/gmvCommissionManage/view/index.module.less';
import PaginationProxy from '@/common/constants/Pagination';
import { useTableHeight } from '@/common/constants/hooks/index';

import { liveRoomDisable, liveRoomEnable } from './Services/yml';
import { useRequest } from 'ahooks';
import { AuthWrapper, checkAuth } from 'qmkit';
import moment, { Moment } from 'moment';
import ExportModal from '@/components/ExportModal';
import { ButtonProxy } from 'web-common-modules/componentsV2';
import { history } from 'qmkit';

const OpenOffLive = () => {
  const {
    dataSource,
    pagination,
    onPageChange,
    onSearch: handleSearch,
    onRefresh,
    loading,
    condition,
  } = useList<LiveRoomListRequest, LiveRoomListResult['records']>(APIKEY.OPEN_OFF_LIVE);
  const { options } = useSearch(true);
  const [authVisible, setAuthVisible] = useState<boolean>(false);
  const [preCreateId, setPreCreateId] = useState<string>('');
  const [talentId, setTalentId] = useState<string | undefined>('');

  const handleOpen = (id: string, tid?: string) => {
    setPreCreateId(id);
    setTalentId(tid);
    setAuthVisible(true);
  };
  const handleClose = () => {
    setAuthVisible(false);
  };
  const { run: liveRoomDisableRun, loading: liveRoomDisableLoading } = useRequest(liveRoomDisable, {
    manual: true,
    onSuccess({ res }) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      onRefresh();
    },
  });
  const { run: liveRoomEnableRun, loading: liveRoomEnableLoading } = useRequest(liveRoomEnable, {
    manual: true,
    onSuccess({ res }) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      onRefresh();
    },
  });

  const onSearch = (values: LiveRoomListRequest & { lateUpdateDate?: Moment[] }) => {
    // console.log(values, '------>');
    const lateUpdateStartTime = values?.lateUpdateDate?.[0]
      ? moment(values?.lateUpdateDate?.[0]).format('YYYY-MM-DD')
      : undefined;
    const lateUpdateEndTime = values?.lateUpdateDate?.[1]
      ? moment(values?.lateUpdateDate?.[1]).format('YYYY-MM-DD')
      : undefined;
    delete values.lateUpdateDate;
    handleSearch({
      ...values,
      lateUpdateEndTime,
      lateUpdateStartTime,
    });
  };
  useEffect(() => {
    onSearch({});
  }, []);

  const { tableHeight } = useTableHeight(85);

  return (
    <PageLayout className={styles['open-off-page']}>
      <div className={`${style['publishFeeContainer']} ${style['page-style']}`}>
        <div className="formHeight">
          <SearchForm onSearch={onSearch} options={options}></SearchForm>
          <div style={{ marginTop: '-4px', marginBottom: '8px' }}>
            <AuthWrapper functionName="f_open_off_live_add">
              <AddEditModal onRefresh={onRefresh}>
                <Button type="primary" icon="plus">
                  新建
                </Button>
              </AddEditModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_open_close_live_room_export">
              <ExportModal
                condition={condition}
                requestFunc={liveRoomInfoChangeExport}
                content={
                  <div>
                    <p>
                      <span>导出完成后可在</span>
                      <a
                        href="javascript:;"
                        onClick={() => {
                          //
                        }}
                      >
                        导出记录
                      </a>
                      <span>中查看并下载。</span>
                    </p>
                  </div>
                }
                onOk={() => null}
              >
                <ButtonProxy className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  <span>导出更名记录</span>
                </ButtonProxy>
              </ExportModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_export_liveroom_close_open_live">
              <ExportModal
                condition={condition}
                requestFunc={liveRoomInfoExport}
                content={
                  <div>
                    <p>
                      <span>导出完成后可在</span>
                      <a
                        href="javascript:;"
                        onClick={() => {
                          //
                        }}
                      >
                        导出记录
                      </a>
                      <span>中查看并下载。</span>
                    </p>
                  </div>
                }
                onOk={() => null}
              >
                <ButtonProxy className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  <span>导出直播间</span>
                </ButtonProxy>
              </ExportModal>
            </AuthWrapper>

            {(checkAuth('f_export_liveroom_close_open_live') ||
              checkAuth('f_open_close_live_room_export')) && (
              <ButtonProxy
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={() => {
                  history.push(
                    `/export-list-open-off-live?configCode=LIVE_ROOM_INFO_CHANGE_EXPORT`,
                  );
                }}
              >
                <span>导出记录</span>
              </ButtonProxy>
            )}
          </div>
        </div>
        <div style={{ flex: 1 }}>
          <AuthWrapper functionName="f_open_close_live_room_list">
            <Table
              rowKey={'id'}
              columns={columns(onRefresh, pagination, handleOpen, {
                liveRoomDisableRun: liveRoomDisableRun,
                liveRoomEnableRun: liveRoomEnableRun,
              })}
              // scroll={{ x: true }}
              dataSource={dataSource as any}
              loading={loading || liveRoomDisableLoading || liveRoomEnableLoading}
              pagination={false}
              rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
              scroll={{ y: tableHeight, x: '100%' }}
            ></Table>
          </AuthWrapper>
        </div>
        <div className={`${style['pagination-box']} pageHeight`}>
          <AuthWrapper functionName="f_open_close_live_room_list">
            <PaginationProxy
              current={pagination?.current}
              pageSize={pagination?.size}
              total={pagination?.total}
              valueType="flatten"
              // @ts-ignore
              onChange={onPageChange}
            />
          </AuthWrapper>
        </div>
        <AuthorizeModal
          visible={authVisible}
          close={handleClose}
          preCreateId={preCreateId}
          talentId={talentId}
        />
      </div>
    </PageLayout>
  );
};

export default OpenOffLive;
