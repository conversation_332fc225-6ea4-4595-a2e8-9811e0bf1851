import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type LiveRoomListRequest = {
  authorizeStatus?: 'DONE_AUTH' | 'WAIT_AUTH' | 'EXPIRE_AUTH' /*抖音授权状态[AuthorizeStatusEnum]*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部id*/;
  enableStatus?: 'DISABLE' | 'ENABLE' /*启用状态[EnableStatusEnum]*/;
  id?: string /*直播间id*/;
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
  lateUpdateEndTime?: string /*最近更名结束时间*/;
  lateUpdateStartTime?: string /*最近更名开始时间*/;
  liveRoomName?: string /*直播间名称*/;
  platformEnum?:
    | 'DY'
    | 'TB'
    | 'KS'
    | 'JD'
    | 'PDD'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[SourceTypeEnum]*/;
  size?: number /*分页大小*/;
  talentNo?: string /*达人编号*/;
};

export type LiveRoomListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    account?: string /*抖币账号*/;
    accountOperator?: string /*运营人*/;
    accountOperatorName?: string /*运营人姓名*/;
    authorId?: string /*直播间authorId*/;
    authorizeStatus?: number /*抖音授权状态*/;
    avatar?: string /*头像*/;
    brandFeeTechRate?: string /*基础服务费分佣比例*/;
    buId?: string /*事业部id*/;
    buName?: string /*事业部名称*/;
    businessLine?: string /*业务线*/;
    businessLineId?: string /*业务线id（系统代码code）*/;
    buyinId?: string /*百应ID*/;
    certExpireDate?: string /*认证期限*/;
    commissionTechRate?: string /*佣金率*/;
    cooperationMode?:
      | 'COLONEL'
      | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
    createTime?: string /*创建时间*/;
    enableStatus?: number /*启用状态*/;
    enterpriseCertSubject?: string /*企业认证主体*/;
    enterpriseCertVersion?: string /*企业号认证版本号*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*id*/;
    institutionId?: string /*机构id*/;
    institutionName?: string /*机构名称*/;
    institutionNo?: string /*机构编号*/;
    mobileOwner?: string /*手机号归属人*/;
    mobileOwnerName?: string /*手机号归属人姓名*/;
    name?: string /*直播间名称*/;
    openId?: string /*直播间openId*/;
    operator?: string /*经营人*/;
    operatorName?: string /*经营人姓名*/;
    platform?: string /*平台*/;
    platformId?: string /*平台达人id*/;
    platformMobile?: string /*平台注册手机号*/;
    preCreateId?: string /*预创建记录主键id*/;
    projectManagerIdList?: Array<string> /*项目经理*/;
    projectManagerIds?: string /*项目经理*/;
    projectManagers?: Array<{
      accountDisableReason?: string /*账号禁用原因*/;
      accountName?: string /*账户名*/;
      accountState?: number /*账户状态  0：启用   1：禁用  2：离职*/;
      accountType?: number /*账号类型 0 b2b账号 1 s2b平台端账号 2 商家端账号 3供应商端账号*/;
      birthday?: string /*生日*/;
      bizRoleType?:
        | 'BUSINESS'
        | 'SELECTION'
        | 'OPERATE'
        | 'MIDDLE_GROUND'
        | 'LEGAL'
        | 'ANCHOR' /*业务角色[RoleTypeEnum]*/;
      companyInfoId?: string /*公司信息ID*/;
      createTime?: string /*创建时间*/;
      email?: string /*邮箱*/;
      employeeId?: string /*员工信息ID*/;
      employeeMobile?: string /*员工手机号*/;
      employeeName?: string /*员工姓名*/;
      feishuOpenId?: string /*飞书员工 open id*/;
      feishuUserId?: string /*飞书员工 userId*/;
      isMasterAccount?: number /*是否主账号 0、否 1、是*/;
      jobNo?: string /*工号*/;
      loginAccount?: string /*登录账号*/;
      position?: string /*职位*/;
      sex?: boolean /*性别，0：保密，1：男，2：女*/;
      tenantId?: string /*租户id,平台端是0*/;
      unionId?: string /*微信unionId,这个字段实际上要从user中获取*/;
    }> /*项目经理*/;
    realNamePerson?: string /*平台实名人*/;
    realNamePersonName?: string /*平台实名人姓名*/;
    sortingNum?: number /*排序权重*/;
    talentCompany?: string /*达人公司*/;
    talentId?: string /*达人id*/;
    talentNo?: string /*达人编号*/;
    updateTime?: string /*更新时间*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *直播间列表
 */
export const liveRoomList = (params: LiveRoomListRequest) => {
  return Fetch<ResponseWithResult<LiveRoomListResult>>('/iasm/public/liveRoom/list', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/liveRoom/list') },
  });
};

export type LiveRoomInstitutionListRequest = {
  institutionName?: string /*机构名称*/;
};

export type LiveRoomInstitutionListResult = Array<{
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
}>;

/**
 *机构名称列表
 */
export const liveRoomInstitutionList = (params: LiveRoomInstitutionListRequest) => {
  return Fetch<ResponseWithResult<LiveRoomInstitutionListResult>>(
    '/iasm/public/liveRoom/InstitutionList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/liveRoom/InstitutionList') },
    },
  );
};

export type CreateStandardRequest = {
  broadcastingRobotLink?: string /*播报机器人链接*/;
  commodityExperienceStandard?: string /*商品体验标准*/;
  favorableRate?: string /*好评率*/;
  liveRoomId?: string /*直播间主键id 所属直播间*/;
  logisticsExperienceStandard?: string /*物流体验标准*/;
  merchantServiceStandard?: string /*商家服务标准*/;
  shopPoints?: string /*店铺分*/;
  talentNo?: string /*达人编号*/;
};

export type CreateStandardResult = boolean;

/**
 *新增直播间评分标准
 */
export const createStandard = (params: CreateStandardRequest) => {
  return Fetch<ResponseWithResult<CreateStandardResult>>(
    '/iasm/public/web/scoringStandard/createStandard',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/web/scoringStandard/createStandard') },
    },
  );
};

export type GetStandardRequest = {
  liveRoomId?: string /*直播间主键id 所属直播间*/;
  talentNo?: string /*达人编号*/;
};

export type GetStandardResult = {
  broadcastingRobotLink?: string /*播报机器人链接*/;
  commodityExperienceStandard?: string /*商品体验标准*/;
  favorableRate?: string /*好评率*/;
  id?: string /*主键*/;
  liveRoomId?: string /*达人账号 直播间ID*/;
  logisticsExperienceStandard?: string /*物流体验标准*/;
  merchantServiceStandard?: string /*商家服务标准*/;
  shopPoints?: string /*店铺分*/;
  talentNo?: string /*达人编号*/;
};

/**
 *查询直播间评分标准
 */
export const getStandard = (params: GetStandardRequest) => {
  return Fetch<ResponseWithResult<GetStandardResult>>(
    '/iasm/public/web/scoringStandard/getStandard',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/web/scoringStandard/getStandard') },
    },
  );
};

export type AddLiveRoomRequest = {
  account?: string /*抖币账号*/;
  authorId?: string /*authorId*/;
  buId?: string /*事业部id*/;
  buName?: string /*事业部名称*/;
  businessLine?: string /*业务线*/;
  businessLineId?: string /*业务线id*/;
  buyinId?: string /*百应ID*/;
  commissionTechRate?: string /*佣金率*/;
  cooperationMode?: 'COLONEL' | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
  id?: string /*id*/;
  img?: string /*头像*/;
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
  liveRoomIds?: Array<string> /*直播间id(与达人id互斥)*/;
  name?: string /*直播间名称*/;
  openId?: string /*直播间openId*/;
  platform?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[PlatformSourceEnum]*/;
  platformId?: string /*平台id*/;
  projectManagerIdList?: Array<string> /*项目经理*/;
  sortingNum?: number /*排序权重*/;
  talentIds?: Array<string> /*达人id(与直播间id互斥)*/;
};

export type AddLiveRoomResult = {
  preCreateId?: string /*预创建记录主键id*/;
};

/**
 *新增直播间接口
 */
export const addLiveRoom = (params: AddLiveRoomRequest) => {
  return Fetch<ResponseWithResult<AddLiveRoomResult>>('/talent/public/liveRoom/addLiveRoom', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/talent/public/liveRoom/addLiveRoom') },
  });
};

export type GetInstitutionPageRequest = {
  current?: number /*当前页码,从1开始*/;
  size?: number /*分页大小*/;
  storeName?: string /*名称*/;
  storeType?: 'SUPPLIER' | 'TALENT' | 'BOSS' | 'INSTITUTION' | 'FINANCE' /*类型[StoreTypeEnum]*/;
};

export type GetInstitutionPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    storeId?: string /*主键*/;
    storeName?: string /*公司名称*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *查询机构信息分页列表
 */
export const getInstitutionPage = (params: GetInstitutionPageRequest) => {
  return Fetch<ResponseWithResult<GetInstitutionPageResult>>(
    '/user/public/store/getInstitutionPage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/store/getInstitutionPage') },
    },
  );
};

export type GetJgpyDouYinAuthRequest = {
  preCreateId?: string /*预创建记录主键id*/;
  scopeBizType?: 'ALL' | 'EXCLUDE_BUSINESS' | 'EXCLUDE_SPECIFIC' /*授权业务类型[ScopeBizTypeEnum]*/;
  scopes?: Array<string> /*授权Scope*/;
};

export type GetJgpyDouYinAuthResult = string;

/**
 *交个朋友端-获取抖音授权页
 */
export const getJgpyDouYinAuth = (params: GetJgpyDouYinAuthRequest) => {
  return Fetch<ResponseWithResult<GetJgpyDouYinAuthResult>>(
    '/talent/public/grant/getJgpyDouYinAuth',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/talent/public/grant/getJgpyDouYinAuth') },
    },
  );
};

export type LiveRoomDetailRequest = {
  id?: string /*业务ID*/;
};

export type LiveRoomDetailResult = {
  account?: string /*抖币账号*/;
  accountOperator?: string /*运营人*/;
  accountOperatorName?: string /*运营人姓名*/;
  authorId?: string /*直播间authorId*/;
  authorizeStatus?: number /*抖音授权状态*/;
  avatar?: string /*头像*/;
  brandFeeTechRate?: string /*品牌费分佣比例*/;
  buId?: string /*事业部id*/;
  buName?: string /*事业部名称*/;
  businessLine?: string /*业务线*/;
  businessLineId?: string /*业务线id（系统代码code）*/;
  buyinId?: string /*百应ID*/;
  certExpireDate?: string /*认证期限*/;
  commissionTechRate?: string /*佣金率*/;
  cooperationMode?: 'COLONEL' | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
  createTime?: string /*创建时间*/;
  enableStatus?: number /*启用状态*/;
  enterpriseCertSubject?: string /*企业认证主体*/;
  enterpriseCertVersion?: string /*企业号认证版本号*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*更新时间*/;
  id?: string /*id*/;
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
  institutionNo?: string /*机构编号*/;
  liveRoomInfoChangeRecordVOList?: Array<{
    businessLine?: string /*业务线*/;
    endTime?: string /*结束时间*/;
    id?: string /*主键*/;
    institutionName?: string /*机构名称*/;
    liveRoomAssociationId?: string /*关联直播间id*/;
    liveRoomName?: string /*直播间名称*/;
    openId?: string /*直播间openId*/;
    startTime?: string /*开始时间*/;
    yongyouRenameStatus?:
      | 'SYNC_SUCCESS'
      | 'SYNC_FAIL'
      | 'NO_SYNC' /*用友更名状态[YougYouRenameStatusEnum]*/;
    yongyouReturnResult?: string /*用友接口返回结果*/;
  }> /*直播间信息变更记录*/;
  mobileOwner?: string /*手机号归属人*/;
  mobileOwnerName?: string /*手机号归属人姓名*/;
  name?: string /*直播间名称*/;
  openId?: string /*直播间openId*/;
  operator?: string /*经营人*/;
  operatorName?: string /*经营人姓名*/;
  platform?: string /*平台*/;
  platformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台枚举[PlatformEnum]*/;
  platformId?: string /*平台id*/;
  platformMobile?: string /*平台注册手机号*/;
  projectManagerIdList?: Array<string> /*项目经理*/;
  projectManagerIds?: string /*项目经理*/;
  projectManagers?: Array<{
    accountDisableReason?: string /*账号禁用原因*/;
    accountName?: string /*账户名*/;
    accountState?: number /*账户状态  0：启用   1：禁用  2：离职*/;
    accountType?: number /*账号类型 0 b2b账号 1 s2b平台端账号 2 商家端账号 3供应商端账号*/;
    birthday?: string /*生日*/;
    bizRoleType?:
      | 'BUSINESS'
      | 'SELECTION'
      | 'OPERATE'
      | 'MIDDLE_GROUND'
      | 'LEGAL'
      | 'ANCHOR' /*业务角色[RoleTypeEnum]*/;
    companyInfoId?: string /*公司信息ID*/;
    createTime?: string /*创建时间*/;
    email?: string /*邮箱*/;
    employeeId?: string /*员工信息ID*/;
    employeeMobile?: string /*员工手机号*/;
    employeeName?: string /*员工姓名*/;
    feishuOpenId?: string /*飞书员工 open id*/;
    feishuUserId?: string /*飞书员工 userId*/;
    isMasterAccount?: number /*是否主账号 0、否 1、是*/;
    jobNo?: string /*工号*/;
    loginAccount?: string /*登录账号*/;
    position?: string /*职位*/;
    sex?: boolean /*性别，0：保密，1：男，2：女*/;
    tenantId?: string /*租户id,平台端是0*/;
    unionId?: string /*微信unionId,这个字段实际上要从user中获取*/;
  }> /*项目经理*/;
  realNamePerson?: string /*平台实名人*/;
  realNamePersonName?: string /*平台实名人姓名*/;
  sortingNum?: number /*排序权重*/;
  talentCompany?: string /*达人公司*/;
  talentId?: string /*达人id*/;
  talentNo?: string /*达人编号*/;
  updateTime?: string /*更新时间*/;
};

/**
 *直播间详情
 */
export const liveRoomDetail = (params: LiveRoomDetailRequest) => {
  return Fetch<ResponseWithResult<LiveRoomDetailResult>>('/iasm/public/liveRoom/detail', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/liveRoom/detail') },
  });
};

export type LiveRoomDisableRequest = {
  id?: string /*业务ID*/;
};

export type LiveRoomDisableResult = boolean;

/**
 *直播间禁用接口
 */
export const liveRoomDisable = (params: LiveRoomDisableRequest) => {
  return Fetch<ResponseWithResult<LiveRoomDisableResult>>('/talent/public/liveRoom/disable', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/talent/public/liveRoom/disable') },
  });
};

export type LiveRoomEnableRequest = {
  id?: string /*业务ID*/;
};

export type LiveRoomEnableResult = boolean;

/**
 *直播间启用接口
 */
export const liveRoomEnable = (params: LiveRoomEnableRequest) => {
  return Fetch<ResponseWithResult<LiveRoomEnableResult>>('/talent/public/liveRoom/enable', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/talent/public/liveRoom/enable') },
  });
};

export type GetDouYinAuthByTalentIdRequest = {
  scopeBizType?: 'ALL' | 'EXCLUDE_BUSINESS' | 'EXCLUDE_SPECIFIC' /*授权业务类型[ScopeBizTypeEnum]*/;
  scopes?: Array<string> /*授权Scope*/;
  talentId?: string /*达人ID*/;
};

export type GetDouYinAuthByTalentIdResult = string;

/**
 *获取抖音授权页
 */
export const getDouYinAuthByTalentId = (params: GetDouYinAuthByTalentIdRequest) => {
  return Fetch<ResponseWithResult<GetDouYinAuthByTalentIdResult>>(
    '/talent/public/grant/getDouYinAuthByTalentId',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/talent/public/grant/getDouYinAuthByTalentId') },
    },
  );
};

export type EditLiveRoomRequest = {
  account?: string /*抖币账号*/;
  authorId?: string /*authorId*/;
  buId?: string /*事业部id*/;
  buName?: string /*事业部名称*/;
  businessLine?: string /*业务线*/;
  businessLineId?: string /*业务线id*/;
  buyinId?: string /*百应ID*/;
  commissionTechRate?: string /*佣金率*/;
  cooperationMode?: 'COLONEL' | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
  id?: string /*id*/;
  img?: string /*头像*/;
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
  liveRoomIds?: Array<string> /*直播间id(与达人id互斥)*/;
  name?: string /*直播间名称*/;
  openId?: string /*直播间openId*/;
  platform?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[PlatformSourceEnum]*/;
  platformId?: string /*平台id*/;
  projectManagerIdList?: Array<string> /*项目经理*/;
  sortingNum?: number /*排序权重*/;
  talentIds?: Array<string> /*达人id(与直播间id互斥)*/;
};

export type EditLiveRoomResult = boolean;

/**
 *编辑直播间接口
 */
export const editLiveRoom = (params: EditLiveRoomRequest) => {
  return Fetch<ResponseWithResult<EditLiveRoomResult>>('/talent/public/liveRoom/editLiveRoom', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/talent/public/liveRoom/editLiveRoom') },
  });
};

export type LiveRoomInfoChangeExportRequest = {
  authorizeStatus?: 'DONE_AUTH' | 'WAIT_AUTH' | 'EXPIRE_AUTH' /*抖音授权状态[AuthorizeStatusEnum]*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部id*/;
  enableStatus?: 'DISABLE' | 'ENABLE' /*启用状态[EnableStatusEnum]*/;
  id?: string /*直播间id*/;
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
  lateUpdateEndTime?: string /*最近更名结束时间*/;
  lateUpdateStartTime?: string /*最近更名开始时间*/;
  liveRoomName?: string /*直播间名称*/;
  platformEnum?:
    | 'DY'
    | 'TB'
    | 'KS'
    | 'JD'
    | 'PDD'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[SourceTypeEnum]*/;
  size?: number /*分页大小*/;
  talentNo?: string /*达人编号*/;
};

export type LiveRoomInfoChangeExportResult = string;

/**
 *直播间更名记录信息导出
 */
export const liveRoomInfoChangeExport = (params: LiveRoomInfoChangeExportRequest) => {
  return Fetch<ResponseWithResult<LiveRoomInfoChangeExportResult>>(
    '/iasm/public/liveRoom/liveRoomInfoChangeExport',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/liveRoom/liveRoomInfoChangeExport') },
    },
  );
};

export type PlatformAccountInfoEditRequest = {
  accountOperator?: string /*运营人*/;
  certExpireDate?: string /*认证期限 格式 yyyy-MM-dd*/;
  enterpriseCertSubject?: string /*企业认证主体*/;
  enterpriseCertVersion?: string /*企业号认证版本号*/;
  liveRoomId?: string /*直播间ID*/;
  mobileOwner?: string /*手机号归属人*/;
  operator?: string /*经营人*/;
  platformMobile?: string /*平台注册手机号*/;
  realNamePerson?: string /*平台实名人*/;
};

export type PlatformAccountInfoEditResult = boolean;

/**
 *编辑平台账号信息
 */
export const platformAccountInfoEdit = (params: PlatformAccountInfoEditRequest) => {
  return Fetch<ResponseWithResult<PlatformAccountInfoEditResult>>(
    '/iasm/public/platformAccountInfo/edit',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/platformAccountInfo/edit') },
    },
  );
};

export type LiveRoomInfoExportRequest = {
  authorizeStatus?: 'DONE_AUTH' | 'WAIT_AUTH' | 'EXPIRE_AUTH' /*抖音授权状态[AuthorizeStatusEnum]*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部id*/;
  enableStatus?: 'DISABLE' | 'ENABLE' /*启用状态[EnableStatusEnum]*/;
  id?: string /*直播间id*/;
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
  lateUpdateEndTime?: string /*最近更名结束时间*/;
  lateUpdateStartTime?: string /*最近更名开始时间*/;
  liveRoomName?: string /*直播间名称*/;
  platformEnum?:
    | 'DY'
    | 'TB'
    | 'KS'
    | 'JD'
    | 'PDD'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[SourceTypeEnum]*/;
  size?: number /*分页大小*/;
  talentNo?: string /*达人编号*/;
};

export type LiveRoomInfoExportResult = string;

/**
 *直播间信息导出
 */
export const liveRoomInfoExport = (params: LiveRoomInfoExportRequest) => {
  return Fetch<ResponseWithResult<LiveRoomInfoExportResult>>(
    '/iasm/public/liveRoom/liveRoomInfoExport',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/liveRoom/liveRoomInfoExport') },
    },
  );
};

export type LiveRoomEditRequest = {
  account?: string /*抖币账号*/;
  authorId?: string /*直播间authorId*/;
  brandFeeTechRate?: string /*基础服务费分佣比例*/;
  businessLine?: string /*业务线*/;
  businessLineId?: string /*业务线id（系统代码code）*/;
  buyinId?: string /*百应ID*/;
  commissionTechRate?: string /*达人分佣比例*/;
  cooperationMode?: 'COLONEL' | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
  deptId?: string /*事业部id*/;
  deptName?: string /*事业部名称*/;
  id?: string /*id*/;
  platformId?: string /*平台达人id*/;
  sortingNum?: number /*排序权重*/;
};

export type LiveRoomEditResult = boolean;

/**
 *直播间编辑
 */
export const liveRoomEdit = (params: LiveRoomEditRequest) => {
  return Fetch<ResponseWithResult<LiveRoomEditResult>>('/iasm/public/liveRoom/liveRoomEdit', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/liveRoom/liveRoomEdit') },
  });
};
