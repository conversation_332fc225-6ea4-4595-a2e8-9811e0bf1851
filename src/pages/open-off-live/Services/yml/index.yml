basic:
  liveRoomList: /iasm/public/liveRoom/list
  liveRoomInstitutionList: /iasm/public/liveRoom/InstitutionList
  createStandard: /iasm/public/web/scoringStandard/createStandard
  getStandard: /iasm/public/web/scoringStandard/getStandard
  addLiveRoom: /talent/public/liveRoom/addLiveRoom
  getInstitutionPage: /user/public/store/getInstitutionPage
  getJgpyDouYinAuth: /talent/public/grant/getJgpyDouYinAuth
  liveRoomDetail: /iasm/public/liveRoom/detail
  liveRoomDisable: /talent/public/liveRoom/disable
  liveRoomEnable: /talent/public/liveRoom/enable
  getDouYinAuthByTalentId: /talent/public/grant/getDouYinAuthByTalentId
  editLiveRoom: /talent/public/liveRoom/editLiveRoom
  liveRoomInfoChangeExport: /iasm/public/liveRoom/liveRoomInfoChangeExport
  platformAccountInfoEdit: /iasm/public/platformAccountInfo/edit
  liveRoomInfoExport: /iasm/public/liveRoom/liveRoomInfoExport

f_open_off_live_edit:
  liveRoomEdit: /iasm/public/liveRoom/liveRoomEdit
