const signMap = {
  '/iasm/public/liveRoom/list': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/InstitutionList': 'basic.x1.xxxx1',
  '/iasm/public/web/scoringStandard/createStandard': 'basic.x1.xxxx1',
  '/iasm/public/web/scoringStandard/getStandard': 'basic.x1.xxxx1',
  '/talent/public/liveRoom/addLiveRoom': 'basic.x1.xxxx1',
  '/user/public/store/getInstitutionPage': 'basic.x1.xxxx1',
  '/talent/public/grant/getJgpyDouYinAuth': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/detail': 'basic.x1.xxxx1',
  '/talent/public/liveRoom/disable': 'basic.x1.xxxx1',
  '/talent/public/liveRoom/enable': 'basic.x1.xxxx1',
  '/talent/public/grant/getDouYinAuthByTalentId': 'basic.x1.xxxx1',
  '/talent/public/liveRoom/editLiveRoom': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/liveRoomInfoChangeExport': 'basic.x1.xxxx1',
  '/iasm/public/platformAccountInfo/edit': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/liveRoomInfoExport': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/liveRoomEdit': 'f_open_off_live_edit.x1.xxxx1',
};

export function getSign(path: string): string {
  return signMap[path];
}
