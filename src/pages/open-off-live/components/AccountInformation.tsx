import { Form, Input, Modal, Select, DatePicker, message } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import React, { useState, useEffect } from 'react';
import { LiveRoomListResult, platformAccountInfoEdit } from '../Services/yml';
import {
  getCompanyPageList,
  GetCompanyPageListResult,
} from '@/pages/company-bank-account/services/index';
import useEmployee from '@/pages/hot-goods/hooks/useEmployee';
import moment from 'moment';
import { isValidChinesePhone } from '@/aiUtils/utils';
import { useDebounceFn } from 'ahooks';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

const { Option } = Select;
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 14,
  },
};
interface PropsType extends FormComponentProps {
  record?: Required<LiveRoomListResult>['records'][number];
  onRefresh: () => void;
}
const AccountInformation: React.FC<PropsType> = ({ form, record, onRefresh }) => {
  const { getFieldDecorator, validateFields } = form;

  // ai生成
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  // 使用state管理企业认证版本号，避免getFieldsValue在初始化时获取不到值的问题
  const [enterpriseCertVersion, setEnterpriseCertVersion] = useState<string | undefined>();

  // 公司数据相关状态
  const [companyList, setCompanyList] = useState<GetCompanyPageListResult['records']>([]);
  const [companyLoading, setCompanyLoading] = useState(false);

  // 获取公司列表
  const getCompanyList = async (name?: string) => {
    setCompanyLoading(true);
    const result = await responseWithResultAsync({
      request: getCompanyPageList,
      params: { name, size: 100 },
    });
    setCompanyList(result?.records || []);
    setCompanyLoading(false);
  };

  // 防抖搜索
  const { run: debouncedSearchCompany } = useDebounceFn(
    (value: string) => {
      getCompanyList(value);
    },
    { wait: 500 },
  );

  const handleCompanySearch = (value: string) => {
    debouncedSearchCompany(value);
  };

  const handleCompanyChange = (value: string) => {
    if (!value) {
      getCompanyList();
    }
  };

  // 员工相关hooks - 手机号归属人
  const {
    loading: phoneOwnerLoading,
    list: phoneOwnerList,
    onSearch: onPhoneOwnerSearch,
  } = useEmployee();

  // 员工相关hooks - 经营人
  const {
    loading: operatorLoading,
    list: operatorList,
    onSearch: onOperatorSearch,
  } = useEmployee();

  // 员工相关hooks - 运营人
  const {
    loading: operationStaffLoading,
    list: operationStaffList,
    onSearch: onOperationStaffSearch,
  } = useEmployee();

  // 员工相关hooks - 平台实名人
  const {
    loading: platformRealNameLoading,
    list: platformRealNameList,
    onSearch: onPlatformRealNameSearch,
  } = useEmployee();

  useEffect(() => {
    if (visible) {
      // 初始化数据
      getCompanyList();
      // 获取员工列表（过滤状态为可用的员工）
      onPhoneOwnerSearch({ accountState: 0 }); // 0为禁用状态
      onOperatorSearch({ accountState: 0 });
      onOperationStaffSearch({ accountState: 0 });
      onPlatformRealNameSearch({ accountState: 0 });

      // 如果有record数据，首先设置enterpriseCertVersion状态
      if (record) {
        setEnterpriseCertVersion(record.enterpriseCertVersion);

        // 使用setTimeout确保表单字段已经渲染完成后再设置值
        setTimeout(() => {
          form.setFieldsValue({
            platformMobile: record.platformMobile,
            // 使用labelInValue模式的员工字段，需要构造 {key, label} 格式
            mobileOwner:
              record.mobileOwner && record.mobileOwnerName
                ? { key: record.mobileOwner, label: record.mobileOwnerName }
                : undefined,
            operator:
              record.operator && record.operatorName
                ? { key: record.operator, label: record.operatorName }
                : undefined,
            enterpriseCertSubject: record.enterpriseCertSubject,
            enterpriseCertVersion: record.enterpriseCertVersion,
            accountOperator:
              record.accountOperator && record.accountOperatorName
                ? { key: record.accountOperator, label: record.accountOperatorName }
                : undefined,
            realNamePerson:
              record.realNamePerson && record.realNamePersonName
                ? { key: record.realNamePerson, label: record.realNamePersonName }
                : undefined,
            certExpireDate: record.certExpireDate ? moment(record.certExpireDate) : undefined,
          });
        }, 0);
      }
    } else {
      // 弹框关闭时重置状态
      setEnterpriseCertVersion(undefined);
      form.resetFields();
    }
  }, [visible, record]);

  // 手机号格式校验
  const validatePhone = (_rule: unknown, value: string, callback: (error?: string) => void) => {
    if (!value) {
      return callback();
    }
    if (!isValidChinesePhone(value)) {
      return callback('请输入正确的手机号格式');
    }
    return callback();
  };

  // 表单提交
  const handleSubmit = async () => {
    validateFields(async (err, values) => {
      if (err) {
        return;
      }

      setLoading(true);

      try {
        // 处理提交数据，将labelInValue格式转换为后端需要的格式
        const submitData = {
          ...values,
          liveRoomId: record?.id, // 直播间ID
          // 处理labelInValue格式的员工字段
          mobileOwner: values.mobileOwner?.key || values.mobileOwner,
          operator: values.operator?.key || values.operator,
          accountOperator: values.accountOperator?.key || values.accountOperator,
          realNamePerson: values.realNamePerson?.key || values.realNamePerson,
          certExpireDate: values.certExpireDate
            ? moment(values.certExpireDate).format('YYYY-MM-DD')
            : undefined,
        };

        // 调用实际的API接口
        const res = await platformAccountInfoEdit(submitData);
        console.log(res);
        if (res.res?.code === '200') {
          message.success('保存成功');
          setVisible(false);
          onRefresh(); // 刷新列表
        } else {
          message.error(res.res?.message);
        }
      } catch (error) {
        console.error('保存失败:', error);
        message.error('保存失败:' + error);
      } finally {
        setLoading(false);
      }
    });
  };
  // 2024年12月17日 开山ai结尾共生成69行代码
  return (
    <>
      <a onClick={() => setVisible(true)}>账号信息</a>
      <Modal
        visible={visible}
        onCancel={() => setVisible(false)}
        title="编辑平台账号信息"
        onOk={handleSubmit}
        confirmLoading={loading}
        width={600}
      >
        <Form labelAlign="right" {...formItemLayout}>
          {/* ai生成 */}
          {/* 平台注册手机号 */}
          <Form.Item label="平台登录手机号">
            {getFieldDecorator('platformMobile', {
              rules: [
                {
                  validator: validatePhone,
                },
              ],
            })(<Input placeholder="请输入手机号" maxLength={11} />)}
          </Form.Item>

          {/* 手机号归属人 */}
          <Form.Item label="手机号归属人">
            {getFieldDecorator('mobileOwner')(
              <Select
                placeholder="请选择手机号归属人"
                allowClear
                showSearch
                loading={phoneOwnerLoading}
                filterOption={false}
                labelInValue
                onSearch={(value) => onPhoneOwnerSearch({ accountState: 0, employeeName: value })}
              >
                {phoneOwnerList?.map((item) => (
                  <Option key={item.employeeId} value={item.employeeId}>
                    {item.employeeName}
                  </Option>
                ))}
              </Select>,
            )}
          </Form.Item>

          {/* 经营人 */}
          <Form.Item label="经营人">
            {getFieldDecorator('operator')(
              <Select
                placeholder="请选择经营人"
                allowClear
                showSearch
                loading={operatorLoading}
                filterOption={false}
                labelInValue
                onSearch={(value) => onOperatorSearch({ accountState: 0, employeeName: value })}
              >
                {operatorList?.map((item) => (
                  <Option key={item.employeeId} value={item.employeeId}>
                    {item.employeeName}
                  </Option>
                ))}
              </Select>,
            )}
          </Form.Item>

          {/* 企业认证主体 */}
          <Form.Item label="企业认证主体">
            {getFieldDecorator('enterpriseCertSubject')(
              <Select
                placeholder="请选择企业认证主体"
                allowClear
                showSearch
                loading={companyLoading}
                onSearch={handleCompanySearch}
                onChange={handleCompanyChange}
                filterOption={false}
              >
                {companyList?.map((item) => (
                  <Option key={item.id} value={item.name}>
                    {item.name}
                  </Option>
                ))}
              </Select>,
            )}
          </Form.Item>

          {/* 企业号认证版本号 */}
          <Form.Item label="企业号认证版本">
            {getFieldDecorator('enterpriseCertVersion')(
              <Select
                placeholder="请选择企业号认证版本"
                allowClear
                onChange={(value) => setEnterpriseCertVersion(value as string)}
              >
                <Option value="new">新</Option>
                <Option value="old">旧</Option>
              </Select>,
            )}
          </Form.Item>

          {/* 运营人 - 仅当企业号认证版本号=新时显示 */}
          {enterpriseCertVersion === 'new' && (
            <Form.Item label="运营人">
              {getFieldDecorator('accountOperator')(
                <Select
                  placeholder="请选择运营人"
                  allowClear
                  showSearch
                  loading={operationStaffLoading}
                  filterOption={false}
                  labelInValue
                  onSearch={(value) =>
                    onOperationStaffSearch({ accountState: 0, employeeName: value })
                  }
                >
                  {operationStaffList?.map((item) => (
                    <Option key={item.employeeId} value={item.employeeId}>
                      {item.employeeName}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          )}

          {/* 平台实名人 - 仅当企业号认证版本号=旧时显示 */}
          {enterpriseCertVersion === 'old' && (
            <Form.Item label="平台实名人">
              {getFieldDecorator('realNamePerson')(
                <Select
                  placeholder="请选择平台实名人"
                  allowClear
                  showSearch
                  loading={platformRealNameLoading}
                  filterOption={false}
                  labelInValue
                  onSearch={(value) =>
                    onPlatformRealNameSearch({ accountState: 0, employeeName: value })
                  }
                >
                  {platformRealNameList?.map((item) => (
                    <Option key={item.employeeId} value={item.employeeId}>
                      {item.employeeName}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          )}

          {/* 认证期限 - 仅当企业号认证版本号=旧时显示 */}
          {enterpriseCertVersion === 'old' && (
            <Form.Item label="认证时间期限">
              {getFieldDecorator('certExpireDate')(
                <DatePicker
                  placeholder="请选择日期"
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                />,
              )}
            </Form.Item>
          )}
          {/* 2024年12月17日 开山ai结尾共生成167行代码 */}
        </Form>
      </Modal>
    </>
  );
};

export default Form.create<PropsType>()(AccountInformation);
