import React, { useEffect, useMemo, useState } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Form, Modal, Button, Select, InputNumber, message, Input, Icon, Spin } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import { useSearch, useInstiution, useByRole } from '../hooks';
import {
  LiveRoomListResult,
  editLiveRoom,
  liveRoomDetail,
  LiveRoomDetailResult,
} from '../Services/yml';
import { useRequest } from 'ahooks';
import { CooperationModeEnum, CooperationModeEnumCode } from '../dataSource';
import { plantformListAll, PLATFORM_ENUM } from '../../../../web_modules/types';
import { FileUploadSouceId, AuthorizeModal } from './index';
import styles from '../index.module.less';
import { useCode } from '@/common/constants/hooks';
import { isNullOrUndefined } from '@/utils/moduleUtils';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  id?: string;
  info?: Required<LiveRoomListResult>['records'][number];
  [key: string]: any;
  // defaultTab: string;
}

const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 14,
  },
};

const EditModal = (props: IProps) => {
  const { form, visible, id, onRefresh, info, ...rest } = props;
  const { getFieldDecorator, getFieldsValue } = form;
  const { platform, cooperationMode } = getFieldsValue();
  const { departmentList } = useSearch(false as any);
  const [techRate, setTechRate] = useState<{
    brandFeeTechRate?: number;
    commissionTechRate?: number;
  }>();
  const [authVisible, setAuthVisible] = useState(false);
  const [detail, setDetail] = useState<LiveRoomDetailResult>();

  const { codeList } = useCode('BUSINESS_UNIT', { able: true });

  const {
    institution,
    handleInstitutionChange,
    handleInstitutionSearch,
    liveRoomInstitutionListRun,
    liveRoomInstitutionListLoading,
  } = useInstiution();

  const { roleList, roleRun, roleLoading, handleRoleChange, handelRoleSearch, handleRoleBlur } =
    useByRole();

  const handleInitForm = (value: LiveRoomDetailResult) => {
    const {
      institutionId,
      institutionName,
      buId,
      buName,
      commissionTechRate,
      name,
      avatar,
      liveRoomInfoChangeRecordVOList,
      platformEnum,
      cooperationMode,
      buyinId,
      platformId,
      account,
      authorId,
      sortingNum,
      businessLineId,
      businessLine,
      projectManagers: projectManagersList,
    } = value;

    const institution = { label: institutionName, key: institutionId };
    const bu = { label: buName, key: buId };
    const businessLineAll = { label: businessLine, key: businessLineId };
    const projectManager = projectManagersList?.map((item) => ({
      label: item?.employeeName,
      key: item?.employeeId,
    }));
    form.setFieldsValue(
      {
        institution,
        bu,
        liveRoomInfoChangeRecordVOList,
        platform: platformEnum,
        cooperationMode,
        platformId,
        account,
        authorId,
        sortingNum,
        businessLine: businessLineAll,
        projectManager,
      },
      () => {
        if (platformEnum && platformEnum !== PLATFORM_ENUM.DY) {
          form.setFieldsValue({
            name,
            img: avatar ? [{ url: avatar }] : [],
          });
        }
        if (platformEnum && platformEnum === PLATFORM_ENUM.DY) {
          form.setFieldsValue({
            buyinId,
          });
        }
        if (cooperationMode === CooperationModeEnumCode.COLONEL) {
          form.setFieldsValue({
            commissionTechRate: !isNullOrUndefined(commissionTechRate)
              ? (commissionTechRate * 100).toFixed(2)
              : undefined,
          });
        }
      },
    );
  };

  const { run: detailRun, loading: detailLoading } = useRequest(liveRoomDetail, {
    manual: true,
    onSuccess({ res }) {
      console.log('🚀 ~ onSuccess ~ res:', res);
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      setDetail(res?.result || {});
      handleInitForm(res?.result || {});
    },
  });
  const { run, loading } = useRequest(editLiveRoom, {
    manual: true,
    onSuccess({ res }) {
      if (res.code === '200') {
        message.success('编辑成功');
        onRefresh();
        // @ts-ignore
        props?.onOk && props?.onOk();
      } else {
        message.warning(res.message || '网络异常');
      }
    },
  });
  const handleOnok = () => {
    form.validateFields((err, values) => {
      console.log('🚀 ~ form.validateFields ~ values:', values);
      if (err) return;
      const {
        institution,
        businessLine: businessLineMap,
        bu,
        commissionTechRate,
        img,
        name,
        projectManager,
        ...otherValue
      } = values;
      const { label: institutionName, key: institutionId } = institution;
      const { label: buName, key: buId } = bu;
      const { label: businessLine, key: businessLineId } = businessLineMap;
      const params = {
        ...otherValue,
        institutionId,
        buId,
        businessLineId,
        commissionTechRate: !isNullOrUndefined(commissionTechRate)
          ? (commissionTechRate / 100).toFixed(4)
          : undefined,
        institutionName,
        buName,
        businessLine,
        id: info?.id,
        name,
        businessLineId: values?.businessLine?.key,
        businessLine: values?.businessLine?.label,
      };
      if (img?.length) {
        params.img = img[0]?.url;
      }
      if (projectManager?.length) {
        params.projectManagerIdList = projectManager?.map((item: any) => item.key);
      }

      run(params);
    });
  };
  const infoNumber = (value?: string | null) => {
    return isNaN(Number(value)) ? null : Number(value);
  };

  useEffect(() => {
    if (info) {
      const commTechRate = !isNaN(Number(info?.commissionTechRate))
        ? Number(info?.commissionTechRate) * 100
        : undefined;
      const branFeeTechRate = !isNaN(Number(info?.brandFeeTechRate))
        ? Number(info?.brandFeeTechRate) * 100
        : undefined;

      setTechRate({
        commissionTechRate: commTechRate,
        brandFeeTechRate: branFeeTechRate,
      });
      form.setFieldsValue({
        dept: { key: info?.buId, label: info?.buName },
        platformId: info.platformId,
        authorId: info?.authorId,
        commissionTechRate: commTechRate ? commTechRate * 100 : commTechRate,
        brandFeeTechRate: branFeeTechRate,
        sortingNum: info?.sortingNum,
        account: info?.account,
        businessLine: { key: info?.businessLineId, label: info?.businessLine },
        buyinId: info?.buyinId,
        cooperationMode: info?.cooperationMode,
      });
      setTimeout(() => {
        form.setFieldsValue({
          commissionTechRate: commTechRate,
        });
      });
    }
  }, [info]);

  const handleValidator = (_: unknown, value: number, callback: any) => {
    if (!value && value != 0) {
      return callback();
    }
    if (Number.isNaN(Number(value))) {
      return callback(`请输入0-100 支持2位小数`);
    }
    if (value > 100 || value < 0) {
      return callback(`可设范围${0}-${100}%`);
    }
    return callback();
  };

  const handlePlantForm = (value: any) => {
    form.setFieldsValue({
      cooperationMode: CooperationModeEnumCode.COLONEL,
    });
  };

  useEffect(() => {
    if (visible) {
      roleRun({ current: 1, size: 20 });
      liveRoomInstitutionListRun({});
    }
    if (visible && info?.id) {
      detailRun({ id: info?.id });
    }
  }, [visible, info]);

  return (
    <>
      <Modal
        title="编辑"
        {...rest}
        visible={visible}
        confirmLoading={loading}
        width={500}
        onOk={handleOnok}
      >
        <Spin spinning={detailLoading}>
          <Form labelAlign="right">
            <Form.Item
              {...formItemLayout}
              required
              label="机构名称"
              style={{ marginBottom: '1px' }}
            >
              {getFieldDecorator('institution', {
                rules: [
                  {
                    required: true,
                    message: '请输入机构名称',
                  },
                ],
              })(
                <Select
                  placeholder="请选择"
                  allowClear
                  onSearch={handleInstitutionSearch}
                  onChange={handleInstitutionChange}
                  filterOption={false}
                  showSearch
                  labelInValue={true}
                  loading={liveRoomInstitutionListLoading}
                >
                  {institution?.map((item) => (
                    <Select.Option value={item?.storeId} key={item?.storeId}>
                      {item?.storeName}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item {...formItemLayout} required label="业务线" style={{ marginBottom: '1px' }}>
              {getFieldDecorator('businessLine', {
                rules: [
                  {
                    required: true,
                    message: '请选择业务线',
                  },
                ],
              })(
                <Select placeholder="请选择业务线" allowClear labelInValue={true}>
                  {codeList.map((item) => (
                    <Select.Option value={item?.value} key={item?.value}>
                      {item?.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item {...formItemLayout} required label="事业部" style={{ marginBottom: '1px' }}>
              {getFieldDecorator('bu', {
                rules: [
                  {
                    required: true,
                    message: '请选择事业部',
                  },
                ],
              })(
                <Select placeholder="请选择事业部" allowClear labelInValue={true}>
                  {departmentList.map((item) => (
                    <Select.Option value={item.id} key={item.id}>
                      {item.deptName}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item {...formItemLayout} required label="平台" style={{ marginBottom: '1px' }}>
              {getFieldDecorator('platform', {
                rules: [
                  {
                    required: true,
                    message: '请选择平台',
                  },
                ],
              })(
                <Select placeholder="请选择" allowClear onChange={handlePlantForm} disabled={true}>
                  {plantformListAll.map((item) => (
                    <Select.Option value={item.value} key={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item
              {...formItemLayout}
              required
              label="推广链接类型"
              style={{ marginBottom: '1px' }}
            >
              {getFieldDecorator('cooperationMode', {
                rules: [
                  {
                    required: true,
                    message: '请选择推广链接类型',
                  },
                ],
                // initialValue: CooperationModeEnumCode.COLONEL,
              })(
                <Select
                  placeholder="请选择推广链接类型"
                  allowClear
                  disabled={platform === PLATFORM_ENUM.DY ? false : true}
                >
                  {Object.entries(CooperationModeEnum).map(([key, value]) => (
                    <Select.Option value={key} key={key}>
                      {value}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>

            {cooperationMode === CooperationModeEnumCode.COLONEL && (
              <Form.Item
                {...formItemLayout}
                required
                label="团长分佣比例"
                style={{ marginBottom: '1px' }}
              >
                {getFieldDecorator('commissionTechRate', {
                  rules: [
                    { required: true, message: '请填写团长分佣比例' },
                    {
                      validator: handleValidator,
                    },
                  ],
                })(
                  <InputNumber
                    formatter={(value) => `${value}%`}
                    parser={(value) => value!.replace('%', '')}
                    placeholder="%"
                    precision={2}
                    style={{ width: '100%' }}
                    min={0}
                    max={100}
                  />,
                )}
              </Form.Item>
            )}
            {platform === PLATFORM_ENUM.DY && (
              <Form.Item
                {...formItemLayout}
                required
                label="达人百应ID"
                style={{ marginBottom: '1px' }}
              >
                {getFieldDecorator('buyinId', {
                  rules: [
                    { required: true, message: '请填写达人百应ID' },
                    {
                      pattern: /^[0-9]+$/,
                      message: '只能输入数字',
                    },
                  ],
                })(<Input placeholder="请输入" style={{ width: '100%' }} maxLength={19} />)}
              </Form.Item>
            )}

            <Form.Item
              {...formItemLayout}
              // required
              label="平台达人ID"
              style={{ marginBottom: '1px' }}
            >
              {getFieldDecorator('platformId', {
                rules: [
                  // { required: true, message: '请填写' },
                  {
                    // validator: handleValidator,
                  },
                ],
              })(<Input placeholder="请输入" style={{ width: '100%' }} maxLength={50} />)}
            </Form.Item>
            <Form.Item
              {...formItemLayout}
              // required
              label="抖币账号"
              style={{ marginBottom: '1px' }}
            >
              {getFieldDecorator('account', {
                rules: [
                  // { required: true, message: '请填写' },
                  {
                    // validator: handleValidator,
                  },
                ],
              })(
                <Input
                  placeholder="多个使用英文逗号隔开"
                  style={{ width: '100%' }}
                  maxLength={50}
                />,
              )}
            </Form.Item>

            <Form.Item
              {...formItemLayout}
              required
              label="author_id"
              style={{ marginBottom: '1px' }}
            >
              {getFieldDecorator('authorId', {
                rules: [{ required: true, message: '请输入author_id' }],
              })(<Input placeholder="请输入" style={{ width: '100%' }} maxLength={50} />)}
            </Form.Item>

            <Form.Item
              {...formItemLayout}
              label="项目经理"
              required
              style={{ marginBottom: '1px' }}
            >
              {getFieldDecorator('projectManager', {
                rules: [{ required: true, message: '请输入项目经理' }],
              })(
                <Select
                  loading={roleLoading}
                  placeholder="请选择"
                  allowClear
                  filterOption={false}
                  onSearch={handelRoleSearch}
                  onChange={handleRoleChange}
                  showSearch
                  onBlur={handleRoleBlur}
                  labelInValue={true}
                  mode="multiple"
                >
                  {roleList?.map((item) => (
                    <Select.Option key={item?.employeeId} value={item?.employeeId}>
                      {item?.employeeName}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
            <Form.Item
              {...formItemLayout}
              // required
              label="所属事业部排序"
              style={{ marginBottom: '1px' }}
            >
              {getFieldDecorator('sortingNum', {
                rules: [
                  {
                    pattern: /^0$|^\+?[1-9]\d*$/,
                    message: '请输入正整数',
                  },
                ],
              })(<InputNumber min={0} max={9999} style={{ width: '100%' }} />)}
            </Form.Item>
            {/**
             * @brief 非抖音显示直播间名称和头像
             */}
            {platform && platform !== PLATFORM_ENUM.DY && (
              <>
                <Form.Item
                  {...formItemLayout}
                  label="直播间名称"
                  style={{ marginBottom: '1px' }}
                  required
                >
                  {getFieldDecorator('name', {
                    rules: [
                      {
                        required: true,
                        message: '请输入直播间名称',
                      },
                    ],
                  })(
                    <Input
                      placeholder="请输入直播间名称"
                      style={{ width: '100%' }}
                      maxLength={30}
                    />,
                  )}
                </Form.Item>
                <Form.Item
                  {...formItemLayout}
                  label="直播间头像"
                  style={{ marginBottom: '1px' }}
                  required
                >
                  {getFieldDecorator('img', {
                    rules: [
                      {
                        required: true,
                        message: '请上传直播间头像',
                      },
                    ],
                  })(
                    <FileUploadSouceId
                      maxLen={1}
                      typeCode="SPU_QUALIFICATION"
                      isImage
                      maxSize={20 * 1024 * 1024}
                      multiple
                      accept={'.jpg,.jpeg,.png'}
                      NoticeText={''}
                    />,
                  )}
                </Form.Item>
              </>
            )}
          </Form>
        </Spin>
      </Modal>
      <AuthorizeModal visible={authVisible} close={() => setAuthVisible(false)} />
    </>
  );
};

export default Form.create<IProps>()(WithToggleModal(EditModal));
