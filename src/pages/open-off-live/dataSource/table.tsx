import React from 'react';
import { Modal } from 'antd';
import { EditModal, ScoringCriteriaModal } from '../components';
import { AuthWrapper } from 'qmkit';
import { PlantformName, PLATFORM_ENUM_KEY, PLATFORM_ENUM } from '../../../../web_modules/types';
import { LiveRoomListResult } from '../Services/yml';
import moment from 'moment';
import { dealDecimalToPercent } from '@/utils/string';
import style from '../../gmvCommissionManage/components/gmvCommissionManage/view/index.module.less';
import PopoverRowText from '@/components/PopoverRowText/index';
import { ColumnProps } from 'antd/lib/table';
import {
  ENABLE_STATUS_TEXT_ENUM,
  AUTHORIZE_STATUS_TEXT_ENUM,
  ENABLE_STATUS_ENUM,
  AUTHORIZE_STATUS_ENUM,
} from '../types';
import { history } from 'qmkit';
import AccountInformation from '../components/AccountInformation';

type LiveRoomListItem = Required<LiveRoomListResult>['records'][number];

export enum CooperationModeEnumCode {
  DIRECT = 'DIRECT',
  COLONEL = 'COLONEL',
}

export enum CooperationModeEnum {
  DIRECT = '定向',
  COLONEL = '团长',
}
export enum CooperationModeColorEnum {
  DIRECT = 'orange',
  COLONEL = 'blue',
}
export const columns = (onRefresh: any, pagination: any, handleOpen: any, disableEnable: any) => {
  const { liveRoomDisableRun, liveRoomEnableRun } = disableEnable;
  const handleDisEna = (id: any, type: 'dis' | 'ena') => {
    Modal.confirm({
      title: '提示',
      content: `您确定要${type === 'dis' ? '禁用' : '启用'}这条数据吗?`,
      onOk() {
        type === 'dis'
          ? liveRoomDisableRun({
              id,
            })
          : liveRoomEnableRun({
              id,
            });
      },
    });
  };
  return [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: style['table-number'],
      render: (_, __, index) => {
        return (
          <span>
            {((pagination.current < 1 ? 1 : pagination.current) - 1) * pagination.size + index + 1}
          </span>
        );
      },
      width: 28,
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'enableStatus',
      key: 'enableStatus',
      render: (_: AUTHORIZE_STATUS_ENUM) => AUTHORIZE_STATUS_TEXT_ENUM[_] || '-',
    },
    {
      title: '达人编号',
      width: 100,
      dataIndex: 'talentNo',
      key: 'talentNo',
      render: (talentNo: string, record) => {
        return talentNo ? (
          <PopoverRowText text={talentNo}>
            <a onClick={() => history.push('/open-off-live-detail?id=' + record?.id)}>
              {' '}
              {talentNo}
            </a>
          </PopoverRowText>
        ) : (
          '-'
        );
      },
    },
    {
      title: '平台',
      width: 80,
      dataIndex: 'platform',
      key: 'platform',
      render: (plantform: string) => {
        return <span>{plantform ? PlantformName[plantform as PLATFORM_ENUM_KEY] : '-'}</span>;
      },
    },
    {
      title: '事业部',
      width: 130,
      dataIndex: 'buName',
      key: 'buName',
      render: (buName: string) => {
        return <span>{buName || '-'}</span>;
      },
    },
    {
      title: '业务线',
      width: 150,
      dataIndex: 'businessLine',
      key: 'businessLine',
      render: (businessUnit: string) => {
        return <span>{businessUnit || '-'}</span>;
      },
    },
    {
      title: '直播间名称',
      width: 150,
      dataIndex: 'name',
      key: 'name',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '平台登录手机号',
      width: 140,
      dataIndex: 'platformMobile',
      key: 'platformMobile',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '手机号归属人',
      width: 120,
      dataIndex: 'mobileOwnerName',
      key: 'mobileOwnerName',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '经营人',
      width: 100,
      dataIndex: 'operatorName',
      key: 'operatorName',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '企业认证主体',
      width: 160,
      dataIndex: 'enterpriseCertSubject',
      key: 'enterpriseCertSubject',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '项目经理',
      width: 150,
      dataIndex: 'projectManagers',
      key: 'projectManagers',
      render: (val: LiveRoomListItem['projectManagers']) => {
        if (!val?.length) return '-';
        const name = val?.map((item) => item?.employeeName);
        const names = name.filter((item) => item);
        if (!names.length) return '-';
        return <PopoverRowText text={names.toString()} />;
      },
    },
    {
      title: '链接类型',
      width: 90,
      dataIndex: 'cooperationMode',
      key: 'cooperationMode',
      render: (cooperationMode: keyof typeof CooperationModeEnum) =>
        cooperationMode ? CooperationModeEnum[cooperationMode] : '-',
    },
    {
      title: '团长佣金',
      width: 90,
      dataIndex: 'commissionTechRate',
      key: 'commissionTechRate',
      render: (commissionTechRate: number) => {
        return <span>{dealDecimalToPercent(commissionTechRate, 2) || '-'}</span>;
      },
    },
    {
      title: '达人百应ID',
      width: 150,
      dataIndex: 'buyinId',
      key: 'buyinId',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '平台达人ID',
      width: 150,
      dataIndex: 'platformId',
      key: 'platformId',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },

    {
      title: 'author_id',
      width: 150,
      dataIndex: 'authorId',
      key: 'authorId',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: 'openid',
      width: 290,
      dataIndex: 'openId',
      key: 'openId',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '机构名称',
      width: 180,
      dataIndex: 'institutionName',
      key: 'institutionName',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '机构Id',
      width: 130,
      dataIndex: 'institutionId',
      key: 'institutionId',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '机构编号',
      width: 140,
      dataIndex: 'institutionNo',
      key: 'institutionNo',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '达人公司全称',
      width: 140,
      dataIndex: 'talentCompany',
      key: 'talentCompany',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    // ai生成

    // 2024年12月17日 开山ai结尾共生成25行代码

    // {
    //   title: '佣金分佣比例',
    //   width: 170,
    //   dataIndex: 'commissionTechRate',
    //   key: 'commissionTechRate',
    //   align: 'right',
    //   render: (commissionTechRate: number) => {
    //     return <span>{dealDecimalToPercent(commissionTechRate, 2) || '-'}</span>;
    //   },
    // },

    {
      title: '固定服务费分佣比例',
      width: 200,
      dataIndex: 'brandFeeTechRate',
      key: 'brandFeeTechRate',
      align: 'right',
      render: (brandFeeTechRate: number) => {
        return <span>{dealDecimalToPercent(brandFeeTechRate, 2) || '-'}</span>;
      },
    },
    {
      title: '抖币账号',
      width: 170,
      dataIndex: 'account',
      key: 'account',
      render: (value: string) => {
        return <PopoverRowText text={value}></PopoverRowText>;
      },
    },
    {
      title: '创建时间',
      width: 140,
      dataIndex: 'gmtCreated',
      key: 'gmtCreated',
      render: (gmtCreated: string) => {
        return <span>{gmtCreated ? moment(gmtCreated).format('YYYY-MM-DD') : '-'}</span>;
      },
    },
    {
      title: '最近修改时间',
      width: 200,
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      render: (gmtModified: string) => {
        return <span>{gmtModified ? moment(gmtModified).format('YYYY-MM-DD') : '-'}</span>;
      },
    },
    {
      title: '所属事业部排序',
      width: 140,
      dataIndex: 'sortingNum',
      key: 'sortingNum',
      render: (sortingNum: string) => {
        return <span>{sortingNum || '-'}</span>;
      },
    },
    {
      title: '授权状态',
      width: 100,
      dataIndex: 'authorizeStatus',
      key: 'authorizeStatus',
      render: (_: ENABLE_STATUS_ENUM, records: LiveRoomListItem) =>
        records?.platform !== PLATFORM_ENUM.DY ? '-' : ENABLE_STATUS_TEXT_ENUM[_] || '-',
    },
    {
      title: '操作',
      width: 270,
      key: 'other',
      fixed: 'right',
      render: (records: LiveRoomListItem) => {
        return (
          <>
            <AuthWrapper functionName="f_open_off_live_edit">
              <EditModal info={records} id={records.id} onRefresh={onRefresh}>
                <a>编辑</a>
              </EditModal>
            </AuthWrapper>
            {records.platform == 'DY' && (
              <>
                <AuthWrapper functionName="f_eidt_scoring_button">
                  <ScoringCriteriaModal info={records} id={records.id} onRefresh={onRefresh}>
                    <a style={{ marginLeft: '4px' }}>评分标准</a>
                  </ScoringCriteriaModal>
                </AuthWrapper>
                {[ENABLE_STATUS_ENUM.WAIT_AUTH, ENABLE_STATUS_ENUM.EXPIRE_AUTH].includes(
                  records?.authorizeStatus as ENABLE_STATUS_ENUM,
                ) ? (
                  <AuthWrapper functionName="f_open_off_live_again">
                    <a
                      style={{ marginLeft: '4px' }}
                      onClick={() =>
                        handleOpen(
                          records?.preCreateId,
                          records?.preCreateId ? undefined : records?.talentId,
                        )
                      }
                    >
                      重新授权
                    </a>
                  </AuthWrapper>
                ) : (
                  <></>
                )}
              </>
            )}
            <AuthWrapper functionName="f_open_off_live_on_off">
              {(records?.enableStatus as LiveRoomListItem['enableStatus'] as AUTHORIZE_STATUS_ENUM) ===
              AUTHORIZE_STATUS_ENUM.DISABLE ? (
                <a
                  style={{ marginLeft: '4px', marginRight: '4px' }}
                  onClick={() => {
                    handleDisEna(records?.id, 'ena');
                  }}
                >
                  启用
                </a>
              ) : (
                <a
                  style={{ marginLeft: '4px', color: 'red', marginRight: '4px' }}
                  onClick={() => {
                    handleDisEna(records?.id, 'dis');
                  }}
                >
                  禁用
                </a>
              )}
            </AuthWrapper>
            <AuthWrapper functionName="f_accountIn_formation_open_close_live">
              <AccountInformation onRefresh={onRefresh} record={records} />
            </AuthWrapper>
          </>
        );
      },
    },
  ] as ColumnProps<LiveRoomListItem>[];
};
