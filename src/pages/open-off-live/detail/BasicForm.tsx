import React from 'react';
import {
  DetailContentItem,
  DetailContextBox,
} from '@/components/DetailFormCompoments/DetailContentItem';
import { Card } from '@/components/DetailFormCompoments';
import { LiveRoomDetailResult } from '../Services/yml';
import { formatFee } from '@/pages/anchor-information/utils/getColumns';
import moment from 'moment';
const Item = DetailContentItem;

type PropsType = {
  info?: LiveRoomDetailResult;
};
enum CooperationModeEnum {
  COLONEL = '团长',
  DIRECT = '定向',
}

const BasicForm: React.FC<PropsType> = ({ info }) => {
  return (
    <Card title="基本信息">
      <DetailContextBox>
        <Item label="事业部">
          <p>{info?.buName ?? '-'}</p>
        </Item>
        <Item label="平台">
          <p>{info?.platform ?? '-'}</p>
        </Item>
        <Item label="直播间名称">
          <p>{info?.name ?? '-'}</p>
        </Item>
        <Item label="推广链接类型">
          <p>{info?.cooperationMode ? CooperationModeEnum[info.cooperationMode] : '-'}</p>
        </Item>
        <Item label="平台注册手机号">
          <p>{info?.platformMobile ?? '-'}</p>
        </Item>
        <Item label="手机号归属人">
          <p>{info?.mobileOwnerName ?? '-'}</p>
        </Item>
        <Item label="经营人">
          <p>{info?.operatorName ?? '-'}</p>
        </Item>
        <Item label="企业认证主体">
          <p>{info?.enterpriseCertSubject ?? '-'}</p>
        </Item>
        <Item label="达人百应ID">
          <p>{info?.buyinId ?? '-'}</p>
        </Item>
        <Item label="平台达人ID">
          <p>{info?.platformId ?? '-'}</p>
        </Item>
        <Item label="抖币账号">
          <p>{info?.account ?? '-'}</p>
        </Item>
        <Item label="author_ID">
          <p>{info?.authorId ?? '-'}</p>
        </Item>
        <Item label="固定服务费分佣比例">
          <p>
            {!isNaN(Number(info?.brandFeeTechRate))
              ? formatFee(Number(info?.brandFeeTechRate) * 100) + '%'
              : '-'}
          </p>
        </Item>
        <Item label="所属事业部排序">
          <p>{info?.sortingNum ?? '-'}</p>
        </Item>
        {/* ai生成 */}
        <Item label="企业号认证版本号">
          <p>
            {info?.enterpriseCertVersion
              ? info.enterpriseCertVersion === 'new'
                ? '新'
                : '旧'
              : '-'}
          </p>
        </Item>
        {/* 运营人 - 仅当企业号认证版本号=新时显示 */}
        {info?.enterpriseCertVersion === 'new' && (
          <Item label="运营人">
            <p>{info?.accountOperatorName ?? '-'}</p>
          </Item>
        )}
        {/* 平台实名人 - 仅当企业号认证版本号=旧时显示 */}
        {info?.enterpriseCertVersion === 'old' && (
          <Item label="平台实名人">
            <p>{info?.realNamePersonName ?? '-'}</p>
          </Item>
        )}
        {/* 认证期限 - 仅当企业号认证版本号=旧时显示 */}
        {info?.enterpriseCertVersion === 'old' && (
          <Item label="认证期限">
            <p>{info?.certExpireDate ? moment(info?.certExpireDate).format('YYYY-MM-DD') : '-'}</p>
          </Item>
        )}
        {/* 2024年12月17日 开山ai结尾共生成29行代码 */}
      </DetailContextBox>
    </Card>
  );
};

export default BasicForm;
