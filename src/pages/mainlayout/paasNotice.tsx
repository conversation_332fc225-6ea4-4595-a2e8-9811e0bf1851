import React, { useEffect, useRef, useState } from 'react';
import { Badge, Icon, message, notification, Popover, Tabs, List, Tag } from 'antd';
import PaasNoticeList from './components/paasNoticeList';
import { workflowUnreadData } from '@/services/workflow/index';
import { util } from 'qmkit';

const { TabPane } = Tabs;

const PaasNotice = () => {
  const [loading, setLoading] = useState(false);
  const [todoMsgList, setTodoMsgList] = useState([]); // 待办列表
  const [noticeList, setNoticeList] = useState([]); // 通知列表
  const [tabsKey, setTabsKey] = useState('1');
  const [visible, setVisible] = useState(false);

  // 获取通知和待办数据
  const fetchNoticeData = () => {
    setLoading(true);
    workflowUnreadData().then((res) => {
      if (res?.res?.ok) {
        const result = res?.res?.data;
        setNoticeList(result?.notifyMsgList); // 通知列表
        setTodoMsgList(result?.todoMsgList); // 待办列表
      } else {
        message.error(res?.res?.msg || '网络错误');
      }
      setLoading(false);
    });
  };

  const tabChange = (key: string) => {
    console.log(`切换到 ${key}`);
    setTabsKey(key);
    fetchNoticeData(); // 切换标签时获取最新数据
  };

  const handleVisibleChange = (newVisible: boolean) => {
    setVisible(newVisible);
    if (newVisible) {
      fetchNoticeData(); // 打开Popover时获取最新数据
    }
  };

  useEffect(() => {
    // 检查用户是否已登录以及当前是否在登录页面
    const isLogin = util.isLogin();
    const isLoginPage = window.location.pathname === '/login';

    // 只有在已登录且不在登录页面的情况下才调用接口
    if (isLogin && !isLoginPage) {
      setTimeout(() => {
        fetchNoticeData();
      }, 2000);
    }
  }, []);

  return (
    <>
      <Popover
        visible={visible}
        onVisibleChange={() => handleVisibleChange(!visible)}
        content={
          <div style={{ width: 322 }}>
            <Tabs defaultActiveKey="1" onChange={tabChange}>
              <TabPane tab={`待办(${todoMsgList.length})`} key="1">
                <PaasNoticeList
                  noticeList={todoMsgList}
                  tabsKey={tabsKey}
                  handleVisibleChange={handleVisibleChange}
                ></PaasNoticeList>
              </TabPane>
              {/* <TabPane tab={`通知(${noticeList.length})`} key="2">
                <PaasNoticeList
                  noticeList={noticeList}
                  tabsKey={tabsKey}
                  handleVisibleChange={handleVisibleChange}
                ></PaasNoticeList>
              </TabPane> */}
            </Tabs>
          </div>
        }
        trigger="click"
      >
        <Badge dot={todoMsgList.length ? true : false}>
          <Icon type="audit" style={{ color: '#999', cursor: 'pointer' }} />
        </Badge>
      </Popover>
    </>
  );
};

export default PaasNotice;
