import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Layout, Icon, Divider, Popover } from 'antd';
import styles from './index.module.less';
import _, { divide } from 'lodash';
import { shallowEqual, useDispatch, useSelector } from 'react-redux';
import { history } from 'qmkit';
import UserHeader from './UserHeader';
import { sessionTagType } from './utils';
import NoticePopover from 'web-common-modules/biz/Notice/NoticePopover';
import Notice from './notice';
import PaasNotice from './paasNotice';
// import './index.module.less';
const { Header } = Layout;
const ROUTE_STORAGE_KEY = 'routeValue';
const ROOT_PATHS = ['/', '/friendSeek/chatMain', '/workbench'];
// 使用useSelector的第二个参数shallowEqual进行性能优化
const useShallowEqualSelector = (selector: any) => useSelector(selector, shallowEqual);

const HeaderTag = () => {
  // 从redux获取按钮的初始状态和导航路由初始状态
  const collapsedData: any = useShallowEqualSelector((state: any) => state.collapsedData);
  const routePathData: any = useShallowEqualSelector((state: any) => state.routePathData);
  const pageNameList: any = useShallowEqualSelector(
    ({ breadcrumb }: any) => breadcrumb?.breadcrumbList,
  );
  const dispatch = useDispatch();
  const dispatchCallback = useCallback((action) => dispatch(action), [dispatch]);

  // 将路由初始状态重新赋值给新变量便于使用
  const [tagList, setTagList] = useState(routePathData?.routePath);

  // 更新redux
  const updateRedux = (type: string, value: sessionTagType | Array<sessionTagType>) => {
    dispatchCallback({
      type: type,
      payload: { value },
    });
  };

  //切换menu的折叠与展开
  const toggle = () => {
    dispatchCallback({
      type: 'SET_COLLAPSED',
      payload: { collapsed: !collapsedData?.collapsed },
    });
  };
  // useEffect(() => {
  //   let pathNameList = tagList?.map((i) => i.path);
  //   pathNameList?.includes('/workbench') && history.push('/workbench');
  // }, []);
  // tag关闭
  const onTagClose = (path: string) => {
    // 深拷贝一次tagList
    if (tagList.length > 1) {
      let newTagList = _.cloneDeep(tagList);
      let tagFlag = false;

      // 如果被点击清除的tag是当前的高亮tag，则将数组的最后一个元素设置为高亮
      const lastTag = newTagList.find(
        (item: sessionTagType) => item?.path === path && item?.isActive === true,
      );
      lastTag && (tagFlag = true);

      // 过滤掉被点击清除的tag
      newTagList = newTagList.filter((item: sessionTagType) => item?.path !== path);

      if (tagFlag) {
        const lastTag = newTagList[newTagList.length - 1];
        console.log('lastTag', lastTag);
        lastTag.isActive = true;
        history.push(lastTag?.path);
      }

      updateRedux('SET_ROUTE', newTagList);
    }
  };

  // 全部关闭
  const allClose = () => {
    const newTagList = [
      { pathName: 'BefriendsAI', isActive: true, path: '/friendSeek/chatMain' },
      { pathName: '首页', isActive: false, path: '/workbench' },
    ];
    updateRedux('SET_ROUTE', newTagList);
    history.push('/friendSeek/chatMain');
  };
  const besideMeClose = () => {
    const newTagList = tagList.filter((i: sessionTagType) => i.isActive === true);
    console.log('tagList001', tagList);
    if (newTagList[0].pageName !== '首页' && newTagList[0].pageName !== 'BefriendsAI') {
      newTagList.unshift({
        pathName: 'BefriendsAI',
        isActive: false,
        path: '/friendSeek/chatMain',
      });
      newTagList.unshift({ pathName: '首页', isActive: false, path: '/workbench' });
    }

    updateRedux('SET_ROUTE', newTagList);
  };

  // 选择tag, lodash的防抖进行优化
  const selectTagDebounce = _.debounce((path: string) => {
    if (path.includes('/financialSettlemen')) {
      window.history.pushState(null, '', path);
      // history.push(path);
    } else {
      history.push(path);
    }
  }, 200);
  const selectHiddenTagDebounce = _.debounce((path: string) => {
    history.push(path);
    setTimeout(() => {
      const arr = [...tagList];
      const arrHidden = [...hiddenArr];
      let index = 0;
      let oldIndex = 0;
      arr.forEach((item, key) => {
        if (arrHidden.length > 0 && item?.path === arrHidden[0].path) {
          index = key - 1;
        }
        if (item?.path === path) {
          oldIndex = key;
          item.isActive = true;
        } else {
          item.isActive = false;
        }
      });
      arr.splice(index, 0, arr.splice(oldIndex, 1)[0]);
      setTagList([...arr]);
      dispatchCallback({
        type: 'SET_KEEP_ALIVE_LIST',
        payload: { keepAliveList: arr },
      });
    }, 300);
  }, 200);

  // 这里是抽离的更新本地session的方法
  const updateSessionStorage = useCallback(
    (value: Array<sessionTagType>, pageName: string, storagePath: string, noSession?: boolean) => {
      let currentFlag = false;

      // 将所有的isActive 设置为false
      value.forEach((item) => {
        item.isActive = false;
        // 如果满足条件则设置高亮 isActive = true
        if (item?.path === storagePath || item?.pathName === pageName) {
          item.isActive = true;
          currentFlag = true;
        }
      });

      if (currentFlag) {
        // 如果当前高亮的路由不在数组中，则更新数组中的路由
        const noExistPath = value.every((item) => item.path !== storagePath);
        if (noExistPath) {
          const noExistItem = value.find((item) => item?.pathName === pageName);
          noExistItem && (noExistItem.path = storagePath);
        }
      } else {
        // 添加新路由
        value.push({
          pathName: pageName,
          isActive: true,
          path: storagePath,
        });
      }

      if (noSession) return;
      updateRedux('SET_ROUTE', value);
    },
    [],
  );

  // 根据当前URL设置高亮状态
  useEffect(() => {
    if (!tagList || !tagList.length) return;

    // 获取当前路径
    const currentPath = window.location.pathname;
    console.log('historycurrentPath', currentPath);
    // 如果当前路径是/friendSeek/chatMain，确保Friends Manus标签被高亮
    if (currentPath === '/') {
      setTimeout(() => {
        history.push('/friendSeek/chatMain');
      }, 100);
    }
    if (currentPath === '/friendSeek/chatMain') {
      const updatedTagList = tagList.map((tag: sessionTagType) => ({
        ...tag,
        isActive: tag.path === '/friendSeek/chatMain',
      }));

      if (JSON.stringify(updatedTagList) !== JSON.stringify(tagList)) {
        setTagList(updatedTagList);

        updateRedux('SET_ROUTE', updatedTagList);
      }
    }
  }, []);

  // 这里监听的pageNameList是从redux中获取的
  // 可以在其他组件中使用redux的useDispatch钩子来改变这里的pageNameList
  useEffect(() => {
    if (!pageNameList || !pageNameList.length) return;
    const firstPage = pageNameList[0];
    if (!firstPage?.name || !firstPage?.path) return;

    const { name: pageName, path } = firstPage;
    let { url: storagePath } = firstPage;
    !storagePath && (storagePath = path);
    const routeValue = JSON.parse(sessionStorage.getItem(ROUTE_STORAGE_KEY) as string);
    // 如果session中有tag则更新redux
    if (routeValue) {
      updateSessionStorage(routeValue, pageName, storagePath);
    } else {
      // 没有则初始化session
      const newRoutePath = _.cloneDeep(routePathData.routePath); // 深拷贝一次
      // 如果当前路由不是首页则将当前路由更新到session
      !ROOT_PATHS.includes(storagePath) &&
        updateSessionStorage(newRoutePath, pageName, storagePath, true);
      sessionStorage.setItem(ROUTE_STORAGE_KEY, JSON.stringify(newRoutePath));
      newRoutePath.length > 1 && updateRedux('SET_ROUTE', newRoutePath);
    }
  }, [pageNameList]);

  // redux变化时更新tagList
  useEffect(() => {
    if (!routePathData.routePath) return;
    const arr = [...routePathData.routePath];
    let activeKey = 0;
    arr.forEach((i, key) => {
      if (i.path === '/workbench') {
        i.pathName = '首页';
      }
      if (i.path === '/friendSeek/chatMain') {
        i.pathName = 'BefriendsAI';
      }
      if (i.isActive === true && key > 8) {
        activeKey = key;
      }
    });
    activeKey && arr.splice(9, 0, arr.splice(activeKey, 1)[0]);
    setTagList(arr);
    dispatchCallback({
      type: 'SET_KEEP_ALIVE_LIST',
      payload: { keepAliveList: arr },
    });
  }, [routePathData.routePath]);
  useEffect(() => {
    getWidth();
  }, [tagList]);
  const tabBoxRef = useRef(null);
  const tabAllRef = useRef(null);
  const [hiddenArr, setHiddenArr] = useState<any[]>([]);
  const getWidth = () => {
    const arr: any = [];
    let sum = 0;
    tagList.forEach((item: any, key: number) => {
      if (sum + 100 < tabAllRef.current?.offsetWidth) {
        sum = sum + item.pathName.length * 12 + 50;
      } else {
        arr.push(item);
      }
    });
    setHiddenArr([...arr]);
  };

  return (
    <Header className={styles.newHeaderBox}>
      {/* 侧边栏控制Icon */}
      <Icon
        className="trigger"
        style={{ fontSize: '18px' }}
        type={collapsedData?.collapsed ? 'menu-unfold' : 'menu-fold'}
        onClick={toggle}
      />

      <div className={styles.contentBox} ref={tabAllRef}>
        <div className={styles.tagBox} ref={tabBoxRef}>
          {tagList.length &&
            tagList.map((item: sessionTagType, index: number) => (
              <div key={item?.path} className={styles.mapTagBox}>
                {/* 路由名称 */}
                <span className={`${styles.tagBoxSpan}  ${item?.isActive ? styles.isActive : ''}`}>
                  <span onClick={() => selectTagDebounce(item?.path)}>{item?.pathName}</span>
                  {/* 关闭icon */}
                  {item.path !== '/workbench' && item.path !== '/friendSeek/chatMain' && (
                    <Icon
                      type="close"
                      className={styles.tagBoxIcon}
                      onClick={() => onTagClose(item?.path)}
                    />
                  )}
                </span>

                {/* 分割线 */}
                {index !== tagList.length - 1 && (
                  <Divider type="vertical" className={styles.tagDivider} />
                )}
              </div>
            ))}
        </div>
        <div className={styles.tagRight}>
          {hiddenArr.length > 0 && (
            <Popover
              trigger="hover"
              content={
                <div className={styles.tagPopverList}>
                  {/* <p className={styles.closeTag}>主页</p>
                <p className={styles.closeTag}>关闭除当前页之外的所有标签页</p> */}
                  {hiddenArr.map((item: sessionTagType, index: number) => (
                    <div key={item?.path}>
                      {/* 路由名称 */}
                      <p className={`${styles.closeTag}  ${item?.isActive ? styles.isActive : ''}`}>
                        <span onClick={() => selectHiddenTagDebounce(item?.path)}>
                          {item?.pathName}
                        </span>
                        {/* 关闭icon */}
                        <Icon
                          type="close"
                          className={styles.tagBoxIcon}
                          onClick={() => onTagClose(item?.path)}
                        />
                      </p>
                    </div>
                  ))}
                </div>
              }
            >
              <div className={styles.dropdownBtn}>···</div>
            </Popover>
          )}
          {tagList.length > 1 && (
            <Popover
              content={
                <div className={styles.tagPopverList}>
                  <p className={styles.closeTag} onClick={allClose}>
                    关闭所有标签页
                  </p>
                  <p className={styles.closeTag} onClick={besideMeClose}>
                    关闭除当前页之外的所有标签页
                  </p>
                </div>
              }
              trigger="click"
            >
              <Icon
                type="close"
                style={{ fontSize: '14px', cursor: 'pointer', marginLeft: '8px' }}
              />
            </Popover>
          )}
        </div>
      </div>
      <div className={styles.paasNoticeBox}>
        <PaasNotice />
      </div>
      <div className={styles.noticeBox}>
        <Notice crm={true} />
      </div>
      <div className={styles.userBox}>
        <UserHeader />
      </div>
    </Header>
  );
};

export default HeaderTag;
