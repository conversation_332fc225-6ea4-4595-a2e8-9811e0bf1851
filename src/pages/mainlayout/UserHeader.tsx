import { Dropdown, Icon, Menu } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './header.module.less';
import { util, Const, AuthWrapper } from 'qmkit';
import { OSSImagePre } from '@/common/constants/moduleConstant';
import { useDispatch } from 'react-redux';

const defaultAvatar = `${OSSImagePre}/jgpy-crm/user.png`;

const apiEntry =
  Const.NODE_SERVER_ENV === 'production'
    ? 'https://paas.befriends.com.cn'
    : Const.NODE_SERVER_ENV === 'development'
    ? '//localhost:8082'
    : Const.NODE_SERVER_ENV === 'dev'
    ? 'https://paas-dev.befriends.com.cn'
    : Const.NODE_SERVER_ENV === 'test'
    ? 'https://paas-test.befriends.com.cn'
    : 'https://paas-uat.befriends.com.cn';
const UserHeader = () => {
  const [tenantAvatar, setTenantAvatar] = useState(defaultAvatar);
  const [nickName, setNickName] = useState('你好');
  const [isUp, setIsUp] = useState(false);
  const dispatch = useDispatch();

  const _handleLogout = () => {
    util.logout(dispatch);
  };
  // 跳转审批流系统
  const goPaasFun = () => {
    const loginInfo = JSON.parse(localStorage.getItem('jgpy-crm@loginInfo') as string);
    // window.open(`${apiEntry}/home?app_token=${loginInfo.token}`);
    window.open(`${apiEntry}/welcome?authUserId=${loginInfo.authUserId}`);
  };

  const changeType = (val: boolean) => {
    setIsUp(val);
  };

  useEffect(() => {
    const loginInfo = JSON.parse(localStorage.getItem('jgpy-crm@loginInfo') as string);
    const loginMsg = JSON.parse(localStorage.getItem('jgpy-crm@login') as string);
    loginInfo && setTenantAvatar(loginInfo?.tenantAvatar || defaultAvatar);
    loginMsg && setNickName(loginMsg?.employeeName || '你好');
  }, []);

  // 自定义下拉菜单内容，避免使用Menu组件
  const menu = (
    <div className={styles.headerMenu}>
      <div className={styles.logout} onClick={() => _handleLogout()}>
        <span>
          <Icon type="logout" /> 退出
        </span>
      </div>
      <AuthWrapper functionName="f_system_settings_goPaas">
        <div className={styles.logout} onClick={() => goPaasFun()}>
          <span>PAAS</span>
        </div>
      </AuthWrapper>
    </div>
  );

  return (
    <div className={styles.userNewBox}>
      <div className={styles.nicknameBox}>
        <span className={styles.nicknameSpan}>Hi，</span>
        <span className={styles.spanTag}>{nickName}</span>
      </div>
      <Dropdown overlay={menu} trigger={['click']} onVisibleChange={changeType}>
        <div style={{ cursor: 'pointer' }}>
          <img className={styles.userTenantAvatar} src={tenantAvatar} alt="" />
          <Icon type={isUp ? 'up' : 'down'} style={{ fontSize: '10px', marginLeft: '10px' }} />
        </div>
      </Dropdown>
    </div>
  );
};

export default UserHeader;
