import React, { useEffect, useMemo } from 'react';
import { useSetState } from 'ahooks';
import { Menu, Layout } from 'antd';
import { cache, history } from 'qmkit';
import { getMenu, MenuResultItem } from '@/services/user';
import { listToTree } from '@/utils/treeData';
import { allRoutes } from '@/router';
import { MENU_TYPE } from '@/common/constants/moduleConstant';
import { filterMenus, handleMenu, MenuIcon, MenuActiveIcon } from './utils';
import { getQueryParams } from 'web-common-modules/utils/params';
import './index.less';
import { useLocation } from 'react-router';
import { IRoute } from '@/router';
import { connect, useSelector } from 'react-redux';
import { searchCondition } from '@/utils/moduleUtils';
import logo from '@/assets/<EMAIL>';
import zbPng from '@/assets/<EMAIL>';
import _ from 'lodash';
// import { useWebSocket } from '../weibo/private-message-task/utils/websocket';

const { Sider } = Layout;
const { SubMenu } = Menu;

const getFirstMenu = (menu: MenuResultItem): string => {
  if (menu.grade === 1 && menu.url) return menu.url;
  if (!menu.children || !menu?.children?.length) return menu?.url;
  return getFirstMenu(menu?.children[0]);
};

interface LayoutMenuProps {
  pathname: string;
  selectedKeys: string[];
}

interface stateProps {
  menuList: MenuResultItem[];
  dealMenus: MenuResultItem[];
  firstActive: number;
  openKeys: string[];
}

// 需要展示侧边栏的路由
const needCbArr = ['/polymerization-goods-detail', '/live-monitoring'];

const LayoutMenu: React.FC<LayoutMenuProps> = ({ pathname, selectedKeys }) => {
  const location = useLocation();
  const [state, setState] = useSetState<stateProps>({
    menuList: [],
    dealMenus: [],
    firstActive: 0,
    openKeys: [],
  });

  // 从redux中读取变量
  const collapsedData = useSelector((state: any) => state.collapsedData);
  // 侧边栏折叠时Menu组件的属性不设置openKeys，不折叠时才设置
  const defaultProps = collapsedData?.collapsed ? {} : { openKeys: state.openKeys };
  console.log('🚀 ~ location ~ location:', location);
  useEffect(() => {
    (async () => {
      const response = await getMenu();
      const { res } = response;
      if (res?.success) {
        const originMenus = res?.result || [];
        const dealMenus: MenuResultItem[] = filterMenus(originMenus);
        const menuList = listToTree(dealMenus, {
          getFirstLevelData: (list) => list.filter((item) => item?.grade == 1),
          key: 'id',
          parentKey: 'pid',
          getTreeData: (item) => ({
            ...item,
          }),
        });
        const newMenus = handleMenu(menuList);
        localStorage.setItem(cache.LOGIN_MENUS, JSON.stringify(newMenus));

        setState({
          dealMenus,
          menuList: newMenus as MenuResultItem[],
        });
      }
    })();
  }, []);

  // 设置选中的菜单
  useEffect(() => {
    const { menuList = [] } = state;

    if (!menuList.length) {
      return;
    }
    if (pathname === '/live-list' && menuList.length) {
      const liveItem = menuList.find((_: any) => _.title === '直播监控');
      const { children } = liveItem || {};
      if (children) {
        const [firstItem] = children;
        firstItem && history.push(children[0].url);
      }
      return;
    }

    if ((!pathname || pathname === '/') && menuList.length) {
      let firstMenu = getFirstMenu(menuList[state.firstActive]);
      // 没办法，兼容老菜单url规则
      firstMenu =
        firstMenu === '/polymerization-goods-detail' ? '/polymerization-goods' : firstMenu;
      history.push(firstMenu);
    }
  }, [state?.menuList, pathname, state.firstActive]);

  useEffect(() => {
    if (!state?.menuList?.length) return;
    const path = location.pathname;
    let firstActive = state.firstActive;
    state.menuList.some((level1, index) => {
      return level1.children?.some((level2) => {
        // 加了一个 needCbArr 路由的判断，为了在这些路由侧边栏也完全展示
        if (level2.url === path || needCbArr.includes(path)) {
          firstActive = index;
          return true;
        } else {
          level2.children?.some((level3) => {
            if (level3.url === path) {
              firstActive = index;
              return true;
            }
            return false;
          });
        }
      });
    });
    setState({ firstActive });
  }, [location.pathname, state?.menuList]);

  const onSelect = ({ key }: { key: string }) => {
    console.log('🚀 ~ onSelect ~ key:', key);
    if (key === 'friendSeek/chatMain') {
      history.push('/friendSeek/chatMain');
      searchCondition.clear();
      return;
    }
    key && history.push(key);
    searchCondition.clear();
  };

  // 防抖
  const selectDebounce = _.debounce((value) => onSelect(value), 300);

  const renderSubMenu = useMemo(() => {
    const menu = state?.menuList[state.firstActive];

    // menu?.children?.forEach((item, key) => {
    //   if (item.url === '/workbench') {
    //     menu?.children?.shift();
    //   }
    // });
    return (
      menu?.children &&
      menu.children.map((item) => {
        let flag;
        if (item?.children) {
          flag = item.children.some((cItem) => selectedKeys[0] === cItem?.url);
          return (
            <SubMenu
              key={item.id}
              popupClassName={collapsedData?.collapsed ? 'menuSubChild' : ''} // 侧边栏折叠时样式
              title={
                <div>
                  {/* 菜单前的图标 */}
                  <img src={flag ? MenuActiveIcon[item.title] : MenuIcon[item.title]} />
                  {!collapsedData?.collapsed && item?.title}
                </div>
              }
            >
              {item?.children.map((menuItem) => (
                <Menu.Item key={menuItem?.url}>{menuItem?.title}</Menu.Item>
              ))}
            </SubMenu>
          );
        }
        flag = selectedKeys[0] === item?.url;
        return (
          <Menu.Item
            className="noChildMenu"
            key={item?.url}
            title="首页"
            style={{ display: 'none' }}
          >
            <img src={flag ? MenuActiveIcon[item.title] : MenuIcon[item.title]} />
            {!collapsedData?.collapsed && item?.title}
          </Menu.Item>
        );
      })
    );
  }, [state?.menuList, state.firstActive, selectedKeys, collapsedData?.collapsed]);

  const shouldRenderMenu = useMemo(() => {
    const path = window.location.pathname;
    const pathItem = allRoutes.find((item: any) => item?.path === path);
    if (pathItem?.menu === MENU_TYPE.NONE || getQueryParams()?.noMenu) {
      return false;
    }
    return true;
  }, [location?.pathname, allRoutes, location?.search]);

  const getOpenKeys = (pid?: string): any => {
    let menu;
    if (pid) {
      menu = state.dealMenus.filter((item) => {
        return item.id === pid;
      })?.[0];
    } else {
      menu = state.dealMenus.filter((item) => {
        return item.url === selectedKeys[0];
      })?.[0];
    }
    if (menu?.grade > 2) {
      return getOpenKeys(menu.pid);
    }
    setState({
      openKeys: [menu?.id],
    });
  };

  const onOpenChange = (openKeys: any[]) => {
    console.log('🚀 ~ onOpenChange ~ openKeys:', openKeys);
    const latestOpenKey = openKeys.find((key) => state.openKeys.indexOf(key) === -1);
    setState({
      openKeys: latestOpenKey ? [latestOpenKey] : [],
    });
  };

  useEffect(() => {
    if (!state.dealMenus?.length || !selectedKeys?.length) return;
    getOpenKeys();
  }, [state.dealMenus, selectedKeys]);
  // const { socket } = useWebSocket();
  return shouldRenderMenu ? (
    <div className="layout-menu-wrap" style={{ height: '100vh', display: 'flex' }}>
      <Sider
        width={200}
        className="leftSideNav"
        collapsedWidth={50}
        trigger={null}
        collapsed={collapsedData?.collapsed}
      >
        {/* 控制logo显示 */}
        <div className="w-full flex justify-center mb-5 siderImgBox">
          <img
            className="siderBoxImg"
            style={!collapsedData?.collapsed ? { marginRight: '10px' } : {}}
            src={logo}
            alt=""
          />
          {!collapsedData?.collapsed && <img className="siderBoxImgTwo" src={zbPng} alt="" />}
        </div>
        {/* 菜单 */}
        <Menu
          inlineIndent={12}
          onSelect={selectDebounce}
          style={{ flex: 1, overflowY: 'auto', overflowX: 'hidden' }}
          selectedKeys={selectedKeys}
          mode="inline"
          // onClick={selectDebounce}
          {...defaultProps}
          onOpenChange={onOpenChange}
        >
          <Menu.Item
            key="friendSeek/chatMain"
            onClick={() => {
              history.push('/friendSeek/chatMain');
            }}
            className="noChildMenu"
          >
            <img
              src={
                location.pathname === '/friendSeek/chatMain'
                  ? MenuActiveIcon['BeFriendsAI']
                  : MenuIcon['BeFriendsAI']
              }
            />
            BeFriendsAI
          </Menu.Item>
          {renderSubMenu}
        </Menu>
      </Sider>
    </div>
  ) : null;
};

export default connect(({ breadcrumb }: { breadcrumb: { breadcrumbList: IRoute[] } }) => {
  const location = window.location.pathname;
  let selectedKeys = breadcrumb?.breadcrumbList?.map((item: IRoute) => item?.path) || [];

  // 如果是 BeFriends 页面，添加对应的 key 到 selectedKeys
  if (location === '/friendSeek/chatMain') {
    selectedKeys = ['friendSeek/chatMain'];
  }

  return {
    selectedKeys,
  };
})(LayoutMenu);
