import { Layout } from 'antd';
import { routeWithSubRoutes } from 'qmkit';
import React from 'react';
import { getUrlParam } from 'web-common-modules/utils';
import { routes } from '@/router';
import { freshToken } from '@/utils/login';
// import Header from './header';
import Menu from './menu';
import HeaderTag from './headerTag';
import { Watermark } from 'web-common-modules/components';
import Aside, { configItem } from 'web-common-modules/components/Aside';
import Version from 'web-common-modules/version';
import { history } from 'qmkit';
import { Provider } from 'react-keep-alive';
import { getQueryParams } from '../anchor-information/utils/utils';
import { ChangeDetectionPolling } from '@/common/constants/freshToken';

const { Content } = Layout;
const AsideConfig: configItem[] = [];

export default class Main extends React.Component<{
  location: {
    pathname: string;
  };
}> {
  private polling: ChangeDetectionPolling | null = null;

  constructor(props: any) {
    super(props);
    this.state = {
      matchedPath: '',
      pageName: '',
    };
  }
  componentDidMount() {
    // sso
    const hrefString = location.href;
    if (hrefString?.indexOf('befriends/auth') && location.href?.indexOf('befriends/auth') > -1) {
      const index = location.href?.indexOf('befriends/auth');
      const queryString = hrefString.substring(index + '/auth'.length + 1);
      const queryPart = queryString.split('?')[1];
      const params: { [key: string]: string } = {};
      queryPart.split('&').forEach(function (pair) {
        const [key, value] = pair.split('=');
        params[key] = value;
      });
      const code = params?.code;
      const state = params?.state;
      console.log('ssoLogin', code);
      history.push('/auth?code=' + code + '&state=' + state);
      return;
    }

    // token登录
    if (!localStorage.getItem('jgpy-crm@loginInfo')) {
      if (window.location.pathname === '/') {
        history.push('/login');
      }
    } else {
      const loginInfo = JSON.parse(localStorage.getItem('jgpy-crm@loginInfo'));
      if (loginInfo?.isSupplierLogin) {
        window.localStorage.clear();
        history.push('/login');
      }
    }
    const loginToken = getUrlParam('token');
    if (loginToken) {
      this.loginByToken(loginToken);
      return;
    }
    // 版本检测
    Version.check();
    // 权限版本更新
    // 创建轮询实例
    this.polling = new ChangeDetectionPolling({
      interval: 60000, // 60秒轮询一次
      onError: (error) => {
        console.error('权限检测轮询出错:', error);
        // 可以在这里添加错误处理逻辑
        // message.error('权限检测失败');
      },
    });

    // 启动轮询
    this.polling.start();
  }

  componentWillUnmount() {
    // 组件卸载时清理轮询
    if (this.polling) {
      this.polling.stop();
      this.polling.destroy();
      this.polling = null;
    }
  }

  handlePathMatched = (path: any) => {
    this.setState({
      matchedPath: path,
    });
  };
  loginByToken = (loginToken: string) => {
    freshToken(loginToken);
  };

  render() {
    return (
      <div className="web-crm-jgpy">
        <Layout style={{ height: '100vh' }}>
          {/*头部-旧*/}
          {/* <Header /> */}
          <Layout className="flex flex-row">
            {/* 菜单 */}
            <Menu pathname={this.props.location.pathname} />

            {/*右侧主操作区域*/}
            <Content className="flex-1" style={{ overflow: 'auto', backgroundColor: '#F6F7F9' }}>
              <div
                id="page-content"
                className="flex-1"
                style={{ height: '100%', overflow: 'hidden' }}
              >
                {/* 头部tag区 */}
                <HeaderTag />

                {/* <div style={{ position: 'relative' }}> */}
                <div style={{ height: 'calc(100vh - 46px)', overflow: 'hidden' }}>
                  <Provider>{routeWithSubRoutes(routes, this.handlePathMatched)}</Provider>
                  {/* <div id='finance-web'>132444</div> */}
                </div>
                <Watermark opacity={0.06} />
                {/* </div> */}
                <div className="copyright text-center">
                  {' '}
                  {/* © 2022 杭州尽微供应链信息服务有限公司 版本号： {COPY_VERSION[Const.SYS_TYPE]}{' '} */}
                </div>
              </div>
            </Content>
          </Layout>
          <Aside config={AsideConfig} />
        </Layout>
      </div>
    );
  }
}
