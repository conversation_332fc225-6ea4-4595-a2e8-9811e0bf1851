// /* 过滤三级菜单 */

import { MenuResultItem } from '@/services/user';
import { allRoutePath } from '@/router';
import { cache } from 'qmkit';
import { isEmpty } from '@/utils/string';
import zjgl from '@/assets/zjgl.png';
import azjgl from '@/assets/azjgl.png';
import indexPng from '@/assets/index.png';
import indexActivePng from '@/assets/index-active.png';
import merchantPng from '@/assets/merchant.png';
import merchantActivePng from '@/assets/merchant-active.png';
import basisPng from '@/assets/basis-file.png';
import basisActivePng from '@/assets/basis-file-active.png';
import studioPng from '@/assets/studio.png';
import studioActivePng from '@/assets/studio-active.png';
import goodsPng from '@/assets/goods.png';
import goodsActivePng from '@/assets/goods-active.png';
import legalPng from '@/assets/legal.png';
import legalActivePng from '@/assets/legal-active.png';
import settingPng from '@/assets/setting.png';
import reportIcon from '@/assets/reportPng.png';
import userIcon666 from '@/assets/userIcon1.png';
import settingActivePng from '@/assets/setting-active.png';
import investmentPng from '@/assets/investment.png';
import investmentActivePng from '@/assets/investment-active.png';
import selectionPng from '@/assets/selection.png';
import selectionActivePng from '@/assets/selection-active.png';
import userIcon from '@/assets/user-icon.png';
import anthorIcon from '@/assets/anthor-icon.png';
import anthorIconActive from '@/assets/anthor-icon-active.png';
import marketingPng from '@/assets/marketingPng.png';
import marketingPngActive from '@/assets/marketingPngActive.png';
import warehousePng from '@/assets/warehouse_icon.png';
import warehouseActivePng from '@/assets/warehouse_icon_active.png';
import aiWhite from '@/assets/aiWhit.png';
import aiBlack from '@/assets/aiBlack.png';
import companyF from '@/assets/companyF.png';
import companyH from '@/assets/companyH.png';

interface sessionTagType {
  pathName: string;
  isActive: boolean;
  path: string;
}

enum GradeEnum {
  ONE = 1,
  TWO = 2,
  THREE = 3,
  // 功能权限
  FUNC = 888,
}

// 业务类型
enum BizTypeEnum {
  // 定向达人菜单，机构端这个菜单隐藏 pd：@王超然
  TARGETED_GOODS_TALENT = 'TARGETED_GOODS_TALENT',
  // 尽微机构不展示快手团长活动
  KUAISHOU_CAPTAIN_ACTIVITY = 'KUAISHOU_CAPTAIN_ACTIVITY',
}

const FILTER_MENU_CONFIG: Array<{
  grade: GradeEnum;
  unionCode?: string;
  url?: string;
  authNm?: string;
  signal: BizTypeEnum;
}> = [
  {
    grade: 3,
    url: '/choice/targeted-goods-talent/list',
    signal: BizTypeEnum.TARGETED_GOODS_TALENT,
  },
  {
    grade: 3,
    url: '/ks-business/ks-captain-activity/list',
    signal: BizTypeEnum.KUAISHOU_CAPTAIN_ACTIVITY,
  },
];

// 所有的机构都不展示
const NOT_SHOW_ALL_ENUM: BizTypeEnum[] = [BizTypeEnum.TARGETED_GOODS_TALENT];

const filterMenus = (allMenus: MenuResultItem[], signalArr?: BizTypeEnum[]): MenuResultItem[] => {
  if (!signalArr) {
    return allMenus;
  }

  const dealSignalArr = [...NOT_SHOW_ALL_ENUM, ...signalArr];

  const needFilterConfigArr = FILTER_MENU_CONFIG.filter((item) =>
    dealSignalArr?.includes(item?.signal),
  );

  // // 二级用 unionCode 过滤
  // const filterLevel2 = needFilterConfigArr
  //   ?.filter((item) => item?.grade === GradeEnum.TWO)
  //   .map((item) => item?.unionCode);

  // 三级用 url 过滤
  const filterLevel3 = needFilterConfigArr
    ?.filter((item) => item?.grade === GradeEnum.THREE)
    .map((item) => item?.url);

  return allMenus?.filter((item) => {
    const { grade, unionCode = '', url = '' } = item;

    // if (grade === GradeEnum.TWO) {
    //   return !filterLevel2?.includes(unionCode);
    // }
    if (grade === GradeEnum.THREE) {
      return !filterLevel3.includes(url);
    }

    return item;
  });
};

// 获取当前menu下的所有url, 用于判断当前menu的url是否有效
const getCurrentMenuUrl = (menu: any) => {
  return (menu.children || []).reduce((urlList: any, children: any) => {
    const hasPage = children.url ? allRoutePath.includes(children.url) : false;
    return urlList.concat(hasPage ? children.url : [], getCurrentMenuUrl(children));
  }, []);
};

// 处理url, 如果没有权限，则重置url
// 处理菜单，没有页面时，不展示菜单
const handleMenu = (menuList: any[]) => {
  return (menuList || []).filter((menu, index) => {
    if (!menu.url && !menu.children) {
      return false;
    }

    // 处理当前menu的url
    const childrenUrlList = getCurrentMenuUrl(menu);
    if (childrenUrlList.length) {
      menu.url = childrenUrlList.includes(menu.url) ? menu.url : '';
    }

    // 非叶子节点，如果存在children，但children都没有权限，则删除当前菜单
    if (menu.children && !childrenUrlList.length) {
      return false;
    }

    // 叶子节点，url不在路由里面时，删除当前菜单
    if (menu.url && !menu.children && !allRoutePath.includes(menu.url)) {
      return false;
    }

    // 处理children
    if (menu.children) {
      menu.children = handleMenu(menu.children);
    }

    return true;
  });
};

const checkFirstMenuAuth = (url: string, name = '') => {
  if (isEmpty(localStorage.getItem(cache.LOGIN_MENUS))) return false;
  const menuList = JSON.parse(localStorage.getItem(cache.LOGIN_MENUS) as string);
  const hasMenu =
    menuList?.filter((firstMenu: any) => firstMenu?.url === url || firstMenu.title === name)
      ?.length > 0;
  return hasMenu;
};

const MenuIcon: any = {
  首页: indexPng,
  商家CRM: merchantPng,
  合同管理: goodsPng,
  基础档案: basisPng,
  直播间管理: studioPng,
  直播间运营: studioPng,
  营销管理: marketingPng,
  商品中心: goodsPng,
  系统配置: settingPng,
  报表中心: reportIcon,
  客服中心: userIcon666,
  用户权限: userIcon,
  法务审核: legalPng,
  质检管理: zjgl,
  招商与供品: investmentPng,
  招商管理: investmentPng,
  选品管理: selectionPng,
  主播管理: anthorIcon,
  财务结算管理: selectionPng,
  // 营销管理: goodsPng,
  资质审核管理: legalPng,
  微博服务: merchantPng,
  库房管理: warehousePng,
  BeFriendsAI: aiBlack,
  公司管理: companyH,
};

const MenuActiveIcon: any = {
  首页: indexActivePng,
  商家CRM: merchantActivePng,
  合同管理: goodsActivePng,
  基础档案: basisActivePng,
  直播间管理: studioActivePng,
  直播间运营: studioActivePng,
  营销管理: marketingPngActive,
  商品中心: goodsActivePng,
  系统配置: settingActivePng,
  报表中心: reportIcon,
  客服中心: userIcon666,
  用户权限: userIcon,
  法务审核: legalActivePng,
  质检管理: azjgl,
  招商与供品: investmentActivePng,
  招商管理: investmentActivePng,
  选品管理: selectionActivePng,
  主播管理: anthorIconActive,
  财务结算管理: selectionActivePng,
  // 营销管理: goodsPng,
  资质审核管理: legalActivePng,
  微博服务: merchantActivePng,
  库房管理: warehouseActivePng,
  BeFriendsAI: aiWhite,
  公司管理: companyF,
};

export {
  filterMenus,
  BizTypeEnum,
  handleMenu,
  checkFirstMenuAuth,
  MenuIcon,
  MenuActiveIcon,
  sessionTagType,
};
