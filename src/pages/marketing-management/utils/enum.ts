export enum BUSINESS_TYPE_ENUM {
  FIELD_COST = 'FIELD_COST',
  PUBLICITY_EXPENSES = 'PUBLICITY_EXPENSES',
  TRAFFIC_DELIVERY = 'TRAFFIC_DELIVERY',
  LUCKY_DRAW_AND_REWARD = 'LUCKY_DRAW_AND_REWARD',
  ARTIST_COST = 'ARTIST_COST',
  // ARTIST_SHARING = 'ARTIST_SHARING',
  GOODS_SUBSIDY = 'GOODS_SUBSIDY',
  SHORT_VIDEO_COST = 'SHORT_VIDEO_COST',
}
export enum BUSINESS_TYPE_MAPPING {
  FIELD_COST = '现场费用',
  PUBLICITY_EXPENSES = '宣传费用',
  TRAFFIC_DELIVERY = '流量投放',
  LUCKY_DRAW_AND_REWARD = '抽奖及打赏',
  ARTIST_COST = '艺人成本',
  // ARTIST_SHARING = 'ARTIST_SHARING',
  GOODS_SUBSIDY = '物品补贴',
  SHORT_VIDEO_COST = '短视频成本',
}

export const BUSINESS_TYPE: Record<BUSINESS_TYPE_ENUM, string> = {
  [BUSINESS_TYPE_ENUM.FIELD_COST]: 'charge_type_1',
  [BUSINESS_TYPE_ENUM.PUBLICITY_EXPENSES]: 'charge_type_2',
  [BUSINESS_TYPE_ENUM.TRAFFIC_DELIVERY]: 'charge_type_3',
  [BUSINESS_TYPE_ENUM.LUCKY_DRAW_AND_REWARD]: 'charge_type_4',
  [BUSINESS_TYPE_ENUM.ARTIST_COST]: 'charge_type_5',
  // [BUSINESS_TYPE_ENUM.ARTIST_SHARING]: 'charge_type_6',
  [BUSINESS_TYPE_ENUM.GOODS_SUBSIDY]: 'charge_type_7',
  [BUSINESS_TYPE_ENUM.SHORT_VIDEO_COST]: 'charge_type_8',
};

export const BUSINESS_TYPE_MAP: Record<typeof BUSINESS_TYPE[BUSINESS_TYPE_ENUM], string> = {
  [BUSINESS_TYPE.FIELD_COST]: '现场费用',
  [BUSINESS_TYPE.PUBLICITY_EXPENSES]: '宣传费用',
  [BUSINESS_TYPE.TRAFFIC_DELIVERY]: '流量投放',
  [BUSINESS_TYPE.LUCKY_DRAW_AND_REWARD]: '抽奖及打赏',
  [BUSINESS_TYPE.ARTIST_COST]: '艺人成本',
  // [BUSINESS_TYPE.ARTIST_SHARING]: '艺人分成',
  [BUSINESS_TYPE.GOODS_SUBSIDY]: '物品补贴',
  [BUSINESS_TYPE.SHORT_VIDEO_COST]: '短视频成本',
};

// 本期只上的枚举 和不做的枚举
export const BUSINESS_TYPE_LIST = [BUSINESS_TYPE['GOODS_SUBSIDY']];
// export const EXPENSECATEGORY_NO_CODE = ['ANCHOR_COUPON'];
export const BUSINESS_TYPE_SELECT_LIST = Object.entries(BUSINESS_TYPE_MAP).map((item) => ({
  key: item[0],
  label: item[1],
}));
// .filter((item) => BUSINESS_TYPE_LIST.includes(item.key));
// 费用中的枚举
export const BUSINESS_TYPE_EXPNSESELIST = ['GOODS_SUBSIDY'];

export enum DATA_TYPE {
  // RED_PACKET = '红包',
  // OTHER_RED_PACKET = '其他类型红包',
  // SHOP_WINDOW_RED_PACKET = '厨窗红包',
  WORRY_FREE_BAG = '无忧福袋',
  PICK_YOUR_OWN_BAG = '自采福袋',
  MERCHANT_PROVIDED = '商家提供',
}

export enum EXPENSE_ORDER_STATUS_ENUM {
  WAIT_AUDIT = '待审核',
  AUDIT_PASS = '财务已审核',
  AUDIT_REJECT = '审核驳回',
  RELATED_SETTLEMENT_ORDER = '已生成付款单',
}
export const EXPENSE_ORDER_STATUS_COLOR = {
  WAIT_AUDIT: 'orange',
  RELATED_SETTLEMENT_ORDER: 'blue',
  AUDIT_PASS: 'green',
  AUDIT_REJECT: 'red',
};

export const BUSINESS_TYPE_TAB_LIST = [
  {
    label: '现场费用',
    key: BUSINESS_TYPE_ENUM.FIELD_COST,
    func: 'f_marketing_expenses_field_cost',
  },
  {
    label: '宣传费用',
    key: BUSINESS_TYPE_ENUM.PUBLICITY_EXPENSES,
    func: 'f_marketing_expenses_publicity_expenses',
  },
  {
    label: '流量投放',
    key: BUSINESS_TYPE_ENUM.TRAFFIC_DELIVERY,
    func: 'f_marketing_expenses_traffic_delivery',
  },
  {
    label: '抽奖及打赏',
    key: BUSINESS_TYPE_ENUM.LUCKY_DRAW_AND_REWARD,
    func: 'f_marketing_expenses_lucky_draw_and_reward',
  },
  {
    label: '艺人成本',
    key: BUSINESS_TYPE_ENUM.ARTIST_COST,
    func: 'f_marketing_expenses_artist_cost',
  },
  {
    label: '物品补贴',
    key: BUSINESS_TYPE_ENUM.GOODS_SUBSIDY,
    func: 'f_marketing_expenses_goods_subsidy',
  },
  {
    label: '短视频成本',
    key: BUSINESS_TYPE_ENUM.SHORT_VIDEO_COST,
    func: 'f_marketing_expenses_short_video_cost',
  },
];

export const TAX_RATE_LIST = [0, 1, 3, 6, 9, 13];
