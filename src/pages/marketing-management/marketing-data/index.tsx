import { Button, Form, message, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { AuthWrapper } from 'qmkit';
import React, { useEffect, useMemo, useState } from 'react';
import PaginationProxy from '@/common/constants/Pagination';
import PageLayout from '@/components/PageLayout/index';
import styles from './index.module.less';
import { useTableHeight } from '@/common/constants/hooks/index';
import SearchForm from './components/SearchForm';
import { useTable } from './utils/getColumns';
import { MarketingDatapageListInfoType, SearchType, useList } from './utils/hook';
import { history } from 'qmkit';
import { ExportModal } from 'web-common-modules/biz';
import { ButtonProxy } from 'web-common-modules/components';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  marketingDataDel,
  MarketingDataDelRequest,
  marketingDataExport,
  MarketingDatapageListResult,
  marketingDataSubmit,
  MarketingDataSubmitRequest,
} from './services';
import { PaginationConfig, SorterResult, TableRowSelection } from 'antd/lib/table';
import { useLocation } from 'react-router-dom';
import SortColumnTable from '@/components/SortColumnTable';
import { SortBizTypeEnum } from '@/components/SortColumnTable/type';
import CorrectionData from './components/CorrectionData';
import BatchSubmit from './components/BatchSubmit';
import ImportData from './components/ImportData';
const AnchorInformation: React.FC<FormComponentProps<SearchType>> = ({ form }) => {
  const { list, loading, getList, pagination, condition, setLoading } = useList(form);
  const [selectedKeys, setSelectKeys] = useState<string[]>([]);
  const [selectedRows, setSelectRows] = useState<MarketingDatapageListResult['records']>([]);

  const onReset = () => {
    form.resetFields();
    setSelectKeys([]);
    getList({
      current: 1,
      size: 20,
    });
  };
  const onRefresh = () => {
    getList({});
    setSelectKeys([]);
  };
  useEffect(() => {
    onRefresh();
  }, []);
  const { getHeight, tableHeight } = useTableHeight(80);

  const handleGoAdd = () => {
    history.push(`/marketing-data-create?type=create`);
  };
  const handleGoEdit = (id?: string) => {
    history.push(`/marketing-data-edit?type=edit&id=${id}`);
  };
  const handleGoDetail = (params: { id?: string; recordCode?: string }) => {
    const { id, recordCode } = params;
    history.push(`/marketing-data-detail?recordCode=${recordCode}&id=${id}`);
  };
  const handleSubmit = async (params: MarketingDataSubmitRequest, isSingle?: boolean) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: marketingDataSubmit,
      params,
    });
    setLoading(false);
    setSelectKeys([]);
    if (isSingle) {
      if (result?.errorList?.length) {
        message.error(result?.errorList?.[0]?.errorReason ?? '提交失败');
      } else {
        message.success('提交成功');
        onRefresh();
      }
    }
    return result;
    // if (result) {
    //   message.success('提交成功');
    //   getList({});
    // }
  };
  const handleEdl = async (params: MarketingDataDelRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: marketingDataDel,
      params,
    });
    setLoading(false);
    if (result) {
      message.success('删除成功');
      onRefresh();
    }
  };
  const rowSelection: TableRowSelection<MarketingDatapageListInfoType> = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRowKeys);
      setSelectRows(selectedRows);
      setSelectKeys(selectedRowKeys as string[]);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
  };
  const handleTableChange = (
    pagination: PaginationConfig,
    filters: Partial<Record<keyof any, string[]>>,
    sorter: SorterResult<any>,
  ) => {
    const sortMap = {
      ascend: 'asc',
      descend: 'desc',
    };
    console.log(sorter, filters);
    // sortOrder?:string/*排序：类型：asc=升序，desc=降序*/,
    getList(
      {
        sortField: sorter.field,
        sortOrder: sorter?.order ? sortMap[sorter.order] : undefined,
      },
      true,
    );
    setSelectKeys([]);
  };
  const location = useLocation();
  useEffect(() => {
    if (location?.pathname === '/marketing-data') {
      onRefresh();
    }
  }, [location?.pathname]);
  const { columns } = useTable({
    goEdit: handleGoEdit,
    goDetail: handleGoDetail,
    onSubmit: handleSubmit,
    onDel: handleEdl,
    pagination,
  });
  return (
    <PageLayout className={styles['cooperation-report-contain']} routePath="/marketing-data">
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            form={form}
            loading={loading}
            onSearch={() => {
              getList({ current: 1 });
              setSelectKeys([]);
            }}
            onReset={onReset}
          />

          <div className="flex items-center mb-16">
            <AuthWrapper functionName="f_marketing_data_create">
              <Button type="primary" icon="plus" onClick={handleGoAdd}>
                新建
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_marketing_data_bacth_submit">
              <BatchSubmit
                onRefresh={onRefresh}
                batchSubmit={async () => {
                  if (selectedKeys.length < 1) {
                    message.warning('请选择要提交的数据');
                    return null;
                  }
                  if (selectedKeys.length > 100) {
                    message.warning('最多操作100条数据');
                    return null;
                  }
                  const res = await handleSubmit({
                    idList: selectedKeys,
                  });
                  return res;
                }}
              ></BatchSubmit>
            </AuthWrapper>
            <ImportData onRefresh={() => onRefresh()} />
            <CorrectionData
              selectedKeys={selectedKeys}
              selectedRows={selectedRows}
              clearSelect={() => {
                setSelectKeys([]);
                setSelectRows([]);
              }}
              onRefresh={onRefresh}
            />
            <AuthWrapper functionName="f_marketing_data_export">
              <ExportModal
                condition={condition}
                requestFunc={marketingDataExport}
                content={
                  <div>
                    <p>
                      <span>导出完成后可在</span>
                      <a
                        href="javascript:;"
                        onClick={() => {
                          //
                        }}
                      >
                        导出记录
                      </a>
                      <span>中查看并下载。</span>
                    </p>
                  </div>
                }
                onOk={() => null}
              >
                <ButtonProxy className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  <span>导出</span>
                </ButtonProxy>
              </ExportModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_marketing_data_export_log">
              <ButtonProxy
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={() => {
                  history.push(`/export-list-marketing-data-records?configCode=MARKETING_DATA`);
                }}
              >
                <span>导出记录</span>
              </ButtonProxy>
            </AuthWrapper>
          </div>
        </div>
        <div className={styles.boardTable} style={{ flex: 1 }}>
          <AuthWrapper functionName="f_marketing_data_page_list">
            <SortColumnTable
              disSortColumns={['index']}
              disabledColumns={[
                'sortColumn',
                'creatorName',
                'gmtCreated',
                'modifierName',
                'gmtModified',
                'action',
              ]}
              rowKey="id"
              loading={loading}
              columns={columns}
              dataSource={list}
              pagination={false}
              scroll={{ y: tableHeight, x: '100%' }}
              rowSelection={rowSelection}
              onChange={handleTableChange}
              bizType={SortBizTypeEnum.MARKETING_DATA}
              setLoading={setLoading}
            />
            {/* <Table
             
            /> */}
          </AuthWrapper>
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <AuthWrapper functionName="f_marketing_data_page_list">
            <PaginationProxy
              current={pagination?.current}
              pageSize={pagination?.size}
              total={pagination?.total}
              // @ts-ignore
              onChange={(current, size) => {
                setSelectKeys([]);
                getList({
                  current,
                  size,
                });
              }}
              valueType="flatten"
              pageSizeOptions={['5', '10', '20', '50', '100', '200']}
            />
          </AuthWrapper>
        </div>
      </div>
    </PageLayout>
  );
};

export default Form.create()(AnchorInformation);
