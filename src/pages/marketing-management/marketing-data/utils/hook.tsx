import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import React, { useEffect, useMemo, useState } from 'react';

import { debounce, get } from 'lodash';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import {
  marketingDatapageList,
  MarketingDatapageListRequest,
  MarketingDatapageListResult,
  marketingLogList,
  MarketingLogListRequest,
  MarketingLogListResult,
} from '../services';
import { Moment } from 'moment';
import {
  getCompanySimpleList,
  GetCompanySimpleListRequest,
  GetCompanySimpleListResult,
} from '@/pages/company-bank-account/services';
import { CreateFormType } from './type';
import {
  supplierPage,
  SupplierPageRequest,
  SupplierPageResult,
} from '@/pages/anchor-information/services';
import { getDetaultJgpyCompanyName, getDetaultSettlementEntityName } from './enum';
import { getDetaultExpensesSettlementEntityName } from '../../marketing-expenses/MarketingExpenses/types';
import { useSetState } from 'ahooks';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { TAX_RATE_LIST } from '../../utils/enum';
export type SearchType = {
  businessType?: string;
  expenseCategoryList?: Array<string> /*费用种类*/;
  liveDate?: Moment[];
  liveRoomIdList?: Array<string> /*直播间id*/;
  recordCodeList?: string;
  statusEnumList?: Array<
    'PROCESSING' | 'WAIT_COMMIT' | 'COMMITED'
  > /*状态[MarketingDataStatusEnum]*/;
};

export const useList = (form: WrappedFormUtils<SearchType>) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<MarketingDatapageListResult['records']>([]);
  const [pagination, setPagination] = useSetState({ current: 1, size: 20, total: 0 });
  const [condition, setCondition] = useState<MarketingDatapageListRequest>();

  const [sortMap, setSortMap] = useState<{ sortField?: string; sortOrder?: string }>({});

  const getList = async (data: MarketingDatapageListRequest, sort?: boolean) => {
    setLoading(true);
    const formValue = form?.getFieldsValue();
    const liveDate = (formValue as SearchType)?.liveDate;
    const sortObj = {
      sortField: sort ? data.sortField : sortMap?.sortField,
      sortOrder: sort ? data.sortOrder : sortMap?.sortOrder,
    };
    const params = {
      ...sortObj,
      ...data,
      ...(formValue as SearchType),
      liveDateStart: liveDate?.[0]?.format('YYYY-MM-DD'),
      liveDateEnd: liveDate?.[1]?.format('YYYY-MM-DD'),
      current: data?.current ?? pagination.current,
      size: data?.size ?? pagination.size,
      recordCodeList: (formValue?.recordCodeList as string)
        ?.split(' ')
        ?.filter((item) => !!item?.trim()),
    };
    delete params.liveDate;
    setCondition(params);
    const result = await responseWithResultAsync({
      request: marketingDatapageList,
      params,
    });
    setLoading(false);
    if (result) {
      setSortMap({
        sortField: params.sortField,
        sortOrder: params.sortOrder,
      });
      setList(result?.records ?? []);
    }
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };

  useEffect(() => {
    getList({});
  }, []);
  return {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
  };
};
export type MarketingDatapageListInfoType = NonNullable<
  MarketingDatapageListResult['records']
>[number];

export const useCompanySimpleList = (params: {
  form?: WrappedFormUtils<CreateFormType>;
  type?: 'edit' | 'create';
}) => {
  const { form, type } = params;
  const [list, setList] = useState<GetCompanySimpleListResult['records']>([]);
  const [loading, setLoading] = useState(false);
  const formValue = useMemo(
    () => form?.getFieldsValue() as CreateFormType,
    [form?.getFieldsValue()],
  );
  const getList = async (params: GetCompanySimpleListRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: getCompanySimpleList,
      params,
    });
    setLoading(false);
    setList(result?.records ?? []);
  };
  const handleSearch = debounce((name: string) => {
    getList({ name });
  }, 100);
  useEffect(() => {
    getList({});
  }, []);
  const handleGeItem = async (name: string) => {
    const result = await responseWithResultAsync({
      request: getCompanySimpleList,
      params: {
        ceTime: {
          startDate: null,
          endDate: null,
        },
        current: 1,
        size: 100,
        name,
      },
    });
    if (result?.records?.[0]) {
      return {
        label: result?.records?.[0]?.name,
        key: result?.records?.[0]?.id,
      };
    } else {
      return {
        label: name,
        key: undefined,
      };
    }
  };
  const expenseCategoryInfo = useMemo(
    () => form?.getFieldsValue()?.expenseCategoryInfo,
    [form?.getFieldsValue()?.expenseCategoryInfo],
  );

  const setJgpyCompanyInfo = async (companyName: string) => {
    if (list?.find((item) => item?.name === companyName)) {
      const item = list?.find((item) => item?.name === companyName);
      form?.setFieldsValue({
        jgpyCompanyInfo: {
          label: item?.name,
          key: item?.id,
        },
      });
    } else {
      const label = await handleGeItem(companyName);
      form?.setFieldsValue({
        jgpyCompanyInfo: label,
      });
    }
  };
  useEffect(() => {
    if (type === 'edit') return;
    const companyName = getDetaultJgpyCompanyName(formValue);
    if (companyName) {
      setJgpyCompanyInfo(companyName);
    }
  }, [formValue?.businessTypeInfo, formValue?.expenseCategoryInfo]);
  return {
    list,
    getList,
    handleSearch,
    loading,
  };
};

export const useSupplierList = (params: {
  form?: WrappedFormUtils<CreateFormType>;
  page?: 'data' | 'expenses';
  type?: 'edit' | 'create';
  oaAuditStatus?: 'PASS' | 'WAIT_SUBMIT' | 'ALREADY_SUBMIT' | 'REFUSED';
}) => {
  const { form, page, type, oaAuditStatus } = params;
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<SupplierPageResult['records']>([]);
  const { codeEnum: taxRateEnum } = useCode(CODE_ENUM.TAX_RATE, {
    able: true,
  }); // 税率枚举
  const formValue = useMemo(
    () => form?.getFieldsValue() as CreateFormType,
    [form?.getFieldsValue()],
  );
  const getList = async (params: SupplierPageRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: supplierPage,
      params: {
        type: 'SUPPLIER',
        current: 1,
        size: 100,
        oaAuditStatus: oaAuditStatus ?? undefined,
        ...params,
      },
    });
    setList(result?.records ?? []);
    setLoading(false);
  };
  useEffect(() => {
    getList({ size: 100 });
  }, []);
  const handleSearch = debounce((supplierName: string) => {
    getList({ supplierName, size: 100 });
  }, 300);
  const handleGeItem = async (supplierName: string) => {
    const result = await responseWithResultAsync({
      request: supplierPage,
      params: {
        ceTime: {
          startDate: null,
          endDate: null,
        },
        current: 1,
        size: 100,
        supplierName,
      },
    });
    if (result?.records?.[0]) {
      return {
        label: result?.records?.[0]?.supplierName,
        key: result?.records?.[0]?.id,
      };
    } else {
      return {
        label: supplierName,
        key: undefined,
      };
    }
  };

  const setSettlementEntityInfo = async (supplierName: string) => {
    if (list?.find((item) => item?.supplierName === supplierName)) {
      const item = list?.find((item) => item?.supplierName === supplierName);
      form?.setFieldsValue({
        settlementEntityInfo: {
          label: item?.supplierName,
          key: item?.id,
        },
      });
    } else {
      const label = await handleGeItem(supplierName);
      form?.setFieldsValue({
        settlementEntityInfo: label,
      });
    }
  };
  useEffect(() => {
    if (type === 'edit') return;

    const supplierName =
      page === 'expenses'
        ? getDetaultExpensesSettlementEntityName(formValue)
        : getDetaultSettlementEntityName(formValue);
    if (supplierName) {
      setSettlementEntityInfo(supplierName);
    }
  }, [
    (formValue as any)?.businessType,
    (formValue as any)?.expenseCategory,
    formValue?.businessTypeInfo,
    formValue?.expenseCategoryInfo,
  ]);
  useEffect(() => {
    if (formValue?.settlementEntityInfo) {
      const taxRate = list?.find(
        (item) => item?.id === formValue?.settlementEntityInfo?.key,
      )?.taxRate;
      const taxRateText = taxRateEnum?.[taxRate as string];
      const taxRateNumber = parseFloat(taxRateText?.replace('%', ''));
      if (!isNaN(taxRateNumber) && TAX_RATE_LIST.includes(taxRateNumber)) {
        // 设置到BusinessForm的税率字段
        form?.setFieldsValue({ taxRate: taxRateNumber });
      } else {
        form?.setFieldsValue({ taxRate: 0 });
      }
    }
  }, [formValue?.settlementEntityInfo]);
  return {
    list,
    loading,
    getList,
    handleSearch,
  };
};
// 1: 营销数据操作日志 2：营销费用操作日志
export const useLogList = (type: 1 | 2) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<MarketingLogListResult['records']>([]);
  const [pagination, setPagination] = useState({ current: 1, size: 5, total: 0 });

  const getList = async (data: MarketingLogListRequest) => {
    setLoading(true);

    const params = {
      ...data,
      type,
      current: data?.current ?? pagination.current,
      size: data?.size ?? pagination.size,
    };
    const result = await responseWithResultAsync({
      request: marketingLogList,
      params,
    });
    setLoading(false);
    if (result) {
      setList(result?.records ?? []);
    }
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };
  // useEffect(() => {
  //   getList({});
  // }, []);
  return {
    list,
    loading,
    getList,
    pagination,
  };
};
export type LogListInfoType = NonNullable<MarketingLogListResult['records']>[number];
