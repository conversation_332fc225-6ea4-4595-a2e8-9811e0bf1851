import { Input, InputNumber, Select } from 'antd';
import Form, { WrappedFormUtils } from 'antd/lib/form/Form';
import React, { useEffect, useMemo } from 'react';
import styles from './index.module.less';
import { CreateFormType } from '../utils/type';
import { BUSINESS_TYPE, DATA_TYPE, TAX_RATE_LIST } from '../../utils/enum';
import { detaultTax, getTotalAmount, isEditTotalAmount } from '../utils/enum';
import { formatFee } from '../utils/getColumns';
import { rebateCompute, RebateComputeRequest } from '../services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

const { Item } = Form;
const { Option } = Select;
type PropsType = {
  form: WrappedFormUtils<CreateFormType>;
  type: 'create' | 'edit';
  recordCode?: string;
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 12,
  },
};
const formItemStyle: React.CSSProperties = {
  width: '200px',
};
const formStyle: React.CSSProperties = {
  width: '25%',
};

const BusinessForm: React.FC<PropsType> = ({ form, type, recordCode }) => {
  const { getFieldDecorator } = form;
  const formValue: CreateFormType = useMemo(() => form?.getFieldsValue(), [form?.getFieldsValue()]);
  const totalAmountEdit = useMemo(() => isEditTotalAmount(formValue), [formValue]);
  const requiredExpenseCategoryList = ['福利品', 'GMV品', '严选红包'];
  const priceGoodsIdRequired = useMemo(
    () =>
      formValue?.businessTypeInfo?.key === BUSINESS_TYPE.GOODS_SUBSIDY &&
      !!formValue?.expenseCategoryInfo?.label &&
      !!requiredExpenseCategoryList.includes(formValue?.expenseCategoryInfo?.label),
    [formValue],
  );

  useEffect(() => {
    if (formValue?.expenseCategoryInfo?.label?.includes('抖币')) return;
    if (!isEditTotalAmount(form?.getFieldsValue())) {
      form?.setFieldsValue({
        totalAmount: getTotalAmount(formValue),
      });
    }
  }, [
    formValue?.businessTypeInfo,
    formValue?.expenseCategoryInfo,
    formValue?.num,
    formValue?.price,
    formValue?.totalConsume,
    formValue?.grantAmount,
    formValue?.refundAmount,
    formValue?.eliminationAllocation,
  ]);
  useEffect(() => {
    if (formValue?.expenseCategoryInfo?.key === 'DOUJIA') {
      console.log(formValue?.totalAmount, formValue?.num);
      form?.setFieldsValue({
        price: Number(formValue?.totalAmount ?? 0) / Number(formValue?.num ?? 0),
      });
    }
  }, [formValue?.totalAmount]);
  useEffect(() => {
    if (!isEditTotalAmount(form?.getFieldsValue())) {
      form?.setFieldsValue({
        totalAmount: getTotalAmount(formValue),
      });
    }
    if (formValue?.expenseCategoryInfo?.key === 'DOU_CURRENCY') {
      form?.setFieldsValue({
        price: getTotalAmount(formValue),
      });
    }
  }, [
    formValue?.businessTypeInfo,
    formValue?.expenseCategoryInfo,
    formValue?.num,
    formValue?.totalConsume,
    formValue?.grantAmount,
    formValue?.refundAmount,
    formValue?.eliminationAllocation,
    formValue?.rebateAmount,
  ]);
  const TRAFFIC_DELIVERY_DISABLED = useMemo(
    () => !(formValue?.businessTypeInfo?.key === BUSINESS_TYPE.TRAFFIC_DELIVERY),
    [formValue?.businessTypeInfo],
  );

  const DB_DISABLED = useMemo(
    () =>
      (formValue?.businessTypeInfo?.label === '现场费用' &&
        formValue?.expenseCategoryInfo?.label?.includes('抖币')) ||
      formValue?.expenseCategoryInfo?.label?.includes('抖加') ||
      formValue?.expenseCategoryInfo?.label?.includes('随心推'),
    [formValue],
  );
  const FD_REQUIRED = useMemo(() => formValue?.expenseCategoryInfo?.label === '福袋', [formValue]);
  const goodsIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.value);
    const inputValue = e.target.value;
    // 只保留数字字符和可能的负号（如果允许负数）
    const numericValue = inputValue.replace(/[^\d]/g, '');
    console.log(numericValue);
    e.target.value = numericValue;
    // 如果需要限制小数点后的位数，可以进一步处理
    form?.setFieldsValue({
      goodsId: numericValue,
    });
  };
  const IS_TRAFFIC_DELIVERY = useMemo(
    () => formValue?.businessTypeInfo?.key === BUSINESS_TYPE.TRAFFIC_DELIVERY,
    [formValue?.businessTypeInfo],
  );
  const getRebateAmount = async (params: RebateComputeRequest) => {
    const result = await responseWithResultAsync({
      request: rebateCompute,
      params,
    });
    if (result) {
      form?.setFieldsValue({
        rebateAmount: result?.rebateAmount,
        totalAmount: result?.totalAmount,
      });
    }
  };

  const isNaNZero = (value?: number | string) => {
    return isNaN(Number(value)) || value === 'NaN' ? 0 : value;
  };
  useEffect(() => {
    if (
      ['SUI_XIN_TUI', 'LIVE_STREAM'].includes(formValue?.expenseCategoryInfo?.key as string) &&
      formValue?.settlementEntityInfo?.label === '海南优矩科技有限公司' &&
      formValue?.liveRoomInfo?.key &&
      recordCode
    ) {
      getRebateAmount({
        businessType: formValue?.businessTypeInfo?.key,
        eliminationAllocation: isNaNZero(formValue?.eliminationAllocation)?.toString(),
        expenseCategory: formValue?.expenseCategoryInfo?.key,
        grantAmount: isNaNZero(formValue?.grantAmount)?.toString(),
        liveRoomId: formValue?.liveRoomInfo?.key,
        recordCode: recordCode,
        refundAmount: isNaNZero(formValue?.refundAmount)?.toString(),
        settlementEntityId: formValue?.settlementEntityInfo?.key,
        totalConsume: isNaNZero(formValue?.totalConsume)?.toString(),
      });
    }
  }, [
    formValue?.totalConsume,
    formValue?.grantAmount,
    formValue?.refundAmount,
    formValue?.eliminationAllocation,
    recordCode,
  ]);
  return (
    <Form {...formItemLayout} className={styles['form-box']}>
      <Item label="商品内容" style={formStyle}>
        {getFieldDecorator('goodsContent')(
          <Input placeholder="请输入" maxLength={200} style={formItemStyle} />,
        )}
      </Item>
      <Item label="采购/补贴单价" style={formStyle}>
        {getFieldDecorator('price', {
          initialValue: 0,
          rules: [
            {
              required: priceGoodsIdRequired || FD_REQUIRED,
              message: '请输入采购/补贴单价',
            },
          ],
        })(
          <InputNumber
            formatter={(value) => `${value}元`}
            placeholder="请输入"
            precision={2}
            step={0.01}
            style={formItemStyle}
            min={0}
            // maxLength={8}
            disabled={DB_DISABLED}
          />,
        )}
      </Item>
      <Item label="数量" style={formStyle}>
        {getFieldDecorator('num', {
          ...(type === 'create'
            ? {
                initialValue: 1,
              }
            : {}),
          rules: [
            {
              required: FD_REQUIRED,
              message: '请输入数量',
            },
          ],
        })(
          <InputNumber
            placeholder="请输入"
            formatter={(value) => `${value ? parseInt(value?.toString()) : value}`}
            style={formItemStyle}
            min={type === 'create' ? 1 : 0}
            // maxLength={8}
            disabled={DB_DISABLED}
          />,
        )}
      </Item>
      <Item label="税率" style={formStyle}>
        {getFieldDecorator('taxRate', {
          initialValue: detaultTax({
            businessTypeInfo: formValue?.businessTypeInfo,
            expenseCategoryInfo: formValue?.expenseCategoryInfo,
          }),
          rules: [
            {
              required: true,
              message: '请选择税率',
            },
          ],
        })(
          <Select placeholder="请选择" allowClear style={formItemStyle}>
            {TAX_RATE_LIST.map((item) => ({ key: item, lable: item + '%' })).map((item) => (
              <Option value={item.key} key={item.key}>
                {item.lable}
              </Option>
            ))}
          </Select>,
        )}
      </Item>
      {!IS_TRAFFIC_DELIVERY && (
        <Item label="所属类型" style={formStyle}>
          {getFieldDecorator('dataType', {
            rules: [
              {
                required: FD_REQUIRED,
                message: '请选择所属类型',
              },
            ],
          })(
            <Select placeholder="请选择" allowClear style={formItemStyle} disabled={DB_DISABLED}>
              {Object.entries(DATA_TYPE).map((item) => (
                <Option value={item[0]} key={item[0]}>
                  {item[1]}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
      )}
      <Item label="汇总金额" style={formStyle}>
        {getFieldDecorator('totalAmount', {
          initialValue: 0,
          rules: [
            {
              required: true,
              message: '请输入汇总金额',
            },
          ],
        })(
          <InputNumber
            disabled={!totalAmountEdit}
            formatter={(value) => `${value}元`}
            placeholder="请输入"
            precision={2}
            step={0.01}
            style={formItemStyle}
            // min={0}
            // maxLength={8}
          />,
        )}
      </Item>
      {!IS_TRAFFIC_DELIVERY && (
        <Item label="商品ID" style={formStyle}>
          {getFieldDecorator('goodsId', {
            rules: [
              {
                required: priceGoodsIdRequired,
                message: '请输入商品ID',
              },
            ],
          })(
            <Input
              style={formItemStyle}
              placeholder="请输入"
              maxLength={64}
              disabled={type === 'edit'}
              onChange={goodsIdChange}
            />,
          )}
        </Item>
      )}
      {!IS_TRAFFIC_DELIVERY && (
        <Item label="店铺名称" style={formStyle}>
          {getFieldDecorator('storeName')(
            <Input
              style={formItemStyle}
              placeholder="请输入"
              maxLength={200}
              disabled={type === 'edit'}
            />,
          )}
        </Item>
      )}
      <Item label="总消耗" style={formStyle}>
        {getFieldDecorator('totalConsume', {
          initialValue: 0,
        })(
          <InputNumber
            disabled={
              TRAFFIC_DELIVERY_DISABLED ||
              formValue?.expenseCategoryInfo?.key === 'DOUJIA' ||
              formValue?.expenseCategoryInfo?.key === 'SUI_XIN_TUI'
            }
            formatter={(value) => `${value}元`}
            placeholder="请输入"
            precision={2}
            step={0.01}
            style={formItemStyle}
            min={0}
            // maxLength={8}
          />,
        )}
      </Item>
      <Item label="赠款金额" style={formStyle}>
        {getFieldDecorator('grantAmount', {
          initialValue: 0,
        })(
          <InputNumber
            disabled={TRAFFIC_DELIVERY_DISABLED}
            formatter={(value) => `${value}元`}
            placeholder="请输入"
            precision={2}
            step={0.01}
            style={formItemStyle}
            // min={0}
            // maxLength={8}
          />,
        )}
      </Item>
      <Item label="消返金额" style={formStyle}>
        {getFieldDecorator('refundAmount', {
          initialValue: 0,
        })(
          <InputNumber
            disabled={TRAFFIC_DELIVERY_DISABLED}
            formatter={(value) => `${value}元`}
            placeholder="请输入"
            precision={2}
            step={0.01}
            style={formItemStyle}
            // min={0}
            // maxLength={8}
          />,
        )}
      </Item>
      <Item label="消返调配" style={formStyle}>
        {getFieldDecorator('eliminationAllocation', {
          initialValue: 0,
        })(
          <InputNumber
            disabled={TRAFFIC_DELIVERY_DISABLED}
            formatter={(value) => `${value}元`}
            placeholder="请输入"
            precision={2}
            step={0.01}
            style={formItemStyle}
            // min={0}
            // maxLength={8}
          />,
        )}
      </Item>
      <Item label="返点金额" style={formStyle}>
        {getFieldDecorator('rebateAmount', {
          initialValue: 0,
        })(
          <InputNumber
            disabled={TRAFFIC_DELIVERY_DISABLED}
            formatter={(value) => `${value}元`}
            placeholder="请输入"
            precision={2}
            step={0.01}
            style={formItemStyle}
            // min={0}
            // maxLength={8}
          />,
        )}
      </Item>
    </Form>
  );
};

export default BusinessForm;
