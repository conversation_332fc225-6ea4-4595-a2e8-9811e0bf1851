import { useSetState } from 'ahooks';
import { Button, Icon, message, Spin } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useEffect, useMemo } from 'react';
import BasicForm from './BasicForm';
import DetailedList from './DetailedList';
import ContractForm from './BusinessForm';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  marketingDataAdd,
  marketingDataEdit,
  marketingDataDetail,
  MarketingDataAddRequest,
  MarketingDataEditRequest,
  MarketingDataDetailResult,
  MarketingDataSubmitRequest,
  marketingDataSubmit,
} from '../services';
import { CreateFormType } from '../utils/type';
import moment from 'moment';
import styles from './index.module.less';
import { history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import ActionLog from '../../utils/ActionLog';
import { WrappedFormUtils } from 'antd/lib/form/Form';

interface PropsType extends FormComponentProps<CreateFormType> {
  form: WrappedFormUtils<CreateFormType>;
}
type ModalState = {
  loading?: boolean;
  detail?: MarketingDataDetailResult;
};

const AddMarketingData: React.FC<PropsType> = ({ form }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const id = useMemo(() => getQueryParams().id, []);
  const type = useMemo(() => getQueryParams().type as 'edit' | 'create', []);
  const { delRoutetag } = useCloseAndJump();
  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };
  const handleDetail = async () => {
    setState((state) => ({ ...state, loading: true }));
    const detail = await responseWithResultAsync({
      request: marketingDataDetail,
      params: { id },
    });
    setState((state) => ({ ...state, loading: false }));
    if (detail) {
      initForm(detail);
      setState((state) => ({ ...state, detail }));
    }
  };
  const formatParams = (values: CreateFormType) => {
    const {
      jgpyCompanyInfo,
      liveDate,
      liveRoomInfo,
      settlementEntityInfo,
      businessTypeInfo,
      expenseCategoryInfo,
    } = values;
    console.log(values);
    const params = {
      ...values,
      expenseCategory: expenseCategoryInfo?.key,
      businessType: businessTypeInfo?.key,
      jgpyCompanyInfoId: jgpyCompanyInfo?.key,
      jgpyCompanyInfoName: jgpyCompanyInfo?.label,
      liveDate: moment(liveDate).format('YYYY-MM-DD'),
      liveRoomId: liveRoomInfo?.key,
      liveRoomName: liveRoomInfo?.label,
      settlementEntityId: settlementEntityInfo?.key,
      settlementEntityName: settlementEntityInfo?.label,
      totalAmount: isNaN(Number(values?.totalAmount))
        ? undefined
        : Number(values?.totalAmount)?.toFixed(2),
    };

    delete params.jgpyCompanyInfo;
    delete params.liveRoomInfo;
    delete params.settlementEntityInfo;
    delete params.businessTypeInfo;
    delete params.expenseCategoryInfo;
    return params as MarketingDataAddRequest;
  };
  const handleCreate = async (params: MarketingDataAddRequest) => {
    const result = await responseWithResultAsync({
      request: marketingDataAdd,
      params,
    });
    setState((state) => ({ ...state, loading: false }));
    return result;
  };
  const handleUpdate = async (params: MarketingDataEditRequest) => {
    const result = await responseWithResultAsync({
      request: marketingDataEdit,
      params: { ...params, id },
    });
    setState((state) => ({ ...state, loading: false }));
    return result;
  };
  const handleOk = (submit?: (listId?: string) => void) => {
    form.validateFields(async (err, values) => {
      if (err) return;
      setState((state) => ({ ...state, loading: true }));
      const params = formatParams(values);
      console.log(params);
      const result = type === 'create' ? await handleCreate(params) : await handleUpdate(params);
      if (result) {
        if (submit) {
          submit(type === 'create' ? (result as string) : id);
          return;
        }
        message.success('操作成功');
        handleCancel();
      }
    });
  };
  const createLabelKey = (obj: { label?: string | number; key?: string | number }) => {
    return obj.label || obj.key ? obj : undefined;
  };
  const initForm = async (info: MarketingDataDetailResult) => {
    form.setFieldsValue({
      businessTypeInfo: createLabelKey({ label: info?.businessTypeName, key: info?.businessType }),
      dataType: info?.dataType,
      eliminationAllocation: info?.eliminationAllocation,
      expenseCategoryInfo: createLabelKey({
        label: info?.expenseCategoryName,
        key: info?.expenseCategory,
      }),
      goodsContent: info?.goodsContent,
      goodsId: info?.goodsId,
      grantAmount: info?.grantAmount,
      jgpyCompanyInfo: createLabelKey({
        label: info?.jgpyCompanyInfoName,
        key: info?.jgpyCompanyInfoId,
      }),
      liveDate: info?.liveDate ? moment(info?.liveDate) : undefined,
      liveRoomInfo: createLabelKey({ label: info?.liveRoomName, key: info?.liveRoomId }),
      num: info?.num,
      price: info?.price,
      refundAmount: info?.refundAmount,
      settlementEntityInfo: createLabelKey({
        label: info?.settlementEntityName,
        key: info?.settlementEntityId,
      }),
      storeName: info?.storeName,
      taxRate: info?.taxRate,
      totalAmount: info?.totalAmount,
      totalConsume: info?.totalConsume,
      openId: info?.openId,
      deptId: info?.deptId,
      rebateAmount: info?.rebateAmount,
    });
    setTimeout(() => {
      form.setFieldsValue({
        taxRate: info?.taxRate ?? 0,
      });
    }, 1000);
  };
  const handleSubmit = async (listId?: string) => {
    // const result = await
    setState((state) => ({ ...state, loading: true }));
    const result = await responseWithResultAsync({
      request: marketingDataSubmit,
      params: { idList: [listId!] },
    });
    setState((state) => ({ ...state, loading: false }));
    if (result?.failCount) {
      message.error(result?.errorList?.map((item) => item?.errorReason).join(','));
      return;
    }
    if (result) {
      message.success('提交成功');
      handleCancel();
    }
  };
  const handleOkAndSubmit = async () => {
    handleOk(handleSubmit);
  };
  useEffect(() => {
    if (type === 'edit' && id) {
      handleDetail();
    }
  }, [type]);
  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="基本信息">
            <div className={styles.extra}>
              <BasicForm form={form} type={type} />
            </div>
          </Card>
          <Card title="业务信息">
            <div className={styles.extra}>
              <ContractForm form={form} type={type} recordCode={modalState?.detail?.recordCode} />
            </div>
          </Card>
          {type === 'edit' && (
            <Card title="明细表">
              <div className={styles.extra}>
                <DetailedList form={form} detail={modalState?.detail} />
              </div>
            </Card>
          )}
          <Card title="操作日志">
            <div className={styles.extra}>
              <section style={{ padding: '0 24px' }} className={styles['list-table']}>
                <ActionLog type={1} recordCode={modalState?.detail?.recordCode} />
              </section>
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          <Button type="primary" onClick={handleOkAndSubmit}>
            保存并提交
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleOk();
            }}
          >
            保存
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddMarketingData);
