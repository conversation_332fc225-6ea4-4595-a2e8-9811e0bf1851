import React, { useCallback, useEffect, useMemo, useState } from 'react';
import style from '@/styles/index.module.less';
import { useCode, useTableHeight } from '@/common/constants/hooks/index';
import { Button, Form, Icon, message, Radio, Table } from 'antd';
import { AuthWrapper, history } from 'qmkit';
import { MarketingExpensepageListInfoType, useList, useTable } from './hooks';
import PaginationProxy from '@/common/constants/Pagination';
import { BUSINESS_TYPE, BUSINESS_TYPE_ENUM, BUSINESS_TYPE_MAP } from '../../utils/enum';
import { FormComponentProps } from 'antd/lib/form';
import { SearchFormType } from './types';
import { PaginationConfig, SorterResult, SortOrder, TableRowSelection } from 'antd/lib/table';
import SearchForm from './components/SearchForm';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  batchPushCost,
  marketingExpenseExport,
  marketingExpenseNullify,
  MarketingExpenseNullifyRequest,
  MarketingExpensePageListResult,
  marketingExpensePushCost,
  MarketingExpensePushCostRequest,
} from './service';
import { ExportModal } from 'web-common-modules/biz';
import { ButtonProxy } from 'web-common-modules/components';
import Modal from 'antd/es/modal';
import { EXPORT_RECORD_ENUM } from '../utils/enum';
import { formatFee } from '@/pages/anchor-information/utils/getColumns';
import ImportBox from './components/ImportBox';
import BatchPush from './components/BatchPush';
import { useLocation } from 'react-router-dom';
const { confirm } = Modal;

interface PropsType extends FormComponentProps<SearchFormType> {
  tabKey: BUSINESS_TYPE_ENUM;
}

const MarketingExpenses: React.FC<PropsType> = ({ tabKey, form }) => {
  const { tableHeight } = useTableHeight(110);
  const [selectedKeys, setSelectKeys] = useState<MarketingExpensePageListResult['records']>([]);
  const { list, loading, getList, pagination, condition, setLoading } = useList(form, tabKey);
  const location = useLocation();
  const onRefresh = () => {
    getList({});
    setSelectKeys([]);
  };
  const handlePushCost = async (params: MarketingExpensePushCostRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: marketingExpensePushCost,
      params,
    });
    if (result) {
      onRefresh();
      message.success('推送成功');
    }
    setLoading(false);
  };
  const handleNullify = async (params: MarketingExpenseNullifyRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: marketingExpenseNullify,
      params,
    });
    if (result) {
      onRefresh();
      message.success('操作成功');
    }
    setLoading(false);
  };

  const rowSelection: TableRowSelection<MarketingExpensepageListInfoType> = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRowKeys);
      setSelectKeys(selectedRows);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys?.map((item) => item?.id) as string[],
  };
  const exportParms = useMemo(
    () => ({ ...condition, id: selectedKeys?.map((item) => item?.id) }),
    [condition, selectedKeys],
  );
  const onReset = () => {
    form.resetFields();
    setSelectKeys([]);
    getList(
      {
        current: 1,
        size: 20,
        sortField: undefined,
        sortOrder: undefined,
      },
      true,
    );
    setSortMap({});
    setSelectKeys([]);
  };

  const handleGoAdd = (dataSource: MarketingExpensePageListResult['records']) => {
    console.log(dataSource);
    if (dataSource?.find((item) => !item.isInitiate)) {
      message.warn('选中数据中存在不参与成本结算的主体，不支持发起付款');
      return;
    }
    if (dataSource && dataSource.length) {
      const allLen = dataSource?.length ?? 0;
      const okKeys: Array<string> = [];

      const settlementEntityNameArr: Array<string | undefined> = [];
      const jgpyCompanyInfoNameArr: Array<string | undefined> = [];
      const businessTypeNameArr: Array<string | undefined> = [];
      const expenseCategoryNameArr: Array<string | undefined> = [];
      const deptIdNameArr: Array<string | undefined> = [];
      const dataTypeNameArr: Array<string | undefined> = [];
      const isOrNameArr: Array<string | undefined> = [];

      dataSource?.forEach((i) => {
        console.log(i);
        settlementEntityNameArr.push(i?.settlementEntityName);
        jgpyCompanyInfoNameArr.push(i?.jgpyCompanyInfoName);
        businessTypeNameArr.push(i?.businessType);
        deptIdNameArr.push(i?.deptId);
        dataTypeNameArr.push(i.dataType);
        expenseCategoryNameArr.push(i?.expenseCategory);
        if (i.expenseCategory === 'DOU_CURRENCY') {
          isOrNameArr.push(i?.expenseCategory);
        }
        if (i?.costStatus === 'AUDIT_PASS') {
          okKeys.push(i.costId!);
        }
      });
      const isSettlementEntityNameSame: boolean =
        [...new Set(settlementEntityNameArr)].length > 1 ? true : false;
      const isJgpyCompanyInfoNameSame: boolean =
        [...new Set(jgpyCompanyInfoNameArr)].length > 1 ? true : false;
      const businessTypeNameSame: boolean =
        [...new Set(businessTypeNameArr)].length > 1 ? true : false;
      const deptIdNameSame: boolean = [...new Set(deptIdNameArr)].length > 1 ? true : false;
      const dataTypeNameSame: boolean = [...new Set(dataTypeNameArr)].length > 1 ? true : false;
      const expenseCategoryNameSame: boolean =
        [...new Set(expenseCategoryNameArr)].length > 1 ? true : false;

      let settlementType = 1;
      // if (isOrNameArr.length > 1) {
      //   if (expenseCategoryNameSame) {
      //     message.warn('费用种类不一致，请重新选择');
      //     return;
      //   }
      // }
      // if (dataTypeNameArr.length > 1) {
      //   if (dataTypeNameSame) {
      //     message.warn('所属类型不一致，请重新选择');
      //     return;
      //   }
      // }

      if (businessTypeNameSame) {
        message.warn('业务类型不一致，请重新选择');
        return;
      }
      if (deptIdNameSame) {
        message.warn('业务线不一致，请重新选择');
        return;
      }
      if (isSettlementEntityNameSame) {
        message.warn('结算主体不一致，请重新选择');
        return;
      }
      const isCompony = !!dataSource?.find((item) => item?.settlementType === 'COMPANY');
      const defaultValue = isCompony ? 1 : 2;
      settlementType = defaultValue;
      if (okKeys.length) {
        if (okKeys.length < allLen) {
          confirm({
            title: '请确认付款单类型:',
            icon: <Icon type="exclamation-circle" theme="filled" style={{ color: '#FAAD14' }} />,
            content: (
              <>
                <Radio.Group
                  onChange={(e) => {
                    settlementType = e?.target?.value;
                  }}
                  defaultValue={defaultValue}
                >
                  <Radio value={1} disabled={!isCompony}>
                    对公付款
                  </Radio>
                  <Radio value={2} disabled={isCompony}>
                    员工报销
                  </Radio>
                  <Radio value={3} disabled={!isCompony}>
                    付款单（预付核销）
                  </Radio>
                </Radio.Group>
              </>
            ),
            onOk() {
              // 根据付款单类型校验我司主体，付款单（预付核销）不校验
              // if (settlementType !== 3 && isJgpyCompanyInfoNameSame) {
              //   message.warn('我司主体不一致，请重新选择');
              //   return;
              // }
              // 根据付款单类型校验费用种类和所属类型，付款单（预付核销）不校验
              if (settlementType !== 3) {
                if (isJgpyCompanyInfoNameSame) {
                  message.warn('我司主体不一致，请重新选择');
                  return;
                }
                if (isOrNameArr.length > 1 && expenseCategoryNameSame) {
                  message.warn('费用种类不一致，请重新选择');
                  return;
                }
                if (dataTypeNameArr.length > 1 && dataTypeNameSame) {
                  message.warn('所属类型不一致，请重新选择');
                  return;
                }
              }
              confirm({
                title: `当前选中${allLen}条数据,其中${
                  allLen - okKeys.length
                }条数据可创建付款单，请确认是否操作?`,
                icon: (
                  <Icon type="exclamation-circle" theme="filled" style={{ color: '#FAAD14' }} />
                ),
                content: '',
                onOk() {
                  const ids = okKeys.join(',');
                  history.push(
                    `/financialSettlemen/mcnAccountantAdd?chargeType6Ids=${ids}&settlementType=${settlementType}`,
                  );
                },
              });
            },
          });
        } else {
          confirm({
            title: '请确认付款单类型:',
            icon: <Icon type="exclamation-circle" theme="filled" style={{ color: '#FAAD14' }} />,
            content: (
              <>
                <Radio.Group
                  onChange={(e) => {
                    settlementType = e?.target?.value;
                  }}
                  defaultValue={defaultValue}
                >
                  <Radio value={1} disabled={!isCompony}>
                    对公付款
                  </Radio>
                  <Radio value={2} disabled={isCompony}>
                    员工报销
                  </Radio>
                  <Radio value={3} disabled={!isCompony}>
                    付款单（预付核销）
                  </Radio>
                </Radio.Group>
              </>
            ),
            onOk() {
              // 根据付款单类型校验我司主体，付款单（预付核销）不校验
              // if (settlementType !== 3 && isJgpyCompanyInfoNameSame) {
              //   message.warn('我司主体不一致，请重新选择');
              //   return;
              // }
              // 根据付款单类型校验费用种类和所属类型，付款单（预付核销）不校验
              if (settlementType !== 3) {
                if (isJgpyCompanyInfoNameSame) {
                  message.warn('我司主体不一致，请重新选择');
                  return;
                }
                if (isOrNameArr.length > 1 && expenseCategoryNameSame) {
                  message.warn('费用种类不一致，请重新选择');
                  return;
                }
                if (dataTypeNameArr.length > 1 && dataTypeNameSame) {
                  message.warn('所属类型不一致，请重新选择');
                  return;
                }
              }
              const ids = okKeys.join(',');
              console.log(okKeys);
              history.push(
                `/financialSettlemen/mcnAccountantAdd?chargeType6Ids=${ids}&settlementType=${settlementType}`,
              );
            },
          });
        }
      } else {
        message.warn('当前没有符合条件的数据，请重新选择');
      }
    } else {
      message.warn('请选择一条数据');
    }
  };
  const { codeList } = useCode(BUSINESS_TYPE?.[tabKey], { able: true });
  const [sortMap, setSortMap] = useState<Record<string, SortOrder | undefined>>({});
  const { columns } = useTable({
    tabKey,
    pagination,
    pushCost: handlePushCost,
    nullify: handleNullify,
    goAdd: handleGoAdd,
    cateList: codeList,
    sortMap,
  });
  useEffect(() => {
    tabKey && onReset();
  }, [tabKey]);
  const handleTableChange = (
    pagination: PaginationConfig,
    filters: Partial<Record<keyof any, string[]>>,
    sorter: SorterResult<any>,
  ) => {
    const sortMap = {
      ascend: 'asc',
      descend: 'desc',
    };
    console.log(sorter, filters);
    // sortOrder?:string/*排序：类型：asc=升序，desc=降序*/,
    getList(
      {
        sortField: sorter.field,
        sortOrder: sorter?.order ? sortMap[sorter.order] : undefined,
      },
      true,
    );
    setSortMap({
      [sorter.field as string]: sorter.order,
    });
    setSelectKeys([]);
  };
  const doBatchPushCost = useCallback(async () => {
    setLoading(true);
    const reuslt = await responseWithResultAsync({
      request: batchPushCost,
      params: {
        idList: selectedKeys?.map((item) => item.id!) ?? [],
      },
    });
    setLoading(false);
    return reuslt;
  }, [selectedKeys]); // 批量推送
  useEffect(() => {
    if (location?.pathname === '/marketing-expenses') {
      onRefresh();
    }
  }, [location?.pathname]);
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        paddingTop: '12px',
        boxSizing: 'border-box',
        flex: 1,
      }}
    >
      <div className="formHeight">
        <SearchForm
          tabKey={tabKey}
          form={form}
          loading={loading}
          onSearch={() => {
            getList({ current: 1 });
            setSelectKeys([]);
          }}
          onReset={onReset}
        ></SearchForm>
        <div className={style.btnGroup} style={{ marginBottom: '16px' }}>
          <AuthWrapper functionName="f_marketing_expenses_create">
            <Button
              icon="plus"
              type="primary"
              onClick={() => {
                history.push('/marketing-expenses-create?type=' + 'create' + '&tabKey=' + tabKey);
              }}
            >
              新建
            </Button>
          </AuthWrapper>
          <AuthWrapper functionName="f_marketing_expenses_create_payment_order_batch">
            <Button
              className="ml-8"
              type="primary"
              onClick={() => {
                handleGoAdd(selectedKeys);
              }}
            >
              创建付款单
            </Button>
          </AuthWrapper>
          <AuthWrapper functionName="f_marketing_btach_push_cost">
            <BatchPush
              onRefresh={onRefresh}
              batchPush={doBatchPushCost}
              selectedKeys={selectedKeys}
            />
          </AuthWrapper>
          <AuthWrapper functionName="f_expense_cost_export">
            <ImportBox onRefresh={onRefresh} />
          </AuthWrapper>
          <AuthWrapper functionName="f_marketing_expenses_export">
            <ExportModal
              condition={exportParms}
              requestFunc={marketingExpenseExport}
              content={
                <div>
                  <p>
                    <span>导出完成后可在</span>
                    <a
                      href="javascript:;"
                      onClick={() => {
                        //
                      }}
                    >
                      导出记录
                    </a>
                    <span>中查看并下载。</span>
                  </p>
                </div>
              }
              onOk={() => null}
            >
              <ButtonProxy className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                <span>导出</span>
              </ButtonProxy>
            </ExportModal>
          </AuthWrapper>
          <AuthWrapper functionName="f_marketing_expenses_export">
            <ButtonProxy
              className="ml-8"
              style={{ borderColor: '#999999', color: '#444444' }}
              onClick={() => {
                history.push(
                  `/export-list-marketing-expenses-records?configCode=${EXPORT_RECORD_ENUM[tabKey]}`,
                );
              }}
            >
              <span>导出记录</span>
            </ButtonProxy>
          </AuthWrapper>
        </div>
      </div>
      <div style={{ flex: 1 }}>
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={list}
          pagination={false}
          scroll={{ y: tableHeight, x: '100%' }}
          rowSelection={rowSelection}
          rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
          onChange={handleTableChange}
        />
      </div>
      <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
        {/* @ts-ignore */}
        <PaginationProxy
          current={pagination?.current}
          pageSize={pagination?.size}
          total={pagination?.total}
          // @ts-ignore
          onChange={(current, size) => {
            setSelectKeys([]);
            getList({
              current,
              size,
            });
          }}
          valueType="flatten"
          pageSizeOptions={['5', '10', '20', '50', '100', '200']}
        />
      </div>
      <div
        style={{
          position: 'absolute',
          bottom: '20px',
          zIndex: '100',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <p style={{ fontWeight: 500, fontSize: '16px', color: '#666' }} className="mr-10">
          合计
        </p>
        <p className="mr-8">
          金额合计:
          <span>
            &yen;
            {formatFee(list?.reduce((a, b) => a + (b?.totalAmount ? Number(b.totalAmount) : 0), 0))}
          </span>
        </p>
        <p>
          数量合计:
          <span>{list?.reduce((a, b) => a + (b?.num ? Number(b.num) : 0), 0)}</span>
        </p>
      </div>
    </div>
  );
};

export default Form.create<PropsType>()(MarketingExpenses);
