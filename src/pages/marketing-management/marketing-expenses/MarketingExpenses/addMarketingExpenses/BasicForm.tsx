import { DatePicker, Input, Select, message } from 'antd';
import Form, { WrappedFormUtils } from 'antd/lib/form/Form';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import styles from './index.module.less';
// import { BUSINESS_TYPE, TypeOfExpense } from '../utils/enum';
import { useLiveRoomList } from '@/hooks/useLiveRoomList';
// import { AddLiveAnchorRequest } from '../services';
import { useCode, CODE_ENUM } from '@/common/constants/hooks';
import {
  BUSINESS_TYPE,
  BUSINESS_TYPE_ENUM,
  BUSINESS_TYPE_EXPNSESELIST,
  BUSINESS_TYPE_MAPPING,
  // EXPENSECATEGORY_NO_CODE,
} from '@/pages/marketing-management/utils/enum';
// import { useSupplierList } from '@/pages/anchor-information/utils/hook';
import {
  useCompanySimpleList,
  useSupplierList,
} from '@/pages/marketing-management/marketing-data/utils/hook';
import { SETTLEMENT_TYPE } from '../../utils/enum';
import { useEmployeeList } from '@/hooks/useEmployeeList';
import { MarketingExpenseDetailResult } from '../service';
import { CreateFormType } from '../types';
import moment from 'moment';
import { supplierDetail } from '@/pages/supplier-list/services';
import { TAX_RATE_LIST } from '../../../utils/enum';

const { Item } = Form;
const { Option } = Select;
type PropsType = {
  form: WrappedFormUtils;
  type: 'edit' | 'create';
  info?: MarketingExpenseDetailResult;
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 12,
  },
};
const formItemStyle: React.CSSProperties = {
  width: '200px',
};
const formStyle: React.CSSProperties = {
  width: '25%',
};

const BasicForm: React.FC<PropsType> = ({ form, type, info }) => {
  const { getFieldDecorator } = form;
  const hasExecutedTaxRateSet = useRef(false); // 用于追踪税率设置是否已执行
  const {
    liveList,
    loading: liveRoomLoaindg,
    handleSearch,
    handleChange,
    buId,
  } = useLiveRoomList(); // 直播间列表
  const { codeList } = useCode(
    BUSINESS_TYPE?.[form?.getFieldsValue()?.businessType as BUSINESS_TYPE_ENUM],
    { able: true },
  ); // 费用种类
  const { codeEnum: taxRateEnum } = useCode(CODE_ENUM.TAX_RATE, {
    able: true,
  }); // 税率枚举
  const formValue = useMemo(
    () => form?.getFieldsValue() as CreateFormType,
    [form?.getFieldsValue()],
  ); // 表单值

  // 注意：下方结算主体的查询仅用于编辑页面进来后根据结算主体查询客商税率，不作为结算主体下拉使用
  const { list: vendorList, handleSearch: vendorSearch } = useSupplierList({
    form,
    page: 'expenses',
    type,
    oaAuditStatus: 'PASS', // 只查询已通过的供应商
  });
  // 注意：上方结算主体的查询仅用于编辑页面进来后根据结算主体查询客商税率，不作为结算主体下拉使用

  // 获取对公，客商结算主体下拉项
  const {
    list: customerNameList,
    loading: customerLoading,
    handleSearch: customerSearch,
  } = useSupplierList({
    form,
    page: 'expenses',
    type,
    oaAuditStatus: 'PASS', // 只查询已通过的供应商
  }); // 结算主体
  const {
    list: companyList,
    loading: companyLoading,
    handleSearch: companySearch,
  } = useCompanySimpleList({ type }); // 我司主体
  const {
    list: employList,
    loading: employLoading,
    handleSearch: employSearch,
  } = useEmployeeList({ excludeBizRoleTypes: ['ANCHOR'] }); // 员工列表，过滤主播类型
  const settlementEntityList = useMemo(() => {
    const settlementType = formValue?.settlementType?.key;
    return settlementType === 'COMPANY'
      ? customerNameList?.map((item) => ({ key: item?.id, label: item?.supplierName }))
      : settlementType === 'PERSON'
      ? employList?.map((item) => ({ key: item?.employeeId, label: item?.employeeName }))
      : undefined;
  }, [formValue?.settlementType, customerNameList, employList]); // 结算主体列表
  const settlementEntityLoading = useMemo(() => {
    const settlementType = formValue?.settlementType?.key;
    return settlementType === 'COMPANY'
      ? customerLoading
      : settlementType === 'PERSON'
      ? employLoading
      : false;
  }, [formValue?.settlementType, customerLoading, employLoading]); // 结算主体列表loading
  const settlementEntitySearch = useCallback(
    (name: string) => {
      const settlementType = formValue?.settlementType?.key;
      settlementType === 'COMPANY'
        ? customerSearch(name)
        : settlementType === 'PERSON'
        ? employSearch(name)
        : undefined;
    },
    [formValue?.settlementType, customerSearch, employSearch],
  ); // 结算主体搜索

  // 处理结算主体选择后的税率设置
  const handleSettlementEntityChange = useCallback(
    (value: { key?: string; label?: string }) => {
      // 清空搜索
      settlementEntitySearch('');
      form?.setFieldsValue({ taxRate: 0 }); // 先重置税率
      // 当选择的是公司类型时，自动设置税率
      if (formValue?.settlementType?.key === 'COMPANY' && value?.key) {
        // 调用详情接口获取税率（防止客商税率变更后，客商列表未更新）
        supplierDetail({ supplierId: value.key }).then((res) => {
          if (res?.res?.code === '200' && taxRateEnum) {
            // 使用taxRateEnum将税率代码转换为显示文本，如'TAX_RATE_1' -> '1%'
            const taxRateText = taxRateEnum[res?.res?.result?.taxRate];
            if (taxRateText) {
              // 提取数字部分，去掉%符号
              const taxRateNumber = parseFloat(taxRateText.replace('%', ''));
              if (!isNaN(taxRateNumber) && TAX_RATE_LIST.includes(taxRateNumber)) {
                // 设置到BusinessForm的税率字段
                form?.setFieldsValue({ taxRate: taxRateNumber });
              }
            }
          }
        });
        // 从customerNameList中找到对应的供应商信息
        // const selectedSupplier = customerNameList?.find((item) => item?.id === value.key);
        // if (selectedSupplier?.taxRate && taxRateEnum) {
        //   // 使用taxRateEnum将税率代码转换为显示文本，如'TAX_RATE_1' -> '1%'
        //   const taxRateText = taxRateEnum[selectedSupplier.taxRate];
        //   if (taxRateText) {
        //     // 提取数字部分，去掉%符号
        //     const taxRateNumber = parseFloat(taxRateText.replace('%', ''));
        //     if (!isNaN(taxRateNumber) && [0, 1, 3, 6, 9, 13].includes(taxRateNumber)) {
        //       // 设置到BusinessForm的税率字段
        //       form?.setFieldsValue({ taxRate: taxRateNumber });
        //     }
        //   }
        // }
      }
    },
    [formValue?.settlementType, customerNameList, taxRateEnum],
  );

  useEffect(() => {
    handleSearch('');
  }, []);
  useEffect(() => {
    form?.setFieldsValue({ deptId: buId });
  }, [buId]);
  useEffect(() => {
    (formValue?.expenseCategory === 'ANCHOR_COUPON' ||
      formValue?.expenseCategory === 'SUI_XIN_TUI') &&
      form?.setFieldsValue({ settlementType: { key: 'COMPANY' } });
  }, [formValue?.expenseCategory]); // 结算主体搜索

  // 当详情数据满足条件时，先搜索对应的供应商
  useEffect(() => {
    if (
      info?.costStatus === 'AUDIT_REJECT' &&
      info?.settlementType === 'COMPANY' &&
      info?.settlementEntityId &&
      info?.settlementEntityName
    ) {
      // 先用settlementEntityName搜索，确保能找到对应的供应商
      vendorSearch(info.settlementEntityName);
    }
  }, [info]);

  // 监听vendorList变化，处理税率设置（只执行一次）
  useEffect(() => {
    if (
      !hasExecutedTaxRateSet.current &&
      info?.costStatus === 'AUDIT_REJECT' &&
      info?.settlementType === 'COMPANY' &&
      info?.settlementEntityId &&
      vendorList?.length > 0
    ) {
      // 调用详情接口获取税率（防止客商税率变更后，客商列表未更新）
      supplierDetail({ supplierId: info.settlementEntityId }).then((res) => {
        if (res?.res?.code === '200' && taxRateEnum) {
          // 使用taxRateEnum将税率代码转换为显示文本，如'TAX_RATE_1' -> '1%'
          const taxRateText = taxRateEnum[res?.res?.result?.taxRate];
          if (taxRateText) {
            // 提取数字部分，去掉%符号
            const taxRateNumber = parseFloat(taxRateText.replace('%', ''));
            if (!isNaN(taxRateNumber) && [0, 1, 3, 6, 9, 13].includes(taxRateNumber)) {
              // 设置到BusinessForm的税率字段
              form?.setFieldsValue({ taxRate: taxRateNumber });
              message.success('已根据当前结算主体获取最新客商税率');
              hasExecutedTaxRateSet.current = true; // 标记已执行
            }
          }
        }
      });
      // 从vendorList中找到对应的供应商信息
      // const selectedSupplier = vendorList?.find((item) => item?.id === info.settlementEntityId);
      // if (selectedSupplier?.taxRate && taxRateEnum) {
      //   // 使用taxRateEnum将税率代码转换为显示文本，如'TAX_RATE_1' -> '1%'
      //   const taxRateText = taxRateEnum[selectedSupplier.taxRate];
      //   if (taxRateText) {
      //     // 提取数字部分，去掉%符号
      //     const taxRateNumber = parseFloat(taxRateText.replace('%', ''));
      //     if (!isNaN(taxRateNumber) && [0, 1, 3, 6, 9, 13].includes(taxRateNumber)) {
      //       // 设置到BusinessForm的税率字段
      //       form?.setFieldsValue({ taxRate: taxRateNumber });
      //       message.success('已根据当前结算主体获取最新客商税率');
      //       hasExecutedTaxRateSet.current = true; // 标记已执行
      //     }
      //   }
      // }
    }
  }, [info, vendorList, taxRateEnum]);

  const disabledDate = (current: moment.Moment | null) => {
    return !!current?.isBefore(moment('2025-01-01'));
  }; // 日期禁用
  return (
    <>
      <Form {...formItemLayout} className={styles['form-box']}>
        <Item label="业务类型" style={formStyle}>
          {getFieldDecorator('businessType', {
            rules: [
              {
                required: true,
                message: '请选择业务类型',
              },
            ],
          })(
            <Select
              disabled={type === 'edit'}
              placeholder="请选择"
              allowClear
              style={formItemStyle}
              onChange={() => {
                form?.setFieldsValue({
                  expenseCategory: undefined,
                });
              }}
            >
              {Object.entries(BUSINESS_TYPE_MAPPING)
                // ?.filter((item) => BUSINESS_TYPE_EXPNSESELIST.includes(item[0]))
                .map((key) => ({ label: key[1], key: key[0] }))
                .map((item) => (
                  <Option value={item.key} key={item.key}>
                    {item.label}
                  </Option>
                ))}
            </Select>,
          )}
        </Item>
        <Item label="费用种类" style={formStyle}>
          {getFieldDecorator('expenseCategory', {
            rules: [
              {
                required: true,
                message: '请选择费用种类',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              allowClear
              style={formItemStyle}
              disabled={type === 'edit'}
              showSearch
              filterOption={(input, option) => {
                const label = option?.props?.children?.toString() ?? '';
                const value = input?.toString() ?? '';
                return label?.includes(value);
              }}
            >
              {codeList
                // ?.filter((item) => !EXPENSECATEGORY_NO_CODE.includes(item.value!))
                ?.map((item) => (
                  <Option value={item.value} key={item.value}>
                    {item.label}
                  </Option>
                ))}
            </Select>,
          )}
        </Item>
        <Item label="结算类型" style={formStyle}>
          {getFieldDecorator('settlementType', {
            rules: [
              {
                required: true,
                message: '请选择结算类型',
              },
            ],
          })(
            <Select
              style={formItemStyle}
              labelInValue
              onChange={() => {
                form?.setFieldsValue({ settlementEntityInfo: undefined });
              }}
              // disabled={info?.source === 'SYSTEM'}
            >
              {Object.entries(SETTLEMENT_TYPE)?.map((item) => (
                <Option key={item[0]} value={item[0]}>
                  {item[1]}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="结算主体" style={formStyle}>
          {getFieldDecorator('settlementEntityInfo', {
            rules: [
              {
                required: true,
                message: '请选择结算主体',
              },
            ],
          })(
            <Select
              style={formItemStyle}
              labelInValue
              loading={settlementEntityLoading}
              placeholder="请选择"
              allowClear
              showSearch
              defaultActiveFirstOption={false}
              showArrow={false}
              filterOption={false}
              onSearch={settlementEntitySearch}
              onChange={handleSettlementEntityChange}
              onBlur={() => {
                settlementEntitySearch('');
              }}
            >
              {settlementEntityList?.map((item) => (
                <Option key={item?.key} value={item?.key}>
                  {item?.label}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="我司主体" style={formStyle}>
          {getFieldDecorator('jgpyCompanyInfo', {
            rules: [
              {
                required: true,
                message: '请选择我司主体',
              },
            ],
          })(
            <Select
              style={formItemStyle}
              loading={companyLoading}
              placeholder="请选择"
              showSearch
              onSearch={companySearch}
              filterOption={false}
              labelInValue
            >
              {companyList?.map((item) => (
                <Option key={item?.id} value={item?.id}>
                  {item?.name}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="直播间" style={formStyle}>
          {getFieldDecorator('liveRoomInfo', {
            rules: [
              {
                // required: form?.getFieldsValue()?.businessType !== 'FIELD_COST',
                required: true,
                message: '请选择直播间',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              allowClear
              loading={liveRoomLoaindg}
              style={formItemStyle}
              labelInValue
              showSearch
              defaultActiveFirstOption={false}
              showArrow={false}
              filterOption={false}
              onSearch={handleSearch}
              onChange={(value: { key?: string; label?: string }) => {
                value?.key && handleChange(value?.key?.toString());
                handleSearch('');
              }}
              onBlur={() => {
                handleSearch('');
              }}
            >
              {liveList?.map((item) => (
                <Option value={Number(item.id)} key={Number(item.id)}>
                  {item.name}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="直播间ID" style={formStyle}>
          <p>{form?.getFieldsValue()?.liveRoomInfo?.key}</p>
        </Item>
        <Item label="直播日期" style={formStyle}>
          {getFieldDecorator('liveDate', {
            rules: [
              {
                required: true,
                message: '请选择直播日期',
              },
            ],
          })(
            <DatePicker disabledDate={disabledDate} format={'YYYY-MM-DD'} style={formItemStyle} />,
          )}
        </Item>
      </Form>
      <section style={{ height: 0, width: 0, opacity: 0, overflow: 'hidden' }}>
        <Form>
          <Item label="直播日期" style={formStyle}>
            {getFieldDecorator('deptId')(<Input />)}
          </Item>
        </Form>
      </section>
    </>
  );
};

export default BasicForm;
