import { DatePicker, Input, InputNumber, Select } from 'antd';
import Form, { WrappedFormUtils } from 'antd/lib/form/Form';
import React, { useEffect, useMemo } from 'react';
import styles from './index.module.less';
import { BUSINESS_FORM_MAP, DATA_TYPE_ENUM } from '../../utils/enum';
import { BUSINESS_TYPE_ENUM, TAX_RATE_LIST } from '@/pages/marketing-management/utils/enum';
import { MarketingExpenseDetailResult } from '../service';
import moment from 'moment';
import { useAnthorList } from '@/pages/anchor-performance/utils/hook';
import { CreateFormType } from '../types';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  rebateCompute,
  RebateComputeRequest,
} from '@/pages/marketing-management/marketing-data/services';
// import { SIGNING_AGREEMENT_TEMPLATE, TYPE } from '../utils/enum';
// import { useSupplierList } from '../utils/hook';
const { Item } = Form;
const { Option } = Select;
type PropsType = {
  form: WrappedFormUtils;
  info?: MarketingExpenseDetailResult;
  recordCode?: string;
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 12,
  },
};
const formItemStyle: React.CSSProperties = {
  width: '200px',
};
const formStyle: React.CSSProperties = {
  width: '25%',
};
const BusinessForm: React.FC<PropsType> = ({ form, info, recordCode }) => {
  const { getFieldDecorator } = form;
  const formValue = useMemo(
    () => form?.getFieldsValue() as CreateFormType,
    [form?.getFieldsValue()],
  ); // 表单值
  const showFormList = useMemo(
    () => BUSINESS_FORM_MAP[formValue?.businessType as BUSINESS_TYPE_ENUM],
    [formValue?.businessType],
  );
  const totalConsume = useMemo(() => Number(formValue?.totalConsume), [formValue?.totalConsume]); // 实际消耗
  const grantAmount = useMemo(() => Number(formValue?.grantAmount), [formValue?.grantAmount]); // 赠款消耗
  const eliminationAllocation = useMemo(
    () => Number(formValue?.eliminationAllocation),
    [formValue?.eliminationAllocation],
  ); // 消返调配
  const refundAmount = useMemo(() => Number(formValue?.refundAmount), [formValue?.refundAmount]); // 消返金额
  const price = useMemo(() => Number(formValue?.price), [formValue?.price]); // 采购/补贴单价
  const num = useMemo(() => Number(form?.getFieldsValue()?.num), [form?.getFieldsValue()?.num]); // 数量
  const rebateAmount = useMemo(
    () => Number(form?.getFieldsValue()?.rebateAmount),
    [form?.getFieldsValue()?.rebateAmount],
  ); // 返点金额
  const businessType = useMemo(() => formValue?.businessType, [formValue?.businessType]);
  const requiredPrice = useMemo(
    () => formValue?.expenseCategory !== 'TAOBAO_RED_ENVELOPE',
    [formValue?.expenseCategory],
  ); // 采购/补贴单价是否必填
  useEffect(() => {
    if (formValue?.expenseCategory === 'TAOBAO_RED_ENVELOPE') {
      // 淘宝红包
      form?.setFieldsValue({ num: 1 });
    }
  }, [formValue?.expenseCategory]);
  useEffect(() => {
    if (businessType === 'TRAFFIC_DELIVERY') {
      // 流量投放
      !isNaN(totalConsume) &&
        !isNaN(grantAmount) &&
        !isNaN(eliminationAllocation) &&
        !isNaN(refundAmount) &&
        form?.setFieldsValue({
          totalAmount:
            totalConsume - grantAmount - eliminationAllocation - refundAmount - rebateAmount,
        });
    }
    if (businessType === 'GOODS_SUBSIDY' || businessType === 'LUCKY_DRAW_AND_REWARD') {
      // 商品补贴
      !isNaN(price) && !isNaN(num) && form?.setFieldsValue({ totalAmount: price * num });
    }
  }, [
    totalConsume,
    grantAmount,
    businessType,
    price,
    num,
    eliminationAllocation,
    refundAmount,
    rebateAmount,
  ]);
  const getRebateAmount = async (params: RebateComputeRequest) => {
    const result = await responseWithResultAsync({
      request: rebateCompute,
      params,
    });
    if (result) {
      form?.setFieldsValue({
        rebateAmount: result?.rebateAmount,
        totalAmount: result?.totalAmount,
      });
    }
  };
  const isNaNZero = (value: number | string) => {
    return isNaN(Number(value)) || value === 'NaN' ? 0 : value;
  };
  useEffect(() => {
    if (
      ['SUI_XIN_TUI', 'LIVE_STREAM'].includes(formValue?.expenseCategory as string) &&
      formValue?.settlementEntityInfo?.label === '海南优矩科技有限公司' &&
      formValue?.liveRoomInfo?.key
    ) {
      getRebateAmount({
        businessType: formValue?.businessType,
        eliminationAllocation: isNaNZero(eliminationAllocation)?.toString(),
        expenseCategory: formValue?.expenseCategory,
        grantAmount: isNaNZero(grantAmount)?.toString(),
        liveRoomId: formValue?.liveRoomInfo?.key,
        recordCode: recordCode,
        refundAmount: isNaNZero(refundAmount)?.toString(),
        settlementEntityId: formValue?.settlementEntityInfo?.key,
        totalConsume: isNaNZero(totalConsume)?.toString(),
      });
    }
  }, [totalConsume, grantAmount, refundAmount, eliminationAllocation]);
  const { list, loading, handleSearch: anthorSearch, handleChange } = useAnthorList();
  return (
    <>
      <Form {...formItemLayout} className={styles['form-box']}>
        {showFormList?.includes('goodsContent') && (
          <Item label="商品内容" style={formStyle}>
            {getFieldDecorator('goodsContent', {
              initialValue: info?.goodsContent,
              rules: [
                {
                  required: businessType !== 'TRAFFIC_DELIVERY',
                  message: '请输入商品内容',
                },
              ],
            })(<Input placeholder="请输入" maxLength={200} />)}
          </Item>
        )}
        {showFormList?.includes('totalAmount') && (
          <Item label="金额" style={formStyle}>
            {getFieldDecorator('totalAmount', {
              initialValue: info?.totalAmount ?? 0,
              rules: [
                {
                  required: true,
                  message: '请输入金额',
                },
              ],
            })(
              <InputNumber
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                // min={0}
                // maxLength={7}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('taxRate') && (
          <Item label="税率" style={formStyle}>
            {getFieldDecorator('taxRate', {
              initialValue: info?.taxRate ?? 0,
              rules: [
                {
                  required: true,
                  message: '请选择税率',
                },
              ],
            })(
              <Select placeholder="请选择" allowClear style={formItemStyle}>
                {TAX_RATE_LIST.map((item) => ({ key: item, label: item + '%' })).map((item) => (
                  <Option value={item.key} key={item.key}>
                    {item.label}
                  </Option>
                ))}
              </Select>,
            )}
          </Item>
        )}
        {showFormList?.includes('goodsId') && (
          <Item label="商品/抽奖ID" style={formStyle}>
            {getFieldDecorator('goodsId', {
              initialValue: info?.goodsId,
            })(<Input placeholder="请输入" style={formItemStyle} maxLength={64} />)}
          </Item>
        )}
        {showFormList?.includes('takeHomePrice') && (
          <Item label="直播间到手价" style={formStyle}>
            {getFieldDecorator('takeHomePrice', {
              initialValue: info?.takeHomePrice ?? 0,
            })(
              <InputNumber
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                min={0}
                // maxLength={7}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('price') && (
          <Item label="采购/补贴单价" style={formStyle}>
            {getFieldDecorator('price', {
              initialValue: info?.price ?? 0,
              rules: [
                {
                  required: requiredPrice,
                  message: '请输入采购/补贴单价',
                },
              ],
            })(
              <InputNumber
                disabled={formValue?.expenseCategory === 'TAOBAO_RED_ENVELOPE'}
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                min={0}
                // maxLength={7}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('num') && (
          <Item label="数量" style={formStyle}>
            {getFieldDecorator('num', {
              initialValue:
                formValue?.expenseCategory === 'TAOBAO_RED_ENVELOPE' ? 1 : info?.num ?? 0,
              rules: [
                {
                  required: true,
                  message: '请输入数量',
                },
              ],
            })(
              <InputNumber
                placeholder="请输入"
                disabled={formValue?.expenseCategory === 'TAOBAO_RED_ENVELOPE'}
                formatter={(value) => `${value ? parseInt(value?.toString()) : value}`}
                // precision={2}
                // step={0.01}
                style={formItemStyle}
                min={0}
                // maxLength={7}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('totalConsume') && (
          <Item label="实际消耗" style={formStyle}>
            {getFieldDecorator('totalConsume', {
              initialValue: 0,
            })(
              <InputNumber
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                min={0}
                // maxLength={7}
                disabled={info?.source === 'SYSTEM'}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('grantAmount') && (
          <Item label="赠款消耗" style={formStyle}>
            {getFieldDecorator('grantAmount', {
              initialValue: 0,
            })(
              <InputNumber
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                // min={0}
                // maxLength={7}
                disabled={info?.source === 'SYSTEM'}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('refundAmount') && (
          <Item label="消返金额" style={formStyle}>
            {getFieldDecorator('refundAmount', {
              initialValue: 0,
            })(
              <InputNumber
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                // min={0}
                // maxLength={8}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('eliminationAllocation') && (
          <Item label="消返调配" style={formStyle}>
            {getFieldDecorator('eliminationAllocation', {
              initialValue: 0,
            })(
              <InputNumber
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                // min={0}
                // maxLength={8}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('rebateAmount') && (
          <Item label="返点金额" style={formStyle}>
            {getFieldDecorator('rebateAmount', {
              initialValue: 0,
            })(
              <InputNumber
                formatter={(value) => `${value}元`}
                placeholder="请输入"
                precision={2}
                step={0.01}
                style={formItemStyle}
                // min={0}
                // maxLength={8}
              />,
            )}
          </Item>
        )}
        {showFormList?.includes('activityLink') && (
          <Item label="活动链接" style={formStyle}>
            {getFieldDecorator('activityLink')(
              <Input placeholder="请输入" style={formItemStyle} maxLength={200} />,
            )}
          </Item>
        )}
        {showFormList?.includes('dataType') && (
          <Item label="所属类型" style={formStyle}>
            {getFieldDecorator('dataType', {
              rules: [
                {
                  required:
                    formValue?.expenseCategory === 'LUCKYBAG_MD' ||
                    formValue?.expenseCategory === 'LUCKY_BAG',
                  message: '请选择所属类型',
                },
              ],
            })(
              <Select placeholder="请选择" style={formItemStyle}>
                {Object.entries(DATA_TYPE_ENUM).map(([value, label]) => (
                  <Option value={value} key={value}>
                    {label}
                  </Option>
                ))}
              </Select>,
            )}
          </Item>
        )}
        {showFormList?.includes('feeDate') && (
          <Item label="对应日期" style={formStyle}>
            {getFieldDecorator('feeDate', {
              initialValue: info?.feeDate ? moment(info.feeDate) : undefined,
              rules: [
                {
                  required: true,
                  message: '请选择对应日期',
                },
              ],
            })(<DatePicker format={'YYYY-MM-DD'} style={formItemStyle} />)}
          </Item>
        )}
        {showFormList?.includes('projectContents') && (
          <Item label="项目内容" style={formStyle}>
            {getFieldDecorator('projectContents', {
              initialValue: info?.projectContents,
              rules: [
                {
                  required: true,
                  message: '请输入项目内容',
                },
              ],
            })(<Input placeholder="请输入" maxLength={200} />)}
          </Item>
        )}
        <Item label="主播" style={formStyle}>
          {getFieldDecorator('anchorInfo')(
            <Select
              placeholder="请选择"
              loading={loading}
              showSearch
              onSearch={anthorSearch}
              filterOption={false}
              // onChange={handleChange}
              labelInValue
            >
              {list?.map((item) => (
                <Select.Option key={item.anchorCode} value={item.anchorCode}>
                  {item.anchorName}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Item>
      </Form>

      <Form labelCol={{ span: 2 }} wrapperCol={{ span: 21 }} className={styles['form-box']}>
        {showFormList?.includes('remark') && (
          <Item label="备注" style={{ width: '100%' }}>
            {getFieldDecorator('remark', { initialValue: info?.remark })(
              <Input.TextArea placeholder="请输入" maxLength={200} />,
            )}
          </Item>
        )}
      </Form>
    </>
  );
};

export default BusinessForm;
