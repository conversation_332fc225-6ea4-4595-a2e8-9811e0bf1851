import React, { useEffect, useMemo } from 'react';
import { DatePicker, Input, Select } from 'antd';
import { FormComponentProps, WrappedFormUtils } from 'antd/es/form/Form';
import { useLiveRoomList } from '@/hooks/useLiveRoomList';
import { useAnthorList } from '@/pages/anchor-performance/utils/hook';
import BatchInput from '@/components/BatchInput';
import {
  BUSINESS_TYPE_MAP,
  EXPENSE_ORDER_STATUS_ENUM,
  BUSINESS_TYPE_ENUM,
} from '@/pages/marketing-management/utils/enum';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import SearchFormComponent, { searchItem } from '../searchFormComponent';
import { useSettlement } from '../../hooks/useSettlement';
import { useDeptList } from '@/hooks/useDeptList';
import { useCompanySimpleList } from '@/pages/marketing-management/marketing-data/utils/hook';
const { Option } = Select;
interface IProps extends FormComponentProps {
  getTableHeight?: any;
  tabKey: keyof typeof BUSINESS_TYPE_MAP;
  onSearch: () => void;
  onReset: () => void;
  loading: boolean;
}
const SearchForm: React.FC<IProps> = ({ tabKey, form, onSearch, onReset, getTableHeight }) => {
  const { liveList, loading: liveRoomLoaindg, getLiveRoomList, handleSearch } = useLiveRoomList();
  useEffect(() => {
    handleSearch('');
  }, []);
  const { list, loading, handleSearch: anthorSearch, handleChange } = useAnthorList();
  const { codeList } = useCode(CODE_ENUM[tabKey as keyof typeof CODE_ENUM], {
    able: true,
  });
  const {
    list: settltmentList,
    loading: settlementLoading,
    handleSearch: settlemengSearch,
  } = useSettlement();
  const reset = () => {
    onReset();
  };
  const { deptList } = useDeptList();
  const {
    list: companyList,
    loading: companyLoading,
    handleSearch: companySearch,
  } = useCompanySimpleList({});
  const optionsMap = {
    [BUSINESS_TYPE_ENUM.FIELD_COST]: {
      goodsContents: {
        label: '商品内容',
        renderNode: <Input placeholder="请输入" maxLength={25} />,
      },
    },
    [BUSINESS_TYPE_ENUM.PUBLICITY_EXPENSES]: {
      feeDate: {
        label: '对应日期',
        renderNode: <DatePicker.RangePicker format={'YYYY-MM-DD'} />,
      },
      projectContents: {
        label: '项目内容',
        renderNode: <Input placeholder="请输入" maxLength={200} />,
      },
    },
    [BUSINESS_TYPE_ENUM.TRAFFIC_DELIVERY]: {
      goodsContents: {
        label: '商品内容',
        renderNode: <Input placeholder="请输入" maxLength={25} />,
      },
    },
    [BUSINESS_TYPE_ENUM.LUCKY_DRAW_AND_REWARD]: {
      goodsContents: {
        label: '商品内容',
        renderNode: <Input placeholder="请输入" maxLength={25} />,
      },
    },
    [BUSINESS_TYPE_ENUM.ARTIST_COST]: {
      anchorCode: {
        label: '主播',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            mode="multiple"
            maxTagCount={1}
            showSearch
            defaultActiveFirstOption={false}
            showArrow={false}
            filterOption={false}
            onBlur={() => {
              anthorSearch('');
            }}
            loading={loading}
            onSearch={anthorSearch}
          >
            {list?.map((item) => (
              <Select.Option key={item.anchorCode} value={item.anchorCode}>
                {item.anchorName}
              </Select.Option>
            ))}
          </Select>
        ),
      },
      projectContents: {
        label: '项目内容',
        renderNode: <Input placeholder="请输入" maxLength={200} />,
      },
    },
    [BUSINESS_TYPE_ENUM.GOODS_SUBSIDY]: {
      goodsId: {
        label: '商品ID',
        renderNode: <BatchInput label="商品ID" maxRow={20} />,
      },
      goodsContents: {
        label: '商品内容',
        renderNode: <Input placeholder="请输入" maxLength={200} />,
      },
    },
    [BUSINESS_TYPE_ENUM.SHORT_VIDEO_COST]: {
      feeDate: {
        label: '对应日期',
        renderNode: <DatePicker.RangePicker format={'YYYY-MM-DD'} />,
      },
    },
  };
  const options = useMemo<Record<string, searchItem>>(
    () => ({
      liveDate: {
        label: '直播日期',
        renderNode: <DatePicker.RangePicker format={'YYYY-MM-DD'} />,
      },
      liveRoomId: {
        label: '直播间',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            onChange={() => {
              handleSearch('');
            }}
            loading={liveRoomLoaindg}
            mode="multiple"
            maxTagCount={1}
            showSearch
            defaultActiveFirstOption={false}
            showArrow={false}
            filterOption={false}
            onSearch={handleSearch}
            onBlur={() => {
              handleSearch('');
            }}
          >
            {liveList?.map((item) => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        ),
      },
      costStatus: {
        label: '费用状态',
        renderNode: (
          <Select placeholder="请选择" mode="multiple" maxTagCount={1} allowClear>
            {Object.entries(EXPENSE_ORDER_STATUS_ENUM)?.map((item) => (
              <Select.Option value={item[0]}>{item[1]}</Select.Option>
            ))}
          </Select>
        ),
      },
      costRecordCode: {
        label: '费用编号',
        renderNode: <BatchInput label="费用编号" maxRow={20} />,
      },
      sourceOrderNo: {
        label: '来源单号',
        renderNode: <BatchInput label="来源单号" maxRow={20} />,
      },
      expenseCategory: {
        label: '费用种类',
        renderNode: (
          <Select
            placeholder="请选择"
            mode="multiple"
            maxTagCount={1}
            allowClear
            showSearch
            optionFilterProp="label"
            filterOption={(input, option) => {
              const label = option?.props?.children?.toString() ?? '';
              const value = input?.toString() ?? '';
              return label?.includes(value);
            }}
          >
            {codeList?.map((item) => (
              <Select.Option value={item.value} key={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        ),
      },
      ...((optionsMap?.[tabKey as BUSINESS_TYPE_ENUM] as Record<BUSINESS_TYPE_ENUM, searchItem>) ??
        {}),
      settlementEntityIdList: {
        label: '结算主体',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            onChange={() => {
              settlemengSearch('');
            }}
            loading={settlementLoading}
            mode="multiple"
            maxTagCount={1}
            showSearch
            defaultActiveFirstOption={false}
            showArrow={false}
            filterOption={false}
            onSearch={settlemengSearch}
            onBlur={() => {
              settlemengSearch('');
            }}
          >
            {settltmentList?.map((item) => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        ),
      },
      settlementNoList: {
        label: '付款单号',
        renderNode: <BatchInput label="付款单号" maxRow={20} />,
      },
      deptId: {
        label: '事业部',
        span: 1,
        draggable: true,
        renderNode: (
          <Select allowClear placeholder="请选择事业部">
            {deptList?.map((item) => {
              return (
                <Option value={item?.value} key={item?.value}>
                  {item.label}
                </Option>
              );
            })}
          </Select>
        ),
      },
      jgpyCompanyInfoId: {
        label: '我司主体',
        renderNode: (
          <Select
            loading={companyLoading}
            placeholder="请选择"
            showSearch
            onSearch={companySearch}
            filterOption={false}
          >
            {companyList?.map((item) => (
              <Option key={item?.id} value={item?.id}>
                {item?.name}
              </Option>
            ))}
          </Select>
        ),
      },
    }),
    [tabKey, liveList, codeList, optionsMap, deptList, companyList, companyLoading, companySearch],
  );

  return (
    <SearchFormComponent
      form={form}
      options={options}
      loading={loading}
      onSearch={onSearch}
      onReset={reset}
      getTableHeight={getTableHeight}
    />
  );
};

export default SearchForm;
