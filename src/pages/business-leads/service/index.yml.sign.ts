const signMap = {
  '/supplier/public/businessOpportunityLead/page': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunityLead/generateBusinessOpportunity': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunityLead/generateInviteLink': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunityLead/delete': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunityLead/batchDelete': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunityLead/update': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunityLead/detail': 'basic.x1.xxxx1',
  '/supplier/open/businessOpportunityLead/create': 'open.x1.xxxx1',
  '/supplier/open/businessOpportunityLead/validateInviteLink': 'open.x1.xxxx1',
};

export function getSign(path: string): string {
  return signMap[path];
}
