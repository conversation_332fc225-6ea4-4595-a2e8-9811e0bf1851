import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type BusinessOpportunityLeadPageRequest = {
  brandName?: string /*品牌名称*/;
  brandTypes?: Array<string> /*品牌类型列表*/;
  cooperationNetPriceMax?: string /*合作到手价最大值*/;
  cooperationNetPriceMin?: string /*合作到手价最小值*/;
  creator?: string /*创建人*/;
  current?: number /*当前页码,从1开始*/;
  dailyRetailPriceMax?: string /*日常零售价格最大值*/;
  dailyRetailPriceMin?: string /*日常零售价格最小值*/;
  douyinStoreName?: string /*抖音店铺名称*/;
  douyinStoreRatings?: Array<string> /*抖音店铺综合评分列表*/;
  gmtCreatedEnd?: string /*创建时间结束*/;
  gmtCreatedStart?: string /*创建时间开始*/;
  intendedCooperationPlatforms?: Array<
    'DY' | 'JD' | 'TB' | 'PDD' | 'KS' | 'WECHAT_VIDEO' | 'BAIDU' | 'RED'
  > /*意向合作平台列表[PlatformSourceEnum]*/;
  masked?: boolean /*是否脱敏*/;
  pastCollaboratingStreamers?: string /*过往合作主播*/;
  productCategories?: Array<string> /*商品类目列表*/;
  productName?: string /*商品名称*/;
  size?: number /*分页大小*/;
  supplierCompanyName?: string /*公司主体名称*/;
  taobaoStoreName?: string /*淘宝店铺名称*/;
  taobaoStoreRatings?: Array<string> /*淘宝店铺综合评分列表*/;
};

export type BusinessOpportunityLeadPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    brandFinancedStatus?: string /*品牌融资状态*/;
    brandFinancedStatusDesc?: string /*品牌融资状态描述*/;
    brandInvestor?: string /*品牌投资方*/;
    brandName?: string /*品牌名称*/;
    brandType?: string /*品牌类型*/;
    brandTypeDesc?: string /*品牌类型描述*/;
    contactMobile?: string /*联系人手机号原始信息*/;
    contactMobileMasked?: string /*联系人手机号脱敏信息*/;
    contactPersonName?: string /*联系人姓名*/;
    contactPosition?: string /*职位*/;
    contactWechat?: string /*联系人微信号原始信息*/;
    contactWechatMasked?: string /*联系人微信号脱敏信息*/;
    cooperationNetPrice?: string /*合作到手价*/;
    creator?: string /*创建人*/;
    creatorName?: string /*创建人名称*/;
    dailyRetailPrice?: string /*日常零售价格*/;
    douyinStoreName?: string /*抖音店铺名称*/;
    douyinStoreRating?: string /*抖音店铺综合评分*/;
    douyinStoreRatingDesc?: string /*抖音店铺综合评分描述*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*修改时间*/;
    gmvAfterCooperation?: string /*合作后GMV*/;
    id?: string /*主键ID*/;
    intendedCooperationPlatforms?: Array<
      'DY' | 'JD' | 'TB' | 'PDD' | 'KS' | 'WECHAT_VIDEO' | 'BAIDU' | 'RED'
    > /*意向合作平台[PlatformSourceEnum]*/;
    intendedCooperationPlatformsDesc?: string /*意向合作平台描述*/;
    modifier?: string /*修改人*/;
    modifierName?: string /*修改人名称*/;
    no?: string /*商机线索编号*/;
    pastCollaboratingStreamers?: string /*过往合作主播*/;
    productCategory?: string /*商品类目*/;
    productCategoryId?: string /*商品类目ID*/;
    productLink?: string /*商品链接*/;
    productName?: string /*商品名称*/;
    productSpecification?: string /*规格/型号*/;
    remarks?: string /*备注信息*/;
    salesAfterCooperation?: string /*合作后销量*/;
    supplierCompanyName?: string /*公司主体名称*/;
    taobaoStoreName?: string /*淘宝店铺名称*/;
    taobaoStoreRating?: string /*淘宝店铺综合评分*/;
    taobaoStoreRatingDesc?: string /*淘宝店铺综合评分描述*/;
    version?: number /*版本号*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *商机线索分页查询
 */
export const businessOpportunityLeadPage = (params: BusinessOpportunityLeadPageRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityLeadPageResult>>(
    '/supplier/public/businessOpportunityLead/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunityLead/page') },
    },
  );
};

export type GenerateBusinessOpportunityRequest = {
  followerId?: string /*跟进人ID*/;
  ids?: Array<string> /*商机线索ID列表*/;
};

export type GenerateBusinessOpportunityResult = boolean;

/**
 *生成商机
 */
export const generateBusinessOpportunity = (params: GenerateBusinessOpportunityRequest) => {
  return Fetch<ResponseWithResult<GenerateBusinessOpportunityResult>>(
    '/supplier/public/businessOpportunityLead/generateBusinessOpportunity',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/supplier/public/businessOpportunityLead/generateBusinessOpportunity'),
      },
    },
  );
};

export type GenerateInviteLinkRequest = any;

export type GenerateInviteLinkResult = string;

/**
 *生成邀请链接
 */
export const generateInviteLink = (params: GenerateInviteLinkRequest) => {
  return Fetch<ResponseWithResult<GenerateInviteLinkResult>>(
    '/supplier/public/businessOpportunityLead/generateInviteLink',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/supplier/public/businessOpportunityLead/generateInviteLink'),
      },
    },
  );
};

export type DeleteBusinessOpportunityLeadRequest = {
  id?: string /*业务ID*/;
};

export type DeleteBusinessOpportunityLeadResult = boolean;

/**
 *删除商机线索
 */
export const deleteBusinessOpportunityLead = (params: DeleteBusinessOpportunityLeadRequest) => {
  return Fetch<ResponseWithResult<DeleteBusinessOpportunityLeadResult>>(
    '/supplier/public/businessOpportunityLead/delete',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunityLead/delete') },
    },
  );
};

export type BatchDeleteBusinessOpportunityLeadRequest = {
  ids?: Array<string> /*ID列表*/;
};

export type BatchDeleteBusinessOpportunityLeadResult = boolean;

/**
 *批量删除商机线索
 */
export const batchDeleteBusinessOpportunityLead = (
  params: BatchDeleteBusinessOpportunityLeadRequest,
) => {
  return Fetch<ResponseWithResult<BatchDeleteBusinessOpportunityLeadResult>>(
    '/supplier/public/businessOpportunityLead/batchDelete',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunityLead/batchDelete') },
    },
  );
};

export type UpdateBusinessOpportunityLeadRequest = {
  brandFinancedStatus?: string /*品牌融资状态：已上市公司、成功融资、暂未融资、我不确定*/;
  brandInvestor?: string /*品牌投资方*/;
  brandName?: string /*品牌名称*/;
  brandType?: string /*品牌类型：品牌方(有商标权)、代理商(有品牌授权)、经销商*/;
  contactMobile?: string /*联系人手机号*/;
  contactPersonName?: string /*联系人姓名*/;
  contactPosition?: string /*职位*/;
  contactWechat?: string /*联系人微信号*/;
  cooperationNetPrice?: string /*合作到手价*/;
  dailyRetailPrice?: string /*日常零售价格*/;
  douyinStoreName?: string /*抖音店铺名称*/;
  douyinStoreRating?: string /*抖音店铺综合评分：4.8分及以上、4.5-4.8分、1.0-4.5分、暂未开店*/;
  gmvAfterCooperation?: string /*合作后GMV*/;
  id?: string /*ID*/;
  intendedCooperationPlatforms?: Array<
    'DY' | 'JD' | 'TB' | 'PDD' | 'KS' | 'WECHAT_VIDEO' | 'BAIDU' | 'RED'
  > /*意向合作平台：淘宝、抖音[PlatformSourceEnum]*/;
  pastCollaboratingStreamers?: string /*过往合作主播*/;
  productCategory?: string /*商品类目*/;
  productCategoryId?: string /*商品类目ID*/;
  productImages?: Array<string> /*商品图片：最多20张*/;
  productLink?: string /*商品链接*/;
  productName?: string /*商品名称*/;
  productSpecification?: string /*规格/型号*/;
  remarks?: string /*备注信息*/;
  salesAfterCooperation?: string /*合作后销量*/;
  supplierCompanyName?: string /*公司主体名称*/;
  taobaoStoreName?: string /*淘宝店铺名称*/;
  taobaoStoreRating?: string /*淘宝店铺综合评分：4.8分及以上、4.5-4.8分、1.0-4.5分、暂未开店*/;
  version?: number /*版本号*/;
};

export type UpdateBusinessOpportunityLeadResult = boolean;

/**
 *更新商机线索
 */
export const updateBusinessOpportunityLead = (params: UpdateBusinessOpportunityLeadRequest) => {
  return Fetch<ResponseWithResult<UpdateBusinessOpportunityLeadResult>>(
    '/supplier/public/businessOpportunityLead/update',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunityLead/update') },
    },
  );
};

export type DetailBusinessOpportunityLeadRequest = {
  id?: string /*商机线索ID*/;
  masked?: boolean /*是否脱敏*/;
};

export type DetailBusinessOpportunityLeadResult = {
  brandFinancedStatus?: string /*品牌融资状态*/;
  brandFinancedStatusDesc?: string /*品牌融资状态描述*/;
  brandInvestor?: string /*品牌投资方*/;
  brandName?: string /*品牌名称*/;
  brandType?: string /*品牌类型*/;
  brandTypeDesc?: string /*品牌类型描述*/;
  contactMobile?: string /*联系人手机号原始信息*/;
  contactMobileMasked?: string /*联系人手机号脱敏信息*/;
  contactPersonName?: string /*联系人姓名*/;
  contactPosition?: string /*职位*/;
  contactWechat?: string /*联系人微信号原始信息*/;
  contactWechatMasked?: string /*联系人微信号脱敏信息*/;
  cooperationNetPrice?: string /*合作到手价*/;
  creator?: string /*创建人*/;
  creatorName?: string /*创建人名称*/;
  dailyRetailPrice?: string /*日常零售价格*/;
  delFlag?: number /*删除标识*/;
  douyinStoreName?: string /*抖音店铺名称*/;
  douyinStoreRating?: string /*抖音店铺综合评分*/;
  douyinStoreRatingDesc?: string /*抖音店铺综合评分描述*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*修改时间*/;
  gmvAfterCooperation?: string /*合作后GMV*/;
  id?: string /*主键ID*/;
  intendedCooperationPlatforms?: Array<
    'DY' | 'JD' | 'TB' | 'PDD' | 'KS' | 'WECHAT_VIDEO' | 'BAIDU' | 'RED'
  > /*意向合作平台[PlatformSourceEnum]*/;
  intendedCooperationPlatformsDesc?: string /*意向合作平台描述*/;
  modifier?: string /*修改人*/;
  modifierName?: string /*修改人名称*/;
  no?: string /*商机线索编号*/;
  pastCollaboratingStreamers?: string /*过往合作主播*/;
  productCategory?: string /*商品类目*/;
  productCategoryId?: string /*商品类目ID*/;
  productImages?: Array<string> /*商品图片*/;
  productLink?: string /*商品链接*/;
  productName?: string /*商品名称*/;
  productSpecification?: string /*规格/型号*/;
  remarks?: string /*备注信息*/;
  salesAfterCooperation?: string /*合作后销量*/;
  supplierCompanyName?: string /*公司主体名称*/;
  taobaoStoreName?: string /*淘宝店铺名称*/;
  taobaoStoreRating?: string /*淘宝店铺综合评分*/;
  taobaoStoreRatingDesc?: string /*淘宝店铺综合评分描述*/;
  version?: number /*版本号*/;
};

/**
 *商机线索详情
 */
export const detailBusinessOpportunityLead = (params: DetailBusinessOpportunityLeadRequest) => {
  return Fetch<ResponseWithResult<DetailBusinessOpportunityLeadResult>>(
    '/supplier/public/businessOpportunityLead/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunityLead/detail') },
    },
  );
};

export type CreateBusinessOpportunityLeadRequest = {
  brandFinancedStatus?: string /*品牌融资状态：已上市公司、成功融资、暂未融资、我不确定*/;
  brandInvestor?: string /*品牌投资方*/;
  brandName?: string /*品牌名称*/;
  brandType?: string /*品牌类型：品牌方(有商标权)、代理商(有品牌授权)、经销商*/;
  contactMobile?: string /*联系人手机号*/;
  contactPersonName?: string /*联系人姓名*/;
  contactPosition?: string /*职位*/;
  contactWechat?: string /*联系人微信号*/;
  cooperationNetPrice?: string /*合作到手价*/;
  creator?: string /*创建人*/;
  dailyRetailPrice?: string /*日常零售价格*/;
  douyinStoreName?: string /*抖音店铺名称*/;
  douyinStoreRating?: string /*抖音店铺综合评分：4.8分及以上、4.5-4.8分、1.0-4.5分、暂未开店*/;
  gmvAfterCooperation?: string /*合作后GMV*/;
  intendedCooperationPlatforms?: Array<
    'DY' | 'JD' | 'TB' | 'PDD' | 'KS' | 'WECHAT_VIDEO' | 'BAIDU' | 'RED'
  > /*意向合作平台：淘宝、抖音[PlatformSourceEnum]*/;
  inviteCode?: string /*邀请码*/;
  pastCollaboratingStreamers?: string /*过往合作主播*/;
  productCategory?: string /*商品类目*/;
  productCategoryId?: string /*商品类目ID*/;
  productImages?: Array<string> /*商品图片：最多20张*/;
  productLink?: string /*商品链接*/;
  productName?: string /*商品名称*/;
  productSpecification?: string /*规格/型号*/;
  remarks?: string /*备注信息*/;
  salesAfterCooperation?: string /*合作后销量*/;
  supplierCompanyName?: string /*公司主体名称*/;
  taobaoStoreName?: string /*淘宝店铺名称*/;
  taobaoStoreRating?: string /*淘宝店铺综合评分：4.8分及以上、4.5-4.8分、1.0-4.5分、暂未开店*/;
};

export type CreateBusinessOpportunityLeadResult = string;

/**
 *创建商机线索
 */
export const createBusinessOpportunityLead = (params: CreateBusinessOpportunityLeadRequest) => {
  return Fetch<ResponseWithResult<CreateBusinessOpportunityLeadResult>>(
    '/supplier/open/businessOpportunityLead/create',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/open/businessOpportunityLead/create') },
    },
  );
};

export type ValidateInviteLinkRequest = {
  inviteCode?: string /*邀请码*/;
};

export type ValidateInviteLinkResult = {
  code?: string /*登录code*/;
  inviteLinkInfo?: {
    expireTime?: string /*过期时间*/;
    fullInviteLink?: string /*完整邀请链接*/;
    inviteCode?: string /*邀请码*/;
    maxUseCount?: number /*最大使用次数*/;
    operatorId?: string /*操作人ID*/;
    operatorName?: string /*操作人姓名*/;
    status?: number /*状态*/;
    statusDesc?: string /*状态描述*/;
    useCount?: number /*已使用次数*/;
  } /*邀请详情*/;
  validResult?: boolean /*验证结果*/;
};

/**
 *验证邀请链接
 */
export const validateInviteLink = (params: ValidateInviteLinkRequest) => {
  return Fetch<ResponseWithResult<ValidateInviteLinkResult>>(
    '/supplier/open/businessOpportunityLead/validateInviteLink',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/open/businessOpportunityLead/validateInviteLink') },
    },
  );
};
