import React from 'react';
import { Descriptions } from './index';
import { DetailBusinessOpportunityLeadResult } from '../service';

interface BrandDetailProps {
  detail: DetailBusinessOpportunityLeadResult;
}

const ShopDetail = ({ detail }: BrandDetailProps) => {
  return (
    <div style={{ padding: '0 16px' }}>
      <Descriptions column={4}>
        <Descriptions.Item label="淘宝店铺名称">
          <span style={{ wordBreak: 'break-all' }}>{detail?.taobaoStoreName || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="淘宝店铺评分">
          {detail?.taobaoStoreRatingDesc || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="抖音店铺名称">
          <span style={{ wordBreak: 'break-all' }}>{detail?.douyinStoreName || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="抖音店铺评分">
          {detail?.douyinStoreRatingDesc || '-'}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default ShopDetail;
