import React, { useEffect } from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { Form, Input, Select, Row, Col } from 'antd';
import { plantformList } from '../../../../web_modules/types';
import { useCode, CODE_ENUM } from '../hooks';

interface IProps {
  form: WrappedFormUtils;
  initLoading?: boolean;
  // detailData?: any;
  onRef?: any;
}

const BrandForm = (props: IProps) => {
  const { form, onRef, initLoading } = props;
  const { getFieldDecorator } = form;

  // 品牌类型
  const {
    codeList: BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE_List = [],
    getEnumList: getBusinessOpportunityLeadBrandTypeList,
  } = useCode(CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE, {
    able: true,
    wait: true,
  });

  const {
    codeList: BUSINESS_OPPORTUNITY_LEAD_BRAND_FINANCING_STATUS_List = [],
    getEnumList: getBusinessOpportunityLeadBrandFinancingStatusList,
  } = useCode(CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_BRAND_FINANCING_STATUS, {
    able: true,
    wait: true,
  });

  useEffect(() => {
    if (initLoading && window.token) {
      getBusinessOpportunityLeadBrandTypeList();
      getBusinessOpportunityLeadBrandFinancingStatusList();
    }
  }, [initLoading]);

  const validatorOrganizationName = (rule: any, value: any, callback: any) => {
    if (value && (value.length > 100 || value.length < 2)) {
      return callback('公司主体名称在2-100个字符之间');
    }

    return callback();
  };

  return (
    <div style={{ padding: '0 12px' }}>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="公司主体名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('supplierCompanyName', {
              rules: [
                {
                  required: true,
                  message: '请输入公司主体名称',
                },
                {
                  validator: validatorOrganizationName,
                },
              ],
            })(<Input maxLength={100} placeholder="请输入公司主体名称" />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="品牌名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('brandName', {
              rules: [
                {
                  required: true,
                  message: '请输入品牌名称',
                },
              ],
            })(<Input maxLength={50} placeholder="请输入品牌名称" />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="品牌类型" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('brandType', {
              rules: [
                {
                  required: true,
                  message: '请选择品牌类型',
                },
              ],
            })(
              <Select placeholder="请选择品牌类型" allowClear labelInValue>
                {BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE_List?.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="品牌是否融资" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('brandFinancedStatus', {
              rules: [],
            })(
              <Select placeholder="请选择品牌是否融资" allowClear labelInValue>
                {BUSINESS_OPPORTUNITY_LEAD_BRAND_FINANCING_STATUS_List?.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="品牌投资方" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('brandInvestor', {
              rules: [
                {
                  required: true,
                  message: '请选择品牌投资方',
                },
              ],
            })(<Input maxLength={50} placeholder="请输入品牌投资方" />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="意向合作平台" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('intendedCooperationPlatforms', {
              rules: [
                {
                  required: true,
                  message: '请选择意向合作平台',
                },
              ],
            })(
              <Select placeholder="请选择意向合作平台" allowClear mode="multiple">
                {plantformList.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12} style={{ marginLeft: '-6px', marginTop: '4px' }}>
          <Form.Item
            label="备注信息"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            style={{ width: 'calc(100% + 6px)' }}
          >
            {getFieldDecorator('remarks', {
              rules: [],
            })(<Input.TextArea maxLength={500} placeholder="请输入备注信息" rows={4} />)}
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default BrandForm;
