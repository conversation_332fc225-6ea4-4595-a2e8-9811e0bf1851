import React from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { Form, Input, Select, Row, Col } from 'antd';
import { checkAuth } from 'qmkit';

interface IProps {
  form: WrappedFormUtils;
  // detailData?: any;
  onRef?: any;
  id?: string;
  detail?: any;
}

const ContentForm = (props: IProps) => {
  const { form, onRef, id, detail } = props;
  const { getFieldDecorator } = form;

  return (
    <div style={{ padding: '0 12px' }}>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="联系人" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('contactPersonName', {
              rules: [{ required: true, message: '请输入联系人' }],
            })(<Input placeholder="请输入联系人" maxLength={50} />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="职位" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('contactPosition', {
              rules: [{ required: true, message: '请输入职位' }],
            })(<Input placeholder="请输入职位" maxLength={50} />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="联系人手机号" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('contactMobile', {
              rules: [{ required: true, message: '请输入联系电话' }],
            })(
              id && !checkAuth('f_business_leads_contact') ? (
                <span>{detail?.contactMobileMasked}</span>
              ) : (
                <Input placeholder="请输入联系电话" maxLength={20} />
              ),
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="联系人微信号" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('contactWechat', {
              rules: [{ required: true, message: '请输入联系人微信号' }],
            })(
              id && !checkAuth('f_business_leads_contact') ? (
                <span>{detail?.contactWechatMasked}</span>
              ) : (
                <Input placeholder="请输入联系人微信号" maxLength={20} />
              ),
            )}
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default ContentForm;
