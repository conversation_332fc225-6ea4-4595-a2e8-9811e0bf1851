import React from 'react';
import { Descriptions } from './index';
import { DetailBusinessOpportunityLeadResult } from '../service';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

interface BrandDetailProps {
  detail: DetailBusinessOpportunityLeadResult;
}

const CooperationDetail = ({ detail }: BrandDetailProps) => {
  return (
    <div style={{ padding: '0 16px' }}>
      <Descriptions column={4}>
        <Descriptions.Item label="过往合作主播">
          <span style={{ wordBreak: 'break-all' }}>
            {detail?.pastCollaboratingStreamers || '-'}
          </span>
        </Descriptions.Item>
        <Descriptions.Item label="合作后销量">
          {isNullOrUndefined(detail?.salesAfterCooperation) ? '-' : detail?.salesAfterCooperation}
        </Descriptions.Item>
        <Descriptions.Item label="合作后的GMV">
          {isNullOrUndefined(detail?.gmvAfterCooperation) ? '-' : detail?.gmvAfterCooperation}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default CooperationDetail;
