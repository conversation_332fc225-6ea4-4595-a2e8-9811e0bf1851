import React, { useMemo } from 'react';
import { Descriptions } from './index';
import { renderQualification } from '@/pages/choice-list-new/components/LegalCheckDrawer/imgUtils';
import { DetailBusinessOpportunityLeadResult } from '../service';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

interface BrandDetailProps {
  detail: DetailBusinessOpportunityLeadResult;
}

const GoodsDetail = ({ detail }: BrandDetailProps) => {
  const productImages = useMemo(() => {
    if (detail?.productImages?.length) {
      return detail?.productImages?.map((item) => ({
        url: item,
      }));
    }
    return [];
  }, [detail]);
  return (
    <div style={{ padding: '0 16px' }}>
      <Descriptions column={4}>
        <Descriptions.Item label="商品名称">
          <span style={{ wordBreak: 'break-all' }}>{detail?.productName || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="规格/型号">
          <span style={{ wordBreak: 'break-all' }}>{detail?.productSpecification || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="商品类目">{detail?.goodsCatePath || '-'}</Descriptions.Item>
        <Descriptions.Item label="商品链接">
          <span style={{ wordBreak: 'break-all' }}>{detail?.productLink || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="日常零售价格">
          {isNullOrUndefined(detail?.dailyRetailPrice) ? '-' : detail?.dailyRetailPrice}
        </Descriptions.Item>
        <Descriptions.Item label="合作到手价">
          {isNullOrUndefined(detail?.cooperationNetPrice) ? '-' : detail?.cooperationNetPrice}
        </Descriptions.Item>
        <Descriptions.Item label="商品图片" span={4}>
          {renderQualification(productImages)}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default GoodsDetail;
