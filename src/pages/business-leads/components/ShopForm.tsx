import React, { useEffect } from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { Form, Input, Select, Row, Col } from 'antd';
import { PLATFORM_ENUM } from '../../../../web_modules/types';
import { useCode, CODE_ENUM } from '../hooks';

interface IProps {
  form: WrappedFormUtils;
  // detailData?: any;
  initLoading?: boolean;
  onRef?: any;
}

const ShopForm = (props: IProps) => {
  const { form, onRef, initLoading } = props;
  const { getFieldDecorator } = form;

  const { intendedCooperationPlatforms } = form.getFieldsValue();

  const {
    codeList: BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE_List = [],
    getEnumList: getBusinessOpportunityLeadTaobaoShopScoreList,
  } = useCode(CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE, {
    able: true,
    wait: true,
  });

  const {
    codeList: BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE_List = [],
    getEnumList: getBusinessOpportunityLeadDouyinShopScoreList,
  } = useCode(CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE, {
    able: true,
    wait: true,
  });

  useEffect(() => {
    if (initLoading && window.token) {
      getBusinessOpportunityLeadTaobaoShopScoreList();
      getBusinessOpportunityLeadDouyinShopScoreList();
    }
  }, [initLoading]);

  return (
    <div style={{ padding: '0 12px' }}>
      <Row gutter={24}>
        {intendedCooperationPlatforms && intendedCooperationPlatforms.includes(PLATFORM_ENUM.TB) && (
          <>
            <Col span={6}>
              <Form.Item label="淘宝店铺名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('taobaoStoreName', {
                  rules: [{ required: true, message: '请输入淘宝店铺名称' }],
                })(<Input placeholder="请输入淘宝店铺名称" maxLength={50} />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="淘宝店铺评分" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('taobaoStoreRating', {
                  rules: [{ required: true, message: '请输入淘宝店铺评分' }],
                })(
                  <Select allowClear placeholder="请选择淘宝店铺评分" labelInValue>
                    {BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE_List?.map((item) => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </>
        )}
        {intendedCooperationPlatforms && intendedCooperationPlatforms.includes(PLATFORM_ENUM.DY) && (
          <>
            <Col span={6}>
              <Form.Item label="抖音店铺名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('douyinStoreName', {
                  rules: [{ required: true, message: '请输入抖音店铺名称' }],
                })(<Input placeholder="请输入抖音店铺名称" maxLength={50} />)}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="抖音店铺评分" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                {getFieldDecorator('douyinStoreRating', {
                  rules: [{ required: true, message: '请输入抖音店铺评分' }],
                })(
                  <Select allowClear placeholder="请选择抖音店铺评分" labelInValue>
                    {BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE_List?.map((item) => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
          </>
        )}
      </Row>
    </div>
  );
};

export default ShopForm;
