import React, { useEffect, useState } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ModalProps } from 'antd/lib/modal';
import { Form, Modal, Select, message, <PERSON><PERSON>, Spin } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import styles from '@/styles/index.module.less';
import { generateInviteLink } from '../service';
import { useRequest } from 'ahooks';
import copy from 'copy-to-clipboard';

interface IProps extends ModalProps {
  // cooperationGuaranteedNo: string;
  // onRefresh: any;
  [key: string]: any;
}

const CreateBusinessLink = (props: IProps) => {
  const { visible, ...rest } = props;

  const [link, setLink] = useState('');

  const { run: generateInviteLinkRun, loading: generateInviteLinkLoading } = useRequest(
    generateInviteLink,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        setLink(res?.result || '');
      },
    },
  );

  const handleCopy = () => {
    if (copy(link)) {
      message.success('复制成功');
      rest.onCancel?.();
    }
  };

  useEffect(() => {
    if (visible) {
      generateInviteLinkRun({});
    }
  }, [visible]);

  return (
    <Modal
      title="生成邀请链接"
      {...rest}
      visible={visible}
      width={500}
      className={styles['modal-sty']}
      maskClosable={false}
      closable={false}
      footer={
        <Button type="primary" onClick={handleCopy}>
          复制
        </Button>
      }
    >
      <Spin spinning={generateInviteLinkLoading}>
        <p style={{ color: '#444444', wordBreak: 'break-all' }}>{link}</p>
        <p style={{ color: '#FAAD14', wordBreak: 'break-all', marginTop: '16px' }}>
          请确保完成复制后关闭弹窗
        </p>
      </Spin>
    </Modal>
  );
};

export default WithToggleModal(CreateBusinessLink);
