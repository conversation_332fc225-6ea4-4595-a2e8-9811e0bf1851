import React from 'react';
import { Descriptions } from './index';
import { DetailBusinessOpportunityLeadResult } from '../service';
import { checkAuth } from 'qmkit';

interface BrandDetailProps {
  detail: DetailBusinessOpportunityLeadResult;
}

const ContentDetail = ({ detail }: BrandDetailProps) => {
  return (
    <div style={{ padding: '0 16px' }}>
      <Descriptions column={4}>
        <Descriptions.Item label="联系人姓名">
          <span style={{ wordBreak: 'break-all' }}>{detail?.contactPersonName || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="职位">{detail?.contactPosition || '-'}</Descriptions.Item>
        <Descriptions.Item label="联系人手机号">
          {checkAuth('f_business_leads_contact')
            ? detail?.contactMobile
            : detail?.contactMobileMasked}
        </Descriptions.Item>
        <Descriptions.Item label="联系人微信号">
          {checkAuth('f_business_leads_contact')
            ? detail?.contactWechat
            : detail?.contactWechatMasked}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default ContentDetail;
