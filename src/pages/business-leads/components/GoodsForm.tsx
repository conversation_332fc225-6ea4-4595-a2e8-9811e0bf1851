import React from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { Form, Input, Select, Row, Col, InputNumber } from 'antd';
import Category from 'web-common-modules/components/Category';
import { FileUploadSouceId } from './index';

interface IProps {
  form: WrappedFormUtils;
  // detailData?: any;
  onRef?: any;
}

const GoodsForm = (props: IProps) => {
  const { form, onRef } = props;
  const { getFieldDecorator } = form;

  return (
    <div style={{ padding: '0 12px' }}>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="商品名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('productName', {
              rules: [{ required: true, message: '请输入商品名称' }],
            })(<Input placeholder="请输入商品名称" maxLength={100} />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="规格/型号" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('productSpecification', {
              rules: [{ required: true, message: '请输入规格/型号' }],
            })(<Input placeholder="请输入规格/型号" maxLength={50} />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="商品类目" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('productCategory', {
              rules: [{ required: true, message: '请选择商品类目' }],
            })(
              <Category
                style={{ width: '100%' }}
                filterOption={false}
                maxTagCount={2}
                allowClear
                mode="multiple"
                placeholder="请先选择平台类型"
                source={'DY'}
                width="100%"
                defaultApi={false}
                needName={true}
              ></Category>,
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="商品链接" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('productLink', {
              rules: [{ required: true, message: '请输入商品链接' }],
            })(<Input placeholder="请输入商品链接" maxLength={200} />)}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="日常零售价格" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('dailyRetailPrice', {
              rules: [{ required: true, message: '请输入日常零售价格' }],
            })(
              <InputNumber
                min={0}
                max={99999999}
                precision={2}
                style={{ width: '100%' }}
                placeholder="请输入日常零售价格"
              />,
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="合作到手价" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('cooperationNetPrice', {
              rules: [{ required: true, message: '请输入合作到手价' }],
            })(
              <InputNumber
                min={0}
                max={99999999}
                precision={2}
                style={{ width: '100%' }}
                placeholder="请输入合作到手价"
              />,
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24} style={{ marginLeft: '-6px', marginTop: '4px' }}>
          <Form.Item label="商品图片" labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
            {getFieldDecorator('productImages', {
              rules: [{ required: true, message: '请上传商品图片' }],
            })(
              <FileUploadSouceId
                maxLen={20}
                typeCode="SPU_QUALIFICATION"
                isImage
                maxSize={20 * 1024 * 1024}
                multiple
                accept={'.jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx'}
                NoticeText={''}
              />,
            )}
            <p style={{ fontSize: '12px', color: '#999', marginTop: '-2px' }}>
              支持png、jpg、jpeg、xls、xlsx、pdf、doc、docx格式，不超过20MB，最多20张。
            </p>
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default GoodsForm;
