import React from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ModalProps } from 'antd/lib/modal';
import { Form, Modal, Select, message } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import styles from '@/styles/index.module.less';
import { useLegal } from '@/pages/certification-audit/hooks';
import { generateBusinessOpportunity } from '../service';
import { useRequest } from 'ahooks';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  // cooperationGuaranteedNo: string;
  onRefresh: any;
  [key: string]: any;
}

const CreateBusiness = (props: IProps) => {
  const { form, visible, onRefresh, ids, type, ...rest } = props;

  const { getFieldDecorator } = form;
  const { roleList, roleRun, roleLoading, handleRoleChange, handelRoleSearch, handleRoleBlur } =
    useLegal(0);

  const { run: generateBusinessOpportunityRun, loading: generateBusinessOpportunityLoading } =
    useRequest(generateBusinessOpportunity, {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('生成商机成功');
        onRefresh();
        rest.onCancel?.();
      },
    });

  const handleOk = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      generateBusinessOpportunityRun({
        ids,
        followerId: values.followerId,
      });
    });
  };

  return (
    <Modal
      title="生成商机"
      {...rest}
      visible={visible}
      width={500}
      className={styles['modal-sty']}
      maskClosable={false}
      confirmLoading={generateBusinessOpportunityLoading}
      onOk={handleOk}
    >
      <p>
        本次已选中
        <span style={{ color: '#204EFF' }}>{ids?.length}</span>条商机线索,是否生成商机
      </p>
      <Form labelAlign="right" labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
        <Form.Item label="跟进人">
          {getFieldDecorator('followerId', {
            // rules: [{ required: true, message: '请选择跟进人' }],
          })(
            <Select
              loading={roleLoading}
              placeholder="请选择"
              allowClear
              showSearch
              filterOption={false}
              onSearch={handelRoleSearch}
              onChange={handleRoleChange}
              onBlur={handleRoleBlur}
            >
              {roleList?.map((item) => (
                <Select.Option key={item.employeeId} value={item.employeeId}>
                  {item.employeeName}-{item.jobNo}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(CreateBusiness));
