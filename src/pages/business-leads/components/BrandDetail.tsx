import React, { useMemo } from 'react';
import { Descriptions } from './index';
import { DetailBusinessOpportunityLeadResult } from '../service';
import { PlantformName, PLATFORM_ENUM } from '../../../../web_modules/types';

interface BrandDetailProps {
  detail: DetailBusinessOpportunityLeadResult;
}

const BrandDetail = ({ detail }: BrandDetailProps) => {
  const intendedCooperationPlatformsDesc = useMemo(() => {
    if (!detail?.intendedCooperationPlatforms) {
      return '-';
    }
    return detail?.intendedCooperationPlatforms
      ?.map((item) => PlantformName[item as PLATFORM_ENUM])
      .join(',');
  }, [detail]);

  return (
    <div style={{ padding: '0 16px' }}>
      <Descriptions column={4}>
        <Descriptions.Item label="公司主体名称">
          <span style={{ wordBreak: 'break-all' }}>{detail?.supplierCompanyName || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="品牌名称">
          <span style={{ wordBreak: 'break-all' }}>{detail?.brandName || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="品牌类型">{detail?.brandTypeDesc || '-'}</Descriptions.Item>
        <Descriptions.Item label="品牌是否融资">
          <span style={{ wordBreak: 'break-all' }}>{detail?.brandFinancedStatusDesc || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="品牌投资方">
          <span style={{ wordBreak: 'break-all' }}>{detail?.brandInvestor || '-'}</span>
        </Descriptions.Item>
        <Descriptions.Item label="意向合作平台">
          {intendedCooperationPlatformsDesc}
        </Descriptions.Item>
        <Descriptions.Item label="备注信息" span={3}>
          <span style={{ wordBreak: 'break-all' }}>{detail?.remarks || '-'}</span>
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default BrandDetail;
