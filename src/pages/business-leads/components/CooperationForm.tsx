import React from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { Form, Input, Select, Row, Col, InputNumber } from 'antd';

interface IProps {
  form: WrappedFormUtils;
  // detailData?: any;
  onRef?: any;
}

const CooperationForm = (props: IProps) => {
  const { form, onRef } = props;
  const { getFieldDecorator } = form;

  return (
    <div style={{ padding: '0 12px' }}>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="过往合作主播" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('pastCollaboratingStreamers', {
              rules: [{ required: true, message: '请选择过往合作主播' }],
            })(<Input placeholder="请输入过往合作主播" maxLength={50} />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="合作后销量" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('salesAfterCooperation', {
              rules: [{ required: true, message: '请选择合作后销量' }],
            })(
              <InputNumber
                min={0}
                max={99999999}
                precision={2}
                style={{ width: '100%' }}
                placeholder="请输入合作后销量"
              />,
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="合作后GMV" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('gmvAfterCooperation', {
              rules: [{ required: true, message: '请选择合作后GMV' }],
            })(
              <InputNumber
                min={0}
                max={99999999}
                precision={2}
                style={{ width: '100%' }}
                placeholder="请输入合作后GMV"
              />,
            )}
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default CooperationForm;
