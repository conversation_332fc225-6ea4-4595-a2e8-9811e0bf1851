import React, { useCallback } from 'react';
import { FormComponentProps } from 'antd/es/form';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { Form } from 'antd';
import { useSearch } from '../hooks';
import moment from 'moment';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

interface IProps extends FormComponentProps {
  onSearch: (value: any) => void;
  getTableHeight?: any;
  clearSelection?: () => void;
}

const SearchForm: React.FC<IProps> = ({ form, onSearch, getTableHeight, clearSelection }) => {
  const { options } = useSearch();
  const onSubmit = useCallback(
    (init?: boolean) => {
      form.validateFields((err, values) => {
        if (!err) {
          const {
            gmtCreated,
            dailyRetailPrice,
            cooperationNetPrice,
            productCategories,
            ...otherValues
          } = values;
          // console.log(
          //   '🚀 ~ SearchForm ~ gmtCreated:',
          //   gmtCreated,
          //   dailyRetailPrice,
          //   cooperationNetPrice,
          // );
          if (gmtCreated?.length) {
            const [start, end] = gmtCreated;
            start && (otherValues.gmtCreatedStart = moment(start).format('YYYY-MM-DD'));
            end && (otherValues.gmtCreatedEnd = moment(end).format('YYYY-MM-DD'));
          }
          if (dailyRetailPrice) {
            const [min, max] = dailyRetailPrice;
            !isNullOrUndefined(min) && (otherValues.dailyRetailPriceMin = min);
            !isNullOrUndefined(max) && (otherValues.dailyRetailPriceMax = max);
          }
          if (cooperationNetPrice) {
            const [min, max] = cooperationNetPrice;
            !isNullOrUndefined(min) && (otherValues.cooperationNetPriceMin = min);
            !isNullOrUndefined(max) && (otherValues.cooperationNetPriceMax = max);
          }
          if (productCategories?.length) {
            otherValues.productCategories = productCategories[productCategories.length - 1];
          }

          onSearch(otherValues);
        }
      });
    },
    [onSearch, form],
  );

  const onReset = () => {
    form.resetFields();
    clearSelection?.();
    onSubmit();
  };

  return (
    <div>
      <SearchFormComponent
        form={form}
        options={options}
        loading={false}
        onSearch={onSubmit}
        onReset={onReset}
        needMore
        getTableHeight={getTableHeight}
      />
    </div>
  );
};

export default Form.create<IProps>()(SearchForm);
