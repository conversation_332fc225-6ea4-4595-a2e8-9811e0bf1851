import { checkAuth } from 'qmkit';

export const formatFormData = (value: any) => {
  const {
    brandFinancedStatus,
    brandType,
    productCategory,
    productImages,
    taobaoStoreRating,
    douyinStoreRating,
    ...otherValue
  } = value;

  const lastproductCategory = productCategory[productCategory?.length - 1];
  const { cateId, cateName } = lastproductCategory || {};
  const params = {
    ...otherValue,
    brandFinancedStatus: brandFinancedStatus?.key,
    brandType: brandType?.key,
    douyinStoreRating: douyinStoreRating?.key,
    productCategoryId: cateId || undefined,
    productCategory: cateName || undefined,
    productImages: productImages?.length ? productImages?.map((item: any) => item?.url) : undefined,
    taobaoStoreRating: taobaoStoreRating?.key,
  };
  return { ...params };
};

export const formatDetailData = (value: any) => {
  const {
    id,
    no,
    version,
    contactMobile,
    contactMobileMasked,
    contactWechat,
    contactWechatMasked,
    productImages,
    productCategoryId,
    productCategory,
    brandFinancedStatus,
    brandFinancedStatusDesc,
    brandType,
    brandTypeDesc,
    douyinStoreRating,
    douyinStoreRatingDesc,
    intendedCooperationPlatformsDesc,
    taobaoStoreRating,
    taobaoStoreRatingDesc,
    goodsCateVOS,
    ...otherValue
  } = value || {};

  return {
    ...otherValue,
    contactMobile: contactMobile,
    contactWechat: contactWechat,
    productImages: productImages?.length
      ? productImages?.map((item: any) => ({
          url: item,
        }))
      : [],
    productCategory: goodsCateVOS,
    brandFinancedStatus: brandFinancedStatus
      ? { key: brandFinancedStatus, label: brandFinancedStatusDesc }
      : undefined,
    brandType: brandType ? { key: brandType, label: brandTypeDesc } : undefined,
    douyinStoreRating: douyinStoreRating
      ? { key: douyinStoreRating, label: douyinStoreRatingDesc }
      : undefined,
    taobaoStoreRating: taobaoStoreRating
      ? { key: taobaoStoreRating, label: taobaoStoreRatingDesc }
      : undefined,
  };
};
