import React from 'react';
import PageLayout from '@/components/PageLayout/index';
import { Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import { Spin, Form, Button } from 'antd';
import styles from './index.module.less';
import { BrandForm, ShopForm, GoodsForm, ContentForm, CooperationForm } from './components';
import { useFormCode } from './hooks';
import { formatFormData } from './utils';
import { cache } from 'qmkit';

const FormPage = (props: any) => {
  const { form } = props;

  const {
    initLoading,
    path,
    handleCancel,
    createBusinessOpportunityLeadLoading,
    createBusinessOpportunityLeadRun,
    updateBusinessOpportunityLeadRun,
    updateBusinessOpportunityLeadLoading,
    id,
    code,
    detail,
  } = useFormCode(form);

  const { intendedCooperationPlatforms } = form.getFieldsValue();

  const handleSubmit = () => {
    form.validateFields((err: any, values: any) => {
      if (err) {
        return;
      }
      const params = formatFormData(values);
      if (code) {
        params.inviteCode = code;
      }
      // 编辑
      if (id) {
        updateBusinessOpportunityLeadRun({ id, ...params, version: detail?.version });
        return;
      }
      if (!code) {
        params.businessIdentity = {
          operatorId: JSON.parse(localStorage.getItem(cache.LOGIN_DATA) || '{}').employeeId,
        };
      }
      createBusinessOpportunityLeadRun(params);
    });
  };

  return (
    <PageLayout className={path === '/business-leads-open' ? styles.formPageContainer : ''}>
      <FormContentLayout>
        <Spin
          spinning={
            !initLoading ||
            createBusinessOpportunityLeadLoading ||
            updateBusinessOpportunityLeadLoading
          }
        >
          {initLoading ? (
            <Form>
              <Card title="品牌信息">
                <BrandForm form={form} initLoading={initLoading} />
              </Card>
              {intendedCooperationPlatforms?.length && (
                <Card title="店铺信息">
                  <ShopForm form={form} initLoading={initLoading} />
                </Card>
              )}
              <Card title="商品信息">
                <GoodsForm form={form} />
              </Card>
              <Card title="联系信息">
                <ContentForm form={form} id={id} detail={detail} />
              </Card>
              <Card title="合作信息">
                <CooperationForm form={form} />
              </Card>
            </Form>
          ) : (
            <></>
          )}
        </Spin>
        <FormBottomCard>
          {initLoading ? (
            <>
              {path !== '/business-leads-open' ? (
                <Button
                  style={{ marginRight: '6px' }}
                  onClick={handleCancel}
                  loading={
                    createBusinessOpportunityLeadLoading || updateBusinessOpportunityLeadLoading
                  }
                >
                  取消
                </Button>
              ) : (
                <></>
              )}
              <Button
                type="primary"
                loading={
                  createBusinessOpportunityLeadLoading || updateBusinessOpportunityLeadLoading
                }
                onClick={handleSubmit}
              >
                保存
              </Button>
            </>
          ) : (
            <></>
          )}
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(FormPage);
