import React, { useCallback, useEffect } from 'react';
import PageLayout from '@/components/PageLayout/index';
import {
  Card,
  FormContentLayout,
  DetailContentLayout,
  DetailTitle,
  SpinCard,
} from '@/components/DetailFormCompoments';
import { Button, message } from 'antd';
import { AuthWrapper, history } from 'qmkit';
import {
  CreateBusiness,
  BrandDetail,
  ShopDetail,
  GoodsDetail,
  ContentDetail,
  CooperationDetail,
} from './components';
import { useFormCode, useDel } from './hooks';

const DetailPage = () => {
  const { detail, refreshDetail } = useFormCode();
  const { handleSingleDel } = useDel(refreshDetail);
  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText="商机线索详情">
          <AuthWrapper functionName="f_business_leads_del">
            <Button
              className="ml-8"
              style={{ borderColor: '#999999', color: '#444444' }}
              onClick={() => handleSingleDel(detail?.id || '')}
            >
              删除
            </Button>
          </AuthWrapper>
          <AuthWrapper functionName="f_business_leads_edit">
            <Button
              className="ml-8"
              style={{ borderColor: '#999999', color: '#444444' }}
              onClick={() => history.push(`/business-leads-edit?id=${detail?.id}`)}
            >
              编辑
            </Button>
          </AuthWrapper>
          <AuthWrapper functionName="f_business_leads_create">
            <CreateBusiness onRefresh={refreshDetail} ids={[detail?.id]}>
              <Button className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                生成商机
              </Button>
            </CreateBusiness>
          </AuthWrapper>
        </DetailTitle>
        <SpinCard spinning={false}>
          <Card title="品牌信息">
            <BrandDetail detail={detail} />
          </Card>
          <Card title="店铺信息">
            <ShopDetail detail={detail} />
          </Card>
          <Card title="商品信息">
            <GoodsDetail detail={detail} />
          </Card>
          <Card title="联系信息">
            <ContentDetail detail={detail} />
          </Card>
          <Card title="合作信息">
            <CooperationDetail detail={detail} />
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default DetailPage;
