import { deleteBusinessOpportunityLead, batchDeleteBusinessOpportunityLead } from '../service';
import { useRequest } from 'ahooks';
import { message, Modal } from 'antd';

export const useDel = (onRefresh: () => void) => {
  const { run: deleteBusinessOpportunityLeadRun, loading: deleteBusinessOpportunityLeadLoading } =
    useRequest(deleteBusinessOpportunityLead, {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        onRefresh();
      },
    });

  const {
    run: batchDeleteBusinessOpportunityLeadRun,
    loading: batchDeleteBusinessOpportunityLeadLoading,
  } = useRequest(batchDeleteBusinessOpportunityLead, {
    manual: true,
    onSuccess: ({ res }) => {
      if (!res?.success) {
        message.error(res?.message || '网络异常');
        return;
      }
      onRefresh();
    },
  });

  const handleDel = (ids: string[]) => {
    if (!ids?.length) {
      message.warning('请先选择商机线索');
      return;
    }
    Modal.confirm({
      title: '提示',
      content: '确定要删除吗？',
      onOk() {
        batchDeleteBusinessOpportunityLeadRun({
          ids,
        });
      },
    });
  };

  const handleSingleDel = (id: string) => {
    Modal.confirm({
      title: '提示',
      content: '确定要删除吗？',
      onOk() {
        deleteBusinessOpportunityLeadRun({
          id,
        });
      },
    });
  };

  return {
    deleteBusinessOpportunityLeadRun,
    deleteBusinessOpportunityLeadLoading,
    batchDeleteBusinessOpportunityLeadRun,
    batchDeleteBusinessOpportunityLeadLoading,
    handleDel,
    handleSingleDel,
  };
};
