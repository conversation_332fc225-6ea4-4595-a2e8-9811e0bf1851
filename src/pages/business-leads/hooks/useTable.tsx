import React from 'react';
import { ColumnProps } from 'antd/lib/table';
import PopoverRowText from '@/components/PopoverRowText';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import moment from 'moment';
import styles from '@/styles/index.module.less';
import { useCode, CODE_ENUM } from '../hooks';
import { PlantformName, PLATFORM_ENUM } from '../../../../web_modules/types';
import { checkAuth, history, AuthWrapper } from 'qmkit';

export const useTable = (onDel: (id: string) => void) => {
  // 品牌类型
  // const { codeEnum: BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE_ENUM } = useCode(
  //   CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE,
  // );

  // 品牌融资状态
  // const { codeEnum: BUSINESS_OPPORTUNITY_LEAD_BRAND_FINANCING_STATUS_ENUM } = useCode(
  //   CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_BRAND_FINANCING_STATUS,
  // );

  // 淘宝店铺综合评分
  // const { codeEnum: BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE_ENUM } = useCode(
  //   CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE,
  // );

  // 抖音店铺综合评分
  // const { codeEnum: BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE } = useCode(
  //   CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE,
  // );
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'index',
      dataIndex: 'index',
      className: styles['table-number'],
      render: (text: any, record: any, index: number) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      dataIndex: 'sortColumn',
    },
    {
      title: '线索编号',
      key: 'no',
      dataIndex: 'no',
      width: 120,
      render: (val: string, record: any) => (
        <a
          onClick={() => {
            history.push(`/business-leads-detail?id=${record.id}`);
          }}
        >
          {val}
        </a>
      ),
    },
    {
      title: '公司主体名称',
      key: 'supplierCompanyName',
      dataIndex: 'supplierCompanyName',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '品牌名称',
      key: 'brandName',
      dataIndex: 'brandName',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '品牌类型',
      key: 'brandTypeDesc',
      dataIndex: 'brandTypeDesc',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '意向合作平台',
      key: 'intendedCooperationPlatformsDesc',
      dataIndex: 'intendedCooperationPlatformsDesc',
      width: 120,
      render: (val: PLATFORM_ENUM, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '抖音店铺名称',
      key: 'douyinStoreName',
      dataIndex: 'douyinStoreName',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '抖音店铺评分',
      key: 'douyinStoreRatingDesc',
      dataIndex: 'douyinStoreRatingDesc',
      width: 140,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '淘宝店铺名称',
      key: 'taobaoStoreName',
      dataIndex: 'taobaoStoreName',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '淘宝店铺评分',
      key: 'taobaoStoreRatingDesc',
      dataIndex: 'taobaoStoreRatingDesc',
      width: 140,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '商品名称',
      key: 'productName',
      dataIndex: 'productName',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '商品类目',
      key: 'goodsCatePath',
      dataIndex: 'goodsCatePath',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '商品链接',
      key: 'productLink',
      dataIndex: 'productLink',
      width: 150,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '日常零售价格(¥)',
      key: 'dailyRetailPrice',
      dataIndex: 'dailyRetailPrice',
      width: 150,
      render: (val: string, record: any) => (!isNullOrUndefined(val) ? val : '-'),
    },
    {
      title: '合作到手价(¥)',
      key: 'cooperationNetPrice',
      dataIndex: 'cooperationNetPrice',
      width: 150,
      render: (val: string, record: any) => (!isNullOrUndefined(val) ? val : '-'),
    },
    {
      title: '联系人',
      key: 'contactPersonName',
      dataIndex: 'contactPersonName',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '联系人手机号',
      key: 'contactMobile',
      dataIndex: 'contactMobile',
      width: 140,
      render: (val: string, record: any) => {
        const contact = checkAuth('f_business_leads_contact') ? val : record?.contactMobileMasked;
        return <PopoverRowText text={contact || '-'} />;
      },
    },
    {
      title: '联系人微信号',
      key: 'contactWechat',
      dataIndex: 'contactWechat',
      width: 140,
      render: (val: string, record: any) => {
        const contact = checkAuth('f_business_leads_contact') ? val : record?.contactWechatMasked;
        return <PopoverRowText text={contact || '-'} />;
      },
    },
    {
      title: '过往合作主播',
      key: 'pastCollaboratingStreamers',
      dataIndex: 'pastCollaboratingStreamers',
      width: 120,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '合作后的销量',
      key: 'salesAfterCooperation',
      dataIndex: 'salesAfterCooperation',
      width: 150,
      render: (val: string, record: any) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '合作后的GMV(¥)',
      key: 'gmvAfterCooperation',
      dataIndex: 'gmvAfterCooperation',
      width: 150,
      render: (val: string, record: any) => (!isNullOrUndefined(val) ? val : '-'),
    },
    {
      title: '创建人',
      key: 'creatorName',
      dataIndex: 'creatorName',
      width: 120,
      render: (val: string, record: any) => val || '-',
    },
    {
      title: '创建时间',
      key: 'gmtCreated',
      dataIndex: 'gmtCreated',
      width: 160,
      render: (val: string, record: any) =>
        isNullOrUndefined(val) ? '-' : moment(val).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      dataIndex: 'action',
      width: 140,
      fixed: 'right',
      render: (val: string, record: any) => {
        return (
          <>
            <AuthWrapper functionName="f_business_leads_edit">
              <a
                style={{ marginRight: '4px' }}
                onClick={() => history.push(`/business-leads-edit?id=${record.id}`)}
              >
                编辑
              </a>
            </AuthWrapper>
            <AuthWrapper functionName="f_business_leads_del">
              <a style={{ color: '#ee0000' }} onClick={() => onDel(record.id)}>
                删除
              </a>
            </AuthWrapper>
          </>
        );
      },
    },
  ];
  return { columns };
};
