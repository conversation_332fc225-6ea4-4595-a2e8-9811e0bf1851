import { useEffect, useState } from 'react';
import { history, cache } from 'qmkit';
import { getQueryParams } from 'web-common-modules/utils/params';
import {
  validateInviteLink,
  createBusinessOpportunityLead,
  updateBusinessOpportunityLead,
  detailBusinessOpportunityLead,
  DetailBusinessOpportunityLeadResult,
} from '../service';
import { message } from 'antd';
import { login } from '@/services/login/index';
import { freshToken } from '@/utils/login';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { useRequest } from 'ahooks';
import { formatDetailData } from '../utils';
import { PLATFORM_ENUM } from '../../../../web_modules/types';

export const useFormCode = (form?: any) => {
  const path = history.location.pathname;
  // console.log('🚀 ~ FormPage ~ path:', path);
  const { id, code } = getQueryParams();

  const [initLoading, setInitLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<DetailBusinessOpportunityLeadResult>({});

  const { delRoutetag } = useCloseAndJump();

  const initPage = async () => {
    if (['/business-leads-add', '/business-leads-edit', '/business-leads-detail'].includes(path)) {
      setInitLoading(true);
      return;
    }
    localStorage.clear();
    const { res } = await validateInviteLink({ inviteCode: code.toString() });
    if (!res?.success) {
      message.error(res?.message || '网络异常');
      return;
    }
    const { code: verifyCode } = res?.result || {};
    const param = {
      verifyCode,
      businessOpportunityCode: code,
      accountType: 5,
      loginSource: 'JGPY',
      loginType: 'BUSINESS_OPPORTUNITY_LEAD_INVITE_LINK',
    };
    const { res: loginRes } = await login({ ...param } as any);
    if (!loginRes?.success) {
      message.error(loginRes?.message || '网络异常');
      return;
    }
    const list = loginRes?.result?.list || [];
    const loginInfo = list?.[0];
    const newlLoginInfo = { ...loginInfo, isSupplierLogin: true };
    loginInfo && localStorage.setItem('jgpy-crm@loginInfo', JSON.stringify(newlLoginInfo));
    freshToken(loginInfo?.token);
    setTimeout(() => {
      setInitLoading(true);
    }, 500);
  };

  const handleCancel = () => {
    delRoutetag();
    if (path === '/business-leads-edit') {
      return history.replace(`/business-leads-detail?id=${id}`);
    }
    history.replace('/business-leads');
  };

  const { run: createBusinessOpportunityLeadRun, loading: createBusinessOpportunityLeadLoading } =
    useRequest(createBusinessOpportunityLead, {
      manual: true,
      onSuccess({ res }) {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('创建成功');
        // if (path === '/business-leads-open') {
        //   form?.resetFields();
        //   return;
        // }
        // handleCancel();
        form?.resetFields();
      },
    });

  const { run: updateBusinessOpportunityLeadRun, loading: updateBusinessOpportunityLeadLoading } =
    useRequest(updateBusinessOpportunityLead, {
      manual: true,
      onSuccess({ res }) {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('编辑成功');
        handleCancel();
      },
    });

  const { run: detailBusinessOpportunityLeadRun, loading: detailBusinessOpportunityLeadLoading } =
    useRequest(detailBusinessOpportunityLead, {
      manual: true,
      onSuccess({ res }) {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        setDetail(res?.result || {});
        if (!form) {
          return;
        }
        const { douyinStoreRating, taobaoStoreRating, taobaoStoreName, douyinStoreName, ...data } =
          formatDetailData(res?.result || {});
        form?.setFieldsValue(data, () => {
          if (data?.intendedCooperationPlatforms?.includes(PLATFORM_ENUM.DY)) {
            form?.setFieldsValue({ douyinStoreRating, douyinStoreName });
          }
          if (data?.intendedCooperationPlatforms?.includes(PLATFORM_ENUM.TB)) {
            form?.setFieldsValue({ taobaoStoreRating, taobaoStoreName });
          }
        });
      },
    });

  const refreshDetail = () => {
    detailBusinessOpportunityLeadRun({ id });
  };

  useEffect(() => {
    if (id) {
      detailBusinessOpportunityLeadRun({ id });
    }
  }, [id]);

  useEffect(() => {
    initPage();
  }, []);

  return {
    initLoading,
    path,
    id,
    handleCancel,
    createBusinessOpportunityLeadRun,
    createBusinessOpportunityLeadLoading,
    updateBusinessOpportunityLeadRun,
    updateBusinessOpportunityLeadLoading,
    detailBusinessOpportunityLeadRun,
    detailBusinessOpportunityLeadLoading,
    detail,
    code,
    refreshDetail,
  };
};
