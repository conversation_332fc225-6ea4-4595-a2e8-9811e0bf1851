import { useState } from 'react';
import { TableRowSelection } from 'antd/es/table/index';
import { message } from 'antd';

export type Item = any;

export const useRowSelection = () => {
  const [selectedRows, setSelectedRows] = useState<Item[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const handleSelectedChane = (selectedRowKeys: string[], sRows: Item[]) => {
    if (selectedRowKeys?.length > 50) {
      message.warning('最多选择50条');
      return;
    }
    setSelectedRowKeys(selectedRowKeys);
    const allRows = [...selectedRows, ...sRows].filter((i) => selectedRowKeys.includes(i.id));
    const allMap = allRows?.reduce((acc: any, cur) => {
      if (acc[cur.id]) {
        return { ...acc };
      } else {
        acc[cur.id] = cur;
        return { ...acc };
      }
    }, {});
    setSelectedRows(Object.values(allMap || {}));
  };

  const rowSelection: TableRowSelection<Item> = {
    selectedRowKeys,
    onChange: handleSelectedChane as any,
    columnWidth: 20,
    // getCheckboxProps: (record) => ({
    //   disabled: record?.status !== 'INSPECTING',
    // }),
  };

  const clearSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  return { rowSelection, selectedRows, selectedRowKeys, clearSelection };
};
