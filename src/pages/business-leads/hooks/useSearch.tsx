import React from 'react';
import { DatePicker, Input, Select } from 'antd';
import { plantformList } from '../../../../web_modules/types';
import Category from 'web-common-modules/components/Category';
import { NumberInterval } from '@/pages/selection-flow-board/components';
import { useCode, CODE_ENUM } from '../hooks';

const { RangePicker } = DatePicker;

export const useSearch = () => {
  const { codeList: brandTypeList } = useCode(CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE);

  const { codeList: taobaoShopScoreList } = useCode(
    CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE,
  );
  const { codeList: douyinShopScoreList } = useCode(
    CODE_ENUM.BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE,
  );

  const options = {
    supplierCompanyName: {
      label: '公司主体名称',
      renderNode: <Input placeholder="请输入" />,
    },
    brandName: {
      label: '品牌名称',
      renderNode: <Input placeholder="请输入" />,
    },
    brandTypes: {
      label: '品牌类型',
      renderNode: (
        <Select allowClear placeholder="请选择" mode="multiple">
          {brandTypeList.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    intendedCooperationPlatforms: {
      label: '意向合作平台',
      renderNode: (
        <Select allowClear placeholder="请选择" mode="multiple">
          {plantformList.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    taobaoStoreName: {
      label: '淘宝店铺名称',
      renderNode: <Input placeholder="请输入" />,
    },
    taobaoStoreRatings: {
      label: '淘宝店铺评分',
      renderNode: (
        <Select allowClear placeholder="请选择" mode="multiple">
          {taobaoShopScoreList.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    douyinStoreName: {
      label: '抖音店铺名称',
      renderNode: <Input placeholder="请输入" />,
    },
    douyinStoreRatings: {
      label: '抖音店铺评分',
      renderNode: (
        <Select allowClear placeholder="请选择" mode="multiple">
          {douyinShopScoreList.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    productName: {
      label: '商品名称',
      renderNode: <Input placeholder="请输入" />,
    },
    productCategories: {
      label: '商品类目',
      renderNode: (
        <Category
          style={{ width: '100%' }}
          filterOption={false}
          maxTagCount={2}
          allowClear
          mode="multiple"
          placeholder="请选择商品类目"
          source={'DY'}
          width="100%"
          defaultApi={false}
        ></Category>
      ),
    },
    dailyRetailPrice: {
      label: '日常零售价格',
      hocOptions: {
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!value) {
                return callback();
              }
              const [min, max] = value;
              if (!min && !max) {
                return callback();
              }
              if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                return callback('请输入0-99999999的数字');
              }
              if ((min && Number(min) > 99999999) || (max && Number(max) > 99999999)) {
                return callback('请输入0-99999999的数字');
              }
              if (min && max && Number(min) > Number(max)) {
                return callback('最低值应该小于最高值');
              }
              callback();
            },
          },
        ],
      },
      renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
    },
    cooperationNetPrice: {
      label: '合作到手价',
      hocOptions: {
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!value) {
                return callback();
              }
              const [min, max] = value;
              if (!min && !max) {
                return callback();
              }
              if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                return callback('请输入0-99999999的数字');
              }
              if ((min && Number(min) > 99999999) || (max && Number(max) > 99999999)) {
                return callback('请输入0-99999999的数字');
              }
              if (min && max && Number(min) > Number(max)) {
                return callback('最低值应该小于最高值');
              }
              callback();
            },
          },
        ],
      },
      renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
    },
    pastCollaboratingStreamers: {
      label: '过往合作主播',
      renderNode: <Input placeholder="请输入" />,
    },
    creator: {
      label: '创建人',
      renderNode: <Input placeholder="请输入" />,
    },
    gmtCreated: {
      label: '创建时间',
      renderNode: <RangePicker />,
    },
  };
  return { options };
};
