import React, { useEffect } from 'react';
import PageLayout from '@/components/PageLayout';
import style from '@/styles/index.module.less';
import styles from './index.module.less';
import { useTableHeight } from '@/common/constants/hooks/index';
import { SearchForm, CreateBusiness, CreateBusinessLink } from './components';
import { Table, Button, message } from 'antd';
import { useTable, useRowSelection, useList, APIKEY, useDel } from './hooks';
import PaginationProxy from '@/common/constants/Pagination';
import { AuthWrapper, history } from 'qmkit';
import { BusinessOpportunityLeadPageRequest, BusinessOpportunityLeadPageResult } from './service';
import SortColumnTable from '@/components/SortColumnTable';
import { SortBizTypeEnum } from '@/components/SortColumnTable/type';

const BusinessLeads: React.FC = () => {
  const { tableHeight, getHeight } = useTableHeight(60);

  const { selectedRows, selectedRowKeys, clearSelection, rowSelection } = useRowSelection();

  const { dataSource, pagination, onPageChange, onSearch, onRefresh, loading } = useList<
    BusinessOpportunityLeadPageRequest,
    BusinessOpportunityLeadPageResult
  >(APIKEY.BUSINESS_OPPORTUNITY_LEAD_PAGE);

  const {
    handleDel,
    batchDeleteBusinessOpportunityLeadLoading,
    handleSingleDel,
    deleteBusinessOpportunityLeadLoading,
  } = useDel(() => {
    onRefresh();
    clearSelection();
  });

  const handleDelSingle = (id: string) => {
    handleSingleDel(id);
  };

  const { columns } = useTable(handleDelSingle);

  useEffect(() => {
    onSearch({});
  }, []);

  return (
    <PageLayout className={styles.businessLeadsContainer}>
      <div
        className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
        style={{ display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            onSearch={onSearch}
            getTableHeight={getHeight}
            clearSelection={clearSelection}
          />
          <div className={style.btnGroup} style={{ marginBottom: '12px' }}>
            <AuthWrapper functionName="f_business_leads_add">
              <Button
                type="primary"
                icon="plus"
                onClick={() => {
                  history.push('/business-leads-add');
                }}
              >
                新建
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_business_leads_create">
              <CreateBusiness
                onRefresh={onRefresh}
                ids={selectedRowKeys}
                beforeClick={(cb: any) => {
                  if (!selectedRowKeys?.length) {
                    message.warning('请先选择商机线索');
                    return false;
                  }
                  cb();
                }}
              >
                <Button className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  生成商机
                </Button>
              </CreateBusiness>
            </AuthWrapper>
            <AuthWrapper functionName="f_business_leads_create_link">
              <CreateBusinessLink>
                <Button className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  生成邀请链接
                </Button>
              </CreateBusinessLink>
            </AuthWrapper>
            <AuthWrapper functionName="f_business_leads_del">
              <Button
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={() => {
                  handleDel(selectedRowKeys);
                }}
                loading={batchDeleteBusinessOpportunityLeadLoading}
              >
                删除
              </Button>
            </AuthWrapper>
          </div>
        </div>
        <div style={{ flex: 1 }}>
          <SortColumnTable
            disSortColumns={['index']}
            disabledColumns={['sortColumn', 'no', 'action']}
            rowKey="id"
            columns={columns}
            dataSource={dataSource as any}
            pagination={false}
            scroll={{ y: tableHeight, x: '100%' }}
            rowSelection={rowSelection}
            bizType={SortBizTypeEnum.BUSINESS_OPPORTUNITY_LEAD_PAGE}
            rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
            loading={
              loading ||
              batchDeleteBusinessOpportunityLeadLoading ||
              deleteBusinessOpportunityLeadLoading
            }
          />
          {/* <Table
            columns={columns}
            pagination={false}
            dataSource={dataSource as any}
            rowKey={(record: { [key: string]: any }) => `${record.id}`}
            rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
            scroll={{ y: tableHeight, x: '100%' }}
            loading={
              loading ||
              batchDeleteBusinessOpportunityLeadLoading ||
              deleteBusinessOpportunityLeadLoading
            }
            rowSelection={rowSelection}
          /> */}
        </div>
        <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
          <PaginationProxy
            {...pagination}
            onChange={({ current, size }: any) => {
              // console.log('🚀 ~ SelectionFlowBoard ~ res:', res);
              onPageChange(current, size);
            }}
            valueType="merge"
          />
        </div>
      </div>
    </PageLayout>
  );
};

export default BusinessLeads;
