import React from 'react';
import BaseExportList from 'web-common-modules/biz/BaseExportList';
import { getUrlParam } from 'web-common-modules/utils';
import './index.less';
import { Const } from 'qmkit';
import { title } from 'process';
import { Button } from 'antd';
import OSSUpload from '@/components/OSSUpload';
import { baseExportListColumn } from 'web-common-modules/biz/BaseExportList/index';
const downLoadPdfColumn = () => {
  const exportListColumn = Object.values(baseExportListColumn);
  //下载报表
  const downloadReport = (record) => async () => {
    console.log('downloading----');
    const { resourceId, taskDesc } = record;
    const urlMap = await OSSUpload.getResourceUrls([resourceId]);
    console.log(urlMap, '0-0-0-');
    if (urlMap[resourceId]) {
      window.open(urlMap[resourceId]);
    }
  };
  console.log(exportListColumn, '深度拷贝');
  exportListColumn[3] = {
    title: '操作',
    dataIndex: 'action',
    align: 'left',
    width: 60,
    render: (_, record) => {
      return [
        record.state == 'UPLOAD_FILE_FINISH' ? (
          <Button
            key="download"
            type="link"
            onClick={downloadReport(record)}
            style={{ paddingLeft: 0 }}
          >
            下载
          </Button>
        ) : null,
      ];
    },
  };
  return exportListColumn;
};
const CONFIG = {
  QUALIFICATION_AUDIT_RECORD: {
    functionName: 'f_legal_audit_export_list',
    title: '审核记录导出记录',
    className: '',
    conditionProps: {
      configCode: 'QUALIFICATION_AUDIT_RECORD',
    },
  },
  WORK_ORDER: {
    functionName: 'f_work-order_export',
    title: '审核记录导出记录',
    className: '',
    conditionProps: {
      configCode: 'WORK_ORDER',
    },
  },
  BUSINESS_SUBMIT_RATE_EXPORT_CODE: {
    functionName: 'f_choice_list_board_export_log',
    title: '数据看板商务导出记录',
    className: '',
    conditionProps: {
      configCode: 'BUSINESS_SUBMIT_RATE_EXPORT_CODE',
    },
  },
  SINGLE_SUBMIT_RATE_EXPORT_CODE: {
    functionName: 'f_choice_list_board_export_log',
    title: '数据看板单场导出记录',
    className: '',
    conditionProps: {
      configCode: 'SINGLE_SUBMIT_RATE_EXPORT_CODE',
    },
  },
  OPERATOR_BOARD_DY_EXPORT_CODE: {
    functionName: 'f_operation_process_board_export_logs',
    title: '运营看板记录',
    className: '',
    conditionProps: {
      configCode: 'OPERATOR_BOARD_DY_EXPORT_CODE',
      configCodeList: [
        'OPERATOR_BOARD_DY_EXPORT_CODE',
        'OPERATOR_BOARD_TB_EXPORT_CODE',
        'OPERATOR_BOARD_DYNAMIC_EXPORT',
      ],
    },
  },
  SELECT_SPU: {
    functionName: 'f_live_round_list',
    title: '定品导出记录',
    className: '',
    conditionProps: {
      configCode: 'SELECT_SPU',
    },
  },
  SELECT_SPU_ALL: {
    functionName: 'f_choice_list',
    title: '选品导出记录',
    className: '',
    conditionProps: {
      configCode: 'SELECT_SPU_ALL',
    },
  },
  DEFINE_SPU_PERSON: {
    functionName: 'f_crm_live_streamed_export',
    title: '已直播场次商品导出记录',
    className: '',
    conditionProps: {
      configCode: 'DEFINE_SPU_PERSON',
    },
  },
  DEFINE_SPU: {
    functionName: 'f_crm_live_streamed_export',
    title: '已直播场次人员导出记录',
    className: '',
    conditionProps: {
      configCode: 'DEFINE_SPU',
    },
  },
  OPERATOR_LIVE_PREPARE_EXPORT: {
    functionName: 'f_export_logs',
    title: '场次货盘导出记录',
    className: '',
    conditionProps: {
      configCode: 'OPERATOR_LIVE_PREPARE_EXPORT',
    },
  },
  ADVANCE_PAYMENT_CONTRACT: {
    functionName: 'f_choice_list_contract_export_record',
    title: '预付款单合同导出记录',
    className: '',
    conditionProps: {
      configCode: 'ADVANCE_PAYMENT_CONTRACT',
      configCodes: ['ADVANCE_PAYMENT_CONTRACT', 'ELECTRON_ADVANCE_PAYMENT_ORDER'],
    },
  },
  FLOW_BOARD_EXPORT: {
    functionName: 'f_selection_flow_board_export_logs',
    title: '选品流程看板导出',
    className: '',
    conditionProps: {
      configCode: 'FLOW_BOARD_EXPORT',
    },
  },
  NON_LIVE_SELECTION_ROUND_EXPORT: {
    functionName: 'f_selection_flow_board_export_logs',
    title: '非直播流程看板导出',
    className: '',
    conditionProps: {
      configCode: 'NON_LIVE_SELECTION_ROUND_EXPORT',
    },
  },
  COOPERATION_ADJUST_ORDER: {
    functionName: 'f_op_coop_adjust_export_list',
    title: '合作调整单导出记录',
    className: '',
    conditionProps: {
      configCode: 'COOPERATION_ADJUST_ORDER',
    },
  },
  COOP_GUARANTEED_EXPORT: {
    functionName: 'f_quality-assurance-cooperation_export_log',
    title: '保量管理导出记录',
    className: '',
    conditionProps: {
      configCode: 'COOP_GUARANTEED_EXPORT',
      configCodeList: ['COOP_GUARANTEED_EXPORT'],
    },
  },
  HIGH_RISK_AUDIT_EXPORT: {
    functionName: 'f_high_risk_records_export_logs',
    title: '特批日志导出记录',
    className: '',
    conditionProps: {
      configCode: 'HIGH_RISK_AUDIT_EXPORT',
    },
  },
  COOPERATION_FRAMEWORK_BILL: {
    functionName:
      'f_cooperation_order_bill_download_List,f_op_frame_coop_batchExport,f_jg_frame_coop_batchExport',
    title: '账单导出记录',
    className: '',
    conditionProps: {
      configCode: 'COOPERATION_FRAMEWORK_BILL',
      configCodes: [
        'COOPERATION_FRAMEWORK_BILL',
        'LIVE_ROUND_FRAMEWORK_BILL',
        'SECTION_FRAMEWORK_BILL',
        'BATCH_BILL_ORDER',
        'EXTRA_REWARD_BILL',
      ],
    },
  },
  COOPERATION_ORDER: {
    functionName: 'f_op_order_export_list',
    title: '合作订单导出记录',
    className: '',
    conditionProps: {
      configCode: 'COOPERATION_ORDER',
    },
  },
  //1.1.1 下载服务单
  ELECTRON_COOPERATION_SERVICE_ORDER: {
    functionName: 'f_cooperation_service_order_download_List',
    title: '服务单导出记录',
    className: '',
    conditionProps: {
      configCode: 'ELECTRON_COOPERATION_SERVICE_ORDER',
    },
    columns: downLoadPdfColumn(),
  },
  LIVE_SCHEDULE_EXPORT: {
    functionName: 'f_anthor_duration_export_record',
    title: '主播时长管理导出记录',
    className: '',
    conditionProps: {
      configCode: 'LIVE_SCHEDULE_EXPORT',
    },
  },
  LIVE_GOODS_BOARD_SOURCE_EXPORT: {
    functionName: 'f_go-on-board_export_record',
    title: '上播商品导出记录',
    className: '',
    conditionProps: {
      configCode: 'LIVE_GOODS_BOARD_SOURCE_EXPORT',
    },
  },
  FINANCE_CONTRACTS_EXPORT: {
    functionName: 'f_expend_contract_export_log',
    title: '支出合同台账',
    className: '',
    conditionProps: {
      configCode: 'FINANCE_CONTRACTS_EXPORT',
      // configCodeList: ['FINANCE_CONTRACTS_EXPORT'],
    },
  },
  MARKETING_DATA: {
    functionName: 'f_marketing_data_export_log',
    title: '营销数据导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_DATA',
    },
  },
  MARKETING_EXPENSE_WPBT: {
    functionName: 'f_marketing_expenses_export',
    title: '营销费用导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_EXPENSE_WPBT',
    },
  },
  MARKETING_EXPENSE_XCFY: {
    functionName: 'f_marketing_expenses_export',
    title: '营销费用导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_EXPENSE_XCFY',
    },
  },
  MARKETING_EXPENSE_XCHY: {
    functionName: 'f_marketing_expenses_export',
    title: '营销费用导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_EXPENSE_XCHY',
    },
  },
  MARKETING_EXPENSE_LLTF: {
    functionName: 'f_marketing_expenses_export',
    title: '营销费用导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_EXPENSE_LLTF',
    },
  },
  MARKETING_EXPENSE_CJDS: {
    functionName: 'f_marketing_expenses_export',
    title: '营销费用导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_EXPENSE_CJDS',
    },
  },
  MARKETING_EXPENSE_YRCB: {
    functionName: 'f_marketing_expenses_export',
    title: '营销费用导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_EXPENSE_YRCB',
    },
  },
  MARKETING_EXPENSE_DSP: {
    functionName: 'f_marketing_expenses_export',
    title: '营销费用导出记录',
    className: '',
    conditionProps: {
      configCode: 'MARKETING_EXPENSE_DSP',
    },
  },
  COST_SETTLEMENT_EXPORT: {
    functionName: 'NO_NEED',
    title: '结账明细导出记录',
    className: '',
    conditionProps: {
      configCode: 'COST_SETTLEMENT_EXPORT',
    },
  },
  COST_SUMMARY_EXPORT: {
    functionName: 'NO_NEED',
    title: '结账汇总导出记录',
    className: '',
    conditionProps: {
      configCode: 'COST_SUMMARY_EXPORT',
    },
  },
  LIVE_BUSINESS_ORDER_EXPORT: {
    functionName: 'f_anchor_commercial_export_list',
    title: '主播商单导出记录',
    className: '',
    conditionProps: {
      configCode: 'LIVE_BUSINESS_ORDER_EXPORT',
    },
  },
  LIVE_REWARDS_PUNISHMENT_EXPORT: {
    functionName: 'f_anchor_performance_export_list',
    title: '主播奖惩导出记录',
    className: '',
    conditionProps: {
      configCode: 'LIVE_REWARDS_PUNISHMENT_EXPORT',
    },
  },
  SELECT_POOL_DATA_EXPORT: {
    functionName: 'f_export-list-highrisk-approval',
    title: '选品池导出记录',
    className: '',
    conditionProps: {
      configCode: 'SELECT_POOL_DATA_EXPORT',
    },
  },
  LIVE_ANCHOR_INCOME_TAX_EXPORT: {
    functionName: 'f_anchror_person_tax_export',
    title: '主播时长管理导出记录',
    className: '',
    conditionProps: {
      configCode: 'LIVE_ANCHOR_INCOME_TAX_EXPORT',
    },
  },
  DATA_BOARD_PROJECT_GROUP_SERVICE_TYPE_EXPORT: {
    functionName: 'f_choice_list_board_export_log',
    title: '各项目组服务类型分析导出记录',
    className: '',
    conditionProps: {
      configCode: 'DATA_BOARD_PROJECT_GROUP_SERVICE_TYPE_EXPORT',
    },
  },
  SPOKEN_SCRIPT_BATCH_EXPORT: {
    functionName: 'f_choice_list_batch_download_export',
    title: '口播稿导出记录',
    className: '',
    conditionProps: {
      configCode: 'SPOKEN_SCRIPT_BATCH_EXPORT',
    },
  },
  BAOLIANG_TOTAL_EXPORT: {
    functionName: 'f_quality-assurance-plan-statistics_total_export',
    title: '保量计划汇总导出记录',
    className: '',
    conditionProps: {
      configCode: 'BAOLIANG_TOTAL_EXPORT',
    },
  },
  BAOLIANG_DETAIL_EXPORT: {
    functionName: 'f_quality-assurance-plan-statistics_detail-export',
    title: '保量计划明细导出记录',
    className: '',
    conditionProps: {
      configCode: 'BAOLIANG_DETAIL_EXPORT',
    },
  },
  COOP_GUARANTEED_REL_EXPORT: {
    functionName: 'f_quality-assurance-cooperation-detail-export',
    title: '保量详情导出记录',
    className: '',
    conditionProps: {
      configCode: 'COOP_GUARANTEED_REL_EXPORT',
    },
  },
  LEGAL_WHITE_DYNAMIC_EXPORT: {
    functionName: 'f_qualification-whitelist_supplier_export_logs',
    title: '资质白名单管理导出记录',
    className: '',
    conditionProps: {
      configCode: 'LEGAL_WHITE_DYNAMIC_EXPORT',
    },
  },
  EXPENSE_ORDER_COST_SUMMARY_EXPORT: {
    functionName: 'f_summary-costs_list',
    title: '成本汇总表导出记录',
    className: '',
    conditionProps: {
      configCode: 'EXPENSE_ORDER_COST_SUMMARY_EXPORT',
    },
  },
  WORK_ORDER_USER_FILES_REAL_INFORMATION: {
    functionName: 'f_userProfile_tab_export',
    title: '用户档案导出记录',
    className: '',
    conditionProps: {
      configCode: 'WORK_ORDER_USER_FILES_REAL_INFORMATION',
    },
  },
  WORK_ORDER_USER_FILES_SPECIAL_USER: {
    functionName: 'f_special_user_file_export',
    title: '特殊用户导出记录',
    className: '',
    conditionProps: {
      configCode: 'WORK_ORDER_USER_FILES_SPECIAL_USER',
    },
  },
  WORK_ORDER_USER_FILES_BLACKLIST: {
    functionName: 'f_user_balck_list_tab-export',
    title: '黑名单导出记录',
    className: '',
    conditionProps: {
      configCode: 'WORK_ORDER_USER_FILES_BLACKLIST',
    },
  },
  WORK_ORDER_USER_FILES_WHITELIST: {
    functionName: 'f_user_white_list_tab-export',
    title: '白名单导出记录',
    className: '',
    conditionProps: {
      configCode: 'WORK_ORDER_USER_FILES_WHITELIST',
    },
  },
  LIVE_ROOM_INFO_CHANGE_EXPORT: {
    functionName: 'f_open_close_live_room_export',
    title: '直播间导出记录',
    className: '',
    conditionProps: {
      configCode: 'LIVE_ROOM_INFO_CHANGE_EXPORT',
      configCodeList: ['LIVE_ROOM_INFO_CHANGE_EXPORT', 'LIVE_ROOM_INFO_EXPORT'],
    },
  },
  LIVE_ANCHOR_INFO_EXPORT: {
    functionName: 'f-anchor-info-export',
    title: '主播信息导出记录',
    className: '',
    conditionProps: {
      configCode: 'LIVE_ANCHOR_INFO_EXPORT',
    },
  },
  SPECIAL_AUDIT_QUOTA_RELEASE_RATE_EXPORT: {
    functionName: 'f_choice_list_board_export_log',
    title: '导出记录',
    className: '',
    conditionProps: {
      configCode: 'SPECIAL_AUDIT_QUOTA_RELEASE_RATE_EXPORT',
    },
  },
  BP_STATISTICS_EXPORT: {
    functionName: 'f_choice_list_board_export_log',
    title: '导出记录',
    className: '',
    conditionProps: {
      configCode: 'BP_STATISTICS_EXPORT',
    },
  },
  SUPPLIER_EXPORT: {
    functionName: 'f_provider_list_export_log',
    title: '客商管理导出记录',
    className: '',
    conditionProps: {
      configCode: 'SUPPLIER_EXPORT',
    },
  },
  TREND_PREDICTION_PRODUCT_EXPORT: {
    functionName: 'f_explosive-product-trend-prediction_export_logs',
    title: '爆品趋势预测导出记录',
    className: '',
    conditionProps: {
      configCode: 'TREND_PREDICTION_PRODUCT_EXPORT',
    },
  },
  EMPLOYEE_ATTENDANCE_EXPORT: {
    functionName: 'f_attendance_manage_day_export_log',
    title: '考勤管理导出记录',
    className: '',
    conditionProps: {
      configCode: 'EMPLOYEE_ATTENDANCE_EXPORT',
    },
  },
};

const ExportList = () => {
  const configCode = getUrlParam('configCode') as keyof typeof CONFIG;
  console.log('🚀 ~ ExportList ~ configCode:', configCode);
  if (!configCode) return null;

  const props = CONFIG[configCode];
  console.log('🚀 ~ ExportList ~ props:', props, Const.SYS_TYPE);
  return (
    <div className="crm-export-list-container">
      <BaseExportList {...props} showHeader={false} />
    </div>
  );
};

export default ExportList;
