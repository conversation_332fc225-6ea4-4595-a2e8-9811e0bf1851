import React, { useContext, useEffect, useImperativeHandle, useState } from 'react';
import { Input, Button, Cascader, DatePicker, Affix } from 'antd';
import { listAllLiveRoom, liveTimeList, liveTimeCountList } from '@/services/yml/choiceList/index';
import moment, { Moment } from 'moment';
import { FlowStatus } from '@/common/constants/moduleConstant';
import List from './List';

import Styles from '../../index.module.less';
import '../../index.less';
import linkImg from '@/assets/link-image.png';
import { PageContext } from '../..';
import ChoiceListContext from '../../dataSouce/context';
const { RangePicker } = DatePicker;
interface PropsType {
  getLiveRoundId?: any;
  liveRoundInfo?: any;
  getListValue?: any;
  // 其他属性
}
const ChoiceListLeft = (props: PropsType) => {
  const context = useContext(PageContext);
  const { getLiveRoundId, liveRoundInfo, isExpand, getListValue } = props;
  // 页面初始化
  useEffect(() => {
    getListAllLiveRoom();
    setDate([moment(), moment().add(7, 'days')]);
  }, []);

  // 直播间列表
  const [options, setOptions] = useState<TreeParams[]>([]);
  interface TreeParams {
    label: string;
    key: string;
    platform?: string;
    children: Array<{ label: string; key: string; value: string; platform: string }>;
  }
  //获取直播间的列表
  const getListAllLiveRoom = () => {
    listAllLiveRoom({ isVisible: true })
      .then((res) => {
        const resp = res?.res;
        if (resp.code == '200') {
          const arr: TreeParams[] = [];
          resp?.result?.treeDataList?.forEach((item: any) => {
            item.label = item.deptName;
            item.value = item.deptId;
            item.children = item?.liveRoomList;
            item.children?.forEach((t) => {
              t.label = t.liveRoomName;
              t.value = t.liveRoomId;
            });
            const obj = {
              label: item.label,
              value: item.value,
              key: item.value,
              children: item.children,
            };
            arr.push(obj);
          });
          setOptions(arr);
          // 设置直播间id

          // console.log('arr', arr);
          setTimeout(() => {
            const id: Array<string> = [];
            arr?.forEach((i) => {
              if (i.children && i.children !== null) {
                if (i.children?.length && !id.length) {
                  id.push(i?.value);
                  id.push(i.children[0]?.value);
                  setLivedId(id);
                  if (id[1]) {
                    context.setLiveRoomId && context.setLiveRoomId(id[1]);
                  }
                  if (id[0]) {
                    context.setDeptId && context.setDeptId(id[0]);
                  }
                }
              }
            });
            // console.log('id', id);
          }, 500);
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };
  // 直播id
  const [liveId, setLivedId] = useState([]);
  // 日期
  const [date, setDate] = useState<[Moment | null, Moment | null]>([null, null]);
  const onChangeDate = (value) => {
    // console.log(value);
    setDate(value);
  };
  const handleLiveIdChange = (e: string) => {
    if (e?.length) {
      context.setDeptId && context.setDeptId(e[0]);
      context.setLiveRoomId && context.setLiveRoomId(e[1]);
      context.setPlatform &&
        context.setPlatform(
          options?.find((item) => item.key === e[0])?.children?.find((item) => item.value === e[1])
            ?.platform as string,
        );
    }
    setLivedId(e);
  };
  useEffect(() => {
    getLiveRoundId();
    getLiveTime();
    handleLiveIdChange(liveId);
  }, [liveId, date]);
  //  获取场次列表
  const [livelist, setLivelist] = useState([]);
  //请求场次列表后 用场次id去请求场次的统计数据。
  const getLiveTime = () => {
    let liveRoomName = '';
    let liveRoomIdList: string[] = [];
    options.forEach((item) => {
      item.children &&
        item.children.forEach((i: any) => {
          if (i?.liveRoomId === liveId[1]) {
            liveRoomName = i.liveRoomName;
            liveRoomIdList = [i.liveRoomId];
          }
        });
    });
    const params = {
      startLiveDate: date.length ? date[0]?.format('YYYY-MM-DD') + ' 00:00:00' : '',
      endLiveDate: date.length ? date[1]?.format('YYYY-MM-DD') + ' 23:59:59' : '',
      liveRoomIdList,
      // liveRoomName,
    };
    liveTimeList(params)
      .then((res) => {
        // console.log('场次列表', res);
        if (res?.res?.code === '200') {
          setLivelist([]);
          // 去获取场次的统计数据
          liveRoomIdList.length && getLiveCountList(res?.res?.result);
        }
      })
      .catch((err: any) => {
        // console.log('场次列表err', err);
      });
  };
  //获取统计数据的接口
  const getLiveCountList = (value: Array<any>) => {
    if (value.length > 0) {
      const arr = value;

      liveTimeCountList({ liveRoundIdlist: value.map((item) => Number(item.id)) }).then((res) => {
        if (res?.res?.code === '200') {
          // console.log('统计', res?.res?.result);
          res?.res?.result.forEach((i) => {
            //组装有数据的
            arr?.forEach((t) => {
              if (i.liveRoundId === t.id) {
                Object.keys(FlowStatus).forEach((key) => {
                  // console.log(key);
                  t[key] = i.statusCountMap?.[key] || 0;
                });
              }
            });
          });
          setLivelist(arr);
        }
      });
    }
  };
  const filter = (inputValue, path) => {
    return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  };
  return (
    <div
      className={Styles.choiceListLeft}
      style={
        !isExpand
          ? { transition: '0.2s', width: '300px' }
          : { transition: '0.2s', width: '0px', padding: '0px' }
      }
    >
      <div
        style={{
          marginBottom: '8px',
          // paddingTop: '16px',
          width: '268px',
        }}
      >
        <Input.Group compact style={{ width: '100%' }}>
          <div className="left-group-title">
            直播间<span style={{ opacity: 0 }}>日</span>
          </div>
          <Cascader
            style={{ width: '203px' }}
            value={liveId}
            showSearch={{ filter }}
            onChange={(e) => {
              if (e?.length) {
                context.setDeptId && context.setDeptId(e[0]);
                context.setLiveRoomId && context.setLiveRoomId(e[1]);
                context.setPlatform &&
                  context.setPlatform(
                    options
                      ?.find((item) => item.key === e[0])
                      ?.children?.find((item) => item.value === e[1])?.platform as string,
                  );
              }
              setLivedId(e);
            }}
            allowClear={false}
            options={options}
          />
        </Input.Group>
        <Input.Group compact style={{ width: '100%', marginTop: '8px' }}>
          <div className="left-group-title">直播日期</div>
          <RangePicker
            value={date}
            style={{ width: '203px' }}
            ranges={{
              今天: [moment(), moment()],
              七天: [moment(), moment().add(6, 'days')],
              三十天: [moment(), moment().add(30, 'days')],
            }}
            onChange={onChangeDate}
          />
        </Input.Group>
        {/* 直播日期 */}
      </div>

      <List
        list={livelist}
        liveRoundInfo={liveRoundInfo}
        getLiveRoundId={getLiveRoundId}
        getListValue={getListValue}
      ></List>
    </div>
  );
};
export default ChoiceListLeft;
