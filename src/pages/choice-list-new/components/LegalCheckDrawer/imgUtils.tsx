import React, { ReactChild } from 'react';
import { Preview } from 'web-common-modules/antd-pro-components';
import classNames from 'classnames';
import { OSSImagePre } from 'web-common-modules/constant';
import { Tag, Icon } from 'antd';

export const UPDATE_TYPE_MAP = {
  ADDED: '新上传',
  DELETED: '已删除',
};

export const QualificationItemDomWrap: React.FC<{ type?: 'pdf'; data: any; pdfSize?: boolean }> = ({
  children,
  type,
  data,
  pdfSize = true,
}) => {
  return (
    <div
      style={{ width: 60, height: 60, border: '1px dashed rgba(46, 52, 66, 0.12)' }}
      className={classNames([
        'relative overflow-hidden flex items-center cursor-pointer image-util-box',
        type === 'pdf' && pdfSize ? 'flex-col justify-end' : 'justify-center',
      ])}
    >
      {children}
      {['ADDED', 'DELETED'].includes(data.updateType!) && (
        <span
          className="absolute top-0 left-0 color-bg-secondary-2"
          style={{ padding: '2px 16px', borderRadius: '2px 22px 22px 2px', color: '#fff' }}
        >
          {UPDATE_TYPE_MAP[data.updateType as 'ADDED']}
        </span>
      )}
    </div>
  );
};

export const renderQualification = (typeArr: any[], pdfSize = true) => {
  // const renderList = qualificationList?.filter((item) =>
  //   typeArr.includes(item.qualificationDetailType as QualificationTypeEnum),
  // );

  if (!typeArr?.length) {
    return (
      <div className="font-body color-font-danger-2" style={{ lineHeight: '18px' }}>
        未上传
      </div>
    );
  }

  const urlList = typeArr?.map((item) => ({ url: item.url!, desc: item.desc! || '' })) ?? [];

  return (
    <div className="flex" style={{ gap: '8px', flexWrap: 'wrap' }}>
      {typeArr.map((item, index) => {
        if (/\.(zip|ZIP|RAR|rar|xlsx|xls)/.test(item.url ?? '')) {
          return item ? (
            <div
              style={{
                backgroundColor: '#f0f4ff',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: '60px',
                height: '60px',
                cursor: 'pointer',
                borderRadius: '4px',
              }}
              onClick={() => {
                window.open(item.url, '_block');
              }}
            >
              <Icon className="fs-18 color-font-info-3" type="file-zip" />
            </div>
          ) : (
            <></>
          );
        }
        if (/\.(png|jpg|jpeg|gif)/.test((item?.url as string)?.toLocaleLowerCase() ?? '')) {
          return item ? (
            <Preview url={item.url!} urlList={urlList} key={index} zIndex={999999}>
              <QualificationItemDomWrap data={item}>
                <img
                  src={item.url}
                  className="w-full h-full"
                  style={{ objectFit: 'contain' }}
                  alt=""
                />
              </QualificationItemDomWrap>
            </Preview>
          ) : (
            <></>
          );
        }

        if (/\.(doc|docx)/.test(item.url ?? '')) {
          return (
            <div
              onClick={() => {
                window.open(item.url, '_block');
              }}
            >
              <QualificationItemDomWrap type="pdf" data={item} pdfSize={pdfSize}>
                <Icon
                  className="fs-18 color-font-info-3"
                  style={{ fontSize: '30px' }}
                  type="file-word"
                  theme="filled"
                />
                <span
                  className="flex color-bg-neutral-black-light4 color-font-neutral-black-3 font-secondary pl-12 pr-12  pb-4 "
                  style={{ marginTop: '2px', paddingTop: '2px' }}
                >
                  <span className="flex-1 text-ellipsis-line-1">
                    {decodeURI(item?.url?.split('?')?.[0]?.split('/')?.pop() || '') ??
                      `${item.desc}`}
                  </span>
                  <span>doc</span>
                </span>
              </QualificationItemDomWrap>
            </div>
          );
        }

        return item ? (
          <Preview key={index} url={item.url!} urlList={urlList} zIndex={999999}>
            <QualificationItemDomWrap type="pdf" data={item} pdfSize={pdfSize}>
              <img
                style={{ width: 30, height: 30 }}
                src={`${OSSImagePre}/icon/icon_pdf.png`}
                alt=""
              />
              {pdfSize ? (
                <span
                  className="flex color-bg-neutral-black-light4 color-font-neutral-black-3 font-secondary pl-12 pr-12  pb-4 "
                  style={{ marginTop: '2px', paddingTop: '2px' }}
                >
                  <span className="flex-1 text-ellipsis-line-1">
                    {decodeURI(item?.url?.split('?')?.[0]?.split('/')?.pop() || '') ??
                      `${item.desc}`}
                  </span>
                  <span>pdf</span>
                </span>
              ) : (
                <></>
              )}
            </QualificationItemDomWrap>
          </Preview>
        ) : (
          <></>
        );
      })}
      {typeArr[0]?.isChangeFlag && (
        <Tag color="#108ee9" style={{ marginLeft: '4px', height: '22px' }}>
          变更
        </Tag>
      )}
    </div>
  );
};
