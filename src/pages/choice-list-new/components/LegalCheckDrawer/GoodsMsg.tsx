import React from 'react';
import styles from './index.module.less';
import { DetailForSelectionResult } from './services/yml';
import { defaultRender } from './utils';
import { renderPlatformSourceLogo } from '@/common/constants/platform';
import { Preview } from 'web-common-modules/antd-pro-components';
import { Table } from 'antd';
import PopoverRowText from '@/components/PopoverRowText';

interface PROPS {
  detail: DetailForSelectionResult | null;
  isPassRate?: boolean;
  isSKU?: boolean;
  detailCur?: any;
}

const GoodsMsg: React.FC<PROPS> = (props) => {
  const { detail, isPassRate = true, isSKU = false, detailCur } = props;

  const columns = [
    {
      title: 'SKU',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (_: string, record: any) => {
        return <PopoverRowText text={_ || '-'}>{_ || '-'}</PopoverRowText>;
      },
    },
    {
      title: '库存(件)',
      dataIndex: 'stock',
      key: 'stock',
      width: 150,
      render: (_: string, record: any) => {
        return <>{_ || '-'}</>;
      },
    },
  ];
  return (
    <>
      <div className={styles['goods-msg']}>
        <Preview url={detail?.spuImage || ''} urlList={[detail?.spuImage || '']}>
          <img src={detail?.spuImage} className={styles['goods-msg-img']} alt="" />
        </Preview>
        <div className={styles['goods-msg-right']}>
          <div className={styles['goods-msg-line']}>
            <div className={styles['name-icon']}>
              {/* @TODO: 平台 */}
              {/* {detail?.} */}
              {renderPlatformSourceLogo({
                platformSource: detail?.platform,
                className: styles['icon'],
              })}
              {/* <img
              src="https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images/icon/icn-tb.png"
              className={styles['icon']}
            /> */}
              {/* 点击跳转 */}
              <span
                className={styles['name-icon-title']}
                onClick={() => {
                  window.open(detail?.spuLink, '_blank');
                }}
              >
                {detail?.spuName || '-'}
              </span>
            </div>
            <div className={styles['line-item']}>
              <span className={styles['line-item-title']}>负责商务: </span>
              <span>
                {detail?.selectionRound?.bpName?.[0] && (
                  <div
                    style={{ width: 18, height: 18, background: '#0D6DEA' }}
                    className="font-secondary overfolw-hidden radius-round  color-font-white text-center"
                  >
                    {detail?.selectionRound?.bpName?.[0]}
                  </div>
                )}
              </span>
              <span style={{ padding: '0 4px' }}>
                {defaultRender(detail?.selectionRound?.bpName)}
              </span>
              {isPassRate ? (
                <span
                  className={`${styles.auditPassRate} ml-4 color-bg-secondary-light3 pl-8 pr-8 radius-s`}
                >
                  {defaultRender(detail?.selectionRound?.passRate)}
                  {detail?.selectionRound?.passRate ? '%' : ''}审核通过率
                </span>
              ) : (
                <></>
              )}
            </div>
          </div>
          <div className={styles['goods-msg-line']}>
            <div className={styles['line-item']}>
              <span className={styles['line-item-title']}>商品类目: </span>
              {defaultRender(detail?.cateNamePath)}
            </div>
            <div className={styles['line-item']}>
              <span className={styles['line-item-title']}>商品编号: </span>
              {defaultRender(detail?.spuNo)}
            </div>
            <div className={styles['line-item']}>
              <span className={styles['line-item-title']}>平台商品ID: </span>
              {defaultRender(detail?.platformSpuId)}
            </div>
          </div>
        </div>
      </div>
      {isSKU && detailCur?.skuList?.length ? (
        <div className={styles['goods-msg']}>
          <div className={styles['goods-msg-img']}></div>
          <div style={{ flex: 1 }}>
            <Table
              pagination={false}
              columns={columns}
              dataSource={detailCur?.skuList}
              scroll={{ x: 'auto', y: 300 }}
            />
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default GoodsMsg;
