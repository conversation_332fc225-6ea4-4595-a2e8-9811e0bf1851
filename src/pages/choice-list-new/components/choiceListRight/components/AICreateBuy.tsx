import { But<PERSON>, Modal } from 'antd';
import React, { useContext } from 'react';
import { AuthWrapper } from 'qmkit';
import { message } from 'antd';
import { batchCreateScript } from '../services/index';
import { useRequest } from 'ahooks';
import { usePoint } from '@/components/OralBroadcast/src/hooks';

interface PropsType {
  infoList: any[];
}

const AICreateBuy: React.FC<PropsType> = ({ infoList }) => {
  const { goodsAICreate } = usePoint();
  const { run: batchCreateScriptRun, loading: batchCreateScriptLoading } = useRequest(
    batchCreateScript,
    {
      manual: true,
      onSuccess({ res }) {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        message.success('操作成功');
      },
    },
  );

  const handleClick = () => {
    // console.log(infoList);
    goodsAICreate();
    if (!infoList.length) {
      message.warning('请选择商品');
      return;
    }
    if (infoList.length > 10) {
      message.warning('最多选择10个商品');
      return;
    }
    Modal.confirm({
      title: '请确认是否通过AI生成口播稿?',
      content: 'AI生成的口播稿信息仅供参考,并且生成存在一定时间加载,请确认是否执行该操作',
      onOk: () => {
        // console.log('确认');
        const selectGoodsPoolNos = infoList.map((item) => item?.sourceOrderNo);
        // 调用接口
        batchCreateScriptRun({
          selectGoodsPoolNos,
        });
      },
    });
  };
  return (
    <AuthWrapper functionName="f_choice_list_ai_create_buy">
      <Button type="primary" size="small" onClick={handleClick} loading={batchCreateScriptLoading}>
        AI生成口播稿
      </Button>
    </AuthWrapper>
  );
};

export default AICreateBuy;
