import { DatePicker, Icon, Input, message, Modal, Radio, Select, Spin } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import Form, { FormComponentProps } from 'antd/lib/form';
import moment, { Moment } from 'moment';
import {
  allRiskLevelColor,
  QUALIFICATION_AUDIT_STATE_COLOR,
  QUALIFICATION_AUDIT_STATE_ICON,
  RISK_LEVEL_TEXT,
} from '@/pages/audit/legal-audit-queue/utils/getRiskLevel';
import { QUALIFICATION_AUDIT_STATE, RISK_LEVEL } from '@/pages/audit/legal-audit-queue/types';
import styles from './index.module.less';
import { highRiskRatifyCreate, HighRiskRatifyCreateRequest } from './fetchApi';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  LiveGoodsInfoResult,
  queryBySelectionIdList,
  QueryBySelectionIdListResult,
} from '@/services/yml/choiceList';
import FileUploadSouceId, { ValueType } from '@/components/FileUploadSouceId';
import { checkAuth } from 'qmkit';
import {
  detailForSelection,
  DetailForSelectionResult,
} from '../../../LegalCheckDrawer/services/yml';
import { QualificationBizTypeEnum } from '@/pages/specil-approval-record/highrisk-approval-records/hooks';

const formItemLayout = {
  labelCol: {
    xs: { span: 18 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 16 },
    sm: { span: 16 },
  },
};

export enum SubjectApprovalReason {
  '合作方企业类型：个体工商户' = '合作方企业类型：个体工商户',
  '公司成立不满一年' = '公司成立不满一年',
  '公司注册资本不足100万' = '公司注册资本不足100万',
  '经营异常' = '经营异常',
}

export enum BrandType {
  AUTHORIZED = 'AUTHORIZED', //'授权品牌'
  OWN = 'OWN', //'自有品牌'
  OTHER = 'OTHER', //'其他-广告代理/代运营方等'
  PURCHASE = 'PURCHASE', //'采买链路'
}
export type ParamsInfoType = NonNullable<HighRiskRatifyCreateRequest['ratifyParamList']>[number] & {
  expireDate: Moment[];
};
interface TProps extends FormComponentProps<ParamsInfoType> {
  visible: boolean;
  cancel: () => void;
  details?: LiveGoodsInfoResult;
  onSearch: () => void;
  isSelectTable?: boolean;
}
const labelStyle: React.CSSProperties = {
  color: '#444444',
  fontWeight: 500,
};
const DateItem: React.FC<{
  disabledDate: (current: Moment | null) => boolean;
  onChange?: (e: Moment | null) => void;
  value?: Moment;
}> = ({ value, disabledDate, onChange }) => {
  return (
    <>
      <DatePicker disabledDate={disabledDate} value={moment()} disabled />
      <DatePicker
        disabledDate={disabledDate}
        onChange={onChange}
        value={value}
        placeholder="请选择结束日期"
      />
    </>
  );
};
const HighRiskModal: React.FC<TProps> = ({
  visible,
  cancel,
  form,
  details,
  onSearch,
  isSelectTable,
}) => {
  const [loading, setLoading] = useState(false);
  const { getFieldDecorator, validateFields } = form;
  const [selectionDetail, setSelectionDetail] = useState<DetailForSelectionResult>();
  const [specialAuditCateExpireDate, setSpecialAuditCateExpireDate] =
    useState<QueryBySelectionIdListResult[number]>();
  const handleOk = () => {
    validateFields((err, values) => {
      if (err) {
        return;
      }
      const suppleQualificationAfter =
        values.suppleQualificationAfter !== undefined
          ? values.suppleQualificationAfter
            ? true
            : false
          : undefined;
      const qualificationSuppleTime = values.qualificationSuppleTime
        ? moment(values.qualificationSuppleTime).format('YYYY-MM-DD')
        : undefined;
      const resourceIdList = values?.resourceIdList?.map(
        (item) => (item as any).resourceId! as string,
      );
      handleSubmit({
        ratifyParamList: [
          {
            ...values,
            suppleQualificationAfter,
            qualificationSuppleTime,
            resourceIdList,
            selectionRoundId: details?.id,
            startExpireDate: values?.expireDate?.length
              ? `${moment(values?.expireDate[0]).format('YYYY-MM-DD')} 00:00:00`
              : undefined,
            endExpireDate: values?.expireDate?.length
              ? `${moment(values?.expireDate[1]).format('YYYY-MM-DD')} 23:59:59`
              : undefined,
          },
        ],
      });
    });
  };
  const handleSubmit = async (params: HighRiskRatifyCreateRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: highRiskRatifyCreate,
      params,
    });
    setLoading(false);
    if (result) {
      result?.failNum
        ? message.error(
            result?.failReasonInfoList?.length
              ? result?.failReasonInfoList[0]?.reason ?? '网络错误'
              : '网络错误',
          )
        : message.success('操作成功');
      if (!result?.failNum) {
        handleCancel();
        onSearch();
      }
    }
  };
  const handleCancel = () => {
    form?.resetFields();
    cancel();
  };
  const getSpecialAuditCateExpireDate = async () => {
    // 拼接穿参数据
    const data = {
      requestList: [
        {
          id: details?.id,
          type: form.getFieldValue('ratifyType'),
          needQualificationCompletion: form.getFieldValue('suppleQualificationAfter'),
        },
      ],
    };
    const result = await responseWithResultAsync({
      request: queryBySelectionIdList,
      params: data,
    });

    setSpecialAuditCateExpireDate(result?.length ? result[0] : undefined);
  };
  const Status = useCallback(() => {
    const { legalStatus, auditDetailMap } = details ?? {};
    return (
      <div className={styles['table-level']}>
        <p style={{ color: allRiskLevelColor[legalStatus as RISK_LEVEL], marginRight: '8px' }}>
          {RISK_LEVEL_TEXT[legalStatus as RISK_LEVEL] || '-'}
        </p>
        <div className={styles['level-box']}>
          <div className={styles['level-box-item']}>
            <Icon
              style={{
                color:
                  QUALIFICATION_AUDIT_STATE_COLOR[
                    auditDetailMap?.SUPPLIER?.auditState as QUALIFICATION_AUDIT_STATE
                  ],
              }}
              className={styles['icon']}
              type={
                QUALIFICATION_AUDIT_STATE_ICON[
                  auditDetailMap?.SUPPLIER?.auditState as QUALIFICATION_AUDIT_STATE
                ]
              }
              theme="filled"
            />
            <span>商家</span>
          </div>
          <div className={styles['level-box-item']}>
            <Icon
              style={{
                color:
                  QUALIFICATION_AUDIT_STATE_COLOR[
                    auditDetailMap?.BRAND?.auditState as QUALIFICATION_AUDIT_STATE
                  ],
              }}
              className={styles['icon']}
              type={
                QUALIFICATION_AUDIT_STATE_ICON[
                  auditDetailMap?.BRAND?.auditState as QUALIFICATION_AUDIT_STATE
                ]
              }
              theme="filled"
            />
            <span>品牌</span>
          </div>
        </div>
        <div className={styles['level-box']}>
          <div className={styles['level-box-item']}>
            <Icon
              style={{
                color:
                  QUALIFICATION_AUDIT_STATE_COLOR[
                    auditDetailMap?.GOODS?.auditState as QUALIFICATION_AUDIT_STATE
                  ],
              }}
              className={styles['icon']}
              type={
                QUALIFICATION_AUDIT_STATE_ICON[
                  auditDetailMap?.GOODS?.auditState as QUALIFICATION_AUDIT_STATE
                ]
              }
              theme="filled"
            />
            <span>商品</span>
          </div>
        </div>
      </div>
    );
  }, [details]);
  const initForm = () => {
    form.setFieldsValue({
      commissionRate: details?.commissionRate ? Number(details.commissionRate) * 100 : undefined,
      // liveRoomIds: goodspoolDetail?.liveRoomMaps?.map((item) => item.liveRoomId) ?? undefined,
    });
  };
  const initSeletionDetail = async (id: string) => {
    const result = await responseWithResultAsync({
      request: detailForSelection,
      params: { id },
    });
    result && setSelectionDetail(result);
  };
  useEffect(() => {
    if (details && visible) {
      setSpecialAuditCateExpireDate(undefined);
      initForm();
      initSeletionDetail(details!.id!);
    }
  }, [details, visible]);
  // 接口获取特批有效期可选范围
  useEffect(() => {
    if (
      form.getFieldValue('ratifyType') &&
      (form.getFieldValue('suppleQualificationAfter') === 0 ||
        form.getFieldValue('suppleQualificationAfter') === 1)
    ) {
      getSpecialAuditCateExpireDate();
      // 清空资质有效期
      form.setFieldsValue({ expireDate: [] });
    }
  }, [form.getFieldValue('ratifyType'), form.getFieldValue('suppleQualificationAfter')]);

  const FormLabel = useCallback(
    (props: { name: string }) => <span style={labelStyle}>{props.name}</span>,
    [],
  );
  const disabledDate = (current: Moment | null) => {
    const isBeforeToday =
      current && current.isBefore(moment(details?.liveDate).startOf('day'), 'day');
    const isAfterFifteenDays =
      current &&
      current.isAfter(
        moment(details?.liveDate)
          .subtract(1, 'days') // 此处逻辑是直播日期加上配置的时间，会导致时间可选范围比配置日期多一天，故减一天
          .add(specialAuditCateExpireDate?.days, 'days'),
        'day',
      );
    return !!(isBeforeToday || isAfterFifteenDays);
  };

  return (
    <Modal
      title="资质特批（高风险特批）"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <section style={{ maxHeight: '500px', overflow: 'auto' }}>
          <div className="ant-descriptions-title">基本信息</div>
          <Form labelAlign="right" {...formItemLayout} className={styles.form}>
            <Form.Item label={<FormLabel name="商品名称" />}>
              <p>{`【${details?.brandName ?? '-'}】${details?.spuName ?? '-'}`}</p>
            </Form.Item>
            <Form.Item label={<FormLabel name="资质风险等级" />}>
              <Status />
            </Form.Item>
            <Form.Item label={<FormLabel name="法务审核意见" />}>
              <p>
                {details?.auditDetailMap
                  ? Object.keys(details?.auditDetailMap)
                      .sort(() => -1)
                      .reduce((a: string, b: string) => {
                        return ` ${
                          (details?.auditDetailMap && details.auditDetailMap[b]?.bizType
                            ? QualificationBizTypeEnum[details.auditDetailMap[b].bizType]
                            : '') + '资质'
                        } ${
                          details?.auditDetailMap
                            ? details?.auditDetailMap[b]?.auditOpinion ?? '-'
                            : '-'
                        }
                  ${a}`;
                      }, '')
                  : '-'}
              </p>
            </Form.Item>
            <Form.Item label={<FormLabel name="是否特殊材质" />}>
              <p>
                {selectionDetail?.isSpecialMaterial !== undefined
                  ? selectionDetail?.isSpecialMaterial
                    ? '是'
                    : '否'
                  : '-'}
              </p>
            </Form.Item>
          </Form>
          <div className="ant-descriptions-title">特批信息</div>
          <section style={{ width: '100%' }}>
            <Form {...formItemLayout} labelAlign="right" className={styles.form}>
              {isSelectTable ? (
                <Form.Item label={<FormLabel name="资质特批方式" />}>
                  {getFieldDecorator('ratifyType', {
                    rules: [{ required: true, message: '请选择资质特批方式' }],
                  })(
                    <Radio.Group>
                      {checkAuth('f_selection_flow_board_online_high_risk_audit') && (
                        <Radio value="ONLINE" key="ONLINE">
                          线上特批
                        </Radio>
                      )}
                      {/* {checkAuth('f_selection_flow_board_high_risk_audit') && (
                        <Radio value="OFFLINE" key="OFFLINE">
                          线下特批
                        </Radio>
                      )} */}
                    </Radio.Group>,
                  )}
                </Form.Item>
              ) : (
                <Form.Item label={<FormLabel name="资质特批方式" />}>
                  {getFieldDecorator('ratifyType', {
                    rules: [{ required: true, message: '请选择资质特批方式' }],
                  })(
                    <Radio.Group>
                      {checkAuth('f_choice_list_online_high_risk_audit') && (
                        <Radio value="ONLINE" key="ONLINE">
                          线上特批
                        </Radio>
                      )}
                      {/* {checkAuth('f_choice_list_high_risk_audit') && (
                        <Radio value="OFFLINE" key="OFFLINE">
                          线下特批
                        </Radio>
                      )} */}
                    </Radio.Group>,
                  )}
                </Form.Item>
              )}
              <Form.Item label={<FormLabel name="后续补足资质" />}>
                {getFieldDecorator('suppleQualificationAfter', {
                  rules: [{ required: true, message: '请选择后续补足资质' }],
                })(
                  <Radio.Group>
                    <Radio value={1} key={1}>
                      是
                    </Radio>
                    <Radio value={0} key={0}>
                      否
                    </Radio>
                  </Radio.Group>,
                )}
              </Form.Item>
              {!!form.getFieldValue('suppleQualificationAfter') && (
                <Form.Item label={<FormLabel name="资质补足时间" />}>
                  {getFieldDecorator('qualificationSuppleTime', {
                    rules: [{ required: true, message: '请选择资质补足时间' }],
                  })(<DatePicker placeholder="资质补足时间" style={{ width: '200px' }} />)}
                </Form.Item>
              )}
              {!!specialAuditCateExpireDate?.days && (
                <Form.Item label={<FormLabel name="特批有效期" />}>
                  {getFieldDecorator('expireDate', {
                    rules: [{ required: true, message: '请选择特批有效期' }],
                  })(<DatePicker.RangePicker disabledDate={disabledDate} />)}
                </Form.Item>
              )}
              <Form.Item label={<FormLabel name="高风险原因说明" />} required={true}>
                {getFieldDecorator('riskDesc', {
                  rules: [{ required: true, message: '请输入高风险原因说明' }],
                })(
                  <Input.TextArea
                    placeholder="请说明高风险的具体原因"
                    maxLength={1000}
                    style={{ width: 200 }}
                  />,
                )}
              </Form.Item>
              <Form.Item label={<FormLabel name="申请特批理由" />} required={true}>
                {getFieldDecorator('qualificationRatifyReason', {
                  rules: [{ required: true, message: '请输入申请特批理由' }],
                })(
                  <Input.TextArea
                    placeholder="请说明商品申请特批的理由,如需添加相关辅助资料可在下方附件上传."
                    maxLength={500}
                    style={{ width: 200 }}
                  />,
                )}
              </Form.Item>
              <Form.Item label={<FormLabel name="资质特批附件" />}>
                {getFieldDecorator('resourceIdList', {
                  // rules: [{ required: true, message: '请选择资质相关附件' }],
                })(
                  <FileUploadSouceId
                    multiple
                    isImage={false}
                    maxSize={10 * 1024 * 1024}
                    maxLen={10}
                    typeCode="SPU_IMG"
                    accept=".png,.jpg,.jpeg,.pdf"
                    title="资质特批附件"
                  />,
                )}
              </Form.Item>
              <p style={{ marginLeft: 100 }}>
                支持png、jpg、jpeg、pdf格式,单张不超过10MB，最多10张
              </p>
            </Form>
          </section>
        </section>
      </Spin>
    </Modal>
  );
};

export default Form.create<TProps>()(HighRiskModal);
