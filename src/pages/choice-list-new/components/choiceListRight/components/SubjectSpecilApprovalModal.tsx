import { liveGoodsInfo, LiveGoodsInfoResult } from '@/services/yml/choiceList';
import { useSetState } from 'ahooks';
import { Button, Form, Input, message, Modal, Spin } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import React, { useCallback, useEffect } from 'react';
import styles from './HighRisk/index.module.less';
import FileUploadSouceId, { ValueType } from '@/components/FileUploadSouceId';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { LiveGoodsListInfo } from '../List/goodsCard';
import {
  supplierBodySpecialAuditCheck,
  SupplierBodySpecialAuditCheckRequest,
  supplierBodySpecialAuditCreate,
  SupplierBodySpecialAuditCreateRequest,
} from './HighRisk/fetchApi';
import { SelectionProcessKanbanPage } from '@/pages/selection-flow-board/hooks';

interface IProps extends FormComponentProps<{ attachment: ValueType[] }> {
  onSearch: () => void;
  onRef?: React.MutableRefObject<SubjectModalRefType | undefined>;
  info?: SelectionProcessKanbanPage;
}
const formItemLayout = {
  labelCol: {
    xs: { span: 15 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 16 },
    sm: { span: 16 },
  },
};
export type SubjectModalRefType = {
  onOpen: (listInfo?: LiveGoodsListInfo) => void;
};

const labelStyle: React.CSSProperties = {
  color: '#444444',
  fontWeight: 500,
};
const SubjectSpecilApprovalModal = React.forwardRef<SubjectModalRefType, IProps>(
  ({ form, onSearch, onRef, info }) => {
    const { getFieldDecorator } = form;
    const [subjectState, setState] = useSetState<{
      visible: boolean;
      loading: boolean;
      details?: LiveGoodsInfoResult;
      info?: LiveGoodsListInfo | SelectionProcessKanbanPage;
    }>({
      visible: false,
      loading: false,
    });
    const FormLabel = useCallback(
      (props: { name: string }) => <span style={labelStyle}>{props.name}</span>,
      [],
    );
    const getDetail = async (id: string) => {
      setState((state) => ({ ...state, loading: true }));
      const reuslt = await responseWithResultAsync({
        request: liveGoodsInfo,
        params: {
          id: id,
        },
      });
      setState((state) => ({ ...state, details: reuslt ?? undefined }));
      setState((state) => ({ ...state, loading: false }));
    };
    const checkSubmit = async (params: SupplierBodySpecialAuditCheckRequest) => {
      try {
        const { res } = await supplierBodySpecialAuditCheck(params);
        if (res.code !== '200') {
          message.error(res.message ?? '网络错误');
          return true;
        }
        const { result } = res;
        return result;
      } catch (error) {
        console.log(error);
        message.error('网络错误');
        return true;
      }
    };
    const handleSubmit = async (params: SupplierBodySpecialAuditCreateRequest) => {
      const checkResult = await checkSubmit({
        deptId: params?.deptId,
        supplierId: params?.supplierId,
      });
      if (checkResult) {
        message.error('当前事业部商家已存在主体特批流程，请勿重复发起');
        return;
      }
      const result = await responseWithResultAsync({
        request: supplierBodySpecialAuditCreate,
        params,
      });
      return result;
    };
    const handleOpen = (listInfo?: LiveGoodsListInfo | SelectionProcessKanbanPage) => {
      setState((state) => ({ ...state, visible: true, info: listInfo }));
      listInfo?.id && getDetail(listInfo.id);
    };
    const handleOk = () => {
      form?.validateFields(async (err, values) => {
        if (!err) {
          setState((state) => ({ ...state, loading: true }));
          const params: SupplierBodySpecialAuditCreateRequest = {
            attachment: values?.attachment?.map((item) => item.resourceId),
            remark: subjectState?.details?.auditDetailMap?.SUPPLIER?.auditOpinion,
            selectionRoundId: subjectState?.info?.id,
            supplierId: subjectState?.info?.supplierId,
            type: 'LIVE',
            deptId: subjectState?.details?.deptId,
            remarkDesc: values?.remarkDesc,
          };
          const reuslt = await handleSubmit(params);
          setState((state) => ({ ...state, loading: false }));
          if (reuslt) {
            message.success('提交成功');
            setState((state) => ({ ...state, visible: false }));
            onSearch();
          }
        }
      });
    };
    const handleCancel = () => {
      setState((state) => ({ ...state, visible: false }));
    };
    React.useImperativeHandle(onRef, () => ({
      onOpen: handleOpen,
    }));
    return (
      <>
        {!!info?.needSupplierBodySpecialAudit &&
          !['WAIT_AUDIT', 'CONFIRMING', 'PASS'].includes(
            info?.supplierBodySpecialAuditStatus ?? '',
          ) && (
            <a onClick={() => handleOpen(info)} style={{ marginRight: 6 }}>
              主体特批
            </a>
          )}
        <Modal
          title="主体特批"
          visible={subjectState.visible}
          onOk={handleOk}
          onCancel={handleCancel}
          maskClosable={false}
          confirmLoading={subjectState.loading}
        >
          <Spin spinning={subjectState.loading}>
            <section style={{ maxHeight: '500px', overflow: 'auto' }}>
              <div className="ant-descriptions-title">基本信息</div>
              <Form labelAlign="right" {...formItemLayout} className={styles.form}>
                <Form.Item label={<FormLabel name="商家名称" />}>
                  <p>{subjectState?.details?.supplierOrgName ?? '-'}</p>
                </Form.Item>
              </Form>
              <div className="ant-descriptions-title">特批信息</div>
              <section style={{ width: '100%' }}>
                <Form {...formItemLayout} labelAlign="right" className={styles.form}>
                  <Form.Item label={<FormLabel name="主体特批原因" />} required>
                    <p>{subjectState?.details?.auditDetailMap?.SUPPLIER?.auditOpinion ?? '-'}</p>
                  </Form.Item>
                  <Form.Item label={<FormLabel name="原因说明" />}>
                    {getFieldDecorator('remarkDesc')(
                      <Input.TextArea maxLength={300} placeholder="请输入原因说明" rows={3} />,
                    )}
                  </Form.Item>

                  <Form.Item label={<FormLabel name="主体相关附件" />}>
                    {getFieldDecorator('attachment', {
                      // rules: [{ required: true, message: '请选择资质相关附件' }],
                    })(
                      <FileUploadSouceId
                        multiple
                        isImage={false}
                        maxSize={20 * 1024 * 1024}
                        maxLen={20}
                        typeCode="SPU_IMG"
                        accept=".png,.jpg,.jpeg,.pdf"
                        title="主体相关附件"
                        hiddenSelectFile
                      >
                        <Button icon="upload">上传附件</Button>
                      </FileUploadSouceId>,
                    )}
                  </Form.Item>
                  <p style={{ marginLeft: 100 }}>
                    支持png、jpg、jpeg、pdf格式,单张不超过20MB，最多20张
                  </p>
                </Form>
              </section>
            </section>
          </Spin>
        </Modal>
      </>
    );
  },
);

export default Form.create<IProps>()(SubjectSpecilApprovalModal);
