import {
  LiveGoodsInfoResult,
  NewLiveGoodsListResult,
  RoleDelRequest,
} from '@/services/yml/choiceList';
import { GetServiceTypeResult } from '@/services/yml/live-service-type-configuration';
import { Form, Icon, message, Modal, Select } from 'antd';
import React, { useEffect, useMemo } from 'react';
// import { ReasonList } from '../List/type';
import { FormComponentProps } from 'antd/lib/form';
import {
  bpConfirm,
  bpCancle,
  roleDel,
  opConfirm,
  chConfirm,
} from '@/services/yml/choiceList/index';
import FrameworkSelectFormTable from './FrameworkSelectFormTable';
import { GuaranteedDetailResult } from '@/services/yml/quality-assurance-cooperation';
import { selectorCheck } from '@/pages/selection-flow-board/hooks/useTable';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';

const { Option } = Select;
export type InfoType = NonNullable<NewLiveGoodsListResult['records']>[number];
type FormValues = {
  liveServiceTypeId: string;
  reason: { key: RoleDelRequest['dropProductReasonType']; label: string };
  frameworkGmvFlag: boolean;
  frameworkRoundFlag: boolean;
  guaranteeQuantityFlag: boolean;
  roundTotalFees: string;
};
interface PropsType extends FormComponentProps<FormValues> {
  visible: boolean;
  childrenMsg?: GetServiceTypeResult;
  roleInfo?: {
    type: string;
    color: string;
    title: string;
    info: string;
  };
  onCancel?: ((e: React.MouseEvent<HTMLElement, MouseEvent>) => void) | undefined;
  info?: NonNullable<NewLiveGoodsListResult['records']>[number];
  setVisible: (value: boolean) => void;
  onGetList: () => void;
  goodsInfo: LiveGoodsInfoResult;
  guaranteedDetailInfo: GuaranteedDetailResult;
}
const GoodsCardConfirmModal: React.FC<PropsType> = ({
  visible,
  childrenMsg,
  roleInfo,
  onCancel,
  info,
  setVisible,
  onGetList,
  form,
  goodsInfo,
  guaranteedDetailInfo,
}) => {
  const { getFieldDecorator } = form;
  const showLioveServiceForm = useMemo(
    () =>
      !!childrenMsg &&
      (roleInfo?.type === 'businessConfirm' || roleInfo?.type === 'businessConfirmAuth'),
    [childrenMsg, roleInfo],
  );
  const showFrameworkTableForm = useMemo(
    () =>
      (roleInfo?.type === 'businessConfirm' || roleInfo?.type === 'businessConfirmAuth') &&
      (info?.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE') ||
        info?.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE') ||
        info?.guaranteeQuantityId ||
        showLioveServiceForm) &&
      visible,
    [info, roleInfo, visible],
  );
  const handleOk = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    e.stopPropagation();
    form.validateFields((err, values) => {
      console.log(values);
      const { liveServiceTypeId, ...otherValue } = values;
      if (err) {
        return;
      }
      handleConfirm({ ...otherValue, liveServiceTypeId: liveServiceTypeId?.key });
      return;
    });
  };
  const handleConfirm = async (values: FormValues) => {
    if (roleInfo?.type === 'businessConfirm' || roleInfo?.type === 'businessConfirmAuth') {
      await bpConfirm({
        id: info?.id,
        version: info?.version,
        ...values,
      }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 商务取消
    if (roleInfo?.type === 'businessCancle') {
      await bpCancle({ id: info?.id, version: info?.version }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 选品确认
    if (roleInfo?.type === 'chooseConfirm') {
      if (!selectorCheck(info?.selectionAuditor)) {
        message.error('被选品人员认领后的商品仅能由该认领人进行选品审核操作');
        return;
      }
      await chConfirm({ id: info?.id, version: info?.version }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 运营确认
    if (roleInfo?.type === 'opConfirm') {
      await opConfirm({ id: info?.id, version: info?.version }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 掉品
    if (roleInfo?.type === 'del') {
      await roleDel({
        id: info?.id,
        reason: values.reason.label,
        dropProductReasonType: values.reason.key,
        version: info?.version,
      }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    setVisible(false);
  };
  const goodsInfoList = useMemo(
    () => [{ ...goodsInfo, childrenMsg: childrenMsg! }],
    [goodsInfo, childrenMsg],
  );
  const modalWidth = useMemo(() => {
    const showFrameworkTableFormWidth = showFrameworkTableForm ? 206 : 0;
    const frameworkCoopModelLiveWidth = info?.frameworkCoopModel?.includes(
      'GUARANTEED_LIVE_ROUND_MODE',
    )
      ? 376
      : 0;
    const frameworkCoopModelGMVWidth = info?.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE')
      ? 376
      : 0;
    const guaranteeQuantityWidth = info?.guaranteeQuantityId ? 276 : 0;
    return showFrameworkTableForm
      ? 474 +
          showFrameworkTableFormWidth +
          frameworkCoopModelLiveWidth +
          frameworkCoopModelGMVWidth +
          guaranteeQuantityWidth
      : 474;
  }, [info, showFrameworkTableForm, visible]);
  const { codeList: ReasonList, getEnumList } = useCode(CODE_ENUM.DROP_PRODUCT_REASON, {
    wait: true,
    able: true,
  });
  useEffect(() => {
    visible && form.resetFields();
    visible && getEnumList();
  }, [visible]);
  return (
    <Modal visible={visible} onOk={handleOk} onCancel={onCancel} width={modalWidth}>
      <div
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        style={{ display: 'flex' }}
      >
        <Icon
          type="exclamation-circle"
          className="iconStyle"
          style={{ color: roleInfo?.color, marginRight: 16 }}
        />
        <div style={{ flex: 1 }}>
          <h3> {roleInfo?.title}</h3> <br />
          {roleInfo?.type === 'businessConfirm' ? '' : roleInfo?.info}
          {roleInfo?.type === 'del' && (
            <Form.Item label="掉品原因：">
              {getFieldDecorator('reason', {
                rules: [
                  {
                    required: true,
                    message: '请选择掉品原因',
                  },
                ],
              })(
                <Select style={{ width: 200 }} dropdownMatchSelectWidth={false} labelInValue>
                  {ReasonList.map((i, index) => {
                    return (
                      <Option value={i.value} key={index}>
                        {i.label}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </Form.Item>
          )}
          {/* {showLioveServiceForm && (
            <Form.Item
              required={childrenMsg?.required === 1 ? true : false}
              label="直播服务类型"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 12 }}
            >
              {getFieldDecorator('liveServiceTypeId', {
                initialValue: info?.liveServiceTypeId,
                rules: [
                  {
                    required: childrenMsg?.required === 1 ? true : false,
                    message: '请选择直播服务类型',
                  },
                ],
              })(
                <Select allowClear style={{ width: 200 }} placeholder="请选择">
                  {childrenMsg?.serviceTypeOptions?.map((item) => {
                    return <Select.Option value={item.id}>{item.name}</Select.Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
          )} */}
          {showFrameworkTableForm && (
            <FrameworkSelectFormTable
              form={form}
              editType="single"
              goodsInfoList={goodsInfoList}
              info={info}
              guaranteedDetailInfo={guaranteedDetailInfo}
            />
          )}
        </div>
      </div>
    </Modal>
  );
};

export default Form.create<PropsType>()(GoodsCardConfirmModal);
