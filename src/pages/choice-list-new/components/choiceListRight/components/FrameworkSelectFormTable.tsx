import { LiveGoodsInfoResult } from '@/services/yml/choiceList';
import { Form, Icon, InputNumber, Select, Table, message, Tag } from 'antd';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ColumnProps, TableRowSelection } from 'antd/lib/table';
import React, { useEffect, useMemo, useState } from 'react';
import { InfoType } from './GoodsCardConfirmModal';
import { GetServiceTypeResult } from '@/services/yml/choose';
import TableTitle from './TableTitle';
import TableNotice from './TableNotice';
import {
  GuaranteedDetailResult,
  guaranteedRelDetailList,
  GuaranteedRelDetailListResult,
} from '@/services/yml/quality-assurance-cooperation';
import { useRequest } from 'ahooks';

type PropsType = {
  form: WrappedFormUtils;
  editType: 'batch' | 'single';
  goodsInfoList: (LiveGoodsInfoResult & { childrenMsg: GetServiceTypeResult })[];
  info?: InfoType;
  onSelect?: (indexList: number[]) => void;
  guaranteedDetailInfo: GuaranteedDetailResult;
};
const formContentStyle: React.CSSProperties = {
  width: '100px',
};
const formStyle: React.CSSProperties = {
  margin: 'auto',
};
const FrameworkSelectFormTable: React.FC<PropsType> = ({
  form,
  editType,
  goodsInfoList,
  info,
  guaranteedDetailInfo,
}) => {
  const { getFieldDecorator } = form;
  const [selectedRowKeyList, setSelectedRowKeys] = useState<number[]>([]);
  const [frameworkRoundFlag, setFrameworkRoundFlag] = useState(true);
  const [dataSource, setDataSource] = useState<PropsType['goodsInfoList']>([]);
  const [GuaranteedRelDetail, setGuaranteedRelDetailList] = useState<GuaranteedRelDetailListResult>(
    {},
  );
  useEffect(() => {
    setDataSource(goodsInfoList);
  }, [goodsInfoList]);
  const frameworkTableFormColumns: ColumnProps<PropsType['goodsInfoList']>[] = useMemo(
    () => [
      {
        title: '#',
        key: 'id',
        dataIndex: 'id',
        render: (id: string, record, index) => {
          return index + 1;
        },
      },
      {
        title: '选品流程编号',
        key: 'no',
        dataIndex: 'no',
        render: (no: string) => info?.no,
      },
      {
        title: '场次',
        key: 'liveRoundInfo',
        dataIndex: 'liveRoundInfo',
        render: (liveRoundInfo: LiveGoodsInfoResult['liveRoundInfo'], record) => {
          return (
            liveRoundInfo?.liveRoundName ??
            info?.liveRoundInfo?.liveRoundName ??
            info?.liveRoundName ??
            '-'
          );
        },
      },
      ...(!!goodsInfoList[0]?.childrenMsg
        ? [
            {
              title: (
                <TableTitle
                  noRequired={!goodsInfoList[0]?.childrenMsg?.required}
                  title="直播服务类型选项"
                />
              ),
              key: 'liveServiceTypeId',
              dataIndex: 'liveServiceTypeId',

              render: (liveServiceTypeId: string, record, index) => {
                const init = { key: liveServiceTypeId, label: record?.liveServiceType };
                return (
                  <Form.Item style={formStyle}>
                    {getFieldDecorator('liveServiceTypeId', {
                      initialValue: init,
                      rules: [
                        {
                          required: record.childrenMsg?.required === 1 ? true : false,
                          message: '请选择直播服务类型',
                        },
                      ],
                    })(
                      <Select
                        allowClear
                        style={formContentStyle}
                        placeholder="请选择"
                        disabled={!record.childrenMsg}
                        labelInValue={true}
                        onChange={(value) => {
                          form.setFieldsValue({
                            [`depositAmount`]: undefined,
                          });
                        }}
                      >
                        {record.childrenMsg?.serviceTypeOptions?.map((item) => {
                          return <Select.Option value={item.id}>{item.name}</Select.Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                );
              },
            },
            {
              title: '定金金额',
              key: 'depositAmount',
              dataIndex: 'depositAmount',

              render: (depositAmount: string, record, index: number) => {
                const liveServiceType = form.getFieldValue('liveServiceTypeId')?.label;
                if (
                  ['预售挂链', '预售讲解', '预热挂链', '预热讲解'].includes(liveServiceType || '')
                ) {
                  return (
                    <Form.Item style={formStyle}>
                      {getFieldDecorator('depositAmount', {
                        initialValue: depositAmount,
                      })(
                        <InputNumber
                          style={formContentStyle}
                          placeholder="请输入"
                          min={0}
                          max={9999999.99}
                          formatter={(value) => `¥${value}`}
                          parser={(value) => value!.replace('¥', '')}
                          precision={2}
                        />,
                      )}
                    </Form.Item>
                  );
                } else {
                  return '-';
                }
              },
            },
          ]
        : []),
      ...(info?.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE')
        ? [
            {
              title: <TableTitle title="是否年框场次" />,
              key: 'frameworkRoundFlag',
              dataIndex: 'frameworkRoundFlag',

              render: (frameworkRoundFlag: LiveGoodsInfoResult['frameworkRoundFlag'], record) => (
                <section style={{ display: 'flex' }}>
                  <Form.Item style={formStyle}>
                    {getFieldDecorator('frameworkRoundFlag', {
                      initialValue: frameworkRoundFlag ?? true,
                      rules: [{ required: true, message: '是否年框场次' }],
                    })(
                      <Select
                        allowClear
                        style={formContentStyle}
                        placeholder="请选择"
                        onChange={(value: boolean) => {
                          setFrameworkRoundFlag(value);
                        }}
                      >
                        <Select.Option label="是" value={true}>
                          是
                        </Select.Option>
                        <Select.Option label="否" value={false}>
                          否
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                  <TableNotice
                    info={record}
                    type="frameworkRoundFlag"
                    guaranteedDetail={guaranteedDetailInfo}
                  />
                </section>
              ),
            },
            ...(frameworkRoundFlag
              ? [
                  {
                    title: <TableTitle title="年框品牌费" />,
                    key: 'roundTotalFees',
                    dataIndex: 'roundTotalFees',

                    render: (roundTotalFees: LiveGoodsInfoResult['roundTotalFees']) => (
                      <Form.Item style={formStyle}>
                        {getFieldDecorator('roundTotalFees', {
                          initialValue: roundTotalFees,
                          rules: [{ required: true, message: '年框品牌费' }],
                        })(
                          <InputNumber
                            formatter={(value) => `¥${value}`}
                            parser={(value) => value!.replace('¥', '')}
                            placeholder="请输入"
                            maxLength={11}
                            style={formContentStyle}
                            min={0}
                          />,
                        )}
                      </Form.Item>
                    ),
                  },
                ]
              : []),
          ]
        : []),
      ...(info?.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE')
        ? [
            {
              title: <TableTitle title="是否年框GMV" />,
              key: 'frameworkGmvFlag',
              dataIndex: 'frameworkGmvFlag',

              render: (frameworkGmvFlag: LiveGoodsInfoResult['frameworkGmvFlag'], record) => (
                <section style={{ display: 'flex' }}>
                  <Form.Item style={formStyle}>
                    {getFieldDecorator('frameworkGmvFlag', {
                      initialValue: frameworkGmvFlag ?? true,
                      rules: [{ required: true, message: '是否年框GMV' }],
                    })(
                      <Select allowClear style={formContentStyle} placeholder="请选择">
                        <Select.Option label="是" value={true}>
                          是
                        </Select.Option>
                        <Select.Option label="否" value={false}>
                          否
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                  <TableNotice
                    info={info}
                    type="frameworkGmvFlag"
                    guaranteedDetail={guaranteedDetailInfo}
                  />
                </section>
              ),
            },
          ]
        : []),
      ...(info?.guaranteeQuantityId && GuaranteedRelDetail[info?.no]
        ? [
            {
              title: <TableTitle title="是否保量" />,
              key: 'guaranteeQuantityFlag',
              dataIndex: 'guaranteeQuantityFlag',

              render: (
                guaranteeQuantityFlag: LiveGoodsInfoResult['guaranteeQuantityFlag'],
                record,
              ) => (
                <section style={{ display: 'flex', justifyContent: 'flex-start' }}>
                  <Form.Item style={formStyle}>
                    {getFieldDecorator('guaranteeQuantityFlag', {
                      initialValue: guaranteeQuantityFlag,
                      rules: [{ required: true, message: '是否保量' }],
                    })(
                      <Select allowClear style={formContentStyle} placeholder="请选择">
                        <Select.Option label="是" value={true}>
                          是
                        </Select.Option>
                        <Select.Option label="否" value={false}>
                          否
                        </Select.Option>
                      </Select>,
                    )}
                  </Form.Item>
                  <TableNotice
                    info={record}
                    deatil={info}
                    type="guaranteeQuantityFlag"
                    guaranteedDetail={guaranteedDetailInfo}
                  />
                  {guaranteedDetailInfo?.almostAchievedFlag ? (
                    <Tag
                      color="green"
                      style={{ marginLeft: '4px', height: '22px', marginTop: '8px' }}
                    >
                      即将达成
                    </Tag>
                  ) : null}
                </section>
              ),
            },
          ]
        : []),
    ],
    [
      goodsInfoList,
      info,
      selectedRowKeyList,
      guaranteedDetailInfo,
      frameworkRoundFlag,
      GuaranteedRelDetail,
    ],
  );
  // const tableColumn:ColumnProps<any> = useMemo(()=>[],)
  useEffect(() => {
    setSelectedRowKeys(goodsInfoList.map((item, index) => index));
  }, [editType]);
  useEffect(() => {}, [goodsInfoList]);
  const rowSelection: TableRowSelection<PropsType['goodsInfoList']> = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      // setSelectedRowKeys(goodsInfoList.map((item, index) => index));
      // onSelect && onSelect(selectedRowKeys as number[]);
    },
    selectedRowKeys: selectedRowKeyList,
  };

  const { run, loading } = useRequest(guaranteedRelDetailList, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        setGuaranteedRelDetailList(res?.result || {});
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  useEffect(() => {
    if (info && info?.guaranteeQuantityId) {
      run({
        detailRequests: [
          { cooperationGuaranteedId: info?.guaranteeQuantityId, selectionNo: info?.no },
        ],
      });
    }
  }, [info]);
  return (
    <Table
      rowSelection={rowSelection}
      pagination={false}
      columns={frameworkTableFormColumns}
      dataSource={dataSource}
      // scroll={{ x: '820px', y: 200 }}
      loading={loading}
    />
  );
};

export default FrameworkSelectFormTable;
