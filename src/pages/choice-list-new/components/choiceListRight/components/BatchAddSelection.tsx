import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { PageContext } from '@/pages/choice-list-new';
import ChoiceListContext from '@/pages/choice-list-new/dataSouce/context';
import {
  getLiveRoomList,
  GetLiveRoomListResult,
} from '@/pages/live-room-operation/live-broadcast/yml';
import {
  liveRoomListAuth,
  LiveRoomListAuthRequest,
} from '@/pages/selection-flow-board/services/yml';
import {
  selectionBatchAdd,
  SelectionBatchAddRequest,
  checkBatchCopyAdd,
  checkBatchCopyGoodsList,
  applyDeadlineNodeConfig,
} from '@/services/yml/choiceList';
import { useSetState } from 'ahooks';
import { Button, Checkbox, DatePicker, Form, message, Modal, notification, Table } from 'antd';
import Select from 'antd/es/select';
import { FormComponentProps } from 'antd/lib/form';
import moment from 'moment';
import { Moment } from 'moment';
import { AuthWrapper } from 'qmkit';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import Space from 'web-common-modules/antd-pro-components/Space';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { useSpokenScriptInfo } from '../hooks';
import AICreateBuy from './AICreateBuy';
import { usePoint } from '@/components/OralBroadcast/src/hooks';

type StateType = {
  liveRoomOptions: GetLiveRoomListResult;
  visible: boolean;
  type?: 'batch' | 'complate';
};
const BatchAddSelection: React.FC<
  FormComponentProps<{
    liveRoomId: { label: string; key: string };
    liveDate: string;
  }> & { getTableHeight: any; isLoading: boolean; optionsSearch: any }
> = ({ form, getTableHeight, isLoading, optionsSearch }) => {
  const { infoList, dispatch, liveRoundInfo, goodsList } = useContext(ChoiceListContext);
  const { deptId, selectPlatform } = useContext(PageContext);
  const [state, setState] = useSetState<StateType>({
    liveRoomOptions: [],
    visible: false,
  });
  const [disabledlineNodeDate, setDisabledlineNodeDate] = useState(
    moment().startOf('day').valueOf(),
  );

  const { openGoodsDetailBatchDownload } = usePoint();

  const checkShow = async () => {
    try {
      const { res } = await checkBatchCopyAdd({ liveRoundId: liveRoundInfo?.liveRoundId });
      if (res?.code !== '200') {
        message.error(res?.message ?? '网络错误');
        return false;
      }
      const { result } = res;
      return result;
    } catch (error) {
      console.log(error);
      return false;
    }
  };
  const [tableList, setTableList] = useState<any[]>([]);
  const checkGoodsLIst = async () => {
    try {
      const { res } = await checkBatchCopyGoodsList({ liveRoundId: liveRoundInfo?.liveRoundId });
      if (res?.code !== '200') {
        message.error(res?.message ?? '网络错误');
        return false;
      }
      const { result } = res;
      console.log('res', result);
      const arr = [];
      result.forEach((i) => {
        arr.push(i);
      });
      arr.length && setTableList(arr);
    } catch (error) {
      console.log(error);
      return false;
    }
  };
  const [type, setType] = useState<'batch' | 'complate'>('batch');
  const handleShow = async (type: 'batch' | 'complate') => {
    setType(type);
    if (type === 'complate') {
      const isCheck = await checkShow();
      if (!isCheck) {
        return;
      }
      setTableList([]);
      await checkGoodsLIst();
      if (!goodsList?.length) {
        message.warn('当前场次无商品不可复制');
        return;
      }
    } else {
      if (infoList.length > 50) {
        message.warn('最多可勾选50个商品');
        return;
      }
    }

    setState((state) => ({ ...state, visible: true, type }));
  };
  const liveRoomList = async () => {
    const result = await responseWithResultAsync({
      request: liveRoomListAuth,
      params: { deptId, platformEnum: selectPlatform as LiveRoomListAuthRequest['platformEnum'] },
    });
    setState((state) => ({ ...state, liveRoomOptions: result ?? [] }));
  };
  // 根据读取参数接口返回的时间来禁用时间选择下拉框
  const applyDeadlineNodeFun = () => {
    applyDeadlineNodeConfig({
      liveRoomId: liveRoundInfo?.liveRoomId,
      deptId,
      platformSource: selectPlatform as LiveRoomListAuthRequest['platformEnum'],
    }).then((res) => {
      if (res?.res?.code === '200') {
        setDisabledlineNodeDate(res.res.result);
      } else {
        message.error(res?.res?.message ?? '网络错误');
      }
    });
  };
  const handleCancel = () => {
    setState((state) => ({ ...state, visible: false }));
    form.resetFields();
  };
  useEffect(() => {
    state.visible && liveRoomList();
    state.visible && applyDeadlineNodeFun();
  }, [state.visible]);
  useEffect(() => {
    dispatch && dispatch({ type: 'infoList', value: [] });
  }, [liveRoundInfo]);
  useEffect(() => {
    form.setFieldsValue({
      liveRoomId: {
        key: liveRoundInfo?.liveRoomId,
        value: liveRoundInfo?.liveRoomId,
        label: liveRoundInfo?.liveRoomName,
      },
    });
  }, [liveRoundInfo, state.visible]);
  const disabledDate = (currentDate: Moment | null) => {
    return moment(currentDate).valueOf() < disabledlineNodeDate;
  };
  const openNotification = () => {
    const oldLiveRound =
      (liveRoundInfo?.liveRoomName ?? '') +
      (moment(liveRoundInfo?.liveDate).format('YYYY-MM-DD') ?? '');
    const newLiveRound =
      form.getFieldValue('liveRoomId').label +
      moment(form.getFieldValue('liveDate')).format('YYYY-MM-DD');
    notification.success({
      duration: 5,
      message: `正在复制${oldLiveRound}场次货盘到${newLiveRound}`,
      // description: '这可能需要一段时间，现在您可以进行其它操作，操作完成后将在右上角消息通知提示您',
      description: (
        <div>
          <p>这可能需要一段时间，现在您可以进行其它操作，操作完成后将在右上角消息通知提示您</p>
          <p>注:禁用状态的商品不允许复制,会为您自动跳过</p>
        </div>
      ),
      placement: 'bottomLeft',
    });
  };
  const handleAddSelection = (value: boolean) => {
    form.validateFields(async (err, values) => {
      if (err) {
        return;
      }
      const params: SelectionBatchAddRequest = {
        ids: state.type === 'batch' ? infoList.map((item) => item.id!) : undefined,
        liveDate: moment(values.liveDate).format('YYYY-MM-DD'),
        liveRoomId: values.liveRoomId.key,
        liveRoundId: state.type === 'complate' ? liveRoundInfo?.liveRoundId : undefined,
        whetherIncludeBrandFee: value,
      };
      const result = await responseWithResultAsync({
        request: selectionBatchAdd,
        params,
      });
      if (result) {
        openNotification();
        handleCancel();
        dispatch && dispatch({ type: 'infoList', value: [] });
      }
    });
  };
  const handleSelectAllChange = (e: CheckboxChangeEvent) => {
    if (e.target.checked) {
      dispatch!({
        type: 'infoList',
        value: goodsList,
      });
    } else {
      handleCheckboxCancel();
    }
  };
  const indeterminate = useMemo(() => {
    if (!infoList.length && !goodsList.length) {
      return false;
    } else {
      return !!(infoList.length && infoList.length < goodsList.length);
    }
  }, [infoList, goodsList]);
  const handleCheckboxCancel = () => {
    dispatch!({ type: 'infoList', value: [] });
  };
  useEffect(() => {
    setTimeout(() => {
      getTableHeight();
    });
  }, [infoList.length, liveRoundInfo?.liveDate]);

  const { handleDownload, downloadLoading } = useSpokenScriptInfo();

  const columns = [
    {
      title: '商品名称',
      dataIndex: 'spuName',
      key: 'spuName',
      align: 'left',
    },
    {
      title: '商品ID',
      dataIndex: 'platformSpuId',
      key: 'platformSpuId',
      width: 200,
    },
    {
      title: '基础服务费',
      dataIndex: 'brandFee',
      key: 'brandFee',
      width: 100,
      align: 'center',
      render: (text: any, record: any) => {
        return record?.hideCommissionFlag ? ' *** ' : <>{record?.brandFee || 0}</>;
      },
    },
  ];

  return (
    <>
      <Space>
        <AuthWrapper functionName="f_choice_list_batch_download">
          <Button
            type="primary"
            onClick={() => {
              openGoodsDetailBatchDownload();
              if (!goodsList?.length) {
                message.warning('没有可以导出的数据');
                return;
              }
              const idList = infoList?.map((item) => item?.id);
              const params = {
                liveRoundId: liveRoundInfo?.liveRoundId,
                ...optionsSearch,
              };
              if (idList?.length) {
                params.idList = idList;
              }
              handleDownload(params);
            }}
            loading={downloadLoading || isLoading}
            size="small"
          >
            批量下载口播稿
          </Button>
        </AuthWrapper>

        <AICreateBuy infoList={infoList} />

        {!!infoList.length && (
          <AuthWrapper functionName="f_batch_add_selection_round">
            <Button
              onClick={() => {
                handleShow('batch');
              }}
              type="primary"
              size="small"
            >
              加入货盘
            </Button>
          </AuthWrapper>
        )}
        {!!liveRoundInfo?.liveDate && (
          <AuthWrapper functionName="f_copy_this_add_selection_round">
            <Button
              type="default"
              size="small"
              onClick={() => {
                handleShow('complate');
              }}
            >
              复制本场货盘
            </Button>
          </AuthWrapper>
        )}
        <Checkbox
          checked={!!(infoList.length && infoList.length === goodsList.length)}
          onChange={handleSelectAllChange}
          indeterminate={indeterminate}
        >
          全选
        </Checkbox>
        <a onClick={handleCheckboxCancel} style={{ marginLeft: '-5px' }}>
          取消
        </a>
      </Space>
      <Modal
        title="批量加入场次货盘"
        visible={state.visible}
        onCancel={() => {
          handleCancel();
        }}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          type === 'complate' && (
            <Button
              key="pureCommission"
              type="primary"
              style={{ backgroundColor: 'rgb(3,180,147)', borderColor: 'rgb(3,180,147)' }}
              onClick={() => {
                handleAddSelection(false);
              }}
            >
              仅加入纯佣商品
            </Button>
          ),
          <Button
            key="all"
            type="primary"
            onClick={() => {
              handleAddSelection(true);
            }}
          >
            {type === 'complate' ? '全部加入' : '确定'}
          </Button>,
        ]}
        width={500}
      >
        <Form labelAlign="right" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <Form.Item label="直播间" style={{ marginBottom: 0 }}>
            {form.getFieldDecorator('liveRoomId', {
              rules: [{ required: true, message: '请选择直播间' }],
            })(
              <Select
                allowClear={true}
                optionFilterProp="children"
                maxTagCount={1}
                showSearch
                filterOption={true}
                placeholder="请选择"
                style={{ width: 300 }}
                labelInValue
              >
                {state.liveRoomOptions.map((item) => (
                  <Select.Option value={item.id}>{item.name}</Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="直播日期" style={{ marginBottom: 0 }}>
            {form.getFieldDecorator('liveDate', {
              rules: [{ required: true, message: '请选择直播日期' }],
            })(<DatePicker allowClear style={{ width: 300 }} disabledDate={disabledDate} />)}
          </Form.Item>
        </Form>
        {tableList.length && type === 'complate' ? (
          <div>
            <p style={{ fontStyle: '500', color: 'black' }}>以下商品含有基础服务费：</p>
            <Table
              columns={columns}
              dataSource={tableList}
              pagination={false}
              scroll={{ y: 500 }}
              rowKey="id"
            />
          </div>
        ) : (
          ''
        )}
      </Modal>
    </>
  );
};
export default Form.create()(BatchAddSelection);
