import { Form, Icon, InputNumber, Popover, Select, Table, Tag } from 'antd';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ColumnProps, TableRowSelection } from 'antd/lib/table';
import React, { useEffect, useMemo, useState } from 'react';
import { GetServiceTypeResult } from '@/services/yml/choose';
import { SelectionProcessKanbanPageResult } from '@/pages/selection-flow-board/services/yml';
import TableTitle from './TableTitle';
import TableNotice from './TableNotice';
import {
  GuaranteedDetailResult,
  GuaranteedRelDetailListResult,
} from '@/services/yml/quality-assurance-cooperation';
import styles from './index.module.less';
type TableType = NonNullable<SelectionProcessKanbanPageResult['records']>[number];
type ListInfo = TableType & {
  childrenMsg: GetServiceTypeResult;
};
type PropsType = {
  form: WrappedFormUtils;
  goodsInfoList: ListInfo[];
  onSelect?: (indexList: number[]) => void;
  guaranteedDetailArray?: GuaranteedDetailResult[];
  guaranteedRelDetailListLoading: boolean;
  guaranteedRelDetail: GuaranteedRelDetailListResult;
};
const formContentStyle: React.CSSProperties = {
  width: '100px',
};
const formStyle: React.CSSProperties = {
  margin: 'auto',
};
const FrameworkSelectFormTable: React.FC<PropsType> = ({
  form,
  goodsInfoList,
  guaranteedDetailArray,
  guaranteedRelDetailListLoading,
  guaranteedRelDetail,
}) => {
  const { getFieldDecorator } = form;
  const [selectedRowKeyList, setSelectedRowKeys] = useState<number[]>([]);
  const isLiveShow = useMemo(
    () => !!goodsInfoList.find((item) => !!item?.childrenMsg),
    [goodsInfoList],
  );
  const isLiveRoundModeShow = useMemo(
    () =>
      !!goodsInfoList.find((item) =>
        item.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE'),
      ),
    [goodsInfoList],
  );
  const isGmvModeShow = useMemo(
    () => !!goodsInfoList.find((item) => item.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE')),
    [goodsInfoList],
  );
  const isGuaranteeQuantityIdShow = useMemo(
    () => !!goodsInfoList.find((item) => item.guaranteeQuantityId && guaranteedRelDetail[item?.no]),
    [goodsInfoList, guaranteedRelDetail],
  );
  // const isGuaranteeQuantityIdShow = !!goodsInfoList.find(
  //   (item) => item.guaranteeQuantityId && guaranteedRelDetail[item?.no],
  // );
  const [feeRequired, setFeeRequired] = useState<boolean[]>([]);
  useEffect(() => {
    setFeeRequired(
      goodsInfoList.map(
        (item) =>
          !!item.frameworkRoundFlag &&
          !!item?.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE'),
      ),
    );
  }, [goodsInfoList]);
  const frameworkTableFormColumns: ColumnProps<ListInfo>[] = useMemo(
    () => [
      {
        title: '#',
        key: 'id',
        dataIndex: 'id',
        render: (id: string, record, index) => {
          return (
            <>
              <Form.Item style={{ display: 'none' }}>
                {getFieldDecorator('id' + index, {
                  initialValue: record?.id,
                })(<p>{index + 1}</p>)}
              </Form.Item>
              {index + 1}
            </>
          );
        },
        // width: 50,
      },
      { title: '选品流程编号', key: 'no', dataIndex: 'no' },
      {
        title: '商品名称',
        key: 'spuName',
        dataIndex: 'spuName',
        render: (spuName: string, records) => (
          <Popover title="商品名称" content={spuName}>
            <p className={styles['ellipsis-box']}>
              【{records?.brandName || '-'}】{records?.spuName || '-'}
            </p>
          </Popover>
        ),
      },
      {
        title: '场次',
        key: 'liveRoundName',
        dataIndex: 'liveRoundName',
        render: (liveRoundName: string) => {
          return liveRoundName;
        },
      },
      ...(isLiveShow
        ? [
            {
              title: (
                <TableTitle
                  title="直播服务类型"
                  // isCopy={goodsInfoList.length > 1}
                  form={form}
                  keyName={'liveServiceTypeId'}
                  noRequired={!!goodsInfoList.find((item) => !item.childrenMsg?.required)}
                />
              ),
              key: 'liveServiceType',
              dataIndex: 'liveServiceType',
              render: (liveServiceType: string, record: ListInfo, index: number) => {
                const item = record?.childrenMsg?.serviceTypeOptions?.find(
                  (item) => item?.name === liveServiceType,
                );
                return !record.childrenMsg ? (
                  <Select allowClear style={formContentStyle} placeholder="请选择" disabled={true}>
                    {record.childrenMsg?.serviceTypeOptions?.map((item) => {
                      return <Select.Option value={item.id}>{item.name}</Select.Option>;
                    })}
                  </Select>
                ) : (
                  <Form.Item style={formStyle}>
                    {getFieldDecorator('liveServiceTypeId' + index, {
                      initialValue: item ? { key: item?.id, label: item?.name } : undefined,
                      rules: [
                        {
                          required: record.childrenMsg?.required === 1,
                          message: '请选择直播服务类型',
                        },
                      ],
                    })(
                      <Select
                        allowClear
                        style={formContentStyle}
                        placeholder="请选择"
                        labelInValue={true}
                        onChange={() => {
                          form.setFieldsValue({
                            [`depositAmount${index}`]: undefined,
                          });
                        }}
                      >
                        {record.childrenMsg?.serviceTypeOptions?.map((item) => {
                          return <Select.Option value={item.id}>{item.name}</Select.Option>;
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                );
              },
            },
            {
              title: '定金金额',
              key: 'depositAmount',
              dataIndex: 'depositAmount',
              render: (depositAmount: string, record, index: number) => {
                const liveServiceType = form.getFieldValue('liveServiceTypeId' + index);
                // console.log('🚀 ~ liveServiceType:', liveServiceType);
                if (
                  ['预售挂链', '预售讲解', '预热挂链', '预热讲解'].includes(liveServiceType?.label)
                ) {
                  return (
                    <Form.Item style={formStyle}>
                      {getFieldDecorator('depositAmount' + index, {
                        initialValue: depositAmount,
                        rules: [{ required: true, message: '定金金额' }],
                      })(
                        <InputNumber
                          min={0}
                          max={9999999.99}
                          placeholder="定金金额"
                          formatter={(value) => `¥${value}`}
                          parser={(value) => value!.replace('¥', '')}
                          precision={2}
                        />,
                      )}
                    </Form.Item>
                  );
                } else {
                  return '-';
                }
              },
            },
          ]
        : []),
      ...(isLiveRoundModeShow
        ? [
            {
              title: (
                <TableTitle
                  title="是否年框场次"
                  isCopy={goodsInfoList.length > 1}
                  form={form}
                  keyName={'frameworkRoundFlag'}
                />
              ),
              key: 'frameworkRoundFlag',
              dataIndex: 'frameworkRoundFlag',

              render: (frameworkRoundFlag: boolean, record, index: number) =>
                !record.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE') ? (
                  <Select allowClear style={formContentStyle} placeholder="请选择" disabled={true}>
                    <Select.Option label="是" value={true}>
                      是
                    </Select.Option>
                    <Select.Option label="否" value={false}>
                      否
                    </Select.Option>
                  </Select>
                ) : (
                  <section style={{ display: 'flex' }}>
                    <Form.Item style={formStyle}>
                      {getFieldDecorator('frameworkRoundFlag' + index, {
                        initialValue: frameworkRoundFlag,
                        rules: [
                          {
                            required: true,
                            message: '是否年框场次',
                          },
                        ],
                      })(
                        <Select
                          allowClear
                          style={formContentStyle}
                          placeholder="请选择"
                          onChange={(value: boolean) => {
                            feeRequired[index] = value;
                            setFeeRequired(feeRequired);
                          }}
                        >
                          <Select.Option label="是" value={true}>
                            是
                          </Select.Option>
                          <Select.Option label="否" value={false}>
                            否
                          </Select.Option>
                        </Select>,
                      )}
                    </Form.Item>
                    <TableNotice
                      info={record}
                      type="frameworkRoundFlag"
                      guaranteedDetail={guaranteedDetailArray?.find(
                        (item) => item.id === record.guaranteeQuantityId,
                      )}
                    />
                  </section>
                ),
            },
            {
              title: (
                <TableTitle
                  title="年框品牌费"
                  isCopy={goodsInfoList.length > 1}
                  form={form}
                  keyName={'roundTotalFees'}
                  noRequired={!!feeRequired.find((item) => !item)}
                />
              ),
              key: 'roundTotalFees',
              dataIndex: 'roundTotalFees',
              hidden: true,
              render: (roundTotalFees: string, record, index: number) => (
                <>
                  {feeRequired[index] ? (
                    <Form.Item style={formStyle}>
                      {getFieldDecorator('roundTotalFees' + index, {
                        initialValue: roundTotalFees,
                        rules: [
                          {
                            required: feeRequired[index],
                            message: '年框品牌费',
                          },
                        ],
                      })(
                        <InputNumber
                          disabled={!feeRequired[index]}
                          formatter={(value) => `¥${value}`}
                          parser={(value) => value!.replace('¥', '')}
                          placeholder="请输入"
                          maxLength={11}
                          style={formContentStyle}
                          min={0}
                        />,
                      )}
                    </Form.Item>
                  ) : (
                    <InputNumber
                      disabled={!feeRequired[index]}
                      formatter={(value) => `¥${value}`}
                      parser={(value) => value!.replace('¥', '')}
                      placeholder="请输入"
                      maxLength={11}
                      style={formContentStyle}
                      min={0}
                    />
                  )}
                </>
              ),
            },
          ]
        : []),
      ...(isGmvModeShow
        ? [
            {
              title: (
                <TableTitle
                  title="是否年框GMV"
                  isCopy={goodsInfoList.length > 1}
                  form={form}
                  keyName={'frameworkGmvFlag'}
                />
              ),
              key: 'frameworkGmvFlag',
              dataIndex: 'frameworkGmvFlag',
              render: (frameworkGmvFlag: boolean, record, index: number) =>
                !record.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE') ? (
                  <Select allowClear style={formContentStyle} placeholder="请选择" disabled={true}>
                    <Select.Option label="是" value={true}>
                      是
                    </Select.Option>
                    <Select.Option label="否" value={false}>
                      否
                    </Select.Option>
                  </Select>
                ) : (
                  <section style={{ display: 'flex' }}>
                    <Form.Item style={{ ...formStyle, marginRight: '5px' }}>
                      {getFieldDecorator('frameworkGmvFlag' + index, {
                        initialValue: frameworkGmvFlag,
                        rules: [
                          {
                            required: selectedRowKeyList.includes(index),
                            message: '是否年框GMV',
                          },
                        ],
                      })(
                        <Select allowClear style={formContentStyle} placeholder="请选择">
                          <Select.Option label="是" value={true}>
                            是
                          </Select.Option>
                          <Select.Option label="否" value={false}>
                            否
                          </Select.Option>
                        </Select>,
                      )}
                    </Form.Item>
                    <TableNotice
                      info={record}
                      type="frameworkGmvFlag"
                      guaranteedDetail={guaranteedDetailArray?.find(
                        (item) => item.id === record.guaranteeQuantityId,
                      )}
                    />
                  </section>
                ),
            },
          ]
        : []),
      ...(isGuaranteeQuantityIdShow
        ? [
            {
              title: (
                <TableTitle
                  title="是否保量"
                  isCopy={goodsInfoList.length > 1}
                  form={form}
                  keyName={'guaranteeQuantityFlag'}
                  tableLength={goodsInfoList.length}
                />
              ),
              key: 'guaranteeQuantityFlag',
              dataIndex: 'guaranteeQuantityFlag',
              width: 220,
              render: (guaranteeQuantityFlag: boolean, record, index: number) =>
                record.guaranteeQuantityId && guaranteedRelDetail[record?.no] ? (
                  <section style={{ display: 'flex', justifyContent: 'flex-start' }}>
                    <Form.Item style={{ margin: 'auto 0' }}>
                      {getFieldDecorator('guaranteeQuantityFlag' + index, {
                        initialValue: guaranteeQuantityFlag,
                        rules: [
                          {
                            required: true,
                            message: '是否保量',
                          },
                        ],
                      })(
                        <Select allowClear style={formContentStyle} placeholder="请选择">
                          <Select.Option label="是" value={true}>
                            是
                          </Select.Option>
                          <Select.Option label="否" value={false}>
                            否
                          </Select.Option>
                        </Select>,
                      )}
                    </Form.Item>
                    <TableNotice
                      info={record}
                      type="guaranteeQuantityFlag"
                      guaranteedDetail={guaranteedDetailArray?.find(
                        (item) => item.id === record.guaranteeQuantityId,
                      )}
                    />
                    {guaranteedDetailArray?.find((item) => item.id === record.guaranteeQuantityId)
                      ?.almostAchievedFlag ? (
                      <Tag
                        color="green"
                        style={{ marginLeft: '4px', height: '22px', marginTop: '8px' }}
                      >
                        即将达成
                      </Tag>
                    ) : null}
                  </section>
                ) : (
                  <Select allowClear style={formContentStyle} placeholder="请选择" disabled={true}>
                    <Select.Option label="是" value={true}>
                      是
                    </Select.Option>
                    <Select.Option label="否" value={false}>
                      否
                    </Select.Option>
                  </Select>
                ),
            },
          ]
        : []),
    ],
    [goodsInfoList, selectedRowKeyList, feeRequired, isGuaranteeQuantityIdShow],
  );
  useEffect(() => {
    setSelectedRowKeys(goodsInfoList.map((item, index) => index));
  }, [goodsInfoList]);
  const rowSelection: TableRowSelection<PropsType['goodsInfoList']> = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    selectedRowKeys: selectedRowKeyList,
  };
  return (
    <Table
      rowSelection={rowSelection}
      pagination={false}
      columns={frameworkTableFormColumns}
      dataSource={goodsInfoList}
      scroll={{ y: 400 }}
      loading={guaranteedRelDetailListLoading}
    />
  );
};

export default FrameworkSelectFormTable;
