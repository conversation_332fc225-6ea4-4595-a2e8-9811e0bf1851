import React, { useEffect, useState, useRef, useMemo, useContext } from 'react';
import {
  Card,
  Tag,
  Button,
  Row,
  Col,
  Popover,
  Select,
  Icon,
  Modal,
  message,
  Spin,
  Form,
} from 'antd';
import { ActionType, ReasonList, roleInfoType, FlowTagColor } from './type';
import './goodsCard.less';
import TagGroup from './TagGroup';
import request from '@/assets/request.png';
import linkImg from '@/assets/link-image.png';
import { AuthWrapper } from 'qmkit';
import YBS from '@/assets/ybs.png';
import JingGao from '@/assets/jinggao.png';
import {
  FlowStatus_ChoiceList,
  FlowStatusColor_ChoiceList,
} from '@/common/constants/moduleConstant';
import {
  bpConfirm,
  bpCancle,
  bpEdit,
  roleDel,
  opConfirm,
  chConfirm,
  chEdit,
  droppedProductRepeat,
  LiveGoodsListResult,
  createBySelectionRound,
  NewLiveGoodsListResult,
  LiveGoodsInfoResult,
  QueryBySelectionIdListResult,
} from '@/services/yml/choiceList/index';
import ChooseModal from './chooseModal';
import GoodsInfoEdit from '@/components/GoodsInfoEdit/index';
import { liveGoodsInfo, getOneLink } from '@/services/yml/choiceList/index';
import { isSignAgreement } from '@/services/yml/signAgreement/index';
import {
  AddPayVoucher,
  ConfirmPayVoucher,
  MarkVoucher,
  AddPayVoucherModal,
  ApproveModal,
  SuppLinkModal,
  LegalCheckDrawer,
} from '../../index';
import { useRequest } from 'ahooks';
import { checkAuth } from 'qmkit';
import { Tooltip } from 'antd';
import styles from '../../../index.module.less';
import { useBtn } from '../../../hooks/useBtn';
import { SuppLinkRefProps } from '../../suppLinkModal';
// import { getServiceType } from '@/services/yml/live-service-type-configuration/index';
import { LowCommissionAuditStatusEnum } from '@/components/GoodsInfoEdit/leftCard';
import {
  getServiceType,
  GetServiceTypeResult,
} from '@/services/yml/live-service-type-configuration/index';
import GoodsCardConfirmModal from '../components/GoodsCardConfirmModal';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  GuaranteedDetailResult,
  guaranteedDetail,
} from '@/services/yml/quality-assurance-cooperation';
import HighRisk, { HighRiskRefProps } from '../components/HighRisk';
import { numberContrast } from '@/utils/formatTime';
import Checkbox from '@/pages/goods-assorting/component/Checkbox';
import ChoiceListContext from '@/pages/choice-list-new/dataSouce/context';
import BatchSupplementLink from '@/pages/selection-flow-board/components/BatchSupplementLink';
import { PageContext } from '@/pages/choice-list-new';
import SubjectSpecilApprovalModal, {
  SubjectModalRefType,
} from '../components/SubjectSpecilApprovalModal';
import { debounce } from 'lodash';
import {
  auditWithdraw,
  AuditWithdrawRequest,
} from '@/pages/selection-flow-board/services/yml/index';
import { formatFee } from '@/pages/anchor-information/utils/getColumns';

import {
  UALITY_INSPECTION_STATUS_COLOR,
  UALITY_INSPECTION_STATUS_ENUM,
  UALITY_INSPECTION_STATUS_NAME,
} from '../../../../../../web_modules/types';
import { numberToColor } from '@/pages/selection-flow-board/utils';
import Auth from '@/pages/auth';
import {
  GOODS_GRADES_STATE,
  GOODS_GRADES_STATE_ENUM,
  GOODS_GRADES_STATE_NAME,
  COMPLIANCESTATUS_NAME,
  COMPLIANCESTATUS_COLOR,
  COMPLIANCESTATUS_ICON,
  COMPLIANCESTATUS_ENUM,
  COMPLIANCESTATUS_COLOR_NAME,
  LUXURY_REVIEW_STATUS_COLOR_NAME,
  LUXURY_REVIEW_STATUS_ENUM,
} from '../../../tools';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import SpecialApprovalGoods from '@/pages/selection-flow-board/components/SpecialApprovalGoods';
import { useSpecialApprovalGoods } from '@/pages/selection-flow-board/hooks';
import { PAY_DAY_ABBREVIATE, PAY_DAY } from '@/pages/quality-assurance-cooperation/types';
interface PropsType {
  info?: LiveGoodsListInfo;
  openEdit?: any; //商务编辑
  search?: any;
  getLiveCountList?: any;
  formData?: any;
  liveRoundId?: string;
  roundListStatus?: any;
  cardWidth?: number;
  codeList?: any;
}
export const ApproveIconStatus = {
  //通过
  PASS: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  //不通过
  REJECT: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>驳回
      </span>
    ),
  },
  //商务审核中是通过
  WAIT_AUDIT: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#108ee9', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#108ee9' }}></span>待审核
      </span>
    ),
  },
  CONFIRMING: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>审核中
      </span>
    ),
  },
  INVALID: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>已失效
      </span>
    ),
  },
  WITHDRAW: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>撤回
      </span>
    ),
  },
};
const IconStatus = {
  // 跳过是通过
  SKIP: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>跳过
      </span>
    ),
  },
  // 自动过审是通过
  AUTO_PASS: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>自动过审
      </span>
    ),
  },
  //通过
  PASS: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  //待审核
  INIT: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>待审核
      </span>
    ),
  },
  //不通过
  REJECT: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>不通过
      </span>
    ),
  },
  //未开始
  null: {
    icon: (
      <Icon style={{ color: 'gray', marginRight: '5px' }} theme="outlined" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#FAAD14', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#FAAD14' }}></span>未开始
      </span>
    ),
  },
  //未开始
  INVITING: {
    icon: (
      <Icon style={{ color: 'gray', marginRight: '5px' }} theme="outlined" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#FAAD14', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#FAAD14' }}></span>未开始
      </span>
    ),
  },
  //商务审核中是通过
  WAIT_AUDIT: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  //商务待直播是通过
  WAIT_LIVE: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  //商务已直播是通过
  COMPLETED_LIVE: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  //商务已掉品是没通过
  ABORT_LIVE: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#f50', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#f50' }}></span>不通过
      </span>
    ),
  },
  ABORT_WAIT_LIVE: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#f50', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#f50' }}></span>不通过
      </span>
    ),
  },
  //商务已掉品是没通过
  CANCEL: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>已取消
      </span>
    ),
  },
  //商务待确认就是待审核
  BP_CONFIRMING: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>待审核
      </span>
    ),
  },
  // 法务高风险 不通过
  HIGH: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>不通过
      </span>
    ),
  },
  // 法务中风险就是已通过
  MIDDLE: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  HIGH_SPECIAL: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  // 法务低风险就是已通过
  LOW: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  // 法务待审核
  NONE: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '8px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>待审核
      </span>
    ),
  },
};
type LiveGoodsList = LiveGoodsListResult['records'];
export type LiveGoodsListInfo = NonNullable<LiveGoodsList>[number];
const GoodsCard: React.FC<PropsType> = (props: PropsType) => {
  const {
    info,
    search,
    getLiveCountList,
    formData,
    liveRoundId,
    roundListStatus,
    cardWidth,
    codeList,
  } = props;
  const [visible, setVisible] = useState(false);
  const [chooseVisible, setChooseVisible] = useState(false);
  const [reason, setReason] = useState();
  const chooseModalRef = useRef(null);
  const goodsInfoRef = useRef(null);
  const suppLinkRef = useRef<SuppLinkRefProps>(null);
  const [roleInfo, setRoleInfo] = useState<{
    type: string;
    color: string;
    title: string;
    info: string;
  }>();
  const [goodsInfo, setGoodsInfo] = useState<LiveGoodsInfoResult>({});
  const [addVisible, setAddVisible] = useState(false);
  const [legalVisible, setLegalVisible] = useState(false);
  const [confirrmPayVoucherVisible, setConfirrmPayVoucherVisible] = useState(false);
  const [markVoucherVisible, setMarkVoucherVisible] = useState(false);
  const [ApproveVisible, setApproveVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [childrenMsg, setChildMsg] = useState<GetServiceTypeResult>();
  const [liveServiceType, setValue] = useState<string>();
  const highRiskRef = useRef<HighRiskRefProps>();
  const { liveRoomId } = useContext(PageContext);
  const isFindId = () =>
    childrenMsg?.serviceTypeOptions?.find((item) => item.id == info?.liveServiceTypeId);
  useEffect(() => {
    setError(false);
    setValue(
      info?.liveServiceTypeId
        ? isFindId()
          ? info?.liveServiceTypeId
          : info?.liveServiceType
        : undefined,
    );
  }, [visible]);
  const closeMarkVoucherVisible = () => {
    setMarkVoucherVisible(false);
  };

  const closeConfirrmPayVoucherVisible = () => {
    setConfirrmPayVoucherVisible(false);
  };

  const closeAddVisible = () => {
    setAddVisible(false);
  };

  const closeApproveVisible = () => {
    setApproveVisible(false);
  };

  const handleGoods = (value: string | object, type: string) => {
    liveGoodsInfo({ id: value.id }).then((res) => {
      if (res?.res?.code === '200') {
        setGoodsInfo(res.res.result);
        if (!type) return;
        setTimeout(() => {
          if (type === 'edit') {
            goodsInfoRef?.current?.onOpen('edit');
          } else if (type === 'info') {
            goodsInfoRef?.current?.onOpen();
          } else {
            chooseModalRef?.current?.onOpen();
          }
        }, 100);
      }
    });
  };
  const handleLiveGoodsInfo = async () => {
    const result = await responseWithResultAsync({
      request: liveGoodsInfo,
      params: { id: info?.id },
    });
    result && setGoodsInfo(result);
  };
  const [guaranteedDetailInfo, setguaranteedInfo] = useState<GuaranteedDetailResult>({});
  const getGuaranteedDetail = async () => {
    const detailListResult = await responseWithResultAsync({
      request: guaranteedDetail,
      params: { id: info?.guaranteeQuantityId },
    });
    detailListResult && setguaranteedInfo(detailListResult);
  };
  const confirmHandle = async (type: string) => {
    if (['businessCancle', 'chooseConfirm', 'del', 'opConfirm'].indexOf(type) > -1) {
      handleLiveGoodsInfo();
      setVisible(true);
      setRoleInfo(roleInfoType[type]);
    }
    //生成预付款单
    if (type === 'prepayment') {
      createBySelectionRound({ selectionRoundId: info?.id }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    if (type === 'businessConfirm' || type === 'businessConfirmAuth') {
      // getIsSign();
      await getServiceType({ liveRoundId: liveRoundId }).then((res) => {
        if (res?.res?.code === '200') {
          setChildMsg(res?.res?.result);
        }
      });
      getIsSign();
      (await info?.guaranteeQuantityId) && getGuaranteedDetail();
      setVisible(true);
    }
  };
  //检查是否签署
  const getIsSign = () => {
    isSignAgreement({ id: info?.id })
      .then((res) => {
        // console.log(res);
        // return res?.result;
        if (res?.res?.result === true) {
          setRoleInfo(roleInfoType['businessConfirm']);
        } else {
          setRoleInfo(roleInfoType['businessConfirmAuth']);
        }
      })
      .catch((err) => {
        return false;
      });
  };
  const [isError, setError] = useState(false);
  const handleOk = (e) => {
    e.stopPropagation();
    if (roleInfo.type === 'businessConfirmAuth' || roleInfo.type === 'businessConfirm') {
      if (!liveServiceType && childrenMsg?.required) {
        setError(true);
        return;
      }
    }
    setVisible(false);
    setValue(null);
    // 商务确认
    // if (roleInfo.type === 'businessConfirmAuth') {
    //   bpConfirm({ id: info?.id, version: info?.version }).then((res) => {
    //     if (res?.res.code === '200') {
    //       message.success('操作成功');
    //       onGetList();
    //     } else {
    //       message.warn(res?.res?.message);
    //     }
    //   });
    // }
    if (roleInfo.type === 'businessConfirm' || roleInfo.type === 'businessConfirmAuth') {
      bpConfirm({
        id: info?.id,
        version: info?.version,
        liveServiceTypeId:
          liveServiceType === info?.liveServiceType ? info?.liveServiceTypeId : liveServiceType,
      }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 商务取消
    if (roleInfo?.type === 'businessCancle') {
      bpCancle({ id: info?.id, version: info?.version }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 选品确认
    if (roleInfo?.type === 'chooseConfirm') {
      chConfirm({ id: info?.id, version: info?.version }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 运营确认
    if (roleInfo?.type === 'opConfirm') {
      opConfirm({ id: info?.id, version: info?.version }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
    // 掉品
    if (roleInfo?.type === 'del') {
      roleDel({ id: info?.id, reason: reason, version: info?.version }).then((res) => {
        if (res?.res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warn(res?.res?.message);
        }
      });
    }
  };
  const handleCancel = (e) => {
    e.stopPropagation();
    setVisible(false);
    setValue(null);
  };
  const onGetList = () => {
    search(formData);
    getLiveCountList();
  };
  const StatusTxt = useMemo(
    () => (
      <div className="hoverGroup">
        {info?.bpConfirmSwitch === 'ON' && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>{IconStatus[info?.bpStatus]?.name}</span>
            <span>商务{info?.bpName ? ' ' + info?.bpName : ''}</span>
          </div>
        )}
        {info?.selectionCheckSwitch === 'ON' && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {/* {IconStatus[info?.selectionStatus].icon} */}
              {IconStatus[info?.selectionStatus]?.name}
            </span>
            <span>选品{info?.selectionAuditorName ? ' ' + info?.selectionAuditorName : ''}</span>
          </div>
        )}
        {info?.operatorCheckSwitch === 'ON' && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {/* {IconStatus[info?.operatorStatus].icon} */}
              {IconStatus[info?.operatorStatus]?.name}
            </span>
            <span>运营{info?.operatorAuditorName ? ' ' + info?.operatorAuditorName : ''}</span>
          </div>
        )}
        {info?.legalCheckSwitch === 'ON' && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {/* {IconStatus[info?.legalStatus].icon} */}
              {IconStatus[info?.legalStatus]?.name}
            </span>
            <span>法务{info?.legalAuditorName ? ' ' + info?.legalAuditorName : ''}</span>
          </div>
        )}
        {info?.lowCommissionAuditStatus !== 'NONE' && (
          <div className="iconPGroup">
            <span style={{ marginRight: '18px' }}>
              {/* {IconStatus[info?.legalStatus].icon} */}
              {LowCommissionAuditStatusEnum[info?.lowCommissionAuditStatus]?.name}
            </span>
            <span>
              低佣{info?.lowCommissionAuditorName ? ' ' + info?.lowCommissionAuditorName : ''}
            </span>
          </div>
        )}
        {info?.supplierConfirmSwitch === 'ON' && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {/* {IconStatus[info?.supplierStatus].icon} */}
              {IconStatus[info?.supplierStatus]?.name}
            </span>
            <span>商家{info?.supplierName ? ' ' + info?.supplierName : ''}</span>
          </div>
        )}
        {info?.legalStatus === 'HIGH' && info?.specialAuditStatus && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {/* {ApproveIconStatus[info.specialAuditStatus!].icon} */}
              {ApproveIconStatus[info.specialAuditStatus!].name}
            </span>
            <span>资质特批</span>
          </div>
        )}
        {/* 商品分 是 审批中或者已通过显示 显示样式 */}
        {[
          GOODS_GRADES_STATE_ENUM.CONFIRMING,
          GOODS_GRADES_STATE_ENUM.PASS,
          GOODS_GRADES_STATE_ENUM.REJECT,
        ].includes(info?.qualityScoreAuditStatus) && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {GOODS_GRADES_STATE[info?.qualityScoreAuditStatus]?.name}
            </span>
            <span>商品分</span>
          </div>
        )}
        {info?.complianceStatus && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {COMPLIANCESTATUS_COLOR_NAME[info?.complianceStatus as COMPLIANCESTATUS_ENUM]}
            </span>
            <span>合规{info?.complianceAuditorName ? ' ' + info?.complianceAuditorName : ''}</span>
          </div>
        )}
        {info?.luxuryReviewStatus &&
        info?.luxuryReviewStatus !== LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW ? (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {
                LUXURY_REVIEW_STATUS_COLOR_NAME[
                  info?.luxuryReviewStatus as LUXURY_REVIEW_STATUS_ENUM
                ]
              }
            </span>
            <span>复查</span>
          </div>
        ) : (
          <></>
        )}

        {/* {info?.legalStatus === 'HIGH' && info?.specialAuditStatus && (
          <div className="iconPGroup">
            <span style={{ marginRight: '11px' }}>
              {ApproveIconStatus[info.specialAuditStatus!].icon}
              {info?.supplierBodySpecialAuditStatus
                ? ApproveIconStatus[info.supplierBodySpecialAuditStatus].name
                : ''}
            </span>
            <span>主体特批</span>
          </div>
        )} */}
      </div>
    ),
    [info],
  );

  const { run: droppedProductRepeatRun, loading: droppedProductRepeatLoading } = useRequest(
    droppedProductRepeat,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (res.code === '200') {
          message.success('操作成功');
          onGetList();
        } else {
          message.warning(res.message || '网络异常');
        }
      },
    },
  );

  const handleReplay = () => {
    Modal.confirm({
      icon: (
        <Icon
          type="exclamation-circle"
          style={{ color: '#FAAD14', fontSize: '16px', marginTop: '4px' }}
        />
      ),
      title: '提示',
      content: '您确认要复播此产品吗?',
      onOk: () => {
        droppedProductRepeatRun({ id: info?.id });
      },
    });
  };

  const AddPayVoucherBtn = useMemo(() => {
    if (!info) {
      return false;
    }
    if (!Object.keys(info || {}).length) {
      return false;
    }
    if (['CONFIRMED'].includes(info?.paymentVoucherStatus)) {
      return false;
    }
    //
    if (
      info?.brandFee > 0 &&
      (!info?.paymentOrderStatus ||
        ['PENDING_PAYMENT', 'PAYING'].includes(info?.paymentOrderStatus))
    ) {
      return true;
    }
    if (
      info?.brandFee > 0 &&
      ['FULL_PAYMENT', 'SETTLED'].includes(info?.paymentOrderStatus) &&
      ['OFFLINE_VOUCHER'].includes(info?.paymentMethod)
    ) {
      return true;
    }
  }, [info]);
  const [btnKeys, setBtnKeys] = useState<string[]>([]);
  const [BPbtnKeys, setBPBtnKeys] = useState<string[]>([]);
  const [WAITbtnKeys, setWAITBtnKeys] = useState<string[]>([]);
  const [COMPLETEDbtnKeys, setCOMPLETEDBtnKeys] = useState<string[]>([]);
  const [INVIbtnKeys, setINVIBtnKeys] = useState<string[]>([]);
  const [ABORTLIVEbtnKeys, setABORTLIVEBtnKeys] = useState<string[]>([]);
  const SubJectModalRef = useRef<SubjectModalRefType>();
  const openSubjectModal = () => {
    SubJectModalRef?.current?.onOpen(info);
  };
  const handleCancelHighRiskAudit = debounce(async (value: any) => {
    console.log('特批撤回', value);
    try {
      const params: AuditWithdrawRequest = {
        id: value.specialAuditId,
      };
      const { res } = await auditWithdraw(params);
      if (res?.code === '200') {
        console.log('特批撤回res', res);
        onGetList();
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log('特批撤回接口报错', e);
    }
  }, 300);

  // 商品分特批
  const { specialApprovalGoodsVisible, setSpecialApprovalGoodsVisible, openSpecialApprovalGoods } =
    useSpecialApprovalGoods();

  const {
    btnElement,
    btnMap,
    WAIT_LIVE_Element,
    BP_CONFIRMING_Element,
    COMPLETED_LIVE_Element,
    BP_CONFIRMING_Map,
    WAIT_LIVE_Map,
    COMPLETED_LIVE_Map,
    INVI_Map,
    INVI_Element,
    // BPbtnKeys,
    // WAITbtnKeys,
    // COMPLETEDbtnKeys,
    // btnKeys,
    ABORT_LIVE_Element,
    ABORT_LIVE_Map,
  } = useBtn(
    info,
    onGetList,
    AddPayVoucherBtn,
    confirmHandle,
    handleGoods,
    setAddVisible,
    setConfirrmPayVoucherVisible,
    setMarkVoucherVisible,
    BPbtnKeys,
    WAITbtnKeys,
    COMPLETEDbtnKeys,
    btnKeys,
    setBtnKeys,
    setBPBtnKeys,
    setWAITBtnKeys,
    setCOMPLETEDBtnKeys,
    setApproveVisible,
    INVIbtnKeys,
    setINVIBtnKeys,
    setLegalVisible,
    highRiskRef,
    openSubjectModal,
    handleCancelHighRiskAudit,
    handleReplay,
    ABORTLIVEbtnKeys,
    setABORTLIVEBtnKeys,
    openSpecialApprovalGoods,
  );

  const openSuppLinkModal = (e) => {
    e.stopPropagation();
    suppLinkRef?.current?.onOpen();
  };
  const showOnlineHighRisk = useMemo(
    () =>
      info?.legalStatus === 'HIGH' &&
      (!info?.specialAuditStatus ||
        info?.specialAuditStatus === 'REJECT' ||
        info?.specialAuditStatus === 'WITHDRAW') &&
      (checkAuth('f_choice_list_high_risk_audit') ||
        checkAuth('f_choice_list_online_high_risk_audit')),
    [info],
  );
  const { infoList, dispatch } = useContext(ChoiceListContext);
  const handleSelectCard = (isCheck: boolean, info?: LiveGoodsListInfo) => {
    let list: LiveGoodsListInfo[] = [];
    if (isCheck) {
      list = infoList.find((item) => item?.id === info?.id) ? infoList : [...infoList, info!];
    } else {
      list = infoList.filter((item) => item?.id !== info?.id);
    }

    dispatch!({ type: 'infoList', value: list });
  };

  return (
    <Card className="goodsCrad" style={{ width: cardWidth + 'px' }}>
      <Spin spinning={isLoading}>
        <div
          className="topCard"
          onClick={() => {
            handleGoods(info, 'info');
          }}
          style={{ position: 'relative' }}
        >
          <section style={{ position: 'absolute', top: 8, right: 10, zIndex: 20, width: '24px' }}>
            <Checkbox
              checked={!!infoList?.find((item) => item?.id === info?.id)}
              onChange={(isCheck) => {
                handleSelectCard(isCheck, info);
              }}
            />
          </section>
          <img src={info?.image} alt={info.spuName} className="goodsImg" />
          <div className="rightTip">
            {info?.status && (
              <Tag color={FlowStatusColor_ChoiceList[info?.status]}>
                {FlowStatus_ChoiceList[info?.status]}
              </Tag>
            )}
          </div>
          {/* 待商务确认 */}
          {info?.status === 'BP_CONFIRMING' && (
            <div className="btnGourp" style={{ zIndex: '10' }}>
              {StatusTxt}
              {roundListStatus !== 'CANCEL' && (
                <div className="btnGourp bckTransparent">
                  {BP_CONFIRMING_Element}
                  {BPbtnKeys?.length > 3 ||
                  (BPbtnKeys?.length === 3 &&
                    ['f_add_pay_credential', 'f_choice_list_special_approval_goods'].includes(
                      BPbtnKeys[2],
                    )) ? (
                    <Tooltip
                      placement="rightTop"
                      overlayClassName={styles['btnMap']}
                      title={
                        //  width: '104px'
                        <ul style={{ cursor: 'pointer' }}>
                          {BPbtnKeys.slice(2).map((item, index) => {
                            return (
                              <li key={index}>
                                <AuthWrapper functionName={item}>
                                  {BP_CONFIRMING_Map[item].tDom}
                                </AuthWrapper>
                              </li>
                            );
                          })}
                        </ul>
                      }
                    >
                      <Button
                        className="btnStyle"
                        style={{ background: '#7B7B7B', color: 'white', border: 'none' }}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Icon type="ellipsis" />
                      </Button>
                    </Tooltip>
                  ) : (
                    <></>
                  )}
                </div>
              )}
            </div>
          )}
          {/* 审核中 */}
          {info?.status === 'WAIT_AUDIT' && (
            <div className="btnGourp" style={{ zIndex: '10' }}>
              {/* 运营通过 */}
              {StatusTxt}
              {roundListStatus !== 'CANCEL' && (
                <div className="btnGourp bckTransparent">
                  {btnElement}
                  {btnKeys?.length + (showOnlineHighRisk ? 1 : 0) > 3 ||
                  (btnKeys?.length === 3 &&
                    ['f_add_pay_credential', 'f_choice_list_special_approval_goods'].includes(
                      btnKeys[2],
                    )) ? (
                    <Tooltip
                      placement="rightTop"
                      overlayClassName={styles['btnMap']}
                      title={
                        //  width: '104px'
                        <ul style={{ cursor: 'pointer' }}>
                          {btnKeys.slice(2).map((item, index) => {
                            return (
                              <li key={index}>
                                <AuthWrapper functionName={item}>{btnMap[item].tDom}</AuthWrapper>
                              </li>
                            );
                          })}
                          {showOnlineHighRisk && (
                            <li>
                              <li
                                style={{ padding: '10px 8px', fontSize: '14px', color: '#ffffff' }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  highRiskRef?.current?.onOpen();
                                }}
                              >
                                资质特批
                              </li>
                            </li>
                          )}
                        </ul>
                      }
                    >
                      <Button
                        className="btnStyle"
                        style={{ background: '#7B7B7B', color: 'white', border: 'none' }}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Icon type="ellipsis" />
                      </Button>
                    </Tooltip>
                  ) : (
                    <></>
                  )}
                </div>
              )}
            </div>
          )}
          {/* 待直播 */}
          {info?.status === 'WAIT_LIVE' && (
            <div className="btnGourp" style={{ zIndex: '10' }}>
              {StatusTxt}
              {roundListStatus !== 'CANCEL' && (
                <div className="btnGourp bckTransparent">
                  {WAIT_LIVE_Element}
                  {WAITbtnKeys?.length > 3 ||
                  (WAITbtnKeys?.length === 3 &&
                    ['f_add_pay_credential', 'f_choice_list_special_approval_goods'].includes(
                      WAITbtnKeys[2],
                    )) ? (
                    <Tooltip
                      placement="rightTop"
                      overlayClassName={styles['btnMap']}
                      title={
                        //  width: '104px'
                        <ul style={{ cursor: 'pointer' }}>
                          {WAITbtnKeys.slice(2).map((item, index) => {
                            return (
                              <li key={index}>
                                <AuthWrapper functionName={item}>
                                  {WAIT_LIVE_Map[item].tDom}
                                </AuthWrapper>
                              </li>
                            );
                          })}
                        </ul>
                      }
                    >
                      <Button
                        className="btnStyle"
                        style={{ background: '#7B7B7B', color: 'white', border: 'none' }}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Icon type="ellipsis" />
                      </Button>
                    </Tooltip>
                  ) : (
                    <></>
                  )}
                </div>
              )}
            </div>
          )}
          {/* 掉品 */}
          {(info?.status === 'ABORT_LIVE' || info?.status === 'ABORT_WAIT_LIVE') && (
            <div className="btnGourp" style={{ zIndex: '10' }}>
              {StatusTxt}
              {/* {roundListStatus !== 'CANCEL' && (
                <AuthWrapper functionName="f_repeat_drop">
                  <Button
                    style={{
                      background: '#C30000',
                      border: 'none',
                      color: 'white',
                      fontWeight: 500,
                    }}
                    className="btnStyle"
                    // type="danger"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleReplay();
                      // confirmHandle('del');
                    }}
                  >
                    掉品复播
                  </Button>
                </AuthWrapper>
              )} */}
              {/* <AuthWrapper functionName="f_choice_list_look_legal">
                <Button
                  className="btnStyle"
                  style={{ background: '#7B7B7B', color: 'white', border: 'none', fontWeight: 500 }}
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    setLegalVisible(true);
                    // handleGoods(info, 'edit');
                  }}
                >
                  查看资质
                </Button>
              </AuthWrapper> */}
              {roundListStatus !== 'CANCEL' && (
                <div className="btnGourp bckTransparent">
                  {ABORT_LIVE_Element}
                  {ABORTLIVEbtnKeys?.length > 3 ||
                  (ABORTLIVEbtnKeys?.length === 3 &&
                    ['f_add_pay_credential', 'f_choice_list_special_approval_goods'].includes(
                      ABORTLIVEbtnKeys[2],
                    )) ? (
                    <Tooltip
                      placement="rightTop"
                      overlayClassName={styles['btnMap']}
                      title={
                        //  width: '104px'
                        <ul style={{ cursor: 'pointer' }}>
                          {ABORTLIVEbtnKeys.slice(2).map((item, index) => {
                            return (
                              <li key={index}>
                                <AuthWrapper functionName={item}>
                                  {ABORT_LIVE_Map[item].tDom}
                                </AuthWrapper>
                              </li>
                            );
                          })}
                        </ul>
                      }
                    >
                      <Button
                        className="btnStyle"
                        style={{ background: '#7B7B7B', color: 'white', border: 'none' }}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Icon type="ellipsis" />
                      </Button>
                    </Tooltip>
                  ) : (
                    <></>
                  )}
                </div>
              )}
            </div>
          )}
          {/* 掉品复播 */}
          {info?.status === 'ABORT_WAIT_LIVE' && (
            <div className="btnGourp">
              {StatusTxt}
              <div className="btnGourp bckTransparent">
                {info?.allowQualityScoreAudit && (
                  <AuthWrapper functionName="f_choice_list_special_approval_goods">
                    <Button
                      style={{ background: '#0D6DEA', color: 'white', fontWeight: 500 }}
                      className="btnStyle"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        openSpecialApprovalGoods();
                        // confirmHandle('prepayment');
                      }}
                      type="primary"
                    >
                      商品分特批
                    </Button>
                  </AuthWrapper>
                )}
              </div>
            </div>
          )}
          {/* 已播 */}
          {info?.status === 'COMPLETED_LIVE' && (
            <div className="btnGourp" style={{ zIndex: '10' }}>
              {StatusTxt}
              {roundListStatus !== 'CANCEL' && (
                <div className="btnGourp bckTransparent">
                  {COMPLETED_LIVE_Element}
                  {COMPLETEDbtnKeys?.length > 3 ||
                  (COMPLETEDbtnKeys?.length === 3 &&
                    ['f_add_pay_credential', 'f_choice_list_special_approval_goods'].includes(
                      COMPLETEDbtnKeys[2],
                    )) ? (
                    <Tooltip
                      placement="rightTop"
                      overlayClassName={styles['btnMap']}
                      title={
                        //  width: '104px'
                        <ul style={{ cursor: 'pointer' }}>
                          {COMPLETEDbtnKeys.slice(2).map((item, index) => {
                            return (
                              <li key={index}>
                                <AuthWrapper functionName={item}>
                                  {COMPLETED_LIVE_Map[item].tDom}
                                </AuthWrapper>
                              </li>
                            );
                          })}
                        </ul>
                      }
                    >
                      <Button
                        className="btnStyle"
                        style={{ background: '#7B7B7B', color: 'white', border: 'none' }}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Icon type="ellipsis" />
                      </Button>
                    </Tooltip>
                  ) : (
                    <></>
                  )}
                </div>
              )}
            </div>
          )}
          {/* 邀约中 */}
          {info?.status === 'INVITING' && (
            <div className="btnGourp" style={{ zIndex: '10' }}>
              {StatusTxt}
              {roundListStatus !== 'CANCEL' && (
                <div className="btnGourp bckTransparent">
                  {INVI_Element}
                  {INVIbtnKeys?.length > 3 ||
                  (INVIbtnKeys?.length === 3 &&
                    ['f_add_pay_credential', 'f_choice_list_special_approval_goods'].includes(
                      INVIbtnKeys[2],
                    )) ? (
                    <Tooltip
                      placement="rightTop"
                      overlayClassName={styles['btnMap']}
                      title={
                        //  width: '104px'
                        <ul style={{ cursor: 'pointer' }}>
                          {INVIbtnKeys.slice(2).map((item, index) => {
                            return (
                              <li key={index}>
                                <AuthWrapper functionName={item}>{INVI_Map[item].tDom}</AuthWrapper>
                              </li>
                            );
                          })}
                        </ul>
                      }
                    >
                      <Button
                        className="btnStyle"
                        style={{ background: '#7B7B7B', color: 'white', border: 'none' }}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Icon type="ellipsis" />
                      </Button>
                    </Tooltip>
                  ) : (
                    <></>
                  )}
                </div>
              )}
            </div>
          )}
          {/* 已取消 */}
          {info?.status === 'CANCEL' && (
            <div className="btnGourp">
              {StatusTxt}
              <div className="btnGourp bckTransparent">
                {info?.allowQualityScoreAudit && (
                  <AuthWrapper functionName="f_choice_list_special_approval_goods">
                    <Button
                      style={{ background: '#0D6DEA', color: 'white', fontWeight: 500 }}
                      className="btnStyle"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        openSpecialApprovalGoods();
                        // confirmHandle('prepayment');
                      }}
                      type="primary"
                    >
                      商品分特批
                    </Button>
                  </AuthWrapper>
                )}
                <AuthWrapper functionName="f_choice_list_look_legal">
                  <Button
                    className="btnStyle"
                    style={{
                      background: '#7B7B7B',
                      color: 'white',
                      border: 'none',
                      fontWeight: 500,
                    }}
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setLegalVisible(true);
                      // handleGoods(info, 'edit');
                    }}
                  >
                    查看资质
                  </Button>
                </AuthWrapper>
              </div>
            </div>
          )}
          {/* 已失效 */}
          {info?.status === 'LOSE_EFFICACY' && (
            <div className="btnGourp">
              {StatusTxt}
              <div className="btnGourp bckTransparent">
                {info?.allowQualityScoreAudit && (
                  <AuthWrapper functionName="f_choice_list_special_approval_goods">
                    <Button
                      style={{ background: '#0D6DEA', color: 'white', fontWeight: 500 }}
                      className="btnStyle"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        openSpecialApprovalGoods();
                        // confirmHandle('prepayment');
                      }}
                      type="primary"
                    >
                      商品分特批
                    </Button>
                  </AuthWrapper>
                )}
                <AuthWrapper functionName="f_choice_list_look_legal">
                  <Button
                    className="btnStyle"
                    style={{
                      background: '#7B7B7B',
                      color: 'white',
                      border: 'none',
                      fontWeight: 500,
                    }}
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setLegalVisible(true);
                      // handleGoods(info, 'edit');
                    }}
                  >
                    查看资质
                  </Button>
                </AuthWrapper>
              </div>
            </div>
          )}
        </div>
        <div
          className="bottomCard"
          onClick={() => {
            handleGoods(info, 'info');
          }}
        >
          <div className="priceRole">
            <span className="price">
              {`${
                info?.minPrice === info?.maxPrice
                  ? '¥' + info?.minPrice
                  : '¥' + info?.minPrice + '-' + info?.maxPrice
              }`}
            </span>
            <span style={{ float: 'right' }}>
              <AuthWrapper functionName="f_choice_list_require_link">
                {!info?.promotionLink &&
                  info?.totalCommission !== 0 &&
                  info?.platformSource === 'TB' &&
                  (info?.status === 'WAIT_AUDIT' || info?.status === 'WAIT_LIVE') && (
                    <Popover content={<p style={{ padding: '3px' }}>请求链接</p>}>
                      <a
                        className="copyBtn"
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsLoading(true);
                          getOneLink({ id: parseInt(info?.id) }).then((res) => {
                            setIsLoading(false);
                            if (res?.res?.result?.autoGetLinks === false) {
                              message.warn(res.res.result.msg);
                            } else {
                              if (res?.res?.code === '200') {
                                message.success('请求成功');
                                onGetList();
                              } else {
                                message.error(res?.res?.message);
                              }
                            }
                          });
                        }}
                        target="_blank"
                      >
                        <img
                          style={{ marginRight: '6px' }}
                          width={12}
                          height={12}
                          src={request}
                          alt=""
                        />
                      </a>
                    </Popover>
                  )}
              </AuthWrapper>
              <AuthWrapper functionName="f_choice_list_add_link">
                {['BP_CONFIRMING', 'CANCEL'].indexOf(info?.status) === -1 && (
                  // <Popover content={<p style={{ padding: '3px' }}>补充链接</p>}>
                  //   <a>
                  //     <img
                  //       onClick={openSuppLinkModal}
                  //       style={{ marginRight: '6px' }}
                  //       width={16}
                  //       height={16}
                  //       src={suppLink}
                  //       alt=""
                  //     />
                  //   </a>
                  // </Popover>
                  <BatchSupplementLink
                    buttonType="icon"
                    onRefresh={onGetList}
                    tabsValue="live"
                    info={info}
                    liveRoomId={liveRoomId}
                  />
                )}
              </AuthWrapper>
              {info?.promotionLink && (
                <Popover content={<p style={{ padding: '3px' }}>上播链接</p>}>
                  <a
                    className="copyBtn"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    target="_blank"
                    href={info?.promotionLink}
                  >
                    <img
                      style={{ marginRight: '4px' }}
                      width={16}
                      height={16}
                      src={linkImg}
                      alt=""
                    />
                  </a>
                </Popover>
              )}
            </span>
          </div>
          {/* 商品标题 */}
          <div className="product">
            <div className="product-name">
              {info?.brandName ? '【' + info?.brandName + '】' : ''}
              {info?.spuName}
            </div>
          </div>
          {/* 标签展示 */}

          <TagGroup info={info} />

          <div className="priceGroup">
            <div style={{ display: 'flex', marginBottom: '4px', textAlign: 'justify' }}>
              <span>
                总佣金(含保量)
                <span style={{ color: '#E02020' }}>
                  {info?.hideCommissionFlag ? (
                    ' *** %'
                  ) : (
                    <>{Math.round(info?.totalCommissionContainGuaranteed * 10000) / 100}%</>
                  )}
                </span>
                {/* <span>
                  (线上
                  {info?.hideCommissionFlag ? (
                    ' *** %'
                  ) : (
                    <>{Math.round(info?.commissionRate * 10000) / 100}%</>
                  )}
                  )
                </span> */}
              </span>
              {/* {info?.lowCommissionAuditStatus !== 'NONE' && (
                <div className="tagGourp" style={{ width: '50px', marginTop: 0, marginBottom: 0 }}>
                  <Tag color="#EDF9FF">
                    <span style={{ color: '#477FFF' }}>低佣</span>
                  </Tag>
                </div>
              )} */}

              {/* <span style={{ float: 'right' }}>
                <Popover content={`￥${info?.sectionFee || 0}`}>
                  切片费 ¥{(info?.sectionFee / 10000).toFixed(2) || 0}w
                </Popover>
              </span> */}
            </div>
            <p>
              线上佣金{' '}
              <span style={{ color: '#E02020' }}>
                {info?.hideCommissionFlag ? (
                  ' *** %'
                ) : (
                  <>{Math.round(info?.commissionRate * 10000) / 100}%</>
                )}
              </span>
            </p>
            <p>
              线下佣金{' '}
              <span style={{ color: '#E02020' }}>
                {info?.hideCommissionFlag ? (
                  ' *** %'
                ) : (
                  <>{Math.round(info?.commissionRateOffline * 10000) / 100}%</>
                )}
              </span>
            </p>
            <p>
              保量基础佣金{' '}
              {info?.hideCommissionFlag ? (
                ' *** %'
              ) : (
                <>
                  <span
                    style={{
                      color: isNullOrUndefined(info?.cooperationGuaranteed?.guaranteeBrandFeeRate)
                        ? 'unset'
                        : '#E02020',
                    }}
                  >
                    {isNullOrUndefined(info?.cooperationGuaranteed?.guaranteeBrandFeeRate)
                      ? '-'
                      : `${(info?.cooperationGuaranteed?.guaranteeBrandFeeRate * 100).toFixed(2)}%`}
                  </span>{' '}
                  <span>
                    {info?.cooperationGuaranteed?.payDurationType &&
                      `(${
                        PAY_DAY_ABBREVIATE[info?.cooperationGuaranteed?.payDurationType as PAY_DAY]
                      })`}
                  </span>
                </>
              )}
            </p>
            <div style={{ textAlign: 'justify' }}>
              <span>
                基础服务费{' '}
                <span style={{ color: '#E02020' }}>
                  {info?.hideCommissionFlag ? ' *** ' : <>¥{info?.brandFee || 0}</>}
                </span>
              </span>

              {info?.frameworkCoopModel?.length && info?.frameworkCoopModel.length > 0 ? (
                <Popover
                  placement="right"
                  content={
                    <>
                      <span style={{ width: '60%' }}>
                        {info?.frameworkCoopModel.indexOf('GUARANTEED_SLICE_MODE') > -1 ||
                        info?.frameworkCoopModel.indexOf('GUARANTEED_GMV_MODE') > -1 ? (
                          <p>保GMV-{Math.round(info?.gmvCommissionRate * 10000) / 100}%</p>
                        ) : (
                          ''
                        )}
                        {info?.frameworkCoopModel.indexOf('GUARANTEED_LIVE_ROUND_MODE') > -1 ||
                        info?.frameworkCoopModel.indexOf('GUARANTEED_SLICE_MODE') > -1 ? (
                          <p>保场次-¥{info?.roundTotalFees}</p>
                        ) : (
                          ''
                        )}
                      </span>
                    </>
                  }
                >
                  <span
                    style={{ float: 'right', paddingRight: '6px', textDecoration: 'underline' }}
                  >
                    年框
                  </span>
                </Popover>
              ) : (
                ''
              )}
            </div>

            <p>
              历史累计支付/T15 ¥
              {info?.historySumSales ? formatFee(info?.historySumSales / 10000) + 'w' : 0}
              /¥
              {info?.historySumSalesForFifteenDays
                ? formatFee(info?.historySumSalesForFifteenDays / 10000) + 'w'
                : 0}
            </p>
            <p>
              场均支付/T15 ¥{info?.avgSales ? formatFee(info?.avgSales / 10000) + 'w' : 0}
              /¥
              {info?.avgSalesForFifteenDays
                ? formatFee(info?.avgSalesForFifteenDays / 10000) + 'w'
                : 0}
            </p>
            {info?.isDisplayQualityScore && (
              <p>
                商品质量分{' '}
                <span style={{ color: numberToColor(info?.goodsQualityScore) }}>
                  {info?.goodsQualityScore}
                </span>
              </p>
            )}

            {info?.platformSource === 'DY' && (
              <div style={{ marginBottom: '4px', textAlign: 'justify' }}>
                <span
                  style={
                    numberContrast({ num1: info?.standardFavorableRate, num2: info?.favorableRate })
                      ? { color: '#EE0000' }
                      : {}
                  }
                >
                  好评率{' '}
                  {info?.favorableRate ? Math.round(info?.favorableRate * 10000) / 100 + '%' : 0}
                </span>
                <span
                  style={{
                    float: 'right',
                    ...(numberContrast({ num1: info?.standardStore, num2: info?.shopPoints })
                      ? { color: '#EE0000' }
                      : {}),
                  }}
                >
                  店铺分 {info?.shopPoints || 0}
                </span>
              </div>
            )}
            <div style={{ margin: '4px 0 0 0' }}>
              <div style={{ position: 'relative', height: '20px' }}>
                <Popover content={info?.supplierOrgName} title="" trigger="hover">
                  <span
                    className="one-line-ellipsis"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      window.open(`/provider-detail/${info?.supplierId}`);
                    }}
                  >
                    {info?.supplierOrgName}
                  </span>
                </Popover>

                {info?.serviceAgreementId !== '-1' && (
                  <span style={{ position: 'absolute', right: '0', bottom: 0, height: '20px' }}>
                    <Popover
                      content={
                        info?.serviceAgreementId
                          ? '已签署'
                          : '【未签署】商家未签署协议时不会触发法务审核流程，请商务尽快联系商家在合同管理中签署协议'
                      }
                    >
                      {info?.serviceAgreementId ? (
                        // <span
                        //   className="iconfont icon-yiqianshu1"
                        //   style={{
                        //     fontSize: '16px',
                        //     bottom: 4,
                        //     position: 'relative',
                        //     color: '#f00'
                        //   }}
                        // ></span>
                        <img
                          src={YBS}
                          style={{
                            width: '16px',
                            height: '16px',
                            marginRight: '4px',
                          }}
                        />
                      ) : (
                        <img
                          src={JingGao}
                          style={{
                            width: '16px',
                            height: '16px',
                            marginRight: '4px',
                          }}
                        />
                      )}
                    </Popover>
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
        <GoodsCardConfirmModal
          info={info}
          childrenMsg={childrenMsg}
          onCancel={handleCancel}
          onGetList={onGetList}
          roleInfo={roleInfo}
          visible={visible}
          setVisible={setVisible}
          goodsInfo={goodsInfo}
          guaranteedDetailInfo={guaranteedDetailInfo}
        />
        {/* <Modal
          width={424}
          visible={visible}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onOk={handleOk}
          onCancel={handleCancel}
        >
          <div
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            style={{ display: 'flex' }}
          >
            <Icon
              type="exclamation-circle"
              className="iconStyle"
              style={{ color: roleInfo?.color, marginRight: 16 }}
            />
            <div>
              <h3> {roleInfo?.title}</h3> <br />
              {roleInfo?.type === 'businessConfirm' ? '' : roleInfo?.info}
              {roleInfo?.type === 'del' && (
                <div style={{ marginTop: '24px' }}>
                  掉品原因：
                  <Select
                    value={reason}
                    style={{ width: 200 }}
                    dropdownMatchSelectWidth={false}
                    onChange={(e) => {
                      setReason(e);
                    }}
                  >
                    {ReasonList.map((i, index) => {
                      return (
                        <Option value={i.value} key={index}>
                          {i.value}
                        </Option>
                      );
                    })}
                  </Select>
                </div>
              )}
              {childrenMsg !== null &&
                (roleInfo?.type === 'businessConfirm' ||
                  roleInfo?.type === 'businessConfirmAuth') && (
                  <Form.Item
                    required={childrenMsg?.required === 1 ? true : false}
                    label="直播服务类型"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 12 }}
                    className={isError ? 'has-error' : ''}
                  >
                    <Select
                      allowClear
                      style={{ width: 200 }}
                      onChange={(val) => {
                        setValue(val);
                        if (childrenMsg?.required) {
                          setError(!val);
                        }
                      }}
                      value={liveServiceType}
                      placeholder="请选择"
                    >
                      {childrenMsg?.serviceTypeOptions?.map((item) => {
                        return <Select.Option value={item.id}>{item.name}</Select.Option>;
                      })}
                    </Select>
                    <section
                      style={{
                        display: isError ? 'block' : 'none',
                        marginTop: '-10px',
                        color: '#FF4D4F',
                      }}
                    >
                      请选择直播服务类型
                    </section>
                  </Form.Item>
                )}
            </div>
          </div>
        </Modal> */}

        <ChooseModal
          liveRoundId={liveRoundId}
          childrenMsg={childrenMsg}
          info={goodsInfo}
          onRef={chooseModalRef}
          search={onGetList}
          codeList={codeList}
        ></ChooseModal>
        <GoodsInfoEdit
          liveRoundId={liveRoundId}
          info={goodsInfo}
          onRef={goodsInfoRef}
          search={onGetList}
          confirmHandle={confirmHandle}
          entry="changci"
          childrenMsg={childrenMsg}
        ></GoodsInfoEdit>
        <AddPayVoucherModal
          info={info}
          onGetList={onGetList}
          onCancel={closeAddVisible}
          visible={addVisible}
          onOk={closeAddVisible}
        ></AddPayVoucherModal>
        <ConfirmPayVoucher
          info={info}
          onGetList={onGetList}
          onCancel={closeConfirrmPayVoucherVisible}
          visible={confirrmPayVoucherVisible}
          onOk={closeConfirrmPayVoucherVisible}
        ></ConfirmPayVoucher>
        <MarkVoucher
          info={info}
          onGetList={onGetList}
          onCancel={closeMarkVoucherVisible}
          visible={markVoucherVisible}
          onOk={closeMarkVoucherVisible}
        ></MarkVoucher>
        <ApproveModal
          info={info}
          onGetList={onGetList}
          onCancel={closeApproveVisible}
          visible={ApproveVisible}
          onOk={closeApproveVisible}
        />
        <SuppLinkModal info={info} onGetList={onGetList} onRef={suppLinkRef} />
        <LegalCheckDrawer
          info={info}
          onGetList={onGetList}
          visible={legalVisible}
          handleClose={() => {
            setLegalVisible(false);
          }}
        ></LegalCheckDrawer>
        <HighRisk onSearch={onGetList} info={info} onRef={highRiskRef} />
        <SubjectSpecilApprovalModal onRef={SubJectModalRef} onSearch={onGetList} />
        <SpecialApprovalGoods
          onRefresh={onGetList}
          visible={specialApprovalGoodsVisible}
          handleClose={() => {
            setSpecialApprovalGoodsVisible(false);
          }}
          info={info}
        />
      </Spin>
    </Card>
  );
};

export default GoodsCard;
