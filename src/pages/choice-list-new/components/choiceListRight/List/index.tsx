import React, { useEffect, useMemo, useRef, useState } from 'react';
import { List, Spin } from 'antd';
import GoodsCard from './goodsCard';
import './index.less';
import { debounce } from 'lodash';
import { useSelector } from 'react-redux';
import { NewLiveGoodsListResult } from '@/services/yml/choiceList';
import { history } from 'qmkit';
import { useCode } from '@/common/constants/hooks/index';
interface PropsType {
  list?: NewLiveGoodsListResult['records'];
  search?: any;
  formData?: any;
  getLiveCountList?: any;
  isExpand?: any;
  tableHeight?: number;
  isLoading?: any;
  roundListStatus?: any;
  liveRoundId?: any;
}
const GoodsList = (props: PropsType) => {
  const {
    list,
    formData,
    search,
    getLiveCountList,
    isExpand,
    tableHeight,
    isLoading,
    roundListStatus,
    liveRoundId,
  } = props;
  const [cardWidth, setCardWidth] = useState(206); // 初始卡片宽度为 206
  const choiceListRightCardRef = useRef<HTMLDivElement>(null);
  const collapsedData = useSelector((state: any) => state.collapsedData);
  const breadcrumb = useSelector((state: any) => state.breadcrumb);
  const pageOnResize = () => {
    //1. 找到div的宽度
    //2. 算以最小的宽度可以放几个。
    if (choiceListRightCardRef.current) {
      const width = choiceListRightCardRef.current.getBoundingClientRect().width - 14;
      if (width < 200) return;
      // console.log('width', width, width % 214, width / 214, Math.floor(width / 214));
      const cardNumber = Math.floor(width / 214);

      const nowWidth = width / cardNumber;
      // console.log('cardNumber', cardNumber, nowWidth);
      setCardWidth(nowWidth - 8);
    }
  };
  const handleResize = debounce(pageOnResize, 300);
  useEffect(() => {
    handleResize(); // 初始化时计算卡片数量
    window.addEventListener('resize', handleResize); // 监听窗口大小变化
    return () => {
      window.removeEventListener('resize', handleResize); // 清除 resize 事件监听
    };
  }, []);
  useEffect(() => {
    handleResize();
  }, [isExpand, collapsedData]);
  useEffect(() => {
    pageOnResize();
    setTimeout(() => {
      pageOnResize();
    }, 0);
  }, [breadcrumb]);
  const { codeList, codeEnum } = useCode('SelectionLabelEnum');
  return (
    <div
      className={'choiceList-right-card'}
      id="choiceList-right-card"
      ref={choiceListRightCardRef}
      style={{
        minWidth: isExpand ? '1226px' : '',
        height: `${tableHeight}px`,
        // paddingRight: '8px',
        padding: '4px',
      }}
    >
      {/* <Spin spinning={isLoading}>
        <List
          grid={{
            gutter: 8,
            xs: 3,
            sm: 3,
            md: 3,
            lg: 3,
            xl: isExpand ? 6 : 4,
            xxl: isExpand ? 8 : 6,
          }}
          dataSource={list}
          renderItem={(item) => (
            <List.Item>
              <GoodsCard
                roundListStatus={roundListStatus}
                info={item}
                search={search}
                getLiveCountList={getLiveCountList}
                liveRoundId={liveRoundId}
                formData={formData}
              ></GoodsCard>
            </List.Item>
          )}
        />
      </Spin> */}
      <Spin spinning={isLoading}>
        <div
          style={{
            flexWrap: 'wrap',
            width: 'auto',
            display: 'flex',
            justifyContent: 'flex-start',
          }}
        >
          {list?.map((item) => {
            return (
              <GoodsCard
                roundListStatus={roundListStatus}
                info={item}
                search={search}
                getLiveCountList={getLiveCountList}
                formData={formData}
                cardWidth={cardWidth}
                liveRoundId={liveRoundId}
                codeList={codeList}
              ></GoodsCard>
            );
          })}
        </div>
      </Spin>
    </div>
  );
};

export default GoodsList;
