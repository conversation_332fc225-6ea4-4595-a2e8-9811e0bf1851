import React, { useEffect, useState, forwardRef, useRef } from 'react';
import {
  Modal,
  Form,
  Input,
  Drawer,
  Button,
  message,
  Select,
  Row,
  Col,
  Radio,
  InputNumber,
} from 'antd';
import './goodsCard.less';
import { ActionType } from './type';
import ChooseLive from 'web-common-modules/components/GoodsInfoEdit/component/chooseLive';
import { cascadeList, liveGoodsInfo, chEdit } from '@/services/yml/choiceList/index';
import { getServiceType } from '@/services/yml/live-service-type-configuration/index';
import OSSUpload from '@/components/OSSUpload';
import FileUploadSouceId from '@/components/FileUploadSouceId';
import { CODE_ENUM, useCode } from '@/common/constants/hooks/index';
import SingleSelect from '@/components/SingleSelect';
import useEmployee from '@/pages/goods-assorting/hooks/useEmployee';
import { debounce } from 'lodash';
import styles from './chooseModal.less';
import LabelOptionFormItem, {
  getLabelOptionList,
} from '@/components/GoodsInfoEdit/component/LabelOptionFormItems';
const { TextArea } = Input;
const { Option } = Select;
export type ChooseRefProps = {
  onOpen: () => void;
  onClose: () => void;
};
const ChooseModal = React.forwardRef<ChooseRefProps>((props) => {
  const { type, info, form, onRef, search, liveRoundId, codeList } = props;
  const { getFieldDecorator, setFieldsValue, getFieldsValue } = form;
  const [visible, setVisible] = useState(false);
  const [goodsInfo, setGoodsInfo] = useState();
  const [cascadeArr, setCascadeArr] = useState([]);
  const [childrenMsg, setChildMsg] = useState(null);
  const [liveServiceTypeName, setLiveServiceTypeName] = useState<string>('');
  const { codeList: selectList, getEnumList } = useCode(CODE_ENUM.SELECTION_LABEL, {
    wait: true,
    able: true,
  });
  const { list: employeeList, onSearch, loading } = useEmployee(); // 反需人员

  //主播
  const getInfo = () => {
    liveGoodsInfo({ id: info.id }).then((res) => {
      if (res?.res?.code === '200') {
        const value = res.res.result;
        setGoodsInfo(value);
        getCascadeList(value);
      }
    });
  };
  //直播类型

  //   标签;
  const getCascadeList = (value) => {
    cascadeList({
      deptId: value.deptId,
      labelStatus: 'ENABLE',
      talentIdList: [value.talentId],
    }).then((res) => {
      if (res?.res?.code === '200') {
        const list = res.res.result.cascadeList;
        setCascadeArr(list);
      }
    });
  };
  const [labelArr, setlabelArr] = useState([]);
  const labelObj = useRef({});
  const onOpen = () => {
    getServiceType({ liveRoundId: liveRoundId }).then((res) => {
      if (res?.res?.code === '200') {
        setChildMsg(res?.res?.result);
      }
    });
    setVisible(true);
    getInfo(info);
    setLiveServiceTypeName(info?.liveServiceType);
    labelObj.current = {};
    const arr: Array<any> = [];
    info.labelList?.forEach((i) => {
      labelObj.current[i.id] = [];
      i.labelOptionList?.forEach((t) => {
        arr.push(t.labelOption);
        labelObj.current[i.id].push(t.labelOption);
      });
    });
    setlabelArr(arr);
  };

  const onClose = () => {
    setVisible(false);
  };
  const radioChange = (e: any) => {
    // 是否反需改变时清空反需人员
    setFieldsValue({ reverseDemandId: '' });
  };
  const handleSearch = debounce(async (v: string) => {
    onSearch({ bizRoleType: 'SELECTION', deptId: info?.deptId, employeeName: v }); //
  }, 500);
  const save = () => {
    form.validateFields((err, values) => {
      if (values && values.liveServiceTypeId === info?.liveServiceType) {
        values.liveServiceTypeId = info?.liveServiceTypeId;
      }
      const labelOptionContent = [];
      if (cascadeArr?.length) {
        cascadeArr?.forEach((item) => {
          if (values[item.id]) {
            const obj = {
              chooseMethod: item?.chooseMethod,
              id: item?.id,
              labelGroupName: item?.labelGroupName,
              requiredFlag: item?.requiredFlag,
              labelOptionList: [],
            };
            item?.labelOptionList.forEach((i) => {
              if (Array.isArray(values[item?.id])) {
                if (values[item?.id].indexOf(i.labelOption) > -1) {
                  obj.labelOptionList.push({
                    id: i.optionId,
                    labelOption: i.labelOption,
                  });
                }
              } else {
                if (values[item?.id] === i.labelOption) {
                  obj.labelOptionList.push({
                    id: i.optionId,
                    labelOption: i.labelOption,
                  });
                }
              }
            });
            labelOptionContent.push(obj);
          }
        });
      }
      if (!err) {
        const labelConfigInfo = JSON.parse(JSON.stringify(getLabelOptionList(values)));
        Object.keys(values).forEach((key: string) => {
          if (key.includes('labelOpTion_label_value')) {
            delete values[key];
          }
        });
        chEdit({
          id: info?.id,
          ...values,
          spuFocusResourceList: values?.spuFocusResourceList?.map((item) => item.resourceId),
          labelOptionContent,
          version: goodsInfo?.version,
          selectionLabel: values.selectionLabel,
          labelConfigInfo,
        }).then((res) => {
          if (res?.res.code === '200') {
            message.success('操作成功');
            search();
          } else {
            message.warn(res?.res?.message);
          }
          setVisible(false);
        });
      }
    });
  };
  React.useImperativeHandle(onRef, () => ({
    onOpen,
    onClose,
  }));
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 4 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };
  useEffect(() => {
    visible && getEnumList && getEnumList();
    visible && onSearch({ bizRoleType: 'SELECTION', deptId: info?.deptId }); // 获取事业部下的所有选品人员
    if (!visible) {
      setLiveServiceTypeName('');
    }
  }, [visible]);

  const handleValidator = (_: unknown, value: number, callback: any) => {
    if (!value && value != 0) {
      return callback();
    }
    if (Number.isNaN(Number(value))) {
      return callback(`请输入数字 支持2位小数`);
    }
    if (Number(value) < 0) {
      return callback('不能输入负数');
    }
    if (Number(value) > 9999999999.99) {
      return callback('最大值9999999999.99');
    }

    return callback();
  };
  return (
    <Drawer
      title={
        <div style={{ display: 'flex' }}>
          <div>选品编号</div>
          <div
            style={{
              position: 'absolute',
              right: 0,
              bottom: 0,
              padding: '10px 16px',
            }}
          >
            <Button
              style={{ marginRight: '10px' }}
              type="primary"
              onClick={() => {
                save();
              }}
            >
              保存
            </Button>
            <Button
              onClick={() => {
                setVisible(false);
              }}
            >
              取消
            </Button>
          </div>
        </div>
      }
      placement="right"
      closable={false}
      width={800}
      onClose={onClose}
      visible={visible}
    >
      {visible && (
        <div>
          <Form {...formItemLayout}>
            {childrenMsg !== null && (
              <Row>
                <Form.Item label="直播服务类型" style={{ marginBottom: '0px' }}>
                  {getFieldDecorator('liveServiceTypeId', {
                    initialValue: childrenMsg?.serviceTypeOptions.find(
                      (item) => item.id === info?.liveServiceTypeId,
                    )
                      ? info?.liveServiceTypeId
                      : info?.liveServiceType,
                    rules: [{ required: !!childrenMsg?.required, message: '直播服务类型' }],
                  })(
                    <Select
                      style={{ width: 200 }}
                      onChange={(value, option: any) => {
                        console.log('🚀 ~ e:', value, option);
                        const { props } = option || {};
                        setLiveServiceTypeName(props?.children || '');
                      }}
                    >
                      {childrenMsg?.serviceTypeOptions?.map((item) => {
                        return <Option value={item.id}>{item.name}</Option>;
                      })}
                    </Select>,
                  )}
                </Form.Item>
              </Row>
            )}
            {['预售讲解', '预售挂链', '预热挂链', '预热讲解'].includes(liveServiceTypeName) ? (
              <Form.Item required label="定金金额" style={{ marginBottom: '0px' }}>
                {form?.getFieldDecorator('depositAmount', {
                  initialValue: info?.depositAmount,
                  rules: [
                    { required: true, message: '请填写定金金额' },
                    { validator: handleValidator },
                  ],
                })(
                  <InputNumber
                    max={9999999999.99}
                    min={0.0}
                    precision={2}
                    style={{ width: 200 }}
                  />,
                )}
              </Form.Item>
            ) : null}
            <LabelOptionFormItem
              form={form}
              deptId={info?.deptId}
              liveRoomId={info?.liveRoundInfo?.liveRoomId}
              type="edit"
              info={info}
            />
            <div>
              {cascadeArr?.map((i) => {
                return (
                  <Form.Item label={i.labelGroupName} key={i.id} style={{ marginBottom: 0 }}>
                    {getFieldDecorator(String(i.id), {
                      initialValue:
                        i.chooseMethod === 'RADIO'
                          ? labelObj.current?.[i.id]?.[0]
                          : labelObj.current?.[i.id],
                      rules: [
                        { required: i.requiredFlag === 1 ? true : false, message: '请选择标签' },
                      ],
                    })(
                      <Select
                        maxTagCount={1}
                        mode={i.chooseMethod === 'RADIO' ? '' : 'multiple'}
                        style={{ width: 200 }}
                      >
                        {i?.labelOptionList.map((t) => {
                          return (
                            <Option key={t.labelOption} value={t.labelOption}>
                              {t.labelOption}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                );
              })}
            </div>
            <Row>
              <Form.Item label="选品标签" style={{ marginBottom: '0px' }}>
                {getFieldDecorator('selectionLabel', {
                  initialValue: info?.selectionLabel ? info?.selectionLabel : '',
                  // rules: [{ required: true, message: '商品主要卖点' }],
                })(<SingleSelect options={selectList}></SingleSelect>)}
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="商品主要卖点" style={{ marginBottom: '0px' }}>
                {getFieldDecorator('sellingPoints', {
                  initialValue: info?.sellingPoints,
                  // rules: [{ required: true, message: '商品主要卖点' }],
                })(
                  <TextArea
                    style={{ width: '523px' }}
                    maxLength={2000}
                    placeholder="商品主要卖点"
                    allowClear
                    autoSize={{ minRows: 3 }}
                  />,
                )}
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="重点展示需求" style={{ marginBottom: '0px' }}>
                {getFieldDecorator('spuFocus', {
                  initialValue: info?.spuFocus,
                  rules: [{ required: false, message: '重点展示需求' }],
                })(
                  <TextArea
                    style={{ width: '523px' }}
                    maxLength={500}
                    placeholder="重点展示需求"
                    allowClear
                    autoSize={{ minRows: 3 }}
                  />,
                )}
              </Form.Item>
            </Row>
            <Row>
              <Form.Item label="重点展示需求（附件）" style={{ marginBottom: '0px' }}>
                {getFieldDecorator('spuFocusResourceList', {
                  initialValue: info?.spuFocusResources,
                })(
                  <FileUploadSouceId
                    multiple
                    isImage={false}
                    maxSize={10 * 1024 * 1024}
                    maxLen={10}
                    typeCode={'UPLOAD_SECTION_CONFIRMATION_RESOURCE'}
                    accept={'.pdf,.jpg,.jpeg,.png,.ppt,.zip,.doc'}
                    // onChange={(list: any) => {
                    //   setFileList(list);
                    // }}
                  ></FileUploadSouceId>,
                )}
              </Form.Item>
            </Row>
            <div>
              <span className="mt-12 text-color-999" style={{ marginLeft: '120px' }}>
                支持：pdf、png、jpg、jpeg、ppt、zip、doc不超过10MB
              </span>
            </div>
            <Row>
              <Form.Item label="选品备注" style={{ marginBottom: '0px' }}>
                {getFieldDecorator('selectionRemark', {
                  initialValue: info?.selectionRemark,
                  // rules: [{ required: false, message: '重点展示需求' }],
                })(<Input maxLength={20} placeholder="选品备注" style={{ width: 200 }} />)}
              </Form.Item>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item
                  label="是否为反需"
                  labelCol={{ span: 8 }}
                  style={{ marginBottom: '0px' }}
                >
                  {getFieldDecorator('reverseDemandTag', {
                    initialValue: info?.reverseDemandTag,
                    // rules: [{ required: false, message: '重点展示需求' }],
                  })(
                    <Radio.Group onChange={radioChange}>
                      <Radio value={true}>是</Radio>
                      <Radio value={false}>否</Radio>
                    </Radio.Group>,
                  )}
                </Form.Item>
              </Col>
              {getFieldsValue()?.reverseDemandTag ? (
                <Col span={12}>
                  <Form.Item label="反需人员" style={{ marginBottom: '0px' }}>
                    {getFieldDecorator('reverseDemandId', {
                      initialValue: info?.reverseDemandId,
                      rules: [{ required: true, message: '请选择反需人员' }],
                    })(
                      <Select
                        style={{ width: 200 }}
                        allowClear
                        showSearch
                        optionFilterProp="children"
                        loading={loading}
                        onSearch={handleSearch}
                      >
                        {employeeList?.map((item) => {
                          return (
                            <Option key={item.employeeId} value={item.employeeId}>
                              {item.employeeName}
                            </Option>
                          );
                        })}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              ) : (
                <></>
              )}
            </Row>
            <Row>
              <Form.Item label="收样登记" style={{ marginBottom: '0px' }}>
                {getFieldDecorator('receiveSampleRegister', {
                  initialValue: info?.receiveSampleRegister,
                })(<Input maxLength={199} placeholder="收样登记" style={{ width: 200 }} />)}
              </Form.Item>
            </Row>
          </Form>
        </div>
      )}
    </Drawer>
  );
});
export default Form.create({ name: 'chooseModal' })(ChooseModal);
