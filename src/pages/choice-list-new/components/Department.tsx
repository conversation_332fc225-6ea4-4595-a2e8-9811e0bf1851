import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { getAllDeptList, GetAllDeptListResult } from '@/services/yml/live-room-manage';
import { useRequest } from 'ahooks';
import { handleResponse } from '@/utils/response';

type IPROPS = {
  onSearch?: any;
  onSelect?: any;
  onChange?: any;
  initValue?: any;
  defaultOptions?: any[];
  filterValues?: any[];
  list?: any[] | undefined;
  labelInValue?: boolean;
  handelProjectTeam?: any;
  disabled: boolean;
  setTb?: () => void;
};

const Department: React.FC<IPROPS> = (
  { list = undefined, handelProjectTeam, setTb, ...rest },
  ref,
) => {
  const [departmentList, setDepartmentId] = useState<GetAllDeptListResult>([]);
  const [defaultValue, setDefaultValue] = useState();
  // 获取所有事业部
  const { run: departmentRun, loading: departmentLoading } = useRequest(getAllDeptList, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        const { result } = res;
        const tb = result.filter((i) => i.deptName === '杭州综合事业部')[0];
        if (tb) {
          handelProjectTeam && handelProjectTeam(tb?.id);
          setTb({ key: tb?.id, label: tb?.deptName });
        }

        setDepartmentId(result);
      });
    },
  });
  useEffect(() => {
    departmentRun({});
  }, []);
  const handelChange = (e: any) => {
    console.log(e);
    handelProjectTeam && handelProjectTeam(e);
    rest.onChange && rest.onChange(e);
  };
  return (
    <Select
      labelInValue={true}
      placeholder="请选择所属事业部"
      loading={departmentLoading}
      {...rest}
      onChange={handelChange}
      style={{ width: 200 }}
    >
      {departmentList.map((item) => (
        <Select.Option key={item.id} value={item.id}>
          {item.deptName}
        </Select.Option>
      ))}
    </Select>
  );
};

export default Department;
