import React, { useContext, useEffect, useRef, useState } from 'react';
import { AuthWrapper, Const } from 'qmkit';
import PageLayout from '@/components/PageLayout';
import Styles from './index.module.less';
import ChoiceListLeft from './components/choiceLIstLeft';
import ChoiceListRight from './components/choiceListRight';
import shouqi from '@/assets/shouqi.png';
import { assertSchema } from 'graphql';
export const PageContext = React.createContext<{
  deptId?: string;
  liveRoomId?: string;
  selectPlatform?: string;
  setDeptId?: (id: string) => void;
  setLiveRoomId?: (id: string) => void;
  setPlatform?: (id: string) => void;
}>({});
const ChoiceList: React.FC = () => {
  const [liveRoundId, setLiveRoundId] = useState<string>();
  const [liveRoundInfo, setLiveRoundInfo] = useState<any>();
  const [pageDeptId, setPageDeptId] = useState<string>();
  const [pageLiveRoomId, setPageLiveRoomId] = useState<string>();
  const [selectPlatform, setPlatform] = useState<string>();
  const [isExpand, setIsexpand] = useState(false);
  const [ListValue, setListValue] = useState('');
  const [roundListStatus, setStatus] = useState('');
  // const [pageDeptId, setPageDeptId] = useState<string>();
  const getLiveRoundId = (value: string, status: string) => {
    // console.log('current', value);
    setLiveRoundId(value);
    setStatus(status);
  };
  const getLiveInfo = (value: any) => {
    setLiveRoundInfo(value);
  };
  const getListValue = (value: any) => {
    setListValue(value?.name);
  };
  return (
    <AuthWrapper functionName="f_choice_list" showType="page">
      <PageLayout routePath="/choice-list">
        <PageContext.Provider
          value={{
            deptId: pageDeptId,
            setDeptId: setPageDeptId,
            liveRoomId: pageLiveRoomId,
            setLiveRoomId: setPageLiveRoomId,
            selectPlatform,
            setPlatform,
          }}
        >
          <section style={{ position: 'relative' }}>
            <div className={Styles.choiceListWrap}>
              {!isExpand ? (
                <img
                  src={shouqi}
                  onClick={() => {
                    setIsexpand(true);
                  }}
                  // className="iconfont icon-shouqi"
                  style={{
                    zIndex: 99,
                    fontSize: '50px',
                    opacity: 0.6,
                    position: 'absolute',
                    top: '225px',
                    left: '300px',
                  }}
                ></img>
              ) : (
                <span
                  onClick={() => {
                    setIsexpand(false);
                  }}
                  className="iconfont icon-zhankai"
                  style={{
                    zIndex: 99,
                    fontSize: '50px',
                    opacity: 0.6,
                    position: 'absolute',
                    top: '225px',
                    left: '-10px',
                  }}
                ></span>
              )}
              <ChoiceListLeft
                getLiveRoundId={getLiveRoundId}
                liveRoundInfo={liveRoundInfo}
                isExpand={isExpand}
                getListValue={getListValue}
              />
              <ChoiceListRight
                getLiveInfo={getLiveInfo}
                liveRoundId={liveRoundId}
                roundListStatus={roundListStatus}
                isExpand={isExpand}
                liveRoundInfo={liveRoundInfo}
                ListValue={ListValue}
              />
            </div>
          </section>
        </PageContext.Provider>
      </PageLayout>
    </AuthWrapper>
  );
};
export default ChoiceList;
