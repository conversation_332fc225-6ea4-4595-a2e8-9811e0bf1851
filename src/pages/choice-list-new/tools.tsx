import React from 'react';
import { Icon } from 'antd';

export enum GOODS_GRADES_STATE_ENUM {
  PASS = 'PASS',
  CONFIRMING = 'CONFIRMING',
  REJECT = 'REJECT',
}

export const GOODS_GRADES_STATE_NAME = {
  [GOODS_GRADES_STATE_ENUM.PASS]: '已通过',
  [GOODS_GRADES_STATE_ENUM.CONFIRMING]: '审核中',
  [GOODS_GRADES_STATE_ENUM.REJECT]: '已驳回',
};
export const GOODS_GRADES_STATE_COLOR = {
  [GOODS_GRADES_STATE_ENUM.PASS]: '#87d068',
  [GOODS_GRADES_STATE_ENUM.CONFIRMING]: '#0C6AE4',
  [GOODS_GRADES_STATE_ENUM.REJECT]: '#E90000',
};

export const GOODS_GRADES_STATE = {
  //通过
  [GOODS_GRADES_STATE_ENUM.PASS]: {
    icon: (
      <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
    ),
    name: (
      <span style={{ color: '#87d068', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#87d068' }}></span>已通过
      </span>
    ),
  },
  [GOODS_GRADES_STATE_ENUM.CONFIRMING]: {
    icon: (
      <Icon style={{ color: '#108ee9', marginRight: '5px' }} theme="filled" type="clock-circle" />
    ),
    name: (
      <span style={{ color: '#0C6AE4', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#0C6AE4' }}></span>审核中
      </span>
    ),
  },
  [GOODS_GRADES_STATE_ENUM.REJECT]: {
    icon: <Icon style={{ color: '#f50', marginRight: '5px' }} theme="filled" type="close-circle" />,
    name: (
      <span style={{ color: '#E90000', marginRight: '8px' }}>
        <span className="dot" style={{ backgroundColor: '#E90000' }}></span>已驳回
      </span>
    ),
  },
};

export enum COMPLIANCESTATUS_ENUM {
  INIT = 'INIT',
  PASS = 'PASS',
  AUTO_PASS = 'AUTO_PASS',
  REJECT = 'REJECT',
  SKIP = 'SKIP',
}

export const COMPLIANCESTATUS_NAME = {
  [COMPLIANCESTATUS_ENUM.INIT]: '待审核',
  [COMPLIANCESTATUS_ENUM.PASS]: '通过',
  [COMPLIANCESTATUS_ENUM.AUTO_PASS]: '自动通过',
  [COMPLIANCESTATUS_ENUM.REJECT]: '驳回',
  [COMPLIANCESTATUS_ENUM.SKIP]: '跳过',
};

export const COMPLIANCESTATUS_COLOR = {
  [COMPLIANCESTATUS_ENUM.INIT]: '#0C6AE4',
  [COMPLIANCESTATUS_ENUM.PASS]: '#87d068',
  [COMPLIANCESTATUS_ENUM.AUTO_PASS]: '#87d068',
  [COMPLIANCESTATUS_ENUM.REJECT]: '#E90000',
  [COMPLIANCESTATUS_ENUM.SKIP]: '#999999',
};

export const COMPLIANCESTATUS_ICON = {
  [COMPLIANCESTATUS_ENUM.INIT]: (
    <Icon style={{ color: '#0C6AE4', marginRight: '5px' }} theme="filled" type="clock-circle" />
  ),
  [COMPLIANCESTATUS_ENUM.PASS]: (
    <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
  ),
  [COMPLIANCESTATUS_ENUM.AUTO_PASS]: (
    <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
  ),
  [COMPLIANCESTATUS_ENUM.REJECT]: (
    <Icon style={{ color: '#E90000', marginRight: '5px' }} theme="filled" type="close-circle" />
  ),
  [COMPLIANCESTATUS_ENUM.SKIP]: (
    <Icon style={{ color: '#999999', marginRight: '5px' }} theme="filled" type="close-circle" />
  ),
};

export const COMPLIANCESTATUS_COLOR_NAME = {
  [COMPLIANCESTATUS_ENUM.INIT]: (
    <span style={{ color: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.INIT], marginRight: '8px' }}>
      <span
        className="dot"
        style={{ backgroundColor: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.INIT] }}
      ></span>
      待审核
    </span>
  ),
  [COMPLIANCESTATUS_ENUM.PASS]: (
    <span style={{ color: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.PASS], marginRight: '8px' }}>
      <span
        className="dot"
        style={{ backgroundColor: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.PASS] }}
      ></span>
      已通过
    </span>
  ),
  [COMPLIANCESTATUS_ENUM.AUTO_PASS]: (
    <span
      style={{ color: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.AUTO_PASS], marginRight: '8px' }}
    >
      <span
        className="dot"
        style={{ backgroundColor: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.AUTO_PASS] }}
      ></span>
      自动通过
    </span>
  ),
  [COMPLIANCESTATUS_ENUM.REJECT]: (
    <span
      style={{ color: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.REJECT], marginRight: '8px' }}
    >
      <span
        className="dot"
        style={{ backgroundColor: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.REJECT] }}
      ></span>
      已驳回
    </span>
  ),
  [COMPLIANCESTATUS_ENUM.SKIP]: (
    <span style={{ color: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.SKIP], marginRight: '8px' }}>
      <span
        className="dot"
        style={{ backgroundColor: COMPLIANCESTATUS_COLOR[COMPLIANCESTATUS_ENUM.SKIP] }}
      ></span>
      跳过
    </span>
  ),
};

export enum LUXURY_REVIEW_STATUS_ENUM {
  NO_NEED_REVIEW = 'NO_NEED_REVIEW',
  PENDING_REVIEW = 'PENDING_REVIEW',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
}

export const LUXURY_REVIEW_STATUS_NAME = {
  [LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW]: '无需审核',
  [LUXURY_REVIEW_STATUS_ENUM.PENDING_REVIEW]: '待审核',
  [LUXURY_REVIEW_STATUS_ENUM.REJECTED]: '不通过',
  [LUXURY_REVIEW_STATUS_ENUM.APPROVED]: '已通过',
};

export const LUXURY_REVIEW_STATUS_COLOR = {
  [LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW]: '#87d068',
  [LUXURY_REVIEW_STATUS_ENUM.PENDING_REVIEW]: '#0C6AE4',
  [LUXURY_REVIEW_STATUS_ENUM.REJECTED]: '#E90000',
  [LUXURY_REVIEW_STATUS_ENUM.APPROVED]: '#87d068',
};

export const LUXURY_REVIEW_STATUS_ICON = {
  [LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW]: (
    <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
  ),
  [LUXURY_REVIEW_STATUS_ENUM.PENDING_REVIEW]: (
    <Icon style={{ color: '#0C6AE4', marginRight: '5px' }} theme="filled" type="clock-circle" />
  ),
  [LUXURY_REVIEW_STATUS_ENUM.REJECTED]: (
    <Icon style={{ color: '#E90000', marginRight: '5px' }} theme="filled" type="close-circle" />
  ),
  [LUXURY_REVIEW_STATUS_ENUM.APPROVED]: (
    <Icon style={{ color: '#87d068', marginRight: '5px' }} theme="filled" type="check-circle" />
  ),
};

export const LUXURY_REVIEW_STATUS_COLOR_NAME = {
  [LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW]: (
    <span
      style={{
        color: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW],
        marginRight: '8px',
      }}
    >
      <span
        className="dot"
        style={{
          backgroundColor: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW],
        }}
      ></span>
      无需审核
    </span>
  ),
  [LUXURY_REVIEW_STATUS_ENUM.PENDING_REVIEW]: (
    <span
      style={{
        color: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.PENDING_REVIEW],
        marginRight: '8px',
      }}
    >
      <span
        className="dot"
        style={{
          backgroundColor: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.PENDING_REVIEW],
        }}
      ></span>
      待审核
    </span>
  ),
  [LUXURY_REVIEW_STATUS_ENUM.REJECTED]: (
    <span
      style={{
        color: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.REJECTED],
        marginRight: '8px',
      }}
    >
      <span
        className="dot"
        style={{ backgroundColor: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.REJECTED] }}
      ></span>
      不通过
    </span>
  ),
  [LUXURY_REVIEW_STATUS_ENUM.APPROVED]: (
    <span
      style={{
        color: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.APPROVED],
        marginRight: '8px',
      }}
    >
      <span
        className="dot"
        style={{ backgroundColor: LUXURY_REVIEW_STATUS_COLOR[LUXURY_REVIEW_STATUS_ENUM.APPROVED] }}
      ></span>
      已通过
    </span>
  ),
};
