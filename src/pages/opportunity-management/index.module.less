.publishFeeManageContainer {
  background-color: white;

  :global {
    .page-layout-body-wrap {
      margin: 0;
    }

    .ant-tabs-bar {
      margin: 0;
      background-color: white;
    }

    .layout-content-item {
      margin: 20px;
    }

    .page-layout-body-wrap {
      margin: 0;
    }

    .ant-tabs {
      .ant-tabs-bar {
        border-bottom: 0 !important;
        // margin-bottom: 16px;
      }

      .ant-tabs-ink-bar {
        background: #204eff;
      }

      .ant-tabs-nav .ant-tabs-tab {
        padding: 12px 6px;
      }

      .ant-tabs-tab {
        margin-right: 16px;
      }
    }

    .ant-table-fixed-header .ant-table-scroll .ant-table-header {
      overflow-x: auto !important;
      padding-bottom: 15px !important;
      width: 101%;
      height: 44px !important;
    }

    .ant-table-header {
      background-color: #f0f4f7 !important;
    }
  }
}

.title-line {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title-line-left {
    display: flex;
    align-items: center;
  }
  .title-line-name {
    color: #444444;
    font-size: 16px;
    font-weight: 500;
    margin-right: 8px;
  }
  .title-line-time {
    font-size: 12px;
    color: #999999;
  }
}

.steps-line {
  margin-top: 16px;
}

.detail-line {
  margin-top: 16px;
}

.tabs-components {
  :global {
    .ant-tabs {
      color: #444444;
    }
    .ant-tabs-nav .ant-tabs-tab {
      padding-top: 0px;
      margin-right: 16px;
    }
  }
}

.opportunity-information-title {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.select-session-calendar {
  margin-top: -16px;
  :global {
    .calendar-header-picker {
      // right: 16px;
      // top: -12px;
      position: unset !important;
    }
    .calendar-header-left {
      width: 100%;
    }
  }
}

.goods-name {
  // width: 150px;
  display: flex;
  align-items: flex-start;
}

.goods-name-line {
  cursor: pointer;
  font-weight: 500;
  width: 170px;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  white-space: nowrap;

  // &:hover {
  //   text-decoration: underline;
  // }
}

.company {
  display: flex;
  align-items: center;
}

.modal-form {
  :global .ant-form-item {
    .ant-form-item-control-wrapper {
      .ant-form-item-control {
        position: relative;
        line-height: 40px !important;
        zoom: 1;
      }
    }
  }
}
