import React, { useState, useEffect } from 'react';
import { Steps } from './components';
import { Tabs } from 'antd';
import styles from './index.module.less';
import { checkAuth } from 'qmkit';
import PageLayout from '@/components/PageLayout';
import style from '../../styles/index.module.less';
import { AllList } from './components';

const OpportunityManagement = () => {
  const [keys, setKeys] = useState<'ALL' | 'PUBLIC_POOL' | undefined>(
    checkAuth('f_opportunity_management_all_list')
      ? 'ALL'
      : checkAuth('f_opportunity_management_seas_list')
      ? 'PUBLIC_POOL'
      : undefined,
  );

  const handleChange = (value: any) => {
    setKeys(value);
  };

  return (
    <PageLayout className={styles.publishFeeManageContainer} routePath="/opportunity-management">
      <div
        className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
        style={{ display: 'flex', flexDirection: 'column' }}
      >
        <Tabs onChange={handleChange} style={{ marginTop: '-10px' }}>
          {checkAuth('f_opportunity_management_all_list') && (
            <Tabs.TabPane key="ALL" tab="全部"></Tabs.TabPane>
          )}
          {checkAuth('f_opportunity_management_seas_list') && (
            <Tabs.TabPane key="PUBLIC_POOL" tab="商机公海池"></Tabs.TabPane>
          )}
        </Tabs>
        <AllList currentKey={keys} />
      </div>
    </PageLayout>
  );
};

export default OpportunityManagement;
