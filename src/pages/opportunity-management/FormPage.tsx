import React, { useEffect } from 'react';
import PageLayout from '@/components/PageLayout/index';
import { Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import { Form, Spin, Button } from 'antd';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { history, cache } from 'qmkit';
import { BasisMeg } from './components';
import { getQueryParams } from 'web-common-modules/utils/params';
import { useAddEdit } from './hooks';
import { checkAuth } from 'qmkit';

const FormPage = (props: any) => {
  const path = history.location.pathname;
  const { form } = props;
  const { delRoutetag } = useCloseAndJump();

  const id = getQueryParams()?.id;

  // @TODO: 取消 如果是编辑进来的 取消的时候返回到详情页面
  const handleCancel = () => {
    delRoutetag();
    if (path.includes('opportunity-management-edit')) {
      return history.replace(`/opportunity-management-detail?id=${id}`);
    }
    history.replace('/opportunity-management');
  };

  const {
    createRun,
    createLoading,
    updateRun,
    updateLoading,
    detailRun,
    detailLoading,
    detailData,
    getContactLoading,
  } = useAddEdit({
    onRefresh: () => {
      handleCancel();
    },
    form: form,
  });

  const handleSubmit = () => {
    form.validateFields((err: any, values: any) => {
      if (err) {
        return;
      }
      const { attachments, follower, sourceChannel, cooperationType, ...otherValues } = values;

      if (attachments?.length) {
        const urls = attachments.map((item: any) => item.url);
        otherValues.attachments = urls;
      }
      if (follower) {
        otherValues.follower = follower.key;
      }

      if (sourceChannel) {
        otherValues.sourceChannel = sourceChannel.key;
      }

      if (cooperationType) {
        otherValues.cooperationTypeList = cooperationType.map((item: any) => item.key);
      }

      if (id) {
        if (!checkAuth('f_opportunity_management_all_see') && !detailData?.follower) {
          otherValues.contactMobile = '******';
        }
        updateRun({ ...otherValues, id, version: detailData?.version });
      } else {
        createRun({ ...otherValues, version: detailData?.version });
      }
    });
  };

  useEffect(() => {
    if (id) {
      detailRun({ id });
    }
    if (!id) {
      const userInfo = JSON.parse(localStorage.getItem(cache.LOGIN_DATA) || '{}');
      form.setFieldsValue({
        follower: {
          key: userInfo?.employeeId,
          label: userInfo?.employeeName,
        },
      });
    }
  }, [id]);

  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={detailLoading || getContactLoading}>
          <Form>
            <Card title="基础信息">
              <BasisMeg form={form} detailData={detailData} />
            </Card>
          </Form>
        </Spin>
        <FormBottomCard>
          <Button
            style={{ marginRight: '6px' }}
            onClick={handleCancel}
            loading={createLoading || updateLoading}
          >
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={createLoading || updateLoading}>
            保存
          </Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(FormPage);
