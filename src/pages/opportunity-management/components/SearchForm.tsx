import React, { useCallback, useEffect } from 'react';
import { FormComponentProps } from 'antd/es/form';
import { Form } from 'antd';
import { useSearch } from '../hooks';
import SearchFormComponent from '@/pages/report-sheet/components/SearchForm';
import moment from 'moment';

interface IProps extends FormComponentProps {
  onSearch: (value: any) => void;
  getTableHeight?: any;
  currentKey: string;
  clearSelection?: () => void;
}
const SearchForm = (props: IProps) => {
  const { form, getTableHeight, onSearch, currentKey, clearSelection } = props;
  const { options } = useSearch({ form });
  const onSubmit = useCallback(
    (init?: boolean) => {
      form.validateFields((err, values) => {
        clearSelection?.();
        const {
          actualCommissionRate,
          actualConfirmedBrandFee,
          contactDate,
          gmtCreated,
          ...otherValues
        } = values;

        if (contactDate?.length) {
          const [contactDateStart, contactDateEnd] = contactDate;
          otherValues.contactDateStart = moment(contactDateStart).format('YYYY-MM-DD');
          otherValues.contactDateEnd = moment(contactDateEnd).format('YYYY-MM-DD');
        }

        if (gmtCreated?.length) {
          const [gmtCreatedStart, gmtCreatedEnd] = gmtCreated;
          otherValues.gmtCreatedStart = moment(gmtCreatedStart).format('YYYY-MM-DD');
          otherValues.gmtCreatedEnd = moment(gmtCreatedEnd).format('YYYY-MM-DD');
        }

        const [actualCommissionRateMin, actualCommissionRateMax] = actualCommissionRate || [];
        if (actualCommissionRateMin) {
          otherValues.actualCommissionRateMin = (actualCommissionRateMin / 100).toFixed(4);
        }
        if (actualCommissionRateMax) {
          otherValues.actualCommissionRateMax = (actualCommissionRateMax / 100).toFixed(4);
        }

        const [actualConfirmedBrandFeeMin, actualConfirmedBrandFeeMax] =
          actualConfirmedBrandFee || [];
        if (actualConfirmedBrandFeeMin) {
          otherValues.actualConfirmedBrandFeeMin = actualConfirmedBrandFeeMin;
        }
        if (actualConfirmedBrandFeeMax) {
          otherValues.actualConfirmedBrandFeeMax = actualConfirmedBrandFeeMax;
        }

        if (!err) {
          onSearch(otherValues);
        }
      });
    },
    [onSearch, form],
  );

  const onReset = () => {
    form.resetFields();
    clearSelection?.();
    onSubmit();
  };

  useEffect(() => {
    form.resetFields();
  }, [currentKey]);

  return (
    <div>
      <SearchFormComponent
        form={form}
        options={options}
        loading={false}
        onSearch={onSubmit}
        onReset={onReset}
        needMore
        getTableHeight={getTableHeight}
      />
    </div>
  );
};

export default Form.create<IProps>()(SearchForm);
