import React, { useState, useEffect } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ModalProps } from 'antd/lib/modal';
import WithToggleModal from '@/components/WithToggleModal';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { Modal, Form, Table, Button, Select, Input, message, Cascader } from 'antd';
import style from '@/styles/index.module.less';
import { useOpportunityCloseReasonOptions, useRowSelection, useBroadcastTable } from '../hooks';
import usePagination from '@/hooks/usePagination';
import PaginationProxy from '@/common/constants/Pagination';
// import { useList, APIKEY } from '@/pages/live-room-manage/hooks/useList';
import {
  SelectionProcessKanbanBusinessOpportunityPageRequest,
  SelectionProcessKanbanBusinessOpportunityPageResult,
  businessOpportunityClose,
  businessOpportunityCloseReason,
  BusinessOpportunityCloseReasonResult,
} from '../services';
import { useRequest } from 'ahooks';
import { FlowStatus_ChoiceList } from '@/pages/selection-flow-board/types';
import { formatString } from '@/pages/selection-flow-board/utils';
import { useList, APIKEY } from '../hooks';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const BroadcastInformationModal = (props: IProps) => {
  const { form, visible, onRefresh, detailData, detailOnRefresh, ...rest } = props;
  const [opportunityCloseReasonVisible, setOpportunityCloseReasonVisible] = useState(false);
  const { getFieldDecorator } = form;
  const [closeReasonList, setCloseReasonList] = useState<BusinessOpportunityCloseReasonResult[]>(
    [],
  );

  const { run: closeOpportunityRun, loading: closeOpportunityLoading } = useRequest(
    businessOpportunityClose,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        message.success('操作成功');
        onRefresh();
        detailOnRefresh();
        rest.onCancel();
        setOpportunityCloseReasonVisible(false);
      },
    },
  );

  const { run: getCloseReasonRun, loading: getCloseReasonLoading } = useRequest(
    businessOpportunityCloseReason,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        setCloseReasonList(res?.result || []);
      },
    },
  );

  const {
    dataSource,
    pagination,
    onPageChange,
    onSearch,
    onRefresh: listOnRefresh,
    loading,
  } = useList<
    SelectionProcessKanbanBusinessOpportunityPageRequest,
    SelectionProcessKanbanBusinessOpportunityPageResult
  >(APIKEY.SELECTION_PROCESS_KANBAN_LIVE_ROOM_LIST);

  const { options } = useOpportunityCloseReasonOptions({ deptId: detailData?.followerDeptId });
  const { rowSelection, selectedRows, selectedRowKeys, clearSelection } = useRowSelection();
  const { columns } = useBroadcastTable({ isBtn: false });

  useEffect(() => {
    if (visible) {
      onSearch({
        businessOpportunityId: detailData?.id,
        status: [
          FlowStatus_ChoiceList.ABORT_LIVE,
          FlowStatus_ChoiceList.CANCEL,
          FlowStatus_ChoiceList.LOSE_EFFICACY,
          FlowStatus_ChoiceList.ABORT_WAIT_LIVE,
        ],
        businessOpportunityRelType: 'DROP_PRODUCT',
      });
      getCloseReasonRun({});
    }
  }, [visible]);

  const handleSearch = () => {
    clearSelection();
    form.validateFields((err, values) => {
      const { liveDate, status, spuName, selectionNo, ...otherValues } = values;
      if (liveDate?.[0] && liveDate?.[1]) {
        const [liveDateStartTime, liveDateEndTime] = liveDate;
        otherValues.liveDateStartTime = liveDateStartTime.format('YYYY-MM-DD');
        otherValues.liveDateEndTime = liveDateEndTime.format('YYYY-MM-DD');
      }
      if (!status?.length) {
        otherValues.status = [
          FlowStatus_ChoiceList.ABORT_LIVE,
          FlowStatus_ChoiceList.CANCEL,
          FlowStatus_ChoiceList.LOSE_EFFICACY,
          FlowStatus_ChoiceList.ABORT_WAIT_LIVE,
        ];
      } else {
        otherValues.status = status.includes(FlowStatus_ChoiceList.ABORT_LIVE)
          ? [...status, FlowStatus_ChoiceList.ABORT_WAIT_LIVE]
          : status;
      }
      onSearch({
        ...otherValues,
        businessOpportunityId: detailData?.id,
        businessOpportunityRelType: 'DROP_PRODUCT',
        spuName: formatString(spuName),
        selectionNo: formatString(selectionNo),
      });
    });
  };

  const handleReset = () => {
    clearSelection();
    form.resetFields();
    onSearch({
      businessOpportunityId: detailData?.id,
      status: [
        FlowStatus_ChoiceList.ABORT_LIVE,
        FlowStatus_ChoiceList.CANCEL,
        FlowStatus_ChoiceList.LOSE_EFFICACY,
      ],
      businessOpportunityRelType: 'DROP_PRODUCT',
    });
  };

  const handleOnOk = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      const { closeReason, closeDescription } = values;
      closeOpportunityRun({
        id: detailData?.id,
        version: detailData?.version,
        closeReason: closeReason?.[closeReason?.length - 1],
        closeDescription,
        selectionIds: selectedRowKeys,
      });
    });
  };

  return (
    <>
      <Modal
        title="商机关闭原因"
        visible={visible}
        // onCancel={onCancel}
        confirmLoading={false}
        // onOk={handleOnOk}
        width={1000}
        className={style['modal-sty']}
        maskClosable={false}
        {...rest}
        footer={
          <>
            <Button
              type="primary"
              onClick={() => {
                if (selectedRows?.length === 0) {
                  message.warning('请选择场次');
                  return;
                }
                setOpportunityCloseReasonVisible(true);
              }}
            >
              下一步
            </Button>
          </>
        }
      >
        <div>
          <SearchFormComponent
            form={form}
            loading={false}
            onSearch={handleSearch}
            onReset={handleReset}
            needMore={true}
            rowShowNum={3}
            options={options}
          />
        </div>
        <div>
          <Table
            columns={columns}
            dataSource={dataSource as any}
            rowSelection={rowSelection}
            pagination={false}
            rowKey="id"
            scroll={{ y: 300 }}
            loading={loading}
          />
        </div>
        <div style={{ display: 'flex', justifyContent: 'flex-end', paddingRight: '4px' }}>
          <PaginationProxy
            {...pagination}
            onChange={({ current, size }: any) => {
              clearSelection();
              onPageChange(current, size);
            }}
            valueType="merge"
          />
        </div>
      </Modal>
      <Modal
        title="商机关闭原因"
        visible={opportunityCloseReasonVisible}
        width={500}
        className={style['modal-sty']}
        maskClosable={false}
        onCancel={() => setOpportunityCloseReasonVisible(false)}
        onOk={handleOnOk}
        confirmLoading={closeOpportunityLoading}
      >
        <p>请选择掉品的场次并选择商机关闭原因:</p>
        <Form labelAlign="right">
          <Form.Item {...formItemLayout} label="关闭原因">
            {getFieldDecorator('closeReason', {
              rules: [{ required: true, message: '请选择商机关闭原因' }],
            })(
              <Cascader
                placeholder="请选择商机关闭原因"
                fieldNames={{ label: 'reasonName', value: 'id' }}
                options={closeReasonList}
              />,
            )}
          </Form.Item>
          <Form.Item {...formItemLayout} label="说明">
            {getFieldDecorator(
              'closeDescription',
              {},
            )(
              <Input.TextArea
                rows={4}
                maxLength={500}
                placeholder="请选择商机关闭原因"
              ></Input.TextArea>,
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default Form.create<IProps>()(WithToggleModal(BroadcastInformationModal));
