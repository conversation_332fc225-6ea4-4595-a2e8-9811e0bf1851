import React, { useEffect } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Modal, Form, InputNumber, message, DatePicker, Select, Radio } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import FileUploadSouceId from '@/components/FileUploadSouceId';
import { useRequest } from 'ahooks';
import style from '@/styles/index.module.less';
import moment from 'moment';
import { businessOpportunityUpdateSampleInfo } from '../services';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const SendSamples = (props: IProps) => {
  const { form, visible, onRefresh, detailData, ...rest } = props;
  const { getFieldDecorator } = form;

  const { run: updateSampleInfoRun, loading: updateSampleInfoLoading } = useRequest(
    businessOpportunityUpdateSampleInfo,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('更新成功');
        onRefresh?.();
        rest?.onCancel?.();
      },
    },
  );

  const handleOnOk = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      updateSampleInfoRun({
        ...values,
        id: detailData?.id,
        version: detailData?.version,
      });
    });
  };

  return (
    <Modal
      title="样品信息"
      {...rest}
      visible={visible}
      confirmLoading={updateSampleInfoLoading}
      onOk={handleOnOk}
      width={500}
      className={style['modal-sty']}
      maskClosable={false}
    >
      <p>请选择是否寄样:</p>
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} label="是否寄样">
          {getFieldDecorator('sampleFlag', {
            rules: [
              {
                required: true,
                message: '请选择是否寄样',
              },
            ],
            initialValue: detailData?.sampleSentFlag,
          })(
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(SendSamples));
