import React, { useEffect } from 'react';
import DetailTitle from '@/components/DetailTitle';
import styles from '../index.module.less';
import { Icon, Table, message } from 'antd';
import PaginationProxy from '@/common/constants/Pagination';
import { BroadcastInformationModal } from './index';
import { BusinessOpportunityDetailResult } from '../services';
import { useCloseBroadcast, useBroadcastTable } from '../hooks';
import { AuthWrapper } from 'qmkit';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const BroadcastInformation = (props: IProps) => {
  const { detailData, onRefresh } = props;

  const {
    dataSource,
    pagination,
    onPageChange,
    deleteSelectionRoundRelRun,
    deleteSelectionRoundRelLoading,
    loading,
    listOnRefresh,
  } = useCloseBroadcast(detailData, 'COMPLETED_LIVE');
  const handleDel = (params: any) => {
    deleteSelectionRoundRelRun(params);
  };

  const { columns } = useBroadcastTable({ handleDel });

  return (
    <div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>上播信息 </span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <BroadcastInformationModal
                detailData={detailData}
                onRefresh={listOnRefresh}
                detailOnRefresh={onRefresh}
              >
                <Icon type="form" />
              </BroadcastInformationModal>
            </AuthWrapper>
          </div>
        }
      />
      <div style={{ marginTop: '-12px' }}>
        <Table
          columns={columns}
          pagination={false}
          rowKey={'id'}
          scroll={{ y: 300 }}
          dataSource={dataSource as any}
          loading={deleteSelectionRoundRelLoading || loading}
        />
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-end', paddingRight: '4px' }}>
        <PaginationProxy
          {...pagination}
          onChange={({ current, size }: any) => {
            onPageChange(current, size);
          }}
          valueType="merge"
        />
      </div>
    </div>
  );
};

export default BroadcastInformation;
