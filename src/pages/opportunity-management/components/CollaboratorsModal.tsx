import React, { useEffect } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Modal, Form, InputNumber, message, DatePicker, Select, Radio } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import FileUploadSouceId from '@/components/FileUploadSouceId';
import { useRequest } from 'ahooks';
import style from '@/styles/index.module.less';
import moment from 'moment';
import { useSupplierPage } from '../hooks';
import { businessOpportunityUpdateCooperationSubject } from '../services';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const CollaboratorsModal = (props: IProps) => {
  const { form, visible, onRefresh, detailData, ...rest } = props;
  const { getFieldDecorator } = form;

  const { run: updateCooperationSubjectRun, loading: updateCooperationSubjectLoading } = useRequest(
    businessOpportunityUpdateCooperationSubject,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        message.success('操作成功');
        onRefresh();
        rest.onCancel();
      },
    },
  );

  const {
    supplierList,
    supplierPageRun,
    supplierPageLoading,
    handleSupplierChange,
    handelSupplierSearch,
    handleSupplierBlur,
  } = useSupplierPage();

  useEffect(() => {
    supplierPageRun({ current: 1, size: 20, type: 'CUSTOMER' });
  }, []);

  const handleOnOk = () => {
    form.validateFields((err, values) => {
      if (err) return;
      const { contractCooperationSubjectSupplierId } = values;
      updateCooperationSubjectRun({
        contractCooperationSubjectSupplierId: contractCooperationSubjectSupplierId?.key,
        id: detailData?.id,
        version: detailData?.version,
      });
    });
  };

  return (
    <Modal
      title="合作主体"
      {...rest}
      visible={visible}
      confirmLoading={updateCooperationSubjectLoading}
      onOk={handleOnOk}
      width={500}
      className={style['modal-sty']}
      maskClosable={false}
    >
      <p>请选择合作主体(关联客户档案):</p>
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} label="客户信息">
          {getFieldDecorator('contractCooperationSubjectSupplierId', {
            rules: [
              {
                required: true,
                message: '请选择客户信息',
              },
            ],
            initialValue: {
              key: detailData?.contractCooperationSubjectSupplierId,
              label: detailData?.contractCooperationSubjectSupplierName,
            },
          })(
            <Select
              loading={supplierPageLoading}
              placeholder="请选择"
              allowClear
              showSearch
              filterOption={false}
              onChange={handleSupplierChange}
              onSearch={handelSupplierSearch}
              onBlur={handleSupplierBlur}
              labelInValue={true}
            >
              {supplierList?.map((item) => (
                <Select.Option key={item.id} value={item.id}>
                  {item.companyName}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(CollaboratorsModal));
