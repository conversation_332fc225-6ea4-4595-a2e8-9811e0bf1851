import React, { useEffect } from 'react';
import DetailTitle from '@/components/DetailTitle';
import { Icon, Table, message } from 'antd';
import styles from '../index.module.less';
import { useSessionTable } from '../hooks/useTable';
import PaginationProxy from '@/common/constants/Pagination';
import usePagination from '@/hooks/usePagination';
import { SelectSession } from './index';
// import { useList, APIKEY } from '@/pages/live-room-manage/hooks/useList';
import {
  BusinessOpportunityDetailResult,
  BusinessOpportunitySessionRelPageRequest,
  BusinessOpportunitySessionRelPageResult,
  businessOpportunitySessionRelDeleteById,
} from '../services';
import { useRequest } from 'ahooks';
import { AuthWrapper } from 'qmkit';
import { useList, APIKEY } from '../hooks';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const SessionInformation = (props: IProps) => {
  const { detailData, onRefresh } = props;
  const {
    dataSource,
    pagination,
    loading,
    onPageChange,
    onSearch,
    onRefresh: listOnRefresh,
  } = useList<BusinessOpportunitySessionRelPageRequest, BusinessOpportunitySessionRelPageResult>(
    APIKEY.BUSINESS_OPPORTUNITY_SESSION_REL_PAGE,
    undefined,
    5,
  );

  const { run: deleteByIdRun, loading: deleteByIdLoading } = useRequest(
    businessOpportunitySessionRelDeleteById,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('删除成功');
        listOnRefresh();
      },
    },
  );

  const handleDelete = (id: string) => {
    deleteByIdRun({
      id,
    });
  };

  const { columns } = useSessionTable({ handleDelete });

  useEffect(() => {
    if (detailData?.id) {
      onSearch({
        businessOpportunityId: detailData?.id,
      });
    }
  }, [detailData?.id]);

  return (
    <div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>场次信息 </span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <SelectSession
                detailData={detailData}
                onRefresh={() => {
                  listOnRefresh();
                  onRefresh();
                }}
              >
                <Icon type="form" />
              </SelectSession>
            </AuthWrapper>
          </div>
        }
      />
      <div style={{ marginTop: '-12px' }}>
        <Table
          columns={columns}
          pagination={false}
          rowKey={'id'}
          scroll={{ y: 300 }}
          dataSource={dataSource as any}
          loading={loading || deleteByIdLoading}
        />
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-end', paddingRight: '4px' }}>
        <PaginationProxy
          {...pagination}
          onChange={({ current, size }: any) => {
            // console.log('🚀 ~ LogsTable ~ current:', current);
            onPageChange(current, size);
          }}
          valueType="merge"
        />
      </div>
    </div>
  );
};

export default SessionInformation;
