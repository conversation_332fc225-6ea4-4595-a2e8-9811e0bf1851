import React, { useMemo, useState } from 'react';
import { Descriptions } from './index';
import styles from '../index.module.less';
import { Icon, message, Spin } from 'antd';
import { renderQualification } from '@/pages/choice-list-new/components/LegalCheckDrawer/imgUtils';
import DetailTitle from '@/components/DetailTitle';
import { history, AuthWrapper } from 'qmkit';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { BusinessOpportunityDetailResult } from '../services';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import moment from 'moment';
import { OPPORTUNITY_TYPE_NAME, OPPORTUNITY_TYPE_ENUM } from '../types';
import { useRequest, useSetState } from 'ahooks';
import { businessOpportunityGetContact } from '../services';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const OpportunityInformation = (props: IProps) => {
  const { detailData } = props;

  const [changePhone, setChangePhone] = useSetState<{
    visible: boolean;
    phone: string;
  }>({
    visible: false,
    phone: '',
  });

  const { run: getContact, loading: getContactLoading } = useRequest(
    businessOpportunityGetContact,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        setChangePhone({
          visible: true,
          phone: res?.result?.contactMobile || '',
        });
      },
    },
  );

  const { closeAndJumpToPage } = useCloseAndJump();

  const { codeEnum: SOURCE_CHANNEL_ENUM } = useCode(CODE_ENUM.SOURCE_CHANNEL);

  const attachments = useMemo(() => {
    return (
      detailData?.attachments?.map((item: any) => ({
        url: item,
      })) || []
    );
  }, [detailData?.attachments]);

  return (
    <div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>基础信息</span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <Icon
                onClick={() => {
                  closeAndJumpToPage(`/opportunity-management-edit?id=${detailData?.id}`);
                }}
                type="form"
              />
            </AuthWrapper>
          </div>
        }
      />
      <Spin spinning={getContactLoading}>
        <div style={{ padding: '0 16px' }}>
          <Descriptions column={4}>
            <Descriptions.Item label="品牌名称">
              <span style={{ wordBreak: 'break-all' }}>{detailData?.brandName || '-'}</span>
            </Descriptions.Item>
            <Descriptions.Item label="联系人">
              <span style={{ wordBreak: 'break-all' }}>{detailData?.contactPerson || '-'}</span>
            </Descriptions.Item>
            <Descriptions.Item label="联系方式">
              <span>
                {changePhone.visible ? changePhone.phone : detailData?.contactMobile || '-'}
              </span>

              {!detailData?.followerName ? (
                <AuthWrapper functionName="f_opportunity_management_all_see">
                  <span style={{ marginLeft: '8px' }}>
                    {changePhone.visible ? (
                      <a
                        onClick={() => {
                          setChangePhone({
                            visible: false,
                            phone: '',
                          });
                        }}
                      >
                        隐藏
                      </a>
                    ) : (
                      <a
                        onClick={() => {
                          getContact({
                            id: detailData?.id,
                          });
                        }}
                      >
                        查看
                      </a>
                    )}
                  </span>
                </AuthWrapper>
              ) : (
                <></>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="合作主体">
              <span style={{ wordBreak: 'break-all' }}>
                {detailData?.cooperationSubject || '-'}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="来源渠道">
              {SOURCE_CHANNEL_ENUM?.[detailData?.sourceChannel as string] || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="建联日期">
              {detailData?.contactDate ? moment(detailData?.contactDate).format('YYYY-MM-DD') : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="跟进人">{detailData?.followerName || '-'}</Descriptions.Item>
            <Descriptions.Item label="商机类型">
              {OPPORTUNITY_TYPE_NAME?.[
                detailData?.businessOpportunityType as OPPORTUNITY_TYPE_ENUM
              ] || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="合作方式">
              {detailData?.cooperationTypeDesc || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="附件" span={4}>
              {renderQualification(attachments)}
            </Descriptions.Item>
            <Descriptions.Item span={2} label="备注信息">
              <div style={{ wordBreak: 'break-all' }}>{detailData?.remark || '-'}</div>
            </Descriptions.Item>
          </Descriptions>
        </div>
      </Spin>
    </div>
  );
};

export default OpportunityInformation;
