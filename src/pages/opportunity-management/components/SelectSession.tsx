import React, { useState, useCallback, useEffect } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ModalProps } from 'antd/lib/modal';
import WithToggleModal from '@/components/WithToggleModal';
import { Form, Modal, Row, Col, Select, Radio, Checkbox, message } from 'antd';
import style from '@/styles/index.module.less';
import { CalendarProxy } from 'web-common-modules/components';
import moment, { Moment } from 'moment';
import styles from '../index.module.less';
import { useGroup } from '../hooks';
import { useLiveRoom } from '@/pages/session-limit-config/hooks';
import { businessOpportunityUpdateSessionInfo } from '../services';
import { useRequest } from 'ahooks';
import { LIVE_SESSION_TIMES_LIST } from '../types';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 7,
  },
  wrapperCol: {
    span: 13,
  },
};

const liveTimeItemLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 19,
  },
};

const liveId = __ENVINFO__.MY_ENV === 'test' ? '67' : '119';

const SelectSession = (props: IProps) => {
  const { form, visible, onCancel, detailData, onRefresh, ...rest } = props;
  const { getFieldDecorator } = form;
  const [selectedDate, setSelectedDate] = useState<{
    [key: string]: string[];
  }>({}); //已选中
  const [checkboxAllMap, setCheckboxAllMap] = useState({}); // 是否全选
  const [YearMonthNumber, setYearMonthNumber] = useState({}); // 年月可选的个数
  const [currentDate, setCurrentDate] = useState(moment().format('YYYY-MM'));
  const [date, setDate] = useState();

  const { liveRoomIds } = form.getFieldsValue();

  const { run: updateSessionInfoRun, loading: updateSessionInfoLoading } = useRequest(
    businessOpportunityUpdateSessionInfo,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('更新成功');
        handleCancel();
        onRefresh?.();
      },
    },
  );

  const {
    liveRoomList,
    liveRoomLoading,
    liveRoomRun,
    handleLiveRoomBlur,
    handleLiveRoomChange,
    handleLiveRoomSearch,
    setLiveRoomList,
  } = useLiveRoom();

  const {
    handleGroupChange,
    groupList,
    loading: projectGroupLoading,
    onSearchGroup,
    handleGroupBlur,
  } = useGroup(detailData?.followerDeptId);

  const dateCellRender = useCallback(
    (date: Moment) => {
      // if (disabledDate(date)) return null;
      const dateString = moment(date).format('YYYY-MM-DD');
      const dateYearMonth = moment(date).format('YYYY-MM');
      const currentYearMonth = selectedDate[dateYearMonth];
      const isBefore = moment().isBefore(moment(date));
      const isSame = moment().isSame(moment(date), 'day');
      // const dateDisabled = disabledData.includes(dateString);
      // console.log('date', dateString, selectedDate);
      // const checked = dateDisabled || selectedDate.includes(dateString);
      const checked = currentYearMonth && currentYearMonth.includes(dateString);

      return (
        <div className="live-round-wrap">
          {isBefore || isSame ? (
            <Checkbox className="live-round-checkbox" checked={checked} />
          ) : (
            <></>
          )}
        </div>
      );
    },
    [selectedDate],
  );

  // 格子选中/取消选中
  const onSelectDate = (date: Moment) => {
    const isSameOrAfter = moment().isBefore(moment(date));
    const isSame = moment().isSame(moment(date), 'day');
    if (!isSameOrAfter && !isSame) {
      return;
    }
    const dateString = moment(date).format('YYYY-MM-DD');
    const dateYearMonth = moment(date).format('YYYY-MM');

    const currentSelect = selectedDate[dateYearMonth] ? [...selectedDate[dateYearMonth]] : [];
    if (currentSelect.includes(dateString)) {
      // 取消选中
      const res = currentSelect.filter((item) => item !== dateString);
      setSelectedDate({ ...selectedDate, ...{ [dateYearMonth]: res } });
      setCheckboxAllMap({ ...checkboxAllMap, ...{ [currentDate]: false } });
    } else {
      // 选中是否是全选
      const currentSelectList = [...currentSelect, dateString];
      setSelectedDate({ ...selectedDate, ...{ [dateYearMonth]: currentSelectList } });
      checkIsAll(currentSelectList);
    }
  };

  // 单选判断是否已经全部选择本月
  const checkIsAll = (list: any) => {
    const currentNumber = YearMonthNumber[currentDate];
    if (currentNumber && currentNumber === list?.length) {
      setCheckboxAllMap({ ...checkboxAllMap, ...{ [currentDate]: true } });
      return;
    }
    // 没有缓存
    const yearMonth = moment().format('YYYY-MM');
    if (yearMonth === currentDate) {
      // 当前月份
      const res = dealCurrentMonth();
      setYearMonthNumber({ ...YearMonthNumber, ...{ [currentDate]: res.length } });
      setCheckboxAllMap({ ...checkboxAllMap, ...{ [currentDate]: res.length === list?.length } });
    } else {
      // 其他月份
      const res = dealOtherMonth();
      setYearMonthNumber({ ...YearMonthNumber, ...{ [currentDate]: res.length } });
      setCheckboxAllMap({ ...checkboxAllMap, ...{ [currentDate]: res.length === list?.length } });
    }
  };

  const dealOtherMonth = () => {
    const currentMonthNumber = moment(currentDate).daysInMonth(); // 选中月份有多少天
    const res = [];
    for (let i = 1; i <= currentMonthNumber; i++) {
      const day = i < 10 ? `0${i}` : `${i}`;
      const str = `${currentDate}-${day}`;
      res.push(moment(str).format('YYYY-MM-DD'));
      // if (!disabledData.includes(str)) {
      //   // continue

      // }
    }
    return res;
  };
  const handleOnPanelChange = (date: Moment, mode: string) => {
    setCurrentDate(moment(date).format('YYYY-MM'));
  };
  const dealCurrentMonth = () => {
    const currentDay = moment().format('D');
    const currentMonthNumber = moment(currentDate).daysInMonth();
    const res = [];
    for (let i = Number(currentDay); i <= currentMonthNumber; i++) {
      const day = i < 10 ? `0${i}` : `${i}`;
      const str = `${currentDate}-${day}`;
      // console.log('🚀 ~ dealCurrentMonth ~ str:', str, disabledData);
      // if (!disabledData.includes(str)) {
      //   // continue

      // }
      res.push(moment(str).format('YYYY-MM-DD'));
    }
    return res;
  };

  const dealCheckAll = () => {
    const yearMonth = moment().format('YYYY-MM');
    if (yearMonth === currentDate) {
      // 当前月份
      const res = dealCurrentMonth();
      setSelectedDate({ ...selectedDate, ...{ [currentDate]: res } });
      setYearMonthNumber({ ...YearMonthNumber, ...{ [currentDate]: res.length } });
      setCheckboxAllMap({ ...checkboxAllMap, ...{ [currentDate]: res?.length ? true : false } });
    } else {
      // 其他月份
      const res = dealOtherMonth();
      setSelectedDate({ ...selectedDate, ...{ [currentDate]: res } });
      setYearMonthNumber({ ...YearMonthNumber, ...{ [currentDate]: res.length } });
      setCheckboxAllMap({ ...checkboxAllMap, ...{ [currentDate]: res?.length ? true : false } });
    }
  };

  const disabledCalendarDate = (date: Moment) => {
    const isBefore = moment().isBefore(moment(date));
    const isSame = moment().isSame(moment(date), 'day');
    return !isBefore && !isSame;
  };
  const handleChangeAllCheckbox = (e: any) => {
    // console.log(currentDate, '------------>');
    const { checked } = e.target;
    if (checked) {
      // setSelectedDate([]);
      dealCheckAll();
    } else {
      setSelectedDate({ ...selectedDate, ...{ [currentDate]: [] } });
      setCheckboxAllMap({ ...checkboxAllMap, ...{ [currentDate]: false } });
      // setSelectedDate({});
    }
  };

  const headerLeftRender = useCallback(() => {
    const currentValue = checkboxAllMap[currentDate];
    // console.log('🚀 ~ headerLeftRender ~ currentValue:', currentValue, currentDate);
    return (
      <div style={{ height: '22px', marginTop: '8px' }}>
        <Checkbox checked={!!currentValue} onChange={handleChangeAllCheckbox}>
          全选本月
        </Checkbox>
      </div>
    );
  }, [checkboxAllMap, currentDate]);

  const onLiveRoomBlur = useCallback(() => {
    handleLiveRoomBlur(detailData?.followerDeptId);
  }, [detailData?.followerDeptId]);

  const onLiveRoomChange = useCallback(
    (v: string) => {
      handleLiveRoomChange(v, detailData?.followerDeptId);
    },
    [detailData?.followerDeptId],
  );

  const onLiveRoomSearch = useCallback(
    (v) => {
      handleLiveRoomSearch(v, detailData?.followerDeptId);
    },
    [detailData?.followerDeptId],
  );

  useEffect(() => {
    if (detailData?.followerDeptId) {
      liveRoomRun({
        deptId: detailData?.followerDeptId,
      });
    }
  }, [detailData?.followerDeptId]);

  const handleCancel = () => {
    onCancel?.();
    form.resetFields();
    setLiveRoomList([]);
  };

  const handleOnOk = () => {
    form.validateFields((err, values) => {
      // console.log('selectedDate', selectedDate, values);
      if (err) {
        return;
      }
      // 拉平数组可以直接使用flat() 但是会存在兼容问题所以使用reduce替换flat
      const dateValues = Object.values(selectedDate).reduce((acc, curr) => {
        return acc.concat(curr);
      }, []);
      // console.log('🚀 ~ form.validateFields ~ dateValues:', dateValues);
      if (dateValues.length === 0) {
        message.error('请选择场次');
        return;
      }
      const { projectGroupId, ...otherValue } = values;
      updateSessionInfoRun({
        ...otherValue,
        projectGroupId: projectGroupId.key,
        liveDates: dateValues,
        id: detailData?.id,
        version: detailData?.version,
      });
    });
  };

  useEffect(() => {
    if (visible && detailData) {
      form.setFieldsValue({
        projectGroupId: {
          key: detailData?.followerProjectGroupId,
          label: detailData?.followerProjectGroupName,
        },
      });
    }
  }, [visible, detailData]);

  return (
    <Modal
      title="请选择场次信息"
      visible={visible}
      onCancel={handleCancel}
      {...rest}
      confirmLoading={updateSessionInfoLoading}
      onOk={handleOnOk}
      width={1000}
      className={style['modal-sty']}
      maskClosable={false}
    >
      <div>
        <Form>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label="直播间" {...formItemLayout}>
                {getFieldDecorator('liveRoomIds', {
                  rules: [{ required: true, message: '请选择直播间' }],
                })(
                  <Select
                    allowClear
                    placeholder="请选择"
                    loading={liveRoomLoading}
                    showSearch
                    filterOption={false}
                    optionFilterProp="children"
                    dropdownMatchSelectWidth={false}
                    onSearch={onLiveRoomSearch}
                    onChange={onLiveRoomChange}
                    onBlur={onLiveRoomBlur}
                    defaultActiveFirstOption={false}
                    mode="multiple"
                    maxTagCount={1}
                  >
                    {liveRoomList?.map((item) => (
                      <Select.Option key={item?.id} value={item?.id}>
                        {item?.name}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="项目组" {...formItemLayout}>
                {getFieldDecorator('projectGroupId', {
                  rules: [{ required: true, message: '请选择项目组' }],
                })(
                  <Select
                    allowClear
                    placeholder="请选择"
                    loading={projectGroupLoading}
                    maxTagCount={1}
                    onBlur={handleGroupBlur}
                    onChange={handleGroupChange}
                    filterOption={false}
                    showSearch
                    onSearch={onSearchGroup}
                    style={{ width: '100%' }}
                    labelInValue={true}
                  >
                    {groupList?.map((item) => (
                      <Select.Option key={item?.projectGroupId} value={item?.projectGroupId}>
                        {item?.projectGroupName}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="是否罗场" {...formItemLayout}>
                {getFieldDecorator('luoFlag', {
                  rules: [{ required: liveRoomIds?.includes(liveId), message: '请选择是否罗场' }],
                })(
                  <Radio.Group>
                    <Radio value={true}>是</Radio>
                    <Radio value={false}>否</Radio>
                  </Radio.Group>,
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={10}>
              <Form.Item label="直播时段" {...liveTimeItemLayout} style={{ marginLeft: '6px' }}>
                {getFieldDecorator('liveSessionTimesList', {
                  // rules: [{ required: true, message: '请选择直播时段' }],
                })(
                  <Checkbox.Group>
                    {LIVE_SESSION_TIMES_LIST?.map((item) => (
                      <Checkbox key={item?.value} value={item?.value}>
                        {item?.label}
                      </Checkbox>
                    ))}
                  </Checkbox.Group>,
                )}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
      <div className={styles['select-session-calendar']}>
        <CalendarProxy
          // disabledDate={moment()}
          className="calendar-in-modal"
          showSelected={false}
          disabledDate={disabledCalendarDate}
          dateCellRender={dateCellRender}
          onSelect={onSelectDate}
          onChange={setDate}
          onPanelChange={handleOnPanelChange}
          headerLeftRender={headerLeftRender}
        />
      </div>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(SelectSession));
