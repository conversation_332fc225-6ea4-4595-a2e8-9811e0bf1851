import React, { useState } from 'react';
import { Modal, Form, Button, message } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import { ModalProps } from 'antd/lib/modal';
('antd');
import style from '@/styles/index.module.less';
import { DataSourceItem } from 'web-common-modules/components/OSSUpload';
import OSSUpload from '@/pages/gmvCommissionManage/components/gmvCommissionManage/view/components/OSSUpload';
import { useRequest } from 'ahooks';
import styles from '../index.module.less';
import { businessOpportunityImport } from '../services/';

interface IProps extends ModalProps {
  [key: string]: any;
  // defaultTab: string;
}

const formItemLayout = {
  labelCol: {
    span: 2,
    offset: 0,
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    span: 24,
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

const ImportForm = (props: IProps) => {
  const { visible, onCancel, form, setResVisble, setImportResult, ...rest } = props;
  const [fileList, setFileList] = useState<Partial<DataSourceItem>[]>([]);
  const [uploadLoading, setUploadLoading] = useState<boolean>(false);
  const { getFieldDecorator } = form;

  const { run, loading } = useRequest(businessOpportunityImport, {
    manual: true,
    onSuccess: ({ res }) => {
      if (!res?.success) {
        message.error(res?.message || '网络异常');
        return;
      }
      cancel();
      setResVisble?.(true);
      setImportResult({
        loading: false,
        result: res?.result || null,
      });
    },
  });

  const cancel = (e?: any) => {
    form.resetFields();
    setFileList([]);
    onCancel && onCancel(e);
  };

  const onConfirm = () => {
    // console.log('onConfirm');
    form.validateFields((err: any, values: any) => {
      if (err) {
        return;
      }
      const [item] = fileList;
      const { resourceId } = item;
      run({
        resourceId: resourceId as string,
      });
    });
  };

  const downTemplate = () => {
    window.open(
      '//befriend-rss-import-prod.oss-cn-hangzhou.aliyuncs.com/crm/business/v2/%E5%95%86%E6%9C%BA%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
    );
  };

  return (
    <Modal
      className={style['modal-style']}
      title="商机导入"
      maskClosable={false}
      width={500}
      visible={visible}
      {...rest}
      footer={
        <div>
          <Button onClick={cancel} loading={loading}>
            取消
          </Button>
          <Button
            htmlType="submit"
            className="ml-10"
            type="primary"
            onClick={onConfirm}
            loading={loading}
          >
            导入
          </Button>
        </div>
      }
      onCancel={cancel}
    >
      <div style={{ marginBottom: '2px', marginLeft: '30px' }}>
        下载
        <Button type="link" style={{ padding: '0px' }} onClick={downTemplate}>
          导入链接模板
        </Button>
        已查看所需格式示例
      </div>
      <Form className={styles['modal-form']}>
        <Form.Item label="附件" required {...formItemLayout} style={{ marginTop: '-2px' }}>
          {getFieldDecorator('fileList', {
            rules: [
              {
                validator: (_, value, callback) => {
                  if (!value?.length || value?.length === 0) {
                    callback('请上传文件');
                    return;
                  }
                  callback();
                },
              },
            ],
          })(
            <OSSUpload
              renderUpload={() => (
                <div className={style.importLine}>
                  <Button
                    className="h-28"
                    icon="upload"
                    style={{
                      display: 'block',
                    }}
                    loading={uploadLoading}
                  >
                    上传
                  </Button>
                  <span className={style.pro}>支持xls、xlsx格式</span>
                </div>
              )}
              uploadLoading={uploadLoading}
              setLoading={(val: any) => setUploadLoading(val)}
              disabled={false}
              accept=".xls,.xlsx"
              typeCode="COMMON_IMPORT"
              isImage={false}
              maxSize={10 * 1024 * 1024}
              maxLen={1}
              dataSource={fileList}
              onChange={(list: DataSourceItem[]) => {
                const dealList = list
                  .map((item) => ({
                    name: item?.name,
                    resourceId: item?.resourceId,
                  }))
                  .slice(-1);
                setFileList(dealList);
              }}
            />,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(WithToggleModal(ImportForm));
