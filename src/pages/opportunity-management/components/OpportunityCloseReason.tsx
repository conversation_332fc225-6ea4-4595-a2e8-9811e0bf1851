import React, { useEffect } from 'react';
import DetailTitle from '@/components/DetailTitle';
import styles from '../index.module.less';
import { Icon, Table, message } from 'antd';
import PaginationProxy from '@/common/constants/Pagination';
import { useBroadcastTable, useCloseBroadcast } from '../hooks';
import { Descriptions, OpportunityCloseReasonModal } from './index';
import { BusinessOpportunityDetailResult } from '../services';
import { AuthWrapper } from 'qmkit';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const opportunityCloseReason = (props: IProps) => {
  const { detailData, onRefresh } = props;
  const {
    dataSource,
    pagination,
    onPageChange,
    deleteSelectionRoundRelRun,
    deleteSelectionRoundRelLoading,
    loading,
    listOnRefresh,
  } = useCloseBroadcast(detailData, 'DROP_PRODUCT');
  const handleDel = (params: any) => {
    deleteSelectionRoundRelRun(params);
  };

  const { columns } = useBroadcastTable({
    handleDel,
  });

  return (
    <div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>商机关闭原因 </span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <OpportunityCloseReasonModal
                detailData={detailData}
                onRefresh={listOnRefresh}
                detailOnRefresh={onRefresh}
              >
                <Icon type="form" />
              </OpportunityCloseReasonModal>
            </AuthWrapper>
          </div>
        }
      />
      <div style={{ padding: '0 16px' }}>
        <Descriptions column={4}>
          <Descriptions.Item span={4} label="关闭原因">
            {detailData?.closeReason || '-'}
          </Descriptions.Item>
          <Descriptions.Item span={4} label="说明">
            <div style={{ wordBreak: 'break-all' }}>{detailData?.closeDescription || '-'}</div>
          </Descriptions.Item>
        </Descriptions>
      </div>
      <div style={{ marginTop: '-12px' }}>
        <Table
          columns={columns}
          pagination={false}
          rowKey={'id'}
          scroll={{ y: 300 }}
          dataSource={dataSource as any}
          loading={deleteSelectionRoundRelLoading || loading}
        />
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-end', paddingRight: '4px' }}>
        <PaginationProxy
          {...pagination}
          onChange={({ current, size }: any) => {
            onPageChange(current, size);
          }}
          valueType="merge"
        />
      </div>
    </div>
  );
};

export default opportunityCloseReason;
