import React, { useState, useMemo } from 'react';
import { Tabs } from 'antd';
import styles from '../index.module.less';
import {
  OpportunityInformation,
  BusinessConditions,
  SessionInformation,
  SampleInformation,
  Collaborators,
  BroadcastInformation,
  OpportunityCloseReason,
} from './index';
import { BusinessOpportunityDetailResult } from '../services';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const componentsMap = {
  '1': (params: any) => {
    return <OpportunityInformation {...params} />;
  },
  '2': (params: any) => {
    return <BusinessConditions {...params} />;
  },
  '3': (params: any) => {
    return <SessionInformation {...params} />;
  },
  '4': (params: any) => {
    return <SampleInformation {...params} />;
  },
  '5': (params: any) => {
    return <Collaborators {...params} />;
  },
  '6': (params: any) => {
    return <BroadcastInformation {...params} />;
  },
  '7': (params: any) => {
    return <OpportunityCloseReason {...params} />;
  },
};

const TabsComponents = (props: IProps) => {
  const { detailData, onRefresh } = props;

  const [activeKey, setActiveKey] = useState<string>('1');

  const handleChangeKey = (value: any) => {
    setActiveKey(value);
  };

  const CurrentComponent = useMemo(() => {
    return componentsMap[activeKey as keyof typeof componentsMap];
  }, [activeKey]);

  return (
    <div className={styles['tabs-components']}>
      <div style={{ padding: '0 16px' }}>
        <Tabs activeKey={activeKey} onChange={handleChangeKey}>
          <Tabs.TabPane tab="商机信息" key="1"></Tabs.TabPane>
          <Tabs.TabPane tab="商务条件" key="2"></Tabs.TabPane>
          <Tabs.TabPane tab="场次信息" key="3"></Tabs.TabPane>
          <Tabs.TabPane tab="样品信息" key="4"></Tabs.TabPane>
          <Tabs.TabPane tab="合作主体" key="5"></Tabs.TabPane>
          <Tabs.TabPane tab="上播信息" key="6"></Tabs.TabPane>
          <Tabs.TabPane tab="商机关闭原因" key="7"></Tabs.TabPane>
        </Tabs>
      </div>
      <div>
        {CurrentComponent &&
          CurrentComponent({ detailData: detailData || {}, onRefresh: onRefresh })}
      </div>
    </div>
  );
};

export default TabsComponents;
