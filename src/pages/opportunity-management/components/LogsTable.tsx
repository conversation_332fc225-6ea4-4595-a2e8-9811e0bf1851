import React, { useEffect, useState } from 'react';
import usePagination from '@/hooks/usePagination';
import { Table, message } from 'antd';
import moment from 'moment';
import PaginationProxy from '@/common/constants/Pagination';
import { queryOperationLogList } from '../services';
import { useRequest } from 'ahooks';
import { ReviewTaskStatus, ReviewTaskStatusName } from '../types';

interface IProps {
  detailData: any;
}

const LogsTable = (props: IProps) => {
  const { detailData } = props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const { pagination, setPagination } = usePagination({
    current: 1,
    size: 5,
  });

  const { run, loading } = useRequest(queryOperationLogList, {
    manual: true,
    onSuccess({ res }) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      setDataSource(res?.result?.records || []);
      setPagination({
        total: res?.result?.total || 0,
      });
    },
  });

  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 50,
    },
    {
      title: '操作类型',
      key: 'secondBizType',
      dataIndex: 'secondBizType',
      render: (_: ReviewTaskStatus) => {
        return ReviewTaskStatusName[_] || '-';
      },
      width: 150,
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      render: (t: string) => t || '-',
      width: 150,
    },
    {
      title: '操作时间',
      dataIndex: 'operateTime',
      key: 'operateTime',
      width: 150,
      render: (t: string) => moment(t).format('YYYY-MM-DD HH:mm:ss') || '-',
    },
  ];

  const onPageChange = (current: number, size: number) => {
    setPagination({ current, size, total: pagination.total });
    run({
      bizOrderNo: detailData?.no,
      bizOrderType: 'USER_BUSINESS_OPPORTUNITY',
      current: current,
      size: size,
    });
  };

  useEffect(() => {
    if (detailData?.no) {
      run({
        bizOrderNo: detailData?.no,
        bizOrderType: 'USER_BUSINESS_OPPORTUNITY',
        current: pagination?.current,
        size: pagination?.size,
      });
    }
  }, [detailData?.no]);

  return (
    <div style={{ padding: '0 16px' }}>
      <Table
        rowKey={'id'}
        columns={columns as any}
        dataSource={dataSource}
        pagination={false}
        scroll={{ y: 300 }}
        loading={loading}
      />
      <div style={{ display: 'flex', justifyContent: 'flex-end', paddingRight: '4px' }}>
        <PaginationProxy
          {...pagination}
          onChange={({ current, size }: any) => {
            // console.log('🚀 ~ LogsTable ~ current:', current);
            onPageChange(current, size);
          }}
          valueType="merge"
        />
      </div>
    </div>
  );
};

export default LogsTable;
