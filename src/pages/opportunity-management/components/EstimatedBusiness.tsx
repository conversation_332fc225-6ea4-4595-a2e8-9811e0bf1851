import React, { useEffect } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Modal, Form, InputNumber, message, DatePicker, Select, Input } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import FileUploadSouceId from '@/components/FileUploadSouceId';
import { useRequest } from 'ahooks';
import style from '@/styles/index.module.less';
import moment from 'moment';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { businessOpportunityUpdateEstimatedConditions } from '../services';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const EstimatedBusiness = (props: IProps) => {
  const { form, visible, onRefresh, detailData, ...rest } = props;
  const { getFieldDecorator } = form;

  const { run: updateEstimatedConditionsRun, loading: updateEstimatedConditionsLoading } =
    useRequest(businessOpportunityUpdateEstimatedConditions, {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('更新成功');
        rest?.onCancel?.();
        onRefresh?.();
      },
    });

  const { codeList: COOPERATION_MODE_LIST } = useCode(CODE_ENUM.COOPERATION_MODE, { able: true });
  const { codeList: COMMISSION_RANGE_LIST } = useCode(CODE_ENUM.COMMISSION_RANGE, { able: true });
  const { codeList: BRAND_FEE_RANGE_LIST } = useCode(CODE_ENUM.BRAND_FEE_RANGE, { able: true });

  const handleOnOk = () => {
    form.validateFields((err, values) => {
      if (err) return;
      const {
        estimatedCooperationMode,
        estimatedCommissionRange,
        estimatedBrandFeeRange,
        ...otherValue
      } = values;
      updateEstimatedConditionsRun({
        ...otherValue,
        estimatedCooperationMode: estimatedCooperationMode?.key,
        estimatedCommissionRange: estimatedCommissionRange?.key,
        estimatedBrandFeeRange: estimatedBrandFeeRange?.key,
        id: detailData?.id,
        version: detailData?.version,
      });
    });
  };

  useEffect(() => {
    if (visible && detailData) {
      form.setFieldsValue({
        estimatedCooperationMode: {
          key: detailData?.estimatedCooperationMode,
          label: detailData?.estimatedCooperationModeDesc,
        },
        estimatedCommissionRange: {
          key: detailData?.estimatedCommissionRange,
          label: detailData?.estimatedCommissionRangeDesc,
        },
        estimatedBrandFeeRange: {
          key: detailData?.estimatedBrandFeeRange,
          label: detailData?.estimatedBrandFeeRangeDesc,
        },
        estimatedDescription: detailData?.estimatedDescription,
      });
    }
  }, [visible, detailData]);

  return (
    <Modal
      title="预估商务条件"
      {...rest}
      visible={visible}
      confirmLoading={updateEstimatedConditionsLoading}
      onOk={handleOnOk}
      width={500}
      className={style['modal-sty']}
      maskClosable={false}
    >
      <p>确认商机需要填写以下信息:</p>
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} label="合作模式">
          {getFieldDecorator('estimatedCooperationMode', {
            rules: [
              {
                required: true,
                message: '请选择合作模式',
              },
            ],
          })(
            <Select
              style={{ width: '100%' }}
              allowClear
              placeholder="请选择合作模式"
              labelInValue={true}
            >
              {COOPERATION_MODE_LIST?.map((item: any) => (
                <Select.Option key={item?.value} value={item?.value}>
                  {item?.label}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="佣金区间">
          {getFieldDecorator('estimatedCommissionRange', {
            rules: [
              {
                required: true,
                message: '请选择佣金区间',
              },
            ],
          })(
            <Select
              style={{ width: '100%' }}
              allowClear
              placeholder="请选择佣金区间"
              labelInValue={true}
            >
              {COMMISSION_RANGE_LIST?.map((item: any) => (
                <Select.Option key={item?.value} value={item?.value}>
                  {item?.label}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="品牌费区间">
          {getFieldDecorator('estimatedBrandFeeRange', {
            rules: [
              {
                required: true,
                message: '请选择品牌费区间',
              },
            ],
          })(
            <Select
              style={{ width: '100%' }}
              allowClear
              placeholder="请选择品牌费区间"
              labelInValue={true}
            >
              {BRAND_FEE_RANGE_LIST?.map((item: any) => (
                <Select.Option key={item?.value} value={item?.value}>
                  {item?.label}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="说明">
          {getFieldDecorator(
            'estimatedDescription',
            {},
          )(<Input.TextArea maxLength={500} placeholder="请输入说明" rows={4} />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(EstimatedBusiness));
