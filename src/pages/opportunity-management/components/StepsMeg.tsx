import React, { useState, useEffect, useMemo } from 'react';
import styles from '../index.module.less';
import { Tag, Dropdown, Menu, Icon, Button, message, Spin, Modal, Form, Radio } from 'antd';
import { Steps, Descriptions } from './index';
import { BusinessOpportunityDetailResult } from '../services';
import moment from 'moment';
import { STAGE_ENUM } from '../types';
import { AuthWrapper } from 'qmkit';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const StepsMeg = (props: IProps) => {
  const { detailData, id, handleChangeStage, changeStageLoading, form } = props;
  const { getFieldDecorator } = form;

  const [current, setCurrent] = useState('');
  const [fouceCuttent, setFouceCuttent] = useState('');
  const [endVisible, setEndVisible] = useState<boolean>(false);

  const handleLiveDrop = (targetStage: STAGE_ENUM) => {
    handleChangeStage({
      targetStage,
    });
  };

  const currentEndItem = useMemo(() => {
    return [
      {
        // title: (
        //   <Dropdown
        //     trigger={['click']}
        //     overlay={
        //       <Menu>
        //         <Menu.Item
        //           key={STAGE_ENUM.COMPLETED_LIVE}
        //           onClick={() => {
        //             handleLiveDrop(STAGE_ENUM.COMPLETED_LIVE);
        //           }}
        //         >
        //           上播
        //         </Menu.Item>
        //         <Menu.Item
        //           key={STAGE_ENUM.DROP_PRODUCT}
        //           onClick={() => {
        //             handleLiveDrop(STAGE_ENUM.DROP_PRODUCT);
        //           }}
        //         >
        //           掉品
        //         </Menu.Item>
        //       </Menu>
        //     }
        //   >
        //     <p>
        //       结束 <Icon type="down" />
        //     </p>
        //   </Dropdown>
        // ),
        title: '结束',
        key: 'END',
      },
    ];
  }, [detailData?.version, handleLiveDrop]);

  const goOnItem = [
    {
      title: '上播',
      color: '#52C41A',
      key: STAGE_ENUM.COMPLETED_LIVE,
    },
  ];

  const dropsItem = [
    {
      title: '掉品',
      color: '#EE0000',
      key: STAGE_ENUM.DROP_PRODUCT,
    },
  ];

  const stepDataInit = [
    { title: '建联客户', key: STAGE_ENUM.CUSTOMER_CONNECTION },
    { title: '确认商机', key: STAGE_ENUM.OPPORTUNITY_CONFIRMATION },
    { title: '机制谈判', key: STAGE_ENUM.MECHANISM_NEGOTIATION },
    { title: '定上播日期', key: STAGE_ENUM.LIVE_DATE_SCHEDULING },
    { title: '寄样选品', key: STAGE_ENUM.SAMPLE_SELECTION },
    { title: '合同签订', key: STAGE_ENUM.CONTRACT_SIGNING },
    // ...currentEndItem,
  ];

  const stepData = useMemo(() => {
    // return [...stepDataInit, ...currentEndItem];
    const list = [...stepDataInit];
    if (detailData?.stage === STAGE_ENUM.COMPLETED_LIVE) {
      return [...list, ...goOnItem];
    }
    if (detailData?.stage === STAGE_ENUM.DROP_PRODUCT) {
      return [...list, ...dropsItem];
    }
    return [...list, ...currentEndItem];
  }, [detailData?.stage]);

  const handleStepChange = (item: any) => {
    if ([STAGE_ENUM.COMPLETED_LIVE, STAGE_ENUM.DROP_PRODUCT].includes(item.key)) {
      return;
    }
    if (item.key === fouceCuttent) {
      setFouceCuttent('');
      return;
    }
    setFouceCuttent(item.key);
  };

  // 强制改变商机阶段
  const changeStageClick = () => {
    if (fouceCuttent === 'END') {
      console.log('fouceCuttent', fouceCuttent);
      setEndVisible(true);
      return;
    }
    handleChangeStage({
      targetStage: fouceCuttent,
    });
  };

  const handleCancel = () => {
    setEndVisible(false);
  };

  const handleOk = () => {
    form.validateFields((err: any, values: any) => {
      if (err) {
        return;
      }
      handleChangeStage({
        targetStage: values.targetStage,
      });
      setEndVisible(false);
    });
  };

  useEffect(() => {
    if (detailData?.stage) {
      setCurrent(detailData?.stage || '');
    }
  }, [detailData?.stage]);

  return (
    <>
      <div className={styles['title-line']}>
        <div className={styles['title-line-left']}>
          <p className={styles['title-line-name']}>{detailData?.brandName || '-'}</p>
          <span className={styles['title-line-time']}>
            创建时间:
            {detailData?.gmtCreated
              ? moment(detailData?.gmtCreated).format('YYYY-MM-DD HH:mm:ss')
              : '-'}
          </span>
        </div>
        {![STAGE_ENUM.COMPLETED_LIVE, STAGE_ENUM.DROP_PRODUCT].includes(
          detailData?.stage as STAGE_ENUM,
        ) && (
          <AuthWrapper functionName="f_opportunity_management_all_change">
            <Button
              size="small"
              disabled={!fouceCuttent}
              type="primary"
              onClick={changeStageClick}
              loading={changeStageLoading}
            >
              变更阶段
            </Button>
          </AuthWrapper>
        )}
      </div>
      <div className={styles['steps-line']}>
        <Steps
          current={current}
          steps={stepData}
          onChange={handleStepChange}
          fouceCuttent={fouceCuttent}
        />
      </div>
      <div className={styles['detail-line']}>
        <Descriptions column={4}>
          <Descriptions.Item label="跟进人">{detailData?.followerName || '-'}</Descriptions.Item>
          <Descriptions.Item label="建联时间">
            {detailData?.contactDate ? moment(detailData?.contactDate).format('YYYY-MM-DD') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="所属事业部">{detailData?.followerDeptName}</Descriptions.Item>
        </Descriptions>
      </div>
      <Modal
        visible={endVisible}
        title="请选择"
        width={500}
        maskClosable={false}
        onCancel={handleCancel}
        onOk={handleOk}
      >
        <Form>
          <Form.Item label="结束节点" {...formItemLayout}>
            {getFieldDecorator('targetStage', {
              initialValue: '',
              rules: [{ required: true, message: '请选择' }],
            })(
              <Radio.Group>
                <Radio value={STAGE_ENUM.COMPLETED_LIVE}>上播</Radio>
                <Radio value={STAGE_ENUM.DROP_PRODUCT}>掉品</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default Form.create()(StepsMeg);
