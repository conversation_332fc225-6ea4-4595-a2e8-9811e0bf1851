import React, { useEffect } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ModalProps } from 'antd/lib/modal';
import { Form, Modal, Select, message } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import styles from '../index.module.less';
import { useLegal } from '@/pages/certification-audit/hooks';
import { useBtn } from '../hooks';
// import { useRequest } from 'ahooks';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  // cooperationGuaranteedNo: string;
  onRefresh: any;
  type: 'assign' | 'copy';
  [key: string]: any;
}

const Claim = (props: IProps) => {
  const { form, visible, onRefresh, selectedRows, selectedRowKeys, type, ...rest } = props;
  const { getFieldDecorator } = form;
  const { roleList, roleRun, roleLoading, handleRoleChange, handelRoleSearch, handleRoleBlur } =
    useLegal(0);

  const { assignRun, assignLoading, copyRun, copyLoading } = useBtn({
    onRefresh: () => {
      onRefresh();
      rest?.onCancel?.();
    },
  });

  const handleOnOk = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }
      if (type === 'assign') {
        assignRun({ ...value, ids: selectedRowKeys });
      } else {
        copyRun({ ...value, ids: selectedRowKeys });
      }
    });
  };

  useEffect(() => {
    if (visible) {
      roleRun({ current: 1, size: 20, accountState: 0 });
    }
  }, [visible]);
  return (
    <Modal
      title="请选择跟进人"
      {...rest}
      visible={visible}
      onOk={handleOnOk}
      width={500}
      className={styles['modal-sty']}
      maskClosable={false}
      confirmLoading={assignLoading || copyLoading}
    >
      <Form labelAlign="right" labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
        <Form.Item className="mb-16" label={'跟进人'} required>
          {getFieldDecorator('followerId', {
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          })(
            <Select
              loading={roleLoading}
              placeholder="请选择"
              allowClear
              showSearch
              filterOption={false}
              onSearch={handelRoleSearch}
              onChange={handleRoleChange}
              onBlur={handleRoleBlur}
            >
              {roleList?.map((item) => (
                <Select.Option key={item.employeeId} value={item.employeeId}>
                  {item.employeeName}-{item.jobNo}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(Claim));
