import React from 'react';
import { Descriptions, SendSamples } from './index';
import styles from '../index.module.less';
import { Icon } from 'antd';
import DetailTitle from '@/components/DetailTitle';
import { BusinessOpportunityDetailResult } from '../services';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import { AuthWrapper } from 'qmkit';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const SampleInformation = (props: IProps) => {
  const { detailData, onRefresh } = props;
  return (
    <div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>样品信息</span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <SendSamples detailData={detailData} onRefresh={onRefresh}>
                <Icon type="form" />
              </SendSamples>
            </AuthWrapper>
          </div>
        }
      />
      <div style={{ padding: '0 16px' }}>
        <Descriptions column={4}>
          <Descriptions.Item label="是否寄样">
            {isNullOrUndefined(detailData?.sampleSentFlag)
              ? '-'
              : detailData?.sampleSentFlag
              ? '是'
              : '否'}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </div>
  );
};

export default SampleInformation;
