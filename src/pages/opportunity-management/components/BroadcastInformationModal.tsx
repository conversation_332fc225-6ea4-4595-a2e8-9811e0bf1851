import React, { useEffect } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { ModalProps } from 'antd/lib/modal';
import WithToggleModal from '@/components/WithToggleModal';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { Modal, Form, Table, message } from 'antd';
import style from '@/styles/index.module.less';
import { useBroadcastOptions, useRowSelection, useBroadcastTable } from '../hooks';
import PaginationProxy from '@/common/constants/Pagination';
// import { useList, APIKEY } from '@/pages/live-room-manage/hooks/useList';
import {
  SelectionProcessKanbanBusinessOpportunityPageRequest,
  SelectionProcessKanbanBusinessOpportunityPageResult,
  businessOpportunityUpdateLiveInfo,
} from '../services';
import { useRequest } from 'ahooks';
import { FlowStatus_ChoiceList } from '@/pages/selection-flow-board/types';
import { formatString } from '@/pages/selection-flow-board/utils';
import { useList, APIKEY } from '../hooks';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  [key: string]: any;
}

const BroadcastInformationModal = (props: IProps) => {
  const { form, visible, onRefresh, detailData, detailOnRefresh, ...rest } = props;
  const { getFieldDecorator } = form;

  const { run: updateLiveInfoRun, loading: updateLiveInfoLoading } = useRequest(
    businessOpportunityUpdateLiveInfo,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        message.success('操作成功');
        onRefresh();
        detailOnRefresh();
        rest.onCancel();
      },
    },
  );

  const {
    dataSource,
    pagination,
    onPageChange,
    onSearch,
    onRefresh: listOnRefresh,
    loading,
  } = useList<
    SelectionProcessKanbanBusinessOpportunityPageRequest,
    SelectionProcessKanbanBusinessOpportunityPageResult
  >(APIKEY.SELECTION_PROCESS_KANBAN_LIVE_ROOM_LIST);

  const { options } = useBroadcastOptions({ deptId: detailData?.followerDeptId });
  const { rowSelection, selectedRows, selectedRowKeys, clearSelection } = useRowSelection();
  const { columns } = useBroadcastTable({ isBtn: false });

  useEffect(() => {
    if (visible) {
      onSearch({
        businessOpportunityId: detailData?.id,
        status: [FlowStatus_ChoiceList.WAIT_LIVE, FlowStatus_ChoiceList.COMPLETED_LIVE],
        businessOpportunityRelType: 'COMPLETED_LIVE',
      });
    }
  }, [visible]);

  const handleSearch = () => {
    clearSelection();
    form.validateFields((err, values) => {
      const { liveDate, status, spuName, selectionNo, ...otherValues } = values;
      if (liveDate?.[0] && liveDate?.[1]) {
        const [liveDateStartTime, liveDateEndTime] = liveDate;
        otherValues.liveDateStartTime = liveDateStartTime.format('YYYY-MM-DD');
        otherValues.liveDateEndTime = liveDateEndTime.format('YYYY-MM-DD');
      }

      if (!status?.length) {
        otherValues.status = [
          FlowStatus_ChoiceList.WAIT_LIVE,
          FlowStatus_ChoiceList.COMPLETED_LIVE,
        ];
      } else {
        otherValues.status = status;
      }
      onSearch({
        ...otherValues,
        businessOpportunityId: detailData?.id,
        businessOpportunityRelType: 'COMPLETED_LIVE',
        spuName: formatString(spuName),
        selectionNo: formatString(selectionNo),
      });
    });
  };

  const handleReset = () => {
    clearSelection();
    form.resetFields();
    onSearch({
      businessOpportunityId: detailData?.id,
      status: [FlowStatus_ChoiceList.WAIT_LIVE, FlowStatus_ChoiceList.COMPLETED_LIVE],
      businessOpportunityRelType: 'COMPLETED_LIVE',
    });
  };

  const handleOnOk = () => {
    if (selectedRows?.length === 0) {
      message.warning('请选择上播信息');
      return;
    }
    updateLiveInfoRun({
      id: detailData?.id,
      version: detailData?.version,
      selectionIds: selectedRowKeys,
    });
  };

  return (
    <Modal
      title="上播信息"
      visible={visible}
      // onCancel={onCancel}
      confirmLoading={updateLiveInfoLoading}
      width={1000}
      className={style['modal-sty']}
      maskClosable={false}
      {...rest}
      onOk={handleOnOk}
    >
      <div>
        <SearchFormComponent
          form={form}
          loading={false}
          onSearch={handleSearch}
          onReset={handleReset}
          needMore={true}
          rowShowNum={3}
          options={options}
        />
      </div>
      <div>
        <Table
          columns={columns}
          dataSource={dataSource as any}
          rowSelection={rowSelection}
          pagination={false}
          rowKey="id"
          scroll={{ y: 300 }}
          loading={loading}
        />
      </div>
      <div style={{ display: 'flex', justifyContent: 'flex-end', paddingRight: '4px' }}>
        <PaginationProxy
          {...pagination}
          onChange={({ current, size }: any) => {
            clearSelection();
            onPageChange(current, size);
          }}
          valueType="merge"
        />
      </div>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(BroadcastInformationModal));
