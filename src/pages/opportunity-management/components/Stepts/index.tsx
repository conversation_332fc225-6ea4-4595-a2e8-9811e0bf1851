import React from 'react';
import styles from './index.module.less';

export interface Step {
  title: React.ReactNode;
  color?: string;
  key?: string;
  [key: string]: any;
}

export interface StepsProps {
  current?: string;
  steps?: Step[];
  className?: string;
  onChange?: (step: Step) => void;
  fouceCuttent?: string;
}

const Steps: React.FC<StepsProps> = ({
  current = '',
  steps = [],
  className = '',
  onChange,
  fouceCuttent,
}) => {
  const onStepClick = (step: Step) => {
    if (onChange) {
      onChange(step);
    }
  };

  const currentIndex = steps.findIndex((step) => step.key === current);

  return (
    <div className={`${styles.customSteps} ${className}`}>
      {steps.map((step, index) => {
        const isActive = step.key === current;
        const isFouceCuttent = step.key === fouceCuttent;

        const stepClasses = [styles.stepItem];

        if (currentIndex !== -1 && index < currentIndex) {
          stepClasses.push(styles.finished);
        }

        if (isActive) {
          stepClasses.push(styles.active);
        }

        if (isFouceCuttent) {
          stepClasses.push(styles.fouceCuttentActive);
        }

        const stepStyle: React.CSSProperties = {};
        if (isActive && step.color) {
          stepStyle.backgroundColor = step.color;
        }

        return (
          <div
            key={step.key || index}
            className={stepClasses.join(' ')}
            style={stepStyle}
            onClick={() => onStepClick(step)}
          >
            <div className={styles.stepContent}>{step.title}</div>
          </div>
        );
      })}
    </div>
  );
};

export default Steps;
