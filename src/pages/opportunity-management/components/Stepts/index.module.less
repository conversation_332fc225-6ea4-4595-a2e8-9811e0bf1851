//ai生成
.customSteps {
  display: flex;
  align-items: center;
  height: 32px;
}

.stepItem {
  --arrow-width: 15px; /* Defines the width of the arrow head/notch */

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 30px 0 calc(30px + var(--arrow-width));
  color: #666;
  background-color: #f0f2f5;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  min-width: 140px;
  flex: 1;
  cursor: pointer;

  /* Creates a V-notch on the left and a > arrow on the right */
  clip-path: polygon(
    calc(100% - var(--arrow-width)) 0,
    100% 50%,
    calc(100% - var(--arrow-width)) 100%,
    0 100%,
    var(--arrow-width) 50%,
    0 0
  );

  /* Overlap items to connect the arrows, with a 2px gap */
  margin-left: calc(-1 * (var(--arrow-width) - 2px));
}

.stepContent {
  white-space: nowrap;
}

.stepItem:first-child {
  margin-left: 0;
  /* Creates a flat left edge and a > arrow on the right */
  clip-path: polygon(
    calc(100% - var(--arrow-width)) 0,
    100% 50%,
    calc(100% - var(--arrow-width)) 100%,
    0 100%,
    0 0
  );
  padding: 0 30px;
}

.stepItem:last-child {
  /* Creates a V-notch on the left and a flat right edge */
  clip-path: polygon(100% 0, 100% 100%, 0 100%, var(--arrow-width) 50%, 0 0);
  padding: 0 30px 0 calc(30px + var(--arrow-width));
}

/* Finished steps (before current) - light blue background */
.finished {
  background-color: #e9f6ff;
  color: #3296ff;
}

/* Active step - blue background */
.active {
  background-color: #3296ff;
  color: #fff;
}

.fouceCuttentActive {
  background-color: #204eff;
  color: #fff;
}

// 2024-07-26 zhouby -> cursor ai结尾共生成80行代码
