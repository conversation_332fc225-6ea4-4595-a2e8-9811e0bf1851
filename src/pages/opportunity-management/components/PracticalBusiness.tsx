import React, { useEffect } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Modal, Form, InputNumber, message, DatePicker, Select, Input } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import FileUploadSouceId from '@/components/FileUploadSouceId';
import { useRequest } from 'ahooks';
import style from '@/styles/index.module.less';
import moment from 'moment';
import { businessOpportunityUpdateActualConditions } from '../services';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const PracticalBusiness = (props: IProps) => {
  const { form, visible, onRefresh, detailData, ...rest } = props;
  const { getFieldDecorator } = form;

  const { run: updateActualConditionsRun, loading: updateActualConditionsLoading } = useRequest(
    businessOpportunityUpdateActualConditions,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        message.success('更新成功');
        rest?.onCancel?.();
        onRefresh?.();
      },
    },
  );

  const handleOnOk = () => {
    form.validateFields((err, values) => {
      if (err) return;
      const {
        actualCommissionRate,
        actualConfirmedBrandFee,
        actualEstimatedLiveDate,
        ...otherValues
      } = values;
      updateActualConditionsRun({
        ...otherValues,
        id: detailData?.id,
        version: detailData?.version,
        actualCommissionRate: isNullOrUndefined(actualCommissionRate)
          ? '-'
          : (actualCommissionRate / 100).toFixed(4),
        actualConfirmedBrandFee,
        actualEstimatedLiveDate: actualEstimatedLiveDate
          ? moment(actualEstimatedLiveDate).format('YYYY-MM-DD')
          : undefined,
      });
    });
  };

  useEffect(() => {
    if (visible && detailData) {
      form.setFieldsValue({
        actualMainProduct: detailData?.actualMainProduct,
        actualGiftMechanism: detailData?.actualGiftMechanism,
        actualConfirmedBrandFee: detailData?.actualConfirmedBrandFee,
        actualEstimatedLiveDate: detailData?.actualEstimatedLiveDate
          ? moment(detailData?.actualEstimatedLiveDate)
          : undefined,
        actualDescription: detailData?.actualDescription,
        actualCommissionRate: !isNullOrUndefined(detailData?.actualCommissionRate)
          ? (detailData?.actualCommissionRate * 100).toFixed(2)
          : undefined,
      });
    }
  }, [visible, detailData]);

  return (
    <Modal
      title="实际商务条件"
      {...rest}
      visible={visible}
      confirmLoading={updateActualConditionsLoading}
      onOk={handleOnOk}
      width={500}
      className={style['modal-sty']}
      maskClosable={false}
    >
      <p>机制谈判需要填写以下信息:</p>
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} label="主推品">
          {getFieldDecorator('actualMainProduct', {
            rules: [
              {
                required: true,
                message: '请填写主推品',
              },
            ],
          })(<Input placeholder="请输入主推品" maxLength={50} />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="赠品机制">
          {getFieldDecorator(
            'actualGiftMechanism',
            {},
          )(<Input placeholder="请输入赠品机制" maxLength={200} />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="佣金比例(%)">
          {getFieldDecorator('actualCommissionRate', {
            rules: [
              {
                required: true,
                message: '请填写佣金比例(%)',
              },
            ],
          })(
            <InputNumber
              style={{ width: '100%' }}
              max={100}
              min={0}
              placeholder="请输入佣金比例(%)"
              precision={2}
            />,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="确认品牌费(元)">
          {getFieldDecorator('actualConfirmedBrandFee', {
            rules: [
              {
                required: true,
                message: '请填写确认品牌费',
              },
            ],
          })(
            <InputNumber
              style={{ width: '100%' }}
              max={99999999}
              min={0}
              placeholder="请输入确认品牌费"
              precision={2}
            />,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="预估直播日期">
          {getFieldDecorator('actualEstimatedLiveDate', {
            rules: [
              {
                required: true,
                message: '请选择预估直播日期',
              },
            ],
          })(<DatePicker style={{ width: '100%' }} placeholder="请选择预估直播日期" />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="说明">
          {getFieldDecorator(
            'actualDescription',
            {},
          )(<Input.TextArea maxLength={500} placeholder="请输入说明" rows={4} />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(PracticalBusiness));
