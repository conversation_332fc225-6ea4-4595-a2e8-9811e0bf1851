import React from 'react';
import { Descriptions, EstimatedBusiness, PracticalBusiness } from './index';
import DetailTitle from '@/components/DetailTitle';
import styles from '../index.module.less';
import { Icon } from 'antd';
import { BusinessOpportunityDetailResult } from '../services';
import moment from 'moment';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import { AuthWrapper } from 'qmkit';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const BusinessConditions = (props: IProps) => {
  const { detailData, onRefresh } = props;

  const { codeEnum: COOPERATION_MODE_LIST } = useCode(CODE_ENUM.COOPERATION_MODE);
  const { codeEnum: COMMISSION_RANGE_LIST } = useCode(CODE_ENUM.COMMISSION_RANGE);
  const { codeEnum: BRAND_FEE_RANGE_LIST } = useCode(CODE_ENUM.BRAND_FEE_RANGE);
  return (
    <div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>预估商务条件</span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <EstimatedBusiness onRefresh={onRefresh} detailData={detailData}>
                <Icon type="form" />
              </EstimatedBusiness>
            </AuthWrapper>
          </div>
        }
      />
      <div style={{ padding: '0 16px' }}>
        <Descriptions column={4}>
          <Descriptions.Item label="合作模式">
            {(COOPERATION_MODE_LIST &&
              COOPERATION_MODE_LIST[detailData?.estimatedCooperationMode]) ||
              '-'}
          </Descriptions.Item>
          <Descriptions.Item label="佣金区间">
            {(COMMISSION_RANGE_LIST &&
              COMMISSION_RANGE_LIST[detailData?.estimatedCommissionRange]) ||
              '-'}
          </Descriptions.Item>
          <Descriptions.Item label="品牌费区间">
            {(BRAND_FEE_RANGE_LIST && BRAND_FEE_RANGE_LIST[detailData?.estimatedBrandFeeRange]) ||
              '-'}
          </Descriptions.Item>
          <Descriptions.Item span={4} label="说明">
            {detailData?.estimatedDescription || '-'}
          </Descriptions.Item>
        </Descriptions>
      </div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>实际商务条件</span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <PracticalBusiness onRefresh={onRefresh} detailData={detailData}>
                <Icon type="form" />
              </PracticalBusiness>
            </AuthWrapper>
          </div>
        }
      />
      <div style={{ padding: '0 16px' }}>
        <Descriptions column={4}>
          <Descriptions.Item label="主推品">
            {detailData?.actualMainProduct || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="赠品机制">
            {detailData?.actualGiftMechanism || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="佣金比例(%)">
            {isNullOrUndefined(detailData?.actualCommissionRate)
              ? '-'
              : `${(detailData?.actualCommissionRate * 100).toFixed(2)}%`}
          </Descriptions.Item>
          <Descriptions.Item label="确认品牌费(元)">
            {!isNullOrUndefined(detailData?.actualConfirmedBrandFee)
              ? detailData?.actualConfirmedBrandFee
              : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="预估直播日期">
            {detailData?.actualEstimatedLiveDate
              ? moment(detailData?.actualEstimatedLiveDate).format('YYYY-MM-DD')
              : '-'}
          </Descriptions.Item>
          <Descriptions.Item span={4} label="说明">
            {detailData?.actualDescription || '-'}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </div>
  );
};

export default BusinessConditions;
