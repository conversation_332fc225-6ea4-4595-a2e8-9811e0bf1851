import React, { useEffect } from 'react';
import { Auth<PERSON>rapper } from 'qmkit';
import { SearchForm, Claim } from './index';
import { useTableHeight } from '@/common/constants/hooks/index';
import { Table, Button, message, Modal } from 'antd';
import { useTable, useRowSelection, useTableList, useBtn } from '../hooks';
import style from '@/styles/index.module.less';
import { history } from 'qmkit';
import PaginationProxy from '@/common/constants/Pagination';
import { ImportInterestBox } from './index';
import { useLocation } from 'react-router-dom';

interface IProps {
  [key: string]: any;
}

const AllList = (props: IProps) => {
  const { currentKey } = props;

  const { tableHeight, getHeight } = useTableHeight(140);
  const { columns } = useTable();

  const { selectedRows, selectedRowKeys, clearSelection, rowSelection } = useRowSelection();

  const { dataSource, pagination, loading, onPage<PERSON>hange, onSearch, onRefresh } = useTableList({
    currentKey,
  });

  const {
    releaseRun,
    claimRun,
    releaseLoading,
    claimLoading,
    assignLoading,
    copyLoading,
    deleteRun,
    deleteLoading,
  } = useBtn({
    onRefresh: () => {
      onRefresh();
      clearSelection();
    },
  });

  const beforeClick = (cb: () => void) => {
    if (!selectedRows?.length) {
      message.warning('请选择数据');
      return;
    }
    cb();
  };

  const handleRelease = () => {
    if (!selectedRowKeys?.length) {
      message.warning('请选择数据');
      return;
    }
    releaseRun({
      ids: selectedRowKeys,
    });
  };

  const handleClaim = () => {
    if (!selectedRowKeys?.length) {
      message.warning('请选择数据');
      return;
    }
    claimRun({ ids: selectedRowKeys });
  };

  const handleDelete = () => {
    if (!selectedRowKeys?.length) {
      message.warning('请选择数据');
      return;
    }
    Modal.confirm({
      title: '商机删除后将不可恢复，是否确认删除？',
      // content: '确定删除吗？',
      onOk: () => {
        deleteRun({ ids: selectedRowKeys });
      },
    });
    // deleteRun({ ids: selectedRowKeys });
  };

  useEffect(() => {
    clearSelection();
  }, [currentKey]);

  const location = useLocation();
  useEffect(() => {
    onRefresh();
    clearSelection();
  }, [location]);

  return (
    <AuthWrapper functionName="f_opportunity_management_all_list" showType="page">
      <div
        style={{
          marginTop: '12px',
          height: '100%',
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <div className="formHeight">
          <SearchForm
            onSearch={onSearch}
            getTableHeight={getHeight}
            currentKey={currentKey}
            clearSelection={clearSelection}
          />
          <div className={style.btnGroup} style={{ marginBottom: '12px' }}>
            <AuthWrapper functionName="f_opportunity_management_all_add">
              <Button
                onClick={() => {
                  history.push('/opportunity-management-form');
                }}
                type="primary"
                icon="plus"
              >
                新建
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_opportunity_management_all_release">
              <Button
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={handleRelease}
                loading={releaseLoading}
              >
                释放
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_opportunity_management_all_assignment">
              <Claim
                beforeClick={beforeClick}
                onRefresh={() => {
                  onRefresh();
                  clearSelection();
                }}
                selectedRows={selectedRows}
                selectedRowKeys={selectedRowKeys}
                type="assign"
              >
                <Button
                  className="ml-8"
                  style={{ borderColor: '#999999', color: '#444444' }}
                  loading={assignLoading}
                >
                  分配
                </Button>
              </Claim>
            </AuthWrapper>
            <AuthWrapper functionName="f_opportunity_management_all_claim">
              <Button
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={handleClaim}
                loading={claimLoading}
              >
                认领
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_opportunity_management_all_copy">
              <Claim
                beforeClick={beforeClick}
                type="copy"
                onRefresh={() => {
                  onRefresh();
                  clearSelection();
                }}
                selectedRows={selectedRows}
                selectedRowKeys={selectedRowKeys}
              >
                <Button
                  className="ml-8"
                  style={{ borderColor: '#999999', color: '#444444' }}
                  loading={copyLoading}
                >
                  复制
                </Button>
              </Claim>
            </AuthWrapper>
            <ImportInterestBox onRefresh={onRefresh} />
            <AuthWrapper functionName="f_opportunity_management_all_del">
              <Button
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                loading={deleteLoading}
                onClick={handleDelete}
              >
                删除
              </Button>
            </AuthWrapper>
          </div>
        </div>
        <div style={{ flex: 1 }}>
          <Table
            columns={columns}
            pagination={false}
            dataSource={dataSource}
            rowKey={(record: { [key: string]: any }) => `${record.id}`}
            rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
            scroll={{ y: tableHeight, x: '100%' }}
            loading={loading || releaseLoading || claimLoading || deleteLoading}
            rowSelection={rowSelection}
          />
        </div>
        <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
          {/* @ts-ignore */}
          <PaginationProxy
            {...pagination}
            onChange={({ current, size }: any) => {
              // console.log('🚀 ~ SelectionFlowBoard ~ res:', res);
              clearSelection();
              onPageChange(current, size);
            }}
            valueType="merge"
          />
        </div>
      </div>
    </AuthWrapper>
  );
};

export default AllList;
