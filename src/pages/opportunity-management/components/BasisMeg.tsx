import React from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { Row, Col, Form, Input, Select, DatePicker, Radio } from 'antd';
import { SOURCE_CHANNEL_LIST, OPPORTUNITY_TYPE_LIST } from '../types';
import { FileUploadSouceId } from './index';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { useLegal } from '@/pages/certification-audit/hooks';
import { checkAuth } from 'qmkit';
import { getQueryParams } from 'web-common-modules/utils/params';
import BatchInput from '@/components/BatchInput';

interface IProps {
  form: WrappedFormUtils;
  detailData?: any;
  onRef?: any;
}

const BasisMeg = (props: IProps) => {
  const { form, detailData, onRef } = props;
  const { getFieldDecorator } = form;

  const { codeList: SOURCE_CHANNEL_LIST } = useCode(CODE_ENUM.SOURCE_CHANNEL, { able: true });
  const { codeList: COOPERATION_TYPE_LIST } = useCode(CODE_ENUM.COOPERATION_TYPE, { able: true });

  const { roleList, roleRun, roleLoading, handleRoleChange, handelRoleSearch, handleRoleBlur } =
    useLegal(0);

  const id = getQueryParams()?.id;

  return (
    <div style={{ padding: '0 12px' }}>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="品牌名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('brandName', {
              rules: [
                {
                  required: true,
                  message: '请输入品牌名称',
                },
              ],
            })(<Input maxLength={50} placeholder="请输入品牌名称" />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="联系人" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('contactPerson', {
              rules: [
                {
                  required: true,
                  message: '请输入联系人',
                },
              ],
            })(<Input maxLength={50} placeholder="请输入联系人" />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="联系方式" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {detailData?.follower || !id
              ? getFieldDecorator('contactMobile', {
                  rules: [
                    {
                      required: true,
                      message: '请输入联系方式',
                    },
                  ],
                })(<Input maxLength={50} placeholder="请输入联系方式" />)
              : getFieldDecorator('contactMobile', {
                  rules: [
                    {
                      required: checkAuth('f_opportunity_management_all_see'),
                      message: '请输入联系方式',
                    },
                  ],
                })(
                  checkAuth('f_opportunity_management_all_see') ? (
                    <Input maxLength={50} placeholder="请输入联系方式" />
                  ) : (
                    <span>******</span>
                  ),
                )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="合作主体" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator(
              'cooperationSubjectList',
              {},
            )(<BatchInput label="合作主体" maxLength={200} />)}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="来源渠道" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('sourceChannel', {
              rules: [
                {
                  required: true,
                  message: '请选择来源渠道',
                },
              ],
            })(
              <Select
                allowClear
                placeholder="请选择来源渠道"
                labelInValue={true}
                style={{ width: '100%' }}
              >
                {SOURCE_CHANNEL_LIST.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="合作方式" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('cooperationType', {
              // rules: [{ required: true, message: '请选择合作方式' }],
            })(
              <Select allowClear placeholder="请选择合作方式" labelInValue={true} mode="multiple">
                {COOPERATION_TYPE_LIST?.map((item) => (
                  <Select.Option value={item.value} key={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="建联日期" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('contactDate', {
              rules: [
                {
                  required: true,
                  message: '请选择建联日期',
                },
              ],
            })(<DatePicker style={{ width: '100%' }} />)}
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item label="跟进人" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('follower', {
              // rules: [
              //   {
              //     required: true,
              //     message: '请选择跟进人',
              //   },
              // ],
            })(
              <Select
                loading={roleLoading}
                placeholder="请选择"
                allowClear
                showSearch
                filterOption={false}
                onSearch={handelRoleSearch}
                onChange={handleRoleChange}
                onBlur={handleRoleBlur}
                labelInValue={true}
              >
                {roleList?.map((item) => (
                  <Select.Option key={item.employeeId} value={item.employeeId}>
                    {item.employeeName}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={6}>
          <Form.Item label="商机类型" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
            {getFieldDecorator('businessOpportunityType', {
              rules: [{ required: true, message: '请选择商机类型' }],
            })(
              <Radio.Group>
                {OPPORTUNITY_TYPE_LIST?.map((item) => (
                  <Radio value={item.value} key={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Radio.Group>,
            )}
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24} style={{ marginLeft: '-6px', marginTop: '4px' }}>
          <Form.Item label="附件" labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
            {getFieldDecorator(
              'attachments',
              {},
            )(
              <FileUploadSouceId
                maxLen={20}
                typeCode="SPU_QUALIFICATION"
                isImage
                maxSize={20 * 1024 * 1024}
                multiple
                accept={'.jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx'}
                NoticeText={''}
              />,
            )}
            <p style={{ fontSize: '12px', color: '#999', marginTop: '-2px' }}>
              支持png、jpg、jpeg、xls、xlsx、pdf、doc、docx格式，不超过20MB，最多20张。
            </p>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12} style={{ marginLeft: '-6px' }}>
          <Form.Item label="备注信息" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
            {getFieldDecorator(
              'remark',
              {},
            )(<Input.TextArea maxLength={500} placeholder="请输入备注信息" rows={4} />)}
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default BasisMeg;
