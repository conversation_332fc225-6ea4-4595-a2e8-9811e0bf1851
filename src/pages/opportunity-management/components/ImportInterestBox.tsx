import React, { useState } from 'react';
import { AuthWrapper } from 'qmkit';
import { Modal, Button } from 'antd';
import style from '@/styles/index.module.less';
import { useSetState } from 'ahooks';
import Result from '@/pages/operation-process-board/components/Result';
import { ImportForm } from './index';

interface IProps {
  onRefresh: () => void;
}

const ImportInterestBox = (props: IProps) => {
  const { onRefresh } = props;
  const [visible, setVisble] = useState(false);
  const [importResult, setImportResult] = useSetState<{
    result?: any;
    loading: boolean;
  }>({
    loading: false,
  });
  return (
    <>
      <AuthWrapper functionName="f_opportunity_management_all_import">
        <ImportForm setResVisble={setVisble} setImportResult={setImportResult}>
          <Button className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
            导入
          </Button>
        </ImportForm>
      </AuthWrapper>
      <Modal
        className={style['modal-style']}
        visible={visible}
        title="导入结果"
        onCancel={() => {
          setVisble(false);
          onRefresh && onRefresh();
        }}
        onOk={() => {
          setVisble(false);
          onRefresh && onRefresh();
        }}
      >
        <Result
          successNumber={
            importResult?.result?.successCount ? importResult?.result?.successCount : 0
          }
          fail={importResult?.result?.errorList ? importResult?.result?.errorList : []}
          markedWord="导入"
          errorNo="rowIndex"
          errorReason="errorMessage"
          failedTitle="行数"
        ></Result>
      </Modal>
    </>
  );
};

export default ImportInterestBox;
