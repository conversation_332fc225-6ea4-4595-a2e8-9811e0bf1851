import React from 'react';
import { Descriptions, CollaboratorsModal } from './index';
import styles from '../index.module.less';
import { Icon } from 'antd';
import DetailTitle from '@/components/DetailTitle';
import { BusinessOpportunityDetailResult } from '../services';
import { AuthWrapper } from 'qmkit';

interface IProps {
  detailData: BusinessOpportunityDetailResult;
  [key: string]: any;
}

const Collaborators = (props: IProps) => {
  const { detailData, onRefresh } = props;
  return (
    <div>
      <DetailTitle
        title={
          <div className={styles['opportunity-information-title']}>
            <span style={{ marginRight: '8px' }}>合作主体</span>
            <AuthWrapper functionName="f_opportunity_management_all_edite">
              <CollaboratorsModal detailData={detailData} onRefresh={onRefresh}>
                <Icon type="form" />
              </CollaboratorsModal>
            </AuthWrapper>
          </div>
        }
      />
      <div style={{ padding: '0 16px' }}>
        <Descriptions column={4}>
          <Descriptions.Item label="客户名称">
            {detailData?.contractCooperationSubjectSupplierName || '-'}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </div>
  );
};

export default Collaborators;
