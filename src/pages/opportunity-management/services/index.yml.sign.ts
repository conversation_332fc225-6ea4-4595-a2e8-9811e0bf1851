const signMap = {
  '/iasm/public/projectGroupManagement/projectGroupList': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/page': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/release': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/assign': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/claim': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/copy': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/create': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/updateBaseInfo': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/detail': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/changeStage': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/updateEstimatedConditions': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/updateActualConditions': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunitySessionRel/page': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunitySessionRel/deleteById': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/updateSessionInfo': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/updateSampleInfo': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/updateCooperationSubject': 'basic.x1.xxxx1',
  '/supplier/public/supplier/page': 'basic.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/businessOpportunityPage': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/updateLiveInfo': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/getCloseReason': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/close': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunitySelectionRoundRel/page': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunitySelectionRoundRel/deleteById': 'basic.x1.xxxx1',
  '/tools/public/operation/queryOperationLogList': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/import': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/batchDelete': 'basic.x1.xxxx1',
  '/supplier/public/businessOpportunity/getContact': 'basic.x1.xxxx1',
};

export function getSign(path: string): string {
  return signMap[path];
}
