basic:
  projectGroupList: /iasm/public/projectGroupManagement/projectGroupList
  businessOpportunityPage: /supplier/public/businessOpportunity/page
  businessOpportunityRelease: /supplier/public/businessOpportunity/release
  businessOpportunityAssign: /supplier/public/businessOpportunity/assign
  businessOpportunityClaim: /supplier/public/businessOpportunity/claim
  businessOpportunityCopy: /supplier/public/businessOpportunity/copy
  businessOpportunityCreate: /supplier/public/businessOpportunity/create
  businessOpportunityUpdate: /supplier/public/businessOpportunity/updateBaseInfo
  businessOpportunityDetail: /supplier/public/businessOpportunity/detail
  businessOpportunityChangeStage: /supplier/public/businessOpportunity/changeStage
  businessOpportunityUpdateEstimatedConditions: /supplier/public/businessOpportunity/updateEstimatedConditions
  businessOpportunityUpdateActualConditions: /supplier/public/businessOpportunity/updateActualConditions
  businessOpportunitySessionRelPage: /supplier/public/businessOpportunitySessionRel/page
  businessOpportunitySessionRelDeleteById: /supplier/public/businessOpportunitySessionRel/deleteById
  businessOpportunityUpdateSessionInfo: /supplier/public/businessOpportunity/updateSessionInfo
  businessOpportunityUpdateSampleInfo: /supplier/public/businessOpportunity/updateSampleInfo
  businessOpportunityUpdateCooperationSubject: /supplier/public/businessOpportunity/updateCooperationSubject
  supplierPage: /supplier/public/supplier/page
  selectionProcessKanbanBusinessOpportunityPage: /iasm/public/selectionProcessKanban/businessOpportunityPage
  businessOpportunityUpdateLiveInfo: /supplier/public/businessOpportunity/updateLiveInfo
  businessOpportunityCloseReason: /supplier/public/businessOpportunity/getCloseReason
  businessOpportunityClose: /supplier/public/businessOpportunity/close
  businessOpportunitySelectionRoundRelPage: /supplier/public/businessOpportunitySelectionRoundRel/page
  businessOpportunitySelectionRoundRelDeleteById: /supplier/public/businessOpportunitySelectionRoundRel/deleteById
  queryOperationLogList: /tools/public/operation/queryOperationLogList
  businessOpportunityImport: /supplier/public/businessOpportunity/import
  businessOpportunityBatchDelete: /supplier/public/businessOpportunity/batchDelete
  businessOpportunityGetContact: /supplier/public/businessOpportunity/getContact
