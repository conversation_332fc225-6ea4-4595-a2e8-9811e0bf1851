import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type ProjectGroupListRequest = {
  current?: number /*当前页码,从1开始*/;
  departmentName?: string /*事业部*/;
  deptId?: string /*事业部id*/;
  employId?: string /*员工id*/;
  projectGroupName?: string /*项目组名称*/;
  projectGroupStatusEnum?: 'ENABLE' | 'DISABLE' /*状态[ProjectGroupStatusEnum]*/;
  size?: number /*分页大小*/;
};

export type ProjectGroupListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    departmentId?: string /*事业部id(主键id)*/;
    departmentName?: string /*事业部名称*/;
    employeeInfoList?: Array<{
      employId?: string /*员工id*/;
      employName?: string /*员工姓名*/;
      projectGroupEmployeeTypeEnum?:
        | 'SELECTION_LEADER'
        | 'SELECTION'
        | 'BUSINESS_LEADER'
        | 'BUSINESS' /*员工类型[ProjectGroupEmployeeTypeEnum]*/;
    }> /*项目组员工信息列表*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    platformEnum?:
      | 'DY'
      | 'JD'
      | 'TB'
      | 'PDD'
      | 'KS'
      | 'WECHAT_VIDEO'
      | 'BAIDU'
      | 'RED' /*平台类型[PlatformEnum]*/;
    projectGroupId?: string /*项目组id*/;
    projectGroupName?: string /*项目组名称*/;
    projectGroupStatusEnum?: 'ENABLE' | 'DISABLE' /*状态[ProjectGroupStatusEnum]*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *项目组列表
 */
export const projectGroupList = (params: ProjectGroupListRequest) => {
  return Fetch<ResponseWithResult<ProjectGroupListResult>>(
    '/iasm/public/projectGroupManagement/projectGroupList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/projectGroupManagement/projectGroupList') },
    },
  );
};

export type BusinessOpportunityPageRequest = {
  actualCommissionRateMax?: string /*确认佣金比例最大值*/;
  actualCommissionRateMin?: string /*确认佣金比例最小值*/;
  actualConfirmedBrandFeeMax?: string /*确认品牌费最大值*/;
  actualConfirmedBrandFeeMin?: string /*确认品牌费最小值*/;
  brandName?: string /*品牌名称*/;
  businessOpportunityType?:
    | 'NEW_OPPORTUNITY'
    | 'OLD_OPPORTUNITY' /*商机类型[BusinessOpportunityTypeEnum]*/;
  contactDateEnd?: string /*建联日期结束时间*/;
  contactDateStart?: string /*建联日期开始时间*/;
  contactMobile?: string /*联系方式*/;
  contactPerson?: string /*联系人*/;
  cooperationSubject?: string /*合作主体*/;
  creator?: string /*创建人*/;
  current?: number /*当前页码,从1开始*/;
  estimatedBrandFeeRangeList?: Array<string> /*品牌费区间列表*/;
  estimatedCommissionRangeList?: Array<string> /*佣金区间列表*/;
  follower?: string /*跟进人*/;
  followerDeptIdList?: Array<string> /*所属事业部ID列表*/;
  gmtCreatedEnd?: string /*创建时间结束时间*/;
  gmtCreatedStart?: string /*创建时间开始时间*/;
  projectGroupIdList?: Array<string> /*项目组ID列表*/;
  queryType?:
    | 'ALL'
    | 'PUBLIC_POOL' /*查询类型：ALL-全部，PUBLIC_POOL-公海池[BusinessOpportunityQueryTypeEnum]*/;
  size?: number /*分页大小*/;
  stage?:
    | 'CUSTOMER_CONNECTION'
    | 'OPPORTUNITY_CONFIRMATION'
    | 'MECHANISM_NEGOTIATION'
    | 'LIVE_DATE_SCHEDULING'
    | 'SAMPLE_SELECTION'
    | 'CONTRACT_SIGNING'
    | 'COMPLETED_LIVE'
    | 'DROP_PRODUCT' /*商机阶段[BusinessOpportunityStageEnum]*/;
  stageList?: Array<
    | 'CUSTOMER_CONNECTION'
    | 'OPPORTUNITY_CONFIRMATION'
    | 'MECHANISM_NEGOTIATION'
    | 'LIVE_DATE_SCHEDULING'
    | 'SAMPLE_SELECTION'
    | 'CONTRACT_SIGNING'
    | 'COMPLETED_LIVE'
    | 'DROP_PRODUCT'
  > /*商机阶段列表[BusinessOpportunityStageEnum]*/;
};

export type BusinessOpportunityPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    actualCommissionRate?: string /*佣金比例（%）*/;
    actualConfirmedBrandFee?: string /*确认品牌费*/;
    actualDescription?: string /*实际商务条件说明*/;
    actualEstimatedLiveDate?: string /*预估直播日期*/;
    actualGiftMechanism?: string /*赠品机制*/;
    actualMainProduct?: string /*主推品*/;
    attachments?: Array<string> /*附件*/;
    brandName?: string /*品牌*/;
    businessOpportunityType?:
      | 'NEW_OPPORTUNITY'
      | 'OLD_OPPORTUNITY' /*商机类型[BusinessOpportunityTypeEnum]*/;
    closeDescription?: string /*商机关闭说明*/;
    closeReason?: string /*商机关闭原因*/;
    contactDate?: string /*建联日期*/;
    contactMobile?: string /*联系方式*/;
    contactPerson?: string /*联系人*/;
    contractCooperationSubjectSupplierId?: string /*合同签订合作主体ID*/;
    cooperationSubject?: string /*合作主体*/;
    creator?: string /*创建人*/;
    creatorName?: string /*创建人姓名*/;
    delFlag?: boolean /*删除标识：0-未删除，1-已删除*/;
    estimatedBrandFeeRange?: string /*品牌费区间*/;
    estimatedBrandFeeRangeDesc?: string /*品牌费区间描述*/;
    estimatedCommissionRange?: string /*佣金区间*/;
    estimatedCommissionRangeDesc?: string /*佣金区间描述*/;
    estimatedCooperationMode?: string /*合作模式*/;
    estimatedCooperationModeDesc?: string /*合作模式描述*/;
    estimatedDescription?: string /*预估商务条件说明*/;
    follower?: string /*跟进人*/;
    followerDeptId?: string /*跟进人所属部门ID*/;
    followerDeptName?: string /*跟进人所属部门名称*/;
    followerName?: string /*跟进人姓名*/;
    followerProjectGroupId?: string /*跟进人项目组ID*/;
    followerProjectGroupName?: string /*跟进人项目组名称*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*修改时间*/;
    id?: string /*主键ID*/;
    lastFollowerChangeTime?: string /*最近跟进人变更时间*/;
    modifier?: string /*修改人*/;
    modifierName?: string /*修改人姓名*/;
    no?: string /*商机编号*/;
    remark?: string /*备注信息*/;
    sampleSentFlag?: boolean /*是否寄样：0-否，1-是*/;
    sourceChannel?: string /*来源渠道*/;
    sourceChannelDesc?: string /*来源渠道描述*/;
    stage?:
      | 'CUSTOMER_CONNECTION'
      | 'OPPORTUNITY_CONFIRMATION'
      | 'MECHANISM_NEGOTIATION'
      | 'LIVE_DATE_SCHEDULING'
      | 'SAMPLE_SELECTION'
      | 'CONTRACT_SIGNING'
      | 'COMPLETED_LIVE'
      | 'DROP_PRODUCT' /*商机阶段[BusinessOpportunityStageEnum]*/;
    version?: number /*版本号*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *商机分页查询
 */
export const businessOpportunityPage = (params: BusinessOpportunityPageRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityPageResult>>(
    '/supplier/public/businessOpportunity/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/page') },
    },
  );
};

export type BusinessOpportunityReleaseRequest = {
  ids?: Array<string> /*商机ID*/;
};

export type BusinessOpportunityReleaseResult = boolean;

/**
 *释放商机
 */
export const businessOpportunityRelease = (params: BusinessOpportunityReleaseRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityReleaseResult>>(
    '/supplier/public/businessOpportunity/release',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/release') },
    },
  );
};

export type BusinessOpportunityAssignRequest = {
  followerId?: string /*跟进人ID*/;
  ids?: Array<string> /*商机ID*/;
};

export type BusinessOpportunityAssignResult = boolean;

/**
 *分配商机
 */
export const businessOpportunityAssign = (params: BusinessOpportunityAssignRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityAssignResult>>(
    '/supplier/public/businessOpportunity/assign',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/assign') },
    },
  );
};

export type BusinessOpportunityClaimRequest = {
  ids?: Array<string> /*商机ID*/;
};

export type BusinessOpportunityClaimResult = boolean;

/**
 *认领商机
 */
export const businessOpportunityClaim = (params: BusinessOpportunityClaimRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityClaimResult>>(
    '/supplier/public/businessOpportunity/claim',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/claim') },
    },
  );
};

export type BusinessOpportunityCopyRequest = {
  followerId?: string /*跟进人ID*/;
  ids?: Array<string> /*商机ID*/;
};

export type BusinessOpportunityCopyResult = string;

/**
 *复制商机
 */
export const businessOpportunityCopy = (params: BusinessOpportunityCopyRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityCopyResult>>(
    '/supplier/public/businessOpportunity/copy',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/copy') },
    },
  );
};

export type BusinessOpportunityCreateRequest = {
  attachments?: Array<string> /*附件*/;
  brandName?: string /*品牌*/;
  businessOpportunityType?:
    | 'NEW_OPPORTUNITY'
    | 'OLD_OPPORTUNITY' /*商机类型[BusinessOpportunityTypeEnum]*/;
  contactDate?: string /*建联日期*/;
  contactMobile?: string /*联系方式*/;
  contactPerson?: string /*联系人*/;
  cooperationSubject?: string /*合作主体*/;
  follower?: string /*跟进人*/;
  remark?: string /*备注信息*/;
  sourceChannel?: string /*来源渠道*/;
};

export type BusinessOpportunityCreateResult = string;

/**
 *新建商机
 */
export const businessOpportunityCreate = (params: BusinessOpportunityCreateRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityCreateResult>>(
    '/supplier/public/businessOpportunity/create',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/create') },
    },
  );
};

export type BusinessOpportunityUpdateRequest = {
  attachments?: Array<string> /*附件*/;
  brandName?: string /*品牌*/;
  businessOpportunityType?:
    | 'NEW_OPPORTUNITY'
    | 'OLD_OPPORTUNITY' /*商机类型[BusinessOpportunityTypeEnum]*/;
  contactDate?: string /*建联日期*/;
  contactMobile?: string /*联系方式*/;
  contactPerson?: string /*联系人*/;
  cooperationSubject?: string /*合作主体*/;
  follower?: string /*跟进人*/;
  id?: string /*商机ID*/;
  remark?: string /*备注信息*/;
  sourceChannel?: string /*来源渠道*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityUpdateResult = boolean;

/**
 *编辑商机基本信息
 */
export const businessOpportunityUpdate = (params: BusinessOpportunityUpdateRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityUpdateResult>>(
    '/supplier/public/businessOpportunity/updateBaseInfo',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/updateBaseInfo') },
    },
  );
};

export type BusinessOpportunityDetailRequest = {
  id?: string /*业务ID*/;
};

export type BusinessOpportunityDetailResult = {
  actualCommissionRate?: string /*佣金比例（%）*/;
  actualConfirmedBrandFee?: string /*确认品牌费*/;
  actualDescription?: string /*实际商务条件说明*/;
  actualEstimatedLiveDate?: string /*预估直播日期*/;
  actualGiftMechanism?: string /*赠品机制*/;
  actualMainProduct?: string /*主推品*/;
  attachments?: Array<string> /*附件*/;
  brandName?: string /*品牌*/;
  businessOpportunityType?:
    | 'NEW_OPPORTUNITY'
    | 'OLD_OPPORTUNITY' /*商机类型[BusinessOpportunityTypeEnum]*/;
  closeDescription?: string /*商机关闭说明*/;
  closeReason?: string /*商机关闭原因*/;
  contactDate?: string /*建联日期*/;
  contactMobile?: string /*联系方式*/;
  contactPerson?: string /*联系人*/;
  contractCooperationSubjectSupplierId?: string /*合同签订合作主体ID*/;
  contractCooperationSubjectSupplierName?: string /*合同签订合作主体名称*/;
  cooperationSubject?: string /*合作主体*/;
  creator?: string /*创建人*/;
  creatorName?: string /*创建人姓名*/;
  delFlag?: boolean /*删除标识：0-未删除，1-已删除*/;
  estimatedBrandFeeRange?: string /*品牌费区间*/;
  estimatedBrandFeeRangeDesc?: string /*品牌费区间描述*/;
  estimatedCommissionRange?: string /*佣金区间*/;
  estimatedCommissionRangeDesc?: string /*佣金区间描述*/;
  estimatedCooperationMode?: string /*合作模式*/;
  estimatedCooperationModeDesc?: string /*合作模式描述*/;
  estimatedDescription?: string /*预估商务条件说明*/;
  follower?: string /*跟进人*/;
  followerDeptId?: string /*跟进人所属部门ID*/;
  followerDeptName?: string /*跟进人所属部门名称*/;
  followerName?: string /*跟进人姓名*/;
  followerProjectGroupId?: string /*跟进人项目组ID*/;
  followerProjectGroupName?: string /*跟进人项目组名称*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*修改时间*/;
  id?: string /*主键ID*/;
  lastFollowerChangeTime?: string /*最近跟进人变更时间*/;
  modifier?: string /*修改人*/;
  modifierName?: string /*修改人姓名*/;
  no?: string /*商机编号*/;
  remark?: string /*备注信息*/;
  sampleSentFlag?: boolean /*是否寄样：0-否，1-是*/;
  sourceChannel?: string /*来源渠道*/;
  sourceChannelDesc?: string /*来源渠道*/;
  stage?:
    | 'CUSTOMER_CONNECTION'
    | 'OPPORTUNITY_CONFIRMATION'
    | 'MECHANISM_NEGOTIATION'
    | 'LIVE_DATE_SCHEDULING'
    | 'SAMPLE_SELECTION'
    | 'CONTRACT_SIGNING'
    | 'COMPLETED_LIVE'
    | 'DROP_PRODUCT' /*商机阶段[BusinessOpportunityStageEnum]*/;
  version?: number /*版本号*/;
};

/**
 *商机详情
 */
export const businessOpportunityDetail = (params: BusinessOpportunityDetailRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityDetailResult>>(
    '/supplier/public/businessOpportunity/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/detail') },
    },
  );
};

export type BusinessOpportunityChangeStageRequest = {
  id?: string /*商机ID*/;
  targetStage?:
    | 'CUSTOMER_CONNECTION'
    | 'OPPORTUNITY_CONFIRMATION'
    | 'MECHANISM_NEGOTIATION'
    | 'LIVE_DATE_SCHEDULING'
    | 'SAMPLE_SELECTION'
    | 'CONTRACT_SIGNING'
    | 'COMPLETED_LIVE'
    | 'DROP_PRODUCT' /*目标阶段[BusinessOpportunityStageEnum]*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityChangeStageResult = boolean;

/**
 *强制变更商机阶段
 */
export const businessOpportunityChangeStage = (params: BusinessOpportunityChangeStageRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityChangeStageResult>>(
    '/supplier/public/businessOpportunity/changeStage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/changeStage') },
    },
  );
};

export type BusinessOpportunityUpdateEstimatedConditionsRequest = {
  estimatedBrandFeeRange?: string /*品牌费区间*/;
  estimatedCommissionRange?: string /*佣金区间*/;
  estimatedCooperationMode?: string /*合作模式*/;
  estimatedDescription?: string /*说明*/;
  id?: string /*商机ID*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityUpdateEstimatedConditionsResult = boolean;

/**
 *编辑预估商务条件
 */
export const businessOpportunityUpdateEstimatedConditions = (
  params: BusinessOpportunityUpdateEstimatedConditionsRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunityUpdateEstimatedConditionsResult>>(
    '/supplier/public/businessOpportunity/updateEstimatedConditions',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/supplier/public/businessOpportunity/updateEstimatedConditions'),
      },
    },
  );
};

export type BusinessOpportunityUpdateActualConditionsRequest = {
  actualCommissionRate?: string /*佣金比例（%）*/;
  actualConfirmedBrandFee?: string /*确认品牌费*/;
  actualDescription?: string /*说明*/;
  actualEstimatedLiveDate?: string /*预估直播日期*/;
  actualGiftMechanism?: string /*赠品机制*/;
  actualMainProduct?: string /*主推品*/;
  id?: string /*商机ID*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityUpdateActualConditionsResult = boolean;

/**
 *编辑实际商务条件
 */
export const businessOpportunityUpdateActualConditions = (
  params: BusinessOpportunityUpdateActualConditionsRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunityUpdateActualConditionsResult>>(
    '/supplier/public/businessOpportunity/updateActualConditions',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/supplier/public/businessOpportunity/updateActualConditions'),
      },
    },
  );
};

export type BusinessOpportunitySessionRelPageRequest = {
  businessOpportunityId?: string /*商机ID*/;
  current?: number /*当前页码,从1开始*/;
  size?: number /*分页大小*/;
};

export type BusinessOpportunitySessionRelPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    businessOpportunityId?: string /*商机ID*/;
    creator?: string /*创建人*/;
    creatorName?: string /*创建人姓名*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*修改时间*/;
    groupId?: string /*项目组ID*/;
    groupName?: string /*项目组名称*/;
    id?: string /*主键ID*/;
    liveDate?: string /*直播日期*/;
    liveRoomId?: string /*直播间ID*/;
    liveRoomName?: string /*直播间名称*/;
    liveSessionTimes?: string /*直播时段*/;
    liveSessionTimesDesc?: string /*直播时段描述*/;
    luoFlag?: boolean /*是否罗场：0-否，1-是*/;
    modifier?: string /*修改人*/;
    modifierName?: string /*修改人姓名*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询场次信息
 */
export const businessOpportunitySessionRelPage = (
  params: BusinessOpportunitySessionRelPageRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunitySessionRelPageResult>>(
    '/supplier/public/businessOpportunitySessionRel/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunitySessionRel/page') },
    },
  );
};

export type BusinessOpportunitySessionRelDeleteByIdRequest = {
  id?: string /*业务ID*/;
};

export type BusinessOpportunitySessionRelDeleteByIdResult = any;

/**
 *删除场次信息
 */
export const businessOpportunitySessionRelDeleteById = (
  params: BusinessOpportunitySessionRelDeleteByIdRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunitySessionRelDeleteByIdResult>>(
    '/supplier/public/businessOpportunitySessionRel/deleteById',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunitySessionRel/deleteById') },
    },
  );
};

export type BusinessOpportunityUpdateSessionInfoRequest = {
  id?: string /*商机ID*/;
  liveDates?: Array<string> /*直播日期列表*/;
  liveRoomIds?: Array<string> /*直播间ID列表*/;
  liveSessionTimesList?: Array<
    'MORNING' | 'AFTERNOON' | 'EVENING'
  > /*直播时段列表，支持多选，MORNING：早场；AFTERNOON：中场；EVENING：晚场[LiveSessionTimeEnum]*/;
  luoFlag?: boolean /*是否罗场*/;
  projectGroupId?: string /*项目组ID*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityUpdateSessionInfoResult = boolean;

/**
 *编辑场次信息
 */
export const businessOpportunityUpdateSessionInfo = (
  params: BusinessOpportunityUpdateSessionInfoRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunityUpdateSessionInfoResult>>(
    '/supplier/public/businessOpportunity/updateSessionInfo',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/updateSessionInfo') },
    },
  );
};

export type BusinessOpportunityUpdateSampleInfoRequest = {
  id?: string /*商机ID*/;
  sampleFlag?: boolean /*是否选择寄样*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityUpdateSampleInfoResult = boolean;

/**
 *编辑样品信息
 */
export const businessOpportunityUpdateSampleInfo = (
  params: BusinessOpportunityUpdateSampleInfoRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunityUpdateSampleInfoResult>>(
    '/supplier/public/businessOpportunity/updateSampleInfo',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/updateSampleInfo') },
    },
  );
};

export type BusinessOpportunityUpdateCooperationSubjectRequest = {
  contractCooperationSubjectSupplierId?: string /*合同签订合作主体ID*/;
  id?: string /*商机ID*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityUpdateCooperationSubjectResult = boolean;

/**
 *编辑合作主体
 */
export const businessOpportunityUpdateCooperationSubject = (
  params: BusinessOpportunityUpdateCooperationSubjectRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunityUpdateCooperationSubjectResult>>(
    '/supplier/public/businessOpportunity/updateCooperationSubject',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/supplier/public/businessOpportunity/updateCooperationSubject'),
      },
    },
  );
};

export type SupplierPageRequest = {
  archiveStatus?: 'ENABLE' | 'DISABLE' /*档案启用状态[ArchiveStatusEnum]*/;
  auditState?: number /*审核状态 0、待审核 1、已审核 2、审核未通过 3：初审 4：复审 5：法务审核*/;
  bestSignCompanyStatus?:
    | 'WAIT_CHECK'
    | 'CHECKING'
    | 'CHECKED' /*企业认证状态[BestSignCompanyStatusEnum]*/;
  companyName?: string /*主体名称*/;
  companyPlace?: string /*主体所属地*/;
  creator?: string /*创建人ID*/;
  current?: number;
  doudianId?: string /*店铺ID*/;
  doudianShopName?: string /*店铺名称*/;
  employeeName?: string /*负责人*/;
  existEmployeeMobile?: boolean /*是否存在员工手机号*/;
  fullCompanyName?: string /*主体全名*/;
  gmtCreatedEnd?: string /*创建时间截止时间*/;
  gmtCreatedStart?: string /*创建时间开始时间*/;
  guaranteeQuantityWhiteFlag?: number /*是否保量白名单*/;
  invoiceType?: Array<
    | 'VAT_INVOICE'
    | 'VAT_ORDINARY_INVOICE'
    | 'ELECTRONIC_INVOICE'
    | 'VAT_ELECTRONIC_INVOICE'
    | 'NON_TAX_BILL'
    | 'DIGITAL_INVOICE'
    | 'VAT_ELECTRONIC_FULL_DIGITAL_INVOICE'
    | 'VAT_ELECTRONIC_GENERAL_INVOICE'
    | 'VAT_ELECTRONIC_COMMON_INVOICE'
    | 'VAT_ELECTRONIC_SPECIAL_INVOICE'
    | 'NO_INVOICE'
    | 'OCR_VAT_SPECIAL_INVOICE'
    | 'OCR_VAT_COMMON_INVOICE'
    | 'OCR_VAT_ELECTRONIC_SPECIAL_INVOICE'
    | 'OCR_VAT_ELECTRONIC_GENERAL_INVOICE'
    | 'OCR_VAT_ELECTRONIC_TOLL_COMMON_INVOICE'
    | 'OCR_VAT_ELECTRONIC_COMMON_INVOICE'
    | 'OCR_VAT_ELECTRONIC_INVOICE'
    | 'OCR_VAT_ROLL_INVOICE'
    | 'OCR_VEHICLE_TOLL_INVOICE'
    | 'OCR_BUS_TICKET'
    | 'OCR_FLIGHT_ITINERARY'
    | 'OCR_FLIGHT_ITINERARY_CORPUSCLE'
    | 'OCR_TAXI_TICKET'
    | 'OCR_TRAIN_TICKET'
    | 'OCR_TRAIN_TICKET_CORPUSCLE'
    | 'OCR_QUOTA_INVOICE'
    | 'OCR_GENERAL_MACHINE_INVOICE'
    | 'OCR_USED_CAR_PURCHASE_INVOICE'
    | 'OCR_MOTOR_VEHICLE_PURCHASE_INVOICE'
    | 'OCR_PAPER_ELECTRONIC_FULL_INVOICE'
    | 'OCR_NON_TAX_BILL'
    | 'OCR_ELECTRONIC_MEDICAL_NOTE'
    | 'OCR_CUSTOMS_PAYMENT_BILL'
    | 'OCR_CUSTOMS_DECLARATION_IMPORTED_GOODS_FORM'
    | 'OCR_CUSTOMS_DECLARATION_EXPORT_GOODS_FORM'
    | 'OCR_SHIPPING_INVOICE'
    | 'OCR_VAT_ELECTRONIC_FULL_DIGITAL_INVOICE'
    | 'OCR_VAT_ELECTRONIC_FULL_DIGITAL_INVOICE_SPECIFIC'
  > /*发票类型[InvoiceTypeEnum]*/;
  mainBrand?: string /*主营品牌*/;
  merchantType?:
    | 'LIVE_CUSTOMER'
    | 'INNER_CUSTOMER'
    | 'OTHER_CUSTOMER'
    | 'LIVE_SUPPLIER'
    | 'INNER_SUPPLIER'
    | 'OTHER_SUPPLIER' /*客商分类[MerchantCateTypeEnum]*/;
  oaAuditStatus?:
    | 'WAIT_SUBMIT'
    | 'ALREADY_SUBMIT'
    | 'REFUSED'
    | 'PASS' /*oa审核状态[SupplierOaAuditStatusEnum]*/;
  partnerRelFlag?: boolean /*是否关联合作方*/;
  providerType?: number /*供应商类型：0 品牌方 1 经销商 2 生产方*/;
  size?: number;
  storeAccountState?: number /*账号状态*/;
  supplierAccount?: string /*商家账号*/;
  supplierName?: string /*课商名称*/;
  supplierNo?: string /*商家编号*/;
  taxRate?: Array<string> /*税率*/;
  type?: 'CUSTOMER' | 'SUPPLIER' /*客商类型 客户 供应商[MerchantTypeEnum]*/;
};

export type SupplierPageResult = {
  countId?: string;
  current?: string;
  hitCount?: boolean;
  maxLimit?: string;
  optimizeCountSql?: boolean;
  orders?: Array<{
    asc?: boolean;
    column?: string;
  }>;
  pages?: string;
  records?: Array<{
    accountStatus?: number /*账号状态*/;
    addQuotationSwitch?: number /*新建报价单*/;
    addressDetail?: string /*地址*/;
    applyEnterTime?: string /*入驻时间*/;
    applyModificationStatus?:
      | 'INIT'
      | 'PASS'
      | 'REJECT' /*修改申请状态[ApplyModificationStateEnum]*/;
    archiveStatus?: 'ENABLE' | 'DISABLE' /*档案启用状态[ArchiveStatusEnum]*/;
    areaId?: string /*区*/;
    auditReason?: string /*审核原因*/;
    auditState?: number /*审核状态*/;
    authShopCount?: number /*授权店铺数量*/;
    bestSignCompanyStatus?: number /*实名状态*/;
    cityId?: string /*市*/;
    companyInfoId?: string /*公司主体Id*/;
    companyName?: string /*商家主体名称*/;
    companyPlace?: string /*商家主体所在地*/;
    contactAllowShow?: boolean /*联系方式是否允许看*/;
    contactEmail?: string /*邮箱*/;
    contactMobile?: string /*手机号*/;
    contactPerson?: string /*联系人*/;
    contactWxNo?: string /*微信*/;
    createTime?: string /*注册时间*/;
    creator?: string /*创建人*/;
    delFlag?: number /*是否软删除*/;
    employeeProviderRels?: Array<{
      accountState?: number /*状态状态    ENABLE(0,"启用"),
    DISABLE(1,"离职"),
    @Deprecated
    DIMISSION(2,"注销"),*/;
      employeeName?: string /*职工名称*/;
    }> /*负责人*/;
    enterDate?: string /*入驻时间*/;
    gender?: number /*性别*/;
    guaranteeQuantityWhiteFlag?: number /*是否保量白名单*/;
    guaranteeQuantityWhiteStatus?:
      | 'WAIT_SUBMIT'
      | 'ALREADY_SUBMIT'
      | 'REFUSED'
      | 'PASS' /*保量白名单审批状态[GuaranteeQuantityWhiteStatusEnum]*/;
    id?: string /*供应商ID,店铺主键*/;
    inviteInstitution?: string /*邀请机构*/;
    inviterName?: string /*邀请人*/;
    invoiceType?:
      | 'VAT_INVOICE'
      | 'VAT_ORDINARY_INVOICE'
      | 'ELECTRONIC_INVOICE'
      | 'VAT_ELECTRONIC_INVOICE'
      | 'NON_TAX_BILL'
      | 'DIGITAL_INVOICE'
      | 'VAT_ELECTRONIC_FULL_DIGITAL_INVOICE'
      | 'VAT_ELECTRONIC_GENERAL_INVOICE'
      | 'VAT_ELECTRONIC_COMMON_INVOICE'
      | 'VAT_ELECTRONIC_SPECIAL_INVOICE'
      | 'NO_INVOICE'
      | 'OCR_VAT_SPECIAL_INVOICE'
      | 'OCR_VAT_COMMON_INVOICE'
      | 'OCR_VAT_ELECTRONIC_SPECIAL_INVOICE'
      | 'OCR_VAT_ELECTRONIC_GENERAL_INVOICE'
      | 'OCR_VAT_ELECTRONIC_TOLL_COMMON_INVOICE'
      | 'OCR_VAT_ELECTRONIC_COMMON_INVOICE'
      | 'OCR_VAT_ELECTRONIC_INVOICE'
      | 'OCR_VAT_ROLL_INVOICE'
      | 'OCR_VEHICLE_TOLL_INVOICE'
      | 'OCR_BUS_TICKET'
      | 'OCR_FLIGHT_ITINERARY'
      | 'OCR_FLIGHT_ITINERARY_CORPUSCLE'
      | 'OCR_TAXI_TICKET'
      | 'OCR_TRAIN_TICKET'
      | 'OCR_TRAIN_TICKET_CORPUSCLE'
      | 'OCR_QUOTA_INVOICE'
      | 'OCR_GENERAL_MACHINE_INVOICE'
      | 'OCR_USED_CAR_PURCHASE_INVOICE'
      | 'OCR_MOTOR_VEHICLE_PURCHASE_INVOICE'
      | 'OCR_PAPER_ELECTRONIC_FULL_INVOICE'
      | 'OCR_NON_TAX_BILL'
      | 'OCR_ELECTRONIC_MEDICAL_NOTE'
      | 'OCR_CUSTOMS_PAYMENT_BILL'
      | 'OCR_CUSTOMS_DECLARATION_IMPORTED_GOODS_FORM'
      | 'OCR_CUSTOMS_DECLARATION_EXPORT_GOODS_FORM'
      | 'OCR_SHIPPING_INVOICE'
      | 'OCR_VAT_ELECTRONIC_FULL_DIGITAL_INVOICE'
      | 'OCR_VAT_ELECTRONIC_FULL_DIGITAL_INVOICE_SPECIFIC' /*发票类型[InvoiceTypeEnum]*/;
    isDirectedInvitation?: string /*是否是被定向邀请的供应商。YES:是；NO:不是*/;
    isSelfDouDian?: number /*自有抖店商品*/;
    mainCate?: string /*主营类目*/;
    mainCommodityCategories?: string /*主营类目ID*/;
    mainCommodityCategoryList?: Array<string> /*主营类目Id列表*/;
    maskContactEmail?: string /*脱敏-邮箱*/;
    maskContactMobile?: string /*脱敏-手机号*/;
    maskContactWxNo?: string /*脱敏-微信*/;
    merchantType?:
      | 'LIVE_CUSTOMER'
      | 'INNER_CUSTOMER'
      | 'OTHER_CUSTOMER'
      | 'LIVE_SUPPLIER'
      | 'INNER_SUPPLIER'
      | 'OTHER_SUPPLIER' /*客商分类[MerchantCateTypeEnum]*/;
    oaAuditStatus?: string /*审核状态*/;
    operationLog?: string /*审核日志*/;
    providerMainBrand?: string /*商家（供应商）主营品牌*/;
    providerType?: number /*供应商类型：0 品牌方 1 经销商 2 生产方*/;
    provinceId?: string /*省*/;
    storeAccountState?: number /*商家状态*/;
    storeName?: string /*店铺名称*/;
    storeState?: number /*店铺状态 0、开启 1、关店*/;
    storeType?: number /*商家类型*/;
    streetId?: string /*街道*/;
    supplierAccount?: string /*供应商账户*/;
    supplierName?: string /*商家（供应商）名称*/;
    supplierNo?: string /*商家编码*/;
    talentType?: number /*类型*/;
    taxRate?: string /*税率*/;
    tradePriceSwitch?: number /*订单销售价格*/;
    type?: 'CUSTOMER' | 'SUPPLIER' /*客商类型[MerchantTypeEnum]*/;
  }>;
  searchCount?: boolean;
  size?: string;
  total?: string;
};

/**
 *商家列表
 */
export const supplierPage = (params: SupplierPageRequest) => {
  return Fetch<ResponseWithResult<SupplierPageResult>>('/supplier/public/supplier/page', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/supplier/public/supplier/page') },
  });
};

export type SelectionProcessKanbanBusinessOpportunityPageRequest = {
  brandId?: string /*品牌ID*/;
  businessOpportunityId?: string /*商机ID*/;
  businessOpportunityRelType?: string /*关联类型*/;
  current?: number /*当前页码,从1开始*/;
  groupId?: Array<string> /*项目组ID*/;
  ids?: Array<string> /*选品流程ID列表*/;
  liveDateEndTime?: string /*直播结束时间*/;
  liveDateStartTime?: string /*直播开始时间*/;
  liveRoomId?: Array<string> /*直播间ID*/;
  selectionNo?: Array<string> /*选品流程编号*/;
  size?: number /*分页大小*/;
  spuName?: Array<string> /*商品名称*/;
  status?: Array<
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED'
  > /*选品阶段[SelectionRoundStatus]*/;
};

export type SelectionProcessKanbanBusinessOpportunityPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    allowQualityScoreAudit?: boolean /*是否允许质量分OA审核 true 是 false 否*/;
    auditDetailMap?: {
      [key: string]: {
        auditOpinion?: string /*审核意见*/;
        auditState?:
          | 'PASS'
          | 'NO_PASS'
          | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
        bizType?:
          | 'SUPPLIER'
          | 'BRAND'
          | 'GOODS'
          | 'SHOP'
          | 'BP_BRAND'
          | 'BP_GOODS'
          | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
        expirationDate?: string /*法务审核有效期*/;
        isBrandWhitelisted?: boolean /*是否白名单B*/;
        itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
        remark?: string /*备注*/;
      };
    } /*法务审核明细, Key是资质项目版本ID[QualificationBizTypeEnum]*/;
    avgSales?: string /*场均(支付)*/;
    avgSalesForFifteenDays?: string /*场均(T15)*/;
    bestSignCompanyStatus?:
      | 'WAIT_CHECK'
      | 'CHECKING'
      | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
    bpCancelReason?: string /*商务取消原因*/;
    bpId?: string /*商务ID*/;
    bpManagerName?: string /*商务经理*/;
    bpName?: string /*商务组长*/;
    bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[SelectionBpStatus]*/;
    brandFee?: string /*基础服务费，坑位费*/;
    brandFeePlatformServiceRate?: string /*基础服务费*/;
    brandName?: string /*品牌名称*/;
    captainPromotionLink?: {
      captainId?: string;
      creator?: string;
      doudianGoodsId?: string;
      doudianId?: string;
      doudianName?: string;
      effectEndTime?: string;
      effectStartTime?: string;
      errorDetail?: string;
      gmtCreated?: string;
      gmtModified?: string;
      id?: string;
      liveTime?: string;
      modifier?: string;
      operatorName?: string;
      promotionLink?: string;
      promotionLinkId?: string;
      reason?: number;
      sellPrice?: string;
      serviceFeeRate?: string;
      source?: number;
      spuId?: string;
      spuImg?: string;
      spuName?: string;
      spuNo?: string;
      status?: number;
      talentCommissionRate?: string;
      type?: number;
    } /*抖音推广链接*/;
    cateName?: string /*类目*/;
    commissionRate?: string /*线上佣金*/;
    commissionRateOffline?: string /*线下佣金*/;
    complianceAuditor?: string /*合规审核人ID*/;
    complianceAuditorName?: string /*合规审核人名称*/;
    complianceStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    coopFrameworkId?: string /*合作框架id*/;
    coopFrameworkType?: number /*合作框架类型,1保gmv模式-按主体,2保gmv模式-指定品牌*/;
    cooperationGuaranteed?: {
      accountNo?: string /*机构银行账号*/;
      achievedCalculationType?:
        | 'BY_PAYMENT'
        | 'BY_SETTLEMENT' /*达成计算方式(按支付-BY_PAYMENT,按结算-BY_SETTLEMENT)[AchievedCalculationTypeEnum]*/;
      agreedRefundFlag?: number /*是否约定退款*/;
      appointReceiptInstitutionId?: string /*指定收款机构id*/;
      appointReceiptInstitutionName?: string /*指定收款机构名称*/;
      bankName?: string /*机构开户行*/;
      bestSignContractId?: string /*上上签合同ID*/;
      bpId?: string /*商务id*/;
      bpName?: string /*商务名称*/;
      brandFee?: string /*基础佣金（原固定服务费）*/;
      brandFeeLatestPaymentTime?: string /*基础佣金最晚支付日期*/;
      brandIds?: Array<string> /*品牌id*/;
      brandInfo?: string /*品牌信息*/;
      brandInfoDTOS?: Array<{
        brandId?: string /*品牌id*/;
        brandName?: string /*品牌名称*/;
      }> /*品牌信息*/;
      commissionRate?: string /*线上佣金比例*/;
      contractApproveStatus?:
        | 'INIT'
        | 'WAIT_PENDING'
        | 'PENDING'
        | 'PASS'
        | 'REJECTED' /*合同审批状态（WAIT_PENDING：待审批；PENDING：审批中；PASS：已通过；REJECTED：已驳回）[ContractApproveStatusEnum]*/;
      contractInfo?: string /*合同信息*/;
      contractInfoList?: Array<string> /*合同信息*/;
      contractStatus?:
        | 'WAIT_START'
        | 'SIGNING'
        | 'SIGNED'
        | 'WITHDRAWAL' /*合同状态（WAIT_START:待开始；SIGNING:签署中；SIGNED:已签署）[ContractStatusEnum]*/;
      contractType?:
        | 'ON_LINE'
        | 'OFF_LINE' /*合同类型（ON_LINE：线上合同；OFF_LINE：线下合同）[GuaranteedContractTypeEnum]*/;
      coopActualEndTime?: string /*合作实际结束时间*/;
      coopAdvanceEndTime?: string /*合作提前结束时间*/;
      coopEndTime?: string /*合作结束时间*/;
      coopExtendEndTime?: string /*保量延长期*/;
      coopManualEndTime?: string /*合作手动结束时间*/;
      coopStartTime?: string /*合作开始时间*/;
      creator?: string /*创建人*/;
      creatorName?: string /*创建人名称*/;
      delFlag?: number /*删除标记*/;
      deptId?: string /*事业部id(已废弃)*/;
      deptInfo?: string /*事业部信息*/;
      deptInfoDTOS?: Array<{
        deptId?: string /*事业部id*/;
        deptName?: string /*事业部名称*/;
      }> /*事业部信息*/;
      description?: string /*合同描述*/;
      estimatedSettlementGmv?: string /*保量实际GMV(原名：预估结算gmv)*/;
      estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
      finishedGmv?: string /*保量预估GMV(原名：已完成目标gmv（原名：已完成保量gmv）)*/;
      finishedRate?: string /*保量预估GMV比例*/;
      gmtCreated?: string /*创建时间*/;
      gmtModified?: string /*更新时间*/;
      goodsKeyWordInfoDTOS?: Array<{
        brandInfoDTOS?: Array<{
          brandId?: string /*品牌id*/;
          brandName?: string /*品牌名称*/;
        }> /*品牌信息*/;
        goodsKeyWord?: string /*商品关键词*/;
        shopInfoDTOS?: Array<{
          shopId?: string /*店铺id*/;
          shopName?: string /*店铺名称*/;
        }> /*店铺信息*/;
      }> /*商品关键字信息*/;
      goodsKeywordInfo?: string /*商品关键字信息*/;
      guaranteeBrandFeeRate?: string /*保量基础佣金*/;
      guaranteeQuantityId?: string /*保量合作id(已废弃)*/;
      guaranteedGmv?: string /*目标gmv（原保量gmv）*/;
      guaranteedGmvAdvanceAchievedRule?:
        | 'AUTO_FINISH'
        | 'GO_ON' /*目标gmv提前达成计算规则(合作自动终止；乙方继续为甲方提供营销服务，对超出目标GMV部分的销售额，由甲方向乙方支付额外的激励佣金，激励佣金=超出的销售额/（目标GMV/基础佣金总额）)[GuaranteedGmvAdvanceAchievedRuleEnum]*/;
      guaranteedGmvFailAchievedRule?:
        | 'B_REFUNDS_UNUSED_BASIC_COMMISSION_TO_A'
        | 'EXTEND_COOPERATION_PERIOD'
        | 'UNTIL_ACHIEVEMENT_GOAL_EXTEND_COOPERATION_PERIOD' /*目标gmv未能达成计算规则（乙方向甲方退还未消耗的基础佣金；延长合作期限；合作期限延长至目标gmv达成之日）[GuaranteedGmvFailAchievedRuleEnum]*/;
      id?: string /*保量合同id*/;
      institutionId?: string /*机构id（弃用）*/;
      institutionIds?: Array<string> /*机构id集合*/;
      institutionInfoDTOS?: Array<{
        institutionId?: string /*机构id*/;
        institutionName?: string /*机构名称*/;
        institutionOrganizationName?: string /*机构实名主体名称*/;
        institutionRelNameVersion?: string /*机构实名快照版本号*/;
      }> /*机构信息*/;
      institutionName?: string /*机构名称（弃用）*/;
      institutionOrganizationName?: string /*机构实名主体名称（弃用）*/;
      institutionRelNameVersion?: string /*机构实名快照版本号（弃用）*/;
      invoiceContent?:
        | 'MODERN_SERVICE_TECHNOLOGY_SERVICE_FEE'
        | 'MODERN_SERVICE_SERVICE_FEE' /*发票内容[InvoiceContentTypeEnum]*/;
      invoiceRate?: string /*发票税率*/;
      isWhitelist?: boolean /*是否白名单（0：否；1：是）*/;
      liveRoomInfo?: string /*直播间信息*/;
      liveRoomInfos?: Array<string> /*直播间id*/;
      liveRoomPossessType?:
        | 'ALL'
        | 'INCLUDE' /*直播间拥有类型（ALL:全部；INCLUDE：包含)[LiveRoomPossessTypeEnum]*/;
      mediaSigningStatus?:
        | 'SIGNED'
        | 'UNSIGNED' /*新媒体服务协议签署状态(SIGNED：已签署；UNSIGNED：未签署)；已废弃[MediaSigningStatusEnum]*/;
      modifier?: string /*更新人*/;
      modifierName?: string /*更新人名称*/;
      name?: string /*保量合同名称*/;
      no?: string /*保量合同编号*/;
      oaRequestId?: string /*oa请求ID「审批流」*/;
      originalCoopGuaranteedId?: string /*原保量合作记录主键ID*/;
      originalCoopGuaranteedNo?: string /*原保量合作记录编号*/;
      payDurationType?:
        | 'T_PAY'
        | 'T_ZERO'
        | 'T_TWO'
        | 'T_FIFTEEN'
        | 'T_SEVEN'
        | 'BY_SETTLEMENT' /*支付天数（T_PAY:支付时；T+0:支付24h内；T+2:支付48h内）[PayDurationTypeEnum]*/;
      paymentAmountTotal?: string /*累计回款金额*/;
      paymentType?:
        | 'SELF_FUNDED'
        | 'THIRD_PARTY' /*付款方式（自行支付/代付）[ContractPaymentTypeEnum]*/;
      platform?: string /*平台*/;
      relVOS?: Array<{
        brandId?: string /*品牌id*/;
        cooperationGuaranteedId?: string /*保量合作记录主键id*/;
        cooperationGuaranteedNo?: string /*保量合作记录编号*/;
        creator?: string /*创建人*/;
        gmtCreated?: string;
        gmtModified?: string;
        id?: string /*id*/;
        legalName?: string /*供应商主体名称*/;
        liveDate?: string /*直播时间*/;
        liveRoomId?: string /*直播间id*/;
        liveRoomName?: string /*直播间名称*/;
        modifier?: string /*更新人*/;
        openId?: string /*直播间open_id*/;
        platformId?: string /*平台id*/;
        platformSource?: string /*商品来源平台*/;
        platformSpuId?: string /*平台商品ID*/;
        relFlag?: number /*关联标记（是否关联， 0 否  1 是）*/;
        selectionNo?: string /*场次货盘编号*/;
        shopId?: string /*店铺id*/;
        spuName?: string /*spu名称*/;
        spuNo?: string /*spu编号*/;
        supplierId?: string /*商家id*/;
        talentId?: string /*达人id*/;
      }> /*关联关系*/;
      settlementIntervalType?:
        | 'MONTH'
        | 'QUARTER'
        | 'YEAR' /*结算周期[ContractSettlementIntervalTypeEnum]*/;
      shopInfo?: string /*店铺信息*/;
      shopInfoDTOS?: Array<{
        shopId?: string /*店铺id*/;
        shopName?: string /*店铺名称*/;
      }> /*店铺信息*/;
      status?:
        | 'DRAFT'
        | 'WAIT_START'
        | 'IN_COOPERATION'
        | 'FINISHED'
        | 'WITHDRAWAL'
        | 'TERMINATE' /*合作状态（DRAFT：暂存；WAIT_START:待开始；IN_COOPERATION:合作中；FINISHED:已结束）[GuaranteedStatusEnum]*/;
      stimulateCommission?: string /*激励佣金*/;
      stimulateCommissionRate?: string /*激励佣金比例*/;
      supplierCompanyNameInfo?: string /*商家主体名称信息*/;
      supplierCompanyNameInfos?: Array<string> /*商家主体名称信息*/;
      supplierInfo?: string /*商家信息*/;
      supplierInfoDTOS?: Array<{
        contactAddress?: string /*联系地址*/;
        contactMail?: string /*联系邮箱*/;
        contactMobile?: string /*联系电话*/;
        contactName?: string /*联系人*/;
        supplierCompanyName?: string /*商家主体名称*/;
        supplierId?: string /*商家id*/;
      }> /*商家信息*/;
      thirdPartyPayerName?: string /*代付方名称*/;
      thirdPartyPayerUscc?: string /*代付方统一社会信用代码*/;
      type?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'SHOP'
        | 'SHOP_BRAND'
        | 'GOODS_KEYWORD' /*保量类型（SUPPLIER：按商家主体；BRAND：按品牌；SHOP：按店铺；SHOP_BRAND：按店铺品牌；GOODS_KEYWORD：商品关键字）[GuaranteedTypeEnum]*/;
      version?: number /*版本号*/;
    } /*保量信息*/;
    cooperationMode?:
      | 'COLONEL'
      | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
    cooperationOrderId?: string /*合作订单id*/;
    deptId?: string /*事业部ID*/;
    dropProductReason?: string /*掉品原因*/;
    dropProductReasonType?: string /*掉品原因类型*/;
    favorableRate?: string /*好评率*/;
    frameworkCoopModel?: Array<
      'GUARANTEED_GMV_MODE' | 'GUARANTEED_LIVE_ROUND_MODE' | 'GUARANTEED_SLICE_MODE'
    > /*合作模式多选：1保 GMV模式，2保场次排期模式，3前两种多选[CooperationFrameworkCoopModelEnum]*/;
    frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
    frameworkRoundFlag?: boolean /*是否计入年框场次*/;
    giftLogisticsNo?: string /*赠品快递单号*/;
    gmtCreated?: string /*创建时间*/;
    gmtCreatedTime?: string /*流程创建时间*/;
    gmvCommissionRate?: string /*GMV模式会有：年框GMV总分佣比例*/;
    gmvPlatformCommissionRate?: string /*GMV模式会有：年框GMV平台分佣比例*/;
    goodsQualityScore?: string /*商品质量分*/;
    goodsScore?: string /*商品体验分*/;
    groupId?: string /*项目组id*/;
    guaranteeProportionFlag?: boolean /*是否保比*/;
    guaranteeProportionId?: string /*保比id*/;
    guaranteeQuantityFlag?: boolean /*是否保量*/;
    guaranteeQuantityId?: string /*保量id*/;
    hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
    historySumSales?: string /*历史累计(支付)*/;
    historySumSalesForFifteenDays?: string /*历史累计(T15)*/;
    id?: string;
    image?: string /*商品图片*/;
    isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
    isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
    labelList?: string /*标签*/;
    legalAuditor?: string /*法务审核人ID*/;
    legalAuditorName?: string /*法务审核人名称*/;
    legalStatus?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*资质风险[SelectionQualificationRiskLevel]*/;
    linkBak?: string /*商品参考链接*/;
    linkCheckRemark?: string /*链接确认备注*/;
    linkCheckStatus?:
      | 'WAIT_CHECK'
      | 'NO_PROMOTION'
      | 'CAN_PROMOTION' /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
    linkValidityEffectiveTime?: string /*上播链接有效时间*/;
    linkValidityEndTime?: string /*上播链接结束时间*/;
    linkValidityStartTime?: string /*上播链接开始时间*/;
    liveDate?: string /*场次日期*/;
    liveDateTime?: string /*直播日期*/;
    livePlatformSpuId?: string /*上播商品id*/;
    liveRoomId?: string /*直播间id*/;
    liveRoundId?: string /*场次*/;
    liveRoundName?: string /*直播间名称*/;
    liveServiceType?: string /*直播服务类型*/;
    logisticsContent?: {
      deliveryCycle?: string /* 发货周期, 必填，xx天内发货，默认2天*/;
      deliveryMode?: 'SPOT' | 'PRESALE' /* 发货模式, 必填，现货/预售[DeliveryModeEnum]*/;
      giftDeliveryMode?: boolean /* 主品赠品发货情况, 赠品是否随主品一起发货，true：是，false：否*/;
      giftLogisticsNo?: string /* 赠品寄样单号，如果选择 主赠分别发货 显示本字段*/;
      logisticsNo?: string /* 主品寄样单号*/;
    } /*物流信息，json对象*/;
    logisticsNo?: string /*主品快递单号*/;
    logisticsScore?: string /*物流体验分*/;
    lowCommissionAuditStatus?:
      | 'NONE'
      | 'INIT'
      | 'PENDING'
      | 'APPROVED'
      | 'REJECTED'
      | 'CANCELED'
      | 'DELETED'
      | 'REVERTED'
      | 'OVERTIME_CLOSE'
      | 'OVERTIME_RECOVER' /*低佣审核状态[LowCommissionAuditStatusEnum]*/;
    lowCommissionAuditTime?: string /*低佣审核时间*/;
    lowCommissionAuditor?: string /*低佣审核人*/;
    lowCommissionAuditorName?: string /*低佣审核姓名*/;
    lowCommissionFlowNo?: string /*低佣审核流程编号*/;
    luckyProductFlag?: boolean /*是否福袋商品*/;
    luxuryReviewStatus?:
      | 'NO_NEED_REVIEW'
      | 'PENDING_REVIEW'
      | 'REJECTED'
      | 'APPROVED' /*高奢商品资质复查状态[LuxuryReviewStatusEnum]*/;
    maxPrice?: string /*最大价 */;
    mdyConfirmLive?: boolean /*明道云确认上播状态,0:否,1:是*/;
    minPrice?: string /*最小价*/;
    needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
    no?: string /*选品编号*/;
    operatorAuditor?: string /*运营审核人ID*/;
    operatorAuditorName?: string /*运营审核人名称*/;
    operatorStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    paymentCooperationId?: string /*合作订单付款订单id*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentVoucherId?: string /*付款凭证id*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    performanceState?: string /*履约状态*/;
    platformShopName?: string /*店铺名称*/;
    platformSource?:
      | 'DY'
      | 'JD'
      | 'TB'
      | 'PDD'
      | 'KS'
      | 'WECHAT_VIDEO'
      | 'BAIDU'
      | 'RED' /*商品来源`平台`[PlatformEnum]*/;
    platformSpuId?: string /*平台商品id*/;
    platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
    preSaleStock?: number /*预售库存*/;
    projectName?: string /*项目组*/;
    promotionLink?: string /*上播链接*/;
    promotionLinkId?: string /*推广链接id*/;
    qualificationRisk?: string /*资质风险等级*/;
    qualificationSupplementFlag?:
      | 'SUPPLEMENT_WAIT_AUDIT'
      | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
    qualifyInspectionStatus?:
      | 'NEEDLESS_INSPECTION'
      | 'NON_INSPECTION'
      | 'INSPECTING'
      | 'PASSED_INSPECTION'
      | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
    qualityScoreAuditStatus?:
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT' /*质量分审核状态[QualityScoreAuditStatusEnum]*/;
    roundTotalFees?: string /*保场次排期模式会有：场次费用*/;
    saasProductId?: string /*SaaS商品id*/;
    sectionFee?: string /*切片费*/;
    selectionAuditor?: string /*选品负责人ID*/;
    selectionAuditorName?: string /*选品负责人名称*/;
    selectionLabel?: string /*选品标签*/;
    selectionStage?: string /*选品阶段*/;
    selectionStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    sellingPoints?: string /*商品主要卖点*/;
    serviceAgreementId?: string /*服务协议id*/;
    serviceScore?: string /*商家服务分*/;
    shopPoints?: string /*店铺分*/;
    skuPrice?: string /*到手价*/;
    skus?: string /*sku信息，json对象*/;
    soldSalesVolume?: string /*已售销量*/;
    specQualificationVersion?: string /*采买链路资质版本ID, 无论是否采买均绑定一个采买链路版本, 用于品牌类型切换处理*/;
    specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
    specialAuditId?: string /*资质特批Id*/;
    specialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'WITHDRAW' /*资质特批状态[SpecialAuditStatus]*/;
    specialAuditType?: 'ONLINE' | 'OFFLINE' | 'HIGH_SPECIAL' /*特批类型[SpecialAuditTypeEnum]*/;
    spokenType?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
    spuFocus?: string /*重点展示需求*/;
    spuId?: string /*spu id*/;
    spuName?: string /*商品名称*/;
    spuNo?: string /*spu编号*/;
    standardFavorableRate?: string /*标准好评率*/;
    standardGoodsScore?: string /*标准商品体验分*/;
    standardLogisticsScore?: string /*标准物流体验分*/;
    standardServiceScore?: string /*标准商家服务分*/;
    standardStore?: string /*标准店铺分*/;
    status?:
      | 'BP_CONFIRMING'
      | 'WAIT_AUDIT'
      | 'WAIT_LIVE'
      | 'ABORT_LIVE'
      | 'ABORT_WAIT_LIVE'
      | 'COMPLETED_LIVE'
      | 'CANCEL'
      | 'INVITING'
      | 'LOSE_EFFICACY'
      | 'TB_ORDERED' /*选品阶段[SelectionRoundStatus]*/;
    strategyFail?: string /*定向策略失败原因*/;
    suggestCommission?: string /*建议佣金*/;
    supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
    supplierBodySpecialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'INVALID' /*主体特批状态[SupplierBodySpecialAuditStatusEnum]*/;
    supplierId?: string /*商家id*/;
    supplierOperatorId?: string /*商家员工ID*/;
    supplierOrgId?: string /*商家主体快照ID*/;
    supplierOrgName?: string /*商家主体名称*/;
    supplierReason?: string /*商家驳回原因*/;
    supplierStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    totalCommission?: string /*总佣金*/;
    totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
    version?: number /*版本号*/;
    yearFrameBrandFee?: string /*年框品牌费*/;
    yearFrameCommissionRate?: string /*年框佣金*/;
    yearFrameType?: string /*年框履约类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *商机上播信息查询
 */
export const selectionProcessKanbanBusinessOpportunityPage = (
  params: SelectionProcessKanbanBusinessOpportunityPageRequest,
) => {
  return Fetch<ResponseWithResult<SelectionProcessKanbanBusinessOpportunityPageResult>>(
    '/iasm/public/selectionProcessKanban/businessOpportunityPage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/iasm/public/selectionProcessKanban/businessOpportunityPage'),
      },
    },
  );
};

export type BusinessOpportunityUpdateLiveInfoRequest = {
  id?: string /*商机ID*/;
  selectionIds?: Array<string> /*选品ID集合*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityUpdateLiveInfoResult = boolean;

/**
 *编辑上播信息
 */
export const businessOpportunityUpdateLiveInfo = (
  params: BusinessOpportunityUpdateLiveInfoRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunityUpdateLiveInfoResult>>(
    '/supplier/public/businessOpportunity/updateLiveInfo',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/updateLiveInfo') },
    },
  );
};

export type BusinessOpportunityCloseReasonRequest = {
  reasonName?: string /*关闭原因名称（模糊查询，可为空）*/;
};

export type BusinessOpportunityCloseReasonResult = Array<{
  children?: Array<{
    id?: string /*主键ID*/;
    parentReasonId?: string /*父级关闭原因ID*/;
    reasonLevel?: number /*原因层级：1-一级原因，2-二级原因*/;
    reasonName?: string /*关闭原因名称*/;
  }> /*子级原因列表*/;
  id?: string /*主键ID*/;
  parentReasonId?: string /*父级关闭原因ID*/;
  reasonLevel?: number /*原因层级：1-一级原因，2-二级原因*/;
  reasonName?: string /*关闭原因名称*/;
}>;

/**
 *查询商机关闭原因
 */
export const businessOpportunityCloseReason = (params: BusinessOpportunityCloseReasonRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityCloseReasonResult>>(
    '/supplier/public/businessOpportunity/getCloseReason',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/getCloseReason') },
    },
  );
};

export type BusinessOpportunityCloseRequest = {
  closeDescription?: string /*关闭说明*/;
  closeReason?: string /*关闭原因*/;
  id?: string /*商机ID*/;
  selectionIds?: Array<string> /*选品ID集合*/;
  version?: number /*版本号*/;
};

export type BusinessOpportunityCloseResult = boolean;

/**
 *关闭商机
 */
export const businessOpportunityClose = (params: BusinessOpportunityCloseRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityCloseResult>>(
    '/supplier/public/businessOpportunity/close',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/close') },
    },
  );
};

export type BusinessOpportunitySelectionRoundRelPageRequest = {
  businessOpportunityId?: string /*商机ID*/;
  current?: number /*当前页码,从1开始*/;
  size?: number /*分页大小*/;
  type?: 'COMPLETED_LIVE' | 'DROP_PRODUCT' /*类型[BusinessOpportunitySelectionRoundRelTypeEnum]*/;
};

export type BusinessOpportunitySelectionRoundRelPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    allowQualityScoreAudit?: boolean /*是否允许质量分OA审核 true 是 false 否*/;
    avgSales?: string /*场均(支付)*/;
    avgSalesForFifteenDays?: string /*场均(T15)*/;
    bestSignCompanyStatus?: string /*商家认证状态*/;
    bpCancelReason?: string /*商务取消原因*/;
    bpId?: string /*商务ID*/;
    bpManagerName?: string /*商务经理*/;
    bpName?: string /*商务组长*/;
    brandFee?: string /*基础服务费，坑位费*/;
    brandFeePlatformServiceRate?: string /*基础服务费*/;
    brandName?: string /*品牌名称*/;
    businessOpportunityId?: string;
    cateName?: string /*类目*/;
    commissionRate?: string /*线上佣金*/;
    commissionRateOffline?: string /*线下佣金*/;
    complianceAuditor?: string /*合规审核人ID*/;
    complianceAuditorName?: string /*合规审核人名称*/;
    complianceStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    coopFrameworkId?: string /*合作框架id*/;
    coopFrameworkType?: number /*合作框架类型,1保gmv模式-按主体,2保gmv模式-指定品牌*/;
    cooperationMode?: string /*合作模式:COLONEL-团长,DIRECT-定向*/;
    cooperationOrderId?: string /*合作订单id*/;
    creator?: string;
    deptId?: string /*事业部ID*/;
    dropProductReason?: string /*掉品原因*/;
    dropProductReasonType?: string /*掉品原因类型*/;
    favorableRate?: string /*好评率*/;
    frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
    frameworkRoundFlag?: boolean /*是否计入年框场次*/;
    giftLogisticsNo?: string /*赠品快递单号*/;
    gmtCreated?: string;
    gmtCreatedTime?: string /*流程创建时间*/;
    gmtModified?: string;
    gmvCommissionRate?: string /*GMV模式会有：年框GMV总分佣比例*/;
    gmvPlatformCommissionRate?: string /*GMV模式会有：年框GMV平台分佣比例*/;
    goodsQualityScore?: string /*商品质量分*/;
    goodsScore?: string /*商品体验分*/;
    groupId?: string /*项目组id*/;
    guaranteeProportionFlag?: boolean /*是否保比*/;
    guaranteeProportionId?: string /*保比id*/;
    guaranteeQuantityFlag?: boolean /*是否保量*/;
    guaranteeQuantityId?: string /*保量id*/;
    hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
    historySumSales?: string /*历史累计(支付)*/;
    historySumSalesForFifteenDays?: string /*历史累计(T15)*/;
    id?: string;
    image?: string /*商品图片*/;
    isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
    isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
    labelList?: string /*标签*/;
    legalAuditor?: string /*法务审核人ID*/;
    legalAuditorName?: string /*法务审核人名称*/;
    legalStatus?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*资质风险[SelectionQualificationRiskLevel]*/;
    linkBak?: string /*商品参考链接*/;
    linkCheckRemark?: string /*链接确认备注*/;
    linkValidityEffectiveTime?: string /*上播链接有效时间*/;
    linkValidityEndTime?: string /*上播链接结束时间*/;
    linkValidityStartTime?: string /*上播链接开始时间*/;
    liveDate?: string /*场次日期*/;
    liveDateTime?: string /*直播日期*/;
    livePlatformSpuId?: string /*上播商品id*/;
    liveRoomId?: string /*直播间id*/;
    liveRoundId?: string /*场次*/;
    liveRoundName?: string /*直播间名称*/;
    liveServiceType?: string /*直播服务类型*/;
    logisticsNo?: string /*主品快递单号*/;
    logisticsScore?: string /*物流体验分*/;
    lowCommissionAuditTime?: string /*低佣审核时间*/;
    lowCommissionAuditor?: string /*低佣审核人*/;
    lowCommissionAuditorName?: string /*低佣审核姓名*/;
    lowCommissionFlowNo?: string /*低佣审核流程编号*/;
    luckyProductFlag?: boolean /*是否福袋商品*/;
    maxPrice?: string /*最大价 */;
    mdyConfirmLive?: boolean /*明道云确认上播状态,0:否,1:是*/;
    minPrice?: string /*最小价*/;
    modifier?: string;
    needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
    operatorAuditor?: string /*运营审核人ID*/;
    operatorAuditorName?: string /*运营审核人名称*/;
    operatorStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    paymentCooperationId?: string /*合作订单付款订单id*/;
    paymentMethod?: string /*付款方式*/;
    paymentVoucherId?: string /*付款凭证id*/;
    performanceState?: string /*履约状态*/;
    platformShopName?: string /*店铺名称*/;
    platformSource?:
      | 'DY'
      | 'JD'
      | 'TB'
      | 'PDD'
      | 'KS'
      | 'WECHAT_VIDEO'
      | 'BAIDU'
      | 'RED' /*商品来源`平台`[PlatformEnum]*/;
    platformSpuId?: string /*平台商品id*/;
    platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
    preSaleStock?: number /*预售库存*/;
    projectName?: string /*项目组*/;
    promotionLink?: string /*上播链接*/;
    promotionLinkId?: string /*推广链接id*/;
    qualificationRisk?: string /*资质风险等级*/;
    roundTotalFees?: string /*保场次排期模式会有：场次费用*/;
    saasProductId?: string /*SaaS商品id*/;
    sectionFee?: string /*切片费*/;
    selectionAuditor?: string /*选品负责人ID*/;
    selectionAuditorName?: string /*选品负责人名称*/;
    selectionId?: string;
    selectionLabel?: string /*选品标签*/;
    selectionNo?: string;
    selectionStage?: string /*选品阶段*/;
    selectionStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    sellingPoints?: string /*商品主要卖点*/;
    serviceAgreementId?: string /*服务协议id*/;
    serviceScore?: string /*商家服务分*/;
    shopPoints?: string /*店铺分*/;
    skuPrice?: string /*到手价*/;
    skus?: string /*sku信息，json对象*/;
    soldSalesVolume?: string /*已售销量*/;
    specQualificationVersion?: string /*采买链路资质版本ID, 无论是否采买均绑定一个采买链路版本, 用于品牌类型切换处理*/;
    specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
    specialAuditId?: string /*资质特批Id*/;
    spuFocus?: string /*重点展示需求*/;
    spuId?: string /*spu id*/;
    spuName?: string /*商品名称*/;
    spuNo?: string /*spu编号*/;
    standardFavorableRate?: string /*标准好评率*/;
    standardGoodsScore?: string /*标准商品体验分*/;
    standardLogisticsScore?: string /*标准物流体验分*/;
    standardServiceScore?: string /*标准商家服务分*/;
    standardStore?: string /*标准店铺分*/;
    status?:
      | 'BP_CONFIRMING'
      | 'WAIT_AUDIT'
      | 'WAIT_LIVE'
      | 'ABORT_LIVE'
      | 'ABORT_WAIT_LIVE'
      | 'COMPLETED_LIVE'
      | 'CANCEL'
      | 'INVITING'
      | 'LOSE_EFFICACY'
      | 'TB_ORDERED' /*选品阶段[SelectionRoundStatus]*/;
    strategyFail?: string /*定向策略失败原因*/;
    suggestCommission?: string /*建议佣金*/;
    supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
    supplierId?: string /*商家id*/;
    supplierOperatorId?: string /*商家员工ID*/;
    supplierOrgId?: string /*商家主体快照ID*/;
    supplierOrgName?: string /*商家主体名称*/;
    supplierReason?: string /*商家驳回原因*/;
    supplierStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    totalCommission?: string /*总佣金*/;
    totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
    type?: 'COMPLETED_LIVE' | 'DROP_PRODUCT';
    version?: number /*版本号*/;
    yearFrameBrandFee?: string /*年框品牌费*/;
    yearFrameCommissionRate?: string /*年框佣金*/;
    yearFrameType?: string /*年框履约类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询选品流程关联信息
 */
export const businessOpportunitySelectionRoundRelPage = (
  params: BusinessOpportunitySelectionRoundRelPageRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunitySelectionRoundRelPageResult>>(
    '/supplier/public/businessOpportunitySelectionRoundRel/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/supplier/public/businessOpportunitySelectionRoundRel/page'),
      },
    },
  );
};

export type BusinessOpportunitySelectionRoundRelDeleteByIdRequest = {
  id?: string /*业务ID*/;
};

export type BusinessOpportunitySelectionRoundRelDeleteByIdResult = any;

/**
 *删除选品流程关联信息
 */
export const businessOpportunitySelectionRoundRelDeleteById = (
  params: BusinessOpportunitySelectionRoundRelDeleteByIdRequest,
) => {
  return Fetch<ResponseWithResult<BusinessOpportunitySelectionRoundRelDeleteByIdResult>>(
    '/supplier/public/businessOpportunitySelectionRoundRel/deleteById',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/supplier/public/businessOpportunitySelectionRoundRel/deleteById'),
      },
    },
  );
};

export type QueryOperationLogListRequest = {
  bizOrderId?: string /*业务单据id*/;
  bizOrderNo?: string /*业务单据编号*/;
  bizOrderType?: string /*业务单据类型*/;
  bizOrderTypeList?: Array<string> /*业务单据类型*/;
  bizTypeModels?: Array<{
    bizOrderType?: string /*业务单据类型*/;
    secondBizTypes?: Array<string> /*二级业务单据类型*/;
  }> /*一二级业务单据类型集合*/;
  current?: number /*当前页码,从1开始*/;
  employeeIdList?: Array<string> /*用户id*/;
  operateDesc?: string /*操作类型描述*/;
  operateEndTime?: string /*操作结束时间*/;
  operateStartTime?: string /*操作开始时间*/;
  operateType?: string /*操作类型*/;
  operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
  operatorName?: string /*操作人名称*/;
  size?: number /*分页大小*/;
};

export type QueryOperationLogListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    bizOrderId?: string /*业务单据id*/;
    bizOrderNo?: string /*业务单据编号*/;
    bizOrderType?: string /*业务单据类型*/;
    contentSnapshot?: string /*操作之前的快照*/;
    id?: string /*主键*/;
    operateDesc?: string /*操作类型*/;
    operateTime?: string /*操作时间*/;
    operateType?: string /*操作类型*/;
    operatorAccount?: string /*操作人账号*/;
    operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
    operatorContent?: string /*操作内容*/;
    operatorId?: string /*操作人id*/;
    operatorName?: string /*操作人姓名*/;
    secondBizType?: string /*二级业务单据类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询操作日志
 */
export const queryOperationLogList = (params: QueryOperationLogListRequest) => {
  return Fetch<ResponseWithResult<QueryOperationLogListResult>>(
    '/tools/public/operation/queryOperationLogList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/operation/queryOperationLogList') },
    },
  );
};

export type BusinessOpportunityImportRequest = {
  resourceId?: string /*导入资源文件id*/;
};

export type BusinessOpportunityImportResult = {
  errorList?: Array<{
    errorMessage?: string /*错误信息*/;
    rowIndex?: number /*行号*/;
  }> /*失败内容*/;
  failCount?: number /*失败数量*/;
  successCount?: number /*成功数量*/;
  totalCount?: number /*总数*/;
};

/**
 *导入商机
 */
export const businessOpportunityImport = (params: BusinessOpportunityImportRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityImportResult>>(
    '/supplier/public/businessOpportunity/import',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/import') },
    },
  );
};

export type BusinessOpportunityBatchDeleteRequest = {
  ids?: Array<string> /*商机ID列表*/;
};

export type BusinessOpportunityBatchDeleteResult = boolean;

/**
 *批量删除商机
 */
export const businessOpportunityBatchDelete = (params: BusinessOpportunityBatchDeleteRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityBatchDeleteResult>>(
    '/supplier/public/businessOpportunity/batchDelete',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/batchDelete') },
    },
  );
};

export type BusinessOpportunityGetContactRequest = {
  id?: string /*业务ID*/;
};

export type BusinessOpportunityGetContactResult = {
  contactMobile?: string /*联系方式*/;
  contactPerson?: string /*联系人*/;
  id?: string /*商机ID*/;
};

/**
 *查看商机联系方式
 */
export const businessOpportunityGetContact = (params: BusinessOpportunityGetContactRequest) => {
  return Fetch<ResponseWithResult<BusinessOpportunityGetContactResult>>(
    '/supplier/public/businessOpportunity/getContact',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/supplier/public/businessOpportunity/getContact') },
    },
  );
};
