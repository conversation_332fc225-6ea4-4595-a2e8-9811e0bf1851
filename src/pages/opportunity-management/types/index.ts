import { FlowStatus_ChoiceList, SELECT_STEP_NAME } from '@/pages/selection-flow-board/types';

export enum SOURCE_CHANNEL_ENUM {
  /**
   * 商务资源拓展
   */
  BUSINESS_RESOURCE_EXPANSION = 'BUSINESS_RESOURCE_EXPANSION',
  /**
   * 行业商家连带
   */
  INDUSTRY_MERCHANT_ASSOCIATION = 'INDUSTRY_MERCHANT_ASSOCIATION',
  /**
   * 产地行业协会
   */
  ORIGIN_INDUSTRY_ASSOCIATION = 'ORIGIN_INDUSTRY_ASSOCIATION',
  /**
   * 电商平台资源
   */
  ECOMMERCE_PLATFORM_RESOURCE = 'ECOMMERCE_PLATFORM_RESOURCE',
  /**
   * 抖音店铺建联
   */
  DOUYIN_SHOP_CONNECTION = 'DOUYIN_SHOP_CONNECTION',
  /**
   * 抖音小二串联
   */
  DOUYIN_XIAOER_CONNECTION = 'DOUYIN_XIAOER_CONNECTION',
}

export const SOURCE_CHANNEL_NAME = {
  [SOURCE_CHANNEL_ENUM.BUSINESS_RESOURCE_EXPANSION]: '商务资源拓展',
  [SOURCE_CHANNEL_ENUM.INDUSTRY_MERCHANT_ASSOCIATION]: '行业商家连带',
  [SOURCE_CHANNEL_ENUM.ORIGIN_INDUSTRY_ASSOCIATION]: '产地行业协会',
  [SOURCE_CHANNEL_ENUM.ECOMMERCE_PLATFORM_RESOURCE]: '电商平台资源',
  [SOURCE_CHANNEL_ENUM.DOUYIN_SHOP_CONNECTION]: '抖音店铺建联',
  [SOURCE_CHANNEL_ENUM.DOUYIN_XIAOER_CONNECTION]: '抖音小二串联',
};

export const SOURCE_CHANNEL_LIST = [
  {
    label: SOURCE_CHANNEL_NAME[SOURCE_CHANNEL_ENUM.BUSINESS_RESOURCE_EXPANSION],
    value: SOURCE_CHANNEL_ENUM.BUSINESS_RESOURCE_EXPANSION,
  },
  {
    label: SOURCE_CHANNEL_NAME[SOURCE_CHANNEL_ENUM.INDUSTRY_MERCHANT_ASSOCIATION],
    value: SOURCE_CHANNEL_ENUM.INDUSTRY_MERCHANT_ASSOCIATION,
  },
  {
    label: SOURCE_CHANNEL_NAME[SOURCE_CHANNEL_ENUM.ORIGIN_INDUSTRY_ASSOCIATION],
    value: SOURCE_CHANNEL_ENUM.ORIGIN_INDUSTRY_ASSOCIATION,
  },
  {
    label: SOURCE_CHANNEL_NAME[SOURCE_CHANNEL_ENUM.ECOMMERCE_PLATFORM_RESOURCE],
    value: SOURCE_CHANNEL_ENUM.ECOMMERCE_PLATFORM_RESOURCE,
  },
  {
    label: SOURCE_CHANNEL_NAME[SOURCE_CHANNEL_ENUM.DOUYIN_SHOP_CONNECTION],
    value: SOURCE_CHANNEL_ENUM.DOUYIN_SHOP_CONNECTION,
  },
  {
    label: SOURCE_CHANNEL_NAME[SOURCE_CHANNEL_ENUM.DOUYIN_XIAOER_CONNECTION],
    value: SOURCE_CHANNEL_ENUM.DOUYIN_XIAOER_CONNECTION,
  },
];

export const BROADCAST_STATUS_LITS = [
  {
    value: FlowStatus_ChoiceList.WAIT_LIVE,
    label: SELECT_STEP_NAME[FlowStatus_ChoiceList.WAIT_LIVE],
  },
  {
    value: FlowStatus_ChoiceList.COMPLETED_LIVE,
    label: SELECT_STEP_NAME[FlowStatus_ChoiceList.COMPLETED_LIVE],
  },
];

export const OPPORTUNITY_CLOSE_REASON_LIST = [
  {
    value: FlowStatus_ChoiceList.ABORT_LIVE,
    label: SELECT_STEP_NAME[FlowStatus_ChoiceList.ABORT_LIVE],
  },
  {
    value: FlowStatus_ChoiceList.CANCEL,
    label: SELECT_STEP_NAME[FlowStatus_ChoiceList.CANCEL],
  },
  {
    value: FlowStatus_ChoiceList.LOSE_EFFICACY,
    label: SELECT_STEP_NAME[FlowStatus_ChoiceList.LOSE_EFFICACY],
  },
];

/**
 * 商机阶段
 */
export enum STAGE_ENUM {
  /**
   * 建联客户
   */
  CUSTOMER_CONNECTION = 'CUSTOMER_CONNECTION',
  /**
   * 确认商机
   */
  OPPORTUNITY_CONFIRMATION = 'OPPORTUNITY_CONFIRMATION',
  /**
   * 机制谈判
   */
  MECHANISM_NEGOTIATION = 'MECHANISM_NEGOTIATION',
  /**
   * 定上播日期
   */
  LIVE_DATE_SCHEDULING = 'LIVE_DATE_SCHEDULING',
  /**
   * 寄样选品
   */
  SAMPLE_SELECTION = 'SAMPLE_SELECTION',
  /**
   * 合同签订
   */
  CONTRACT_SIGNING = 'CONTRACT_SIGNING',
  /**
   * 上播
   */
  COMPLETED_LIVE = 'COMPLETED_LIVE',
  /**
   * 掉品
   */
  DROP_PRODUCT = 'DROP_PRODUCT',
}

/**
 * 商机阶段名称
 */
export const STAGE_NAME = {
  [STAGE_ENUM.CUSTOMER_CONNECTION]: '建联客户',
  [STAGE_ENUM.OPPORTUNITY_CONFIRMATION]: '确认商机',
  [STAGE_ENUM.MECHANISM_NEGOTIATION]: '机制谈判',
  [STAGE_ENUM.LIVE_DATE_SCHEDULING]: '定上播日期',
  [STAGE_ENUM.SAMPLE_SELECTION]: '寄样选品',
  [STAGE_ENUM.CONTRACT_SIGNING]: '合同签订',
  [STAGE_ENUM.COMPLETED_LIVE]: '上播',
  [STAGE_ENUM.DROP_PRODUCT]: '掉品',
};

/**
 * 商机阶段列表
 */
export const STAGE_LIST = [
  {
    label: STAGE_NAME[STAGE_ENUM.CUSTOMER_CONNECTION],
    value: STAGE_ENUM.CUSTOMER_CONNECTION,
  },
  {
    label: STAGE_NAME[STAGE_ENUM.OPPORTUNITY_CONFIRMATION],
    value: STAGE_ENUM.OPPORTUNITY_CONFIRMATION,
  },
  {
    label: STAGE_NAME[STAGE_ENUM.MECHANISM_NEGOTIATION],
    value: STAGE_ENUM.MECHANISM_NEGOTIATION,
  },
  {
    label: STAGE_NAME[STAGE_ENUM.LIVE_DATE_SCHEDULING],
    value: STAGE_ENUM.LIVE_DATE_SCHEDULING,
  },
  {
    label: STAGE_NAME[STAGE_ENUM.SAMPLE_SELECTION],
    value: STAGE_ENUM.SAMPLE_SELECTION,
    sort: 5,
  },
  {
    label: STAGE_NAME[STAGE_ENUM.CONTRACT_SIGNING],
    value: STAGE_ENUM.CONTRACT_SIGNING,
  },
  {
    label: STAGE_NAME[STAGE_ENUM.COMPLETED_LIVE],
    value: STAGE_ENUM.COMPLETED_LIVE,
  },
  {
    label: STAGE_NAME[STAGE_ENUM.DROP_PRODUCT],
    value: STAGE_ENUM.DROP_PRODUCT,
  },
];

export enum ReviewTaskStatus {
  /**
   * 新建商机
   */
  BUSINESS_OPPORTUNITY_CREATE = 'BUSINESS_OPPORTUNITY_CREATE',
  /**
   * 建联客户
   */
  BUSINESS_OPPORTUNITY_CONTACT_CUSTOMER = 'BUSINESS_OPPORTUNITY_CONTACT_CUSTOMER',
  /**
   * 确认商机
   */
  BUSINESS_OPPORTUNITY_CONFIRM = 'BUSINESS_OPPORTUNITY_CONFIRM',
  /**
   * 机制谈判
   */
  BUSINESS_OPPORTUNITY_MECHANISM_NEGOTIATION = 'BUSINESS_OPPORTUNITY_MECHANISM_NEGOTIATION',
  /**
   * 定上播日期
   */
  BUSINESS_OPPORTUNITY_SET_LIVE_DATE = 'BUSINESS_OPPORTUNITY_SET_LIVE_DATE',
  /**
   * 寄样选品
   */
  BUSINESS_OPPORTUNITY_SEND_SAMPLE = 'BUSINESS_OPPORTUNITY_SEND_SAMPLE',
  /**
   * 合同签订
   */
  BUSINESS_OPPORTUNITY_CONTRACT_SIGN = 'BUSINESS_OPPORTUNITY_CONTRACT_SIGN',
  /**
   * 上播
   */
  BUSINESS_OPPORTUNITY_LIVE = 'BUSINESS_OPPORTUNITY_LIVE',
  /**
   * 掉品
   */
  BUSINESS_OPPORTUNITY_DROP = 'BUSINESS_OPPORTUNITY_DROP',
  /**
   * 释放
   */
  BUSINESS_OPPORTUNITY_RELEASE = 'BUSINESS_OPPORTUNITY_RELEASE',
  /**
   * 自动释放
   */
  BUSINESS_OPPORTUNITY_AUTO_RELEASE = 'BUSINESS_OPPORTUNITY_AUTO_RELEASE',
  /**
   * 认领
   */
  BUSINESS_OPPORTUNITY_CLAIM = 'BUSINESS_OPPORTUNITY_CLAIM',
  /**
   * 分配
   */
  BUSINESS_OPPORTUNITY_ASSIGN = 'BUSINESS_OPPORTUNITY_ASSIGN',
  /**
   * 编辑商机基本信息
   */
  BUSINESS_OPPORTUNITY_EDIT_BASIC_INFO = 'BUSINESS_OPPORTUNITY_EDIT_BASIC_INFO',
  /**
   * 编辑预估商务条件
   */
  BUSINESS_OPPORTUNITY_EDIT_ESTIMATED_CONDITIONS = 'BUSINESS_OPPORTUNITY_EDIT_ESTIMATED_CONDITIONS',
  /**
   * 编辑实际商务条件
   */
  BUSINESS_OPPORTUNITY_EDIT_ACTUAL_CONDITIONS = 'BUSINESS_OPPORTUNITY_EDIT_ACTUAL_CONDITIONS',
  /**
   * 编辑场次信息
   */
  BUSINESS_OPPORTUNITY_EDIT_SESSION_INFO = 'BUSINESS_OPPORTUNITY_EDIT_SESSION_INFO',
  /**
   * 编辑样品信息
   */
  BUSINESS_OPPORTUNITY_EDIT_SAMPLE_INFO = 'BUSINESS_OPPORTUNITY_EDIT_SAMPLE_INFO',
  /**
   * 编辑合作主体
   */
  BUSINESS_OPPORTUNITY_EDIT_COOPERATION_SUBJECT = 'BUSINESS_OPPORTUNITY_EDIT_COOPERATION_SUBJECT',
  /**
   * 编辑上播信息
   */
  BUSINESS_OPPORTUNITY_EDIT_LIVE_INFO = 'BUSINESS_OPPORTUNITY_EDIT_LIVE_INFO',
  /**
   * 编辑关闭商机
   */
  BUSINESS_OPPORTUNITY_CLOSE = 'BUSINESS_OPPORTUNITY_CLOSE',
  /**
   * 删除场次
   */
  BUSINESS_OPPORTUNITY_DELETE_SESSION = 'BUSINESS_OPPORTUNITY_DELETE_SESSION',
  /**
   * 删除上播选品关联
   */
  BUSINESS_OPPORTUNITY_DELETE_SELECTION_LIVE = 'BUSINESS_OPPORTUNITY_DELETE_SELECTION_LIVE',
  /**
   * 删除掉品选品关联
   */
  BUSINESS_OPPORTUNITY_DELETE_SELECTION_DROP = 'BUSINESS_OPPORTUNITY_DELETE_SELECTION_DROP',
}

/**
 * 商机管理相关操作名称
 */
export const ReviewTaskStatusName = {
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_CREATE]: '新建商机',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_CONTACT_CUSTOMER]: '建联客户',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_CONFIRM]: '确认商机',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_MECHANISM_NEGOTIATION]: '机制谈判',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_SET_LIVE_DATE]: '定上播日期',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_SEND_SAMPLE]: '寄样选品',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_CONTRACT_SIGN]: '合同签订',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_LIVE]: '上播',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_DROP]: '掉品',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_RELEASE]: '释放商机',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_AUTO_RELEASE]: '自动释放商机',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_CLAIM]: '认领商机',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_ASSIGN]: '分配商机',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_EDIT_BASIC_INFO]: '编辑商机基本信息',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_EDIT_ESTIMATED_CONDITIONS]: '编辑预估商务条件',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_EDIT_ACTUAL_CONDITIONS]: '编辑实际商务条件',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_EDIT_SESSION_INFO]: '编辑场次信息',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_EDIT_SAMPLE_INFO]: '编辑样品信息',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_EDIT_COOPERATION_SUBJECT]: '编辑合作主体',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_EDIT_LIVE_INFO]: '编辑上播信息',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_CLOSE]: '编辑关闭商机',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_DELETE_SESSION]: '删除场次',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_DELETE_SELECTION_LIVE]: '删除上播选品关联',
  [ReviewTaskStatus.BUSINESS_OPPORTUNITY_DELETE_SELECTION_DROP]: '删除掉品选品关联',
};

export enum OPPORTUNITY_TYPE_ENUM {
  NEW = 'NEW_OPPORTUNITY',
  OLD = 'OLD_OPPORTUNITY',
}

export const OPPORTUNITY_TYPE_NAME = {
  [OPPORTUNITY_TYPE_ENUM.NEW]: '新商机',
  [OPPORTUNITY_TYPE_ENUM.OLD]: '老商机',
};

export const OPPORTUNITY_TYPE_LIST = [
  {
    label: OPPORTUNITY_TYPE_NAME[OPPORTUNITY_TYPE_ENUM.NEW],
    value: OPPORTUNITY_TYPE_ENUM.NEW,
  },
  {
    label: OPPORTUNITY_TYPE_NAME[OPPORTUNITY_TYPE_ENUM.OLD],
    value: OPPORTUNITY_TYPE_ENUM.OLD,
  },
];

export enum LIVE_SESSION_TIMES {
  MORNING = 'MORNING',
  AFTERNOON = 'AFTERNOON',
  EVENING = 'EVENING',
}

export const LIVE_SESSION_TIMES_NAME = {
  [LIVE_SESSION_TIMES.MORNING]: '早场',
  [LIVE_SESSION_TIMES.AFTERNOON]: '中场',
  [LIVE_SESSION_TIMES.EVENING]: '晚场',
};

export const LIVE_SESSION_TIMES_LIST = [
  {
    label: LIVE_SESSION_TIMES_NAME[LIVE_SESSION_TIMES.MORNING],
    value: LIVE_SESSION_TIMES.MORNING,
  },
  {
    label: LIVE_SESSION_TIMES_NAME[LIVE_SESSION_TIMES.AFTERNOON],
    value: LIVE_SESSION_TIMES.AFTERNOON,
  },
  {
    label: LIVE_SESSION_TIMES_NAME[LIVE_SESSION_TIMES.EVENING],
    value: LIVE_SESSION_TIMES.EVENING,
  },
];
