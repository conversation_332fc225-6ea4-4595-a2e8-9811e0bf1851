import React, { useCallback, useEffect } from 'react';
import PageLayout from '@/components/PageLayout/index';
import {
  Card,
  FormContentLayout,
  DetailContentLayout,
  DetailTitle,
  SpinCard,
} from '@/components/DetailFormCompoments';
import { Button, message } from 'antd';
import { AuthWrapper } from 'qmkit';
import { StepsMeg, LogsTable, TabsComponents, Claim } from './components';
import { getQueryParams } from 'web-common-modules/utils/params';
import { useBtn, useAddEdit } from './hooks';
import { businessOpportunityChangeStage } from './services';
import { useRequest } from 'ahooks';

const DetailPage = () => {
  const id = getQueryParams()?.id;

  const { detailRun, detailLoading, detailData } = useAddEdit({ isContact: false });

  const onRefresh = () => {
    detailRun({ id });
  };

  const {
    releaseRun,
    releaseLoading,
    assignRun,
    assignLoading,
    claimRun,
    claimLoading,
    copyRun,
    copyLoading,
  } = useBtn({
    onRefresh: onRefresh,
  });

  const handleRelease = () => {
    releaseRun({
      ids: [id],
    });
  };

  const handleClaim = () => {
    claimRun({
      ids: [id],
    });
  };

  const { run: changeStageRun, loading: changeStageLoading } = useRequest(
    businessOpportunityChangeStage,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          return message.warning(res?.message || '变更阶段失败');
        }
        message.success('变更阶段成功');
        onRefresh();
      },
    },
  );

  const handleChangeStage = useCallback(
    (params: any) => {
      changeStageRun({ ...params, id, version: detailData?.version });
    },
    [changeStageRun, detailData?.version, id],
  );

  useEffect(() => {
    if (id) {
      detailRun({ id });
    }
  }, [id]);

  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText="商机详情">
          <AuthWrapper functionName="f_opportunity_management_all_release">
            <Button
              className="ml-8"
              style={{ borderColor: '#999999', color: '#444444' }}
              onClick={handleRelease}
              loading={releaseLoading}
            >
              释放
            </Button>
          </AuthWrapper>
          <AuthWrapper functionName="f_opportunity_management_all_assignment">
            <Claim
              onRefresh={onRefresh}
              selectedRows={[{ id: id }]}
              selectedRowKeys={[id]}
              type="assign"
            >
              <Button
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                loading={assignLoading}
              >
                分配
              </Button>
            </Claim>
          </AuthWrapper>
          <AuthWrapper functionName="f_opportunity_management_all_claim">
            <Button
              className="ml-8"
              style={{ borderColor: '#999999', color: '#444444' }}
              onClick={handleClaim}
              loading={claimLoading}
            >
              认领
            </Button>
          </AuthWrapper>
          <AuthWrapper functionName="f_opportunity_management_all_copy">
            <Claim
              onRefresh={onRefresh}
              selectedRows={[{ id: id }]}
              selectedRowKeys={[id]}
              type="copy"
            >
              <Button
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                loading={copyLoading}
              >
                复制
              </Button>
            </Claim>
          </AuthWrapper>
        </DetailTitle>
        <SpinCard
          spinning={
            detailLoading ||
            releaseLoading ||
            assignLoading ||
            claimLoading ||
            copyLoading ||
            changeStageLoading
          }
        >
          <Card title="">
            <div style={{ padding: '0 16px' }}>
              <StepsMeg
                detailData={detailData}
                id={id}
                handleChangeStage={handleChangeStage}
                changeStageLoading={changeStageLoading}
              />
            </div>
          </Card>
          <Card>
            <TabsComponents detailData={detailData || {}} onRefresh={onRefresh} />
          </Card>
          <Card title="日志记录">
            <LogsTable detailData={detailData} />
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default DetailPage;
