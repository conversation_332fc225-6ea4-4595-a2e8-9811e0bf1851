import React from 'react';
import { Select, DatePicker, Input } from 'antd';
import { BatchInput, BrandSelect } from '../components';
import { useGroup } from './index';
import { BROADCAST_STATUS_LITS, OPPORTUNITY_CLOSE_REASON_LIST } from '../types';
import { useTalent } from '@/pages/selection-flow-board/hooks/useTalent';
import { FlowStatus_ChoiceList } from '@/pages/selection-flow-board/types';

const { RangePicker } = DatePicker;

export const useBroadcastOptions = ({ deptId }: { deptId?: string }) => {
  const {
    handleGroupChange,
    groupList,
    loading: groupLoading,
    onSearchGroup,
    handleGroupBlur,
  } = useGroup();

  const {
    list: talentList,
    loading: talentLoading,
    debounce: talentDebounce,
    reset: talentReset,
    deptChangeGetList,
  } = useTalent(deptId);
  const options = {
    liveRoomId: {
      label: '直播间',
      renderNode: (
        <Select
          placeholder="请输入"
          allowClear
          loading={talentLoading}
          showSearch
          filterOption={false}
          optionFilterProp="children"
          dropdownMatchSelectWidth={false}
          onSearch={(value) => {
            talentDebounce(value);
          }}
          onChange={(val) => {
            if (!val) {
              talentReset();
            }
          }}
          onBlur={() => {
            talentReset();
          }}
          defaultActiveFirstOption={false}
          mode="multiple"
          maxTagCount={1}
        >
          {talentList?.map((item) => {
            // const { companyInfo } = item;
            return (
              <Select.Option value={item?.id} key={item?.id}>
                {item?.name}-{item?.talentNo}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    liveDate: {
      label: '直播日期',
      renderNode: <RangePicker style={{ width: '100%' }} />,
    },
    spuName: {
      label: '商品名称',
      renderNode: <BatchInput label="商品名称" />,
    },
    brandId: {
      label: '品牌',
      renderNode: <BrandSelect />,
    },
    selectionNo: {
      label: '选品流程',
      draggable: false,
      renderNode: <BatchInput label="选品流程" />,
    },
    groupId: {
      label: '项目组',
      renderNode: (
        <Select
          allowClear
          placeholder="全部"
          filterOption={false}
          onSearch={onSearchGroup}
          mode="multiple"
          showSearch
          onChange={handleGroupChange}
          loading={groupLoading}
          onBlur={handleGroupBlur}
        >
          {groupList?.map((item) => (
            <Select.Option key={item?.projectGroupId} value={item?.projectGroupId}>
              {item?.projectGroupName}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    status: {
      label: '选品阶段',
      hocOptions: {
        initialValue: [FlowStatus_ChoiceList.WAIT_LIVE, FlowStatus_ChoiceList.COMPLETED_LIVE],
      },
      renderNode: (
        <Select allowClear mode="multiple" placeholder="全部" filterOption={false}>
          {BROADCAST_STATUS_LITS.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
  };

  return {
    options,
  };
};

export const useOpportunityCloseReasonOptions = ({ deptId }: { deptId?: string }) => {
  const {
    handleGroupChange,
    groupList,
    loading: groupLoading,
    onSearchGroup,
    handleGroupBlur,
  } = useGroup();

  const {
    list: talentList,
    loading: talentLoading,
    debounce: talentDebounce,
    reset: talentReset,
    deptChangeGetList,
  } = useTalent(deptId);
  const options = {
    liveRoomId: {
      label: '直播间',
      renderNode: (
        <Select
          placeholder="请输入"
          allowClear
          loading={talentLoading}
          showSearch
          filterOption={false}
          optionFilterProp="children"
          dropdownMatchSelectWidth={false}
          onSearch={(value) => {
            talentDebounce(value);
          }}
          onChange={(val) => {
            if (!val) {
              talentReset();
            }
          }}
          onBlur={() => {
            talentReset();
          }}
          defaultActiveFirstOption={false}
          mode="multiple"
          maxTagCount={1}
        >
          {talentList?.map((item) => {
            // const { companyInfo } = item;
            return (
              <Select.Option value={item?.id} key={item?.id}>
                {item?.name}-{item?.talentNo}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    liveDate: {
      label: '直播日期',
      renderNode: <RangePicker style={{ width: '100%' }} />,
    },
    spuName: {
      label: '商品名称',
      renderNode: <BatchInput label="商品名称" />,
    },
    brandId: {
      label: '品牌',
      renderNode: <BrandSelect />,
    },
    selectionNo: {
      label: '选品流程',
      draggable: false,
      renderNode: <BatchInput label="选品流程" />,
    },
    groupId: {
      label: '项目组',
      renderNode: (
        <Select
          allowClear
          placeholder="全部"
          filterOption={false}
          onSearch={onSearchGroup}
          mode="multiple"
          showSearch
          onChange={handleGroupChange}
          loading={groupLoading}
          onBlur={handleGroupBlur}
        >
          {groupList?.map((item) => (
            <Select.Option key={item?.projectGroupId} value={item?.projectGroupId}>
              {item?.projectGroupName}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    status: {
      label: '选品阶段',
      hocOptions: {
        initialValue: [
          FlowStatus_ChoiceList.ABORT_LIVE,
          FlowStatus_ChoiceList.CANCEL,
          FlowStatus_ChoiceList.LOSE_EFFICACY,
        ],
      },
      renderNode: (
        <Select allowClear mode="multiple" placeholder="全部" filterOption={false}>
          {OPPORTUNITY_CLOSE_REASON_LIST.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
  };

  return {
    options,
  };
};
