import { useRequest } from 'ahooks';

import { useEffect, useState } from 'react';
import usePagination from '@/hooks/usePagination';
import { selectionProcessKanbanPage } from '../../selection-flow-board/services/yml';
import { handleResponse } from '@/utils/response';
import { businessOpportunitySessionRelPage } from '@/pages/opportunity-management/services';
import { selectionProcessKanbanBusinessOpportunityPage } from '@/pages/opportunity-management/services';
import { businessOpportunitySelectionRoundRelPage } from '@/pages/opportunity-management/services';

export enum APIKEY {
  BUSINESS_OPPORTUNITY_SESSION_REL_PAGE = 'business_opportunity_session_rel_page',
  SELECTION_PROCESS_KANBAN_LIVE_ROOM_LIST = 'selection_process_kanban_live_room_list',
  BUSINESS_OPPORTUNITY_SELECTION_ROUND_REL_PAGE = 'business_opportunity_selection_round_rel_page',
}

const apiMap = {
  [APIKEY.BUSINESS_OPPORTUNITY_SESSION_REL_PAGE]: businessOpportunitySessionRelPage,
  [APIKEY.SELECTION_PROCESS_KANBAN_LIVE_ROOM_LIST]: selectionProcessKanbanBusinessOpportunityPage,
  [APIKEY.BUSINESS_OPPORTUNITY_SELECTION_ROUND_REL_PAGE]: businessOpportunitySelectionRoundRelPage,
};

export const useList = <Req, Res>(
  type: APIKEY,
  formatList: undefined | ((value: any) => any) = undefined,
  size?: number,
  subCurrent?: boolean,
) => {
  // 列表数据
  const [dataSource, setDataSource] = useState<Res>();
  // 保存搜索数据
  const [condition, setCondition] = useState<Req>();
  const { pagination, setPagination } = usePagination({
    current: 1,
    size: size || 20,
  });

  const { loading, run } = useRequest(apiMap[type], {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        const { records = [], total, current, size, pages } = res.result;
        if (!records?.length && pages && (current || 0) > (pages || 0)) {
          run({
            ...condition,
            current: pages,
            size: size,
          });
          return;
        }
        setPagination({
          total,
          current: subCurrent ? current + 1 : current,
          size,
        });
        if (formatList) {
          setDataSource(formatList(records) || []);
          return;
        }
        setDataSource((records as Res) || []);
      });
    },
  });

  // 分页修改
  const onPageChange = (current: number, size: number) => {
    setPagination({ current, size, total: pagination.total });
    run({ ...condition, current: subCurrent ? current - 1 : current, size });
  };

  // 搜索
  const onSearch = (value: Req) => {
    console.log('value', value);
    setCondition(value);
    setPagination({
      current: 1,
      size: pagination.size,
    });
    run({ ...value, current: subCurrent ? 0 : 1, size: pagination.size });
  };

  // 刷新当前页面
  const onRefresh = () => {
    run({
      ...condition,
      current: subCurrent ? pagination.current - 1 : pagination.current,
      size: pagination.size,
    });
  };

  // useEffect(() => {
  //   run({ current: pagination.current, size: pagination.size });
  // }, []);

  return {
    dataSource,
    // setDataSource,
    condition,
    // setCondition,
    pagination,
    // setPagination,
    loading,
    // run,
    onPageChange,
    onSearch,
    onRefresh,
  };
};

// 2025-05-15zhouby -> cursor ai结尾共生成10行代码
