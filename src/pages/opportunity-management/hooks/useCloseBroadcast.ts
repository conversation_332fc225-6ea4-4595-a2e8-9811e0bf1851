import {
  BusinessOpportunitySelectionRoundRelPageRequest,
  BusinessOpportunitySelectionRoundRelPageResult,
  businessOpportunitySelectionRoundRelDeleteById,
  BusinessOpportunityDetailResult,
} from '../services';
// import { useList, APIKEY } from '@/pages/live-room-manage/hooks/useList';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { useEffect } from 'react';
import { useList, APIKEY } from '../hooks';

export const useCloseBroadcast = (
  detailData: BusinessOpportunityDetailResult,
  type: 'COMPLETED_LIVE' | 'DROP_PRODUCT',
) => {
  const {
    dataSource,
    pagination,
    onPageChange,
    onSearch,
    onRefresh: listOnRefresh,
    loading,
  } = useList<
    BusinessOpportunitySelectionRoundRelPageRequest,
    BusinessOpportunitySelectionRoundRelPageResult
  >(APIKEY.BUSINESS_OPPORTUNITY_SELECTION_ROUND_REL_PAGE);

  const { run: deleteSelectionRoundRelRun, loading: deleteSelectionRoundRelLoading } = useRequest(
    businessOpportunitySelectionRoundRelDeleteById,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        message.success('操作成功');
        listOnRefresh();
      },
    },
  );

  useEffect(() => {
    if (detailData?.id) {
      onSearch({ businessOpportunityId: detailData?.id, type });
    }
  }, [detailData?.id]);

  return {
    dataSource,
    pagination,
    onPageChange,
    // onSearch,
    listOnRefresh,
    deleteSelectionRoundRelRun,
    deleteSelectionRoundRelLoading,
    loading,
  };
};
