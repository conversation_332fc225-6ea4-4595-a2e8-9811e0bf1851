import { useRequest } from 'ahooks';
import {
  businessOpportunityRelease,
  businessOpportunityAssign,
  businessOpportunityClaim,
  businessOpportunityCopy,
  businessOpportunityBatchDelete,
} from '../services';
import { handleResponse } from '@/utils/response';
import { message } from 'antd';

export const useBtn = ({ onRefresh }: { onRefresh: () => void }) => {
  //释放商机
  const { run: releaseRun, loading: releaseLoading } = useRequest(businessOpportunityRelease, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        message.success('操作成功');
        onRefresh();
      });
    },
  });

  //分配商机
  const { run: assignRun, loading: assignLoading } = useRequest(businessOpportunityAssign, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        message.success('操作成功');
        onRefresh();
      });
    },
  });

  //认领商机
  const { run: claimRun, loading: claimLoading } = useRequest(businessOpportunityClaim, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        message.success('操作成功');
        onRefresh();
      });
    },
  });

  //复制商机
  const { run: copyRun, loading: copyLoading } = useRequest(businessOpportunityCopy, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        message.success('操作成功');
        onRefresh();
      });
    },
  });

  // 删除
  const { run: deleteRun, loading: deleteLoading } = useRequest(businessOpportunityBatchDelete, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        message.success('操作成功');
        onRefresh();
      });
    },
  });

  return {
    releaseRun,
    releaseLoading,
    assignRun,
    assignLoading,
    claimRun,
    claimLoading,
    copyRun,
    copyLoading,
    deleteRun,
    deleteLoading,
  };
};
