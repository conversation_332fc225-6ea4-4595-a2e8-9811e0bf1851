import React, { useEffect } from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { Input, DatePicker, Select } from 'antd';
import { useDeptList } from '@/hooks/useDeptList';
import { useGroup } from './useGroup';
import { NumberInterval } from '@/pages/dy-head-link/components';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { useLegal } from '@/pages/certification-audit/hooks';
import { STAGE_LIST, OPPORTUNITY_TYPE_LIST } from '../types';

const { RangePicker } = DatePicker;

export const useSearch = ({ form }: { form?: WrappedFormUtils }) => {
  const { deptList, loading: deptLoading } = useDeptList();

  const {
    handleGroupChange,
    groupList,
    loading: projectGroupLoading,
    onSearchGroup,
    handleGroupBlur,
  } = useGroup();

  const { roleList, roleRun, roleLoading, handleRoleChange, handelRoleSearch, handleRoleBlur } =
    useLegal();

  const { codeList: COMMISSION_RANGE_LIST } = useCode(CODE_ENUM.COMMISSION_RANGE);
  const { codeList: BRAND_FEE_RANGE_LIST } = useCode(CODE_ENUM.BRAND_FEE_RANGE);
  const { codeList: SOURCE_CHANNEL_LIST } = useCode(CODE_ENUM.SOURCE_CHANNEL, { able: true });
  const { codeList: COOPERATION_TYPE_LIST } = useCode(CODE_ENUM.COOPERATION_TYPE, { able: true });

  useEffect(() => {
    roleRun({ current: 1, size: 20 });
  }, []);

  const options = {
    no: {
      label: '商机编号',
      renderNode: <Input placeholder="请输入" maxLength={50} />,
    },
    brandName: {
      label: '品牌名称',
      renderNode: <Input placeholder="请输入" />,
    },
    contactPerson: {
      label: '联系人',
      renderNode: <Input placeholder="请输入" />,
    },
    contactMobile: {
      label: '联系方式',
      renderNode: <Input placeholder="请输入" />,
    },
    cooperationSubject: {
      label: '合作主体',
      renderNode: <Input placeholder="请输入" />,
    },
    contactDate: {
      label: '建联日期',
      renderNode: <RangePicker />,
    },
    follower: {
      label: '跟进人',
      renderNode: (
        <Select
          loading={roleLoading}
          placeholder="请选择"
          allowClear
          showSearch
          filterOption={false}
          onSearch={handelRoleSearch}
          onChange={handleRoleChange}
          onBlur={handleRoleBlur}
        >
          {roleList?.map((item) => (
            <Select.Option key={item.employeeId} value={item.employeeId}>
              {item.employeeName}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    stageList: {
      label: '商机阶段',
      renderNode: (
        <Select
          allowClear
          placeholder="请选择"
          mode="multiple"
          maxTagCount={1}
          filterOption={false}
          showSearch={false}
        >
          {STAGE_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    sourceChannel: {
      label: '来源渠道',
      renderNode: (
        <Select allowClear placeholder="请选择" filterOption={false} showSearch={false}>
          {SOURCE_CHANNEL_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    cooperationTypeList: {
      label: '合作方式',
      renderNode: (
        <Select
          allowClear
          placeholder="请选择"
          filterOption={false}
          showSearch={false}
          mode="multiple"
        >
          {COOPERATION_TYPE_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    businessOpportunityType: {
      label: '商机类型',
      renderNode: (
        <Select allowClear placeholder="请选择" filterOption={false} showSearch={false}>
          {OPPORTUNITY_TYPE_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    followerDeptIdList: {
      label: '所属事业部',
      renderNode: (
        <Select allowClear placeholder="请选择" loading={deptLoading} mode="multiple">
          {deptList?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    projectGroupIdList: {
      label: '项目组',
      renderNode: (
        <Select
          allowClear
          placeholder="请选择"
          loading={projectGroupLoading}
          mode="multiple"
          maxTagCount={1}
          onBlur={handleGroupBlur}
          onChange={handleGroupChange}
          filterOption={false}
          showSearch
          onSearch={onSearchGroup}
        >
          {groupList?.map((item) => (
            <Select.Option key={item?.projectGroupId} value={item?.projectGroupId}>
              {item?.projectGroupName}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    estimatedCommissionRangeList: {
      label: '预佣金范围',
      renderNode: (
        <Select
          allowClear
          placeholder="请选择"
          filterOption={false}
          showSearch={false}
          mode="multiple"
        >
          {COMMISSION_RANGE_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    actualCommissionRate: {
      label: '确认佣金比例',
      hocOptions: {
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!value) {
                return callback();
              }
              const [min, max] = value;
              if (!min && !max) {
                return callback();
              }
              if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                return callback('请输入0-100的数字');
              }
              if ((min && Number(min) > 100) || (max && Number(max) > 100)) {
                return callback('请输入0-100的数字');
              }
              if (min && max && Number(min) > Number(max)) {
                return callback('最低值应该小于最高值');
              }
              callback();
            },
          },
        ],
      },
      renderNode: <NumberInterval minPlaceholder="最低(%)" maxPlaceholder="最高(%)" />,
    },
    estimatedBrandFeeRangeList: {
      label: '品牌费区间',
      renderNode: (
        <Select
          allowClear
          placeholder="请选择"
          filterOption={false}
          showSearch={false}
          mode="multiple"
        >
          {BRAND_FEE_RANGE_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    actualConfirmedBrandFee: {
      label: '确认品牌费',
      hocOptions: {
        rules: [
          {
            validator: (rule: any, value: any, callback: any) => {
              if (!value) {
                return callback();
              }
              const [min, max] = value;
              if (!min && !max) {
                return callback();
              }
              if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                return callback('请输入0-99999999的数字');
              }
              if ((min && Number(min) > 99999999) || (max && Number(max) > 99999999)) {
                return callback('请输入0-99999999的数字');
              }
              if (min && max && Number(min) > Number(max)) {
                return callback('最低值应该小于最高值');
              }
              callback();
            },
          },
        ],
      },
      renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
    },
    creator: {
      label: '创建人',
      renderNode: (
        <Select
          loading={roleLoading}
          placeholder="请选择"
          allowClear
          showSearch
          filterOption={false}
          onSearch={handelRoleSearch}
          onChange={handleRoleChange}
          onBlur={handleRoleBlur}
        >
          {roleList?.map((item) => (
            <Select.Option key={item.employeeId} value={item.employeeId}>
              {item.employeeName}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    gmtCreated: {
      label: '创建时间',
      renderNode: <RangePicker />,
    },
  };

  return {
    options,
  };
};
