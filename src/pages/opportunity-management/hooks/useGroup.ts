import { useEffect, useState } from 'react';
import { projectGroupList } from '../services/index';
import { useRequest } from 'ahooks';
import { handleResponse } from '@/utils/response';
import { debounce } from 'lodash';
import { message } from 'antd';

export const useGroup = (deptId?: string) => {
  const [groupList, setGroupList] = useState<any[]>([]);
  const { loading, run } = useRequest(projectGroupList, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        if (res?.success) {
          const { records } = res?.result || {};
          setGroupList(records || []);
        } else {
          message.warning(res?.message || '网络异常');
        }
        // setGroupList(res?.result?.spuBrandByName || []);
      });
    },
  });

  useEffect(() => {
    run({ current: 1, size: 50, projectGroupStatusEnum: 'ENABLE', deptId: deptId || undefined });
  }, [deptId]);

  const handleGroupChange = (v: string) => {
    if (!v) {
      run({ current: 1, size: 50, projectGroupStatusEnum: 'ENABLE', deptId: deptId || undefined });
    }
  };

  const onSearchGroup = debounce((name: string) => {
    run({
      current: 1,
      size: 50,
      projectGroupName: name,
      projectGroupStatusEnum: 'ENABLE',
      deptId: deptId || undefined,
    });
  }, 500);
  const onSearchGroupByDept = debounce((departmentName: string) => {
    run({
      current: 1,
      size: 50,
      departmentName,
      projectGroupStatusEnum: 'ENABLE',
      deptId: deptId || undefined,
    });
  }, 500);
  const onSearchGroupByDeptId = debounce((deptId: string) => {
    run({ current: 1, size: 50, deptId: deptId || undefined, projectGroupStatusEnum: 'ENABLE' });
  }, 500);
  const handleGroupBlur = () => {
    run({ current: 1, size: 50, projectGroupStatusEnum: 'ENABLE', deptId: deptId || undefined });
  };

  return {
    handleGroupChange,
    groupList,
    loading,
    onSearchGroup,
    onSearchGroupByDept,
    handleGroupBlur,
    onSearchGroupByDeptId,
    getList: run,
  };
};
