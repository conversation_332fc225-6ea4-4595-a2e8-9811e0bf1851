import { useRequest } from 'ahooks';
import {
  businessOpportunityPage,
  BusinessOpportunityPageRequest,
  BusinessOpportunityPageResult,
} from '../services';
import usePagination from '@/hooks/usePagination';
import { useState, useEffect } from 'react';
import { handleResponse } from '@/utils/response';

type LIST = Required<BusinessOpportunityPageResult['records']>;

export const useTableList = ({ currentKey }: { currentKey: string }) => {
  // 列表数据
  const [dataSource, setDataSource] = useState<LIST>();
  // 保存搜索数据
  const [condition, setCondition] = useState<BusinessOpportunityPageRequest>();

  const { pagination, setPagination } = usePagination({
    current: 1,
    size: 20,
  });
  let didCancel = false;

  const { loading, run } = useRequest(businessOpportunityPage, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        if (didCancel) return;
        const { records = [], total, current, size, pages } = res.result;
        if (!records?.length && pages && (current || 0) > (pages || 0)) {
          run({
            ...condition,
            current: pages,
            size: size,
          });
          return;
        }
        setPagination({
          total,
          current: current,
          size,
        });
        setDataSource(records as LIST);
      });
    },
  });

  // 分页修改
  const onPageChange = (current: number, size: number) => {
    setPagination({ current, size, total: pagination.total });
    run({ ...condition, current: current, size });
  };

  // 搜索
  const onSearch = (value: BusinessOpportunityPageRequest) => {
    console.log('value', value);
    setCondition({ ...value, queryType: currentKey as 'ALL' | 'PUBLIC_POOL' });
    setPagination({
      current: 1,
      size: pagination.size,
    });
    run({
      ...value,
      queryType: currentKey as 'ALL' | 'PUBLIC_POOL',
      current: 1,
      size: pagination.size,
    });
  };

  // 刷新当前页面
  const onRefresh = () => {
    run({
      ...condition,
      current: pagination.current,
      size: pagination.size,
    });
  };

  useEffect(() => {
    onSearch({});
    return () => {
      didCancel = true;
    };
  }, [currentKey]);

  return {
    dataSource,
    // setDataSource,
    condition,
    // setCondition,
    pagination,
    // setPagination,
    loading,
    // run,
    onPageChange,
    onSearch,
    onRefresh,
  };
};
