import React from 'react';
import styles from '@/styles/index.module.less';
import { ColumnProps } from 'antd/es/table';
import PopoverRowText from '@/components/PopoverRowText';
import moment from 'moment';
import style from '../index.module.less';
import { Popover, Icon, Tag } from 'antd';
import { copyText } from '@/utils/moduleUtils';
import IDIMG from '@/assets/id.png';
import JingGao from '@/assets/jinggao.png';
import YBS from '@/assets/ybs.png';
import { newRenderPlatformSourceLogo } from '@/common/constants/platform';
import {
  SELECT_STEP_COLOR,
  SELECT_STEP_NAME,
  FlowStatus_ChoiceList as FlowStatus_ChoiceList_Enum,
} from '@/pages/selection-flow-board/types';
import { PAY_DAY_ABBREVIATE, PAY_DAY } from '@/pages/quality-assurance-cooperation/types';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import { STAGE_NAME, OPPORTUNITY_TYPE_NAME, OPPORTUNITY_TYPE_ENUM } from '../types';
import { history } from 'qmkit';

export const useTable = () => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, record: any, index: number) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '线索编号',
      key: 'businessOpportunityLeadNo',
      dataIndex: 'businessOpportunityLeadNo',
      width: 120,
      render: (val: string, record: any) => (
        <a
          onClick={() => {
            if (!record?.businessOpportunityLeadId || !val) {
              return;
            }
            history.push(`/business-leads-detail?id=${record?.businessOpportunityLeadId}`);
          }}
        >
          {val || '-'}
        </a>
      ),
    },
    {
      title: '商机编号',
      key: 'no',
      dataIndex: 'no',
      width: 120,
      render: (val: string, record: any) => (
        <a
          onClick={() => {
            history.push(`/opportunity-management-detail?id=${record?.id}`);
          }}
        >
          {val}
        </a>
      ),
    },
    {
      title: '品牌名称',
      key: 'brandName',
      dataIndex: 'brandName',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '合作方式',
      key: 'cooperationTypeDTO',
      dataIndex: 'cooperationTypeDTO',
      width: 120,
      render: (val: any[]) => {
        const str = val?.map((item) => item?.cooperationTypeDesc) || [];
        return <PopoverRowText text={str.join(',') || '-'} />;
      },
    },
    {
      title: '商机阶段',
      key: 'stage',
      dataIndex: 'stage',
      width: 120,
      render: (val: string) => (
        <PopoverRowText text={STAGE_NAME[val as keyof typeof STAGE_NAME] || '-'} />
      ),
    },
    {
      title: '来源渠道',
      key: 'sourceChannelDesc',
      dataIndex: 'sourceChannelDesc',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '商机类型',
      key: 'businessOpportunityType',
      dataIndex: 'businessOpportunityType',
      width: 120,
      render: (val: OPPORTUNITY_TYPE_ENUM) => OPPORTUNITY_TYPE_NAME[val],
    },
    {
      title: '联系人',
      key: 'contactPerson',
      dataIndex: 'contactPerson',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '联系方式',
      key: 'contactMobile',
      dataIndex: 'contactMobile',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '合作主体',
      key: 'cooperationSubject',
      dataIndex: 'cooperationSubject',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '建联日期',
      key: 'contactDate',
      dataIndex: 'contactDate',
      width: 120,
      render: (val: string) => (val ? moment(val).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '跟进人',
      key: 'followerName',
      dataIndex: 'followerName',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    // {
    //   title: '跟进日期',
    //   key: 'lastFollowerChangeTime',
    //   dataIndex: 'lastFollowerChangeTime',
    //   width: 120,
    //   render: (val: string) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
    // },
    {
      title: '所属事业部',
      key: 'followerDeptName',
      dataIndex: 'followerDeptName',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '创建人',
      key: 'creatorName',
      dataIndex: 'creatorName',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '创建时间',
      key: 'gmtCreated',
      dataIndex: 'gmtCreated',
      width: 120,
      render: (val: string) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
    {
      title: '更新人',
      key: 'modifierName',
      dataIndex: 'modifierName',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '更新时间',
      key: 'gmtModified',
      dataIndex: 'gmtModified',
      width: 120,
      render: (val: string) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
    },
  ];
  return { columns };
};

export const useSessionTable = ({ handleDelete }: { handleDelete: (id: string) => void }) => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, record: any, index: number) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '直播间',
      key: 'liveRoomName',
      dataIndex: 'liveRoomName',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '项目组',
      key: 'groupName',
      dataIndex: 'groupName',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '是否罗场',
      key: 'luoFlag',
      dataIndex: 'luoFlag',
      width: 120,
      render: (val: string) => (val ? '是' : '否'),
    },
    {
      title: '直播日期',
      key: 'liveDate',
      dataIndex: 'liveDate',
      width: 120,
      render: (val: string) => (val ? moment(val).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '直播时段',
      key: 'liveSessionTimesDesc',
      dataIndex: 'liveSessionTimesDesc',
      width: 120,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (val: string, record: any) => (
        <a style={{ color: '#ee0000' }} onClick={() => handleDelete(record?.id)}>
          删除
        </a>
      ),
    },
  ];
  return { columns };
};

export const useBroadcastTable = ({
  handleDel,
  isBtn = true,
}: {
  handleDel?: (params: any) => void;
  isBtn?: boolean;
}) => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, record: any, index: number) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '图片',
      key: 'image',
      dataIndex: 'image',
      width: 80,
      render: (image: string) => (
        <>
          {image ? (
            <img src={image} className={styles['table-img']} style={{ objectFit: 'cover' }} />
          ) : (
            '-'
          )}
        </>
      ),
    },
    {
      title: '商品信息',
      key: 'goodsMsg',
      dataIndex: 'goodsMsg',
      width: 204,
      render: (_: any, records: any) => {
        return (
          <div>
            <div className={style['goods-name']}>
              {/* 显示详情 */}
              <Popover
                title="商品名称"
                content={
                  <>
                    {records?.spuName || '-'}
                    <Icon
                      onClick={() => {
                        copyText(records?.spuName || '');
                      }}
                      type="copy"
                      style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                    />
                  </>
                }
              >
                <p className={style['goods-name-line']} style={{ marginRight: '4px' }}>
                  【{records?.brandName || '-'}】{records?.spuName || '-'}
                </p>
              </Popover>
              <Popover
                title="商品信息"
                content={
                  <div>
                    <p>
                      商品编号: {records?.spuNo || '-'}
                      <Icon
                        onClick={() => {
                          copyText(records?.spuNo || '');
                        }}
                        type="copy"
                        style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                      />
                    </p>
                    <p>
                      平台商品ID: {records?.platformSpuId || '-'}
                      <Icon
                        onClick={() => {
                          copyText(records?.platformSpuId || '');
                        }}
                        type="copy"
                        style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                      />
                    </p>
                    <p>
                      选品编号: {records?.selectionNo || '-'}
                      <Icon
                        onClick={() => {
                          copyText(records?.selectionNo || '');
                        }}
                        type="copy"
                        style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                      />
                    </p>
                  </div>
                }
              >
                <img
                  src={IDIMG}
                  style={{ width: '16px', height: '16px', marginLeft: '4px' }}
                  alt=""
                />
                {/* <span
                  className={`iconfont icon-id ${style['id-style']}`}
                  style={{
                    fontSize: '16px',
                    position: 'relative',
                    color: '#52C41A',
                    marginLeft: '4px',
                  }}
                ></span> */}
                {/* <div className={}>ID</div> */}
              </Popover>
            </div>
            <div style={{ color: '#EE0000' }}>
              {records?.minPrice === records?.maxPrice
                ? `¥${records?.minPrice}`
                : `¥${records?.minPrice} - ${records?.maxPrice}`}
            </div>
            <div className={style['company']}>
              <Popover
                content={
                  records?.serviceAgreementId
                    ? '已签署'
                    : '【未签署】商家未签署协议时不会触发法务审核流程，请商务尽快联系商家在合同管理中签署协议'
                }
              >
                {records?.serviceAgreementId ? (
                  <img
                    src={YBS}
                    style={{
                      width: '16px',
                      height: '16px',
                      marginRight: '4px',
                    }}
                  />
                ) : (
                  <img
                    src={JingGao}
                    style={{
                      width: '16px',
                      height: '16px',
                      marginRight: '4px',
                    }}
                  />
                )}
              </Popover>

              <div
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  window.open(`/provider-detail/${records?.supplierId}`);
                }}
                style={{ cursor: 'pointer' }}
              >
                {records?.supplierOrgName}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: '场次信息',
      key: 'session',
      dataIndex: 'session',
      width: 200,
      render: (_: any, records: any) => {
        return (
          <div>
            <div style={{ marginBottom: '8px' }}>
              {newRenderPlatformSourceLogo({
                platform: records?.platformSource as any,
                width: 14,
              })}
            </div>
            <div>{records?.liveRoundName || '-'}</div>
          </div>
        );
      },
    },
    {
      title: '流程状态',
      key: 'status',
      dataIndex: 'status',
      width: 100,
      render: (_: any) => {
        return _ ? (
          <Tag color={SELECT_STEP_COLOR[_ as keyof typeof FlowStatus_ChoiceList_Enum]}>
            {SELECT_STEP_NAME[_ as keyof typeof FlowStatus_ChoiceList_Enum]}
          </Tag>
        ) : (
          <></>
        );
      },
    },
    {
      title: '商务条件',
      key: 'business',
      dataIndex: 'business',
      width: 170,
      render: (_: any, records: any) => {
        return (
          <div>
            <p>
              总佣金(含保量):
              {records?.hideCommissionFlag ? (
                ' *** %'
              ) : (
                <>
                  {records?.totalCommissionContainGuaranteed
                    ? `${(Number(records?.totalCommissionContainGuaranteed) * 100).toFixed(2)}%`
                    : '0.00%'}
                </>
              )}
            </p>
            <p>
              线上佣金:
              {records?.hideCommissionFlag ? (
                ' *** %'
              ) : (
                <>
                  {records?.commissionRate
                    ? `${(Number(records?.commissionRate) * 100).toFixed(2)}%`
                    : '0.00%'}
                </>
              )}
            </p>
            {/* 为0时不在列表页做展示  */}
            {records?.commissionRateOffline ? (
              <p>
                线下佣金:
                {records?.hideCommissionFlag ? ( // 根据后端返回字段判断显示隐藏
                  ' *** %'
                ) : (
                  <>{`${(Number(records?.commissionRateOffline) * 100).toFixed(2)}%`}</>
                )}
              </p>
            ) : (
              <p>线下佣金: 0.00%</p>
            )}
            <>
              <p>
                保量基础佣金:
                {records?.hideCommissionFlag ? (
                  ' *** %'
                ) : (
                  <>
                    <span>
                      {isNullOrUndefined(
                        (records as any)?.cooperationGuaranteed?.guaranteeBrandFeeRate,
                      )
                        ? '-'
                        : `${(
                            (records as any)?.cooperationGuaranteed?.guaranteeBrandFeeRate * 100
                          ).toFixed(2)}%`}
                    </span>
                    <span>
                      {(records as any)?.cooperationGuaranteed?.payDurationType &&
                        `(${
                          PAY_DAY_ABBREVIATE[
                            (records as any)?.cooperationGuaranteed?.payDurationType as PAY_DAY
                          ]
                        })`}
                    </span>
                  </>
                )}
              </p>
              <p>
                基础服务费:
                {records?.hideCommissionFlag ? ' *** ' : <>{records?.brandFee || 0}</>}
              </p>
              {/* 为0时不在列表页做展示 */}
              {records?.sectionFee ? (
                <p>
                  切片费:
                  {records?.hideCommissionFlag ? ' *** ' : <>{records?.sectionFee || 0}</>}
                </p>
              ) : (
                <></>
              )}
            </>
          </div>
        );
      },
    },
  ];

  const btn = [
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (val: string, record: any) => (
        <a style={{ color: '#ee0000' }} onClick={() => handleDel?.({ id: record?.id })}>
          删除
        </a>
      ),
    },
  ];

  return { columns: isBtn ? [...columns, ...btn] : columns };
};
