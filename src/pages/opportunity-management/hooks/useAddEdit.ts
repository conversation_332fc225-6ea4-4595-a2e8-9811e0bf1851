import { useRequest } from 'ahooks';
import {
  businessOpportunityCreate,
  businessOpportunityUpdate,
  businessOpportunityDetail,
  BusinessOpportunityDetailResult,
  businessOpportunityGetContact,
} from '../services';
import { handleResponse } from 'web-common-modules/utils/response';
import { message } from 'antd';
import { useState } from 'react';
import { WrappedFormUtils } from 'antd/es/form/Form';
import moment from 'moment';
import { checkAuth } from 'qmkit';

export const useAddEdit = ({
  onRefresh,
  form,
  isContact = true,
}: {
  onRefresh?: () => void;
  form?: WrappedFormUtils;
  isContact?: boolean;
}) => {
  const [detailData, setDetailData] = useState<BusinessOpportunityDetailResult>({});
  // const [changePhone, setChangePhone] = useSetState<{
  //   phone: string;
  // }>({
  //   phone: '',
  // });

  const { run: detailRun, loading: detailLoading } = useRequest(businessOpportunityDetail, {
    manual: true,
    onSuccess({ res }) {
      handleResponse(res).then((res) => {
        if (res?.success) {
          setDetailData(res?.result || {});
          if (
            isContact &&
            (checkAuth('f_opportunity_management_all_see') || res?.result?.follower)
          ) {
            getContact({
              id: res?.result?.id,
            });
          }
          const {
            brandName,
            contactPerson,
            cooperationSubject,
            sourceChannel,
            contactDate,
            follower,
            followerName,
            attachments,
            remark,
            sourceChannelDesc,
            businessOpportunityType,
            cooperationTypeDTO,
          } = res?.result || {};

          const attachmentsList = attachments?.map((item: any) => ({
            url: item,
          }));
          if (!form) {
            return;
          }

          form?.setFieldsValue({
            brandName,
            contactPerson,

            cooperationSubject,
            contactDate: contactDate ? moment(contactDate) : undefined,
            follower: follower ? { key: follower, label: followerName } : undefined,
            attachments: attachmentsList,
            remark,
            sourceChannel: {
              key: sourceChannel,
              label: sourceChannelDesc,
            },
            businessOpportunityType,
            cooperationType: cooperationTypeDTO?.map((item: any) => ({
              key: item?.cooperationType,
              label: item?.cooperationTypeDesc,
            })),
          });
        }
      });
    },
  });

  const { run: createRun, loading: createLoading } = useRequest(businessOpportunityCreate, {
    manual: true,
    onSuccess({ res }) {
      handleResponse(res).then((res) => {
        if (res?.success) {
          message.success('创建成功');
          onRefresh?.();
        }
      });
    },
  });

  const { run: updateRun, loading: updateLoading } = useRequest(businessOpportunityUpdate, {
    manual: true,
    onSuccess({ res }) {
      handleResponse(res).then((res) => {
        if (res?.success) {
          message.success('更新成功');
          onRefresh?.();
        }
      });
    },
  });

  const { run: getContact, loading: getContactLoading } = useRequest(
    businessOpportunityGetContact,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (!res?.success) {
          message.error(res?.message || '网络异常');
          return;
        }
        // setChangePhone({
        //   phone: res?.result?.contactMobile || '',
        // });
        form?.setFieldsValue({
          contactMobile: res?.result?.contactMobile || '',
        });
      },
    },
  );

  return {
    createRun,
    createLoading,
    updateRun,
    updateLoading,
    detailRun,
    detailLoading,
    detailData,
    getContactLoading,
  };
};
