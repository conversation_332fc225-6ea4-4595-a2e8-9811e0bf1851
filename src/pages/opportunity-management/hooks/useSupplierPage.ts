import React, { useState } from 'react';
import { supplierPage, SupplierPageRequest, SupplierPageResult } from '../services';
import { useRequest } from 'ahooks';
import { handleResponse } from 'web-common-modules/utils/response';
import { message } from 'antd';
import { debounce } from 'lodash';

export type GetEmployeePageByRoleResultRecords = Required<SupplierPageResult>['records'];

export const useSupplierPage = () => {
  const [supplierList, setSupplierList] = useState<GetEmployeePageByRoleResultRecords>([]);
  const { run: supplierPageRun, loading: supplierPageLoading } = useRequest(supplierPage, {
    manual: true,
    onSuccess({ res }) {
      handleResponse(res).then((res) => {
        if (res?.success) {
          const { records } = res?.result || {};
          setSupplierList(records || []);
        } else {
          message.warning(res?.message || '网络异常');
        }
      });
    },
  });

  const handleSupplierChange = (v: string) => {
    if (!v) {
      supplierPageRun({ current: 1, size: 10, type: 'CUSTOMER' });
    }
  };
  const handelSupplierSearch = debounce((companyName: string) => {
    if (companyName?.length >= 2) {
      supplierPageRun({ current: 1, size: 10, companyName, type: 'CUSTOMER' });
    }
  }, 500);
  const handleSupplierBlur = () => {
    supplierPageRun({ current: 1, size: 10, type: 'CUSTOMER' });
  };
  return {
    supplierList,
    supplierPageRun,
    supplierPageLoading,
    handleSupplierChange,
    handelSupplierSearch,
    handleSupplierBlur,
  };
};
