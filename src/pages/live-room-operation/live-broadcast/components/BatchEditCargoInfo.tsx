// ai生成
import React, { useEffect, useMemo, useState } from 'react';
import { ModalProps } from 'antd/lib/modal';
import WithToggleModal from '@/components/WithToggleModal';
import { Modal, Table, Button, message, Input, Select } from 'antd';
import PopoverRowText from '@/components/PopoverRowText';
import moment from 'moment';
import { useRequest } from 'ahooks';
import { ColumnProps } from 'antd/lib/table';
import { liveRoundBatchEditBase } from '../services';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { useLiveRoom } from '@/pages/session-limit-config/hooks';
import { DataSourceItemType } from '../constant';

interface IProps extends ModalProps {
  list: DataSourceItemType[];
  onRefresh: () => void;
  [key: string]: any;
}

const BatchEditCargoInfo = (props: IProps) => {
  const { visible, list, onRefresh, ...rest } = props;
  const { codeList: roundTagList } = useCode(CODE_ENUM.ROUND_TAG, {
    able: true,
  });
  const { liveRoomList, liveRoomRun } = useLiveRoom();
  const hangzongLiveRoomList = useMemo(() => {
    return liveRoomList?.filter((item) => item?.buName?.includes('杭州综合'));
  }, [liveRoomList]);

  const { run: updateRun, loading: updateLoading } = useRequest(liveRoundBatchEditBase, {
    manual: true,
    onSuccess({ res }) {
      if (!res?.success) {
        message.warning(res?.message || '网络异常');
        return;
      }
      handleCancel();
      message.success('操作成功');
      onRefresh();
    },
  });
  const [newList, setNewList] = useState<DataSourceItemType[]>([]);

  const columns = useMemo<ColumnProps<DataSourceItemType>[]>(
    () => [
      {
        title: '#',
        align: 'center',
        key: 'number',
        render: (_, __, index) => {
          return <span>{index + 1}</span>;
        },
        width: 50,
      },
      {
        title: '直播间',
        width: 100,
        key: 'liveRoomOpenName',
        dataIndex: 'liveRoomOpenName',
        render: (v) => <PopoverRowText text={v || '-'} />,
      },
      {
        title: '直播日期',
        key: 'liveDate',
        dataIndex: 'liveDate',
        width: 150,
        render: (v) => (v ? moment(v).format('YYYY-MM-DD') : '-'),
      },
      {
        title: '场次标签',
        key: 'roundTag',
        dataIndex: 'roundTag',
        width: 150,
        render: (_, __, index) =>
          hangzongLiveRoomList?.find(
            (item) => item?.name === newList?.[index]?.liveRoomOpenName,
          ) ? (
            <Select
              value={newList?.[index]?.roundTag?.split(',') ?? undefined}
              onChange={(value) => {
                console.log('value', value);
                const editList = [...newList];
                editList[index].roundTag = value?.length ? value.join(',').trim() : undefined;
                setNewList(editList);
              }}
              style={{ width: 120, marginLeft: '-10px' }}
              mode="multiple"
              allowClear
              maxTagCount={1}
            >
              {roundTagList?.map((item) => (
                <Select.Option key={item.label} value={item.label}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          ) : (
            <Input
              value={newList?.[index]?.roundTag}
              onChange={(e) => {
                const editList = [...newList];
                editList[index].roundTag = e.target.value;
                setNewList(editList);
              }}
              placeholder="请输入场次标签"
              maxLength={200}
              style={{ width: 120, marginLeft: '-10px' }}
            />
          ),
      },
      {
        title: '主题',
        key: 'subject',
        dataIndex: 'subject',
        width: 150,
        render: (_, __, index) => (
          <Input
            value={newList?.[index]?.subject}
            onChange={(e) => {
              const editList = [...newList];
              editList[index].subject = e.target.value;
              setNewList(editList);
            }}
            placeholder="请输入主题"
            maxLength={200}
            style={{ marginLeft: '-10px' }}
          />
        ),
      },
      {
        title: '货盘简述',
        key: 'roundDesc',
        dataIndex: 'roundDesc',
        width: 150,
        render: (_, __, index) => (
          <Input
            value={newList?.[index]?.roundDesc}
            onChange={(e) => {
              const editList = [...newList];
              editList[index].roundDesc = e.target.value;
              setNewList(editList);
            }}
            placeholder="请输入货盘简述"
            maxLength={200}
            style={{ marginLeft: '-10px' }}
          />
        ),
      },
      {
        title: '备注',
        key: 'remark',
        dataIndex: 'remark',
        width: 200,
        render: (_, __, index) => (
          <Input
            value={newList?.[index]?.remark}
            onChange={(e) => {
              const editList = [...newList];
              editList[index].remark = e.target.value;
              setNewList(editList);
            }}
            placeholder="请输入备注"
            maxLength={200}
            style={{ marginLeft: '-10px' }}
          />
        ),
      },
    ],
    [newList, roundTagList, hangzongLiveRoomList],
  );

  const handleCancel = () => {
    rest?.onCancel?.({} as any);
  };

  const handleSubmit = () => {
    const newEditLiveRoundRequests = newList?.map((item) => ({
      anchorType: item.anchorType,
      brandFeeGoal: item.brandFeeGoal,
      commissionGoal: item.commissionGoal,
      gmvGoal: item.gmvGoal,
      holeNum: item.holeNum,
      id: item.id,
      incomeGoal: item.incomeGoal,
      linkNum: item.linkNum,
      // liveCalendarDetails: item.liveCalendarDetails,
      // name: item.liveRoomOpenName,
      personInCharge: item.personInChargeId,
      remark: item.remark,
      roundDesc: item.roundDesc,
      roundTag: item.roundTag,
      subject: item.subject,
      talkNum: item.talkNum,
    }));
    updateRun({
      dataList: newEditLiveRoundRequests,
    });
  };
  useEffect(() => {
    setNewList(list);
    liveRoomRun({});
  }, [list]);
  return (
    <Modal
      title="批量编辑货盘信息"
      {...rest}
      visible={visible}
      width={1000}
      maskClosable={false}
      footer={
        <div>
          <Button onClick={handleCancel} loading={updateLoading}>
            取消
          </Button>
          <Button className="ml-10" type="primary" onClick={handleSubmit} loading={updateLoading}>
            确定
          </Button>
        </div>
      }
    >
      <Table
        columns={columns}
        dataSource={list}
        rowKey={'id'}
        pagination={false}
        scroll={{ x: 'auto', y: 400 }}
      />
    </Modal>
  );
};

export default WithToggleModal(BatchEditCargoInfo);
// 2024年3月21日 开山ai结尾共生成150行代码
