import React, { useEffect, useRef } from 'react';
import styles from '../index.module.less';
import { Form, Table } from 'antd';
import { useSearch, useTable, useList } from './hooks';
import { SearchForm } from './components';
import { useTableHeight } from '@/common/constants/hooks/index';
import { PaginationProxy } from 'web-common-modules/components';
import HighRiskDetail, { DetailRefType } from './components/HighRiskDetail';
import PageLayout from '@/components/PageLayout';
import { FormComponentProps } from 'antd/lib/form';

type PropsType = FormComponentProps;

const HighriskApprovalRecords: React.FC<PropsType> = ({ form }) => {
  const { options } = useSearch();
  const { tableHeight } = useTableHeight(80);
  const { list, loading, pagination, getList } = useList(form);
  const HighRiskDetailRef = useRef<DetailRefType>();
  // 结束
  const { columns, rowSelection } = useTable(HighRiskDetailRef, () => {
    getList({});
  });
  useEffect(() => {
    getList({});
  }, []);
  return (
    <PageLayout
      className={styles['cooperation-report-contain']}
      routePath="/high-risk-limit-detail"
    >
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            options={options}
            onSearch={() => {
              getList({ current: 1 });
            }}
            form={form}
          ></SearchForm>
        </div>
        <div className={styles.boardTable} style={{ flex: 1 }}>
          <Table
            columns={columns}
            pagination={false}
            dataSource={list}
            rowKey={'id'}
            scroll={{ y: tableHeight, x: '100%' }}
            rowSelection={rowSelection}
            loading={loading}
          />
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <PaginationProxy
            current={pagination?.current}
            pageSize={pagination?.size}
            total={pagination?.total}
            // @ts-ignore
            onChange={(current, size) => {
              getList({
                current,
                size,
              });
            }}
            valueType="flatten"
          />
        </div>
      </div>
      <HighRiskDetail onRef={HighRiskDetailRef} />
    </PageLayout>
  );
};

export default Form.create()(HighriskApprovalRecords);
