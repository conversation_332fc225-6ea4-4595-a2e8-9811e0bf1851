import { DatePicker, Form, Input, message, Select } from 'antd';
import { WrappedFormUtils } from 'antd/es/form/Form';
import React, { useEffect, useState } from 'react';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';

import {
  auditOrderStatusList,
  auditTypeList,
  goodsResourceList,
} from '@/common/constants/moduleConstant';
import { fetchReasonTypeList } from '../services/cooperationAdjustOrder';

const Option = Select.Option;
const RangePicker = DatePicker.RangePicker;
interface SearchFormProps {
  form: WrappedFormUtils;
  onSearch: Function;
  loading: boolean;
  onReset: () => void;
}

const SearchForm = ({ form, onSearch, onReset, loading }: SearchFormProps) => {
  const [reasonList, setReasonList] = useState([{ reasonId: -1, reasonName: '其他' }]);
  useEffect(() => {
    fetchReasonTypeList().then((res) => {
      if (res?.res?.success) {
        const result = res?.res?.result;
        for (let i = 0; i < result?.length; i++) {
          const tmp = { reasonId: i, reasonName: result[i] };
          reasonList.push(tmp);
        }
      } else {
        message.error(res?.errMsg || '获取原因列表失败');
      }
    });
  }, []);
  const options: Record<string, searchItem> = {
    no: {
      label: '合作单号/调整单号',
      renderNode: <Input placeholder="完整合作订单号/调整单号" />,
    },
    type: {
      label: '调整类型',
      renderNode: (
        <Select placeholder="全部" allowClear={true}>
          {auditTypeList.map((e) => (
            <Option value={e.value} key={e.value}>
              {e.name}
            </Option>
          ))}
        </Select>
      ),
    },
    spuName: {
      label: '商品名称',
      renderNode: <Input placeholder="商品名称关键词" />,
    },
    statusList: {
      label: '状态',
      renderNode: (
        <Select placeholder="全部" style={{ marginTop: '6px' }} mode="multiple">
          {auditOrderStatusList.map((e) => (
            <Option value={e.value} key={e.value}>
              {e.name}
            </Option>
          ))}
        </Select>
      ),
    },
    spuNo: {
      label: '商品编号',
      renderNode: <Input placeholder="完整商品编号" />,
    },
    doudianGoodsId: {
      label: '平台商品ID',
      renderNode: <Input placeholder="完整平台商品ID" />,
    },
    storeName: {
      label: '商家名称',
      renderNode: <Input placeholder="商家名称关键词" />,
    },
    storeNo: {
      label: '商家编号',
      renderNode: <Input placeholder="完整商家编号" />,
    },
    reasonType: {
      label: '调整原因',
      renderNode: (
        <Select
          showSearch
          placeholder="全部"
          notFoundContent="暂无原因"
          allowClear={true}
          optionFilterProp="children"
          filterOption={(input, option: any) => {
            return typeof option.props.children === 'string'
              ? option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              : true;
          }}
        >
          {reasonList.map((item) => {
            return (
              <Select.Option key={item.reasonId} value={item.reasonName}>
                {item.reasonName}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    applyTime: {
      label: '申请时间',

      renderNode: <RangePicker />,
    },
    talentName: {
      label: '达人昵称',
      renderNode: <Input placeholder="达人昵称" />,
    },
    talentNo: {
      label: '达人编码',
      renderNode: <Input placeholder="达人编码" />,
    },
    operateTime: {
      label: '操作时间',

      renderNode: <RangePicker />,
    },
    platform: {
      label: '平台',
      renderNode: (
        <Select placeholder="全部" allowClear>
          {goodsResourceList.map((_status) => (
            <Select.Option value={_status.value} key={_status.value}>
              {_status.name}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    deptName: {
      label: '事业部',
      renderNode: <Input placeholder="事业部" />,
    },
  };

  const transformSearchData = (params: any) => {
    return params;
  };

  const onSearchTable = () => {
    const params = form.getFieldsValue();
    onSearch(transformSearchData(params));
  };

  const onResetTable = () => {
    form.resetFields();
    onReset();
  };

  return (
    <SearchFormComponent
      onSearch={onSearchTable}
      onReset={onResetTable}
      needMore={true}
      form={form}
      options={options}
      showRow={2}
      loading={loading}
    />
  );
};

export default Form.create<SearchFormProps>()(SearchForm);
