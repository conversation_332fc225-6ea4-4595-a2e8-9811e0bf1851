import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Badge, Divider, Icon, message, Modal, Popover, Tag, Tooltip, Popconfirm } from 'antd';
import {
  cooperationOrderListTalentHeader,
  paymentStatusNode,
} from 'web-common-modules/biz/Order/RooperationOrder';
import { OneAuthWrapper, AuthWrapper, Const, history } from 'qmkit';
import { CardTable, PaginationProxy } from 'web-common-modules/components';
import { SYS_TYPE } from 'web-common-modules/constant';
import { renderEllipsis } from 'web-common-modules/utils/tool';
import styles from './index.module.less';
import moment from 'moment';
import copy from 'copy-to-clipboard';
import TagProxy from 'web-common-modules/components/Tag';
import { PAY_TYPE } from 'web-common-modules/components/EbPay';
import { classNames } from 'web-common-modules/utils';
import { dealDecimalToPercent } from 'web-common-modules/utils/string';
import {
  NonLiveOrderStatusText,
  NonLiveServiceTypeText,
  NonLiveOrderStatusBadge,
  NonLiveServiceTypeColor,
  NonLiveOrderStatus,
} from './const';
import { useConfiguration, ConfigurationEnum } from 'web-common-modules/hooks/useConfiguration';
import { cancelNonLiveCooperation } from './services/yml';
import { useRequest } from 'ahooks';
import { OSSImagePre } from 'web-common-modules/constant';

// 付款方式
export const getPayType = () => {
  return ['production', 'gray'].includes(Const.NODE_SERVER_ENV)
    ? [PAY_TYPE.EBANK_B2B, PAY_TYPE.OFF_LINE_BANK_TRANSFER]
    : [PAY_TYPE.EBANK_B2B, PAY_TYPE.EBANK_B2C, PAY_TYPE.OFF_LINE_BANK_TRANSFER];
};

interface iCooperationOrder<T> {
  /** 直播场次id */
  liveId?: string;
  isLivingCalendarDetail?: boolean;
  coopOrderNo?: string;
  [k: string]: any;
  refreshList: Function;
  dataSource: T[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  onPaginationChange: Function;
  onSearch?: Function;
  loading?: boolean;
  isHideTechFee?: boolean;
  functionList?: any[];
}

export default function NonLiveOrderContent<T = Record<string, any>>(props: iCooperationOrder<T>) {
  const { onPaginationChange, dataSource, pagination, loading = false, refreshList } = props;

  const { data: configFunctionData } = useConfiguration(SYS_TYPE.ORGANIZATION);
  const isHideTechFee = useMemo(() => {
    if (!configFunctionData?.length) return false;
    return configFunctionData?.some(
      (item) => item?.code === ConfigurationEnum.PLATFORM_TECH_FEES_HIDE,
    );
  }, [configFunctionData]);

  const serviceFee = (lp: string, temRate: number) => {
    const temPrice = lp?.split('~');
    //
    if (temPrice.length == 2) {
      return `¥${(temPrice[0] * temRate).toFixed(2)} ~ ¥${(temPrice[1] * temRate).toFixed(2)}`;
    } else if (temPrice.length == 1) {
      return `¥ ${(temPrice[0] * temRate).toFixed(2)}`;
    }
  };

  const coopPriceWasher = (lp: string) => {
    const temPrice = lp?.split('~');
    if (!temPrice?.length) return [];
    if (temPrice.length > 1) {
      const tmp = `¥${temPrice[1]}`;
      temPrice.push(tmp);
      temPrice[1] = ` ~ `;
    }
    temPrice[0] = '¥' + temPrice[0];
    return temPrice;
  };

  const AlinItemTopStyle = {
    display: 'inline-block',
  };

  const { run: cancelNonLiveCooperationRun } = useRequest(cancelNonLiveCooperation, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res?.success) {
        message.success('取消成功');
        refreshList();
        return;
      }
      message.error(res?.message || '请求失败，请重试');
    },
  });

  const NonLiveColumns = [
    {
      title: '商品信息',
      key: 'orderInfo',
      render: (record: T) => {
        return (
          <div className={classNames(styles.orderInfo)} style={{ paddingLeft: '-20px' }}>
            <img
              src={record?.spuId == -1 ? `${OSSImagePre}/icon/empty-goods.png` : record.spuImg}
            />
            {record?.spuId == -1 ? (
              <p className={`ml-8 ${styles.emptyGoods}`}>以商家提供给达人方上架的商品链接为准</p>
            ) : (
              <div
                className={classNames(styles.detail, 'pl-8')}
                style={{ display: 'flex', flexDirection: 'column' }}
              >
                <p className={styles.spuName}>{record.spuName || '-'}</p>

                <p className={styles.coopPrice}>
                  {coopPriceWasher(record.coopPrice).map((item) => (
                    <span style={{ marginRight: 3 }}>{item}</span>
                  ))}
                </p>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginTop: 2,
                    color: '#999999',
                    fontSize: 12,
                  }}
                >
                  <span
                    onClick={() => {
                      if (record.spuNo && copy(record.spuNo)) {
                        message.success('复制商品编码成功');
                      } else {
                        message.info('暂无商品编码');
                      }
                    }}
                    style={{ cursor: 'pointer' }}
                  >
                    <Tooltip title={record?.spuNo || '-'} overlayClassName={styles.titleTooltip}>
                      商品编码
                    </Tooltip>
                    <Icon
                      type="copy"
                      className="ml-4"
                      theme="outlined"
                      style={{ color: '#999999' }}
                    />
                  </span>
                  <Divider type="vertical" />
                  <span
                    onClick={() => {
                      if (record.spuDoudianGoodsId && copy(record.spuDoudianGoodsId)) {
                        message.success('复制抖店商品ID成功');
                      } else {
                        message.info('暂无抖店商品ID');
                      }
                    }}
                    style={{ cursor: 'pointer', marginTop: 0 }}
                  >
                    <Tooltip
                      title={record?.spuDoudianGoodsId || '-'}
                      overlayClassName={styles.titleTooltip}
                    >
                      平台商品ID
                    </Tooltip>
                    <Icon
                      type="copy"
                      className="ml-4"
                      theme="outlined"
                      style={{ color: '#999999' }}
                    />
                  </span>
                </div>
              </div>
            )}
          </div>
        );
      },
      width: 400,
    },
    {
      key: 'publishFee',
      title: '推广服务费',
      // (
      //   <span>
      //     推广服务费
      //     {![SYS_TYPE.TALENT].includes(Const.SYS_TYPE) && (
      //       <Tooltip
      //         title="如果设置了机构基础服务费分成比例，基础服务费就包含机构和达人两部分的金额"
      //         overlayClassName={styles.titleTooltip}
      //       >
      //         <Icon type="question-circle" style={{ marginLeft: '4px' }} />
      //       </Tooltip>
      //     )}
      //   </span>
      // )
      render: (record) => {
        return (
          <div className={styles.orderInfo} style={AlinItemTopStyle}>
            <div className={styles.detail}>
              <p className={styles.desc}>
                达人方佣金比例：
                <span className={styles.spuFee}>
                  {`${dealDecimalToPercent(record.talentCommissionRate, 2)}` || '线下沟通为准'}
                  {SYS_TYPE.TALENT === Const.SYS_TYPE &&
                    ` ( ${serviceFee(record.coopPrice, record.talentCommissionRate) || '-'} )`}
                </span>
              </p>
              <p className={styles.desc}>
                单号：
                <span className={styles.spuFee}>{record.coopOrderNo || '-'}</span>
              </p>
            </div>
          </div>
        );
      },
      width: 285,
    },
    {
      key: 'techFee',
      title: (
        <span>
          技术服务费
          <Tooltip
            title="平台收取的技术佣金和基础技术服务费"
            overlayClassName={styles.titleTooltip}
          >
            <Icon type="question-circle" style={{ marginLeft: '4px' }} />
          </Tooltip>
        </span>
      ),
      render: (record) => {
        return (
          <div className={styles.orderInfo} style={AlinItemTopStyle}>
            <div className={styles.detail}>
              <p className={styles.desc}>
                技术服务费佣金比例：
                <span className={styles.spuFee}>
                  {`${dealDecimalToPercent(record.platformCommissionRate, 2)}` || '-'}
                </span>
              </p>
              <p className={styles.desc}>
                单号：
                <span className={styles.spuFee}>{record.serviceChargeNo || '-'}</span>
              </p>
            </div>
          </div>
        );
      },
      width: 300,
    },
    // {
    //   key: 'paymentStatus',
    //   title: (
    //     <span>
    //       付款状态
    //       <Tooltip title="逾期时间将在直播日期过后开始计算" overlayClassName={styles.titleTooltip}>
    //         <Icon type="question-circle" style={{ marginLeft: '4px' }} />
    //       </Tooltip>
    //     </span>
    //   ),
    //   render: (record) => {
    //     return paymentStatusNode(record);
    //   },
    //   width: 210,
    // },
    {
      title: '合作状态',
      key: 'status',
      render: (record) => {
        return (
          <div className={styles.orderInfo} style={AlinItemTopStyle}>
            <div className={styles.detail}>
              <p className={styles.title}>
                <Badge status={NonLiveOrderStatusBadge[record.status]} />
                {NonLiveOrderStatusText[record.status] || '--'}
              </p>
            </div>
          </div>
        );
      },
      width: 230,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (record) => {
        return (
          <div className={styles.orderInfo} style={AlinItemTopStyle}>
            <div className={styles.operatorBtn}>
              <OneAuthWrapper functionName="f_op_non_live_oder_detail,f_sp_non_live_oder_detail,f_jg_non_live_oder_detail">
                <a
                  onClick={() =>
                    [SYS_TYPE.JGPY].includes(Const.SYS_TYPE)
                      ? history.push(
                          `/non-live-order-cooperation/${record.coopOrderNo}?platform=${record.platform}`,
                        )
                      : window.open(
                          `/non-live-order-cooperation/${record.coopOrderNo}?platform=${record.platform}`,
                        )
                  }
                >
                  详情
                </a>
              </OneAuthWrapper>
              {Const.SYS_TYPE == SYS_TYPE.SUPPLIER &&
                record?.status == NonLiveOrderStatus.TALENT_CONFIRMING && (
                  <AuthWrapper functionName="f_sp_non_live_order_cancel">
                    <Popconfirm
                      title="确认要取消合作信息吗？"
                      onConfirm={() => cancelNonLiveCooperationRun({ orderId: record?.id })}
                    >
                      <a>取消</a>
                    </Popconfirm>
                  </AuthWrapper>
                )}
            </div>
          </div>
        );
      },
    },
  ].filter((column) => {
    const { key } = column;
    switch (key) {
      case 'techFee':
        return Const.SYS_TYPE !== SYS_TYPE.TALENT && !isHideTechFee;
      default:
    }

    return true;
  });

  const headerHandler = (record: T) => {
    let defaultDom;
    switch (Const.SYS_TYPE) {
      case SYS_TYPE.TALENT:
        defaultDom = (
          <>
            {renderEllipsis(`${record?.storeName || '-'}`, {
              maxLen: 20,
              defaultValue: '-',
              tooltip: (dom: any) => {
                return (
                  <Tooltip overlayClassName={styles.titleTooltip} title={record?.storeName || '-'}>
                    {dom}
                  </Tooltip>
                );
              },
            })}
            <Divider type="vertical" className={styles.divider} />
          </>
        );
      default:
        defaultDom = cooperationOrderListTalentHeader(record);
    }

    return (
      <div className={styles.cardHeader}>
        <TagProxy type={NonLiveServiceTypeColor[record.serviceType]}>
          {NonLiveServiceTypeText[record.serviceType]}
        </TagProxy>
        {defaultDom}
        <div className={styles.itemHeaderSuffix}>
          <span className={styles.createTime}>{`${moment(record.gmtCreated)
            .format(Const.TIME_FORMAT)
            .toString()} 创建`}</span>
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className={styles.nonLiveOrderList}>
        <CardTable
          loading={loading}
          cardColumns={NonLiveColumns}
          dataSource={dataSource}
          cardItemHeaderRender={(record) => headerHandler(record)}
        />
        <PaginationProxy
          showTotal={(total: number) => `共 ${pagination.total} 条`}
          {...pagination}
          showSizeChanger={false}
          onChange={onPaginationChange}
        />
      </div>
    </div>
  );
}
