import React from 'react';

export const diffTwoDay = (date) => {
  //如果时间格式是正确的，那下面这一步转化时间格式就可以不用了
  let _date = date;
  const userAgent = navigator.userAgent;
  if (!!_date && userAgent.indexOf('Safari') > -1) {
    _date = date.replace(/-/g, '/');
  }
  const dateEnd = new Date(_date);
  const dateBegin = new Date(); //获取当前时间
  const dateDiff = dateEnd.getTime() - dateBegin.getTime(); //时间差的毫秒数
  // let leave1=dateDiff%(24*3600*1000)    //计算天数后剩余的毫秒数
  const hours = Math.floor(dateDiff / (3600 * 1000)); //计算出小时数
  //计算相差分钟数
  const leave2 = dateDiff % (3600 * 1000); //计算小时数后剩余的毫秒数/
  const minutes = Math.floor(leave2 / (60 * 1000)); //计算相差分钟数
  //计算相差秒数
  const leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
  const seconds = Math.round(leave3 / 1000);
  // console.log(" 相差 "+hours+"小时 "+minutes+" 分钟"+seconds+" 秒")
  return { hours, seconds, minutes };
};
export function CountDown({ hours = 0, minutes = 0, seconds = 0 }) {
  const [paused, setPaused] = React.useState(false);
  const [over, setOver] = React.useState(false);
  // time 默认值是一个 object
  const [time, setTime] = React.useState(() =>
    hours < 0 || minutes < 0 || seconds < 0
      ? {
          hours: 0,
          minutes: 0,
          seconds: 0,
        }
      : {
          hours: parseInt(hours),
          minutes: parseInt(minutes),
          seconds: parseInt(seconds),
        },
  );

  const tick = () => {
    // 暂停，或已结束
    if (paused || over) return;
    if (time.hours === 0 && time.minutes === 0 && time.seconds === 0) setOver(true);
    else if (time.minutes === 0 && time.seconds === 0)
      setTime({
        hours: time.hours - 1,
        minutes: 59,
        seconds: 59,
      });
    else if (time.seconds === 0)
      setTime({
        hours: time.hours,
        minutes: time.minutes - 1,
        seconds: 59,
      });
    else
      setTime({
        hours: time.hours,
        minutes: time.minutes,
        seconds: time.seconds - 1,
      });
  };

  React.useEffect(() => {
    // 执行定时
    const timerID = setInterval(() => tick(), 1000);
    // 卸载组件时进行清理
    return () => clearInterval(timerID);
  });

  return (
    <div style={{ color: '#ffcc00' }}>
      <p>{`${time.hours.toString().padStart(2, '0')}时${time.minutes
        .toString()
        .padStart(2, '0')}分${time.seconds.toString().padStart(2, '0')}秒`}</p>
    </div>
  );
}
