import { Form, message, Modal } from 'antd';
import { FormProps } from 'antd/lib/form';
import { ModalProps } from 'antd/lib/modal';
import { Fetch, OSSUpload } from 'qmkit';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PDFPreview from 'web-common-modules/components/PDFPreview';

import { getPaymentCertificateOp } from './services/cooperation';

import styles from './index.module.less';
import { useModalAction } from './hook';

const MAX_UPLOAD = 6;
const certPng = require('./certPreview.png');

interface iCertificateProps {
  record: {
    id: number;
    investmentTitle: string;
    liveName: string;
    paymentVoucher: string;
  };
}

export default Form.create({ name: 'CertificateModal' })(function CertificateModal(
  props: ModalProps & FormProps & iCertificateProps,
) {
  const { visible, record, onOk } = props;
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState('');

  const { visible: previewVisible, onCancel: handleCancel, openModal } = useModalAction({});
  const onUploadChange = useCallback((fileList) => {
    setFileList(fileList);
  }, []);
  // const { visible: previewVisible, onCancel, openModal } = useModalAction({});
  const onUploadCert = useCallback(() => {
    const uploadFile = fileList
      .filter((el) => el.resourceId)
      ?.map((el) => el.resourceId)
      ?.join(';');
    if (!uploadFile) {
      return message.error('请重新上传文件');
    }
    Fetch('/agreement/public/cooperation/platform/coopUploadPaymentCertificate', {
      method: 'POST',
      body: JSON.stringify({ cooperationOrderId: record.id, uploadFile }),
    }).then(({ res }) => {
      if (!res.success) {
        message.error(res.message);
        return;
      }
      message.success('上传成功');
      onOk();
    });
  }, [fileList, record, onOk]);
  const onGetRssLink = useCallback((resourceIds) => {
    Fetch('/befriend-service-rss/public/resource/batchGetResourceUrls', {
      method: 'POST',
      body: JSON.stringify({
        accessTerm: 'FRONT',
        resourceIds,
      }),
    }).then(({ res }) => {
      if (res.code === '200') {
        if (res.result) {
          const fileList = [];
          Object.keys(res.result).forEach((k) => {
            fileList.push({ resourceId: k, url: res.result[k] });
          });
          setFileList(fileList);
        }
      }
    });
  }, []);
  useEffect(() => {
    if (record.id && record.paymentVoucher && visible) {
      getPaymentCertificateOp({ cooperationOrderId: record.id }).then(({ res }) => {
        if (res.success) {
          if (res.result) {
            onGetRssLink(res.result);
          }
        } else {
          message.error(res.message);
        }
      });
    } else {
      setFileList([]);
    }
  }, [visible, record]);

  const [imageFiles, pdfFiles] = useMemo(() => {
    const imgList = [],
      pdfList = [];
    fileList.forEach((_) => {
      if (_?.url?.includes('.pdf') || _?.type?.includes('pdf')) {
        pdfList.push(_);
      } else {
        imgList.push(_);
      }
    });
    return [imgList, pdfList];
  }, [fileList]);
  return (
    <Modal
      {...props}
      title={`${record.paymentVoucher ? '查看' : '上传'}线下支付凭证`}
      cancelButtonProps={{
        style: record.paymentVoucher ? { display: 'none' } : {},
      }}
      onOk={() => {
        if (record.paymentVoucher) {
          props.onCancel();
          return;
        }
        onUploadCert();
      }}
    >
      <Form
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        className={styles.certificateForm}
      >
        <Form.Item label="合作名称">{record.investmentTitle + record.liveName}</Form.Item>
        <Form.Item label="付款凭证">
          <OSSUpload
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            accept={'image/jpg,image/jpeg,image/png,image/gif,image/bmp,image/svg+xml'}
            editable={!record.paymentVoucher}
            onChange={onUploadChange}
            maxLen={MAX_UPLOAD}
            typeCode="COOPERATION_PAYMENT_CERT"
            dataSource={[...imageFiles]}
          />
          {!record.paymentVoucher && (
            <>
              <div
                style={{
                  border: '1px dashed #e1e1e1',
                  marginTop: -12,
                  marginBottom: 6,
                }}
              />
              <div
                className="ant-upload-list ant-upload-list-picture-card"
                style={{ display: 'block' }}
                onClick={() => {
                  setPreviewImage(certPng);
                  openModal();
                }}
              >
                <div className="ant-upload-list-item ant-upload-list-item-done ant-upload-list-item-list-type-picture-card">
                  <div className="ant-upload-list-item-info">
                    <img src={certPng} style={{ width: 102, height: 102 }} />
                  </div>
                  <div className="ant-upload-list-item-actions cursor-pointer">
                    <span style={{ color: '#fff' }}>示例</span>
                    <i aria-label="图标: eye-o" className="anticon anticon-eye-o">
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        className=""
                        data-icon="eye"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 0 0 0 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"></path>
                      </svg>
                    </i>
                  </div>
                </div>
                <div style={{ paddingTop: 104, width: 102, textAlign: 'center' }}>示例图片</div>
              </div>
              <Modal visible={previewVisible} footer={null} onCancel={handleCancel}>
                <img alt="示例凭证" style={{ width: '100%' }} src={previewImage} />
              </Modal>
            </>
          )}
          {pdfFiles?.map((_) => (
            <PDFPreview url={_?.url} className={styles.pdfPreview} />
          ))}
        </Form.Item>
      </Form>
    </Modal>
  );
});
