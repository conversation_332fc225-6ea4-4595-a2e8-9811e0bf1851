import { useCallback, useState } from 'react';
import { Fetch } from 'qmkit';
import { message } from 'antd';
import { createPaymentOrder, payForAdvancePayment } from './services/yml';

// 针对购买类型不同特殊处理，后续有新的加到这里
export enum PayTypeEnum {
  // 切片支付
  SECTION_PAY = 'SECTION_PAY',
  // 预付款单支付
  PREPAY_PAY = 'PREPAY_PAY',
}

export function useModalAction({
  onOk = () => {},
  onCancel = () => {},
}: {
  onOk?: Function;
  onCancel?: Function;
}) {
  const [visible, setVisible] = useState(false);
  return {
    visible,
    setVisible,
    openModal() {
      setVisible(true);
    },
    onCancel: () => {
      setVisible(false);
      onCancel();
    },
    onOk: () => {
      onOk();
    },
  };
}
const returnPayMethod = (orderNo, payType) => {
  let payPromise = null;
  if (payType === PayTypeEnum.SECTION_PAY) {
    payPromise = createPaymentOrder({ id: orderNo });
  } else if (payType == PayTypeEnum.PREPAY_PAY) {
    payPromise = payForAdvancePayment({ no: orderNo });
  } else {
    payPromise = Fetch<{ code: string; message: string; context: string }>(
      '/agreement/public/cooperation/supplier/payForCooperation',
      {
        body: JSON.stringify({ coopOrderNo: orderNo }),
        method: 'POST',
      },
    );
  }
  return payPromise;
};

export function useEbPay(onOk: () => void, onCancel: () => void) {
  const [orderNo, setOrderNo] = useState('');
  const {
    visible,
    openModal,
    onOk: onPayok,
    onCancel: onPayCancel,
    setVisible: setPayVisable,
  } = useModalAction({ onOk, onCancel });
  const [loadingInfo, setLoadingInfo] = useState({ loading: false, loadingKey: '' });
  const onCreateOrder = useCallback((coopOrderNo, payType: PayTypeEnum) => {
    setLoadingInfo({ loading: true, loadingKey: coopOrderNo });
    const payPromise = returnPayMethod(coopOrderNo, payType);
    payPromise
      .then(({ res }) => {
        setLoadingInfo({ loading: false, loadingKey: coopOrderNo });
        if (!res?.success) {
          message.error(res.message);
          return;
        }
        setOrderNo(res.result);
        openModal();
      })
      .catch((res) => {
        setLoadingInfo({ loading: false, loadingKey: coopOrderNo });
      });
  }, []);
  return {
    orderNo,
    onCreateOrder,
    payVisable: visible,
    setPayVisable,
    onPayok,
    onPayCancel,
    loadingInfo,
  };
}
