import React from 'react';
import OrderCooperationDetail from 'web-common-modules/biz/Order/OrderCooperationDetail';

export default function CooperationOrderDetail(props: any) {
  const params = window.location.pathname.split('/')[2];
  const coopOrderNo = params.split('?')[0];
  // const {
  //   match: {
  //     params: { coopOrderNo123 },
  //   },
  // } = props;

  return <OrderCooperationDetail coopOrderNo={coopOrderNo} />;
}
export const serviceFeeRate = (talentCommission: number) => {
  return talentCommission;
};
export const serviceFee = (lp: string, talentTechServiceFeeRate: number) => {
  const temPrice = lp?.split('~');
  const temRate = talentTechServiceFeeRate;
  if (temPrice.length == 2) {
    return `¥${(temPrice[0] * temRate).toFixed(2)} ~ ¥${(temPrice[1] * temRate).toFixed(2)}`;
  } else if (temPrice.length == 1) {
    return `¥ ${(temPrice[0] * temRate).toFixed(2)}`;
  } else {
    return '-';
  }
};
