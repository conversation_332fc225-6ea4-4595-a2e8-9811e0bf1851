import React from 'react';
import { ColumnProps } from 'antd/es/table';
import { But<PERSON>, Tag, Icon } from 'antd';
import styles from '../index.module.less';
import { Preview } from 'web-common-modules/antd-pro-components';
import { PopoverProxy } from 'web-common-modules/componentsV2';
import { OSSImagePre, ANTD_PREFIX } from 'web-common-modules/constant';
import { copyText } from 'web-common-modules/utils/copy';
import { defaultRender } from '@/utils/string';
import moment from 'moment';
import { AuditDetail, GLobalModal } from '../components';
import { LEVEL, LEVEL_NAME, LEVEL_COLOR } from '../../../../web_modules/types';
import {
  allRiskLevelColor,
  QUALIFICATION_AUDIT_STATE_COLOR,
  QUALIFICATION_AUDIT_STATE_ICON,
} from '@/pages/audit/legal-audit-queue/utils/getRiskLevel';
import { QUALIFICATION_AUDIT_STATE } from '@/pages/audit/legal-audit-queue/types';
import { QualificationReviewTaskPageResult } from '../services/yml';
import {
  TASK_STATUS,
  REVIEW_TASK_RES_ENUM,
  REVIEW_TASK_RES_NAME,
  REVIEW_TASK_RES_COLOR,
  GOODS_TYPE_NAME,
  GOODS_TYPE_ENUM,
} from '../types';
import { AuthWrapper } from 'qmkit';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

export type QualificationReviewTaskPageResultItem =
  Required<QualificationReviewTaskPageResult>['records'][number];

export const useTable = ({
  // onPass,
  // onReview,
  // onCompleteCheck,
  onRefresh,
  checkJumpAudit,
  keys,
}: {
  // onPass: (id: string) => void;
  // onReview: (record: QualificationReviewTaskPageResultItem) => void;
  // onCompleteCheck: (id: string) => void;
  onRefresh: () => void;
  checkJumpAudit: (record: QualificationReviewTaskPageResultItem) => void;
  keys: string;
}) => {
  const columns: ColumnProps<QualificationReviewTaskPageResultItem>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '任务编码',
      key: 'qualificationReviewTaskCode',
      dataIndex: 'qualificationReviewTaskCode',
      width: 170,
      render: (qualificationReviewTaskCode: string, record: any) => (
        <AuditDetail
          id={record?.lastQualificationAuditId}
          bpName={(record as any).brandName}
          taskId={record?.id}
          onRefresh={onRefresh}
          checkJumpAudit={checkJumpAudit}
        >
          <a
            onClick={() => {
              // history.push(`/quality-inspection-task-detail?id=${record?.id}`);
            }}
          >
            {qualificationReviewTaskCode || '-'}
          </a>
        </AuditDetail>
      ),
    },
    {
      title: '任务状态',
      key: 'status',
      dataIndex: 'status',
      width: 100,
      render: (_: string) => {
        return (
          <>
            {_ === 'FINISH' ? (
              <Tag style={{ background: '#F6FFED', borderColor: '#52C41A', color: '#52C41A' }}>
                已完成
              </Tag>
            ) : _ === 'WAIT_REVIEW' ? (
              <Tag style={{ background: '#FFFBE6', borderColor: '#FAAD14', color: '#FAAD14' }}>
                待复查
              </Tag>
            ) : (
              <Tag style={{ background: '#EDF4FF', borderColor: '#9CB9FF', color: '#204EFF' }}>
                待确认
              </Tag>
            )}
          </>
        );
      },
    },
    {
      title: '复查结果',
      key: 'reviewTaskResult',
      dataIndex: 'reviewTaskResult',
      width: 100,
      render: (_: REVIEW_TASK_RES_ENUM) => {
        return (
          <span style={{ color: REVIEW_TASK_RES_COLOR[_] }}>{REVIEW_TASK_RES_NAME[_] || '-'}</span>
        );
      },
    },
    {
      title: '商品类型',
      key: 'goodsType',
      dataIndex: 'goodsType',
      width: 100,
      render: (_: GOODS_TYPE_ENUM) => {
        return <span>{GOODS_TYPE_NAME[_] || '-'}</span>;
      },
    },
    {
      title: '商品信息',
      key: 'goods',
      dataIndex: 'goods',
      width: 350,
      render: (_: string, record: any) => {
        return (
          <div className={styles['table-goods-line']}>
            <div
              className="overflow-hidden"
              style={{ flexShrink: 0, width: '60px', height: '60px' }}
            >
              <Preview url={record?.spuImage || ''} urlList={[record?.spuImage || '']}>
                <img src={record?.spuImage} alt="" className="w-full h-full rounded-2" />
              </Preview>
            </div>
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
              <div
                className={`${ANTD_PREFIX}-copy-withId ml-8 ${styles.spuName} `}
                style={{ justifyContent: 'space-between' }}
              >
                <PopoverProxy title={'商品信息'} content={record?.spuName || '-'}>
                  <div
                    style={{ margin: '0 6px', width: '160px', fontWeight: 500 }}
                    className="copy-withId-name text-ellipsis-line-1"
                  >
                    {record?.spuName || '-'}
                  </div>
                </PopoverProxy>
                <PopoverProxy
                  title={'ID'}
                  content={
                    <div>
                      <div style={{ marginLeft: '4px', display: 'flex' }}>
                        <div className="font-secondary color-font-1">
                          平台商品ID: {record?.platformSpuId || '-'}
                        </div>
                        <img
                          src={`${OSSImagePre}/icon/copy.png`}
                          style={{ width: '14px', marginLeft: '4px', cursor: 'pointer' }}
                          onClick={() => copyText(record?.platformSpuId as string)}
                        />
                      </div>
                      <div style={{ marginLeft: '4px', display: 'flex' }}>
                        <div className="font-secondary color-font-1">
                          SaaS商品编号: {record?.spuNo || '-'}
                        </div>
                        <img
                          src={`${OSSImagePre}/icon/copy.png`}
                          style={{ width: '14px', marginLeft: '4px', cursor: 'pointer' }}
                          onClick={() => copyText(record?.spuNo as string)}
                        />
                      </div>
                    </div>
                  }
                >
                  <span className={styles['copy-withId-tag']}>ID</span>
                </PopoverProxy>
              </div>
              {keys !== 'HIGH_VALUE' ? (
                <div
                  style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
                  className="ml-12 mt-8"
                >
                  <span>
                    {record?.minPrice === record?.maxPrice
                      ? `¥${isNullOrUndefined(record?.minPrice) ? '-' : record?.minPrice}`
                      : `¥${isNullOrUndefined(record?.minPrice) ? '-' : record?.minPrice} ~ ${
                          isNullOrUndefined(record?.maxPrice) ? '-' : record?.maxPrice
                        }`}
                  </span>
                  <span style={{ flexShrink: 0 }}>
                    {isNullOrUndefined(record?.totalCommissionContainGuaranteed)
                      ? '-'
                      : `${((record?.totalCommissionContainGuaranteed || 0) * 100).toFixed(2)}%`}
                  </span>
                </div>
              ) : (
                <></>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: (
        <p>
          上次审核结果
          <PopoverProxy
            title="上次审核结果"
            content={
              <ul>
                <li>“待复查”，“待确认”状态下显示当前最近一次审核的结果</li>
                <li>“已完成”状态下显示复查时最近一次审核的结果</li>
              </ul>
            }
          >
            <Icon type="question-circle" theme="filled" />
          </PopoverProxy>
        </p>
      ),
      key: 'level',
      dataIndex: 'level',
      width: 170,
      render: (_, records: any) => {
        //allRiskLevelColor
        const {
          lastRiskLevel,
          lastBrandQualificationAuditState,
          lastSpuQualificationAuditState,
          lastSupplierQualificationAuditState,
        } = records;
        return (
          <div className={styles['table-level']}>
            <p style={{ color: LEVEL_COLOR[lastRiskLevel] }}>{LEVEL_NAME[lastRiskLevel] || '-'}</p>
            <div className={styles['level-box']}>
              <div className={styles['level-box-item']}>
                <Icon
                  style={{
                    color:
                      QUALIFICATION_AUDIT_STATE_COLOR[
                        lastSupplierQualificationAuditState as QUALIFICATION_AUDIT_STATE
                      ],
                  }}
                  className={styles['icon']}
                  type={
                    QUALIFICATION_AUDIT_STATE_ICON[
                      lastSupplierQualificationAuditState as QUALIFICATION_AUDIT_STATE
                    ]
                  }
                  theme="filled"
                />
                <span>商家</span>
              </div>
              <div className={styles['level-box-item']}>
                <Icon
                  style={{
                    color:
                      QUALIFICATION_AUDIT_STATE_COLOR[
                        lastBrandQualificationAuditState as QUALIFICATION_AUDIT_STATE
                      ],
                  }}
                  className={styles['icon']}
                  type={
                    QUALIFICATION_AUDIT_STATE_ICON[
                      lastBrandQualificationAuditState as QUALIFICATION_AUDIT_STATE
                    ]
                  }
                  theme="filled"
                />
                <span>品牌</span>
              </div>
            </div>
            <div className={styles['level-box']}>
              <div className={styles['level-box-item']}>
                <Icon
                  style={{
                    color:
                      QUALIFICATION_AUDIT_STATE_COLOR[
                        lastSpuQualificationAuditState as QUALIFICATION_AUDIT_STATE
                      ],
                  }}
                  className={styles['icon']}
                  type={
                    QUALIFICATION_AUDIT_STATE_ICON[
                      lastSpuQualificationAuditState as QUALIFICATION_AUDIT_STATE
                    ]
                  }
                  theme="filled"
                />
                <span>商品</span>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: '上次审核法务',
      key: 'lastAuditName',
      render: (_, { lastAuditName }) => defaultRender(lastAuditName),
      width: 100,
    },
    {
      title: '上次审核时间',
      key: 'lastAuditTime',
      dataIndex: 'lastAuditTime',
      width: 140,
      render: (_: number) => {
        return _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '复查法务',
      key: 'reviewAuditName',
      dataIndex: 'reviewAuditName',
      render: (_) => defaultRender(_),
      width: 100,
    },
    {
      title: '任务完成时间',
      key: 'taskFinishTime',
      dataIndex: 'taskFinishTime',
      width: 140,
      render: (_: number) => {
        return _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '任务创建时间',
      key: 'taskCreateTime',
      dataIndex: 'taskCreateTime',
      width: 140,
      render: (_: number) => {
        return _ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      key: 'other',
      dataIndex: 'other',
      width: 150,
      fixed: 'right',
      render: (_: string, record: QualificationReviewTaskPageResultItem) => {
        if (record?.status === TASK_STATUS.CONFIRMING) {
          return (
            <AuthWrapper functionName="f_queues_review_task_complete_check">
              <GLobalModal id={record?.id} onRefresh={onRefresh} type="finish">
                <Button
                  type="link"
                  // onClick={() => {
                  //   // onCompleteCheck(record?.id as string);
                  // }}
                  style={{ padding: 0 }}
                >
                  复核完成
                </Button>
              </GLobalModal>
            </AuthWrapper>
          );
        }
        return record?.status === 'WAIT_REVIEW' ? (
          <>
            <AuthWrapper functionName="f_queues_review_task_pass">
              <GLobalModal id={record?.id} onRefresh={onRefresh} type="pass">
                <Button
                  type="link"
                  // onClick={() => {
                  //   onPass(record?.id as string);
                  // }}
                  style={{ padding: 0 }}
                >
                  复查通过
                </Button>
              </GLobalModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_queues_review_task_reload_check">
              <GLobalModal id={record?.id} onRefresh={onRefresh} type="review">
                <Button
                  type="link"
                  style={{ color: 'red', padding: '0px 0px 0px 6px' }}
                  // onClick={() => {
                  //   onReview(record);
                  // }}
                >
                  重新复核
                </Button>
              </GLobalModal>
            </AuthWrapper>
          </>
        ) : (
          <></>
        );
      },
    },
  ];

  return { columns };
};
