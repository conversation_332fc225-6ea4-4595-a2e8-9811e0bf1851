import React, { useCallback, useImperativeHandle } from 'react';
import { FormComponentProps } from 'antd/es/form';
import { Form } from 'antd';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { useSearch } from '../hooks';
import moment from 'moment';
import { formatString } from '@/pages/operation-process-board/utils';

interface IProps extends FormComponentProps {
  onSearch: (value: any) => void;
  getTableHeight?: any;
  onRef: any;
}

const SearchForm = (props: IProps) => {
  const { form, getTableHeight, onSearch, onRef } = props;
  const { options } = useSearch();
  const onSubmit = useCallback(
    (init?: boolean) => {
      form.validateFields((err, values) => {
        // console.log(values, '------>');
        const { taskCreateTime, taskFinishTime, platformSpuIdList, ...restValue } = values;
        const params = {
          ...restValue,
          platformSpuIdList: Array.isArray(platformSpuIdList)
            ? platformSpuIdList
            : formatString(platformSpuIdList),
        };
        if (taskCreateTime?.length) {
          const [taskCreateTimeStart, taskCreateTimeEnd] = taskCreateTime;
          params.taskCreateTimeStart = moment(taskCreateTimeStart).format('YYYY-MM-DD');
          params.taskCreateTimeEnd = moment(taskCreateTimeEnd).format('YYYY-MM-DD');
        }

        if (taskFinishTime?.length) {
          const [taskFinishTimeStart, taskFinishTimeEnd] = taskFinishTime;
          params.taskFinishTimeStart = moment(taskFinishTimeStart).format('YYYY-MM-DD');
          params.taskFinishTimeEnd = moment(taskFinishTimeEnd).format('YYYY-MM-DD');
        }

        onSearch(params);
        // confirm(values);
        // if (!err) {
        //   onSearch(values, { init });
        // }
      });
    },
    [onSearch, form],
  );

  const onReset = () => {
    form.resetFields();
    onSubmit();
  };

  useImperativeHandle(onRef, () => ({
    resetForm: () => {
      form.resetFields();
    },
  }));

  return (
    <div style={{ marginTop: '12px' }}>
      <SearchFormComponent
        form={form}
        options={options}
        loading={false}
        onSearch={onSubmit}
        onReset={onReset}
        needMore
        getTableHeight={getTableHeight}
        showRow={2}
      />
    </div>
  );
};

export default Form.create<IProps>()(SearchForm);
