import React, { useEffect, useState, useMemo } from 'react';
import { DrawerProps } from 'antd/es/drawer';
import WithToggleModal from '@/components/WithToggleModal';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import { Button, Drawer, message, Spin, Tag } from 'antd';
import style from '@/pages/choice-list-new/components/LegalCheckDrawer/index.module.less';
import { useRequest } from 'ahooks';
import { qualityWhiteListValid } from '@/pages/choice-list-new/components/LegalCheckDrawer/services/yml';
import { useDetail, useBtns } from '../hooks';
import { DetailTitle } from '@/components/index';
import LegalResult from '@/pages/audit/legal-audit-workbench/Index/LegalResult';
import { LEVEL } from '../../../../web_modules/types';
import GoodsMsg from '@/pages/choice-list-new/components/LegalCheckDrawer/GoodsMsg';
import SelectionProcess from '@/pages/choice-list-new/components/LegalCheckDrawer/SelectionProcess';
import Shops from '@/pages/choice-list-new/components/LegalCheckDrawer/Shops';
import Brand from '@/pages/choice-list-new/components/LegalCheckDrawer/Brand';
import Commodity from '@/pages/choice-list-new/components/LegalCheckDrawer/Commodity';
import { TASK_STATUS } from '../types';
import { AuthWrapper } from 'qmkit';
import { GLobalModal, DetailLog, History } from './index';
import { QualificationReviewTaskPageResultItem } from '../hooks/useTable';

interface IProps extends WithToggleModalProps, DrawerProps {
  id?: any;
  selectFlowNo?: string;
  source?: string; //choiceList 场次货盘
  bpName?: string;
  taskId: string;
  onRefresh: () => void;
  checkJumpAudit: (record: QualificationReviewTaskPageResultItem) => void;
}

const AuditDetail: React.FC<IProps> = ({
  onOk,
  id,
  source,
  selectFlowNo,
  visible,
  bpName,
  taskId,
  onRefresh,
  checkJumpAudit,
  ...rest
}) => {
  const [brandWhite, setBrandWhite] = useState<boolean>(false);
  //  useState<SupplierBodySpecialAuditLatestResult>();
  const { run: whiteRun } = useRequest(qualityWhiteListValid, {
    manual: true,
    onSuccess({ res }) {
      if (res.code !== '200') {
        message.warning(res.message || '网络异常');
        return;
      }
      setBrandWhite(res?.result);
    },
  });
  const {
    current,
    qualification,
    qualificationMap,
    detailLoading,
    basicItem,
    selectFlowsList,
    detailCurLoading,
    detailCur,
  } = useDetail(id, visible as boolean, taskId, source);

  // console.log('🚀 ~ file: index.tsx:43 ~ qualificationList:', qualificationList);

  const isbrandQualificationWhite = useMemo(() => {
    console.log('qualification', qualification);
    if (qualification?.supplierQualification && qualification?.detail) {
      const { auditDetailMap } = qualification?.detail;
      const supplier = qualification?.brandQualification;
      const item = auditDetailMap
        ? auditDetailMap[supplier?.itemVersionId]
        : { isBrandWhitelisted: false };
      return item?.isBrandWhitelisted;
    } else {
      return false;
    }
  }, [qualification?.brandQualification, qualification?.detail]);

  // const {
  //   reviewLoading,
  //   qualificationReviewTaskReCheckLoading,
  //   qualificationReviewTaskCompleteCheckLoading,
  //   onPass,
  //   onReview,
  //   onCompleteCheck,
  // } = useBtns({
  //   onRefresh: () => {
  //     rest?.onCancel();
  //     onRefresh();
  //   },
  // });

  useEffect(() => {
    if (current?.brandName && visible) {
      whiteRun({ brandName: current?.brandName });
    }
  }, [current, visible]);
  return (
    <Drawer
      {...rest}
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingRight: '38px',
          }}
        >
          <span>资质详情</span>
          <div>
            {detailCur?.status === TASK_STATUS.CONFIRMING ? (
              <AuthWrapper functionName="f_queues_review_task_complete_check">
                <GLobalModal
                  id={detailCur?.qualificationReviewTaskId}
                  onRefresh={() => {
                    rest?.onCancel();
                    onRefresh();
                  }}
                  type="finish"
                >
                  <Button
                    type="primary"

                    // onClick={() => {
                    //   onCompleteCheck(taskId);
                    // }}
                  >
                    复核完成
                  </Button>
                </GLobalModal>
              </AuthWrapper>
            ) : detailCur?.status === TASK_STATUS.REVIEWED ? (
              <div
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
              >
                <AuthWrapper functionName="f_queues_review_task_pass">
                  <GLobalModal
                    id={detailCur?.qualificationReviewTaskId}
                    onRefresh={() => {
                      rest?.onCancel();
                      onRefresh();
                    }}
                    type="pass"
                  >
                    <Button
                      type="primary"
                      // onClick={() => {
                      //   onPass(taskId);
                      // }}
                    >
                      复查通过
                    </Button>
                  </GLobalModal>
                </AuthWrapper>
                <AuthWrapper functionName="f_queues_review_task_reload_check">
                  <GLobalModal
                    id={detailCur?.qualificationReviewTaskId}
                    onRefresh={() => {
                      rest?.onCancel();
                      onRefresh();
                    }}
                    type="review"
                  >
                    <Button
                      style={{ marginLeft: '8px' }}
                      type="danger"
                      // onClick={() => {
                      //   onReview({
                      //     id: taskId,
                      //   });
                      // }}
                    >
                      重新复核
                    </Button>
                  </GLobalModal>
                </AuthWrapper>
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
      }
      width={1350}
      maskClosable={true}
      // onOk={onOk}
      // footer={null}
      className={style['legal-check-drawer']}
      visible={visible}
      style={{ transform: 'translateX(0)' }}
      headerStyle={{
        position: 'sticky',
        top: '0px',
        left: '0px',
        background: '#ffffff',
        zIndex: 2,
      }}
      zIndex={999}
    >
      <Spin spinning={detailLoading || detailCurLoading}>
        {current?.lastRiskLevel !== LEVEL.NONE ? (
          <>
            <DetailTitle title="审核结果" style={{ paddingBottom: '8px' }}></DetailTitle>
            <LegalResult detail={current}></LegalResult>
            <div className={style['divider']}></div>
          </>
        ) : (
          <></>
        )}
        <DetailTitle title="商品信息" style={{ paddingBottom: '8px' }}></DetailTitle>
        <GoodsMsg
          detail={basicItem}
          isPassRate={false}
          isSKU={true}
          detailCur={detailCur}
        ></GoodsMsg>
        {/* <DetailTitle title="选品流程" style={{ paddingBottom: '8px' }}></DetailTitle>
        <SelectionProcess dataSource={selectFlowsList}></SelectionProcess> */}
        <div className={style['divider']}></div>
        <DetailTitle
          title={
            <span>
              商家资质
              {qualification?.SUPPLIER?.isBrandWhitelisted ? (
                <Tag color="orange" style={{ marginLeft: '4px' }}>
                  白名单
                </Tag>
              ) : qualification?.detail?.isSupplierBodySpecialAuditPass ? (
                <Tag color="green" style={{ marginLeft: '4px' }}>
                  主体特批
                </Tag>
              ) : (
                <></>
              )}
            </span>
          }
          shopsTag={true}
          style={{ paddingBottom: '8px' }}
        ></DetailTitle>
        <Shops
          supplierQualification={qualificationMap?.SUPPLIER}
          auditExpirationDateMap={{
            BUSINESS_LICENSE: {
              expirationDate: qualification?.SUPPLIER?.timeMap['BUSINESS_LICENSE'],
            },
          }}
        ></Shops>
        <div className={style['divider']}></div>
        <DetailTitle
          title={
            <span>
              品牌资质
              {qualification?.BRAND?.isBrandWhitelisted ? (
                <Tag color="orange" style={{ marginLeft: '4px' }}>
                  白名单
                </Tag>
              ) : (
                <></>
              )}
            </span>
          }
          shopsTag={true}
          businessTag={true}
          style={{ paddingBottom: '8px' }}
        ></DetailTitle>
        <Brand
          bpBrandQualification={qualificationMap?.BPBRAND}
          brandQualification={qualificationMap?.BRAND}
          auditExpirationDateMap={{}}
          auditDetailMap={{}}
          isEdit={false}
        ></Brand>
        <div className={style['divider']}></div>
        <DetailTitle
          title={
            <span>
              商品资质
              {brandWhite ? (
                <Tag color="orange" style={{ marginLeft: '4px' }}>
                  白名单B
                </Tag>
              ) : (
                <></>
              )}
            </span>
          }
          shopsTag={true}
          businessTag={true}
          style={{ paddingBottom: '8px' }}
        ></DetailTitle>
        <Commodity
          bpSpuQualification={qualificationMap?.BPSPU}
          spuQualification={qualificationMap?.SPU}
          auditDetailMap={{}}
          auditExpirationDateMap={{}}
          isBtn={false}
          isEdit={true}
          detail={qualification?.detail}
        ></Commodity>
        <div className={style['divider']}></div>
        <DetailTitle
          title={
            <p>
              历史审核结果 <span style={{ fontSize: '12px' }}>(仅展示该商品最近5次的审核结果)</span>
            </p>
          }
          style={{ paddingBottom: '8px' }}
        ></DetailTitle>
        <History checkJumpAudit={checkJumpAudit} list={detailCur?.historyAuditResult} />
        <div className={style['divider']}></div>
        <DetailTitle title="操作日志" style={{ paddingBottom: '8px' }}></DetailTitle>
        <DetailLog bizOrderId={detailCur?.qualificationReviewTaskId as string} />
      </Spin>
    </Drawer>
  );
};

export default WithToggleModal<Omit<IProps, 'form'>>(AuditDetail);
