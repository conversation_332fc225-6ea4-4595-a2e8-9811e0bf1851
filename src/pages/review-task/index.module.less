@import '~web-common-modules/styles//token.less';
@import '~web-common-modules/styles/var.less';

.publishFeeManageContainer {
  background-color: white;

  :global {
    .page-layout-body-wrap {
      margin: 0;
    }

    .ant-tabs-bar {
      margin: 0;
      background-color: white;
    }

    .layout-content-item {
      margin: 20px;
    }

    .page-layout-body-wrap {
      margin: 0;
    }
    .ant-tabs {
      .ant-tabs-bar {
        border-bottom: 0 !important;
        // margin-bottom: 12px;
      }

      .ant-tabs-ink-bar {
        background: #204eff;
      }

      .ant-tabs-nav .ant-tabs-tab {
        padding: 12px 6px;
      }

      .ant-tabs-tab {
        margin-right: 16px;
      }
    }
  }
}

.table-goods-line {
  display: flex;
  align-items: flex-start;
  :global {
    .copy-withId-name {
      -webkit-line-clamp: 2;
    }

    // .copy-withId-tag {
    //   transform: translateY(1px);
    // }
  }
}

.table-level {
  .all-title {
    font-size: 13px;
    margin-bottom: 8px;
  }

  .level-box {
    display: flex;
    align-items: center;
    margin-bottom: 2px;

    .level-box-item {
      display: flex;
      align-items: center;
      margin-right: 8px;

      .icon {
        margin-right: 2px;
      }

      span {
        font-size: 12px;
        color: rgba(46, 52, 66, 0.45);
      }
    }
  }
}

.drawer-title {
  font-size: 20px;
  line-height: 28px;
  color: #111111;
  font-weight: 500;

  h2 {
    padding-right: 8px;
  }
}

.container {
  // background-color: rgb(246, 247, 249);
  // overflow: hidden;
  :global {
    .ant-tabs-tab {
      margin-right: 32px !important;
      padding: 0 0 10px !important;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }

    .ant-tabs-bar {
      background-color: #fff;
      margin: 0 !important;
      border-bottom: 1px solid #ebedf0;
    }

    .ant-tabs-tabpane {
      padding-top: 16px;
    }

    // .page-layout .page-layout-content{
    //   overflow: hidden;
    // }
  }
}

.new-page-box {
  display: flex;
  width: 100%;

  .main-box {
    // flex: 1;
    width: 100%;
    margin-right: 8px;
    contain: paint;
    // align-self: flex-start;
    // position: sticky;
    // top: 0;
  }

  .comment-box {
    flex-shrink: 0;
    width: 264px;
    height: calc(100vh - 74px);
    position: sticky;
    top: 0;

    &-main {
      width: 100%;
      height: calc(100vh - 74px);
      background: #fff;
      box-sizing: border-box;
      align-self: flex-start;
      display: flex;
      flex-direction: column;
    }
  }
}

.process-box {
  padding: 0 0 0 16px;
  background: #eceef1;
}

.spuName {
  align-items: flex-start;
  flex: 1;
  color: #666666;
  display: flex;

  :global {
    .copy-withId-name {
      -webkit-line-clamp: 2;
    }
  }
  .copy-withId-tag {
    transform: translateY(1px);
    min-width: max-content;
    padding: 2px;
    font-weight: 600;
    font-size: 12px;
    line-height: 12px;
    color: #778294;
    background: #eef0f3;
    border-radius: 2px;
  }
}
