import { LEVEL, LEVEL_NAME } from '../../../../web_modules/types';

export enum TASK_STATUS {
  REVIEWED = 'WAIT_REVIEW',
  FINISH = 'FINISH',
  CONFIRMING = 'WAIT_CHECK',
}

export const taskStatusName = {
  [TASK_STATUS.REVIEWED]: '待复查',
  [TASK_STATUS.FINISH]: '已完成',
  [TASK_STATUS.CONFIRMING]: '待确认',
};

export const taskStatusList = [
  {
    label: taskStatusName[TASK_STATUS.FINISH],
    value: TASK_STATUS.FINISH,
  },
  {
    label: taskStatusName[TASK_STATUS.REVIEWED],
    value: TASK_STATUS.REVIEWED,
  },
  {
    label: taskStatusName[TASK_STATUS.CONFIRMING],
    value: TASK_STATUS.CONFIRMING,
  },
];

export const levelList = [
  {
    label: LEVEL_NAME[LEVEL.HIGH],
    value: LEVEL.HIGH,
  },
  {
    label: LEVEL_NAME[LEVEL.MIDDLe],
    value: LEVEL.MIDDLe,
  },
  {
    label: LEVEL_NAME[LEVEL.LOW],
    value: LEVEL.LOW,
  },
  {
    label: LEVEL_NAME[LEVEL.PASS],
    value: LEVEL.PASS,
  },
];

export enum REVIEW_TASK_RES_ENUM {
  PASS = 'PASS',
  NO_PASS = 'NO_PASS',
}

export const REVIEW_TASK_RES_NAME = {
  [REVIEW_TASK_RES_ENUM.PASS]: '通过',
  [REVIEW_TASK_RES_ENUM.NO_PASS]: '未通过',
};

export const REVIEW_TASK_RES_LIST = [
  {
    label: REVIEW_TASK_RES_NAME[REVIEW_TASK_RES_ENUM.PASS],
    value: REVIEW_TASK_RES_ENUM.PASS,
  },
  {
    label: REVIEW_TASK_RES_NAME[REVIEW_TASK_RES_ENUM.NO_PASS],
    value: REVIEW_TASK_RES_ENUM.NO_PASS,
  },
];

export const REVIEW_TASK_RES_COLOR = {
  [REVIEW_TASK_RES_ENUM.PASS]: '#52C41A',
  [REVIEW_TASK_RES_ENUM.NO_PASS]: '#EE0000',
};

// Review task status constants
export enum ReviewTaskStatus {
  REVIEW_TASK_PASS = 'REVIEW_TASK_PASS',
  REVIEW_TASK_COMPLETE = 'REVIEW_TASK_COMPLETE',
  REVIEW_TASK_RE_CHECK = 'REVIEW_TASK_RE_CHECK',
  REVIEW_TASK_IS_CREATED = 'REVIEW_TASK_IS_CREATED',
}

export const ReviewTaskStatusLabels = {
  [ReviewTaskStatus.REVIEW_TASK_PASS]: '复查通过',
  [ReviewTaskStatus.REVIEW_TASK_COMPLETE]: '复核完成',
  [ReviewTaskStatus.REVIEW_TASK_RE_CHECK]: '重新复核',
  [ReviewTaskStatus.REVIEW_TASK_IS_CREATED]: '任务创建',
};

export const ReviewTaskStatusOptions = [
  {
    label: ReviewTaskStatusLabels[ReviewTaskStatus.REVIEW_TASK_PASS],
    value: ReviewTaskStatus.REVIEW_TASK_PASS,
  },
  {
    label: ReviewTaskStatusLabels[ReviewTaskStatus.REVIEW_TASK_COMPLETE],
    value: ReviewTaskStatus.REVIEW_TASK_COMPLETE,
  },
  {
    label: ReviewTaskStatusLabels[ReviewTaskStatus.REVIEW_TASK_RE_CHECK],
    value: ReviewTaskStatus.REVIEW_TASK_RE_CHECK,
  },
  {
    label: ReviewTaskStatusLabels[ReviewTaskStatus.REVIEW_TASK_IS_CREATED],
    value: ReviewTaskStatus.REVIEW_TASK_IS_CREATED,
  },
];

export enum AUDIT_TYPE_ENUM {
  MANUAL = 'MANUAL',
  AUTO = 'AUTO',
  WHITE_LIST = 'WHITE_LIST',
}

export const AUDIT_TYPE_NAME = {
  [AUDIT_TYPE_ENUM.MANUAL]: '人工审核',
  [AUDIT_TYPE_ENUM.AUTO]: '自动过审',
  [AUDIT_TYPE_ENUM.WHITE_LIST]: '白名单过审',
};

export enum GOODS_TYPE_ENUM {
  LUXURY = 'LUXURY',
  HIGH_VALUE = 'HIGH_VALUE',
  LUXURY_LIVE_AFTER = 'LUXURY_LIVE_AFTER',
}

export const GOODS_TYPE_NAME = {
  [GOODS_TYPE_ENUM.LUXURY]: '高奢商品',
  [GOODS_TYPE_ENUM.HIGH_VALUE]: '高价值商品',
  [GOODS_TYPE_ENUM.LUXURY_LIVE_AFTER]: '高奢播后复查',
};
