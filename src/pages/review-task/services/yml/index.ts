import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type QualificationReviewTaskPageRequest = {
  bpId?: string /*商务ID*/;
  brandId?: string /*品牌id*/;
  current?: number /*当前页码,从1开始*/;
  goodsType?: 'LUXURY' | 'HIGH_VALUE' | 'LUXURY_LIVE_AFTER' /*商品类型[GoodsTypeEnum]*/;
  platformSpuIdList?: Array<string> /*平台商品id列表*/;
  qualificationReviewTaskCode?: string /*资质复查任务编号*/;
  reviewAuditId?: string /*复查法务id*/;
  reviewTaskResult?: 'PASS' | 'NO_PASS' /*复查结果[ReviewTaskResultEnum]*/;
  size?: number /*分页大小*/;
  spuName?: string /*商品名称*/;
  status?: 'FINISH' | 'WAIT_REVIEW' | 'WAIT_CHECK' /*任务状态[QualificationReviewTaskStatusEnum]*/;
  supplierId?: string /*商家id*/;
  taskCreateTimeEnd?: string /*任务创建时间-截止时间*/;
  taskCreateTimeStart?: string /*任务创建时间-开始时间*/;
  taskFinishTimeEnd?: string /*任务完成时间-截止时间*/;
  taskFinishTimeStart?: string /*任务完成时间-开始时间*/;
};

export type QualificationReviewTaskPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    deptId?: string /*事业部ID*/;
    goodsType?: 'LUXURY' | 'HIGH_VALUE' | 'LUXURY_LIVE_AFTER' /*商品类型[GoodsTypeEnum]*/;
    id?: string /*主键*/;
    lastAuditId?: string /*上次审核法务id*/;
    lastAuditName?: string /*上次审核法务名称*/;
    lastAuditTime?: string /*上次审核时间*/;
    lastBrandQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*上次店铺品牌资质审核状态[QualificationAuditStateEnum]*/;
    lastQualificationAuditId?: string /*上次资质审核记录id*/;
    lastRiskLevel?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*上次资质审核风险等级[QualificationRiskLevelEnum]*/;
    lastSpuQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*上次商品资质审核状态[QualificationAuditStateEnum]*/;
    lastSupplierQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*上次商家资质审核状态[QualificationAuditStateEnum]*/;
    maxPrice?: string /*最高售价*/;
    minPrice?: string /*最低售价*/;
    platformSpuId?: string /*平台商品ID*/;
    qualificationReviewTaskCode?: string /*资质复查任务编号*/;
    reviewAuditId?: string /*复查法务id*/;
    reviewAuditName?: string /*复查法务名称*/;
    reviewTaskResult?: 'PASS' | 'NO_PASS' /*复查结果[ReviewTaskResultEnum]*/;
    spuId?: string /*商品ID*/;
    spuImage?: string /*商品图片*/;
    spuName?: string /*商品名称*/;
    spuNo?: string /*商品编号*/;
    status?: string /*任务状态*/;
    supplierId?: string /*商家ID*/;
    taskCreateTime?: string /*任务创建时间*/;
    taskFinishTime?: string /*任务完成时间*/;
    totalCommissionContainGuaranteed?: string /*总佣金含保量*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *资质复查任务列表
 */
export const qualificationReviewTaskPage = (params: QualificationReviewTaskPageRequest) => {
  return Fetch<ResponseWithResult<QualificationReviewTaskPageResult>>(
    '/pim/public/qualificationReviewTask/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/page') },
    },
  );
};

export type QualificationReviewTaskPassRequest = {
  qualificationReviewTaskId?: string /*资质审核任务id*/;
  reviewComments?: string /*复查意见*/;
};

export type QualificationReviewTaskPassResult = any;

/**
 *资质复查-审核通过
 */
export const qualificationReviewTaskPass = (params: QualificationReviewTaskPassRequest) => {
  return Fetch<ResponseWithResult<QualificationReviewTaskPassResult>>(
    '/pim/public/qualificationReviewTask/pass',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/pass') },
    },
  );
};

export type DetailRecordForInstRequest = {
  forResubmit?: boolean /*是否重新审核*/;
  id?: string /*审核记录主键*/;
  isRemoveBusinessPassRate?: boolean /*是否去除商务通过率*/;
};

export type DetailRecordForInstResult = {
  auditorName?: string /*当前审核人*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  cateNamePath?: string /*商品类目路径*/;
  deptId?: string /*事业部ID, 默认-1为非主体特批拆分, 其他值为主体特批拆分*/;
  id?: string /*审核记录主键*/;
  isBodySpecial?: boolean /*是否主体特批拆分队列*/;
  isSpecialMaterial?: boolean /*是否特殊材质(1:是,0:否,无默认值)*/;
  isSupplierBodySpecialAuditPass?: boolean /*是否商家主体特批通过*/;
  lastAuditDetailMap?: {
    [key: string]: {
      auditOpinion?: string /*审核意见*/;
      auditState?:
        | 'PASS'
        | 'NO_PASS'
        | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
      bizType?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'GOODS'
        | 'SHOP'
        | 'BP_BRAND'
        | 'BP_GOODS'
        | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
      isBrandWhitelisted?: boolean /*是否白名单B*/;
      itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
      remark?: string /*备注*/;
    };
  } /*复用法务审核明细, Key是资质项目版本ID*/;
  lastAuditExpirationDateMap?: {
    [key: string]: {
      bizType?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'GOODS'
        | 'SHOP'
        | 'BP_BRAND'
        | 'BP_GOODS'
        | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
      expirationDate?: string /*法务审核有效期*/;
      itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
      qualificationsTypeNo?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
    };
  } /*复用法务审核有效期, Key是资质类型编号*/;
  lastAuditOpinion?: string /*上次法务意见备注*/;
  lastAuditTime?: string /*上次法务审核时间*/;
  lastBrandQualificationAuditState?:
    | 'PASS'
    | 'NO_PASS'
    | 'NONE' /*上次店铺品牌资质审核状态[QualificationAuditStateEnum]*/;
  lastExpirationDate?: string /*上次资质有效期*/;
  lastRiskLevel?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*上次风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待审核-NONE[QualificationRiskLevelEnum]*/;
  lastSpuQualificationAuditState?:
    | 'PASS'
    | 'NO_PASS'
    | 'NONE' /*上次商品资质审核状态[QualificationAuditStateEnum]*/;
  lastSupplierQualificationAuditState?:
    | 'PASS'
    | 'NO_PASS'
    | 'NONE' /*上次商家资质审核状态[QualificationAuditStateEnum]*/;
  manual?: boolean /*是否人工拆分*/;
  maxLiveDate?: string /*场次货盘最大直播日期*/;
  platform?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[PlatformEnum]*/;
  platformSpuId?: string /*平台商品ID*/;
  qualification?: {
    bpBrandQualification?: {
      brandEndTime?: string /*品牌资质有效期-结束时间*/;
      brandId?: string /*品牌ID*/;
      brandName?: string /*品牌名称*/;
      brandStartTime?: string /*品牌资质有效期-开始时间*/;
      changeType?:
        | 'SAME'
        | 'APPEND'
        | 'MODIFY' /*过审资质变更类型-与上次过审资质实时对比结果[QualificationChangeTypeEnum]*/;
      extension?: string /*资质扩展信息*/;
      hasRegExt?: boolean /*是否存在多页、转让、续展等情形*/;
      isReg?: boolean /*是否已注册商标,是:商标注册证,否:商标注册受理通知书*/;
      itemVersionId?: string /*资质项版本ID*/;
      platformShopCode?: string /*平台店铺ID*/;
      platformShopOrgName?: string /*平台（淘宝/抖音/京东等）平台的开店主体，不一致是需要填写*/;
      quantity?: number /*资质数量*/;
      regExtType?: Array<string> /*注册证扩展类型*/;
      regNum?: string /*(商标注册证)注册号*/;
      regPerson?: string /*(商标注册证)注册人*/;
      shopId?: string /*店铺ID*/;
      shopName?: string /*店铺名称*/;
      snapshotChangeMap?: {
        [key: string]: boolean;
      } /*资质变更标记集合, key:资质类型编号, value:资质变更标记*/;
      snapshotMap?: {
        [key: string]: Array<{
          content?: string /*资质内容*/;
          qualificationType?: string /*资质类型,从资质类型编号取值*/;
          qualificationTypeName?: string /*资质类型名称*/;
          resourceType?:
            | 'RSS'
            | 'TEXT'
            | 'LINK' /*资源类型:RSS,TEXT,LINK[QualificationResourceTypeEnum]*/;
          url?: string /*资质url*/;
        }>;
      } /*资质快照集合, key:资质类型编号, value:资质快照*/;
      trademarkApplicant?: string /*商标申请人*/;
      trademarkApplyDate?: string /*商标申请日期*/;
      trademarkApplyNo?: string /*商标申请号*/;
      trademarkCategory?: string /*商标类别*/;
      trademarkName?: string /*商标名称*/;
      type?:
        | 'OWN'
        | 'AUTHORIZED'
        | 'OTHER'
        | 'PURCHASE' /*品牌类型:自有品牌OWN 授权品牌 AUTHORIZED[BrandTypeEnum]*/;
      types?: Array<{
        bizType?:
          | 'SUPPLIER'
          | 'BRAND'
          | 'GOODS'
          | 'SHOP'
          | 'BP_BRAND'
          | 'BP_GOODS'
          | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
        creator?: string /*创建人*/;
        gmtCreated?: string /*创建时间*/;
        gmtModified?: string /*更新时间*/;
        id?: string /*主键*/;
        imageResourceIds?: Array<string> /*示例图资源IDS,如:[111,222]*/;
        mode?:
          | 'USER'
          | 'SYSTEM' /*创建方式,USER-用户创建, SYSTEM-系统默认[QualificationTypeModeEnum]*/;
        modifier?: string /*修改人*/;
        name?: string /*资质类型名称*/;
        no?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
        noneImage?: boolean /*是否无示例图*/;
        sort?: number /*排序*/;
        specification?: string /*资质类型说明*/;
      }> /*资质类型信息*/;
    } /*商务店铺品牌资质*/;
    bpSpuQualification?: {
      changeType?:
        | 'SAME'
        | 'APPEND'
        | 'MODIFY' /*过审资质变更类型-与上次过审资质实时对比结果[QualificationChangeTypeEnum]*/;
      extension?: string /*资质扩展信息*/;
      itemVersionId?: string /*资质项版本ID*/;
      platformSpuId?: string /*平台商品ID*/;
      quantity?: number /*资质数量*/;
      snapshotChangeMap?: {
        [key: string]: boolean;
      } /*资质变更标记集合, key:资质类型编号, value:资质变更标记*/;
      snapshotMap?: {
        [key: string]: Array<{
          content?: string /*资质内容*/;
          qualificationType?: string /*资质类型,从资质类型编号取值*/;
          qualificationTypeName?: string /*资质类型名称*/;
          resourceType?:
            | 'RSS'
            | 'TEXT'
            | 'LINK' /*资源类型:RSS,TEXT,LINK[QualificationResourceTypeEnum]*/;
          url?: string /*资质url*/;
        }>;
      } /*资质快照集合, key:资质类型编号, value:资质快照*/;
      spuId?: string /*商品ID*/;
      spuName?: string /*商品名称*/;
      spuNo?: string /*商品编号*/;
      supplySource?:
        | 'HOME_BRED'
        | 'CROSS_BORDER'
        | 'IMPORT' /*商品来源,HOME_BRED:国产,IMPORT:进口,CROSS_BORDER:跨境[SupplySourceEnum]*/;
      types?: Array<{
        bizType?:
          | 'SUPPLIER'
          | 'BRAND'
          | 'GOODS'
          | 'SHOP'
          | 'BP_BRAND'
          | 'BP_GOODS'
          | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
        creator?: string /*创建人*/;
        gmtCreated?: string /*创建时间*/;
        gmtModified?: string /*更新时间*/;
        id?: string /*主键*/;
        imageResourceIds?: Array<string> /*示例图资源IDS,如:[111,222]*/;
        mode?:
          | 'USER'
          | 'SYSTEM' /*创建方式,USER-用户创建, SYSTEM-系统默认[QualificationTypeModeEnum]*/;
        modifier?: string /*修改人*/;
        name?: string /*资质类型名称*/;
        no?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
        noneImage?: boolean /*是否无示例图*/;
        sort?: number /*排序*/;
        specification?: string /*资质类型说明*/;
      }> /*资质类型信息*/;
    } /*商务商品资质*/;
    brandQualification?: {
      brandEndTime?: string /*品牌资质有效期-结束时间*/;
      brandId?: string /*品牌ID*/;
      brandName?: string /*品牌名称*/;
      brandStartTime?: string /*品牌资质有效期-开始时间*/;
      changeType?:
        | 'SAME'
        | 'APPEND'
        | 'MODIFY' /*过审资质变更类型-与上次过审资质实时对比结果[QualificationChangeTypeEnum]*/;
      extension?: string /*资质扩展信息*/;
      hasRegExt?: boolean /*是否存在多页、转让、续展等情形*/;
      isReg?: boolean /*是否已注册商标,是:商标注册证,否:商标注册受理通知书*/;
      itemVersionId?: string /*资质项版本ID*/;
      platformShopCode?: string /*平台店铺ID*/;
      platformShopOrgName?: string /*平台（淘宝/抖音/京东等）平台的开店主体，不一致是需要填写*/;
      quantity?: number /*资质数量*/;
      regExtType?: Array<string> /*注册证扩展类型*/;
      regNum?: string /*(商标注册证)注册号*/;
      regPerson?: string /*(商标注册证)注册人*/;
      shopId?: string /*店铺ID*/;
      shopName?: string /*店铺名称*/;
      snapshotChangeMap?: {
        [key: string]: boolean;
      } /*资质变更标记集合, key:资质类型编号, value:资质变更标记*/;
      snapshotMap?: {
        [key: string]: Array<{
          content?: string /*资质内容*/;
          qualificationType?: string /*资质类型,从资质类型编号取值*/;
          qualificationTypeName?: string /*资质类型名称*/;
          resourceType?:
            | 'RSS'
            | 'TEXT'
            | 'LINK' /*资源类型:RSS,TEXT,LINK[QualificationResourceTypeEnum]*/;
          url?: string /*资质url*/;
        }>;
      } /*资质快照集合, key:资质类型编号, value:资质快照*/;
      trademarkApplicant?: string /*商标申请人*/;
      trademarkApplyDate?: string /*商标申请日期*/;
      trademarkApplyNo?: string /*商标申请号*/;
      trademarkCategory?: string /*商标类别*/;
      trademarkName?: string /*商标名称*/;
      type?:
        | 'OWN'
        | 'AUTHORIZED'
        | 'OTHER'
        | 'PURCHASE' /*品牌类型:自有品牌OWN 授权品牌 AUTHORIZED[BrandTypeEnum]*/;
      types?: Array<{
        bizType?:
          | 'SUPPLIER'
          | 'BRAND'
          | 'GOODS'
          | 'SHOP'
          | 'BP_BRAND'
          | 'BP_GOODS'
          | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
        creator?: string /*创建人*/;
        gmtCreated?: string /*创建时间*/;
        gmtModified?: string /*更新时间*/;
        id?: string /*主键*/;
        imageResourceIds?: Array<string> /*示例图资源IDS,如:[111,222]*/;
        mode?:
          | 'USER'
          | 'SYSTEM' /*创建方式,USER-用户创建, SYSTEM-系统默认[QualificationTypeModeEnum]*/;
        modifier?: string /*修改人*/;
        name?: string /*资质类型名称*/;
        no?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
        noneImage?: boolean /*是否无示例图*/;
        sort?: number /*排序*/;
        specification?: string /*资质类型说明*/;
      }> /*资质类型信息*/;
    } /*店铺品牌资质*/;
    changeType?:
      | 'SAME'
      | 'APPEND'
      | 'MODIFY' /*过审资质变更类型-与上次过审资质实时对比结果[QualificationChangeTypeEnum]*/;
    specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
    spuQualification?: {
      changeType?:
        | 'SAME'
        | 'APPEND'
        | 'MODIFY' /*过审资质变更类型-与上次过审资质实时对比结果[QualificationChangeTypeEnum]*/;
      extension?: string /*资质扩展信息*/;
      itemVersionId?: string /*资质项版本ID*/;
      platformSpuId?: string /*平台商品ID*/;
      quantity?: number /*资质数量*/;
      snapshotChangeMap?: {
        [key: string]: boolean;
      } /*资质变更标记集合, key:资质类型编号, value:资质变更标记*/;
      snapshotMap?: {
        [key: string]: Array<{
          content?: string /*资质内容*/;
          qualificationType?: string /*资质类型,从资质类型编号取值*/;
          qualificationTypeName?: string /*资质类型名称*/;
          resourceType?:
            | 'RSS'
            | 'TEXT'
            | 'LINK' /*资源类型:RSS,TEXT,LINK[QualificationResourceTypeEnum]*/;
          url?: string /*资质url*/;
        }>;
      } /*资质快照集合, key:资质类型编号, value:资质快照*/;
      spuId?: string /*商品ID*/;
      spuName?: string /*商品名称*/;
      spuNo?: string /*商品编号*/;
      supplySource?:
        | 'HOME_BRED'
        | 'CROSS_BORDER'
        | 'IMPORT' /*商品来源,HOME_BRED:国产,IMPORT:进口,CROSS_BORDER:跨境[SupplySourceEnum]*/;
      types?: Array<{
        bizType?:
          | 'SUPPLIER'
          | 'BRAND'
          | 'GOODS'
          | 'SHOP'
          | 'BP_BRAND'
          | 'BP_GOODS'
          | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
        creator?: string /*创建人*/;
        gmtCreated?: string /*创建时间*/;
        gmtModified?: string /*更新时间*/;
        id?: string /*主键*/;
        imageResourceIds?: Array<string> /*示例图资源IDS,如:[111,222]*/;
        mode?:
          | 'USER'
          | 'SYSTEM' /*创建方式,USER-用户创建, SYSTEM-系统默认[QualificationTypeModeEnum]*/;
        modifier?: string /*修改人*/;
        name?: string /*资质类型名称*/;
        no?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
        noneImage?: boolean /*是否无示例图*/;
        sort?: number /*排序*/;
        specification?: string /*资质类型说明*/;
      }> /*资质类型信息*/;
    } /*商品资质*/;
    supplierQualification?: {
      changeType?:
        | 'SAME'
        | 'APPEND'
        | 'MODIFY' /*过审资质变更类型-与上次过审资质实时对比结果[QualificationChangeTypeEnum]*/;
      extension?: string /*资质扩展信息*/;
      itemVersionId?: string /*资质项版本ID*/;
      quantity?: number /*资质数量*/;
      registerCapital?: string /*注册资本*/;
      snapshotChangeMap?: {
        [key: string]: boolean;
      } /*资质变更标记集合, key:资质类型编号, value:资质变更标记*/;
      snapshotMap?: {
        [key: string]: Array<{
          content?: string /*资质内容*/;
          qualificationType?: string /*资质类型,从资质类型编号取值*/;
          qualificationTypeName?: string /*资质类型名称*/;
          resourceType?:
            | 'RSS'
            | 'TEXT'
            | 'LINK' /*资源类型:RSS,TEXT,LINK[QualificationResourceTypeEnum]*/;
          url?: string /*资质url*/;
        }>;
      } /*资质快照集合, key:资质类型编号, value:资质快照*/;
      supplierId?: string /*商家ID*/;
      supplierRealName?: string /*商家主体名称*/;
      types?: Array<{
        bizType?:
          | 'SUPPLIER'
          | 'BRAND'
          | 'GOODS'
          | 'SHOP'
          | 'BP_BRAND'
          | 'BP_GOODS'
          | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
        creator?: string /*创建人*/;
        gmtCreated?: string /*创建时间*/;
        gmtModified?: string /*更新时间*/;
        id?: string /*主键*/;
        imageResourceIds?: Array<string> /*示例图资源IDS,如:[111,222]*/;
        mode?:
          | 'USER'
          | 'SYSTEM' /*创建方式,USER-用户创建, SYSTEM-系统默认[QualificationTypeModeEnum]*/;
        modifier?: string /*修改人*/;
        name?: string /*资质类型名称*/;
        no?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
        noneImage?: boolean /*是否无示例图*/;
        sort?: number /*排序*/;
        specification?: string /*资质类型说明*/;
      }> /*资质类型信息*/;
      validPeriod?: string /*营业执照有效期*/;
    } /*商家资质*/;
    versionId?: string /*版本ID*/;
  } /*资质版本聚合信息*/;
  selectionRounds?: Array<{
    anchorType?:
      | 'PRIMARY_ANCHOR'
      | 'SECONDARY_ANCHOR'
      | 'TALENT_ANCHOR' /*直播间类型[AnchorTypeEnum]*/;
    applyChannel?:
      | 'INVESTMENT_PLAN_DIRECT'
      | 'INVESTMENT_PLAN_INVITE'
      | 'INVESTMENT_PLAN_LIVE_ROUND_INVITE' /*报名渠道，枚举项：招商计划；货盘邀请链接；选品邀请链接[ApplyChannelEnum]*/;
    bizType?: 'LIVE' | 'SHOP_WINDOW' | 'VIDEO_CLIP' | 'SLICE' /*业务类型[ServiceTypeEnum]*/;
    bpName?: string /*商务姓名*/;
    hasBrandFee?: boolean /*有无基础服务费*/;
    hasGifts?: boolean /*是否有赠品*/;
    id?: string /*关联记录主键*/;
    isSpec?: boolean /*是否采买*/;
    liveDateEnd?: string /*合作结束日期*/;
    liveDateStart?: string /*合作开始日期*/;
    liveRoomId?: string /*直播间Id*/;
    liveRoomImg?: string /*直播间头像*/;
    liveRoomName?: string /*直播间名称*/;
    liveRoundId?: string /*直播场次ID*/;
    liveRoundName?: string /*直播场次名称*/;
    luckyProductFlag?: boolean /*是否福袋商品*/;
    passRate?: string /*审核通过率-商务*/;
    promotionLink?: string /*上播链接*/;
    selectionRoundNo?: string /*场次货盘编号*/;
    serviceType?: string /*直播服务类型(讲解类型)*/;
    spuNo?: string /*商品编号*/;
  }> /*场次货盘信息*/;
  skus?: Array<{
    id?: string /*skuId*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
  }> /*资质审核Sku信息*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  spuImage?: string /*商品图片*/;
  spuLink?: string /*商品参考链接*/;
  spuName?: string /*商品名称*/;
  spuNo?: string /*商品编号*/;
  supplierId?: string /*商家ID*/;
  supplierName?: string /*商家主体名称*/;
  trademarkRegInfo?: {
    applicant?: string /*申请人/注册人*/;
    brandName?: string /*商标名称*/;
    dataAcquisitionTime?: string /*数据获取时间*/;
    effectiveGroup?: string /*有效群组*/;
    exclusivePermissionPeriod?: string /*商品有效期*/;
    internationalCategory?: string /*国际类别*/;
    trademarkStatus?: string /*商标状态*/;
    trademarkingNumber?: string /*商标注册号/申请号*/;
  } /*rpa返回商标信息*/;
  version?: string /*审核队列版本号*/;
  versionId?: string /*资质版本ID*/;
};

/**
 *审核详情
 */
export const detailRecordForInst = (params: DetailRecordForInstRequest) => {
  return Fetch<ResponseWithResult<DetailRecordForInstResult>>(
    '/pim/public/qualificationAudit/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationAudit/detail') },
    },
  );
};

export type CheckReSubmitRequest = {
  id?: string /*资质审核id*/;
};

export type CheckReSubmitResult = any;

/**
 *资质复查-校验是否能提交重新审核
 */
export const checkReSubmit = (params: CheckReSubmitRequest) => {
  return Fetch<ResponseWithResult<CheckReSubmitResult>>(
    '/pim/public/qualificationReviewTask/checkReSubmit',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/checkReSubmit') },
    },
  );
};

export type SupplierBodySpecialAuditLatestRequest = {
  deptId?: string /*事业部ID*/;
  supplierId?: string /*商家ID*/;
};

export type SupplierBodySpecialAuditLatestResult = {
  approvalProcessNo?: string /*审批流程编号*/;
  auditTime?: string /*特批通过时间*/;
  deptId?: string /*事业部ID*/;
  id?: string /*主键*/;
  liveRoomId?: string /*直播间ID*/;
  platform?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[PlatformEnum]*/;
  remark?: string /*特批原因*/;
  selectionRoundId?: string /*场次货盘ID*/;
  selectionRoundNo?: string /*场次货盘编号*/;
  status?:
    | 'WAIT_AUDIT'
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT'
    | 'INVALID' /*特批状态  WAIT_AUDIT：待审核，CONFIRMING：审核中，PASS：通过，REJECT：驳回，INVALID：失效[SupplierBodySpecialAuditStatusEnum]*/;
  supplierId?: string /*商家ID*/;
  supplierOrgName?: string /*商家主体名称*/;
};

/**
 *获取商家ID和事业部ID最新已审批主体特批信息
 */
export const supplierBodySpecialAuditLatest = (params: SupplierBodySpecialAuditLatestRequest) => {
  return Fetch<ResponseWithResult<SupplierBodySpecialAuditLatestResult>>(
    '/pim/public/supplierBodySpecialAudit/latest',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/supplierBodySpecialAudit/latest') },
    },
  );
};

export type GetLegalAuditQueueListRequest = {
  auditEndTime?: string /*法务审核时间区间-截止时间*/;
  auditStartTime?: string /*法务审核时间区间-开始时间*/;
  auditTypes?: Array<
    'MANUAL' | 'AUTO' | 'WHITE_LIST' | 'NON_LIVE_JOIN'
  > /*过审类型, MANUAL:人工审核, AUTO:自动过审, WHITE_LIST:白名单过审[QualificationAuditTypeEnum]*/;
  auditors?: Array<string> /*当前审核人*/;
  bpId?: string /*商务ID*/;
  brandFeeFlag?: boolean /*是否品牌费标记(true：是；false：否)*/;
  brandId?: string /*品牌id*/;
  cateId?: string /*类目ID，查询改类目及其子类目的商品*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部id*/;
  hasGifts?: boolean /*是否有赠品*/;
  hasMarkedOwner?: boolean /*是否标记归属人(是否认领)*/;
  isTestChain?: boolean /*是否侧链(true：是；false：否)*/;
  lastAuditor?: string /*上次审核人*/;
  lastRiskLevels?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待审核-NONE[QualificationRiskLevelEnum]*/;
  liveEndDate?: string /*直播日期区间-截止时间*/;
  liveRoomIds?: Array<string> /*直播间IDS*/;
  liveStartDate?: string /*直播日期区间-开始时间*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  owner?: string /*归属人(认领法务)*/;
  platform?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[PlatformEnum]*/;
  platformSpuId?: Array<string> /*平台商品ID*/;
  qualificationSupplementFlag?:
    | 'SUPPLEMENT_WAIT_AUDIT'
    | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
  riskLevelList?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*审核状态, NONE:待审核，其他都是已审核[QualificationRiskLevelEnum]*/;
  selectionRoundNo?: Array<string> /*场次货盘编号*/;
  size?: number /*分页大小*/;
  sortEnum?:
    | 'AUDIT_TIME'
    | 'GMT_CREATED'
    | 'GMT_MODIFIED'
    | 'SPU_COUNT'
    | 'RATE' /*排序字段[QualificationAuditSortEnum]*/;
  sortType?: 'ASC' | 'DESC' /*排序方式[SortTypeEnum]*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  spuNames?: Array<string> /*商品名称关键字*/;
  spuNo?: Array<string> /*商品编号*/;
  supplierIds?: Array<string> /*商家ID*/;
};

export type GetLegalAuditQueueListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    auditTime?: string /*法务审核时间*/;
    auditType?:
      | 'MANUAL'
      | 'AUTO'
      | 'WHITE_LIST'
      | 'NON_LIVE_JOIN' /*过审类型, MANUAL:人工审核, AUTO:自动过审, WHITE_LIST:白名单过审[QualificationAuditTypeEnum]*/;
    auditorName?: string /*当前审核人姓名*/;
    bpName?: string /*商务姓名*/;
    brandFeeFlag?: boolean /*是否品牌费标记(true：是；false：否)*/;
    brandId?: string /*品牌ID*/;
    brandQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*当前店铺品牌资质审核状态[QualificationAuditStateEnum]*/;
    cateNamePath?: string /*类目名称全路径*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键*/;
    isTestChain?: boolean /*是否侧链(true：是；false：否)*/;
    lastAuditorName?: string /*上次审核人姓名*/;
    lastBrandQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*上次店铺品牌资质审核状态[QualificationAuditStateEnum]*/;
    lastRiskLevel?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*上次风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待审核-NONE[QualificationRiskLevelEnum]*/;
    lastSpuQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*上次商品资质审核状态[QualificationAuditStateEnum]*/;
    lastSupplierQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*上次商家资质审核状态[QualificationAuditStateEnum]*/;
    ownerName?: string /*归属人姓名(认领法务)*/;
    platformSpuId?: string /*平台商品ID*/;
    previousVersion?: boolean /*是否为最新审核通过的版本，true：表示法务可以调整等级*/;
    qualificationSupplementFlag?:
      | 'SUPPLEMENT_WAIT_AUDIT'
      | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
    riskLevel?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待审核-NONE[QualificationRiskLevelEnum]*/;
    shopId?: string /*店铺ID*/;
    specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
    spuId?: string /*商品ID*/;
    spuImage?: string /*商品图片*/;
    spuName?: string /*商品名称*/;
    spuNo?: string /*商品编号*/;
    spuQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*当前商品资质审核状态[QualificationAuditStateEnum]*/;
    supplierId?: string /*商家ID*/;
    supplierQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*当前商家资质审核状态[QualificationAuditStateEnum]*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *审核队列分页查询
 */
export const getLegalAuditQueueList = (params: GetLegalAuditQueueListRequest) => {
  return Fetch<ResponseWithResult<GetLegalAuditQueueListResult>>(
    '/pim/public/qualificationAudit/queue',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationAudit/queue') },
    },
  );
};

export type QualificationAuditResubmitRequest = {
  auditOpinion?: string /*法务意见备注*/;
  automatedAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING'
    | 'PASS_SPECIAL' /*自动化审核结果[SupplierQualificationAuditResultEnum]*/;
  automatedSnapshotId?: string /*自动化审核id*/;
  brandAutomatedAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING' /*品牌自动化审核结果[BrandQualificationAuditResultEnum]*/;
  brandAutomatedSnapshotId?: string /*品牌自动化审核id*/;
  details?: Array<{
    auditOpinion?: string /*审核意见*/;
    auditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
    bizType?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'GOODS'
      | 'SHOP'
      | 'BP_BRAND'
      | 'BP_GOODS'
      | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
    isBrandWhitelisted?: boolean /*是否白名单B*/;
    itemVersionId?: string /*资质项版本ID*/;
    remark?: string /*备注*/;
  }> /*审核请求明细*/;
  expirationDate?: string /*法务审核有效期*/;
  expirationDates?: Array<{
    bizType?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'GOODS'
      | 'SHOP'
      | 'BP_BRAND'
      | 'BP_GOODS'
      | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
    expirationDate?: string /*法务审核有效期*/;
    itemVersionId?: string /*资质项版本ID*/;
    qualificationsTypeNo?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
  }> /*审核有效期*/;
  goodsAutoAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING' /*商品自动化审核结果[GoodsQualificationAuditResultEnum]*/;
  goodsSnapshotId?: string /*商品自动化审核id*/;
  id?: string /*审核记录主键*/;
  isSpecialMaterial?: boolean /*是否特殊材质(1:是,0:否,无默认值)*/;
  isSupplierBodySpecialAuditPass?: boolean /*是否商家主体特批通过*/;
  riskLevel?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待处理-NONE[QualificationRiskLevelEnum]*/;
  selectionRoundExtensions?: Array<{
    id?: string /*关联记录主键*/;
    promotionLink?: string /*上播链接*/;
  }> /*场次货盘扩展请求*/;
  trademarkNumber?: string /*商标注册号/申请号*/;
  version?: string /*版本号-乐观锁*/;
  versionId?: string /*资质版本ID*/;
};

export type QualificationAuditResubmitResult = any;

/**
 *资质复查-重新审核
 */
export const qualificationAuditResubmit = (params: QualificationAuditResubmitRequest) => {
  return Fetch<ResponseWithResult<QualificationAuditResubmitResult>>(
    '/pim/public/qualificationReviewTask/reSubmit',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/reSubmit') },
    },
  );
};

export type QualityWhiteListValidRequest = {
  brandName?: string /*品牌名称*/;
};

export type QualityWhiteListValidResult = boolean;

/**
 *查询该品牌是否是免质检白名单
 */
export const qualityWhiteListValid = (params: QualityWhiteListValidRequest) => {
  return Fetch<ResponseWithResult<QualityWhiteListValidResult>>(
    '/iasm/public/qualityWhiteList/valid',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/qualityWhiteList/valid') },
    },
  );
};

export type QualificationReviewTaskBatchPassRequest = {
  qualificationReviewTaskIdList?: Array<string> /*资质审核任务id列表*/;
};

export type QualificationReviewTaskBatchPassResult = any;

/**
 *资质复查-批量通过审核
 */
export const qualificationReviewTaskBatchPass = (
  params: QualificationReviewTaskBatchPassRequest,
) => {
  return Fetch<ResponseWithResult<QualificationReviewTaskBatchPassResult>>(
    '/pim/public/qualificationReviewTask/batchPass',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/batchPass') },
    },
  );
};

export type QualificationReviewTaskReCheckRequest = {
  qualificationReviewTaskId?: string /*资质审核任务id*/;
  reviewComments?: string /*复查意见*/;
};

export type QualificationReviewTaskReCheckResult = any;

/**
 *资质复查-重新复核
 */
export const qualificationReviewTaskReCheck = (params: QualificationReviewTaskReCheckRequest) => {
  return Fetch<ResponseWithResult<QualificationReviewTaskReCheckResult>>(
    '/pim/public/qualificationReviewTask/reCheck',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/reCheck') },
    },
  );
};

export type QualificationReviewTaskCompleteCheckRequest = {
  qualificationReviewTaskId?: string /*资质审核任务id*/;
  reviewComments?: string /*复查意见*/;
};

export type QualificationReviewTaskCompleteCheckResult = any;

/**
 *资质复查-复核完成
 */
export const qualificationReviewTaskCompleteCheck = (
  params: QualificationReviewTaskCompleteCheckRequest,
) => {
  return Fetch<ResponseWithResult<QualificationReviewTaskCompleteCheckResult>>(
    '/pim/public/qualificationReviewTask/completeCheck',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/completeCheck') },
    },
  );
};

export type QualificationReviewTaskBatchCompleteCheckRequest = {
  qualificationReviewTaskIdList?: Array<string> /*资质审核任务id列表*/;
};

export type QualificationReviewTaskBatchCompleteCheckResult = any;

/**
 *资质复查-批量复核完成
 */
export const qualificationReviewTaskBatchCompleteCheck = (
  params: QualificationReviewTaskBatchCompleteCheckRequest,
) => {
  return Fetch<ResponseWithResult<QualificationReviewTaskBatchCompleteCheckResult>>(
    '/pim/public/qualificationReviewTask/batchCompleteCheck',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/batchCompleteCheck') },
    },
  );
};

export type QualificationReviewTaskDetailRequest = {
  qualificationReviewTaskId?: string /*资质审核任务id*/;
};

export type QualificationReviewTaskDetailResult = {
  historyAuditResult?: Array<{
    auditTime?: string /*法务审核时间*/;
    auditType?:
      | 'MANUAL'
      | 'AUTO'
      | 'WHITE_LIST'
      | 'NON_LIVE_JOIN' /*过审类型, MANUAL:人工审核, AUTO:自动过审, WHITE_LIST:白名单过审[QualificationAuditTypeEnum]*/;
    auditorName?: string /*当前审核人姓名*/;
    brandId?: string /*品牌ID*/;
    brandQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*当前店铺品牌资质审核状态[QualificationAuditStateEnum]*/;
    gmtCreated?: string /*送达法务时间*/;
    id?: string /*主键*/;
    platformSpuId?: string /*平台商品ID*/;
    riskLevel?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待审核-NONE[QualificationRiskLevelEnum]*/;
    shopId?: string /*店铺ID*/;
    spuId?: string /*商品ID*/;
    spuName?: string /*商品名称*/;
    spuNo?: string /*商品编号*/;
    spuQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*当前商品资质审核状态[QualificationAuditStateEnum]*/;
    supplierId?: string /*商家ID*/;
    supplierQualificationAuditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*当前商家资质审核状态[QualificationAuditStateEnum]*/;
  }> /*历史审核结果*/;
  qualificationReviewTaskCode?: string /*资质复查任务编号*/;
  qualificationReviewTaskId?: string /*主键*/;
  skuList?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    purchasePrice?: string /*采购价（含税）*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
    taxRate?: string /*税率（%）*/;
  }> /*SKU信息列表*/;
  status?: 'FINISH' | 'WAIT_REVIEW' | 'WAIT_CHECK' /*任务状态[QualificationReviewTaskStatusEnum]*/;
};

/**
 *资质复查-复查任务详情
 */
export const qualificationReviewTaskDetail = (params: QualificationReviewTaskDetailRequest) => {
  return Fetch<ResponseWithResult<QualificationReviewTaskDetailResult>>(
    '/pim/public/qualificationReviewTask/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationReviewTask/detail') },
    },
  );
};

export type QueryOperationLogListRequest = {
  bizOrderId?: string /*业务单据id*/;
  bizOrderNo?: string /*业务单据编号*/;
  bizOrderType?: string /*业务单据类型*/;
  bizOrderTypeList?: Array<string> /*业务单据类型*/;
  bizTypeModels?: Array<{
    bizOrderType?: string /*业务单据类型*/;
    secondBizTypes?: Array<string> /*二级业务单据类型*/;
  }> /*一二级业务单据类型集合*/;
  current?: number /*当前页码,从1开始*/;
  employeeIdList?: Array<string> /*用户id*/;
  operateDesc?: string /*操作类型描述*/;
  operateEndTime?: string /*操作结束时间*/;
  operateStartTime?: string /*操作开始时间*/;
  operateType?: string /*操作类型*/;
  operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
  operatorName?: string /*操作人名称*/;
  size?: number /*分页大小*/;
};

export type QueryOperationLogListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    bizOrderId?: string /*业务单据id*/;
    bizOrderNo?: string /*业务单据编号*/;
    bizOrderType?: string /*业务单据类型*/;
    contentSnapshot?: string /*操作之前的快照*/;
    id?: string /*主键*/;
    operateDesc?: string /*操作类型*/;
    operateTime?: string /*操作时间*/;
    operateType?: string /*操作类型*/;
    operatorAccount?: string /*操作人账号*/;
    operatorAccountType?: string /*账号类型 TALENT:达人端/SUPPLIER:商家端/PLATFORM:平台端/INSTITUTION:机构端*/;
    operatorContent?: string /*操作内容*/;
    operatorId?: string /*操作人id*/;
    operatorName?: string /*操作人姓名*/;
    secondBizType?: string /*二级业务单据类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询操作日志
 */
export const queryOperationLogList = (params: QueryOperationLogListRequest) => {
  return Fetch<ResponseWithResult<QueryOperationLogListResult>>(
    '/tools/public/operation/queryOperationLogList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/operation/queryOperationLogList') },
    },
  );
};
