import React, { Ref, useEffect, useRef, useState } from 'react';
import { AuthWrapper, history, OneAuthWrapper } from 'qmkit';
import PageLayout from '@/components/PageLayout';
import styles from './index.module.less';
import style from '@/styles/index.module.less';
import { useLocation } from 'react-router-dom';
import { SearchForm, RiskLevelDrawer, RiskLevelDetailDrawer } from './components';
import { useTableHeight } from '@/common/constants/hooks/index';
import { Button, Table, Modal, message, Tabs } from 'antd';
import { useTable, useRowSelection, useList, APIKEY, useBtns } from './hooks';
import {
  QualificationReviewTaskPageRequest,
  QualificationReviewTaskPageResult,
  qualificationReviewTaskPass,
  // checkReSubmit,
  DetailRecordForInstResult,
  detailRecordForInst,
  supplierBodySpecialAuditLatest,
  SupplierBodySpecialAuditLatestRequest,
  // GetLegalAuditQueueListRequest,
  // getLegalAuditQueueList,
  SupplierBodySpecialAuditLatestResult,
  qualificationReviewTaskBatchPass,
  qualificationReviewTaskReCheck,
  qualificationReviewTaskBatchCompleteCheck,
  qualificationReviewTaskCompleteCheck,
} from './services/yml';
import PaginationProxy from '@/common/constants/Pagination';
import { useRequest, useSetState } from 'ahooks';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { QualificationReviewTaskPageResultItem } from './hooks/useTable';
import { TASK_STATUS } from './types';
import { checkAuth } from 'qmkit';
import { debounce } from 'lodash';

const ReviewTask: React.FC = () => {
  const { getHeight, tableHeight } = useTableHeight(100);
  const [keys, setKeys] = useState<'LUXURY' | 'HIGH_VALUE' | 'LUXURY_LIVE_AFTER' | ''>(
    checkAuth('f_queues_review_task_high_list')
      ? 'LUXURY'
      : checkAuth('f_queues_review_task_list')
      ? 'HIGH_VALUE'
      : checkAuth('f_queues_review_task_check_after_broadcast')
      ? 'LUXURY_LIVE_AFTER'
      : '',
  );

  const searchFormRef = useRef<any>(null);

  // 保存点击的重新审核按钮的数据
  // const saveItem: Ref<QualificationReviewTaskPageResultItem> = useRef({});

  // const [historyList, setHistoryList] = useState([]);

  // 重新审核参数
  const [drawer, setDrawer] = useSetState<{
    visible: boolean;
    current: DetailRecordForInstResult | null;
    isDetail: boolean;
    // isCheckDetail: boolean; // 点击查看详情按钮
    loading: boolean;
    supplierBodyDetail?: SupplierBodySpecialAuditLatestResult;
  }>({
    visible: false,
    current: null,
    isDetail: false,
    // isCheckDetail: false, // 点击查看详情按钮为true
    loading: false,
  });

  // 重新审核里面的历史记录
  // const qulificationHistoryListFn = async (value: any) => {
  //   console.log('value111', value);
  //   const params: GetLegalAuditQueueListRequest = {
  //     size: 10,
  //     current: 1,
  //     platformSpuId: [value.platformSpuId],
  //     sortEnum: 'GMT_CREATED',
  //     riskLevelList: ['HIGH', 'MIDDLE', 'LOW', 'PASS'],
  //     sortType: 'DESC',
  //   };
  //   const { res } = await getLegalAuditQueueList(params);
  //   console.log('res001', res);
  //   if (res.code === '200') {
  //     setHistoryList(res?.result?.records || []);
  //   }
  // };

  // 批量通过审核
  const {
    run: qualificationReviewTaskBatchPassRun,
    loading: qualificationReviewTaskBatchPassLoading,
  } = useRequest(qualificationReviewTaskBatchPass, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('操作成功');
        onRefresh();
        clearSelection();
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  // 批量复核完成
  const {
    run: qualificationReviewTaskBatchCompleteCheckRun,
    loading: qualificationReviewTaskBatchCompleteCheckLoading,
  } = useRequest(qualificationReviewTaskBatchCompleteCheck, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('操作成功');
        onRefresh();
        clearSelection();
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  // 获取资质特批先关信息
  const getSupplierBodyDetail = async (params: SupplierBodySpecialAuditLatestRequest) => {
    const result = await responseWithResultAsync({
      request: supplierBodySpecialAuditLatest,
      params,
    });
    setDrawer((state) => ({ ...state, supplierBodyDetail: result ?? undefined }));
  };
  const location = useLocation();

  // 获取列表
  const {
    dataSource,
    pagination,
    onPageChange,
    onSearch,
    onRefresh,
    loading,
    condition,
    setDataSource,
  } = useList<QualificationReviewTaskPageRequest, QualificationReviewTaskPageResult>(
    APIKEY.REVIEW_TASK,
  );

  const checkJumpAudit = async (item: QualificationReviewTaskPageResultItem) => {
    setDrawer({
      loading: true,
    });
    try {
      const { res } = await detailRecordForInst({
        id: item?.id,
      });
      const { result, code, message: resMessgae } = res;
      if (code !== '200') {
        message.error(resMessgae || '网络异常');
        setDrawer({
          loading: false,
        });
        return;
      }
      if (!res.result) {
        message.warn('审批流程不存在，请确认');
        setDrawer({
          loading: false,
        });
        return;
      }

      if (result?.isSupplierBodySpecialAuditPass) {
        await getSupplierBodyDetail({ deptId: result?.deptId, supplierId: item?.supplierId });
      }
      // qulificationHistoryListFn(result);

      setDrawer({
        visible: true,
        current: result,
        isDetail: true,
        // isCheckDetail: false,
        // supplierBodyDetail: undefined,
      });
    } catch (error) {
      console.log('🚀 ~ file: getColumns.tsx:46 ~ error:', error);
      setDrawer({
        loading: false,
      });
    }
  };

  // const { run: checkReSubmitRun, loading: checkReSubmitLoading } = useRequest(checkReSubmit, {
  //   manual: true,
  //   onSuccess({ res }) {
  //     // 不符合重新审核
  //     if (!res?.success) {
  //       Modal.confirm({
  //         title: '当前商品有进行中的审核队列,请与审核人沟通',
  //         content: res?.message,
  //         okText: '前往查看',
  //         cancelText: '取消',
  //         onOk() {
  //           history.push('/legal-audit/queues');
  //         },
  //       });
  //       return;
  //     }
  //     // 符合重新审核
  //     checkJumpAudit(saveItem.current as any);
  //     // console.log(saveItem, '------------->');
  //   },
  // });

  const { columns } = useTable({ onRefresh, checkJumpAudit, keys });

  const { rowSelection, selectedRows, selectedRowKeys, clearSelection } = useRowSelection();

  const handleReviewSubmit = () => {
    // console.log(selectedRows, selectedRowKeys, '---->');
    if (!selectedRowKeys?.length) {
      message.warning('请选择待复查状态的复查任务~');
      return;
    }
    // 复查通过先判断数据的任务状态
    const taskStatus = selectedRows?.every((item) => item.status === TASK_STATUS.REVIEWED);
    if (!taskStatus) {
      // 存在不是待复查状态的数据
      message.warning('请选择待复查状态的复查任务~');
      return;
    }
    Modal.confirm({
      title: `当前选中了${selectedRowKeys?.length}个商品, 请确认是否批量通过?`,
      okText: '确定',
      cancelText: '取消',
      onOk: debounce(() => {
        qualificationReviewTaskBatchPassRun({
          qualificationReviewTaskIdList: selectedRowKeys,
        });
      }, 1000),
    });
  };

  // 批量复核完成
  const handleCompletionOfReview = () => {
    if (!selectedRowKeys?.length) {
      message.warning('请选择待确认状态的复查任务~');
      return;
    }
    // 复查通过先判断数据的任务状态
    const taskStatus = selectedRows?.every((item) => item.status === TASK_STATUS.CONFIRMING);
    if (!taskStatus) {
      // 存在不是待确认状态的数据
      message.warning('请选择待确认状态的复查任务~');
      return;
    }
    Modal.confirm({
      title: `当前选中了${selectedRowKeys?.length}个商品, 请确认是否批量复核完成?`,
      okText: '确定',
      cancelText: '取消',
      onOk: debounce(() => {
        qualificationReviewTaskBatchCompleteCheckRun({
          qualificationReviewTaskIdList: selectedRowKeys,
        });
      }, 1000),
    });
  };

  useEffect(() => {
    // 属性接口
    onSearch({ goodsType: keys });
  }, [location, keys]);

  const handleTabs = (value: any) => {
    setDataSource([] as any);
    clearSelection();
    setKeys(value);
    searchFormRef?.current?.resetForm();
  };
  return (
    <OneAuthWrapper
      functionName="f_queues_review_task_list,f_queues_review_task_high_list"
      showType="page"
    >
      <PageLayout
        routePath="/queues-review-task"
        className={`${styles.publishFeeManageContainer} ${style['publish-fee-page']}`}
      >
        <div
          className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
          style={{ display: 'flex', flexDirection: 'column' }}
        >
          <div className="formHeight">
            <Tabs onChange={handleTabs} style={{ marginTop: '-10px' }}>
              {checkAuth('f_queues_review_task_high_list') && (
                <Tabs.TabPane tab="高奢商品" key="LUXURY"></Tabs.TabPane>
              )}
              {checkAuth('f_queues_review_task_list') && (
                <Tabs.TabPane tab="高价值商品" key="HIGH_VALUE"></Tabs.TabPane>
              )}
              {checkAuth('f_queues_review_task_check_after_broadcast') && (
                <Tabs.TabPane tab="高奢播后复查" key="LUXURY_LIVE_AFTER"></Tabs.TabPane>
              )}
            </Tabs>
            <SearchForm
              onSearch={(value) => {
                clearSelection();
                onSearch({ ...value, goodsType: keys });
              }}
              getTableHeight={getHeight}
              onRef={searchFormRef}
            />
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <AuthWrapper functionName="f_queues_review_task_pass">
                <Button type="primary" onClick={handleReviewSubmit}>
                  批量复查通过
                </Button>
              </AuthWrapper>
              <AuthWrapper functionName="f_queues_review_task_complete_check">
                <Button
                  type="primary"
                  style={{ marginLeft: '8px' }}
                  onClick={handleCompletionOfReview}
                >
                  批量复核完成
                </Button>
              </AuthWrapper>
            </div>
          </div>
          <div style={{ flex: 1, marginTop: '8px' }}>
            <Table
              columns={columns}
              pagination={false}
              dataSource={dataSource as any[]}
              rowKey={(record: { [key: string]: any }) => `${record.id}`}
              rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
              scroll={{ y: tableHeight, x: '100%' }}
              loading={
                loading ||
                drawer.loading ||
                qualificationReviewTaskBatchPassLoading ||
                qualificationReviewTaskBatchCompleteCheckLoading
              }
              rowSelection={rowSelection}
            />
          </div>
          <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
            <PaginationProxy
              {...pagination}
              onChange={({ current, size }: any) => {
                // console.log('🚀 ~ SelectionFlowBoard ~ res:', res);
                onPageChange(current, size);
              }}
              valueType="merge"
            />
          </div>
        </div>
        <RiskLevelDetailDrawer
          visible={drawer.visible}
          current={drawer.current}
          // historyList={historyList}
          isDetail={drawer.isDetail}
          // isCheckDetail={drawer.isCheckDetail}
          supplierBodyDetail={drawer.supplierBodyDetail}
          handleClose={() => {
            setDrawer({
              visible: false,
              current: null,
              isDetail: false,
              isCheckDetail: false,
              supplierBodyDetail: undefined,
              loading: false,
            });
            // onRefresh();
            // saveItem.current = {};
          }}
        />
        {/* <RiskLevelDrawer
          visible={drawer.visible}
          current={drawer.current}
          historyList={historyList}
          isDetail={drawer.isDetail}
          isCheckDetail={drawer.isCheckDetail}
          supplierBodyDetail={drawer.supplierBodyDetail}
          checkJumpDetail={() => {
            //
          }}
          handleClose={() => {
            setDrawer({
              visible: false,
              current: null,
              isDetail: false,
              isCheckDetail: false,
              supplierBodyDetail: undefined,
              loading: false,
            });
            onRefresh();
            saveItem.current = {};
          }}
        /> */}
      </PageLayout>
    </OneAuthWrapper>
  );
};

export default ReviewTask;
