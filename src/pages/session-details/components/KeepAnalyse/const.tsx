import moment from 'moment';

export const processData = (origin, { startTime = null, endTime = null } = {}) => {
  const result = {};
  if (!origin) {
    return;
  }
  if (startTime && endTime) {
    console.log('我看下有没开始时间和结束时间', startTime, endTime);
    const arr = [];
    let start = moment(startTime).startOf('minutes').valueOf();
    const end = moment(endTime).startOf('minutes').valueOf();
    // arr.push(moment(start).format('HH:mm'));
    while (moment(start).valueOf() <= end) {
      arr.push(moment(start).format('HH:mm'));
      if (!result[moment(start).format('HH:mm')]) {
        result[moment(start).format('HH:mm')] = {
          bargain_list: [],
          club_list: [],
          conver_list: [],
          flow_list: [],
          lottery_list: [],
          pop_list: [],
          up_list: [],
        };
      }
      start = moment(start).add(1, 'minutes').valueOf();
    }
    // console.log('计算结果', arr);
  }
  for (const key in origin) {
    if (origin[key] && Array.isArray(origin[key])) {
      origin[key].forEach((item) => {
        const timeKey = moment(item?.date).format('HH:mm');
        if (!result[timeKey]) {
          result[timeKey] = {
            bargain_list: [],
            club_list: [],
            conver_list: [],
            flow_list: [],
            lottery_list: [],
            pop_list: [],
            up_list: [],
          };
          result[timeKey][key].push(item);
        } else {
          result[timeKey][key].push(item);
        }
      });
    }
  }
  return result;
};
export const splitData = (data) => {
  const result = {
    // 所有日期
    dates: [],
    // 在线人数
    watchUserCount: [],
    // 进场人数
    watchInc: [],
    // 离场人数
    outInc: [],
    // ---------------
    // 销量（增量）
    salesInc: [],
    // 销量（总量)
    sales: [],
    // 销售额（增量)
    gmvInc: [],
    // 销售额（总量）
    gmv: [],
    // ---------------
    // 涨粉数
    fansInc: [],
    // 新增粉丝团
    fansClubInc: [],
    // --------
    // 福袋
    lottery_list: [],
    // 上架
    up_list: [],
    // 讲解
    pop_list: [],
  };
  for (const date in data) {
    result.dates.push(date);
    // 人气指标
    if (data[date]['flow_list'][0]) {
      result.watchUserCount.push(data[date]['flow_list'][0]?.watchUserCount || null);
      result.watchInc.push(data[date]['flow_list'][0]?.watchInc);
      result.outInc.push(data[date]['flow_list'][0]?.outInc);
    } else {
      result.watchUserCount.push(null);
      result.watchInc.push(null);
      result.outInc.push(null);
    }
    // 成交指标
    if (data[date]['bargain_list'][0]) {
      result.salesInc.push(data[date]['bargain_list'][0]?.salesInc);
      result.sales.push(data[date]['bargain_list'][0]?.sales);
      result.gmvInc.push(data[date]['bargain_list'][0]?.gmvInc);
      result.gmv.push(data[date]['bargain_list'][0]?.gmv);
    } else {
      result.salesInc.push(null);
      result.sales.push(null);
      result.gmvInc.push(null);
      result.gmv.push(null);
    }
    // 互动指标
    if (data[date]['conver_list'][0]) {
      result.fansInc.push(data[date]['conver_list'][0]?.fansInc);
    } else {
      result.fansInc.push(null);
    }
    if (data[date]['club_list'][0]) {
      result.fansClubInc.push(data[date]['club_list'][0]?.fansClubInc);
    } else {
      result.fansClubInc.push(null);
    }
    // 福袋
    if (data[date]['lottery_list'][0]) {
      result.lottery_list.push(0);
    } else {
      result.lottery_list.push(null);
    }
    // 上架
    if (data[date]['up_list'][0]) {
      result.up_list.push(0);
    } else {
      result.up_list.push(null);
    }
    // 讲解
    if (data[date]['pop_list'][0]) {
      result.pop_list.push(0);
    } else {
      result.pop_list.push(null);
    }
  }
  return result;
};
