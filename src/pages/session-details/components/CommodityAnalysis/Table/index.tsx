import React, { useContext, useMemo } from 'react';
import { TableProxy } from 'web-common-modules/components';
import styles from './index.module.less';
import { Icon, Tooltip } from 'antd';
import { Context } from '../index';
import GoodsDetailCommon from '../GoodsDetailCommon';
import PaginationProxy from '@/common/constants/Pagination';
import classnames from 'classnames';
const Table = () => {
  const {
    tableHeaderList,
    pagination,
    setPagination,
    setSort,
    list,
    loading,
    roomId,
    uid,
    handleGetList,
  } = useContext(Context);

  const getColumns = useMemo(() => {
    if (uid) {
      return [
        {
          dataIndex: 'shopInfo',
          title: '商品信息',
          render: (_: any, record: any) => (
            <div className="flex">
              <img className="h-72 w-72 mr-8" src={record?.cover_url} alt="" />
              <div>
                <GoodsDetailCommon id={record?.product_id}>
                  <Tooltip title={record?.title}>
                    <div
                      className={classnames(
                        styles.shopInfoTitle,
                        'font-body color-font-1 text-ellipsis cursor-pointer',
                      )}
                      style={{ minWidth: 250 }}
                    >
                      {record?.title}
                    </div>
                  </Tooltip>
                </GoodsDetailCommon>
                <div className="font-secondary color-font-3">{record?.category || '-'}</div>
                <div className="font-secondary color-font-3">品牌 {record?.brand}</div>
                <div className="font-secondary color-font-3">ID {record?.product_id}</div>
              </div>
            </div>
          ),
        },
        {
          dataIndex: 'live_price',
          title: '售卖价',
          render: (val: string, record: any) => (
            <div>
              <div className="font-body">¥{val}</div>
              <div
                className="font-secondary color-font-3"
                style={{ textDecoration: 'line-through' }}
              >
                ¥{record?.coupon_price}
              </div>
            </div>
          ),
          sorter: true,
        },
        {
          dataIndex: 'cos_ratio',
          title: '佣金比例',
          sorter: true,
        },
        {
          dataIndex: 'sales_gmv',
          title: '预估销售额',
          render: (val: string) => `¥${val}`,
          sorter: true,
        },
        {
          dataIndex: 'sales',
          title: '预估销量',
          render: (val: string) => `${val}`,
          sorter: true,
        },
      ];
    }
    if (roomId) {
      const columns: any[] = [
        {
          dataIndex: 'shopInfo',
          title: '商品信息',
          fixed: 'left',
          render: (_: void, record: any) => (
            <div className="flex">
              <img className="h-72 w-72 mr-8" src={record?.cover_url} alt="" />
              <div className="w-250">
                <GoodsDetailCommon id={record?.product_id}>
                  <Tooltip title={record?.title}>
                    <div
                      className="font-body color-font-1 text-ellipsis cursor-pointer"
                      style={{ maxWidth: 250 }}
                    >
                      {record?.title}
                    </div>
                  </Tooltip>
                </GoodsDetailCommon>
                <Tooltip title={record?.category}>
                  <div className="font-secondary color-font-3 text-ellipsis">
                    {record?.category || '-'}
                  </div>
                </Tooltip>
                <Tooltip title={record?.brand}>
                  <div className="font-secondary color-font-3 text-ellipsis">
                    品牌 {record?.brand}
                  </div>
                </Tooltip>
                <div className="font-secondary color-font-3">ID {record?.product_id}</div>
              </div>
            </div>
          ),
        },
      ];
      tableHeaderList?.map((item) => {
        if (item.value === 'live_price') {
          columns.push({
            dataIndex: item.value,
            title: item.label,
            render: (val: string, record: any) => (
              <div>
                <div>{val}</div>
                {record?.activity && <div className={styles.tag}>{record?.activity}</div>}
              </div>
            ),
          });
          return;
        }
        if (item.value === 'shelf_sales') {
          columns.push({
            dataIndex: item.value,
            title: item.label,
            render: (val: string, record: any) => `${val} ~ ${record?.sold_out_sales}`,
          });
          return;
        }
        if (item.value === 'vis_rate') {
          columns.push({
            dataIndex: item.value,
            title: (
              <div>
                {item.label}
                <Tooltip title="=商品销量/点击数">
                  <Icon className="ml-4" type="question-circle" />
                </Tooltip>
              </div>
            ),
          });
          return;
        }
        columns.push({
          dataIndex: item.value,
          title: item.label,
          render: (val: string) =>
            ['live_price', 'sales_gmv'].includes(item.value) ? `¥${val}` : val,
          sorter: ['cos_ratio', 'sales'].includes(item.value),
        });
      });

      return columns;
    }
    return [];
  }, [tableHeaderList]);

  return (
    <>
      <TableProxy
        className={styles.table}
        key="id"
        loading={loading}
        scroll={{ x: 'max-content' }}
        pagination={false}
        columns={getColumns}
        dataSource={list}
        onChange={(_, _filters, sorter: any) => {
          const { sortField, sortOrder } = sorter;
          setSort({
            sort_field: sortField,
            sort_type: sortOrder?.toLowerCase(),
          });
          handleGetList({
            sort_field: sortField,
            sort_type: sortOrder?.toLowerCase(),
          });
        }}
      />

      <PaginationProxy
        current={pagination?.current}
        pageSize={pagination?.size}
        total={pagination?.total}
        className={styles.paginationProxy}
        // @ts-ignore
        onChange={(current, size) => {
          setPagination({
            current,
            size,
          });
          handleGetList({
            page: current,
            size,
          });
        }}
        valueType="flatten"
        showQuickJumper={true}
        showSizeChanger={false}
      />
    </>
  );
};
export default Table;
