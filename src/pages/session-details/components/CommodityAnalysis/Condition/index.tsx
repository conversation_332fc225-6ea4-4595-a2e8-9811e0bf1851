import { Input, InputNumber } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import styles from './index.module.less';
import classnames from 'classnames';
import { MaxMin } from './MaxMin';
import { useSetState } from 'ahooks';
import { CateItemTypeForSearchInput } from 'web-common-modules/biz/CateCondition/utils';
import FilterOptions from './FilterOptions';
import CateCondition from '../../LiveCateCondition';
import ShowMetrics from './ShowMetrics';
import { Context } from '..';

export interface ICondition {
  cate?: Array<CateItemTypeForSearchInput>;
  kwd?: string;
  brand?: string;
  salsget?: number | undefined;
  gmvget?: number | undefined;
  commssion_rate?: number | undefined;
  price?: (number | undefined)[];
  price_max?: number;
  price_min?: number;
}

const Condition = () => {
  const { roomId, uid, start_date, end_date, handleGetList, setPagination } = useContext(Context);
  const [condition, setCondition] = useSetState<ICondition>({
    cate: [],
    kwd: '',
    brand: '',
  });
  const [isChange, setIsChange] = useState<boolean>(false);
  const handleUpdateCondition = (val: Partial<ICondition>) => {
    setCondition(val);
  };

  const onReset = () => {
    setCondition({
      cate: [],
      kwd: '',
      brand: '',
      salsget: undefined,
      gmvget: undefined,
      commssion_rate: undefined,
      price: [],
    });
  };

  useEffect(() => {
    if (!isChange) return;
    const { cate, kwd, brand, salsget, gmvget, commssion_rate, price } = condition;

    const _cate =
      cate
        ?.map((item) => {
          const { level, level1, level2, id } = item;
          if (level === 0) {
            return id;
          }
          if (level === 1) {
            return level1?.id;
          }
          if (level === 2) {
            return level2?.id;
          }
        })
        .filter(Boolean) || [];
    handleGetList({
      cate: _cate,
      kwd,
      brand,
      salsget,
      gmvget,
      commssion_rate,
      price_min: price?.[0],
      price_max: price?.[1],
      page: 1,
    });
    setPagination({ current: 1 });
    setIsChange(false);
  }, [isChange]);

  return (
    <div className={styles.condition}>
      <div className={styles.conditionItem}>
        <CateCondition
          roomId={roomId}
          uid={uid}
          start_date={start_date}
          end_date={end_date}
          condition={condition}
          updateCondition={(v) => {
            handleUpdateCondition(v);
            setIsChange(true);
          }}
        />
      </div>
      <div className={styles.conditionItem}>
        <label className={classnames('font-body', styles.label, 'mr-8')}>条件筛选：</label>
        <div className={styles.conditionInner}>
          <dl>
            <dt>名称</dt>
            <dd>
              <Input
                value={condition?.kwd}
                onChange={(e) =>
                  handleUpdateCondition({
                    kwd: e.target.value,
                  })
                }
                onPressEnter={() => setIsChange(true)}
                onBlur={() => setIsChange(true)}
                placeholder="关键词搜索"
              />
            </dd>
          </dl>
          <dl>
            <dt>品牌</dt>
            <dd>
              <Input
                value={condition?.brand}
                onChange={(e) =>
                  handleUpdateCondition({
                    brand: e.target.value,
                  })
                }
                onPressEnter={() => setIsChange(true)}
                onBlur={() => setIsChange(true)}
                placeholder="关键词搜索"
              />
            </dd>
          </dl>
          <dl>
            <dt>销量 ≥</dt>
            <dd>
              <InputNumber
                value={condition?.salsget}
                onPressEnter={() => setIsChange(true)}
                onBlur={() => setIsChange(true)}
                onChange={(v) =>
                  handleUpdateCondition({
                    salsget: v,
                  })
                }
              />
            </dd>
          </dl>
          <dl>
            <dt>销售额 ≥</dt>
            <dd>
              <InputNumber
                value={condition?.gmvget}
                onPressEnter={() => setIsChange(true)}
                onBlur={() => setIsChange(true)}
                onChange={(v) =>
                  handleUpdateCondition({
                    gmvget: v,
                  })
                }
              />
            </dd>
          </dl>
          <dl>
            <dt>佣金比例 ≥</dt>
            <dd>
              <InputNumber
                value={condition?.commssion_rate}
                onPressEnter={() => setIsChange(true)}
                onBlur={() => setIsChange(true)}
                onChange={(v) =>
                  handleUpdateCondition({
                    commssion_rate: v,
                  })
                }
                placeholder="请输入佣金率"
              />
            </dd>
          </dl>
          <div className={styles.maxMinWrapper}>
            <MaxMin
              min={0}
              precision={2}
              label="售卖价"
              value={[condition?.price?.[0], condition?.price?.[1]]}
              onSearch={(val) => {
                handleUpdateCondition({
                  price: val,
                });
                setIsChange(true);
              }}
            />
          </div>
        </div>
      </div>
      {roomId && <ShowMetrics />}
      {uid && (
        <FilterOptions
          condition={condition}
          updateCondition={(v) => {
            handleUpdateCondition(v);
            setIsChange(true);
          }}
          onReset={onReset}
        />
      )}
    </div>
  );
};

export default Condition;
