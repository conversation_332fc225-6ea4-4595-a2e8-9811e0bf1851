// ai生成
import React, { useEffect, useState } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import {
  jobTitlePermissionConfigAdd,
  JobTitlePermissionConfigAddRequest,
  JobTitlePermissionConfigAddResult,
  jobTitlePermissionConfigDel,
  jobTitlePermissionConfigQueryPage,
  JobTitlePermissionConfigQueryPageRequest,
  JobTitlePermissionConfigQueryPageResult,
  jobTitlePermissionConfigUpdate,
  JobTitlePermissionConfigUpdateRequest,
  JobTitlePermissionConfigUpdateResult,
  jobTitleQueryByDept,
  JobTitleQueryByDeptRequest,
  JobTitleQueryByDeptResult,
} from '../services';
import { useSetState } from 'ahooks';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

// ai生成
// 为了保持与主页面和hook文件的兼容性，添加别名导出
export type JobAuthPageRequest = JobTitlePermissionConfigQueryPageRequest;
export type JobAuthPageResult = JobTitlePermissionConfigQueryPageResult;
export type JobAuthAddRequest = JobTitlePermissionConfigAddRequest;
export type JobAuthAddResult = JobTitlePermissionConfigAddResult;
export type JobAuthUpdateRequest = JobTitlePermissionConfigUpdateRequest;
export type JobAuthUpdateResult = JobTitlePermissionConfigUpdateResult;

/**
 * 分页查询职务权限配置（别名）
 */
export const jobAuthPage = jobTitlePermissionConfigQueryPage;

/**
 * 删除职务权限配置（别名）
 */
export const jobAuthDelete = jobTitlePermissionConfigDel;

/**
 * 新增职务权限配置（别名）
 */
export const jobAuthAdd = jobTitlePermissionConfigAdd;

/**
 * 修改职务权限配置（别名）
 */
export const jobAuthUpdate = jobTitlePermissionConfigUpdate;

// 2024年12月29日 开山ai结尾共生成6行代码
// ai生成

export const useList = (
  form: WrappedFormUtils<
    JobTitlePermissionConfigQueryPageRequest & {
      jpgyDeptIds: string;
    }
  >,
) => {
  // 2024年12月31日 开山ai结尾共生成16行代码
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<JobAuthPageResult['records']>([]);
  const [pagination, setPagination] = useSetState({ current: 1, size: 20, total: 0 });
  const [condition, setCondition] = useState<JobAuthPageRequest>();

  const getList = async (data: JobTitlePermissionConfigQueryPageRequest) => {
    console.log(data);
    // 2024年12月31日 开山ai结尾共生成11行代码
    setLoading(true);
    const formValue = form?.getFieldsValue() as JobTitlePermissionConfigQueryPageRequest;

    // ai生成
    // 映射搜索表单字段到接口请求参数

    // 2024年12月31日 开山ai结尾共生成25行代码

    // ai生成
    const params = {
      ...formValue,
      jobTitles: formValue?.jobTitles?.map((item) => item?.split('-')?.[1]),
      current: data?.current ?? pagination.current,
      size: data?.size ?? pagination.size,
    };
    // 2024年12月31日 开山ai结尾共生成5行代码

    setCondition(params);
    const result = await responseWithResultAsync({
      request: jobAuthPage,
      params,
    });
    setLoading(false);
    setList(result?.records ?? []);
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };
  // 2024年12月31日 开山ai结尾共生成36行代码

  useEffect(() => {
    getList({});
  }, []);

  return {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
  };
};

export type JobAuthPageListInfoType = NonNullable<JobAuthPageResult['records']>[number];

export const useJobTitleList = () => {
  const [jobTitleList, setJobTitleList] = useState<JobTitleQueryByDeptResult>([]);
  const [loading, setLoading] = useState(false);
  const getJobTitleList = async (params: JobTitleQueryByDeptRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: jobTitleQueryByDept,
      params,
    });
    setJobTitleList(result ?? []);
    setLoading(false);
  };

  return {
    jobTitleList,
    loading,
    getJobTitleList,
  };
};
