import { Moment } from 'moment';

export type CreateFormType = {
  businessTypeInfo?: { label?: string; key?: string };
  dataType?: string /*所属数据类型[DataTypeEnum]*/;
  deptId?: string /*事业部id*/;
  eliminationAllocation?: string /*消返调配*/;
  expenseCategoryInfo?: { label?: string; key?: string };
  goodsContent?: string /*商品内容*/;
  goodsId?: string /*商品id*/;
  grantAmount?: string /*赠款金额*/;
  jgpyCompanyInfo?: { label?: string; key?: string };
  liveDate?: string /*直播日期*/;
  liveRoomInfo?: { label?: string; key?: string };
  num?: string /*数量*/;
  price?: string /*采购/补贴单价*/;
  refundAmount?: string /*消返金额*/;
  settlementEntityInfo?: { label?: string; key?: string };
  settlementType?: 'COMPANY' | 'PERSON' /*结算主体类型[SettlementTypeEnum]*/;
  status?:
    | 'PROCESSING'
    | 'WAIT_COMMIT'
    | 'COMMITED' /*状态（PROCESSING=处理中，WAIT_COMMIT=待提交，COMMITED=已提交）[MarketingDataStatusEnum]*/;
  storeName?: string /*店铺名称*/;
  taxRate?: string /*税率*/;
  totalAmount?: string /*汇总金额*/;
  totalConsume?: string /*总消耗*/;
  openId?: string /*直播间openid*/;
  rebateAmount?: string /*直播间openid*/;
};
