// ai生成
import { ColumnProps } from 'antd/lib/table';
import React, { useMemo } from 'react';
import moment from 'moment';
import PopoverRowText from '@/components/PopoverRowText';
import { Space } from 'web-common-modules/antd-pro-components';
import { Popconfirm } from 'antd';
import { JobAuthPageListInfoType } from './hook';
// 2024年12月29日 开山ai结尾共生成8行代码

export const useTable = (params: {
  onRefresh: () => void;
  onDelete: (info?: JobAuthPageListInfoType) => void;
}) => {
  const { onRefresh, onDelete } = params;
  // ai生成

  const columns = useMemo<ColumnProps<JobAuthPageListInfoType>[]>(
    () => [
      {
        dataIndex: 'index',
        title: '#',
        width: 60,
        render: (_, __, index) => index + 1,
      },
      // ai生成
      {
        title: '事业部',
        width: 120,
        dataIndex: 'jpgyDeptName',
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '部门',
        width: 150,
        dataIndex: 'departmentName',
        render: (val) => {
          return val ? <PopoverRowText text={val} /> : '-';
        },
      },
      {
        title: '职务',
        width: 120,
        dataIndex: 'jobTitle',
        render: (val) => {
          return val ? <PopoverRowText text={val} /> : '-';
        },
      },
      // 2024年12月31日 开山ai结尾共生成20行代码
      {
        title: '状态',
        width: 80,
        dataIndex: 'status',
        render: (val: number) => (val === 1 ? '启用' : '禁用'),
      },
      // ai生成
      {
        title: '角色权限',
        width: 150,
        dataIndex: 'roleNames',
        render: (val: string[]) => {
          const roleNames = val?.join('、') || '-';
          return <PopoverRowText text={roleNames} />;
        },
      },
      {
        title: '数据权限',
        width: 150,
        dataIndex: 'dataRoleNames',
        render: (val: string[]) => {
          const dataNames = val?.join('、') || '-';
          return <PopoverRowText text={dataNames} />;
        },
      },
      {
        title: '创建人',
        width: 100,
        dataIndex: 'creatorName',
        render: (val) => val ?? '-',
      },
      {
        title: '创建时间',
        width: 160,
        dataIndex: 'gmtCreated',
        render: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      // 2024年12月31日 开山ai结尾共生成22行代码
      {
        title: '操作',
        width: 100,
        dataIndex: 'action',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              {record.status === 1 ? (
                <Popconfirm
                  title="确定要删除此配置吗？"
                  onConfirm={() => onDelete(record)}
                  okText="确定"
                  cancelText="取消"
                >
                  <a style={{ color: '#ff4d4f' }}>删除</a>
                </Popconfirm>
              ) : (
                <span style={{ color: '#ccc', cursor: 'not-allowed' }}>删除</span>
              )}
            </Space>
          );
        },
      },
    ],
    [onRefresh, onDelete],
  );
  return { columns };
};
// 2024年12月31日 开山ai结尾共生成7行代码
