// ai生成
import { useDeptList } from '@/hooks/useDeptList';
import useDepartmentList from '@/pages/setting/employee-list/hooks/useDepartmentList';
import { Modal, Button, Form, Select, TreeSelect, message } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import React, { useState, useEffect, useMemo } from 'react';
import { useJobTitleList } from '../utils/hook';
import useRoleInfoList from '@/pages/setting/employee-list/hooks/useRoleInfoList';
import useDataRoleInfoList from '@/pages/setting/employee-list/hooks/useDataRoleInfoList';
import { IsBanStatus } from '@/services/setting/employee';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  jobTitlePermissionConfigAdd,
  JobTitlePermissionConfigAddRequest,
  JobTitleQueryByDeptResult,
} from '../services';
// 2024年12月29日 开山ai结尾共生成12行代码

const { Option } = Select;

// ai生成

interface PropsType
  extends FormComponentProps<
    JobTitlePermissionConfigAddRequest & {
      departmentIds: string;
    }
  > {
  type: 'create';
  onRefresh: () => void;
}
// 2024年12月31日 开山ai结尾共生成13行代码

const titleMap = {
  create: '新建配置',
};

const CreateEditModal: React.FC<PropsType> = ({ type, form, onRefresh }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const { deptList } = useDeptList();
  const {
    getDepartmentListLoading,
    departmentTreeList,
    debounceSearchDepartmentList,
    getDepartmentListRun,
  } = useDepartmentList();
  const { jobTitleList, loading: jobTitleListLoading, getJobTitleList } = useJobTitleList();
  const { getRoleInfoListLoading, roleList, debounceSearchRoleInfo, getRoleInfoListRun } =
    useRoleInfoList();
  const {
    getDataRoleInfoListLoading,
    dataRoleList,
    debounceSearchDataRoleInfo,
    getDataRoleInfoListRun,
  } = useDataRoleInfoList();
  const formValue = useMemo(() => form?.getFieldsValue(), [form?.getFieldsValue()]);
  useEffect(() => {
    getDepartmentListRun();
    getRoleInfoListRun();
    getDataRoleInfoListRun();
    debounceSearchDepartmentList('');
    getJobTitleList({});
  }, []);
  useEffect(() => {
    getJobTitleList({
      departmentIds: [formValue?.departmentIds],
      jpgyDeptId: formValue?.jpgyDeptId,
    });
  }, [formValue?.jpgyDeptId, formValue?.departmentIds]);
  // 获取下拉选项数据
  const handleOpen = () => {
    form.resetFields();
    setVisible(true);
  };
  const handleCancel = () => {
    setVisible(false);
  };
  const handleConfirm = async (params: JobTitlePermissionConfigAddRequest) => {
    setLoading(true);
    const reuslt = await responseWithResultAsync({
      request: jobTitlePermissionConfigAdd,
      params,
    });
    if (reuslt) {
      message.success('新建成功');
      onRefresh();
      handleCancel();
    }
    setLoading(false);
  };
  const handleOk = () => {
    form.validateFields((err, values) => {
      if (err) return;
      console.log(values);
      handleConfirm({ ...values, departmentIds: [values?.departmentIds] });
    });
  };
  useEffect(() => {
    if (formValue?.jobTitles?.includes('全部') && formValue?.jobTitles?.length > 1) {
      form?.setFieldsValue({
        jobTitles: ['全部'],
      });
    }
  }, [formValue?.jobTitles]);
  return (
    <>
      <Button type="primary" icon="plus" onClick={handleOpen}>
        新建配置
      </Button>
      <Modal
        visible={visible}
        title={titleMap[type]}
        onCancel={handleCancel}
        onOk={handleOk}
        confirmLoading={loading}
        width={600}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          <Form.Item label="事业部">
            {form.getFieldDecorator('jpgyDeptId', {
              rules: [{ required: true, message: '请选择事业部' }],
            })(
              <Select placeholder="请选择事业部">
                {deptList?.map((item) => (
                  <Select.Option value={item?.value} key={item?.value}>
                    {item?.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="部门">
            {form.getFieldDecorator('departmentIds', {
              rules: [{ required: true, message: '请选择部门' }],
            })(
              <TreeSelect
                loading={getDepartmentListLoading}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                // showSearch
                placeholder="请选择"
                allowClear
                treeDefaultExpandAll
                treeData={departmentTreeList?.map((item) => ({
                  ...item,
                  disabled: true,
                  children: item?.children?.map((child: any) => ({
                    ...child,
                    disabled: child?.departmentName === '交个朋友',
                  })),
                }))}
                filterTreeNode={(inputValue: string, treeNode: any) => false}
                onSearch={(value) => {
                  debounceSearchDepartmentList(value);
                }}
                defaultActiveFirstOption={false}
                onChange={(value: string) => {
                  if (!value) {
                    getDepartmentListRun();
                    return;
                  }
                }}
              />,
            )}
          </Form.Item>
          <Form.Item label="职务">
            {form.getFieldDecorator('jobTitles', {
              rules: [{ required: true, message: '请选择职务' }],
            })(
              <Select
                placeholder="请选择职务"
                loading={jobTitleListLoading}
                allowClear
                mode="multiple"
                maxTagCount={3}
              >
                {jobTitleList
                  ?.reduce(
                    (pre: JobTitleQueryByDeptResult, cur) => {
                      return pre?.find((item) => item?.jobTitle === cur?.jobTitle)
                        ? pre
                        : [...pre, cur];
                    },
                    [{ departmentName: undefined, jobTitle: '全部' }],
                  )
                  ?.map((item) => (
                    <Select.Option
                      value={item?.jobTitle}
                      key={item?.jobTitle}
                      disabled={formValue?.jobTitles?.includes('全部') && item?.jobTitle !== '全部'}
                    >
                      {item?.departmentName ? item?.departmentName + '-' : ''}
                      {item?.jobTitle}
                    </Select.Option>
                  ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="角色权限">
            {form.getFieldDecorator('roleIds', {
              rules: [{ required: true, message: '请选择角色权限' }],
            })(
              <Select
                allowClear
                loading={getRoleInfoListLoading}
                showSearch
                onSearch={(value) => {
                  debounceSearchRoleInfo(value);
                }}
                defaultActiveFirstOption={false}
                filterOption={false}
                placeholder="请选择"
                onChange={(value: string) => {
                  if (!value) {
                    getRoleInfoListRun();
                    return;
                  }
                }}
                mode="multiple"
                maxTagCount={3}
              >
                {roleList.map((item) => {
                  return (
                    <Option value={item?.roleInfoId} key={item?.roleInfoId}>
                      {item?.roleName}{' '}
                      {item?.roleStatus === IsBanStatus.DISABLE && (
                        <span style={{ color: '#f00' }}>(已停用)</span>
                      )}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="数据权限">
            {form.getFieldDecorator('dataRoleIds', {
              rules: [{ required: true, message: '请选择数据权限' }],
            })(
              <Select
                allowClear
                loading={getDataRoleInfoListLoading}
                showSearch
                onSearch={(value) => {
                  debounceSearchDataRoleInfo(value);
                }}
                defaultActiveFirstOption={false}
                filterOption={false}
                placeholder="请选择"
                onChange={(value: string) => {
                  if (!value) {
                    getDataRoleInfoListRun();
                    return;
                  }
                }}
                mode="multiple"
                maxTagCount={3}
              >
                {dataRoleList.map((item) => {
                  return (
                    <Option value={item?.id} key={item?.id}>
                      {item?.name}{' '}
                      {item?.status === IsBanStatus.DISABLE && (
                        <span style={{ color: '#f00' }}>(已停用)</span>
                      )}
                    </Option>
                  );
                })}
              </Select>,
            )}
          </Form.Item>
          {/* <Form.Item label="状态">
            {form.getFieldDecorator('status', {
              rules: [{ required: true, message: '请选择状态' }],
            })(
              <Select placeholder="请选择状态">
                {[
                  { value: 1, label: '启用' },
                  { value: 0, label: '停用' },
                ]?.map((item) => (
                  <Select.Option value={item?.value} key={item?.value}>
                    {item?.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item> */}
        </Form>
      </Modal>
    </>
  );
};

export default Form.create<PropsType>()(CreateEditModal);
