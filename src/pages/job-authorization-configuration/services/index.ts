import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type JobTitlePermissionConfigAddRequest = {
  businessIdentity?: {
    companyInfoId?: string;
    employeeId?: string;
    employeeName?: string;
    institutionId?: string;
    institutionName?: string;
    operatorId?: string;
    operatorName?: string;
    platform?: string;
    supplierId?: string;
    supplierName?: string;
    talentId?: string;
    talentName?: string;
    tenantId?: string;
    tenantType?: number;
    userId?: string;
  } /*业务身份信息*/;
  dataRoleIds?: Array<string> /*数据权限ID列表*/;
  departmentIds?: Array<string> /*部门ID列表*/;
  jobTitles?: Array<string> /*职务名称列表，为空表示全部职务*/;
  jpgyDeptId?: string /*事业部ID*/;
  roleIds?: Array<string> /*角色权限ID列表*/;
  status?: number /*状态 1：启用 0：停用*/;
};

export type JobTitlePermissionConfigAddResult = string;

/**
 *新增职务权限配置
 */
export const jobTitlePermissionConfigAdd = (params: JobTitlePermissionConfigAddRequest) => {
  return Fetch<ResponseWithResult<JobTitlePermissionConfigAddResult>>(
    '/user/public/jobTitlePermissionConfig/add',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/jobTitlePermissionConfig/add') },
    },
  );
};

export type JobTitlePermissionConfigDelRequest = {
  businessIdentity?: {
    companyInfoId?: string;
    employeeId?: string;
    employeeName?: string;
    institutionId?: string;
    institutionName?: string;
    operatorId?: string;
    operatorName?: string;
    platform?: string;
    supplierId?: string;
    supplierName?: string;
    talentId?: string;
    talentName?: string;
    tenantId?: string;
    tenantType?: number;
    userId?: string;
  } /*业务身份信息*/;
  id?: string /*主键ID*/;
};

export type JobTitlePermissionConfigDelResult = string;

/**
 *删除职务权限配置
 */
export const jobTitlePermissionConfigDel = (params: JobTitlePermissionConfigDelRequest) => {
  return Fetch<ResponseWithResult<JobTitlePermissionConfigDelResult>>(
    '/user/public/jobTitlePermissionConfig/delete',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/jobTitlePermissionConfig/delete') },
    },
  );
};

export type JobTitlePermissionConfigQueryPageRequest = {
  current?: number /*当前页*/;
  dataRoleIds?: Array<string> /*数据权限ID列表*/;
  departmentIds?: Array<string> /*部门ID列表*/;
  jobTitles?: Array<string> /*职务名称列表*/;
  jpgyDeptId?: string /*事业部ID*/;
  roleIds?: Array<string> /*角色权限ID列表*/;
  size?: number /*每页大小*/;
  status?: number /*状态 1：启用 0：停用*/;
};

export type JobTitlePermissionConfigQueryPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    creator?: string /*创建人*/;
    dataRoleIds?: Array<string> /*数据权限ID列表*/;
    dataRoleNames?: Array<string> /*数据权限名称列表*/;
    departmentId?: string /*部门ID*/;
    departmentName?: string /*部门名称*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键*/;
    jobTitle?: string /*职务名称*/;
    jpgyDeptId?: string /*事业部ID*/;
    jpgyDeptName?: string /*事业部名称*/;
    modifier?: string /*修改人*/;
    roleIds?: Array<string> /*角色权限ID列表*/;
    roleNames?: Array<string> /*角色权限名称列表*/;
    status?: number /*状态 1：启用 0：停用*/;
    statusName?: string /*状态名称*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询职务权限配置
 */
export const jobTitlePermissionConfigQueryPage = (
  params: JobTitlePermissionConfigQueryPageRequest,
) => {
  return Fetch<ResponseWithResult<JobTitlePermissionConfigQueryPageResult>>(
    '/user/public/jobTitlePermissionConfig/queryPage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/jobTitlePermissionConfig/queryPage') },
    },
  );
};

export type JobTitlePermissionConfigUpdateRequest = {
  businessIdentity?: {
    companyInfoId?: string;
    employeeId?: string;
    employeeName?: string;
    institutionId?: string;
    institutionName?: string;
    operatorId?: string;
    operatorName?: string;
    platform?: string;
    supplierId?: string;
    supplierName?: string;
    talentId?: string;
    talentName?: string;
    tenantId?: string;
    tenantType?: number;
    userId?: string;
  } /*业务身份信息*/;
  dataRoleIds?: Array<string> /*数据权限ID列表*/;
  departmentIds?: Array<string> /*部门ID列表*/;
  id?: string /*主键ID*/;
  jobTitles?: Array<string> /*职务名称列表，为空表示全部职务*/;
  jpgyDeptId?: string /*事业部ID*/;
  roleIds?: Array<string> /*角色权限ID列表*/;
  status?: number /*状态 1：启用 0：停用*/;
};

export type JobTitlePermissionConfigUpdateResult = string;

/**
 *修改职务权限配置
 */
export const jobTitlePermissionConfigUpdate = (params: JobTitlePermissionConfigUpdateRequest) => {
  return Fetch<ResponseWithResult<JobTitlePermissionConfigUpdateResult>>(
    '/user/public/jobTitlePermissionConfig/update',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/jobTitlePermissionConfig/update') },
    },
  );
};

export type JobTitleQueryByDeptRequest = {
  departmentIds?: Array<string> /*部门ID列表*/;
  jpgyDeptId?: string /*事业部ID*/;
};

export type JobTitleQueryByDeptResult = Array<{
  departmentId?: string /*部门ID*/;
  departmentName?: string /*部门名称*/;
  jobTitle?: string /*职务名称*/;
}>;

/**
 *根据事业部ID和部门ID查询职务和部门信息（包含子级部门）
 */
export const jobTitleQueryByDept = (params: JobTitleQueryByDeptRequest) => {
  return Fetch<ResponseWithResult<JobTitleQueryByDeptResult>>(
    '/user/public/jobTitle/queryWithDeptByDept',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/jobTitle/queryWithDeptByDept') },
    },
  );
};
