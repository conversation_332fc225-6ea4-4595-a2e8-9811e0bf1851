// ai生成
import { Form, message, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useEffect } from 'react';
import PaginationProxy from '@/common/constants/Pagination';
import PageLayout from '@/components/PageLayout/index';
import styles from './index.module.less';
import { useTableHeight } from '@/common/constants/hooks/index';
import SearchForm from './components/SearchForm';
import { useTable } from './utils/getColumns';
import { JobAuthPageListInfoType, useList } from './utils/hook';
import CreateEditModal from './CreateEditModal';
import { jobAuthDelete } from './utils/hook';
import { AuthWrapper } from 'qmkit';
// 2024年12月29日 开山ai结尾共生成12行代码

// ai生成
interface SearchFormFields {
  businessDivision?: string;
  departmentIds?: string[];
  positions?: string[];
  rolePermissions?: string[];
  dataPermissions?: string[];
  status?: number;
}

const JobAuthorizationConfiguration: React.FC<FormComponentProps<SearchFormFields>> = ({
  form,
}) => {
  // 2024年12月31日 开山ai结尾共生成13行代码
  const { list, loading, getList, pagination, setLoading } = useList(form);

  // ai生成
  const onReset = () => {
    form.resetFields();
    getList({
      current: 1,
      size: 20,
    });
  };
  // 2024年12月31日 开山ai结尾共生成8行代码

  const onRefresh = () => {
    getList({});
  };

  useEffect(() => {
    onRefresh();
  }, []);

  const { tableHeight } = useTableHeight(80);

  // ai生成
  const handleDelete = async (info?: JobAuthPageListInfoType) => {
    setLoading(true);
    try {
      const result = await jobAuthDelete({
        id: info?.id || '',
      });
      if (result?.res?.success) {
        message.success('删除成功');
        onRefresh();
      } else {
        message.error(result?.res?.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    } finally {
      setLoading(false);
    }
  };
  // 2024年12月31日 开山ai结尾共生成18行代码
  const { columns } = useTable({
    onRefresh,
    onDelete: handleDelete,
  });
  return (
    <PageLayout
      className={styles['cooperation-report-contain']}
      routePath="/job-authorization-configuration"
    >
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            form={form}
            loading={loading}
            onSearch={() =>
              getList({
                current: 1,
              })
            }
            onReset={onReset}
          />

          <div className="flex items-center mb-16">
            <AuthWrapper functionName="f_job_authorization_configuration_create">
              <CreateEditModal type="create" onRefresh={() => onRefresh()} />
            </AuthWrapper>
          </div>
        </div>
        <AuthWrapper functionName="f_job_authorization_configuration_list">
          <div className={styles.boardTable} style={{ flex: 1 }}>
            <Table
              rowKey="id"
              loading={loading}
              columns={columns}
              dataSource={list}
              pagination={false}
              scroll={{ y: tableHeight, x: '100%' }}
            />
          </div>
        </AuthWrapper>
        <AuthWrapper functionName="f_job_authorization_configuration_list">
          <div className={styles['pagination-box'] + ' pageHeight'}>
            {/* @ts-ignore */}
            <PaginationProxy
              current={pagination?.current}
              pageSize={pagination?.size}
              total={pagination?.total}
              // @ts-ignore
              onChange={(current, size) => {
                console.log(current, size);
                getList({
                  current: current || 1,
                  size: size || 20,
                });
              }}
              valueType="flatten"
              pageSizeOptions={['5', '10', '20', '50', '100']}
            />
          </div>
        </AuthWrapper>
      </div>
    </PageLayout>
  );
};

export default Form.create()(JobAuthorizationConfiguration);
