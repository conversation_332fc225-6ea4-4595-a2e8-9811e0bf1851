// ai生成
import { Select, TreeSelect } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useMemo, useEffect, useState, Children } from 'react';
import SearchFormComponent, { searchItem } from '../searchFormComponent';
import { useDeptList } from '@/hooks/useDeptList';
import useDepartmentList from '@/pages/setting/employee-list/hooks/useDepartmentList';
import { JobAuthPageRequest } from '../../utils/hook';
import { useJobTitleList } from '../../utils/hook';
import useRoleInfoList from '@/pages/setting/employee-list/hooks/useRoleInfoList';
import { IsBanStatus } from '@/services/setting/employee';
import useDataRoleInfoList from '@/pages/setting/employee-list/hooks/useDataRoleInfoList';
// 2024年12月29日 开山ai结尾共生成12行代码
interface IProps extends FormComponentProps<JobAuthPageRequest> {
  onSearch: () => void;
  onReset: () => void;
  loading: boolean;
}

const { Option } = Select;

const SearchForm: React.FC<IProps> = ({ form, onSearch, onReset, loading }) => {
  // ai生成
  // 用于监听表单变化的状态

  const reset = () => {
    onReset();
  };
  const { deptList } = useDeptList();
  const {
    getDepartmentListLoading,
    departmentTreeList,
    debounceSearchDepartmentList,
    getDepartmentListRun,
  } = useDepartmentList();
  const { jobTitleList, loading: jobTitleListLoading, getJobTitleList } = useJobTitleList();
  const { getRoleInfoListLoading, roleList, debounceSearchRoleInfo, getRoleInfoListRun } =
    useRoleInfoList();
  const {
    getDataRoleInfoListLoading,
    dataRoleList,
    debounceSearchDataRoleInfo,
    getDataRoleInfoListRun,
  } = useDataRoleInfoList();
  const formValue = useMemo(() => form?.getFieldsValue(), [form?.getFieldsValue()]);
  useEffect(() => {
    getDepartmentListRun();
    getRoleInfoListRun();
    getDataRoleInfoListRun();
    debounceSearchDepartmentList('');
    onSearch();
    getJobTitleList({});
  }, []);
  useEffect(() => {
    getJobTitleList({
      departmentIds: formValue?.departmentIds,
      jpgyDeptId: formValue?.jpgyDeptId,
    });
  }, [formValue?.jpgyDeptId, formValue?.departmentIds]);
  useEffect(() => {
    console.log(departmentTreeList);
  }, [departmentTreeList]);
  const options: Record<string, searchItem> = useMemo(() => {
    return {
      jpgyDeptId: {
        label: '事业部',
        renderNode: (
          <Select placeholder="请选择事业部" allowClear>
            {deptList?.map((item) => (
              <Select.Option value={item?.value} key={item?.value}>
                {item?.label}
              </Select.Option>
            ))}
          </Select>
        ),
      },
      departmentIds: {
        label: '部门',
        renderNode: (
          <TreeSelect
            loading={getDepartmentListLoading}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
            showSearch
            placeholder="请选择"
            allowClear
            treeDefaultExpandAll
            treeData={departmentTreeList?.map((item) => ({
              ...item,
              disabled: true,
              children: item?.children?.map((child: any) => ({
                ...child,
                disabled: child?.departmentName === '交个朋友',
              })),
            }))}
            filterTreeNode={(inputValue: string, treeNode: any) => false}
            onSearch={(value) => {
              debounceSearchDepartmentList(value);
            }}
            defaultActiveFirstOption={false}
            maxTagCount={1}
            multiple
            onChange={(value: string) => {
              if (!value) {
                getDepartmentListRun();
                return;
              }
            }}
          />
        ),
      },
      jobTitles: {
        label: '职务',
        renderNode: (
          <Select
            placeholder="请选择职务"
            loading={jobTitleListLoading}
            allowClear
            maxTagCount={1}
            mode="multiple"
          >
            {[{ departmentName: undefined, jobTitle: '全部' }, ...jobTitleList]?.map((item) => (
              <Select.Option
                value={item?.departmentName + '-' + item?.jobTitle}
                key={item?.departmentName + '-' + item?.jobTitle}
              >
                {item?.departmentName ? item?.departmentName + '-' : ''}
                {item?.jobTitle}
              </Select.Option>
            ))}
          </Select>
        ),
      },
      roleIds: {
        label: '角色权限',
        renderNode: (
          <Select
            allowClear
            loading={getRoleInfoListLoading}
            showSearch
            onSearch={(value) => {
              debounceSearchRoleInfo(value);
            }}
            defaultActiveFirstOption={false}
            filterOption={false}
            placeholder="请选择"
            onChange={(value: string) => {
              if (!value) {
                getRoleInfoListRun();
                return;
              }
            }}
            mode="multiple"
            maxTagCount={1}
          >
            {roleList.map((item) => {
              return (
                <Option value={item?.roleInfoId} key={item?.roleInfoId}>
                  {item?.roleName}{' '}
                  {item?.roleStatus === IsBanStatus.DISABLE && (
                    <span style={{ color: '#f00' }}>(已停用)</span>
                  )}
                </Option>
              );
            })}
          </Select>
        ),
      },
      dataRoleIds: {
        label: '数据权限',
        renderNode: (
          <Select
            allowClear
            loading={getDataRoleInfoListLoading}
            showSearch
            onSearch={(value) => {
              debounceSearchDataRoleInfo(value);
            }}
            defaultActiveFirstOption={false}
            filterOption={false}
            placeholder="请选择"
            onChange={(value: string) => {
              if (!value) {
                getDataRoleInfoListRun();
                return;
              }
            }}
            mode="multiple"
            maxTagCount={1}
          >
            {dataRoleList.map((item) => {
              return (
                <Option value={item?.id} key={item?.id}>
                  {item?.name}{' '}
                  {item?.status === IsBanStatus.DISABLE && (
                    <span style={{ color: '#f00' }}>(已停用)</span>
                  )}
                </Option>
              );
            })}
          </Select>
        ),
      },
      status: {
        label: '状态',
        renderNode: (
          <Select placeholder="请选择状态" allowClear>
            {[
              { value: 1, label: '启用' },
              { value: 0, label: '禁用' },
            ]?.map((item) => (
              <Select.Option value={item?.value} key={item?.value}>
                {item?.label}
              </Select.Option>
            ))}
          </Select>
        ),
      },
    };
  }, [
    deptList,
    jobTitleList,
    departmentTreeList,
    roleList,
    dataRoleList,
    getDepartmentListLoading,
    getRoleInfoListLoading,
    getDataRoleInfoListLoading,
    jobTitleListLoading,
    getDepartmentListLoading,
    getRoleInfoListLoading,
  ]);

  return (
    <SearchFormComponent
      form={form}
      options={options}
      loading={loading}
      onSearch={onSearch}
      onReset={reset}
    />
  );
};

export default SearchForm;
