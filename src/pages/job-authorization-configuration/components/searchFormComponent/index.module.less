.tableBox {
  :global {
    .ant-form-inline .ant-form-item > .ant-form-item-label {
      text-align: right;
    }

    .inline-searchform .common-form-item {
      padding-right: 8px;
      margin-bottom: 0px !important;
    }

    // .index_tableBox__txcog .inline-searchform .common-form-item{

    // }
    .ant-form-item-label {
      line-height: 30px;
    }

    .ant-form-item {
      width: 100%;
      display: flex;
      height: 34px;

      .ant-form-item-label {
        flex-shrink: 0;
        max-width: 82px;
        min-width: 82px;
        font-size: 12px;

        .ant-form-item-no-colon {
          font-size: 12px !important;
          color: #111111 !important;
          // font-weight: 500;
        }
      }

      .ant-form-item-control-wrapper {
        flex: 1;
      }

      .ant-form-item-control {
        width: 100%;
        line-height: 30px !important;
        max-width: 240px;
        min-width: 240px;
      }
    }

    .inline-searchform {
      margin-bottom: 0px;
    }

    .ant-calendar-range-picker-input {
      text-align: start;
      // width: 40%;
    }

    .ant-calendar-picker-input input:nth-of-type(2) {
      text-align: end;
      padding-right: 2px;
      // margin-left: -6px;
    }

    .ant-calendar-picker-clear,
    .ant-calendar-picker-icon {
      right: 10px;
    }

    .ant-select-arrow {
      right: 10px;
    }

    .inline-searchform .ant-input {
      font-size: 12px !important;
    }

    .inline-searchform .ant-select-selection--single {
      font-size: 12px !important;
    }
  }

  .tableLabel {
    min-width: 102px;
  }

  .lableBtn {
    // text-align: end;
    display: flex;
    justify-content: flex-end;
    width: 100%;

    :global {
      .ant-form-item-control-wrapper {
        display: flex;
        // justify-content: flex-end;
      }

      .ant-form-item-label > label::after {
        content: '';
      }

      .ant-btn {
        padding: 0 16px;
        height: 30px !important;
        font-size: 12px;
      }

      .ant-btn-primary {
        background-color: #204eff;
        border-color: #204eff;
      }
    }
  }
}

.other-btn {
  // :global{
  //   .ant-form-item{
  //     .ant-form-item-control {
  //       width: 100%;
  //       line-height: 30px !important;
  //       max-width: unset !important;
  //     }
  //   }

  // }
}

@media screen and (max-width: 1919px) {
  .tableBox {
    :global {
      .ant-form-item {
        .ant-form-item-control {
          max-width: 200px;
          min-width: 200px;
          line-height: 30px;
        }
      }
    }
  }
}

// @media screen and (min-width: 1920px) {
//   .tableBox {
//     :global {
//       .ant-form-item {

//         .ant-form-item-control {
//           max-width: 240px;
//         }
//       }
//     }
//   }
// }
