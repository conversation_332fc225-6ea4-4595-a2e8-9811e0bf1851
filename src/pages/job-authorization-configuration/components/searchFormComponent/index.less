@minWidth: 20%;
@middleWidth: 100% / 6;
@maxWidth: 100% / 7;

.inline-searchform {
  overflow: hidden;
  margin-bottom: 6px !important;
  .show {
    display: inline-block !important;
  }
  .common-form-item {
    display: none;
    padding-right: 24px;
    margin-bottom: 12px;
  }
  .ant-calendar-picker {
    width: 100% !important;
  }
  .ant-input {
    height: 28px !important;
    font-size: 13px !important;
  }
  .common-form-item-more {
    margin-left: 16px;
  }
  // .ant-form-item-control {
  //   line-height: 30px !important;
  // }
  // .ant-select-selection--single {
  //   height: 28px !important;
  //   font-size: 13px !important;
  //   .ant-select-selection__rendered {
  //     height: 26px !important;
  //     line-height: 26px !important;
  //   }
  // }
  // .ant-input-number {
  //   height: 28px !important;
  //   .ant-input-number-input {
  //     height: 26px !important;
  //   }
  // }
  // .ant-select-selection--multiple > ul > li,
  // .ant-select-selection--multiple .ant-select-selection__rendered > ul > li {
  //   margin-top: 1px;
  // }
  // .ant-select-selection--multiple {
  //   min-height: 28px;
  //   height: 28px !important;
  //   overflow: hidden;
  //   .ant-select-selection__rendered {
  //     display: flex !important;
  //     line-height: 28px;
  //   }
  //   ul {
  //     display: flex;
  //     li {
  //       flex-shrink: 0;
  //     }
  //   }
  // }
  // .ant-btn {
  //   height: 28px !important;
  //   font-size: 13px !important;
  // }
  // .ant-form-item-label {
  //   text-align: left;

  //   padding-bottom: 4px !important;
  // }
  // .ant-form-item-label > label {
  //   color: #666 !important;
  // }
  // .ant-btn.ant-btn-loading:not(.ant-btn-circle):not(.ant-btn-circle-outline):not(.ant-btn-icon-only) {
  //   padding-left: 15px;
  // }
  // .ant-btn > .anticon + span,
  // .ant-btn > span + .anticon {
  //   margin-left: 0;
  // }

  .loopSimpleAndDouble(@val,@add) when (@val<=4) {
    .simple-@{val} {
      width: 100% / @val;
      .common-form-item-more {
        margin-left: 6px;
      }
      .ant-btn {
        padding: 0 13px;
      }
    }
    .double-@{val} {
      width: 100% / @val * 2;
    }
    .loopSimpleAndDouble(@val+@add, @add);
  }
  .loopSimpleAndDouble(1, 1);

  .simple-5 {
    width: @minWidth;
    .common-form-item-more {
      margin-left: 6px;
    }
    .ant-btn {
      padding: 0 13px;
    }
  }
  .double-5 {
    width: @minWidth * 2;
  }

  .simple-6 {
    width: @middleWidth;
  }
  .double-6 {
    width: @middleWidth * 2;
  }

  .simple-7 {
    width: @maxWidth;
  }
  .double-7 {
    width: @maxWidth * 2;
  }

  .search-btn {
    float: right;
    text-align: right;
  }
  .move-top {
    margin-top: 0;
  }
  .search-btn button + button {
    margin-left: 8px;
  }
}
