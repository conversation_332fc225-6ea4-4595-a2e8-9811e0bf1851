import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type GetByProcessInstanceIdRequest = {
  processInstanceId?: string /*审批流程实例ID*/;
};

export type GetByProcessInstanceIdResult = {
  approvalProcessNo?: string /*审批流程编号*/;
  attachmentIdList?: Array<string> /*附件：[1735540048441352193,1735540048441352195]*/;
  creatorName?: string /*申请人*/;
  deptName?: string /*事业部*/;
  gmtCreated?: string /*创建时间*/;
  remark?: string /*特批原因*/;
  remarkDesc?: string /*特批原因说明*/;
  supplierOrgName?: string /*商家主体名称*/;
};

/**
 *自研工作流获取主体特批信息
 */
export const getByProcessInstanceId = (params: GetByProcessInstanceIdRequest) => {
  return Fetch<ResponseWithResult<GetByProcessInstanceIdResult>>(
    '/pim/public/supplierBodySpecialAudit/getByProcessInstanceId',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/pim/public/supplierBodySpecialAudit/getByProcessInstanceId'),
      },
    },
  );
};
