import React, { useState, useEffect, useMemo } from 'react';
import { Card, SpinCard } from '@/components/DetailFormCompoments';
import { Descriptions } from '@/pages/compliance-audit-logs/components';
import { renderQualification } from '@/pages/choice-list-new/components/LegalCheckDrawer/imgUtils';
import { useRequest } from 'ahooks';
import { message, Popover, Spin, Tooltip } from 'antd';
import moment from 'moment';
import { copyText } from '@/utils/moduleUtils';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import { getByProcessInstanceId, GetByProcessInstanceIdResult } from './services';
import { getResourceUrls } from '@/components/FileUploadSouceId/api';

interface DetailComProps {
  id?: string;
  sourceOrderNo?: string;
  processInstanceId?: string;
}

const SupplierAuditDetail: React.FC<DetailComProps> = ({
  id,
  sourceOrderNo,
  processInstanceId,
}) => {
  const [detail, setDetail] = useState<GetByProcessInstanceIdResult>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [resourceUrls, setResourceUrls] = useState<string[]>([]);

  //ai生成
  useEffect(() => {
    let didCancel = false; // 解决竞态问题
    if (processInstanceId) {
      setLoading(true);
      getByProcessInstanceId({ processInstanceId: processInstanceId }).then(({ res }) => {
        setLoading(false);
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        if (!didCancel) {
          setDetail(res?.result || {});
        }
      });
    } else {
      setDetail({});
    }
    return () => {
      didCancel = true;
    };
  }, [id, sourceOrderNo]);
  const getUrls = async (resourceIds: string[]) => {
    try {
      const response = await getResourceUrls(resourceIds);
      if (response?.res?.result) {
        setResourceUrls(Object.values(response.res.result));
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (detail?.attachmentIdList?.length) {
      getUrls(detail?.attachmentIdList);
    }
  }, [detail?.attachmentIdList]);
  // 2024-06-10zhouby -> cursor ai结尾共生成21行代码

  // const auditResult = useMemo(() => {
  //   if (!detail) {
  //     return '-';
  //   }
  //   const {
  //     supplierQualificationAuditState,
  //     brandQualificationAuditState,
  //     spuQualificationAuditState,
  //     riskLevel,
  //   } = detail;
  //   return `商家:${
  //     QUALIFICATION_AUDIT_STATE_TEXT[
  //       supplierQualificationAuditState as QUALIFICATION_AUDIT_STATE
  //     ] || '-'
  //   }
  //   品牌:${
  //     QUALIFICATION_AUDIT_STATE_TEXT[brandQualificationAuditState as QUALIFICATION_AUDIT_STATE] ||
  //     '-'
  //   }
  //   商品:${
  //     QUALIFICATION_AUDIT_STATE_TEXT[spuQualificationAuditState as QUALIFICATION_AUDIT_STATE] || '-'
  //   }
  //   `;
  // }, [detail]);

  return (
    <SpinCard spinning={loading}>
      <Card title="基础信息">
        <div style={{ padding: '0 12px' }}>
          <Descriptions column={4}>
            <Descriptions.Item label="审批单号">
              {detail?.approvalProcessNo || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="申请人">{detail?.creatorName || '-'}</Descriptions.Item>
            <Descriptions.Item label="部门">{detail?.deptName || '-'}</Descriptions.Item>
            <Descriptions.Item label="申请日期">
              {detail?.gmtCreated ? moment(detail?.gmtCreated).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </Descriptions.Item>
          </Descriptions>
        </div>
      </Card>
      <div style={{ flex: 1 }}>
        <Card style={{ height: '100%' }} title="特批信息">
          <div style={{ padding: '0 12px' }}>
            <Descriptions column={4}>
              <Descriptions.Item label="商家名称">{detail?.supplierOrgName}</Descriptions.Item>
              <Descriptions.Item label="特批原因">{detail?.remark || '-'}</Descriptions.Item>
              <Descriptions.Item label="原因说明">{detail?.remarkDesc || '-'}</Descriptions.Item>
              <Descriptions.Item label="附件">
                {renderQualification(
                  resourceUrls?.length ? resourceUrls.map((item) => ({ url: item })) : [],
                )}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </Card>
      </div>
    </SpinCard>
  );
};

export default SupplierAuditDetail;
