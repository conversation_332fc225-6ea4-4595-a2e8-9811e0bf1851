import React, { useState, useEffect } from 'react';
import { Modal, Table, Radio, message } from 'antd';
import Space from 'web-common-modules/antd-pro-components/Space';
import { ColumnProps } from 'antd/lib/table';
import { workflowReject, workflowTaskOperData } from '@/services/workflow';

interface ApproveReturnModalProps {
  id?: string;
  taskId?: string;
  nowNodeId?: string;
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
  onSuccess?: () => void;
  onLoadingChange?: (loading: boolean) => void; // 添加loading状态回调
}

interface UserVo {
  id: string;
  name: string;
  showTime?: string;
  showTimeStr?: string;
  avatar?: string;
  status?: number;
  operType?: string;
}

interface NodeItem {
  id: string;
  name: string;
}

// ai生成
interface ProcessNode {
  id: string;
  nodeName: string;
  type: number;
  childNode?: ProcessNode;
  conditionNodes?: ProcessNode[];
  branch?: boolean;
}

// 获取可驳回节点列表(逻辑参照paas系统内逻辑利用ai实现)
const getRejectableNodeList = (process: ProcessNode, currentNodeId: string): NodeItem[] => {
  // 递归查找父节点
  const findParent = (node: ProcessNode | undefined, targetId: string): ProcessNode | undefined => {
    if (!node || !node.id) return undefined;

    // 检查直接子节点
    if (node.childNode && node.childNode.id === targetId) {
      node.branch = false;
      return node;
    }

    // 检查分支节点
    if ((node.type === 4 || node.type === 5 || node.type === 8) && node.conditionNodes) {
      for (const branch of node.conditionNodes) {
        if (branch && branch.id === targetId) {
          node.branch = true;
          return node;
        }
        const found = findParent(branch, targetId);
        if (found) {
          found.branch = true;
          return found;
        }
      }
    }

    return findParent(node.childNode, targetId);
  };

  // 获取所有父节点
  const parentNodes: ProcessNode[] = [];
  let searchId = currentNodeId;
  let parent: ProcessNode | undefined;

  while ((parent = findParent(process, searchId))) {
    parentNodes.push(parent);
    searchId = parent.id;
  }

  // 筛选可驳回节点
  const result: NodeItem[] = [];
  for (const item of parentNodes) {
    const type = item.type;
    const branch = item.branch;

    if (branch && (type === 5 || type === 8)) break;

    if (
      type === 1 ||
      type === 12 ||
      type === 17 ||
      type === 18 ||
      type === 0 ||
      type === 4 ||
      type === 5 ||
      type === 8
    ) {
      result.push({
        id: item.id,
        name: item.nodeName,
      });
    }
  }

  return result;
};

//ai生成
const ApproveReturnModal: React.FC<ApproveReturnModalProps> = ({
  id,
  taskId,
  nowNodeId,
  visible,
  onCancel,
  onOk,
  onSuccess,
  onLoadingChange, // 添加loading状态回调
}) => {
  const [nodeList, setNodeList] = useState<NodeItem[]>([]);
  const [selectedNode, setSelectedNode] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [jumpBack, setJumpBack] = useState<boolean>(false);
  // const [currentNodeId, setCurrentNodeId] = useState<string>(''); // 存储当前节点ID (status===1)

  // 获取节点列表
  useEffect(() => {
    if (visible && id) {
      fetchNodeList();
    }
  }, [visible, id]);

  const fetchNodeList = async () => {
    setLoading(true);
    setSelectedNode('');
    setNodeList([]);
    // setCurrentNodeId(''); // 重置当前节点ID
    try {
      const params = `taskId=${taskId}`;
      const res = await workflowTaskOperData(params);

      if (res && res?.res?.ok) {
        const rejectableNodeList = getRejectableNodeList(
          res.res.data?.process,
          res.res.data?.nodeId,
        );
        setNodeList(rejectableNodeList);
      } else {
        message.error('获取退回节点列表失败');
      }
    } catch (err) {
      console.error('获取退回节点列表失败:', err);
      message.error('获取退回节点列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!selectedNode) {
      message.warning('请选择退回节点');
      return;
    }

    setSubmitLoading(true);
    onLoadingChange && onLoadingChange(true); // 通知父组件开始loading

    try {
      const res = await workflowReject({
        processInstanceId: id,
        taskId: taskId,
        jumpBack, // 退回后再次提交处理方式:true直达本节点,false逐级审批
        nodeId: nowNodeId, // 使用status===1的节点ID作为当前节点
        targetNodeId: selectedNode, // 目标节点
      });

      if (res && res?.res?.ok) {
        message.success('审批退回成功');
        onOk();
        onSuccess && onSuccess();
      } else {
        message.error(res?.res?.msg || '审批退回失败');
      }
    } catch (err) {
      console.error('审批退回失败:', err);
      message.error('审批退回失败');
    } finally {
      setSubmitLoading(false);
      onLoadingChange && onLoadingChange(false); // 通知父组件结束loading
    }
  };

  const columns: ColumnProps<NodeItem>[] = [
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name',
      // width: '40%',
    },
    // {
    //   title: '操作者',
    //   dataIndex: 'userVoList',
    //   key: 'userVoList',
    //   width: '60%',
    //   render: (userVoList: UserVo[]) => {
    //     return userVoList.map((user) => user.name).join('，') || '-';
    //   },
    // },
  ];

  return (
    <Modal
      title="退回设置"
      visible={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={submitLoading}
      width={600}
      maskClosable={false}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>退回提醒节点</div>
      <Table
        rowKey="id"
        columns={columns}
        dataSource={nodeList}
        loading={loading}
        pagination={false}
        rowSelection={{
          type: 'radio',
          onChange: (selectedRowKeys) => {
            setSelectedNode(selectedRowKeys[0] as string);
          },
        }}
      />

      <div style={{ marginTop: 24 }}>
        <div>退回后再次提交处理方式</div>
        <Space style={{ marginTop: 16 }}>
          <Radio checked={!jumpBack} onChange={() => setJumpBack(false)}>
            逐级审批
          </Radio>
          <Radio checked={jumpBack} onChange={() => setJumpBack(true)}>
            直达本节点
          </Radio>
        </Space>
      </div>
    </Modal>
  );
};

export default ApproveReturnModal;
//2024年06月20日-飞虎ai结尾共生成244行代码
