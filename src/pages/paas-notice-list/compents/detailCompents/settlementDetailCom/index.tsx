import React, { useState, useEffect } from 'react';
import {
  DetailContentItem,
  DetailContextBox,
} from '@/components/DetailFormCompoments/DetailContentItem';
import { Card } from '@/components/DetailFormCompoments';
import { Table, Spin, message } from 'antd';
import moment from 'moment';
import { FilePreview } from '@/components';
import { getApprovalSettlementDetail, GetApprovalSettlementDetailResult } from './service/index';
import { BUSSINESS_TYPE } from '@/pages/summary-costs/type';
import { BUSINESS_TYPE_MAP } from '@/pages/marketing-management/utils/enum';

const Item = DetailContentItem;

type PropsType = {
  id?: string;
  isPrintMode?: boolean; // 是否为打印模式
  onDataLoaded?: (data: GetApprovalSettlementDetailResult) => void; // 数据加载完成回调
};

const SettlementDetailCom: React.FC<PropsType> = ({ id, isPrintMode = false, onDataLoaded }) => {
  const [info, setInfo] = useState<GetApprovalSettlementDetailResult | undefined>(undefined);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取详情数据
  const fetchDetailData = async (processInstanceId: string) => {
    setLoading(true);
    try {
      const response = await getApprovalSettlementDetail({ processInstanceId });
      if (response.res.code === '200') {
        setInfo(response.res.result);
        // 调用回调函数，传递数据给父组件
        if (onDataLoaded) {
          onDataLoaded(response.res.result);
        }
      } else {
        message.error(response.res.message || '获取详情数据失败');
      }
    } catch (error) {
      message.error('获取详情数据失败');
      console.error('Error fetching settlement detail:', error);
    } finally {
      setLoading(false);
    }
  };

  // 监听id变化，调用接口
  useEffect(() => {
    if (id) {
      fetchDetailData(id);
    } else {
      setInfo(undefined);
    }
  }, [id]);

  // 转换附件数据为FileItem数组的辅助函数
  const convertAttachmentsToFileItems = (
    attachments:
      | Array<{ fileName: string; filePath: string }>
      | { fileName: string; filePath: string }
      | null
      | undefined,
    fallbackPrefix = '附件',
  ) => {
    if (!attachments) return [];

    const attachmentArray = Array.isArray(attachments) ? attachments : [attachments];

    return attachmentArray
      .filter((item) => item && item.filePath)
      .map((item, index) => ({
        key: `${fallbackPrefix}_${index}_${item.filePath}`, // 添加唯一key
        fileName: item.fileName || `${fallbackPrefix}${index + 1}`,
        fileUrl: item.filePath,
      }));
  };

  const columns = [
    {
      title: '#',
      align: 'center' as const,
      width: !isPrintMode ? 40 : undefined,
      render: (text: any, record: any, index: number) => {
        return <span>{index + 1}</span>;
      },
    },
    {
      title: '费用所属公司',
      dataIndex: 'expenseCompanyName',
      key: 'expenseCompanyName',
      width: !isPrintMode ? 120 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '付款单号',
      dataIndex: 'paymentOrderNo',
      key: 'paymentOrderNo',
      width: !isPrintMode ? 120 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '结算主体',
      dataIndex: 'settlementEntityName',
      key: 'settlementEntityName',
      width: !isPrintMode ? 100 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '费用所属公司与发票是否一致',
      dataIndex: 'isExpenseCompanyConsistent',
      key: 'isExpenseCompanyConsistent',
      width: !isPrintMode ? 210 : undefined,
      render: (text: string) => (
        <span style={{ color: text !== '一致' ? '#ff4d4f' : undefined }}>{text || '-'}</span>
      ),
    },
    {
      title: '收款银行账号',
      dataIndex: 'payeeBankAccount',
      key: 'payeeBankAccount',
      width: !isPrintMode ? 140 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '开户银行',
      dataIndex: 'openingBank',
      key: 'openingBank',
      width: !isPrintMode ? 120 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '付款申请金额',
      dataIndex: 'paymentAmount',
      key: 'paymentAmount',
      width: !isPrintMode ? 120 : undefined,
      render: (amount: number) => amount || '-',
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: !isPrintMode ? 80 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '合同附件',
      dataIndex: 'contractAttachment',
      key: 'contractAttachment',
      width: !isPrintMode ? 100 : undefined,
      render: (attachment: { fileName: string; filePath: string }) => {
        const files = convertAttachmentsToFileItems(attachment, '合同附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
    {
      title: '发票附件',
      dataIndex: 'invoiceAttachment',
      key: 'invoiceAttachment',
      width: !isPrintMode ? 100 : undefined,
      render: (attachment: { fileName: string; filePath: string }) => {
        const files = convertAttachmentsToFileItems(attachment, '发票附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
    {
      title: '成本明细附件',
      dataIndex: 'costDetailAttachment',
      key: 'costDetailAttachment',
      width: !isPrintMode ? 120 : undefined,
      render: (attachment: { fileName: string; filePath: string }) => {
        const files = convertAttachmentsToFileItems(attachment, '成本明细附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
    {
      title: '附件',
      dataIndex: 'attachments',
      key: 'attachments',
      width: !isPrintMode ? 80 : undefined,
      render: (attachments: Array<{ fileName: string; filePath: string }>) => {
        const files = convertAttachmentsToFileItems(attachments, '附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
  ];

  const heXiaoColumns = [
    {
      title: '#',
      align: 'center' as const,
      width: !isPrintMode ? 40 : undefined,
      render: (text: any, record: any, index: number) => {
        return <span>{index + 1}</span>;
      },
    },
    {
      title: '核销金额',
      dataIndex: 'writeOffAmount',
      key: 'writeOffAmount',
      width: !isPrintMode ? 100 : undefined,
      render: (amount: number) => amount || '-',
    },
    {
      title: '核销附件',
      dataIndex: 'writeOffAttachment',
      key: 'writeOffAttachment',
      width: !isPrintMode ? 100 : undefined,
      render: (attachment: { fileName: string; filePath: string }) => {
        const files = convertAttachmentsToFileItems(attachment, '核销附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
    {
      title: '发票附件',
      dataIndex: 'invoiceAttachment',
      key: 'invoiceAttachment',
      width: !isPrintMode ? 100 : undefined,
      render: (attachment: { fileName: string; filePath: string }) => {
        const files = convertAttachmentsToFileItems(attachment, '发票附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
    {
      title: '成本明细附件',
      dataIndex: 'costDetailAttachment',
      key: 'costDetailAttachment',
      width: !isPrintMode ? 120 : undefined,
      render: (attachment: { fileName: string; filePath: string }) => {
        const files = convertAttachmentsToFileItems(attachment, '成本明细附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
    {
      title: '附件',
      dataIndex: 'attachments',
      key: 'attachments',
      width: !isPrintMode ? 80 : undefined,
      render: (attachments: Array<{ fileName: string; filePath: string }>) => {
        const files = convertAttachmentsToFileItems(attachments, '附件');
        return files.length > 0 ? <FilePreview files={files} /> : '-';
      },
    },
    {
      title: '费用所属公司',
      dataIndex: 'expenseCompanyName',
      key: 'expenseCompanyName',
      width: !isPrintMode ? 120 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '费用所属公司与发票是否一致',
      dataIndex: 'isExpenseCompanyConsistent',
      key: 'isExpenseCompanyConsistent',
      width: !isPrintMode ? 210 : undefined,
      render: (text: string) => (
        <span style={{ color: text !== '一致' ? '#ff4d4f' : undefined }}>{text || '-'}</span>
      ),
    },
    {
      title: '业务类型',
      dataIndex: 'businessType',
      key: 'businessType',
      width: !isPrintMode ? 100 : undefined,
      render: (text: string) => BUSSINESS_TYPE[text as keyof typeof BUSSINESS_TYPE] || '-',
    },

    {
      title: '费用种类',
      dataIndex: 'expenseTypeName',
      key: 'expenseTypeName',
      width: !isPrintMode ? 100 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '所属类型',
      dataIndex: 'expenseSubType',
      key: 'expenseSubType',
      width: !isPrintMode ? 100 : undefined,
      render: (text: string) => BUSINESS_TYPE_MAP[text as keyof typeof BUSINESS_TYPE_MAP] || '-',
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: !isPrintMode ? 80 : undefined,
      render: (text: string) => text || '-',
    },
    {
      title: '付款单号',
      dataIndex: 'paymentOrderNo',
      key: 'paymentOrderNo',
      width: !isPrintMode ? 120 : undefined,
      render: (text: string) => text || '-',
    },
  ];
  return (
    <Spin spinning={loading}>
      <Card title="基本信息">
        <DetailContextBox>
          <Item label="标题" span={4}>
            {info?.basicInfo?.title ?? '-'}
          </Item>
          {!isPrintMode && (
            <Item label="流程编号" span={isPrintMode ? 2 : 1}>
              {info?.basicInfo?.approvalNo ?? '-'}
            </Item>
          )}
          <Item label="申请人" span={isPrintMode ? 2 : 1}>
            {info?.basicInfo?.applicantName ?? '-'}
          </Item>
          <Item label="创建时间" span={isPrintMode ? 2 : 1}>
            {info?.basicInfo?.creationTime
              ? moment(info.basicInfo.creationTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'}
          </Item>
          <Item label="业务类型" span={isPrintMode ? 2 : 1}>
            {info?.basicInfo?.businessType
              ? BUSSINESS_TYPE[info.basicInfo.businessType as keyof typeof BUSSINESS_TYPE] || '-'
              : '-'}
          </Item>
          <Item label="业务线" span={isPrintMode ? 2 : 1}>
            {info?.basicInfo?.businessLineName ?? '-'}
          </Item>
          <Item label="费用所属部门" span={isPrintMode ? 2 : 1}>
            {info?.basicInfo?.expenseDepartmentName ?? '-'}
          </Item>
          {info?.basicInfo?.settlementType !== '3' && (
            <Item label="结算主体" span={isPrintMode ? 2 : 1}>
              {info?.basicInfo?.settlementEntityName ?? '-'}
            </Item>
          )}
          <Item
            label={info?.basicInfo?.settlementType === '3' ? '本次核销合计' : '付款申请金额'}
            span={isPrintMode ? 2 : 1}
          >
            {info?.basicInfo?.paymentAmount}
          </Item>
        </DetailContextBox>
      </Card>

      {info?.basicInfo?.settlementType !== '3' && (
        <Card title="付款信息">
          <Table
            columns={columns}
            dataSource={info?.paymentInfoList || []}
            rowKey="id"
            pagination={false}
            scroll={{ x: '100%' }}
          />
        </Card>
      )}

      {info?.basicInfo?.settlementType === '3' && (
        <Card title="核销信息">
          <Table
            columns={heXiaoColumns}
            dataSource={info?.writeOffInfoList || []}
            rowKey="id"
            pagination={false}
            scroll={{ x: '100%' }}
          />
        </Card>
      )}
    </Spin>
  );
};

export default SettlementDetailCom;
