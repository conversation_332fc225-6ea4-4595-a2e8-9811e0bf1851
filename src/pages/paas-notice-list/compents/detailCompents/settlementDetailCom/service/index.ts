import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type GetApprovalSettlementDetailRequest = {
  processInstanceId?: string /*流程实例ID*/;
};

export type GetApprovalSettlementDetailResult = {
  basicInfo: {
    applicant: string /*申请人*/;
    applicantName: string /*申请人名称*/;
    approvalNo: string /*审批编号*/;
    businessLine: string /*业务线*/;
    businessLineName: string /*业务线名称*/;
    businessType: string /*业务类型*/;
    creationTime: string /*创建时间*/;
    expenseDepartmentId: string /*费用所属部门ID*/;
    expenseDepartmentName: string /*费用所属部门名称*/;
    paymentAmount: string /*付款申请金额*/;
    processInstanceId: string /*流程实例id*/;
    settlementEntityId: string /*结算主体ID*/;
    settlementEntityName: string /*结算主体名称*/;
    settlementType:
      | '1'
      | '2'
      | '3' /*结算主体类型, 1:对公付款 2:员工报销 3:预付核销[SettlementTypeEnum]*/;
    title: string /*标题*/;
  } /*基础信息*/;
  paymentInfoList: Array<{
    approvalNo: string /*审批编号*/;
    attachments: Array<{
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    }> /*附件*/;
    contractAttachment: {
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    } /*合同附件*/;
    costDetailAttachment: {
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    } /*成本明细附件*/;
    expenseCompanyId: string /*费用所属公司ID*/;
    invoiceAttachment: {
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    } /*发票附件*/;
    isExpenseCompanyConsistent: string /*费用所属公司与发票是否一致*/;
    openingBank: string /*开户银行*/;
    payeeBankAccount: string /*收款银行账号*/;
    paymentAmount: string /*付款申请金额*/;
    paymentOrderNo: string /*付款单号*/;
    remarks: string /*备注*/;
    settlementEntityId: string /*结算主体ID*/;
  }> /*付款信息列表*/;
  writeOffInfoList: Array<{
    approvalNo: string /*审批编号*/;
    attachments: Array<{
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    }> /*附件*/;
    businessType: string /*业务类型*/;
    costDetailAttachment: {
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    } /*成本明细附件*/;
    expenseCompanyId: string /*费用所属公司ID*/;
    expenseSubType:
      | 'WORRY_FREE_BAG'
      | 'PICK_YOUR_OWN_BAG'
      | 'MERCHANT_PROVIDED' /*所属类型[ExpenseSubTypeEnum]*/;
    expenseType: string /*费用种类*/;
    invoiceAttachment: {
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    } /*发票附件*/;
    isExpenseCompanyConsistent: string /*费用所属公司与发票是否一致*/;
    prepaymentOrderNo: string /*预付款单号*/;
    remarks: string /*备注*/;
    settlementEntityId: string /*结算主体ID*/;
    writeOffAmount: string /*核销金额*/;
    writeOffAttachment: {
      fileName: string /*文件名称*/;
      filePath: string /*文件路径*/;
    } /*核销附件*/;
  }> /*核销信息列表*/;
};

/**
 *根据流程实例ID查询付款单审批详情
 */
export const getApprovalSettlementDetail = (params: GetApprovalSettlementDetailRequest) => {
  return Fetch<ResponseWithResult<GetApprovalSettlementDetailResult>>(
    '/lop-finance/public/settlementOrderApprovalBasicInfo/getApprovalDetail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign(
          '/lop-finance/public/settlementOrderApprovalBasicInfo/getApprovalDetail',
        ),
      },
    },
  );
};
