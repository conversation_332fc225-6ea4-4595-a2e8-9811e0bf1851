import { DrawerProps } from 'antd/es/drawer';
import React, { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import WithToggleModal from '@/components/WithToggleModal';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import style from './index.module.less';
import { Spin, Table, Tag, Drawer, message, Button, Modal } from 'antd';
import moment from 'moment';
import ApproveList from '../approveList';
import ApproveBtn from '../approveBtn';
import { Const, history } from 'qmkit';
import PrintModal from '@/pages/paas-print-page/printAdvancePayment/PrintModal';
import PrintSettlementModal from '@/pages/paas-print-page/printSettlement/index';
import { getAdvancePaymentDetail } from '@/services/finance';
import { AuthWrapper } from 'qmkit';

const apiEntry =
  Const.NODE_SERVER_ENV === 'production'
    ? 'https://caiwu-new.befriends.com.cn'
    : Const.NODE_SERVER_ENV === 'development'
    ? '//localhost:8081'
    : Const.NODE_SERVER_ENV === 'dev'
    ? 'https://caiwu-new-dev.befriends.com.cn'
    : Const.NODE_SERVER_ENV === 'test'
    ? 'https://caiwu-new-test.befriends.com.cn'
    : 'https://caiwu-new-uat.befriends.com.cn';

// @DES: zhouby 正常情况下这个页面不需要再动了 除非现有代码和需求不匹配
// @DES: zhouby 理论上朋友云可以直接采用组件形式传入 存在可能使用其他系统嵌入的可能
// @DES: zhouby DetailCom组件在全局components已经注册 直接应用全局的components
/** @DES: zhouby
 * 组件形式 如何使用请看列表页面中的详情注释
 * 如果是iframe请将完整路径拼接好传入 iframeUrl
 * 兼容之前的财务系统即没有传入componentsChildren和iframeUrl的情况
 * componentsChildren 优先级更高
 */

interface IProps extends WithToggleModalProps, DrawerProps {
  id?: any;
  taskId?: any;
  sourceNo?: any;
  approvalNo?: any; // 审批流程编号
  children?: React.ReactNode;
  // ai生成
  onRefresh?: () => void; // 添加刷新数据的回调函数
  componentsChildren?: React.ReactNode;
  iframeUrl?: string;
  // 2024-12-19 zhouby -> cursor ai结尾共生成1行代码
  documentType?: string; // 单据类型用于判断左侧详情展示
}

interface DetailData {
  // 这里定义详情数据的类型
  id: string | number;
  [key: string]: any;
}

const DetailDrawer: React.FC<IProps> = ({
  id,
  taskId,
  sourceNo,
  approvalNo,
  children,
  onRefresh,
  documentType,
  componentsChildren,
  iframeUrl,
}) => {
  const [detailLoading, setDetailLoading] = useState(false);
  const [detailData, setDetailData] = useState<DetailData | null>(null);
  const [visible, setVisible] = useState(false);
  const [isPrintModalVisible, setIsPrintModalVisible] = useState(false);

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const iframeToken = (window as any).token;
  const [iframeId, setIframeId] = useState('');
  const printFrameRef = useRef<HTMLIFrameElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // ai生成
  const onCancel = () => {
    setVisible(false);
    setDetailData(null);
    setDetailLoading(false);

    // 调用刷新回调函数
    if (onRefresh) {
      onRefresh();
    }
  };
  // 20240720-飞虎ai结尾共生成7行代码

  // 打开抽屉
  const showDrawer = () => {
    setVisible(true);
  };

  // 获取详情数据
  const fetchDetail = async () => {
    setDetailLoading(true);
    try {
      // 模拟接口请求

      setIframeId(sourceNo);
    } catch (error) {
      message.error('获取详情失败');
      console.error('获取详情失败:', error);
    } finally {
      setDetailLoading(false);
    }
  };

  // 当抽屉变为可见时获取详情
  useEffect(() => {
    if (visible) {
      fetchDetail();
    }
  }, [visible]);

  // 打印
  const handlePrint = () => {
    // ai生成
    // 显示打印预览模态框，而不是直接调用directPrint方法
    if (iframeId) {
      setIsPrintModalVisible(true);
    } else {
      message.error('未获取到打印所需的ID信息');
    }
    // 20240520-飞虎ai结尾共生成5行代码
  };

  return (
    <>
      <span onClick={showDrawer}>{children}</span>
      <Drawer
        title={
          <div>
            <div>详情</div>
            <div style={{ position: 'absolute', right: '56px', top: '12px' }}>
              <AuthWrapper functionName="f_paas_print_page">
                {(documentType === 'PRE_ORDER' || documentType === 'SETTLEMENT_ORDER') && ( // 目前仅有预付款申请单和结算单支持打印，后续支持打印需要增加打印页面
                  <Button type="primary" onClick={handlePrint}>
                    打印
                  </Button>
                )}
              </AuthWrapper>
              {taskId && <ApproveBtn id={id} taskId={taskId} onSuccess={onCancel} />}
            </div>
          </div>
        }
        width={1350}
        maskClosable={true}
        destroyOnClose={true}
        className={style['legal-check-drawer']}
        visible={visible}
        style={{ transform: 'translateX(0)' }}
        onClose={onCancel}
      >
        <Spin spinning={detailLoading}>
          <div className={style['drawer-content-layout']} ref={contentRef}>
            <div className={style['left-content']}>
              {componentsChildren ? (
                //ai生成
                componentsChildren
              ) : // 2024-12-19 zhouby -> cursor ai结尾共生成1行代码
              iframeUrl ? (
                <iframe
                  ref={iframeRef}
                  id="afferentIframe"
                  src={iframeUrl}
                  className={style['iframe-container']}
                />
              ) : (
                documentType === 'PRE_ORDER' && (
                  <iframe
                    ref={iframeRef}
                    id="myIframe"
                    //   src='http://localhost:8081/advancePaymentAdd?id=1017&token=eyJhbGciOiJIUzI1NiJ9.eyJjb21wYW55SW5mb0lkIjoxNTQ2Miwibmlja05hbWUiOiIiLCJhZG1pbklkIjoxNTQ2Miwicm9sZU5hbWUiOiLlhajpg6giLCJlbXBsb3llZUlkIjoiODAwMDAxOTFiOGFiYTE4MTA5MzE5YmZlMDU2ZjQ0MTkiLCJzdG9yZU5hbWUiOiIiLCJzdG9yZUlkIjoyMzQ1ODIzNjQsImV4cCI6MTc3ODU3MjI0OCwidXNlcklkIjo2NDM4LCJFBXBSB3llZU5hbWUiOiLmnY7po57omY4iLCJwbGF0Zm9ybSI6IkZJTkFOQ0UiLCJyZWFsRW1wbG95ZWVOYW1lIjoi5p2O6aOe6JmOIn0.Dlfcfo7IKdjtb9lmBHX0xiScuwlp54oBQMOG-BifIg4'
                    src={`${apiEntry}/advancePaymentAdd?code=${iframeId}&token=${iframeToken}`}
                    className={style['iframe-container']}
                  />
                )
              )}
              {/* 左边使用iframe解决财务子系统详情展示问题，后学接入其他主系统模块再设计其他方式 */}
            </div>
            <div className={style['right-content']}>
              {/* 右边使用ApproveList组件 */}
              {id && <ApproveList id={id} taskId={taskId} />}
            </div>
          </div>
        </Spin>
      </Drawer>

      {/* 打印预览模态框 */}
      {isPrintModalVisible && (
        <>
          {documentType === 'PRE_ORDER' && (
            <PrintModal
              visible={isPrintModalVisible}
              id={id}
              taskId={taskId}
              sourceNo={sourceNo}
              approvalNo={approvalNo}
              onCancel={() => setIsPrintModalVisible(false)}
            />
          )}
          {documentType === 'SETTLEMENT_ORDER' && (
            <PrintSettlementModal
              visible={isPrintModalVisible}
              id={id}
              taskId={taskId}
              sourceNo={sourceNo}
              approvalNo={approvalNo}
              onCancel={() => setIsPrintModalVisible(false)}
            />
          )}
        </>
      )}
    </>
  );
};

export default DetailDrawer;
