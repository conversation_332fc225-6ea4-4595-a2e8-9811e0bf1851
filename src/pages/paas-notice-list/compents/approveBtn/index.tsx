// ai生成
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Spin, message } from 'antd';
import {
  workflowTaskOperData,
  workflowCompleteTask,
  workflowRefuseTask,
  workflowReject,
} from '@/services/workflow';
import style from './index.module.less';
import ApproveReturnModal from '../approveReturnModal';
import ApproveCommentModal from '../approveCommentModal';

interface ApproveButtonProps {
  id?: string | number;
  taskId?: string | number;
  onSuccess?: () => void; // 添加成功回调函数
}

interface OperItem {
  key: string;
  edit: boolean;
  name: string;
  type: string;
  checked: boolean;
  defaultName: string;
  icon?: string;
}

const ApproveBtn: React.FC<ApproveButtonProps> = ({ id, taskId, onSuccess }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [buttonLoading, setButtonLoading] = useState<string>(''); // 添加按钮loading状态
  const [operList, setOperList] = useState<OperItem[]>([]);
  const [returnModalVisible, setReturnModalVisible] = useState<boolean>(false);
  const [commentModalVisible, setCommentModalVisible] = useState<boolean>(false);
  const [currentOperation, setCurrentOperation] = useState<'pass' | 'refuse' | ''>('');
  const [nowNodeId, setNowNodeId] = useState<any>('');

  const fetchOperButtons = async () => {
    setLoading(true);
    try {
      const params = `taskId=${taskId}`;
      const response: any = await workflowTaskOperData(params);

      if (response && response.res && response.res.ok) {
        const filteredOperList = (response.res.data?.operList || []).filter(
          (item: OperItem) => item.name !== '提交',
        );
        setOperList(filteredOperList);
        setNowNodeId(response.res.data?.nodeId || '');
      } else {
        message.error(response?.res?.msg || '获取审批按钮失败');
      }
    } catch (error) {
      console.error('获取审批按钮失败:', error);
      message.error('获取审批按钮失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOperButtons();
  }, [id]);

  // 处理评论提交
  const handleCommentSubmit = (approveDesc: string, approveImageList: any[]) => {
    if (!id) {
      message.error('缺少必要参数');
      setCommentModalVisible(false);
      return;
    }

    // 设置按钮loading状态
    setButtonLoading(currentOperation);

    let imageList: any[] = [];
    if (approveImageList.length > 0) {
      imageList = approveImageList.map((item) => {
        return {
          url: item.url,
        };
      });
    }
    const params = {
      processInstanceId: id,
      taskId: taskId,
      approveDesc,
      approveImageList: imageList,
    };

    if (currentOperation === 'pass') {
      // 调用审批批准接口
      workflowCompleteTask(params)
        .then((response) => {
          const res = response?.res || response;
          if (res && res.ok) {
            message.success('审批成功');
            fetchOperButtons();
            // 调用成功回调函数关闭抽屉
            onSuccess && onSuccess();
          } else {
            message.error(res?.msg || '审批失败');
          }
        })
        .catch((err) => {
          console.error('审批失败:', err);
          message.error('审批失败');
        })
        .finally(() => {
          // 清除按钮loading状态
          setButtonLoading('');
        });
    } else if (currentOperation === 'refuse') {
      // 调用审批驳回接口
      workflowRefuseTask(params)
        .then((response) => {
          const res = response?.res || response;
          if (res && res.ok) {
            message.success('审批成功');
            fetchOperButtons();
            // 调用成功回调函数关闭抽屉
            onSuccess && onSuccess();
          } else {
            message.error(res?.msg || '审批失败');
          }
        })
        .catch((err) => {
          console.error('审批失败:', err);
          message.error('审批失败');
        })
        .finally(() => {
          // 清除按钮loading状态
          setButtonLoading('');
        });
    }

    setCommentModalVisible(false);
    setCurrentOperation('');
  };

  const handleButtonClick = (key: string) => {
    console.log('按钮点击:', key);

    switch (key) {
      case 'pass':
        // 显示评论弹框
        setCurrentOperation('pass');
        setCommentModalVisible(true);
        break;
      case 'refuse':
        // 显示评论弹框
        setCurrentOperation('refuse');
        setCommentModalVisible(true);
        break;
      case 'reject':
        // 显示退回弹框
        setReturnModalVisible(true);
        break;
      default:
        message.info(`点击了${key}按钮，功能开发中...`);
        break;
    }
  };

  // 退回Modal确认
  const handleReturnConfirm = () => {
    setReturnModalVisible(false);
    // 刷新页面数据
    fetchOperButtons();
  };

  return (
    <>
      {operList
        .filter((item) => item.checked)
        .map((item) => (
          <Button
            key={item.key}
            type={item.type as any}
            loading={buttonLoading === item.key} // 添加loading状态
            onClick={() => handleButtonClick(item.key)}
            style={{ marginLeft: '8px' }}
          >
            {item.name}
          </Button>
        ))}

      {/* 退回设置弹框 */}
      <ApproveReturnModal
        id={id as string}
        taskId={taskId as string}
        nowNodeId={nowNodeId as string}
        visible={returnModalVisible}
        onCancel={() => setReturnModalVisible(false)}
        onOk={handleReturnConfirm}
        onSuccess={onSuccess}
        onLoadingChange={(loading) => setButtonLoading(loading ? 'reject' : '')} // 添加loading状态回调
      />

      {/* 审批评论弹框 */}
      <ApproveCommentModal
        visible={commentModalVisible}
        operationType={currentOperation}
        onCancel={() => {
          setCommentModalVisible(false);
          setCurrentOperation('');
        }}
        onOk={handleCommentSubmit}
      />
    </>
  );
};

export default ApproveBtn;
// 2024年07月22日-飞虎ai结尾共生成147行代码
