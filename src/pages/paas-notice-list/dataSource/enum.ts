// ai生成
export enum MODULE_TYPE_ENUM {
  LOP_FINANCE = 'LOP_FINANCE',
  GOODS_CENTER = 'GOODS_CENTER',
  LOP_PIM = 'LOP_PIM',
}

// 所属模块枚举
export enum ModuleTypeEnum {
  LOP_FINANCE = '财务结算管理',
  GOODS_CENTER = '商品中心',
  LOP_PIM = '法务',
}

export enum DOCUMENT_TYPE_ENUM {
  PRE_ORDER = 'PRE_ORDER',
  SETTLEMENT_ORDER = 'SETTLEMENT_ORDER',
  GOODS_COMPLIANCE_APPROVAL = 'GOODS_COMPLIANCE_APPROVAL',
  UNREPORTED_GOODS_SPECIALLY_ALLOWED = 'UNREPORTED_GOODS_SPECIALLY_ALLOWED',
  SUPPLIER_AUDIT = 'SUPPLIER_AUDIT',
}

// 单据类型枚举
export enum DocumentTypeEnum {
  PRE_ORDER = '预付款申请单',
  SETTLEMENT_ORDER = '付款结算单',
  GOODS_COMPLIANCE_APPROVAL = '商品合规审批',
  UNREPORTED_GOODS_SPECIALLY_ALLOWED = '未提报商品特批',
  SUPPLIER_AUDIT = '主体特批',
}

// 获取所属模块显示文本
export const getModuleTypeText = (type?: string): string => {
  if (!type) return '-';
  return ModuleTypeEnum[type as keyof typeof ModuleTypeEnum] || type;
};

// 获取单据类型显示文本
export const getDocumentTypeText = (type?: string): string => {
  if (!type) return '-';
  return DocumentTypeEnum[type as keyof typeof DocumentTypeEnum] || type;
};
// 20240722-飞虎ai结尾共生成17行代码
