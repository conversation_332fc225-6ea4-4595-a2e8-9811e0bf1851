import React from 'react';
import { DOCUMENT_TYPE_ENUM } from '../dataSource/enum';
import { DetailCom } from '@/pages/compliance-audit-logs/components';
import DelistingDetail from '@/pages/productDelisting/components/DetailCom';
import SettlementDetailCom from '../compents/detailCompents/settlementDetailCom';
import SupplierAuditDetail from '../compents/supplierAuditDetail';

export const comUrlMap: (params: any) => {
  [key: string]: {
    componentsChildren?: React.ReactNode;
    iframeUrl?: string;
  };
} = (params = {}) => {
  // @DES: zhouby 以防产品或者后端变化所以自己解构出需要的参数传到自己的组件吧
  const { id, processInstanceId } = params;
  // @DES: zhouby 在这里配置单据类型对应detailDrawer左侧详情页面的组件或者iframeUrl
  // @DES: zhouby componentsChildren 组件和iframeUrl 二选一 componentsChildren优先级更高
  return {
    [DOCUMENT_TYPE_ENUM.GOODS_COMPLIANCE_APPROVAL]: {
      componentsChildren: <DetailCom sourceOrderNo={id} />,
    },
    [DOCUMENT_TYPE_ENUM.UNREPORTED_GOODS_SPECIALLY_ALLOWED]: {
      componentsChildren: <DelistingDetail sourceOrderNo={id} />,
    },
    [DOCUMENT_TYPE_ENUM.SETTLEMENT_ORDER]: {
      componentsChildren: <SettlementDetailCom id={processInstanceId} />,
    },
    [DOCUMENT_TYPE_ENUM.SUPPLIER_AUDIT]: {
      componentsChildren: (
        <SupplierAuditDetail sourceOrderNo={id} processInstanceId={processInstanceId} />
      ),
    },
  };
};
