import { ColumnProps } from 'antd/lib/table';
import moment from 'moment';
import styles from '../index.module.less';
import React from 'react';
import { history } from 'qmkit';
import DetailDrawer from '../compents/detailDrawer';
import { getModuleTypeText, getDocumentTypeText } from './enum';
import PopoverRowText from '@/components/PopoverRowText';
// ai生成
// 根据实际路径调整
import { getAdvancePaymentDetail } from '@/services/finance';
import { comUrlMap } from './comMap';
import { ModuleTypeEnum, MODULE_TYPE_ENUM, DOCUMENT_TYPE_ENUM } from './enum';
// 20240718-飞虎ai结尾共生成1行代码

interface TableRowData {
  id: string | number;
  approvalNo?: string;
  approvalTitle?: string;
  moduleType?: string;
  documentType?: string;
  sourceNoList?: string[];
  currentNodeName?: string;
  approvalUserList?: any[];
  creatorName?: string;
  createTime?: string;
  [key: string]: any;
}

// ai生成
// 定义刷新回调函数类型
type RefreshCallback = () => void;

// 修改GetColunms类型定义，使其接收refreshCallback参数
type GetColunms<T> = (refreshCallback?: RefreshCallback) => ColumnProps<T>[];
// 20240720-飞虎ai结尾共生成4行代码

const getColumnsTodo: GetColunms<TableRowData> = (refreshCallback) => [
  {
    dataIndex: 'index',
    title: '#',
    width: 40,
    render: (_, record, index) => index + 1,
  },
  {
    title: '审批编号',
    width: 150,
    dataIndex: 'approvalNo',
    key: 'approvalNo',
    render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
  },
  {
    title: '审批标题',
    width: 150,
    dataIndex: 'approvalTitle',
    key: 'approvalTitle',
    render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
  },
  {
    title: '所属模块',
    width: 100,
    dataIndex: 'moduleType',
    key: 'moduleType',
    render: (val) => getModuleTypeText(val),
  },
  {
    title: '单据类型',
    width: 100,
    dataIndex: 'documentType',
    key: 'documentType',
    render: (val) => getDocumentTypeText(val),
  },
  {
    title: '来源单号',
    width: 150,
    dataIndex: 'sourceNoList',
    key: 'sourceNoList',
    render: (val) =>
      Array.isArray(val) && val.length > 0 ? <PopoverRowText text={val.join('，')} /> : '-',
  },
  {
    title: '当前审批节点',
    width: 150,
    dataIndex: 'currentNodeName',
    key: 'currentNodeName',
    render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
  },
  {
    title: '审批人',
    width: 80,
    dataIndex: 'approvalUserList',
    key: 'approvalUserList',
    render: (val) =>
      Array.isArray(val) && val.length > 0 ? (
        <PopoverRowText text={val.map((item) => item.userName).join('，')} />
      ) : (
        '-'
      ),
  },
  {
    title: '创建人',
    width: 80,
    dataIndex: 'creatorName',
    key: 'creatorName',
    render: (val) => val || '-',
  },
  {
    title: '创建时间',
    width: 150,
    dataIndex: 'createTime',
    key: 'createTime',
    render: (val) => val || '-',
    // render: (val) => (val ? moment(val).format('YYYY-MM-DD') : '-'),
  },
  {
    title: '操作',
    width: 80,
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    // ai生成
    render: (_, record) => {
      const handleClick = async (e: React.MouseEvent) => {
        e.preventDefault();
        // if (
        //   record.moduleType === 'LOP_FINANCE' &&
        //   record.documentType === 'PRE_ORDER' &&
        //   record.sourceNoList &&
        //   record.sourceNoList.length > 0
        // ) {
        //   try {
        //     const response = await getAdvancePaymentDetail({ no: record.sourceNoList[0] });
        //     console.log('response', response);
        //     if (response?.res?.result?.status === 'SEND_BACK') {
        //       history.push(`/financialSettlemen/advancePaymentAdd?code=${record.sourceNoList[0]}`);
        //       return;
        //     }
        //   } catch (error) {
        //     console.error('获取预付款详情失败', error);
        //   }
        // }
      };
      // @DES: zhouby 目前和后端规定所有左侧详情页面都使用来源单号作为id查详情
      // @DES: zhouby 以防产品或者后端变化所以自己传入自己需要的参数把
      // @DES: zhouby 按照规范这里和DetailDrawer组件不需要修改 只需要在comURLMap中添加新对应的新的单据类型map 除非左侧详情需要特殊的参数在下面传入需要的参数
      const comUrl = comUrlMap({
        id: record?.sourceNoList?.length ? record?.sourceNoList[0] : undefined,
        processInstanceId: record?.processInstanceId,
      });

      // @DES: zhouby 注释这段是测试用的
      // const { componentsChildren, iframeUrl } =
      //   comUrl[DOCUMENT_TYPE_ENUM.GOODS_COMPLIANCE_APPROVAL] || {};
      const { componentsChildren, iframeUrl } =
        comUrl[record?.documentType as DOCUMENT_TYPE_ENUM] || {};

      return (
        <DetailDrawer
          id={record.processInstanceId}
          taskId={record?.taskDto?.taskId}
          sourceNo={record?.sourceNoList?.[0]}
          approvalNo={record.approvalNo}
          onRefresh={refreshCallback} // 传递刷新回调函数
          documentType={record.documentType} // 单据类型
          componentsChildren={componentsChildren}
          iframeUrl={iframeUrl}
        >
          <a onClick={handleClick}>详情</a>
        </DetailDrawer>
      );
      // 20240720-飞虎ai结尾共生成24行代码
    },
  },
];

export { getColumnsTodo };
