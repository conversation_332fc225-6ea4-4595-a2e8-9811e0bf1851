import React, { useEffect, useState } from 'react';
import {
  DetailContextBox,
  DetailContentItem,
} from '@/components/DetailFormCompoments/DetailContentItem';
import { Card, Title } from '@/components/DetailFormCompoments';
import { LiveAnchorDeatilResult } from '../services';
import moment from 'moment';
import { renderQualification } from '@/pages/choice-list-new/components/LegalCheckDrawer/imgUtils';
import { getResourceUrls } from '@/components/FileUploadSouceId/api';

const Item = DetailContentItem;

const BasicForm: React.FC<{ info?: LiveAnchorDeatilResult }> = ({ info }) => {
  const [urls, seturls] = useState<{ url: string }[]>([]);
  const imageList = async () => {
    try {
      const { res } = await getResourceUrls(info?.imagePhotoList as string[]);
      if (res?.result) {
        seturls(info?.imagePhotoList?.map((item) => ({ url: res?.result?.[item] })) ?? []);
      }
    } catch (error) {
      seturls([]);
    }
  };
  useEffect(() => {
    info?.imagePhotoList?.length && imageList();
  }, [info?.imagePhotoList]);
  return (
    <Card>
      <Title>个人信息</Title>
      <DetailContextBox>
        <Item label="毕业院校">{info?.school ?? '-'}</Item>
        <Item label="生日">
          {info?.birthday ? moment(info?.birthday).format('YYYY-MM-DD') : '-'}
        </Item>
        <Item label="身高（cm）">{info?.height ?? '-'}</Item>
        <Item label="体重（kg）">{info?.weight ?? '-'}</Item>
        <Item label="上衣尺码">{info?.topSize ?? '-'}</Item>
        <Item label="裤子尺码">{info?.pantsSize ?? '-'}</Item>
        <Item label="裙子尺码">{info?.skirtSize ?? '-'}</Item>
        <Item label="鞋子尺码（欧码）">{info?.shoesSize ?? '-'}</Item>
        <Item label="邮箱账号">{info?.email ?? '-'}</Item>
        <Item label="邮寄地址">{info?.postalAddress ?? '-'}</Item>
        <Item label="紧急联系人">{info?.urgencyUser ?? '-'}</Item>
        <Item label="紧急联系人电话">{info?.urgencyMobile ?? '-'}</Item>
        <Item label="形象照">{urls?.length ? renderQualification(urls) : '-'}</Item>
      </DetailContextBox>
    </Card>
  );
};

export default BasicForm;
