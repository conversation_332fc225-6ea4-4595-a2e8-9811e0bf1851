export enum ANCHOR_RATING {
  'S' = 'S',
  A = 'A',
  'B+' = 'B+',
  B = 'B',
  'C+' = 'C+',
  C = 'C',
  D = 'D',
  E = 'E',
}

export enum ARTIST_CATEGORY {
  internal = '内部',
  external = '外部',
}

export enum SIGNING_AGREEMENT_TEMPLATE {
  '主播经纪合作协议' = '1',
  '补充协议' = '2',
  '服务费确认单' = '3',
  '主播服务合同' = '8',
}
export enum SIGNING_AGREEMENT_TEMPLATE_OUT {
  '劳务协议' = '4',
  '补充协议' = '5',
  '合作确认单' = '6',
  '直播营销服务' = '7',
}

export enum COOPERATION_STATUS {
  '未开始' = 1,
  '合作中' = 2,
  '已到期' = 3,
}
export enum SIGN_TYPE {
  'SIGN_CONTRACT_PUBLIC' = '对公签约',
  'SIGN_CONTRACT_INDIVIDUAL' = '个人签约',
}
export enum CASH_TYPE {
  CORPORATE = '对公打款',
  PLATFORM = '平台结算',
  PERSONAL = '个人结算',
}
export enum FEE_TYPE {
  ZERO = '0%',
  CUSTOMIZE = '6.5%',
}
export enum FEE_TYPE_TXT {
  ZERO = '我司承担',
  CUSTOMIZE = '主播承担',
}
