import React, { useMemo, useState, useCallback } from 'react';
import styles from '@/styles/index.module.less';
import { Tag, Form, Select, Input, InputNumber, message, Table } from 'antd';
import { STATUS, PageQueryItem, STATUS_NAME, STATUS_LIST } from '../types';
import moment from 'moment';
import { AuthWrapper, history } from 'qmkit';
import { debounce } from 'lodash';
import { useRequest } from 'ahooks';
import { systemCodeTypeDisable, systemCodeTypeEnable } from '../services/yml';
import PopoverRowText from '@/components/PopoverRowText/index';
import { ChildForm } from '../components';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

export const useTable = (onRefresh: () => void) => {
  const { run: disableRun, loading: disabledLoading } = useRequest(systemCodeTypeDisable, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('操作成功');
        onRefresh?.();
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });
  const { run: enableRun, loading: enableLoading } = useRequest(systemCodeTypeEnable, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('操作成功');
        onRefresh?.();
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const columns = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 30,
    },
    {
      title: '代码code',
      key: 'codeType',
      dataIndex: 'codeType',
      width: 110,
      render: (codeType: string, record: PageQueryItem) => {
        return (
          <a
            onClick={() => {
              history.push(`system-code-detail?codeType=${record?.codeType}`);
            }}
          >
            <PopoverRowText text={codeType || '-'} />
          </a>
        );
      },
    },
    {
      title: '代码名称',
      key: 'typeName',
      dataIndex: 'typeName',
      width: 110,
      render: (typeName: string) => <PopoverRowText text={typeName || '-'} />,
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      width: 70,
      render: (status: STATUS) => {
        return status === STATUS.DISABLE ? (
          <Tag color="red">{STATUS_NAME[status] || '-'}</Tag>
        ) : (
          <Tag color="green">{STATUS_NAME[status] || '-'}</Tag>
        );
      },
    },
    {
      title: '创建人',
      key: 'creatorName',
      dataIndex: 'creatorName',
      width: 80,
      render: (creatorName: string) => creatorName || '-',
    },
    {
      title: '创建时间',
      key: 'gmtCreated',
      dataIndex: 'gmtCreated',
      width: 130,
      render: (gmtCreated: string) =>
        gmtCreated ? moment(gmtCreated).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '最近修改人',
      key: 'modifierName',
      dataIndex: 'modifierName',
      width: 100,
      render: (modifierName: string) => modifierName || '-',
    },
    {
      title: '最近修改时间',
      key: 'gmtModified',
      dataIndex: 'gmtModified',
      width: 130,
      render: (gmtModified: string) =>
        gmtModified ? moment(gmtModified).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '操作',
      key: 'other',
      dataIndex: 'other',
      width: 140,
      fixed: 'right',
      render: (_: string, record: PageQueryItem) => {
        return (
          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
            <AuthWrapper functionName="f_system_code_add">
              <a
                onClick={() => {
                  history.push(`system-code-add?codeType=${record?.codeType}`);
                }}
              >
                编辑
              </a>
            </AuthWrapper>
            <AuthWrapper functionName="f_system_code_close_open">
              {record?.status === STATUS.DISABLE ? (
                <a
                  style={{ marginLeft: '6px' }}
                  onClick={() => {
                    enableRun({ id: record?.id });
                  }}
                >
                  启用
                </a>
              ) : (
                <a
                  style={{ color: 'red', marginLeft: '6px' }}
                  onClick={() => {
                    disableRun({ id: record?.id });
                  }}
                >
                  禁用
                </a>
              )}
            </AuthWrapper>
          </div>
        );
      },
    },
  ];

  return {
    columns,
    disabledLoading,
    enableLoading,
  };
};

export const useAddTable = (
  getFieldDecorator: any,
  initValue: any,
  codeType: boolean,
  validateInput: any,
  setValue: (id: string) => void,
) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [childrenDataSource, setChildrenDataSource] = useState<any[]>([]);
  const [selectedKeys, setSelecteKeys] = useState([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  const handleDelAllChild = debounce((pIds: string[] = []) => {
    setChildrenDataSource(childrenDataSource.filter((item) => !pIds.includes(item?.pId)));
  }, 500);

  const AddItem = debounce(() => {
    const id = `${Math.random()}${moment().valueOf()}`;
    setDataSource((prev: any) => [...prev, { id: id }]);
    setValue(id);
  }, 500);

  const expandedRowRender = useCallback(
    (record: any) => {
      const currentLineData = childrenDataSource.filter((item) => item?.pId === record?.id);

      const handleDel = debounce((record: any) => {
        setChildrenDataSource(childrenDataSource.filter((item) => item?.id !== record?.id));
      }, 500);

      const handleEditItem = debounce((record: any) => {
        const arr = [...childrenDataSource];

        arr.forEach((item) => {
          if (item?.id === record?.id) {
            Object.assign(item, record);
          }
        });
        setChildrenDataSource(arr);
      }, 500);

      const columns = [
        {
          title: '#',
          align: 'center',
          key: 'number',
          className: styles['table-number'],
          render: (text: any, red: any, index: any) => {
            return <span>{index + 1}</span>;
          },
          width: 30,
        },

        {
          title: '状态',
          key: 'status',
          dataIndex: 'status',
          width: 130,
          render: (_: STATUS, record: any, index: number) => {
            return <span>{STATUS_NAME[_] || '-'}</span>;
          },
        },
        {
          title: '代码值code',
          key: 'codeValue',
          dataIndex: 'codeValue',
          width: 130,
          render: (_: any, record: any, index: number) => {
            return <span>{_ || '-'}</span>;
          },
        },
        {
          title: '代码值名称',
          key: 'valueName',
          dataIndex: 'valueName',
          width: 130,
          render: (_: any, record: any, index: number) => {
            return <span>{_ || '-'}</span>;
          },
        },
        {
          title: '排序',
          key: 'sort',
          dataIndex: 'sort',
          width: 130,
          render: (_: any, record: any, index: number) => {
            return <span>{isNullOrUndefined(_) ? '0' : _}</span>;
          },
        },
        {
          title: '操作',
          key: 'other',
          dataIndex: 'other',
          width: 100,
          render: (_: any, record: any, index: number) => {
            return (
              <>
                <ChildForm
                  pId={record?.pId}
                  handleEditItem={handleEditItem}
                  initValue={record}
                  codeType={codeType}
                >
                  <a>编辑</a>
                </ChildForm>
                {codeType && record.disabled ? (
                  <></>
                ) : (
                  <a
                    onClick={() => {
                      handleDel(record);
                    }}
                    style={{ color: 'red', marginLeft: '8px' }}
                  >
                    删除
                  </a>
                )}
              </>
            );
          },
        },
      ];
      return (
        <Table
          columns={columns as any}
          dataSource={currentLineData}
          pagination={false}
          rowKey="id"
        />
      );
    },
    [childrenDataSource],
  );

  const handleAdd = debounce((record: any) => {
    setChildrenDataSource([...childrenDataSource, { ...record }]);
    if (!expandedRowKeys.includes(record?.pId)) {
      setExpandedRowKeys([...expandedRowKeys, record?.pId]);
    }
  }, 500);

  const columns = useMemo(
    () => [
      {
        title: '#',
        align: 'center',
        key: 'id',
        dataIndex: 'id',
        className: styles['table-number'],
        render: (text: any, red: any, index: any) => {
          return (
            <Form.Item>
              {getFieldDecorator(
                `systemCodeValueModels[${index}].id`,
                {},
              )(<span>{index + 1}</span>)}
            </Form.Item>
          );
        },
        width: 30,
      },
      // {
      //   title: 'id',
      //   key: 'id',
      //   dataIndex: 'id',
      //   width: 0,
      //   render: (_: any, record: any, index: number) => {
      //     return (
      //       <Form.Item>
      //         {getFieldDecorator(
      //           `systemCodeValueModels[${index}].id`,
      //           {},
      //         )(<span>{record?.id}</span>)}
      //       </Form.Item>
      //     );
      //   },
      // },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>状态
          </span>
        ),
        key: 'status',
        dataIndex: 'status',
        width: 160,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              {getFieldDecorator(`systemCodeValueModels[${index}].status`, {
                rules: [
                  {
                    required: true,
                    message: '请选择状态',
                  },
                ],
                initialValue: dataSource?.[index]?.status,
              })(
                <Select style={{ width: '160px' }} allowClear placeholder="请选择">
                  {STATUS_LIST?.map((item) => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>代码值code
          </span>
        ),
        key: 'codeValue',
        dataIndex: 'codeValue',
        width: 130,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              {getFieldDecorator(`systemCodeValueModels[${index}].codeValue`, {
                rules: [
                  {
                    required: true,
                    message: '请输入代码值code',
                  },
                  {
                    validator: validateInput,
                  },
                ],
                initialValue: dataSource?.[index]?.codeValue,
              })(
                <Input
                  placeholder="请输入代码值code"
                  maxLength={50}
                  disabled={codeType && dataSource?.[index]?.disabled}
                />,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>代码值名称
          </span>
        ),
        key: 'valueName',
        dataIndex: 'valueName',
        width: 130,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              {getFieldDecorator(`systemCodeValueModels[${index}].valueName`, {
                rules: [
                  {
                    required: true,
                    message: '请输入代码值名称',
                  },
                ],
                initialValue: dataSource?.[index]?.valueName,
              })(<Input placeholder="请输入代码值名称" maxLength={200} />)}
            </Form.Item>
          );
        },
      },
      {
        title: <span>排序</span>,
        key: 'sort',
        dataIndex: 'sort',
        width: 130,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              {getFieldDecorator(`systemCodeValueModels[${index}].sort`, {
                initialValue: dataSource?.[index]?.sort,
              })(<InputNumber style={{ width: '100%' }} placeholder="请输入排序" min={0} />)}
            </Form.Item>
          );
        },
      },
      {
        title: '操作',
        key: 'other',
        dataIndex: 'other',
        width: 100,
        render: (_: any, record: any, index: number) => {
          return (
            <ChildForm pId={record?.id} handleAddItem={handleAdd}>
              <a>新增</a>
            </ChildForm>
          );
        },
      },
    ],
    [initValue, getFieldDecorator, childrenDataSource],
  );

  const rowSelection = {
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
    onChange: (selectedRowKeys: any) => {
      setSelecteKeys(selectedRowKeys);
    },
    getCheckboxProps: (record) => {
      return {
        disabled: record?.disabled,
      };
    },
  };

  return {
    columns,
    dataSource,
    setDataSource,
    AddItem,
    rowSelection,
    selectedKeys,
    expandedRowRender,
    childrenDataSource,
    setChildrenDataSource,
    handleDelAllChild,
    expandedRowKeys,
    setExpandedRowKeys,
  };
};

export const useDetailTable = (
  getFieldDecorator: any,
  initValue: any,
  codeType: boolean,
  validateInput: any,
) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedKeys, setSelecteKeys] = useState([]);
  const [childrenDataSource, setChildrenDataSource] = useState<any[]>([]);

  const AddItem = debounce(() => {
    setDataSource((prev: any) => [...prev, { id: `${Math.random()}${moment().valueOf()}` }]);
  }, 500);

  const expandedRowRender = useCallback(
    (record: any) => {
      const currentLineData = childrenDataSource.filter((item) => item?.pId === record?.id);
      const columns = [
        {
          title: '#',
          align: 'center',
          key: 'number',
          className: styles['table-number'],
          render: (text: any, red: any, index: any) => {
            return <span>{index + 1}</span>;
          },
          width: 30,
        },

        {
          title: '状态',
          key: 'status',
          dataIndex: 'status',
          width: 130,
          render: (_: STATUS, record: any, index: number) => {
            return <span>{STATUS_NAME[_] || '-'}</span>;
          },
        },
        {
          title: '代码值code',
          key: 'codeValue',
          dataIndex: 'codeValue',
          width: 130,
          render: (_: any, record: any, index: number) => {
            return <span>{_ || '-'}</span>;
          },
        },
        {
          title: '代码值名称',
          key: 'valueName',
          dataIndex: 'valueName',
          width: 130,
          render: (_: any, record: any, index: number) => {
            return <span>{_ || '-'}</span>;
          },
        },
        {
          title: '排序',
          key: 'sort',
          dataIndex: 'sort',
          width: 130,
          render: (_: any, record: any, index: number) => {
            return <span>{isNullOrUndefined(_) ? '0' : _}</span>;
          },
        },
      ];
      return (
        <Table
          columns={columns as any}
          dataSource={currentLineData}
          pagination={false}
          rowKey="id"
        />
      );
    },
    [childrenDataSource],
  );

  const columns = useMemo(
    () => [
      {
        title: '#',
        align: 'center',
        key: 'number',
        className: styles['table-number'],
        render: (text: any, red: any, index: any) => {
          return <span>{index + 1}</span>;
        },
        width: 30,
      },
      {
        title: <span>状态</span>,
        key: 'status',
        dataIndex: 'status',
        width: 160,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>
                {_ ? STATUS_NAME[_ as STATUS] : '-'}
              </span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>代码值code</span>,
        key: 'codeValue',
        dataIndex: 'codeValue',
        width: 130,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>{_ || '-'}</span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>代码值名称</span>,
        key: 'valueName',
        dataIndex: 'valueName',
        width: 130,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>{_ || '-'}</span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>排序</span>,
        key: 'sort',
        dataIndex: 'sort',
        width: 130,
        render: (_: any, record: any, index: number) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>
                {isNullOrUndefined(_) ? '0' : _}
              </span>
            </Form.Item>
          );
        },
      },
    ],
    [initValue, getFieldDecorator],
  );

  const rowSelection = {
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
    onChange: (selectedRowKeys: any) => {
      setSelecteKeys(selectedRowKeys);
    },
    getCheckboxProps: (record) => {
      return {
        disabled: record?.disabled,
      };
    },
  };

  return {
    columns,
    dataSource,
    setDataSource,
    AddItem,
    rowSelection,
    selectedKeys,
    childrenDataSource,
    setChildrenDataSource,
    expandedRowRender,
  };
};
