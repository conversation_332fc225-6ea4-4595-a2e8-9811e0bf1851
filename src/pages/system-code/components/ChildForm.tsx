import React, { useEffect } from 'react';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { Modal, Form, InputNumber, Select, Input } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import style from '@/styles/index.module.less';
import { STATUS_LIST } from '../types';
import moment from 'moment';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const ChildForm = (props: IProps) => {
  const {
    form,
    visible,
    onRefresh,
    pId,
    handleAddItem,
    handleEditItem,
    initValue,
    codeType,
    ...rest
  } = props;

  const { getFieldDecorator } = form;

  const handleOnOk = () => {
    form.validateFields((err, values) => {
      console.log(err, values);
      if (err) {
        return;
      }
      if (initValue?.id) {
        handleEditItem({ id: initValue?.id, pId, ...values });
        rest?.onCancel();
        return;
      }
      const id = `${Math.random()}${moment().valueOf()}`;
      handleAddItem({ id, pId, ...values });
      rest?.onCancel();
    });
  };

  const validateInput = (rule: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    // 使用正则表达式来检查是否只包含字母、数字及下划线
    if (!/^[a-zA-Z0-9_]*$/.test(value)) {
      return Promise.reject(new Error('请输入字母、数字或下划线'));
    }
    return Promise.resolve();
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]);

  return (
    <Modal
      title="添加二级代码值"
      {...rest}
      visible={visible}
      onOk={handleOnOk}
      width={500}
      className={style['modal-sty']}
      maskClosable={false}
    >
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} required label="状态" style={{ marginBottom: '10px' }}>
          {getFieldDecorator(`status`, {
            rules: [
              {
                required: true,
                message: '请选择状态',
              },
            ],
            initialValue: initValue?.status,
          })(
            <Select style={{ width: '100%' }} allowClear placeholder="请选择">
              {STATUS_LIST?.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="代码值code" style={{ marginBottom: '10px' }}>
          {getFieldDecorator(`codeValue`, {
            rules: [
              { required: true, message: '请输入代码值code' },
              {
                validator: validateInput,
              },
            ],
            initialValue: initValue?.codeValue,
          })(
            <Input
              placeholder="请输入代码值code"
              maxLength={50}
              disabled={codeType && initValue?.disabled}
            />,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="代码值名称" style={{ marginBottom: '10px' }}>
          {getFieldDecorator(`valueName`, {
            rules: [
              {
                required: true,
                message: '请输入代码值名称',
              },
            ],
            initialValue: initValue?.valueName,
          })(<Input placeholder="请输入代码值名称" maxLength={200} />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="排序" style={{ marginBottom: '10px' }}>
          {getFieldDecorator(`sort`, {
            initialValue: initValue?.sort,
          })(<InputNumber style={{ width: '100%' }} placeholder="请输入排序" min={0} />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(ChildForm));
