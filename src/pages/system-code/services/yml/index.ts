import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type PageQueryRequest = {
  codeType?: string /*系统代码枚举-类型code*/;
  current?: number /*当前页码,从1开始*/;
  size?: number /*分页大小*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
  typeName?: string /*系统代码枚举-类型名称*/;
};

export type PageQueryResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    codeType?: string /*系统代码枚举-类型code*/;
    creator?: string /*创建者*/;
    creatorName?: string /*创建者名称*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键id*/;
    modifier?: string /*修改者*/;
    modifierName?: string /*修改者名称*/;
    sort?: number /*排序值*/;
    status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
    systemCodeValueModels?: Array<{
      codeType?: string /*系统代码枚举-类型code*/;
      codeValue?: string /*系统代码枚举-code*/;
      creator?: string /*创建者*/;
      gmtCreated?: string /*创建时间*/;
      gmtModified?: string /*更新时间*/;
      id?: string /*主键id*/;
      modifier?: string /*修改者*/;
      sort?: number /*排序值*/;
      status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
      superCodeValue?: string /*上级-系统代码枚举-类型code*/;
      valueName?: string /*系统代码枚举-code名称*/;
    }> /*系统代码类型对应代码值*/;
    typeName?: string /*系统代码枚举-类型名称*/;
    version?: number /*版本号*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询
 */
export const pageQuery = (params: PageQueryRequest) => {
  return Fetch<ResponseWithResult<PageQueryResult>>('/tools/public/systemCodeType/pageQuery', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/tools/public/systemCodeType/pageQuery') },
  });
};

export type SystemCodeTypeCreateRequest = {
  codeType?: string /*系统代码枚举-类型code*/;
  sort?: number /*排序值*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
  systemCodeValueModels?: Array<{
    codeType?: string /*系统代码枚举-类型code*/;
    codeValue?: string /*系统代码枚举-code*/;
    creator?: string /*创建者*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键id*/;
    modifier?: string /*修改者*/;
    sort?: number /*排序值*/;
    status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
    superCodeValue?: string /*上级-系统代码枚举-类型code*/;
    valueName?: string /*系统代码枚举-code名称*/;
  }> /*代码值*/;
  typeName?: string /*系统代码枚举-类型名称*/;
};

export type SystemCodeTypeCreateResult = boolean;

/**
 *新增系统代码类型
 */
export const systemCodeTypeCreate = (params: SystemCodeTypeCreateRequest) => {
  return Fetch<ResponseWithResult<SystemCodeTypeCreateResult>>(
    '/tools/public/systemCodeType/create',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemCodeType/create') },
    },
  );
};

export type SystemCodeTypeDetailRequest = {
  codeType?: string /*系统代码枚举-类型code*/;
  isGetMultilevel?: boolean /*是否获取多级 true：是;false:否*/;
};

export type SystemCodeTypeDetailResult = {
  codeType?: string /*系统代码枚举-类型code*/;
  creator?: string /*创建者*/;
  creatorName?: string /*创建者名称*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*更新时间*/;
  id?: string /*主键id*/;
  modifier?: string /*修改者*/;
  modifierName?: string /*修改者名称*/;
  sort?: number /*排序值*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
  systemCodeValueModels?: Array<{
    codeType?: string /*系统代码枚举-类型code*/;
    codeValue?: string /*系统代码枚举-code*/;
    creator?: string /*创建者*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键id*/;
    modifier?: string /*修改者*/;
    sort?: number /*排序值*/;
    status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
    superCodeValue?: string /*上级-系统代码枚举-类型code*/;
    valueName?: string /*系统代码枚举-code名称*/;
  }> /*系统代码类型对应代码值*/;
  typeName?: string /*系统代码枚举-类型名称*/;
  version?: number /*版本号*/;
};

/**
 *系统代码类型详情查询
 */
export const systemCodeTypeDetail = (params: SystemCodeTypeDetailRequest) => {
  return Fetch<ResponseWithResult<SystemCodeTypeDetailResult>>(
    '/tools/public/systemCodeType/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemCodeType/detail') },
    },
  );
};

export type SystemCodeTypeUpdateRequest = {
  id?: string /*主键id*/;
  sort?: number /*排序值*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
  systemCodeValueModels?: Array<{
    codeType?: string /*系统代码枚举-类型code*/;
    codeValue?: string /*系统代码枚举-code*/;
    creator?: string /*创建者*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键id*/;
    modifier?: string /*修改者*/;
    sort?: number /*排序值*/;
    status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
    superCodeValue?: string /*上级-系统代码枚举-类型code*/;
    valueName?: string /*系统代码枚举-code名称*/;
  }> /*代码值*/;
  typeName?: string /*系统代码枚举-类型名称*/;
  version?: number /*版本号*/;
};

export type SystemCodeTypeUpdateResult = boolean;

/**
 *编辑系统代码类型
 */
export const systemCodeTypeUpdate = (params: SystemCodeTypeUpdateRequest) => {
  return Fetch<ResponseWithResult<SystemCodeTypeUpdateResult>>(
    '/tools/public/systemCodeType/update',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemCodeType/update') },
    },
  );
};

export type SystemCodeSearchRequest = {
  codeType?: string /*系统代码枚举-类型code*/;
  isGetMultilevel?: boolean /*是否获取多级 true：是;false:否*/;
  valueName?: string /*系统代码枚举-code名称*/;
};

export type SystemCodeSearchResult = {
  codeType?: string /*系统代码枚举-类型code*/;
  creator?: string /*创建者*/;
  creatorName?: string /*创建者名称*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*更新时间*/;
  id?: string /*主键id*/;
  modifier?: string /*修改者*/;
  modifierName?: string /*修改者名称*/;
  sort?: number /*排序值*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
  systemCodeValueModels?: Array<{
    codeType?: string /*系统代码枚举-类型code*/;
    codeValue?: string /*系统代码枚举-code*/;
    creator?: string /*创建者*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键id*/;
    modifier?: string /*修改者*/;
    sort?: number /*排序值*/;
    status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
    superCodeValue?: string /*上级-系统代码枚举-类型code*/;
    valueName?: string /*系统代码枚举-code名称*/;
  }> /*系统代码类型对应代码值*/;
  typeName?: string /*系统代码枚举-类型名称*/;
  version?: number /*版本号*/;
};

/**
 *系统代码枚举类型名称查询
 */
export const systemCodeSearch = (params: SystemCodeSearchRequest) => {
  return Fetch<ResponseWithResult<SystemCodeSearchResult>>(
    '/tools/public/systemCodeType/listValueName',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemCodeType/listValueName') },
    },
  );
};

export type SystemCodeTypeDisableRequest = {
  id?: string /*业务ID*/;
};

export type SystemCodeTypeDisableResult = boolean;

/**
 *禁用系统代码类型
 */
export const systemCodeTypeDisable = (params: SystemCodeTypeDisableRequest) => {
  return Fetch<ResponseWithResult<SystemCodeTypeDisableResult>>(
    '/tools/public/systemCodeType/disable',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemCodeType/disable') },
    },
  );
};

export type SystemCodeTypeEnableRequest = {
  id?: string /*业务ID*/;
};

export type SystemCodeTypeEnableResult = boolean;

/**
 *启用系统代码类型
 */
export const systemCodeTypeEnable = (params: SystemCodeTypeEnableRequest) => {
  return Fetch<ResponseWithResult<SystemCodeTypeEnableResult>>(
    '/tools/public/systemCodeType/enable',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemCodeType/enable') },
    },
  );
};
