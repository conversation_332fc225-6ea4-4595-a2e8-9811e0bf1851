import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import { Spin, Button, Form, Row, Col, Input, Select, Table, message } from 'antd';
import styles from '../index.module.less';
import { DetailTitle } from '@/pages/report-sheet/components';
import { FormComponentProps } from 'antd/lib/form';
import { STATUS_LIST } from '../types';
import { useAddTable } from '../hooks';
import { history } from 'qmkit';
import { getQueryParams } from 'web-common-modules/utils/params';
import {
  systemCodeTypeCreate,
  systemCodeTypeDetail,
  systemCodeTypeUpdate,
  SystemCodeTypeDetailResult,
} from '../services/yml/index';
import { useRequest } from 'ahooks';
import moment from 'moment';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';

const AddPage = ({ form }: FormComponentProps) => {
  const codeType = getQueryParams()?.codeType;
  const { getFieldDecorator } = form;
  const [detail, setDetail] = useState<SystemCodeTypeDetailResult>();
  const { closeAndJumpToPage } = useCloseAndJump();
  const validateInput = (rule: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    // 使用正则表达式来检查是否只包含字母、数字及下划线
    if (!/^[a-zA-Z0-9_]*$/.test(value)) {
      return Promise.reject(new Error('请输入字母、数字或下划线'));
    }
    return Promise.resolve();
  };
  const {
    columns,
    dataSource,
    AddItem,
    rowSelection,
    selectedKeys,
    setDataSource,
    expandedRowRender,
    childrenDataSource,
    handleDelAllChild,
    expandedRowKeys,
    setExpandedRowKeys,
    setChildrenDataSource,
  } = useAddTable(
    getFieldDecorator,
    {},
    codeType as any as boolean,
    validateInput,
    (id: string) => {
      const systemCodeValueModels = form.getFieldValue('systemCodeValueModels');
      console.log('🚀 ~ AddPage ~ systemCodeValueModels:', systemCodeValueModels, id);
      const arr = [...systemCodeValueModels];
      arr[arr.length - 1] = { ...arr[arr.length - 1], id };
      console.log('🚀 ~ AddPage ~ arr:', arr);
      form.setFieldsValue({
        systemCodeValueModels: [...arr],
      });
    },
  );

  //新增
  const { run: createRun, loading: creatreLoading } = useRequest(systemCodeTypeCreate, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('新增成功');
        closeAndJumpToPage('/system-code');
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  // 详情
  const { run: detailRun, loading: detailLoading } = useRequest(systemCodeTypeDetail, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        setDetail(res?.result);
        // 回显
        const { codeType, typeName, status, systemCodeValueModels } = res?.result || {};
        const firstLevel = systemCodeValueModels?.filter(
          (item) => item.superCodeValue === codeType,
        );

        const firstIdCodeMap = firstLevel?.reduce((acc: any, item: any) => {
          acc[item?.codeValue] = item?.id;
          return acc;
        }, {});

        const formSystemCodeValue = firstLevel?.map((item) => ({
          codeValue: item?.codeValue,
          status: item?.status,
          valueName: item?.valueName,
          sort: item?.sort,
          // id: `${Math.random()}${moment().valueOf()}`,
          id: item?.id,
          disabled: true,
        }));

        const formLits = firstLevel?.map((item) => ({
          codeValue: item?.codeValue,
          status: item?.status,
          valueName: item?.valueName,
          sort: item?.sort,
          id: item?.id,
        }));

        const secondLevel = systemCodeValueModels?.filter(
          (item) => item.superCodeValue !== codeType,
        );
        const secondLevelList = secondLevel?.map((item) => {
          const { codeValue, valueName, status, sort, id } = item;
          return {
            codeValue,
            valueName,
            status,
            sort,
            id,
            pId: firstIdCodeMap[item?.superCodeValue],
            disabled: true,
          };
        });

        setDataSource(formSystemCodeValue as any);
        setChildrenDataSource(secondLevelList as any);
        form.setFieldsValue({
          codeType,
          typeName,
          status,
          systemCodeValueModels: formLits,
        });
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  // 编辑
  const { run: updateRun, loading: updateLoading } = useRequest(systemCodeTypeUpdate, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('编辑成功');
        closeAndJumpToPage('/system-code');
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const handleSubmit = () => {
    form.validateFields((err, value) => {
      if (err) return;
      if (!value?.systemCodeValueModels || !value?.systemCodeValueModels?.length) {
        message.warning('请添加代码值');
        return;
      }
      const { systemCodeValueModels, ...params } = value;

      // 可以提出来 暂时这样吧
      if (childrenDataSource?.length) {
        const idCodeMap = systemCodeValueModels?.reduce((acc: any, item: any) => {
          acc[item?.id] = item?.codeValue;
          return acc;
        }, {});
        const childArr = childrenDataSource.map((item: any) => ({
          ...item,
          superCodeValue: idCodeMap[item?.pId],
        }));
        const newSystemCodeValueModels = [...systemCodeValueModels, ...childArr];

        const filtrationSystemCodeValueModels = newSystemCodeValueModels.map((item) => {
          const { id, pId, disabled, ...otherItem } = item;
          return otherItem;
        });
        params.systemCodeValueModels = filtrationSystemCodeValueModels;
      } else {
        params.systemCodeValueModels = systemCodeValueModels?.map((item: any) => {
          const { id, ...otherItem } = item;
          return {
            ...otherItem,
          };
        });
      }
      console.log('🚀 ~ handleSubmit ~ params:', params);
      if (codeType) {
        // 编辑
        updateRun({ id: detail?.id, ...params, version: detail?.version });
      } else {
        // 新增
        createRun(params);
      }
    });
  };

  const handleDelete = () => {
    const systemCodeValueModels = form.getFieldValue('systemCodeValueModels');
    const valueArray = dataSource.map((item: any, index: number) => ({
      ...item,
      ...(systemCodeValueModels[index] || {}),
    }));
    const filterArr = [...valueArray]?.filter((item) => !selectedKeys.includes(item?.id)) || [];
    console.log('🚀 ~ handleDelete ~ filterArr:', filterArr);
    handleDelAllChild(selectedKeys);
    setDataSource([...filterArr]);
    form.setFieldsValue({
      systemCodeValueModels: [...filterArr],
    });
  };

  useEffect(() => {
    if (codeType) {
      detailRun({ codeType, isGetMultilevel: true });
    }
  }, []);

  const handleCancel = () => {
    closeAndJumpToPage('/system-code');
  };

  const handleOnExpand = (expanded: boolean, record: any) => {
    console.log('🚀 ~ handleOnExpand ~ expanded:', expanded, record);
    if (expanded) {
      setExpandedRowKeys([...expandedRowKeys, record?.id]);
    } else {
      setExpandedRowKeys(expandedRowKeys.filter((item) => item !== record?.id));
    }
  };

  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={creatreLoading || detailLoading || updateLoading}>
          <Form>
            <Card title="代码类型">
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="代码code"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('codeType', {
                      rules: [
                        {
                          required: true,
                          message: '请填写代码code',
                        },
                        {
                          validator: validateInput,
                        },
                      ],
                    })(
                      <Input
                        placeholder="请填写代码code"
                        disabled={codeType as any as boolean}
                        maxLength={50}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="代码名称"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('typeName', {
                      rules: [
                        {
                          required: true,
                          message: '请填写代码名称',
                        },
                      ],
                    })(<Input placeholder="请填写代码名称" maxLength={50} />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="状态" labelCol={{ span: 8 }} required wrapperCol={{ span: 16 }}>
                    {getFieldDecorator('status', {
                      rules: [
                        {
                          required: true,
                          message: '请选择状态',
                        },
                      ],
                    })(
                      <Select allowClear placeholder="请选择">
                        {STATUS_LIST?.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </Card>
            <Card title="代码值">
              <div style={{ padding: '0 16px' }} className={styles['table-card']}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                  <Button type="primary" style={{ marginRight: '6px' }} onClick={AddItem}>
                    新增
                  </Button>
                  <Button type="danger" ghost onClick={handleDelete}>
                    删除
                  </Button>
                </div>
                <Table
                  pagination={false}
                  columns={columns as any}
                  dataSource={dataSource}
                  rowKey={'id'}
                  rowSelection={rowSelection}
                  expandedRowRender={expandedRowRender}
                  onExpand={handleOnExpand}
                  expandedRowKeys={expandedRowKeys}
                ></Table>
              </div>
            </Card>
          </Form>
        </Spin>
        <FormBottomCard>
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={creatreLoading || detailLoading || updateLoading}
          >
            保存
          </Button>
          <Button style={{ marginRight: '6px' }} onClick={handleCancel}>
            取消
          </Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddPage);
