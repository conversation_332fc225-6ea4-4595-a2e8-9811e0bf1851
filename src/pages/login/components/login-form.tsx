import { Divider, Form, Input, message, Tabs, Tooltip, Button } from 'antd';
import { Base64, Base64Props } from 'qmkit/base64';
import React, { forwardRef, useEffect, useMemo, useState } from 'react';
import FeishuLogin from 'web-common-modules/biz/FeishuLogin';
import LoginInput from 'web-common-modules/biz/LoginInput';

import { OSSImagePre } from '@/common/constants/moduleConstant';
import { getUrlParam } from 'web-common-modules/utils';
import loginLogo from '@/assets/<EMAIL>';
import fontWL from '@/assets/<EMAIL>';
import fontSH from '@/assets/<EMAIL>';

import { freshToken } from '@/utils/login';

import AccountModal from './AccountModal';
import style from './index.module.less';

const { TabPane } = Tabs;

enum PLAT_ENUM {
  JW = 'JW',
  SJRK = 'SJRK',
}

const platMap = {
  [PLAT_ENUM.JW]: {
    title: '尽微好物',
    key: PLAT_ENUM.JW,
    value: 1,
  },
  [PLAT_ENUM.SJRK]: {
    title: '世纪睿科',
    key: PLAT_ENUM.SJRK,
    value: 2,
  },
};

interface TProps {
  login: Function;
  loginSms: Function;
  form: any;
}
const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};
enum LoginType {
  SCAN = 'SCAN',
  PASSWORD = 'PASSWORD',
}
const LoginForm = Form.create<TProps>({})(forwardRef(Login));
function Login(props: TProps) {
  const {
    login,
    form: { validateFields, getFieldDecorator },
  } = props;
  const [tabKey, setTabKey] = useState('1');
  const [account, setAccount] = useState('');
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);
  const [type, setType] = useState<LoginType>(LoginType.SCAN);
  const [plat, setPlat] = useState<PLAT_ENUM>(PLAT_ENUM.SJRK);

  useEffect(() => {
    const feishuCode = getUrlParam('code');
    const state = getUrlParam('state');
    const isRefresh = getUrlParam('isRefresh');
    if (feishuCode && state) {
      const stateArr = state.split('_');
      handleLogin({
        loginType: 'FEI_SHU',
        feiShuLoginCode: feishuCode,
        accountType: 5,
        loginSource: 'JGPY',
        feishuCompany: stateArr[1] || PLAT_ENUM.JW,
      });
      return;
    }
    if (!window?.h5sdk) {
      console.log('invalid h5sdk');
      setTimeout(() => {
        h5Ready(isRefresh);
      }, 1000);
    } else {
      h5Ready(isRefresh);
    }
  }, []);
  const h5Ready = (isRefresh) => {
    if (isRefresh === 'true') {
      return;
    }
    window?.h5sdk?.ready(() => {
      tt.requestAuthCode({
        appId: 'cli_a3bb860e12b9100e',
        // 获取成功后的回调
        success(res: any) {
          handleLogin({
            loginType: 'FEI_SHU_JGPY_FREE_AUTH',
            feiShuLoginCode: res.code,
            accountType: 5,
            loginSource: 'JGPY',
          });
        },
        // 获取失败后的回调
        fail(err: any) {
          if ([10228].includes(Number(err.errCode))) {
            message.error('应用暂不可用请联系管理员');
            return;
          }
          if ([10232, 10234].includes(Number(err.errCode))) {
            message.error(`${err.errCode}：网络出错请重新打开应用`);
            return;
          }
          if ([10236, 10235, 10200].includes(Number(err.errCode))) {
            message.error(`${err.errCode}：应用出错请联系管理员`);
            return;
          }
          message.error(`${err.errCode}：${err.errMsg}`);
        },
      });
    });
  };
  const handleLogin = (params: any) => {
    login(params).then(({ res }: any) => {
      if (res?.code === '200') {
        if (res?.result?.list?.length > 1) {
          setList(res?.result?.list);
          setVisible(true);
          return;
        }
        const loginInfo = res.result?.list?.[0];
        loginInfo && localStorage.setItem('jgpy-crm@loginInfo', JSON.stringify(loginInfo));
        // 存起来authUserId，自研审批流paas需要
        window.authUserId = loginInfo?.authUserId;
        message.success('登录成功');
        if (
          window.location.origin.includes('localhost') ||
          window.location.origin.includes('http://***********') ||
          window.location.origin === loginInfo?.host
        ) {
          freshToken(loginInfo?.token);
        } else {
          window.location.href =
            loginInfo.host + '/login?token=' + loginInfo.token + '&isRefresh=true';
        }
      } else {
        message.error(res?.message || '网络错误');
      }
    });
  };
  const toLogin = () => {
    const base64: Base64Props = new Base64();
    validateFields((errors: any, values: any) => {
      if (!errors) {
        const params = {
          account: base64.urlEncode(values?.account),
          accountType: 5,
          loginSource: 'JGPY',
        } as {
          account: string;
          accountType: number;
          password?: string;
          verifyCode?: string;
          loginType: string;
          loginSource: string;
        };
        if (tabKey === '1') {
          params.password = base64.urlEncode(values?.password);
          params.loginType = 'PASS';
        }
        handleLogin(params);
      }
    });
  };
  const ToolTitle = () => {
    return (
      <>
        <img src={`${OSSImagePre}/logo/logo-fs.png`} alt="" className={style.toolPic} />
        飞书扫码登录更便捷
      </>
    );
  };
  return (
    <div className={style.container}>
      <div className={style.left}>
        <img src={loginLogo} className={style.imgLogo} />
        <img src={fontWL} className={style.imgFontWL} />

        {/* <img src={fontSH} className={style.imgFontSH} /> */}
        <div className={style.sologan}>
          <p>让业务更高效，让工作更简单</p>
          {/* <p> </p> */}
        </div>
      </div>
      <div className={style.right}>
        <Tooltip
          placement="topRight"
          title={ToolTitle}
          visible={type === LoginType.PASSWORD}
          overlayClassName={style.tooltip}
        >
          <div
            className={style.pic}
            onClick={() => {
              setType(type === LoginType.PASSWORD ? LoginType.SCAN : LoginType.PASSWORD);
            }}
          >
            <span style={type === LoginType.PASSWORD ? { width: '52px', right: '-102px' } : {}}>
              {type === LoginType.PASSWORD ? '二维码登录' : '账号登录'}
            </span>
          </div>
        </Tooltip>
        {type === LoginType.PASSWORD ? (
          <Form className={style.form} {...formItemLayout}>
            <p className={style.title}>登录</p>
            <p className={style.welcome}>欢迎登录朋友云</p>
            <Form.Item>
              {getFieldDecorator('account', {
                getValueFromEvent: (v: any) => {
                  setAccount(v.target.value);
                  return v.target.value;
                },
                rules: [
                  {
                    required: true,
                    message: '请输入手机号码',
                  },
                  {
                    pattern: /^1\d{10}$/,
                    message: '请输入正确的手机号码',
                  },
                ],
              })(
                <LoginInput
                  maxLength={11}
                  placeholder="用户名"
                  className={style.input}
                  allowClear={true}
                />,
              )}
            </Form.Item>
            {tabKey === '1' && (
              <Form.Item className={style.inputTwo}>
                {getFieldDecorator('password', {
                  rules: [
                    {
                      required: true,
                      message: '请输入登录密码',
                    },
                    {
                      pattern: /^[\da-zA-Z]{6,16}$/,
                      message: '长度6-16位，仅支持数字和字母',
                    },
                  ],
                })(
                  <LoginInput
                    className={style.input}
                    placeholder="密码"
                    maxLength={20}
                    type="pass"
                    allowClear={true}
                  />,
                )}
              </Form.Item>
            )}
            <div className={style.btns}>
              <span onClick={() => window.open('/find-password')}>忘记密码</span>
            </div>
            <Button type="primary" className={style.login} onClick={toLogin}>
              登录
            </Button>
          </Form>
        ) : (
          <div className={style.scanBox}>
            <p className={style.title}>登录</p>
            <p className={style.welcome}>欢迎登录朋友云</p>
            <FeishuLogin plat={plat} />
            {/* <p className={style.btns}>扫码登录</p> */}
          </div>
        )}
      </div>
      {visible && <AccountModal list={list} handleClose={() => setVisible(false)} />}
    </div>
  );
}

export default LoginForm;
