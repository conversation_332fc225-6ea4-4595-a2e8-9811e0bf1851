import { useSetState } from 'ahooks';
import { Button, Form, Icon, message, Spin, Popconfirm } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { getUserFileInfo, getUserFileLog, deleteUserFile } from '../../services/index';

import { AuthWrapper, history } from 'qmkit';
import BasicInfo from './BasicInfo';
import { FormComponentProps } from 'antd/lib/form';

import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { DetailContentLayout, DetailTitle, SpinCard } from '@/components/DetailFormCompoments';

type PropsType = FormComponentProps<any>;
type ModalState = {
  loading?: boolean;
  detail?: any;
};

const AddSelectTabPage: React.FC<PropsType> = ({ form }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const id = useMemo(() => getQueryParams()?.id, [location]);

  const type = useMemo(() => getQueryParams()?.type as 'detail', [location]);
  const { delRoutetag } = useCloseAndJump();

  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };
  const [serviceChargeConfirmationLink, setServiceChargeConfirmationLink] = useState('');
  const handleDetail = async () => {
    setState((state) => ({ ...state, loading: true }));
    const detail = await responseWithResultAsync({
      request: getUserFileInfo,
      params: { id },
    });
    console.log('详情数据', detail);

    setState((state) => ({
      ...state,
      loading: false,
      detail: detail ?? undefined,
    }));
  };

  const handleGoEdit = () => {
    history.push(`/speUserProfile-edit?type=edit&id=${id}`);
  };
  useEffect(() => {
    if (id) {
      handleDetail();
      getLogList();
    }
  }, [id]);

  const handleDel = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const params = {
        id,
      };
      const { res } = await deleteUserFile(params);
      if (res.code === '200') {
        handleCancel();
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    });
  };

  const [workLogList, setWorkLogList] = useState([]);
  const getLogList = async () => {
    const result = await responseWithResultAsync({
      request: getUserFileLog,
      params: { userId: id },
    });
    if (result?.records) {
      setWorkLogList(result.records);
    }
  };
  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText={modalState.detail?.userCode}>
          {type === 'detail' && (
            <AuthWrapper functionName="f_special_user_file_edit">
              <Button type="primary" onClick={handleGoEdit}>
                编辑
              </Button>
            </AuthWrapper>
          )}
          {type === 'detail' && (
            <AuthWrapper functionName="f_special_user_file_del">
              <Popconfirm
                title={`您确定要删除吗?`}
                okText="确定"
                cancelText="取消"
                onConfirm={() => handleDel()}
              >
                <Button type="danger">删除</Button>
              </Popconfirm>
            </AuthWrapper>
          )}

          {/* <Button onClick={handleCancel}>取消</Button> */}
        </DetailTitle>
        <SpinCard spinning={modalState.loading}>
          <BasicInfo
            type={type}
            info={modalState.detail}
            serviceChargeConfirmationLink={serviceChargeConfirmationLink}
            workLogList={workLogList}
            form={form}
          />
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
