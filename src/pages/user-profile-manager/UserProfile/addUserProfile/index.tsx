import { useSetState } from 'ahooks';
import { Button, Icon, message, Spin, Modal } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useEffect, useMemo } from 'react';
import BasicForm from './BasicForm';

import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  createUserFile,
  CreateUserFileRequest,
  getUserFileInfo,
  editUserFile,
  getUserByPhone,
} from '../../services';

import moment from 'moment';
import styles from './index.module.less';

import { history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import { debounce } from 'lodash';

type PropsType = FormComponentProps;
type ModalState = {
  loading?: boolean;
  detail?: any;
};

const AddSelectTabPage: React.FC<PropsType> = ({ form }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const id = useMemo(() => getQueryParams().id, []);

  const { delRoutetag } = useCloseAndJump();
  const { confirm } = Modal;
  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };

  const formatParams = (values) => {
    const params: CreateUserFileRequest = {
      ...values,
      userType: 'REAL_INFORMATION',
      city: values?.city ? values?.city.join('/') : '',
    };
    return params;
  };
  const handleCreate = async (params: CreateUserFileRequest) => {
    const result = await responseWithResultAsync({
      request: createUserFile,
      params,
    });
    return result;
  };

  const handleOk = () => {
    setState((state) => ({ ...state, loading: true })); // 设置 loading 为 true
    form.validateFields(async (err, values) => {
      if (err) {
        setState((state) => ({ ...state, loading: false })); // 设置 loading 为 false
        return;
      }
      const params: CreateUserFileRequest = formatParams(values);
      const isInData = await getUserByPhoneFn({
        phone: params.phone,
        aliAccount: params.alipayAccount,
      });
      if (isInData) {
        confirm({
          title: '该手机号/支付宝账号已存在真实信息档案【' + isInData + '】，是否继续提交',
          icon: <Icon type="exclamation-circle" />,
          onOk() {
            finlySave(params);
          },
          onCancel() {
            setState((state) => ({ ...state, loading: false })); // 设置 loading 为 false
            console.log('Cancel');
          },
        });
      } else {
        finlySave(params);
      }
    });
  };
  const finlySave = async (params) => {
    const result = await handleCreate(params);
    setState((state) => ({ ...state, loading: false })); // 设置 loading 为 false
    if (result) {
      message.success('操作成功');

      form.resetFields();
      // handleCancel();
    }
  };

  // 创建防抖处理函数
  const debouncedHandleOk = debounce(handleOk, 300);
  const getUserByPhoneFn = async (params: { phone?: string; aliAccount?: string }) => {
    return new Promise((resolve, reject) => {
      getUserByPhone(params)
        .then(({ res }) => {
          console.log('res', res);

          if (res.code === '200') {
            if (res.result) {
              resolve(res.result);
            } else {
              resolve(false);
            }
          }
        })
        .catch((err) => {
          resolve(false);
        });
    });
  };

  return (
    <PageLayout className={styles['cooperation-report-contain']} routePath="/userProfile-add">
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="基本信息">
            <div className={styles.extra}>
              <BasicForm form={form} type={'create'} />
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          <Button type="primary" onClick={debouncedHandleOk} loading={modalState.loading}>
            保存
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
