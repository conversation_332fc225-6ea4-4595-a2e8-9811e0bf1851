import { Button, Icon, message, Spin } from 'antd';
import { useSetState } from 'ahooks';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useEffect, useMemo } from 'react';
import BasicForm from './BasicForm';

import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  createUserFile,
  CreateUserFileRequest,
  getUserFileInfo,
  editUserFile,
} from '../../services';

import moment from 'moment';
import styles from './index.module.less';

import { history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';

import { getResourceUrls } from '@/components/FileUpload/api';
// 引入 lodash 的 debounce 函数
import { debounce } from 'lodash';

type PropsType = FormComponentProps;
type ModalState = {
  loading?: boolean;
  detail?: any;
};

const AddSelectTabPage: React.FC<PropsType> = ({ form }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const id = useMemo(() => getQueryParams().id, []);

  const { delRoutetag } = useCloseAndJump();

  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };

  const formatParams = (values) => {
    const params: CreateUserFileRequest = {
      ...values,
      userType: 'BLACKLIST',
    };
    return params;
  };
  const handleCreate = async (params: CreateUserFileRequest) => {
    const result = await responseWithResultAsync({
      request: createUserFile,
      params,
    });
    return result;
  };

  const handleOk = () => {
    setState((state) => ({ ...state, loading: true })); // 设置 loading 为 true
    form.validateFields(async (err, values) => {
      if (err) {
        setState((state) => ({ ...state, loading: false })); // 设置 loading 为 false
        return;
      }
      const params: CreateUserFileRequest = formatParams(values);

      const result = await handleCreate(params);
      setState((state) => ({ ...state, loading: false })); // 设置 loading 为 false
      if (result) {
        message.success('操作成功');
        form.resetFields();
        // handleCancel();
      }
    });
  };

  // 创建防抖处理函数
  const debouncedHandleOk = debounce(handleOk, 300);

  return (
    <PageLayout className={styles['cooperation-report-contain']} routePath="/userBlack-add">
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="基本信息">
            <div className={styles.extra}>
              <BasicForm form={form} type={'create'} />
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          <Button type="primary" onClick={debouncedHandleOk} loading={modalState.loading}>
            保存
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
