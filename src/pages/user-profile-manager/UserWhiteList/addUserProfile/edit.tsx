import { useSetState } from 'ahooks';
import { Button, Icon, message, Spin } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useEffect, useMemo } from 'react';
import BasicForm from './BasicForm';

import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  createUserFile,
  CreateUserFileRequest,
  getUserFileInfo,
  editUserFile,
} from '../../services';

import moment from 'moment';
import styles from './index.module.less';

import { history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';

import { getResourceUrls } from '@/components/FileUpload/api';
import { debounce } from 'lodash';

type PropsType = FormComponentProps;
type ModalState = {
  loading?: boolean;
  detail?: any;
};

const AddSelectTabPage: React.FC<PropsType> = ({ form }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const id = useMemo(() => getQueryParams().id, []);
  const type = useMemo(() => getQueryParams().type as 'edit' | 'create', []);
  const { delRoutetag } = useCloseAndJump();

  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };

  const handleDetail = async () => {
    setState((state) => ({ ...state, loading: true }));
    const detail = await responseWithResultAsync({
      request: getUserFileInfo,
      params: { id },
    });

    setState((state) => ({ ...state, detail: detail!, loading: false }));

    if (detail) {
      initForm(detail);
    }
  };

  const formatParams = (values) => {
    const params: CreateUserFileRequest = {
      ...values,
      userType: 'WHITELIST',
    };
    return params;
  };
  const handleCreate = async (params: CreateUserFileRequest) => {
    const result = await responseWithResultAsync({
      request: createUserFile,
      params,
    });
    return result;
  };
  const handleUpdate = async (params) => {
    const result = await responseWithResultAsync({
      request: editUserFile,
      params: { ...params, id },
    });
    return result;
  };
  const handleOk = () => {
    setState((state) => ({ ...state, loading: true })); // 设置 loading 为 true
    form.validateFields(async (err, values) => {
      if (err) {
        setState((state) => ({ ...state, loading: false })); // 设置 loading 为 false
        return;
      }
      const params: CreateUserFileRequest = formatParams(values);

      const result = type === 'create' ? await handleCreate(params) : await handleUpdate(params);
      setState((state) => ({ ...state, loading: false })); // 设置 loading 为 false
      if (result) {
        message.success('操作成功');
        if (type === 'create') {
          form.resetFields();
          handleCancel();
        } else {
          handleCancel();
        }
      }
    });
  };

  // 创建防抖处理函数
  const debouncedHandleOk = debounce(handleOk, 300);

  const initForm = async (info: any) => {
    form.setFieldsValue({
      ...info,
    });
  };

  useEffect(() => {
    if (type === 'edit' && id) {
      handleDetail();
    }
  }, [type]);

  return (
    <PageLayout className={styles['cooperation-report-contain']} routePath="/userWhite-edit">
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="基本信息">
            <div className={styles.extra}>
              <BasicForm form={form} type={type} />
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          <Button type="primary" onClick={debouncedHandleOk} loading={modalState.loading}>
            保存
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
