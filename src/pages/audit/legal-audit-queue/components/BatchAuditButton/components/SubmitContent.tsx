import React, { useEffect, useImperativeHandle } from 'react';
import { Form, DatePicker, Input, Tag } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import ChooseRiskLevel from '@/pages/audit/legal-audit-workbench/components/SubmitModal/ChooseRiskLevel';
import moment from 'moment';

interface IProps extends FormComponentProps {
  [key: string]: any;
}

const SubmitContent = (props: IProps) => {
  const { form, qualification, onRef, step, saveStepValue } = props;
  const { getFieldDecorator } = form;
  const { riskLevel } = form.getFieldsValue();
  const getValue = () => {
    return new Promise((resolve, reject) => {
      form.validateFieldsAndScroll((err, value) => {
        if (err) {
          reject();
          return;
        }
        resolve(value);
      });
    });
  };
  useImperativeHandle(onRef, () => {
    return {
      getValue,
    };
  });
  // 做一些回显 时间是所有有效期中最小时间 审核意见是和各个意见整合
  useEffect(() => {
    if (step !== 1 && saveStepValue && riskLevel !== 'HIGH') {
      const { isSpecialMaterial, ...otherTime } = saveStepValue;
      if (!Object.keys(otherTime || {}).length) return;
      const times = Object.values(otherTime).filter((item) => !!item);
      const minTime = times?.length ? moment.min(times || []) : undefined;
      form.setFieldsValue({
        expirationDate: minTime,
      });
    }
    if (step === 1 || !qualification) return;
    const { SUPPLIER, BRAND, GOODS: SPU } = qualification;
    // formateTime();
    form.setFieldsValue({
      auditOpinion: `商家资质：${SUPPLIER?.auditState !== 'PASS' ? SUPPLIER?.auditOpinion : '合格'}
品牌资质：${BRAND?.auditState !== 'PASS' ? BRAND?.auditOpinion : '合格'}
商品资质：${SPU?.auditState !== 'PASS' ? SPU?.auditOpinion : '合格'}`,
    });
  }, [step, qualification, saveStepValue, riskLevel]);

  const handleSetDate = (days: number) => {
    const minTime = moment().add(days, 'days');
    form.setFieldsValue({
      expirationDate: minTime,
    });
  };
  return (
    <Form layout="vertical">
      <Form.Item label="风险等级" required>
        {getFieldDecorator('riskLevel', {
          rules: [
            {
              validator(_, val, cb) {
                if (!val) {
                  cb('请选择风险等级');
                  return;
                }
                cb();
              },
            },
          ],
        })(
          // @ts-ignore
          <ChooseRiskLevel qualification={qualification as any} qualificationResult={{} as any} />,
        )}
      </Form.Item>
      {riskLevel !== 'HIGH' && (
        <Form.Item
          label={
            <span>
              审核有效期
              <span className="ml-12 font-secondary color-font-neutral-black-4">
                有效期内，商品再次提交将免审通过
              </span>
            </span>
          }
        >
          {form.getFieldDecorator('expirationDate', {
            rules: [
              {
                required: riskLevel !== 'HIGH',
                message: '请选择审核有效期',
              },
            ],
          })(
            <DatePicker
              style={{ width: '100%' }}
              showToday={false}
              renderExtraFooter={() => {
                return (
                  <div>
                    <Tag
                      color="blue"
                      style={{ marginRight: '4px' }}
                      onClick={() => {
                        handleSetDate(30);
                      }}
                    >
                      30天
                    </Tag>
                    <Tag
                      color="blue"
                      style={{ marginRight: '4px' }}
                      onClick={() => {
                        handleSetDate(35);
                      }}
                    >
                      35天
                    </Tag>
                    <Tag
                      color="blue"
                      style={{ marginRight: '4px' }}
                      onClick={() => {
                        handleSetDate(90);
                      }}
                    >
                      90天
                    </Tag>
                    <Tag
                      color="blue"
                      style={{ marginRight: '4px' }}
                      onClick={() => {
                        handleSetDate(180);
                      }}
                    >
                      180天
                    </Tag>
                    <Tag
                      color="blue"
                      style={{ marginRight: '4px' }}
                      onClick={() => {
                        handleSetDate(365);
                      }}
                    >
                      365天
                    </Tag>
                  </div>
                );
              }}
            />,
          )}
        </Form.Item>
      )}
      <Form.Item label="审核意见">
        {getFieldDecorator('auditOpinion', {
          rules: [
            {
              required: riskLevel === 'HIGH',
              message: '请填写审核意见',
            },
          ],
        })(
          <Input.TextArea
            style={{ resize: 'none' }}
            rows={3}
            placeholder="最终审核意见"
            maxLength={500}
          />,
        )}
      </Form.Item>
    </Form>
  );
};

export default Form.create()(SubmitContent);
