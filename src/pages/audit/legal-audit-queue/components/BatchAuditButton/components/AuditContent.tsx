import React, { useEffect, useMemo, useState, useRef } from 'react';
import { Modal, Form, Row, Col, DatePicker, Checkbox, Radio, message, Spin, Tag } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import styles from '@/pages/choice-list-new/components/LegalCheckDrawer/index.module.less';
import moment from 'moment';
import AuditForm from '@/pages/audit/legal-audit-workbench/components/AuditForm';
import { useSetState } from 'ahooks';
import SubmitContent from './SubmitContent';
import {
  qualificationAuditDetail,
  QualificationAuditDetailResult,
  qualificationAuditSubmitBatch,
} from '../../../yml';
import { useRequest } from 'ahooks';
import { checkoutAudit, formatValue } from '../utils';

interface IProps extends FormComponentProps {
  [key: string]: any;
  visible: boolean;
  onCancel: any;
}

const initQualification = {
  SUPPLIER: {
    init: false,
    done: undefined,
    auditState: 'NONE',
    auditOpinion: undefined,
  },
  BRAND: {
    init: false,
    done: undefined,
    auditState: 'NONE',
    auditOpinion: undefined,
  },
  GOODS: {
    init: false,
    done: undefined,
    auditState: 'NONE',
    auditOpinion: undefined,
  },
};

const AuditContent = (props: IProps) => {
  const { visible, onCancel, form, selectedRows, selectedRowKeys, handleOnConfirm } = props;
  const [step, setStep] = useState<number>(1);
  const { getFieldDecorator } = form;
  const [saveStepValue, setSaveStepValue] = useState<any>({});
  const SubmitContentRef = useRef(null);
  const [qualification, setQualification] = useSetState(initQualification);
  const [firstItem, setFirstItem] = useState<QualificationAuditDetailResult>({});

  // 获取详情
  const { run: detailRun, loading: detailLoading } = useRequest(qualificationAuditDetail, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        setFirstItem(res?.result || {});
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  // 提交批量审核
  const { run: submitRun, loading: submitLoading } = useRequest(qualificationAuditSubmitBatch, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        // 存id
        handleOnConfirm?.(res?.result);
        setStep(1);
        setQualification(initQualification);
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const handleForm = () => {
    form.validateFieldsAndScroll((err, value) => {
      if (err) {
        return;
      }
      // 判断资质是否审核没有审核进行提示
      if (!checkoutAudit(qualification)) {
        message.warning('请完善资质审核');
        return;
      }

      setSaveStepValue(value);
      setStep(step + 1);
    });
  };

  const handleSubmit = () => {
    //@ts-ignore
    SubmitContentRef?.current?.getValue().then((res) => {
      const value = formatValue(saveStepValue, qualification);
      const params = {
        ...res,
        ...value,
        ids: selectedRowKeys,
        expirationDate: res?.expirationDate
          ? moment(res?.expirationDate).format('YYYY-MM-DD')
          : undefined,
      };
      submitRun(params);
    });
  };

  const handleConfirm = () => {
    if (step === 1) {
      // setStep(step + 1);
      handleForm();
      return;
    }
    handleSubmit();
  };
  useEffect(() => {
    if (selectedRows?.length) {
      const id = selectedRows[0]?.id;
      detailRun({ id });
    }
  }, [selectedRows]);
  const handleOnCancel = () => {
    if (submitLoading) return;
    if (step === 1) {
      onCancel?.();
      setQualification(initQualification);
      return;
    }
    // 在第二步的时候点取消回显
    setStep(1);
    // console.log(saveStepValue, '--------->');
    setTimeout(() => {
      form.setFieldsValue(saveStepValue || {});
    }, 0);
    // const { expirationDates, isSpecialMaterial } = saveStepValue || {};
  };

  return (
    <Modal
      title="批量审核"
      width={500}
      onCancel={handleOnCancel}
      visible={visible}
      onOk={handleConfirm}
      destroyOnClose={true}
      maskClosable={false}
      confirmLoading={submitLoading}
    >
      <Spin spinning={detailLoading}>
        {step === 1 ? (
          <Form>
            <div
              style={{
                height: '500px',
                overflowY: 'auto',
                overflowX: 'hidden',
                scrollbarWidth: 'none',
              }}
            >
              <h3 style={{ color: '#444', fontWeight: 'bold', marginBottom: '6px' }}>商家资质</h3>
              <div className={styles['legal-line']}>
                <div
                  className={styles['legal-line-title']}
                  style={{ width: '108px', display: 'flex', justifyContent: 'flex-end' }}
                >
                  <span style={{ color: 'red' }}>*</span>商家主体名称：
                </div>
                <div className={styles['legal-detail']}>{firstItem?.supplierName || '-'}</div>
              </div>
              <div className={styles['legal-line']} style={{ alignItems: 'center' }}>
                <div
                  className={styles['legal-line-title']}
                  style={{ width: '108px', display: 'flex', justifyContent: 'flex-end' }}
                >
                  营业执照有效期：
                </div>
                <Row
                  className={styles['legal-detail']}
                  style={{ width: '100%', display: 'flex', alignItems: 'center' }}
                  gutter={24}
                >
                  <Col span={17}>
                    <Form.Item style={{ marginBottom: '0' }}>
                      {getFieldDecorator('BUSINESS_LICENSE')(
                        <DatePicker
                          disabled={
                            form?.getFieldValue('BUSINESS_LICENSE') &&
                            moment(form?.getFieldValue('BUSINESS_LICENSE')).isSame(
                              moment(32503564800000),
                            )
                          }
                          style={{ width: '100%' }}
                        />,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={7} style={{ paddingLeft: '0px' }}>
                    <Checkbox
                      onChange={(e: any) => {
                        const { checked } = e?.target;
                        form?.setFieldsValue({
                          BUSINESS_LICENSE: checked ? moment(32503564800000) : undefined,
                        });
                      }}
                      checked={
                        form?.getFieldValue('BUSINESS_LICENSE') &&
                        moment(form?.getFieldValue('BUSINESS_LICENSE')).isSame(
                          moment(32503564800000),
                        )
                      }
                    >
                      长期有效
                    </Checkbox>
                  </Col>
                </Row>
              </div>
              <div style={{ paddingBottom: '16px' }}>
                <AuditForm
                  value={qualification['SUPPLIER'] as any}
                  type="info"
                  onChange={(val: any) => {
                    setQualification({ SUPPLIER: val });
                  }}
                  style={{ width: '100%' }}
                />
              </div>
              <h3 style={{ color: '#444', fontWeight: 'bold', marginBottom: '6px' }}>品牌资质</h3>
              <div className={styles['legal-line']}>
                <div
                  className={styles['legal-line-title']}
                  style={{ width: '108px', display: 'flex', justifyContent: 'flex-end' }}
                >
                  <span style={{ color: 'red' }}>*</span>品牌名称：
                </div>
                <div className={styles['legal-detail']}>{firstItem?.brandName || '-'}</div>
              </div>
              <div className={styles['legal-line']} style={{ alignItems: 'center' }}>
                <div
                  className={styles['legal-line-title']}
                  style={{ width: '108px', display: 'flex', justifyContent: 'flex-end' }}
                >
                  有效期至：
                </div>
                <Row
                  className={styles['legal-detail']}
                  style={{ width: '100%', display: 'flex', alignItems: 'center' }}
                  gutter={24}
                >
                  <Col span={17}>
                    <Form.Item style={{ marginBottom: '0' }}>
                      {getFieldDecorator('BRAND_REGISTRATION')(
                        <DatePicker
                          disabled={
                            form?.getFieldValue('BRAND_REGISTRATION') &&
                            moment(form?.getFieldValue('BRAND_REGISTRATION')).isSame(
                              moment(32503564800000),
                            )
                          }
                          style={{ width: '100%' }}
                        />,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={7} style={{ paddingLeft: '0px' }}>
                    <Checkbox
                      onChange={(e: any) => {
                        const { checked } = e?.target;
                        form?.setFieldsValue({
                          BRAND_REGISTRATION: checked ? moment(32503564800000) : undefined,
                        });
                      }}
                      checked={
                        form?.getFieldValue('BRAND_REGISTRATION') &&
                        moment(form?.getFieldValue('BRAND_REGISTRATION')).isSame(
                          moment(32503564800000),
                        )
                      }
                    >
                      长期有效
                    </Checkbox>
                  </Col>
                </Row>
              </div>
              <div className={styles['legal-line']} style={{ alignItems: 'center' }}>
                <div
                  className={styles['legal-line-title']}
                  style={{ width: '108px', display: 'flex', justifyContent: 'flex-end' }}
                >
                  授权链路有效期至：
                </div>
                <Row
                  className={styles['legal-detail']}
                  style={{ width: '100%', display: 'flex', alignItems: 'center' }}
                  gutter={24}
                >
                  <Col span={17}>
                    <Form.Item style={{ marginBottom: '0' }}>
                      {getFieldDecorator('BRAND_SUPPLIER_QUALIFICATION')(
                        <DatePicker
                          disabled={
                            form?.getFieldValue('BRAND_SUPPLIER_QUALIFICATION') &&
                            moment(form?.getFieldValue('BRAND_SUPPLIER_QUALIFICATION')).isSame(
                              moment(32503564800000),
                            )
                          }
                          style={{ width: '100%' }}
                        />,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={7} style={{ paddingLeft: '0px' }}>
                    <Checkbox
                      onChange={(e: any) => {
                        const { checked } = e?.target;
                        form?.setFieldsValue({
                          BRAND_SUPPLIER_QUALIFICATION: checked
                            ? moment(32503564800000)
                            : undefined,
                        });
                      }}
                      checked={
                        form?.getFieldValue('BRAND_SUPPLIER_QUALIFICATION') &&
                        moment(form?.getFieldValue('BRAND_SUPPLIER_QUALIFICATION')).isSame(
                          moment(32503564800000),
                        )
                      }
                    >
                      长期有效
                    </Checkbox>
                  </Col>
                </Row>
              </div>
              <div style={{ paddingBottom: '16px' }}>
                <AuditForm
                  value={qualification['BRAND'] as any}
                  type="info"
                  onChange={(val: any) => {
                    setQualification({ BRAND: val });
                  }}
                  style={{ width: '100%' }}
                />
              </div>
              <h3 style={{ color: '#444', fontWeight: 'bold', marginBottom: '6px' }}>商品资质</h3>
              <div className={styles['legal-line']} style={{ alignItems: 'center' }}>
                <div
                  className={styles['legal-line-title']}
                  style={{ width: '108px', display: 'flex', justifyContent: 'flex-end' }}
                >
                  <span style={{ color: 'red' }}>*</span>特殊材质：
                </div>
                <Form.Item style={{ marginBottom: '0px' }}>
                  {form.getFieldDecorator('isSpecialMaterial', {
                    rules: [{ required: true, message: '请选择是否为特殊材质' }],
                  })(
                    <Radio.Group style={{ marginTop: '6px' }}>
                      <Radio value={1}>是</Radio>
                      <Radio value={0}>否</Radio>
                    </Radio.Group>,
                  )}
                </Form.Item>
              </div>
              <div style={{ paddingBottom: '16px' }}>
                <AuditForm
                  value={qualification['GOODS'] as any}
                  type="info"
                  onChange={(val: any) => {
                    setQualification({ GOODS: val });
                  }}
                  style={{ width: '100%' }}
                />
              </div>
            </div>
          </Form>
        ) : (
          <SubmitContent
            qualification={qualification}
            onRef={SubmitContentRef}
            step={step}
            saveStepValue={saveStepValue}
          ></SubmitContent>
        )}
      </Spin>
    </Modal>
  );
};

export default Form.create()(AuditContent);
