import React from 'react';
import WithToggleModal from '@/components/WithToggleModal';
import { Modal, Button } from 'antd';
import style from '@/styles/index.module.less';
import { ModalProps } from 'antd/lib/modal';

interface IProps extends ModalProps {
  // onRefresh: any;
  thoughtProcess?: string;
  [key: string]: any;
}

const ThoughtProcess = (props: IProps) => {
  const { visible, thoughtProcess, ...rest } = props;
  return (
    <Modal
      title="思考过程"
      {...rest}
      visible={visible}
      width={888}
      className={style['modal-sty']}
      footer={<Button onClick={rest?.onCancel}>关闭</Button>}
    >
      <div style={{ height: '500px', overflowY: 'auto', wordBreak: 'break-all' }}>
        {/* {thoughtProcess || '-'} */}
        <div
          dangerouslySetInnerHTML={{
            __html: thoughtProcess || '',
          }}
        />
      </div>
    </Modal>
  );
};

export default WithToggleModal(ThoughtProcess);
