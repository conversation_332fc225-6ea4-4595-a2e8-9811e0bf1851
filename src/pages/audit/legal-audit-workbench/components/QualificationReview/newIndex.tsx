import React, {
  useMemo,
  useState,
  useRef,
  useEffect,
  useCallback,
  useImperativeHandle,
} from 'react';
import { DescriptionsProxy } from 'web-common-modules/components';
import { defaultRender } from '@/utils/string';
import styles from './index.module.less';
import AuditForm from '../AuditForm';
import { QualificationItemType, QUALIFICATION_AUDIT_STATE } from '../../constant';
import { GetSelectSpuDetailResult } from '@/services/yml/legal-audit';
import { QualificationTypeEnum, QualificationKeyEnum } from '@/common/constants/audit/legal-audit';
import { getIdCardName } from '@/common/constants/audit/utils';
import {
  Timeline,
  Checkbox,
  DatePicker,
  Form,
  Icon,
  Tag,
  Radio,
  Row,
  Col,
  Popover,
  Button,
  message,
  Spin,
} from 'antd';
import {
  allRiskLevelColor,
  RISK_LEVEL_TEXT,
  QUALIFICATION_AUDIT_STATE_COLOR,
  QUALIFICATION_AUDIT_STATE_ICON,
} from '../../../legal-audit-queue/utils/getRiskLevel';
import { classNames } from '@/utils/moduleUtils';
import {
  AUDIT_PROJECT_CONFIG,
  QUALIFICATION_TYPE,
  QUALIFICATION_TYPE_MAP,
  CHANGE_VALUE,
} from './const';
import {
  getAuditOptions,
  getAuditStatusResult,
  getQualificationNum,
  getQualificationfilterNum,
  hasQualication,
  renderQualification,
  hasFileQualication,
  checkExpirationDate,
} from './utils';
import { WrappedFormUtils } from 'antd/es/form/Form';
import moment, { Moment } from 'moment';
import { DetailTitle } from '@/components/index';
import Shops from '@/pages/choice-list-new/components/LegalCheckDrawer/Shops';
import Brand from '@/pages/choice-list-new/components/LegalCheckDrawer/Brand';
import style from '@/pages/choice-list-new/components/LegalCheckDrawer/index.module.less';
import Commodity from '@/pages/choice-list-new/components/LegalCheckDrawer/Commodity';
import {
  QualificationAuditLockResult,
  SupplierBodySpecialAuditLatestResult,
} from '@/pages/audit/legal-audit-queue/yml';
import Item from 'antd/lib/list/Item';
import autoIcon from '@/assets/autoIcon.png';
import autoSucIcon from '@/assets/autoIconSuc.png';
import autoUnIcon from '@/assets/autoIconUn.png';
import {
  RISK_LEVEL_COLOR,
  RISK_LEVEL_ENUM,
  RISK_LEVEL_NAME,
} from '@/pages/certification-audit/types';
import AuditDetail from '../AuditDetail';
import { useRequest, useSetState } from 'ahooks';
import {
  detailToSupplierId,
  goodsQualificationLatestDetail,
  goodsQualificationRulesResult,
  rulesResult,
} from '../../yml/index';
// import {
//   QualificationAuditLockResult,
//   SupplierBodySpecialAuditLatestResult,
// } from '@/pages/audit/legal-audit-queue/yml';
// import Item from 'antd/lib/list/Item';
import { brandQualificationLatestDetail } from '@/pages/certification-audit/service';
import { BrandAuditDetail } from '@/pages/certification-audit/components';
import { useBrandAudit } from '../../hooks';
import GoodsAuditDetail from '@/pages/certification-audit/components/GoodsAuditDetail';
import TrademarkInformation from '../TrademarkInformation';
import stylesAuditForm from '../AuditForm/index.module.less';
import { AI_STATUS_ENUM, AI_STATUS_NAME, AI_STATUS_COLOR } from './const';
import {
  regenerateAiAuditOrGetResult,
  RegenerateAiAuditOrGetResultResult,
} from '../../yml/resubmit';
import { LEVEL, LEVEL_NAME, LEVEL_COLOR } from '../../../../../../web_modules/types';
import ThoughtProcess from './thoughtProcess';

// const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 3 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};

interface IProps {
  // qualificationResult: Record<QualificationKeyEnum, QualificationItemType>;
  // selectSpuData: GetSelectSpuDetailResult;
  // qualificationReuseData: QualificationReuseInfo;
  // updateQualification: (val: QualificationItemType & { key: QualificationKeyEnum }) => void;
  form: WrappedFormUtils;
  qualification: QUALIFICATION_TYPE | null;
  qualificationMap: QUALIFICATION_TYPE_MAP | null;
  handleQualification?: (
    value: CHANGE_VALUE | { expirationDate: string | undefined },
    key: QualificationKeyEnum,
  ) => void;
  brandWhite?: boolean;
  current: QualificationAuditLockResult;
  visible: boolean;
  onRef: any;
  supplierBodyDetail?: SupplierBodySpecialAuditLatestResult;
  historyList?: [];
  checkJumpDetail?: (value: any) => void;
  isHistory?: boolean;
  isDetail: boolean;
}

const QualificationReview: React.FC<IProps> = ({
  // qualificationResult,
  // selectSpuData,
  // updateQualification,
  // qualificationReuseData,
  form,
  qualification,
  qualificationMap,
  handleQualification,
  brandWhite,
  supplierBodyDetail,
  historyList,
  checkJumpDetail,
  isHistory = true,
  isDetail,
  current,
  visible,
  onRef,
}) => {
  // console.log(
  //   '🚀 ~ qualification:',
  //   form,
  //   qualification,
  //   qualificationMap,
  //   handleQualification,
  //   brandWhite,
  //   supplierBodyDetail,
  //   historyList,
  // );
  // console.log('🚀 ~ file: index.tsx:72 ~ brandWhite:', brandWhite);
  const [activeTab, setActiveTab] = useState<QualificationKeyEnum>(QualificationKeyEnum.SUPPLIER);

  const SupplierRef = useRef();
  const BrandRef = useRef();
  const SpuRef = useRef();

  const [autoCheck, setAutoCheck] = useState<string[]>([]);
  const [supplierId, setSupplierId] = useState<any>();
  // 写麻烦了不改了
  const [auditResult, setAuditResult] = useState<any>();
  const [auditResultEnum, setAuditResultEnum] = useState<any>();
  const [auditResultUn, setAuditResultUn] = useState<any>();
  const [auditResultId, setAuditResultId] = useState<any>();

  const [autoList, setAutoList] = useState<any>([]);
  const [autoSucList, setAutoSucList] = useState<boolean>(false);
  const [transformShop, setTransformShop] = useState(false);

  const [AIAudit, setAIAudit] = useSetState<RegenerateAiAuditOrGetResultResult>({
    brandLinkAiHandleResult: '',
    brandLinkAiHandleState: undefined,
    brandLinkAiThoughtProcessResult: undefined,
  });

  const { run: regenerateAiAuditOrGetResultRun, loading: regenerateAiAuditOrGetResultLoading } =
    useRequest(regenerateAiAuditOrGetResult, {
      manual: true,
      onSuccess({ res }) {
        setTransformShop(false);
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        const result = res?.result || {};
        setAIAudit({
          brandLinkAiHandleResult: result?.brandLinkAiHandleResult,
          brandLinkAiHandleState: result?.brandLinkAiHandleState,
          brandLinkAiThoughtProcessResult: result?.brandLinkAiThoughtProcessResult,
        });
        if (
          current?.isUseAiMark &&
          !result?.hasCompleteAuthorization &&
          !isDetail &&
          qualification
        ) {
          handleQualification?.(
            {
              ...qualification['BRAND'],
              auditState: 'NO_PASS' as QUALIFICATION_AUDIT_STATE,
              done: !!qualification['BRAND']?.auditOpinion,
            },
            'BRAND',
          );
        }
      },
    });

  const {
    brandAuditRun,
    brandAuditLoading,
    brandAudit,
    brandRulesLoading,
    brandRulesRun,
    setBrandAudit,
    brandParams,
  } = useBrandAudit(current);

  const [goodsAuditResult, setGoodsAuditResult] = useState<string>();
  const [goodsAuditResultId, setGoodsAuditResultId] = useState<any>();
  const [goodsAuditResultEnum, setGoodsAuditResultEnum] = useState<any>();
  const [goodsAutoCheck, setGoodsAutoCheck] = useState<string[]>([]);
  const [goodsAuditResultUn, setGoodsAuditResultUn] = useState<any>();
  const [goodsAutoList, setGoodsAutoList] = useState<any>([]);
  const [goodsAutoSucList, setGoodsAutoSucList] = useState<boolean>(false);

  const { run: getRun, loading: getLoading } = useRequest(detailToSupplierId, {
    manual: true,
    onSuccess({ res }) {
      if (res.success) {
        const result = res?.result || {};
        setAuditResult(
          result?.qualificationSnapshot?.auditResult === 'PASS'
            ? '合格'
            : result?.qualificationSnapshot?.auditResult === 'NO_PASS'
            ? !!supplierBodyDetail
              ? '合格-主体特批'
              : '不合格'
            : result?.qualificationSnapshot?.auditResult === 'REVIEW_PENDING'
            ? '待复核'
            : '-',
        );
        setAuditResultId(result?.id);
        setAuditResultEnum(result?.qualificationSnapshot?.auditResult);
        setAuditResultUn(result?.auditRulesDetail);
        // setAutoList(result?.automatedAuditRulesResults || []);
        result?.id && rulesRun({ id: result?.id });
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const { run: getGoodsRun, loading: getGoodsLoading } = useRequest(
    goodsQualificationLatestDetail,
    {
      manual: true,
      onSuccess({ res }) {
        if (res.success) {
          const result = res?.result || {};
          setGoodsAuditResult(
            result?.qualificationSnapshot?.auditResult === 'PASS'
              ? '合格'
              : result?.qualificationSnapshot?.auditResult === 'NO_PASS'
              ? '不合格'
              : result?.qualificationSnapshot?.auditResult === 'REVIEW_PENDING'
              ? '待复核'
              : undefined,
          );
          setGoodsAuditResultId(result?.id);
          setGoodsAuditResultEnum(result?.qualificationSnapshot?.auditResult);
          setGoodsAuditResultUn(result?.automatedAuditRulesResults);
          // setAuditResultUn(result?.existValidAuditResult);
          // setAutoList(result?.automatedAuditRulesResults || []);
          result?.id && goodsRulesRun({ id: result?.id });
        } else {
          message.warning(res?.message || '网络异常');
        }
      },
    },
  );

  const { run: rulesRun, loading: rulesLoading } = useRequest(rulesResult, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        const list = res?.result?.filter((item) => item.riskLevel !== RISK_LEVEL_ENUM.PASS);
        const suc =
          !!res?.result?.length &&
          res?.result?.every((item) => item.riskLevel === RISK_LEVEL_ENUM.PASS);
        setAutoSucList(suc);
        setAutoList(list || []);
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const { run: goodsRulesRun, loading: goodsRulesLoading } = useRequest(
    goodsQualificationRulesResult,
    {
      manual: true,
      onSuccess({ res }) {
        if (res?.success) {
          const list = res?.result?.filter((item) => item.riskLevel !== RISK_LEVEL_ENUM.PASS);
          const suc =
            !!res?.result?.length &&
            res?.result?.every((item) => item.riskLevel === RISK_LEVEL_ENUM.PASS);
          setGoodsAutoSucList(suc);
          setGoodsAutoList(list || []);
        } else {
          message.warning(res?.message || '网络异常');
        }
      },
    },
  );

  useEffect(() => {
    if (visible && current) {
      const supplierQualification = current?.qualification?.supplierQualification || {};
      supplierQualification && getRun({ id: supplierQualification.supplierId });
      getGoodsRun({
        brandId: current.brandId,
        shopId: current?.qualification?.brandQualification?.shopId,
        specVersion: current.specVersion,
        spuNo: current.spuNo,
        supplierId: current?.qualification?.supplierQualification?.supplierId,
      });
      setSupplierId(supplierQualification.supplierId);
      setAutoCheck([]);
      setBrandAudit({
        autoCheck: [],
      });
      const spuNo = current?.spuNo;
      const specVersion = current?.specVersion;
      const brandQualification = current?.qualification?.brandQualification || {};
      spuNo &&
        brandAuditRun({
          spuNo,
          specVersion,
          supplierId: supplierQualification.supplierId,
          brandId: brandQualification?.brandId,
          shopId: brandQualification?.shopId,
        });
      setGoodsAutoCheck([]);
      current?.id && regenerateAiAuditOrGetResultRun({ id: current?.id, isRegenerate: false });
    } else {
      setAutoList([]);
      setAutoSucList(false);
      setBrandAudit({
        autoList: [],
        autoSucList: false,
      });
      setGoodsAutoList([]);
      setGoodsAutoSucList(false);
    }
  }, [visible, current]);

  const mapRef = {
    [QualificationKeyEnum.SUPPLIER]: SupplierRef,
    [QualificationKeyEnum.BRAND]: BrandRef,
    [QualificationKeyEnum.SPU]: SpuRef,
    // [QualificationKeyEnum.SHOP]: ShopRef,
  };

  const onChangeTabKey = (key: QualificationKeyEnum) => {
    setActiveTab(key);

    // document.getElementById(key).scrollIntoView()
    // window.scrollBy(0, -155);
    // document.getElementById('aaaa').scrollTo({
    //   top: 1000,
    //   behavior: 'smooth'
    // })

    // mapRef[key]?.current.scrollBy(0, 200);
    mapRef[key]?.current.scrollIntoView({
      behavior: 'smooth',
      // block: 'start',
      block: 'center',
    });
  };

  // 没有资质审核
  if (!qualification) {
    return <></>;
  }

  const initialValue_BRAND_SUPPLIER_QUALIFICATION = useMemo(() => {
    return qualification['BRAND']?.timeMap
      ? qualification['BRAND']?.timeMap['BRAND_SUPPLIER_QUALIFICATION']
      : undefined;
  }, [qualification]);

  const checkChange = (value: string) => {
    if (autoCheck.includes(value)) {
      setAutoCheck(autoCheck.filter((item) => item !== value));
    } else {
      setAutoCheck([...autoCheck, value]);
    }
  };
  const brandCheckChange = (value: string) => {
    if (brandAudit?.autoCheck.includes(value)) {
      // setAutoCheck(autoCheck.filter((item) => item !== value));
      setBrandAudit({
        autoCheck: brandAudit?.autoCheck.filter((item) => item !== value),
      });
    } else {
      // setAutoCheck([...autoCheck, value]);
      setBrandAudit({
        autoCheck: [...(brandAudit.autoCheck || []), value],
      });
    }
  };
  const checkGoodsChange = (value: string) => {
    if (goodsAutoCheck.includes(value)) {
      setGoodsAutoCheck(goodsAutoCheck.filter((item) => item !== value));
    } else {
      setGoodsAutoCheck([...goodsAutoCheck, value]);
    }
  };

  const handleAddAuto = () => {
    if (!autoCheck?.length) {
      message.warning('请选择审核意见');
      return;
    }
    const qSupplier = { ...(qualification['SUPPLIER'] || {}) };
    const auditOpinion = qSupplier.auditOpinion
      ? qSupplier.auditOpinion.replaceAll(';', '').split('\n')
      : [];

    const valueList = [...new Set([...auditOpinion, ...autoCheck])];

    const saveIndex: any = [];
    valueList.forEach((item, index) => {
      const autoValue = autoList.some((aItem: any) => aItem.automatedAuditRulesResult === item);
      if (autoValue && !autoCheck?.includes(item)) {
        // 代表删除
        saveIndex.push(index);
      }
    });
    saveIndex.forEach((item: number) => {
      valueList.splice(Number(item), 1);
    });
    // console.log('🚀 ~ handleAddAuto ~ valueList:', valueList.join(';\n'), saveIndex);
    const curSUPPLIER = {
      auditOpinion: valueList.join(';\n'),
    };
    // console.log('🚀 ~ handleAddAuto ~ curSUPPLIER:', curSUPPLIER);
    handleQualification?.(curSUPPLIER, 'SUPPLIER');
  };
  const handleGoodsAddAuto = () => {
    if (!goodsAutoCheck?.length) {
      message.warning('请选择审核意见');
      return;
    }
    const qSupplier = { ...(qualification['SPU'] || {}) };
    const auditOpinion = qSupplier.auditOpinion
      ? qSupplier.auditOpinion.replaceAll(';', '').split('\n')
      : [];

    const valueList = [...new Set([...auditOpinion, ...goodsAutoCheck])];

    const saveIndex: any = [];
    valueList.forEach((item, index) => {
      const autoValue = goodsAutoList.some(
        (aItem: any) => aItem.automatedAuditRulesResult === item,
      );
      if (autoValue && !goodsAutoCheck?.includes(item)) {
        // 代表删除
        saveIndex.push(index);
      }
    });
    saveIndex.forEach((item: number) => {
      valueList.splice(Number(item), 1);
    });
    // console.log('🚀 ~ handleAddAuto ~ valueList:', valueList.join(';\n'), saveIndex);
    const curSUPPLIER = {
      auditOpinion: valueList.join(';\n'),
    };
    // console.log('🚀 ~ handleAddAuto ~ curSUPPLIER:', curSUPPLIER);
    handleQualification?.(curSUPPLIER, 'SPU');
  };

  const brandhandleAddAuto = () => {
    if (!brandAudit?.autoCheck?.length) {
      message.warning('请选择审核意见');
      return;
    }
    const qSupplier = { ...(qualification['BRAND'] || {}) };
    const auditOpinion = qSupplier.auditOpinion
      ? qSupplier.auditOpinion.replaceAll(';', '').split('\n')
      : [];

    const valueList = [...new Set([...auditOpinion, ...(brandAudit.autoCheck || [])])];

    const saveIndex: any = [];
    valueList.forEach((item, index) => {
      const autoValue = brandAudit?.autoList.some(
        (aItem: any) => aItem.automatedAuditRulesResult === item,
      );
      if (autoValue && !brandAudit?.autoCheck?.includes(item)) {
        // 代表删除
        saveIndex.push(index);
      }
    });
    saveIndex.forEach((item: number) => {
      valueList.splice(Number(item), 1);
    });
    // console.log('🚀 ~ handleAddAuto ~ valueList:', valueList.join(';\n'), saveIndex);
    const curSUPPLIER = {
      auditOpinion: valueList.join(';\n'),
    };
    // console.log('🚀 ~ handleAddAuto ~ curSUPPLIER:', curSUPPLIER);
    handleQualification(curSUPPLIER, 'BRAND');
  };

  const handleRefresh = useCallback(() => {
    supplierId && getRun({ id: supplierId });
  }, [supplierId]);
  const handleBrandRefresh = useCallback(() => {
    brandAuditRun(brandParams);
  }, [brandParams]);
  const handleGoodsRefresh = useCallback(() => {
    current &&
      getGoodsRun({
        brandId: current.brandId,
        shopId: current?.qualification?.brandQualification?.shopId,
        specVersion: current.specVersion,
        spuNo: current.spuNo,
        supplierId: current?.qualification?.supplierQualification?.supplierId,
      });
  }, [current]);

  const autoPage = (
    <Spin spinning={rulesLoading || getLoading}>
      <div className={styles.autoPage}>
        <ul className={styles.checkBox}>
          {autoList?.map((item: any, index: number) => (
            <li className={styles.checkItem} key={index}>
              <Checkbox
                checked={autoCheck.includes(item.automatedAuditRulesResult)}
                onChange={() => {
                  checkChange(item.automatedAuditRulesResult);
                }}
              >
                <Tag color={RISK_LEVEL_COLOR[item.riskLevel as RISK_LEVEL_ENUM]}>
                  {RISK_LEVEL_NAME[item.riskLevel as RISK_LEVEL_ENUM]}
                </Tag>
                {item.automatedAuditRulesResult}
              </Checkbox>
            </li>
          ))}
        </ul>
        <Button
          type="primary"
          style={{ marginTop: '8px', marginLeft: '20px' }}
          size="small"
          onClick={handleAddAuto}
        >
          快速填入
        </Button>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <AuditDetail id={supplierId} onRefresh={handleRefresh}>
            <Button type="link">查看完整结果</Button>
          </AuditDetail>
        </div>
      </div>
    </Spin>
  );
  const brandAutoPage = (
    <Spin spinning={brandAuditLoading || brandRulesLoading}>
      <div className={styles.autoPage}>
        <ul className={styles.checkBox}>
          {brandAudit?.autoList?.map((item: any, index: number) => (
            <li className={styles.checkItem} key={index}>
              <Checkbox
                checked={brandAudit?.autoCheck.includes(item.automatedAuditRulesResult)}
                onChange={() => {
                  brandCheckChange(item.automatedAuditRulesResult);
                }}
              >
                <Tag color={RISK_LEVEL_COLOR[item.riskLevel as RISK_LEVEL_ENUM]}>
                  {RISK_LEVEL_NAME[item.riskLevel as RISK_LEVEL_ENUM]}
                </Tag>
                {item.automatedAuditRulesResult}
              </Checkbox>
            </li>
          ))}
        </ul>
        <Button
          type="primary"
          style={{ marginTop: '8px', marginLeft: '20px' }}
          size="small"
          onClick={brandhandleAddAuto}
        >
          快速填入
        </Button>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <BrandAuditDetail type="queues" apiParams={brandParams} onRefresh={handleBrandRefresh}>
            <Button type="link">查看完整结果</Button>
          </BrandAuditDetail>
        </div>
      </div>
    </Spin>
  );
  const goodsAutoPage = (
    <Spin spinning={goodsRulesLoading || getGoodsLoading}>
      <div className={styles.autoPage}>
        <ul className={styles.checkBox}>
          {goodsAutoList?.map((item: any, index: number) => (
            <li className={styles.checkItem} key={index}>
              <Checkbox
                checked={goodsAutoCheck.includes(item.automatedAuditRulesResult)}
                onChange={() => {
                  checkGoodsChange(item.automatedAuditRulesResult);
                }}
              >
                <Tag color={RISK_LEVEL_COLOR[item.riskLevel as RISK_LEVEL_ENUM]}>
                  {RISK_LEVEL_NAME[item.riskLevel as RISK_LEVEL_ENUM]}
                </Tag>
                {item.automatedAuditRulesResult}
              </Checkbox>
            </li>
          ))}
        </ul>
        <Button
          type="primary"
          style={{ marginTop: '8px', marginLeft: '20px' }}
          size="small"
          onClick={handleGoodsAddAuto}
        >
          快速填入
        </Button>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <GoodsAuditDetail id={goodsAuditResultId} onRefresh={handleGoodsRefresh}>
            <Button type="link">查看完整结果</Button>
          </GoodsAuditDetail>
        </div>
      </div>
    </Spin>
  );

  useImperativeHandle(onRef, () => {
    return {
      getAudit() {
        return {
          automatedAuditResult: auditResultEnum,
          automatedSnapshotId: auditResultId,
          brandAutomatedAuditResult: brandAudit?.auditResultEnum,
          brandAutomatedSnapshotId: brandAudit?.auditResultId,
          goodsAutoAuditResult: goodsAuditResultEnum,
          goodsSnapshotId: goodsAuditResultId,
        };
      },
    };
  });

  // 立即审核并且没有上次审核结果并且自动化审核结果是合格的自动打标
  useEffect(() => {
    if (
      qualification &&
      qualification['SUPPLIER']?.auditState === 'NONE' &&
      (auditResult === '合格' || auditResult === '不合格') &&
      !isDetail
    ) {
      handleQualification?.(
        {
          ...qualification['SUPPLIER'],
          auditState:
            auditResult === '合格'
              ? ('PASS' as QUALIFICATION_AUDIT_STATE)
              : ('NO_PASS' as QUALIFICATION_AUDIT_STATE),
          done: auditResult === '合格' ? true : false,
        },
        'SUPPLIER',
      );
    }
  }, [qualification, auditResult, isDetail]);

  const GoodsAuditIcon = useMemo(
    () =>
      goodsAuditResultEnum === 'PASS'
        ? autoSucIcon
        : !goodsAuditResultEnum || goodsAuditResultEnum === 'WAIT'
        ? autoUnIcon
        : autoIcon,
    [goodsAuditResultEnum],
  );

  const handleAgain = () => {
    regenerateAiAuditOrGetResultRun({ id: current?.id, isRegenerate: true });
  };

  // 刷新AI结果
  const handleRefreshAiResult = () => {
    setTransformShop(true);
    regenerateAiAuditOrGetResultRun({ id: current?.id });
  };

  return (
    <div
      className={classNames({
        [styles.container]: true,
        flex: true,
      })}
      style={{ justifyItems: 'flex-start' }}
    >
      <div>
        <div
          style={{ background: '#ffffff', padding: '8px', marginRight: '16px', flexShrink: 0 }}
          className={`${styles.timeLine}`}
        >
          <Timeline style={{ width: 258 }} className={`mt-16`}>
            {AUDIT_PROJECT_CONFIG.map((item, index) => (
              <Timeline.Item
                key={index}
                color={activeTab === item.sign ? 'blue' : 'gray'}
                className={classNames({ [styles.active]: activeTab === item.sign })}
              >
                <div
                  className={classNames({
                    'pt-12 pb-12 pl-8 pr-8 cursor-pointer': true,
                    'color-bg-primary-light3': activeTab === item.sign,
                  })}
                  onClick={() => onChangeTabKey(item.sign)}
                >
                  <span className="font-title-4">{item.title}</span>
                  <span
                    className={classNames({
                      'ml-2': true,
                      'color-font-neutral-black-4': activeTab !== item.sign,
                    })}
                  >
                    {getQualificationfilterNum(qualification as QUALIFICATION_TYPE, item.sign)}
                  </span>

                  <span className="ml-4">
                    {getAuditStatusResult(qualification as QUALIFICATION_TYPE, item.sign)}
                  </span>
                </div>

                {getAuditOptions(qualification as QUALIFICATION_TYPE, item.sign) && (
                  <div className="font-secondary mt-4">
                    <p className="color-font-neutral-black-5">审核意见：</p>
                    <div className="color-font-neutral-black-4">
                      {getAuditOptions(qualification as QUALIFICATION_TYPE, item.sign)}
                    </div>
                  </div>
                )}
              </Timeline.Item>
            ))}
          </Timeline>
        </div>
        {isHistory ? (
          <div
            style={{
              background: '#ffffff',
              padding: '8px',
              marginRight: '16px',
              marginTop: '16px',
              top: '400px',
            }}
            className={`${styles.timeLine}`}
          >
            <p className={styles.historyTitle}>历史审核结果</p>
            <div style={{ height: '400px', overflowY: 'auto' }}>
              {historyList?.map((i, index) => {
                return (
                  <div className={styles.historyBox} key={index}>
                    <div className={styles.spubox}>
                      <div
                        className={styles.spuName}
                        onClick={() => {
                          checkJumpDetail(i);
                        }}
                      >
                        {i.spuName}
                      </div>
                      <div className={styles.checkTime}>
                        {i.auditTime ? moment(i.auditTime).format('YYYY-MM-DD') : ''}
                      </div>
                    </div>
                    <div>
                      <p style={{ color: LEVEL_COLOR[i?.riskLevel as 'NONE'] }}>
                        {LEVEL_NAME[i?.riskLevel as 'NONE'] || '-'}
                      </p>
                      <div className={styles['level-box']}>
                        <div className={styles['level-box-item']}>
                          <Icon
                            style={{
                              color:
                                QUALIFICATION_AUDIT_STATE_COLOR[
                                  i?.supplierQualificationAuditState as QUALIFICATION_AUDIT_STATE
                                ],
                            }}
                            className={styles['icon']}
                            type={
                              QUALIFICATION_AUDIT_STATE_ICON[
                                i?.supplierQualificationAuditState as QUALIFICATION_AUDIT_STATE
                              ]
                            }
                            theme="filled"
                          />
                          <span>商家</span>
                        </div>
                        <div className={styles['level-box-item']}>
                          <Icon
                            style={{
                              color:
                                QUALIFICATION_AUDIT_STATE_COLOR[
                                  i?.brandQualificationAuditState as QUALIFICATION_AUDIT_STATE
                                ],
                            }}
                            className={styles['icon']}
                            type={
                              QUALIFICATION_AUDIT_STATE_ICON[
                                i?.brandQualificationAuditState as QUALIFICATION_AUDIT_STATE
                              ]
                            }
                            theme="filled"
                          />
                          <span>品牌</span>
                        </div>
                      </div>
                      <div className={styles['level-box']}>
                        <div className={styles['level-box-item']}>
                          <Icon
                            style={{
                              color:
                                QUALIFICATION_AUDIT_STATE_COLOR[
                                  i?.spuQualificationAuditState as QUALIFICATION_AUDIT_STATE
                                ],
                            }}
                            className={styles['icon']}
                            type={
                              QUALIFICATION_AUDIT_STATE_ICON[
                                i?.spuQualificationAuditState as QUALIFICATION_AUDIT_STATE
                              ]
                            }
                            theme="filled"
                          />
                          <span>商品</span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <p style={{ textAlign: 'center', marginTop: '6px' }}>仅展示该商品10次审核结果 </p>
          </div>
        ) : (
          <></>
        )}
      </div>

      <div
        style={{
          flex: 1,
          boxSizing: 'border-box',

          background: '#ffffff',
        }}
      >
        <div id="SUPPLIER" ref={mapRef['SUPPLIER']}>
          <DetailTitle
            title={
              <span style={{ display: 'flex', alignItems: 'center' }}>
                商家资质
                {qualification['SUPPLIER']?.isBrandWhitelisted ? (
                  <Tag color="orange" style={{ marginLeft: '4px' }}>
                    白名单
                  </Tag>
                ) : supplierBodyDetail ? (
                  <Tag color="green" style={{ marginLeft: '4px' }}>
                    主体特批
                  </Tag>
                ) : (
                  <></>
                )}
                {autoList?.length ? (
                  <Popover content={autoPage} title="自动化审核异常项" trigger="hover">
                    <img
                      src={autoIcon}
                      style={{
                        width: '20px',
                        height: '20px',
                        marginLeft: '4px',
                        cursor: 'pointer',
                      }}
                    />
                  </Popover>
                ) : (
                  <></>
                )}
                {autoSucList ? (
                  <AuditDetail id={supplierId} onRefresh={handleRefresh}>
                    <img
                      src={autoSucIcon}
                      style={{
                        width: '20px',
                        height: '20px',
                        marginLeft: '4px',
                        cursor: 'pointer',
                      }}
                    />
                  </AuditDetail>
                ) : (
                  <></>
                )}
                {!auditResultUn ? (
                  <img
                    src={autoUnIcon}
                    style={{
                      width: '20px',
                      height: '20px',
                      marginLeft: '4px',
                    }}
                  />
                ) : (
                  <></>
                )}
              </span>
            }
            shopsTag={true}
            style={{ paddingBottom: '8px' }}
          ></DetailTitle>
          <Shops
            supplierQualification={qualificationMap?.SUPPLIER}
            auditExpirationDateMap={{}}
            isCheck={true}
            form={form}
            initialValue={
              qualification['SUPPLIER']?.timeMap
                ? qualification['SUPPLIER']?.timeMap['BUSINESS_LICENSE']
                : undefined
            }
          />

          <div style={{ paddingLeft: '16px', paddingBottom: '16px' }}>
            <div className={styles['legal-line']}>
              <div className={styles['legal-line-title']}>自动化审核结果：</div>
              <div className={styles['legal-detail']}>{auditResult}</div>
            </div>

            <AuditForm
              value={qualification['SUPPLIER']}
              type="info"
              onChange={(val) => {
                // console.log('🚀 ~ val:', val);
                handleQualification(val, 'SUPPLIER');
              }}
              supplierBodyDetail={supplierBodyDetail}
            />
          </div>
          <div className={style['divider']}></div>
        </div>
        <div id="BRAND" ref={mapRef['BRAND']}>
          <DetailTitle
            title={
              <span>
                品牌资质
                {qualification['BRAND']?.isBrandWhitelisted ? (
                  <Tag color="orange" style={{ marginLeft: '4px' }}>
                    白名单
                  </Tag>
                ) : (
                  <></>
                )}
                {brandAudit?.autoList?.length ? (
                  <Popover content={brandAutoPage} title="自动化审核异常项" trigger="hover">
                    <img
                      src={autoIcon}
                      style={{
                        width: '20px',
                        height: '20px',
                        marginLeft: '4px',
                        cursor: 'pointer',
                      }}
                    />
                  </Popover>
                ) : (
                  <></>
                )}
                {brandAudit?.autoSucList ? (
                  <BrandAuditDetail
                    apiParams={brandParams}
                    onRefresh={handleBrandRefresh}
                    type="queues"
                  >
                    <img
                      src={autoSucIcon}
                      style={{
                        width: '20px',
                        height: '20px',
                        marginLeft: '4px',
                        cursor: 'pointer',
                      }}
                    />
                  </BrandAuditDetail>
                ) : (
                  <></>
                )}
                {!brandAudit?.auditResultUn ? (
                  <img
                    src={autoUnIcon}
                    style={{
                      width: '20px',
                      height: '20px',
                      marginLeft: '4px',
                    }}
                  />
                ) : (
                  <></>
                )}
              </span>
            }
            shopsTag={true}
            businessTag={true}
            style={{ paddingBottom: '8px' }}
          ></DetailTitle>
          <Brand
            bpBrandQualification={qualificationMap?.BPBRAND}
            brandQualification={qualificationMap?.BRAND}
            auditExpirationDateMap={{}}
            auditDetailMap={{}}
            isCheck={true}
            form={form}
            initialValue_BRAND_REGISTRATION={
              qualificationMap?.BRAND ? qualificationMap?.BRAND?.brandEndTime : undefined
            }
            initialValue_BRAND_SUPPLIER_QUALIFICATION={initialValue_BRAND_SUPPLIER_QUALIFICATION}
          ></Brand>
          <div
            style={{ paddingLeft: '16px', paddingRight: '16px', paddingBottom: '8px' }}
            className={styles['trademark-information']}
          >
            {/* 商务 商家 */}
            <TrademarkInformation
              trademarkNumber={qualificationMap?.BPBRAND?.regNum || qualificationMap?.BRAND?.regNum}
            />
          </div>
          <div style={{ paddingLeft: '16px', paddingBottom: '16px' }}>
            <div className={styles['legal-line']}>
              <div className={styles['legal-line-title']}>自动化审核结果：</div>
              <div className={styles['legal-detail']}>
                {brandAudit?.auditResult}
                {brandAudit?.isTbFlagship && brandAudit?.auditResultEnum === 'PASS'
                  ? `-淘宝旗舰店`
                  : ''}
              </div>
            </div>
            <div style={{ display: 'flex' }}>
              <AuditForm
                value={qualification['BRAND']}
                type="info"
                onChange={(val) => {
                  // console.log('🚀 ~ val:', val);
                  handleQualification(val, 'BRAND');
                }}
              />
              <div
                style={{ flex: 1, marginLeft: '12px', marginRight: '16px' }}
                className={stylesAuditForm['out-info']}
              >
                <div style={{ color: '#444444', fontSize: '12px', fontWeight: 500 }}>
                  <span style={{ marginRight: '4px' }}>品牌链路AI审核结果:</span>
                  {AIAudit?.brandLinkAiHandleState ? (
                    <Tag color={AI_STATUS_COLOR[AIAudit?.brandLinkAiHandleState as AI_STATUS_ENUM]}>
                      {AI_STATUS_NAME[AIAudit?.brandLinkAiHandleState as AI_STATUS_ENUM]}
                    </Tag>
                  ) : (
                    '-'
                  )}
                  <span
                    className={`iconfont icon-jiazai ${
                      transformShop ? stylesAuditForm['icon-transform-shopPoints'] : null
                    }`}
                    style={{
                      color: '#204EFF',
                      marginLeft: '4px',
                      cursor: 'pointer',
                      marginTop: '4px',
                    }}
                    onClick={handleRefreshAiResult}
                  ></span>
                </div>
                <div
                  className={stylesAuditForm['ai-content']}
                  dangerouslySetInnerHTML={{
                    __html: AIAudit?.brandLinkAiHandleResult || '',
                  }}
                >
                  {/* {AIAudit?.brandLinkAiHandleResult || '-'} */}
                </div>
                <div>
                  {AIAudit?.brandLinkAiThoughtProcessResult ? (
                    <ThoughtProcess thoughtProcess={AIAudit?.brandLinkAiThoughtProcessResult}>
                      <Button size="small" style={{ marginRight: '8px' }}>
                        查看思考过程
                      </Button>
                    </ThoughtProcess>
                  ) : (
                    <></>
                  )}

                  <Button
                    size="small"
                    loading={regenerateAiAuditOrGetResultLoading}
                    onClick={handleAgain}
                  >
                    重新生成
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className={style['divider']}></div>
        </div>
        <div id="SPU" ref={mapRef['SPU']} style={{ paddingBottom: '16px' }}>
          <DetailTitle
            title={
              <span>
                商品资质
                {brandWhite ? (
                  <Tag color="orange" style={{ marginLeft: '4px' }}>
                    白名单B
                  </Tag>
                ) : (
                  <></>
                )}
                {goodsAutoList?.length ? (
                  <Popover content={goodsAutoPage} title="自动化审核异常项" trigger="hover">
                    <img
                      src={GoodsAuditIcon}
                      style={{
                        width: '20px',
                        height: '20px',
                        marginLeft: '4px',
                        cursor: 'pointer',
                      }}
                    />
                  </Popover>
                ) : !goodsAuditResult ? (
                  <img
                    src={GoodsAuditIcon}
                    style={{
                      width: '20px',
                      height: '20px',
                      marginLeft: '4px',
                    }}
                  />
                ) : (
                  <GoodsAuditDetail id={goodsAuditResultId} onRefresh={handleGoodsRefresh}>
                    <img
                      src={GoodsAuditIcon}
                      style={{
                        width: '20px',
                        height: '20px',
                        marginLeft: '4px',
                        cursor: 'pointer',
                      }}
                    />
                  </GoodsAuditDetail>
                )}
              </span>
            }
            shopsTag={true}
            businessTag={true}
            style={{ paddingBottom: '8px' }}
          ></DetailTitle>
          <Commodity
            bpSpuQualification={qualificationMap['BPSPU']}
            spuQualification={qualificationMap['SPU']}
            auditDetailMap={{}}
            auditExpirationDateMap={{}}
            isEdit={false}
          ></Commodity>
          <div style={{ paddingLeft: '16px', paddingBottom: '16px' }}>
            <div className={styles['legal-line']}>
              <div className={styles['legal-line-title']}>自动化审核结果：</div>
              <div className={styles['legal-detail']}>{goodsAuditResult ?? '-'}</div>
            </div>
          </div>
          <div style={{ paddingLeft: '16px', paddingBottom: '16px', marginTop: '-14px' }}>
            <div style={{ width: '380px', padding: '20px 16px 0 16px', background: '#f7faff' }}>
              <Form.Item labelCol={{ span: 5 }} label="特殊材质" required>
                {form.getFieldDecorator('isSpecialMaterial', {
                  initialValue: qualification['SPU']?.timeMap
                    ? qualification['SPU']?.timeMap['GOODS_QUALIFICATION']
                    : undefined,
                  rules: [{ required: true, message: '请选择是否为特殊材质' }],
                })(
                  <Radio.Group style={{ marginTop: '6px' }}>
                    <Radio value={1}>是</Radio>
                    <Radio value={0}>否</Radio>
                  </Radio.Group>,
                )}
              </Form.Item>
              <Form.Item labelCol={{ span: 7 }} wrapperCol={{ span: 17 }} label="商品资质有效期">
                {form.getFieldDecorator('special', {
                  //初始值
                })(
                  <Row gutter={24} style={{ display: 'flex', alignItems: 'center' }}>
                    <Col span={15}>
                      <Form.Item>
                        {form?.getFieldDecorator('GOODS_QUALIFICATION_TIME')(
                          <DatePicker
                            disabled={
                              form?.getFieldValue('GOODS_QUALIFICATION_TIME') &&
                              moment(form?.getFieldValue('GOODS_QUALIFICATION_TIME')).isSame(
                                moment(32503564800000),
                              )
                            }
                            style={{ width: '100%' }}
                          />,
                        )}
                      </Form.Item>
                    </Col>
                    <Col span={9} style={{ paddingLeft: '0px' }}>
                      <Checkbox
                        onChange={(e) => {
                          const { checked } = e?.target;
                          form?.setFieldsValue({
                            GOODS_QUALIFICATION_TIME: checked ? moment(32503564800000) : undefined,
                          });
                        }}
                        checked={
                          form?.getFieldValue('GOODS_QUALIFICATION_TIME') &&
                          moment(form?.getFieldValue('GOODS_QUALIFICATION_TIME')).isSame(
                            moment(32503564800000),
                          )
                        }
                      >
                        长期有效
                      </Checkbox>
                    </Col>
                  </Row>,
                )}
              </Form.Item>
            </div>
            <AuditForm
              value={qualification['SPU']}
              type="info"
              onChange={(val) => {
                // console.log('🚀 ~ val:', val);
                handleQualification(val, 'SPU');
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default QualificationReview;
