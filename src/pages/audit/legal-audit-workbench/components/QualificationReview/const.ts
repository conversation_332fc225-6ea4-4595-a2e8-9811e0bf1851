import { QualificationKeyEnum } from '@/common/constants/audit/legal-audit';
export * from '../../constant';

export const AUDIT_PROJECT_CONFIG = [
  {
    title: '商家资质',
    sign: QualificationKeyEnum.SUPPLIER,
  },
  // {
  //   title: '店铺资质',
  //   sign: QualificationKeyEnum.SHOP,
  // },
  {
    title: '品牌资质',
    sign: QualificationKeyEnum.BRAND,
  },
  {
    title: '商品资质',
    sign: QualificationKeyEnum.SPU,
  },
];

export const UPDATE_TYPE_MAP = {
  ADDED: '新上传',
  DELETED: '已删除',
};

export const AUDIT_PROJECT_MAP = {
  brandQualification: QualificationKeyEnum.BRAND,
  shopQualification: QualificationKeyEnum.SHOP,
  spuQualification: QualificationKeyEnum.SPU,
  supplierQualification: QualificationKeyEnum.SUPPLIER,
};

export enum AI_STATUS_ENUM {
  PENDING = 'PENDING',
  NO_ACTION_REQUIRED = 'NO_ACTION_REQUIRED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

export const AI_STATUS_NAME: Record<AI_STATUS_ENUM, string> = {
  [AI_STATUS_ENUM.PENDING]: '待处理',
  [AI_STATUS_ENUM.NO_ACTION_REQUIRED]: '无需处理',
  [AI_STATUS_ENUM.PROCESSING]: '生成中',
  [AI_STATUS_ENUM.COMPLETED]: '已生成',
  [AI_STATUS_ENUM.FAILED]: '生成失败',
};

export const AI_STATUS_COLOR: Record<AI_STATUS_ENUM, string> = {
  [AI_STATUS_ENUM.PENDING]: 'blue',
  [AI_STATUS_ENUM.NO_ACTION_REQUIRED]: 'green',
  [AI_STATUS_ENUM.PROCESSING]: 'orange',
  [AI_STATUS_ENUM.COMPLETED]: 'green',
  [AI_STATUS_ENUM.FAILED]: 'red',
};
