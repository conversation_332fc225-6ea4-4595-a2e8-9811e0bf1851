import { Button, Input, Popover } from 'antd';
import React from 'react';
import { classNames } from '@/utils/moduleUtils';
import styles from './index.module.less';
import {
  QualificationItemType,
  QUALIFICATION_TYPE,
  QualificationKeyEnumKey,
  CHANGE_VALUE,
  QUALIFICATION_AUDIT_STATE,
} from '../../constant';
import CommonReviewComments from './CommonReviewComments';
import { SupplierBodySpecialAuditLatestResult } from '@/pages/audit/legal-audit-queue/yml';

interface IProps {
  type: 'success' | 'info' | 'danger' | 'waring';
  onChange: (val: CHANGE_VALUE) => void;
  value: QUALIFICATION_TYPE[QualificationKeyEnumKey];
  style?: any;
  supplierBodyDetail?: SupplierBodySpecialAuditLatestResult;
}

const AuditForm: React.FC<IProps> = ({
  value: state = {} as QUALIFICATION_TYPE[QualificationKeyEnumKey],
  type,
  onChange,
  style,
  supplierBodyDetail,
}) => {
  // 手动输入填入
  const onInputChange = (value: string) => {
    const flag = state.auditState === 'PASS' || (state.auditState === 'NO_PASS' && !!value);

    onChange({
      init: !['PASS', 'NO_PASS'].includes(state.auditState!),
      auditState: state.auditState,
      auditOpinion: value,
      done: flag,
    });
  };
  // 常用审核意见填入时保留手动输入的内容
  const onBtnClickChange = (value: string) => {
    const flag = state.auditState === 'PASS' || (state.auditState === 'NO_PASS' && !!value);

    const jionValue = state.auditOpinion ? state.auditOpinion + ';\n' + value : value;
    // 数组去重，不然会无限累加
    let valueList = jionValue.split(';\n');
    valueList = [...new Set(valueList)];
    // 转化为字符串展示
    const valueStr = valueList.join(';\n');

    onChange({
      init: !['PASS', 'NO_PASS'].includes(state.auditState!),
      auditState: state.auditState,
      auditOpinion: valueStr,
      done: flag,
    });
  };

  return (
    <div className={`${styles[type]} ${styles.auditFormWrap}`} style={style}>
      <div>
        <Button
          type="primary"
          icon="check"
          className={classNames({
            [styles.resetButton]: true,
            [styles.notChecked]: !state.init,
            [styles.pass]: !state.init && state.auditState === 'PASS',
          })}
          onClick={() => {
            onChange({
              init: false,
              auditState: 'PASS' as QUALIFICATION_AUDIT_STATE,
              auditOpinion: state.auditOpinion,
              done: true,
            });
          }}
        >
          合格
        </Button>
        {(!supplierBodyDetail || supplierBodyDetail?.status !== 'PASS') && (
          <Button
            type="primary"
            icon="close"
            className={classNames({
              [styles.resetButton]: true,
              'ml-8': true,
              [styles.notChecked]: !state.init,
              [styles.noPass]: !state.init && state.auditState === 'NO_PASS',
            })}
            onClick={() => {
              onChange({
                init: false,
                auditState: 'NO_PASS' as QUALIFICATION_AUDIT_STATE,
                auditOpinion: state.auditOpinion,
                done: !!state.auditOpinion,
              });
            }}
          >
            不合格
          </Button>
        )}
      </div>
      <Input.TextArea
        value={state.auditOpinion}
        maxLength={500}
        className="mt-8"
        placeholder="审核意见"
        rows={6}
        onChange={(e) => {
          const val = e.target.value;
          onInputChange(val);
        }}
        style={{
          resize: 'none',
        }}
      />

      {state.done === false && <div className="color-font-danger-2">请完善审核意见</div>}
      <Popover
        overlayClassName={styles.resetPopover}
        content={<CommonReviewComments onInputChange={onBtnClickChange} />}
        placement="topRight"
        title={null}
        getPopupContainer={(node) => node?.parentElement || node}
      >
        <span
          className="cursor-pointer mt-8 pl-8 pr-8 color-bg-neutral-white"
          style={{
            display: 'inline-block',
            paddingTop: 2,
            paddingBottom: 2,
            border: '1px solid rgba(46,52,66,0.12)',
          }}
        >
          常用审核意见
        </span>
      </Popover>
    </div>
  );
};
export default AuditForm;
