import React, { useState, useEffect } from 'react';
import { classNames } from '@/utils/moduleUtils';
import { COMMON_REVIEW_COMMENTS } from './const';
import styles from './index.module.less';
import { Button, Checkbox, Icon, message } from 'antd';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';

interface IProps {
  onInputChange: (val: string) => void;
}

const CommonReviewComments: React.FC<IProps> = ({ onInputChange }) => {
  const { codeList: COMMON_REVIEW_COMMENTS_LIST = [] } = useCode(CODE_ENUM.COMMON_AUDIT_OPINION, {
    able: true,
    isGetMultilevel: true,
  });
  const [activeId, setActiveId] = useState(-1);

  const [opinionsList, setOpinionsList] = useState<string[]>([]);

  const handleMouseEnter = (id: string) => {
    setActiveId(id);
  };

  const onCheck = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    const { value, checked } = e.target;
    if (!checked) {
      const temp = [...opinionsList];
      const index = temp.findIndex((i) => i === value);
      temp.splice(index, 1);
      setOpinionsList(temp);
    } else {
      const list = [...opinionsList, value];
      setOpinionsList(list);
    }
  };

  useEffect(() => {
    if (COMMON_REVIEW_COMMENTS_LIST?.length > 0) {
      setActiveId(COMMON_REVIEW_COMMENTS_LIST[0].id);
    }
  }, [COMMON_REVIEW_COMMENTS_LIST]);

  return (
    <div className={styles.CommonReviewCommentsWrap}>
      <div className={`flex`} style={{ border: '1px solid rgba(46, 51, 66, 0.08)' }}>
        <ul className={styles.left}>
          {COMMON_REVIEW_COMMENTS_LIST?.map((item) => {
            return (
              <li
                onMouseEnter={() => handleMouseEnter(item.id)}
                className={classNames({
                  'cursor-pointer flex items-center': true,
                  [styles.cateItem]: true,
                  [styles.active]: activeId === item.id,
                })}
                key={item.id}
              >
                <span>{item.label} </span>
                <Icon className="ml-4 text-12" type="right" />
              </li>
            );
          })}
        </ul>
        <div className="ml-12 mr-12 pb-40 h-300" style={{ overflow: 'auto' }}>
          {COMMON_REVIEW_COMMENTS_LIST?.find((item) => item.id === activeId)?.children?.map(
            (item) => {
              return (
                <div key={item.id} className="mt-12">
                  <Checkbox
                    checked={opinionsList.includes(item.label)}
                    value={item.label}
                    onClick={onCheck}
                  >
                    {item.label}
                  </Checkbox>
                </div>
              );
            },
          )}
        </div>
      </div>
      <div className="flex justify-between items-center pl-12 pt-4 pr-20">
        <span>已选：{opinionsList.length}</span>
        <Button
          type="primary"
          size="small"
          onClick={() => {
            if (opinionsList.length === 0) {
              message.warn('请选择审核意见');
              return;
            }
            message.success('已成功填入');
            onInputChange(opinionsList.join(';\n'));
          }}
        >
          快速填入
        </Button>
      </div>
    </div>
  );
};

export default CommonReviewComments;
