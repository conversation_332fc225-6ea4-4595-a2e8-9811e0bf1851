@import '~web-common-modules/styles//token.less';
@import '~web-common-modules/styles/var.less';
.auditFormWrap {
  width: 380px;
  padding: 16px 20px;

  &.success {
    background-color: @color-success-light3;
  }

  &.info {
    background-color: @color-info-light3;
  }

  &.danger {
    background-color: @color-danger-light3;
  }

  &.waring {
    background-color: @color-warning-light3;
  }
  .resetButton {
    height: 24px;
    padding: 2px 8px;
  }

  .notChecked {
    background-color: #ffffff;
    border-color: @color-neutral-black-light1;
    color: @color-neutral-black-3;
  }

  .noPass {
    background-color: @color-danger-2;
    border-color: @color-danger-2;
    color: #fff;
  }

  .pass {
    background-color: @color-success-2;
    border-color: @color-success-2;
    color: #fff;
  }

  .resetPopover {
    :global {
      .ant-popover-inner-content {
        padding: 0 !important;
      }
    }
  }
}

.CommonReviewCommentsWrap {
  padding: 4px 0;

  :global {
    .ant-checkbox-wrapper {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      width: 300px;
    }
  }

  .left {
    border-right: 1px solid @color-neutral-black-light2;
  }
  .cateItem {
    padding: 8px 16px;
  }
  .active {
    background: @color-primary-light2;
  }
}

.out-info {
  background-color: @color-info-light3;
  display: flex;
  flex-direction: column;
  padding: 16px 12px;
}

.ai-content {
  width: 100%;
  height: 127px;
  padding: 4px 0;
  overflow-y: auto;
  margin: 8px 0;
}

.icon-transform-shopPoints {
  display: inline-block;
  transform-origin: center center;
  animation: spin 1s 1 linear;
}

@keyframes spin {
  from {
    transform: rotate(360deg);
  }

  to {
    transform: rotate(0deg);
  }
}
