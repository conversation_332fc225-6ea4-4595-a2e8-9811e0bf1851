import { Form, Input, DatePicker, Modal, message, Tag } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { DrawerProps } from 'antd/es/drawer';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import WithToggleModal from '@/components/WithToggleModal';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import moment, { Moment } from 'moment';
import ChooseRiskLevel from './ChooseRiskLevel';
import { QualificationResultType } from '../../Index';
import { WrappedFormUtils } from 'antd/es/form/Form';
import { QUALIFICATION_TYPE } from '../../constant';
import { qualificationAuditCateCheck } from '../../yml';
import { useRequest } from 'ahooks';

export enum LiveTimeRangeEnum {
  ALL_DAY = 'ALL_DAY',
  TIME_INTERVAL = 'TIME_INTERVAL',
  CROSS_DAY = 'CROSS_DAY',
}

export enum OperationType {
  ADD = 'add',
  EDIT = 'edit',
}

interface IProps extends FormComponentProps<{}>, WithToggleModalProps, DrawerProps {
  id?: string;
  qualificationResult: QualificationResultType;
  qualification: QUALIFICATION_TYPE | null;
  current: any;
  qualificationMap: any;
  getTime: any;
  onRef: any;
}

const SubmitModal: React.FC<IProps> = ({
  form,
  onOk,
  id,
  qualificationResult,
  qualification,
  qualificationMap,
  visible,
  current,
  getTime,
  onRef,
  ...rest
}) => {
  const [cateCheck, setCateCheck] = useState<boolean>(false);

  const { run: cateCheckRun } = useRequest(qualificationAuditCateCheck, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        setCateCheck(res?.result);
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });
  const onSubmit = () => {
    form.validateFieldsAndScroll((err, values) => {
      if (err) {
        return;
      }

      const { expirationDate, riskLevel, ...restValue } = values;

      onOk?.({
        ...restValue,
        riskLevel,
        expirationDate: expirationDate ? moment(expirationDate).endOf('day').valueOf() : undefined,
      });
    });
  };
  // console.log(current.qualification?.brandQualification?.type);
  // current.qualification?.brandQualification?.type==='PURCHASE'
  const disabledDate = (currentDate: Moment | null) => {
    return moment(currentDate).valueOf() < moment().startOf('day').valueOf();
  };

  const formateTime = () => {
    // const minTime = '';
    // console.log(qualification, '-------------->');
    const mapValue = Object.values(qualification || {});
    // console.log('🚀 ~ file: index.tsx:65 ~ formateTime ~ mapValue:', mapValue);
    const timeList = mapValue.reduce((pre: any, cur: any) => {
      return { ...pre, ...(cur.timeMap || {}) };
    }, {});
    const snapshotMap = formateMap();
    // 去处未上传的值
    const timeValue = Object.entries(timeList)
      .filter(
        ([kItem, vItem]) =>
          vItem && vItem !== 32503564800000 && snapshotMap[kItem] && snapshotMap[kItem]?.length,
      )
      .map((item) => moment(item[1] || ''));
    // console.log('🚀 ~ file: index.tsx:71 ~ formateTime ~ timeValue:', timeValue);
    const minTIme =
      timeValue && timeValue.length ? moment.min(timeValue || []) : moment(current.maxLiveDate);
    // console.log('🚀 ~ file: index.tsx:72 ~ formateTime ~ minTIme:', minTIme);
    // //  用form set时间
    form.setFieldsValue({
      expirationDate: minTIme,
    });
  };

  const formateMap = () => {
    if (qualificationMap) {
      const value = Object.values(qualificationMap || {});
      const types: any = value.reduce((pre: any, cur: any) => {
        return [...pre, ...(cur.types || [])];
      }, []);
      // console.log('🚀 ~ file: index.tsx:91 ~ types ~ types:', types);
      const map = types.reduce((pre: any, cur: any) => {
        return { ...pre, [cur.no]: cur.snapshotMap };
      }, {});
      return map;
    }
    return {};
  };

  useEffect(() => {
    if (visible && qualification) {
      const { SUPPLIER, BRAND, SPU } = qualification;
      // formateTime();
      form.setFieldsValue({
        auditOpinion: `商家资质：${SUPPLIER.auditOpinion ? SUPPLIER.auditOpinion : '合格'}
品牌资质：${BRAND.auditOpinion ? BRAND.auditOpinion : '合格'}
商品资质：${SPU.auditOpinion ? SPU.auditOpinion : '合格'}`,
      });
      cateCheckRun({ id: current?.id });
    }
  }, [visible, qualification]);

  const { riskLevel } = form.getFieldsValue();

  useEffect(() => {
    if (riskLevel !== 'HIGH' && current?.qualification?.brandQualification?.type !== 'PURCHASE') {
      // formateTime();
      const formTimeValue = getTime && getTime();
      // 取最小时间
      const {
        BRAND_REGISTRATION_TIME,
        BRAND_SUPPLIER_QUALIFICATION_TIME,
        BUSINESS_LICENSE_TIME,
        GOODS_QUALIFICATION_TIME,
      } = formTimeValue || {};
      const filterTime = [
        BRAND_REGISTRATION_TIME,
        BRAND_SUPPLIER_QUALIFICATION_TIME,
        BUSINESS_LICENSE_TIME,
        GOODS_QUALIFICATION_TIME,
      ].filter((item) => item && moment(item).valueOf() !== 32503564800000);
      // console.log('🚀 ~ useEffect ~ filterTime:', filterTime);
      const minTime =
        filterTime && filterTime?.length ? moment.min(filterTime) : moment(current.maxLiveDate);
      form.setFieldsValue({
        expirationDate: minTime,
      });
    }
  }, [riskLevel]);

  const handleSetDate = (days: number) => {
    const minTime = moment().add(days, 'days');
    form.setFieldsValue({
      expirationDate: minTime,
    });
  };

  useImperativeHandle(onRef, () => ({
    onCancel: rest?.onCancel,
  }));

  return (
    <Modal
      // 372 420
      width={420}
      {...rest}
      title={'提交审核'}
      maskClosable={false}
      visible={visible}
      onOk={onSubmit}
    >
      <Form layout="vertical">
        <Form.Item label="风险等级" required>
          {form.getFieldDecorator('riskLevel', {
            rules: [
              {
                validator(_, val, cb) {
                  if (!val) {
                    cb('请选择风险等级');
                    return;
                  }
                  cb();
                },
              },
            ],
          })(
            // @ts-ignore
            <ChooseRiskLevel
              qualification={qualification}
              qualificationResult={qualificationResult}
              cateCheck={cateCheck}
            />,
          )}
        </Form.Item>

        {riskLevel !== 'HIGH' && current?.qualification?.brandQualification?.type !== 'PURCHASE' && (
          <Form.Item
            label={
              <span>
                审核有效期
                <span className="ml-12 font-secondary color-font-neutral-black-4">
                  有效期内，商品再次提交将免审通过
                </span>
              </span>
            }
          >
            {form.getFieldDecorator('expirationDate', {
              rules: [
                {
                  required: riskLevel !== 'HIGH',
                  message: '请选择审核有效期',
                },
              ],
            })(
              <DatePicker
                style={{ width: '100%' }}
                disabledDate={disabledDate}
                showToday={false}
                renderExtraFooter={() => {
                  return (
                    <div>
                      <Tag
                        color="blue"
                        style={{ marginRight: '4px' }}
                        onClick={() => {
                          handleSetDate(30);
                        }}
                      >
                        30天
                      </Tag>
                      <Tag
                        color="blue"
                        style={{ marginRight: '4px' }}
                        onClick={() => {
                          handleSetDate(35);
                        }}
                      >
                        35天
                      </Tag>
                      <Tag
                        color="blue"
                        style={{ marginRight: '4px' }}
                        onClick={() => {
                          handleSetDate(90);
                        }}
                      >
                        90天
                      </Tag>
                      <Tag
                        color="blue"
                        style={{ marginRight: '4px' }}
                        onClick={() => {
                          handleSetDate(180);
                        }}
                      >
                        180天
                      </Tag>
                      <Tag
                        color="blue"
                        style={{ marginRight: '4px' }}
                        onClick={() => {
                          handleSetDate(365);
                        }}
                      >
                        365天
                      </Tag>
                    </div>
                  );
                }}
              />,
            )}
          </Form.Item>
        )}

        <Form.Item label="审核意见">
          {form.getFieldDecorator('auditOpinion', {
            rules: [
              {
                required: riskLevel === 'HIGH',
                message: '请填写审核意见',
              },
            ],
          })(
            <Input.TextArea
              style={{ resize: 'none' }}
              rows={6}
              placeholder="最终审核意见"
              maxLength={500}
            />,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default WithToggleModal<Omit<IProps, 'form'>>(Form.create<IProps>()(SubmitModal));
