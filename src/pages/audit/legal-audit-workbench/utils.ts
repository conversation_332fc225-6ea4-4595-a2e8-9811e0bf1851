import { QualificationAuditLockResult } from '../legal-audit-queue/yml';
import OSSUpload from 'web-common-modules/biz/gmvCommissionManage/view/components/OSSUpload';
import {
  QUALIFICATION_TYPE_MAP,
  QUALIFICATION_TYPE,
  QualificationKeyEnumKey,
  QualificationKeyEnumName,
  QUALIFICATION_AUDIT_STATE,
} from './constant';
import { QualificationKeyEnum } from '@/common/constants/audit/legal-audit';

export const formatQualification = (current: QualificationAuditLockResult) => {
  const { qualification, lastAuditDetailMap, lastAuditExpirationDateMap } = current;
  const {
    brandQualification,
    spuQualification,
    supplierQualification,
    bpBrandQualification,
    bpSpuQualification,
  } = qualification as any;
  const nameList = ['商家资质', '品牌资质', '商品资质'];

  const keyList = [
    QualificationKeyEnum.SUPPLIER,
    QualificationKeyEnum.BRAND,
    QualificationKeyEnum.SPU,
  ];
  const basicQualification = [
    supplierQualification, // 商家资质
    // shopQualification, // 店铺资质
    brandQualification, // 品牌资质
    spuQualification, // 商品资质
  ].reduce((pre: QUALIFICATION_TYPE, cur: any, currentIndex: number) => {
    // console.log('🚀 ~ file: utils.ts:34 ~ ].reduce ~ cur:', cur, lastAuditExpirationDateMap);
    if (!cur) {
      return { ...pre };
    }
    const auditDetail = ((lastAuditDetailMap as any) || {})[cur.itemVersionId];

    const item: QUALIFICATION_TYPE[QualificationKeyEnumKey] = {
      name: nameList[currentIndex] as string,
      key: keyList[currentIndex],
      fileLength: cur.quantity || 0,
      itemVersionId: cur.itemVersionId,
      init: !auditDetail ? false : true,
      done: !auditDetail ? false : true,
      isBrandWhitelisted: false,
      timeMap: {},
      auditState: QUALIFICATION_AUDIT_STATE.NONE,
      // timeMap: {
      //   no:xxxx
      //   no:xxx
      // }
    };
    if (auditDetail) {
      item.auditState = auditDetail.auditState;
      item.expirationDate = auditDetail.expirationDate;
      item.auditOpinion = auditDetail.auditOpinion;
      item.init = auditDetail?.auditState === 'NONE';

      item.done = auditDetail?.auditState === 'NO_PASS' && !auditDetail.auditOpinion ? false : true;

      item.isBrandWhitelisted = auditDetail.isBrandWhitelisted;
    }

    if (cur.types && lastAuditExpirationDateMap) {
      // console.log(
      //   '🚀 ~ file: utils.ts:61 ~ ].reduce ~ lastAuditExpirationDateMap:',
      //   lastAuditExpirationDateMap,
      // );
      // console.log('🚀 ~ file: utils.ts:61 ~ ].reduce ~ cur.types:', cur.types);
      const lastAuditExpirationDate = cur.types?.reduce((pre: any, dateCurrent: any) => {
        const dateItem = lastAuditExpirationDateMap[dateCurrent.no];
        // if (dateItem?.expirationDate === 32503564800000) {
        //   console.log(keyList[currentIndex], dateCurrent.no, '--------------------->');
        //   setFieldsValue({
        //     [`${keyList[currentIndex]}_${dateCurrent.no}_check`]: true,
        //   });
        // }
        return {
          ...pre,
          [dateCurrent.no]: dateItem?.expirationDate || undefined,
        };
      }, {});
      // console.log(
      //   '🚀 ~ file: utils.ts:61 ~ lastAuditExpirationDate ~ lastAuditExpirationDate:',
      //   lastAuditExpirationDate,
      // );
      item.timeMap = lastAuditExpirationDate;
    }

    return {
      ...pre,
      ...{
        [keyList[currentIndex]]: item,
      },
    };
  }, {});

  const basicQualificationMap: QUALIFICATION_TYPE_MAP = {
    ...formateQualificationMap(supplierQualification, QualificationKeyEnum.SUPPLIER),
    // ...formateQualificationMap(shopQualification, QualificationKeyEnum.SHOP),
    ...formateQualificationMap(brandQualification, QualificationKeyEnum.BRAND),
    ...formateQualificationMap(spuQualification, QualificationKeyEnum.SPU),
    ...formateQualificationMap(bpBrandQualification, 'BPBRAND'),
    ...formateQualificationMap(bpSpuQualification, 'BPSPU'),
  };

  return {
    basicQualification: basicQualification || null,
    basicQualificationMap: basicQualificationMap || null,
  };
};

type Q_KEY =
  | 'brandQualification'
  | 'shopQualification'
  | 'spuQualification'
  | 'supplierQualification';

type formateQualificationMapType = (
  qualification: Required<QualificationAuditLockResult>['qualification'][Q_KEY],
  key: QualificationKeyEnum | 'BPBRAND' | 'BPSPU',
) => QUALIFICATION_TYPE_MAP;

const formateQualificationMap: formateQualificationMapType = (qualification, key) => {
  // 不存在资质信息
  if (!qualification) {
    return {
      [key]: {
        name: QualificationKeyEnumName[key],
      },
    } as QUALIFICATION_TYPE_MAP;
  }
  // 存在资质信息
  const { types, snapshotMap, extension, snapshotChangeMap, ...other } = qualification;
  const handleTypes = types?.map((item) => {
    const { name, no } = item;
    return {
      name,
      no,
      snapshotMap: snapshotMap ? snapshotMap[no as string] : [],
    };
  });
  // 此处在处理snapshotMap时，需要判断是否有变更，如果有变更，则将isChangeFlag设置为true。是否变更返回在同级对象snapshotChangeMap中
  // 此参数将控制src/pages/choice-list-new/components/LegalCheckDrawer/imgUtils.tsx中是否显示变更图标
  // 目前仅适用于审核队列-待审核-立即审核弹框内
  handleTypes.forEach((ele) => {
    ele.snapshotMap = ele.snapshotMap?.map((item) => {
      item.isChangeFlag = snapshotChangeMap
        ? snapshotChangeMap[item.qualificationType as string]
        : false;
      return item;
    });
  });

  return {
    [key]: {
      name: QualificationKeyEnumName[key],
      extension,
      types: handleTypes || [],
      ...other,
    },
  } as QUALIFICATION_TYPE_MAP;
};
