import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type QualificationAuditResubmitRequest = {
  auditOpinion?: string /*法务意见备注*/;
  automatedAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING'
    | 'PASS_SPECIAL' /*自动化审核结果[SupplierQualificationAuditResultEnum]*/;
  automatedSnapshotId?: string /*自动化审核id*/;
  brandAutomatedAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING' /*品牌自动化审核结果[BrandQualificationAuditResultEnum]*/;
  brandAutomatedSnapshotId?: string /*品牌自动化审核id*/;
  details?: Array<{
    auditOpinion?: string /*审核意见*/;
    auditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
    bizType?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'GOODS'
      | 'SHOP'
      | 'BP_BRAND'
      | 'BP_GOODS'
      | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
    isBrandWhitelisted?: boolean /*是否白名单B*/;
    itemVersionId?: string /*资质项版本ID*/;
    remark?: string /*备注*/;
  }> /*审核请求明细*/;
  expirationDate?: string /*法务审核有效期*/;
  expirationDates?: Array<{
    bizType?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'GOODS'
      | 'SHOP'
      | 'BP_BRAND'
      | 'BP_GOODS'
      | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
    expirationDate?: string /*法务审核有效期*/;
    itemVersionId?: string /*资质项版本ID*/;
    qualificationsTypeNo?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
  }> /*审核有效期*/;
  goodsAutoAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING' /*商品自动化审核结果[GoodsQualificationAuditResultEnum]*/;
  goodsSnapshotId?: string /*商品自动化审核id*/;
  id?: string /*审核记录主键*/;
  isSpecialMaterial?: boolean /*是否特殊材质(1:是,0:否,无默认值)*/;
  isSupplierBodySpecialAuditPass?: boolean /*是否商家主体特批通过*/;
  riskLevel?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待处理-NONE[QualificationRiskLevelEnum]*/;
  selectionRoundExtensions?: Array<{
    id?: string /*关联记录主键*/;
    promotionLink?: string /*上播链接*/;
  }> /*场次货盘扩展请求*/;
  trademarkNumber?: string /*商标注册号/申请号*/;
  version?: string /*版本号-乐观锁*/;
  versionId?: string /*资质版本ID*/;
};

export type QualificationAuditResubmitResult = {
  auditorName?: string /*当前认领审核人*/;
  errorCode?:
    | 'INVALID_PARAM'
    | 'EXPORT_EMPTY_DATA_ERROR'
    | 'DATA_ERROR'
    | 'UNKNOWN_ERROR'
    | 'DOWNLOAD_ERROR'
    | 'LEAF_KEY_IS_NONE'
    | 'GENERATE_LEAF_ID_ERROR'
    | 'INST_GRANT_ERROR'
    | 'ACCESS_TOKEN_IS_NULL'
    | 'KUAI_SHOU_ACCESS_TOKEN_IS_NULL'
    | 'UPLOAD_TEMPLATE_ERROR'
    | 'UPLOAD_DATA_EMPTY'
    | 'IMPORT_OUT_OF_LIMIT'
    | 'QUALIFICATION_LOCKED'
    | 'QUALIFICATION_AUDITED'
    | 'QUALIFICATION_CHANGED'
    | 'QUALIFICATION_UN_SUPPORT_RESUBMIT'
    | 'ONE_OF_THESE_APTITUDE_MUST_CONFIGURED'
    | 'QUALIFICATION_EXPIRATION_DATE_INVALID'
    | 'CATE_CHECK_ERROR'
    | 'DY_CATE_CHECK_ERROR'
    | 'TB_CATE_CHECK_ERROR'
    | 'INVOKE_IASM_SERVICE_FAIL'
    | 'SELECTION_ROUND_IS_EXISTS'
    | 'LIVE_ROUND_NOT_EXISTS'
    | 'INVOKE_OFTEN'
    | 'DO_CREATE_SELECTION_ROUND_SHOP_ID_IS_NULL'
    | 'DO_CREATE_SELECTION_ROUND_EXIST_DUPLICATES'
    | 'DO_CREATE_SELECTION_ROUND_NOT_IN_INVESTMENT_PLAN_TIME'
    | 'APPLY_CHANNEL_NOT_EQ_INVESTMENT_PLAN_LIVE_ROUND_INVITE'
    | 'EXISTS_BLACKLIST'
    | 'BUSINESS_QUANTITY_LIMIT_REACHED'
    | 'FAVORABLE_RATE_BELOW_STANDARD'
    | 'SHOP_POINT_BELOW_STANDARD'
    | 'EXISTENCE_AUDIT_RULE'
    | 'EXISTENCE_HIT_CONDITION'
    | 'INVOKE_TOOLS_SERVICE_FAIL'
    | 'OA_MAPPING_ERROR'
    | 'LOW_OF_EXPORT_ZERO'
    | 'EXCEEDED_MAX_EXPORT_LIMIT'
    | 'BUSINESS_TERM_DATE_IS_NOT_NULL'
    | 'DEPOSIT_AMOUNT_IS_NOT_NULL'
    | 'SERVICE_TYPE_OPTION_IS_NOT_EXIST'
    | 'QUALIFICATION_WHITE_LIST_CATE_RULE_EXIST'
    | 'CURR_AUDIT_IS_WAIT_AUDIT'
    | 'INVOKE_BIG_DATA_SERVICE_FAIL'
    | 'SHOP_QUALITY_RETURN_RATE_GE_THAN_CONFIG_VALUE'
    | 'BRAND_AUTOMATED_QUALIFICATION_NON_EXIST'
    | 'EXCEEDED_MAX_EXPORT_LIMIT_HUNDRED'
    | 'EXPORT_FAILURE'
    | 'SELECTION_ROUND_ADD_QUOTA_OVER_THE_CONFIG'
    | 'SELECT_GOODS_POOL_ID_OR_NO_IS_EMPTY'
    | 'INEFFICIENCY_RATIO_CONFIG'
    | 'INEFFICIENT_PRODUCTS_PAY_GMV_BELOW_STANDARD'
    | 'GET_SENSITIVE_WORDS_CONFIG_ERROR'
    | 'PAY_GMV_BELOW_STANDARD'
    | 'SENSITIVE_WORDS_CHECK_FAIL'
    | 'DEPT_NAME_ERROR'
    | 'LIVE_ROOM_NAME_ERROR'
    | 'CREATOR_ERROR'
    | 'GMT_CREATED_ERROR'
    | 'MODIFIER_ERROR'
    | 'AUDIT_TIME_ERROR'
    | 'SUPPLIER_NO_ERROR'
    | 'SUPPLIER_NAME_ERROR'
    | 'SAVE_SUPPLIER_BODY_SPECIAL_AUDIT_ERROR'
    | 'CATEGORY_ID_EMPTY'
    | 'WORD_SEGMENTATION_RESULT_INCORRECT'
    | 'AI_ASSIST_AUDIT_IS_NULL'
    | 'SPECIAL_MATERIAL_NOT_ALLOWED'
    | 'SPECIAL_AUDIT_LINK_QUOTA_INSUFFICIENT'
    | 'LUXURY_BRAND_MIN_PRICE'
    | 'LUXURY_BRAND_MAX_COMMISSION'
    | 'SPOKEN_SCRIPT_HTML_CHECK_FAIL'
    | 'UNREPORTED_OFF_SHELF_GOODS_DOWN_FAIL'
    | 'OA_REQUEST_FAIL' /*错误码, 视交互情况再定义更多错误码[BizErrorEnum]*/;
  result?: boolean /*提交结果, true:成功, false:失败*/;
};

/**
 *重新提交审核
 */
export const qualificationAuditResubmit = (params: QualificationAuditResubmitRequest) => {
  return Fetch<ResponseWithResult<QualificationAuditResubmitResult>>(
    '/pim/public/qualificationAudit/resubmit',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationAudit/resubmit') },
    },
  );
};

export type CheckHighRiskLevelSelectionRoundRequest = {
  id?: string /*审核队列id*/;
};

export type CheckHighRiskLevelSelectionRoundResult = {
  waitLiveSelectionRoundList?: Array<{
    id?: string /*关联记录主键*/;
    liveDate?: string /*直播间名称*/;
    liveRoomId?: string /*直播间Id*/;
    liveRoomName?: string /*直播间名称*/;
    selectionRoundNo?: string /*场次货盘编号*/;
    spuNo?: string /*商品编号*/;
    version?: number /*版本号*/;
  }> /*待直播选品数据*/;
};

/**
 *已审核数据高风险校验
 */
export const checkHighRiskLevelSelectionRound = (
  params: CheckHighRiskLevelSelectionRoundRequest,
) => {
  return Fetch<ResponseWithResult<CheckHighRiskLevelSelectionRoundResult>>(
    '/pim/public/qualificationAudit/checkHighRiskLevelSelectionRound',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/pim/public/qualificationAudit/checkHighRiskLevelSelectionRound'),
      },
    },
  );
};

export type NewQualificationAuditResubmitRequest = {
  auditOpinion?: string /*法务意见备注*/;
  automatedAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING'
    | 'PASS_SPECIAL' /*自动化审核结果[SupplierQualificationAuditResultEnum]*/;
  automatedSnapshotId?: string /*自动化审核id*/;
  brandAutomatedAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING' /*品牌自动化审核结果[BrandQualificationAuditResultEnum]*/;
  brandAutomatedSnapshotId?: string /*品牌自动化审核id*/;
  details?: Array<{
    auditOpinion?: string /*审核意见*/;
    auditState?:
      | 'PASS'
      | 'NO_PASS'
      | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
    bizType?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'GOODS'
      | 'SHOP'
      | 'BP_BRAND'
      | 'BP_GOODS'
      | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
    isBrandWhitelisted?: boolean /*是否白名单B*/;
    itemVersionId?: string /*资质项版本ID*/;
    remark?: string /*备注*/;
  }> /*审核请求明细*/;
  expirationDate?: string /*法务审核有效期*/;
  expirationDates?: Array<{
    bizType?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'GOODS'
      | 'SHOP'
      | 'BP_BRAND'
      | 'BP_GOODS'
      | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
    expirationDate?: string /*法务审核有效期*/;
    itemVersionId?: string /*资质项版本ID*/;
    qualificationsTypeNo?: string /*资质类型编号，默认类型使用枚举值，用户创建系统赋值*/;
  }> /*审核有效期*/;
  goodsAutoAuditResult?:
    | 'WAIT'
    | 'PASS'
    | 'NO_PASS'
    | 'REVIEW_PENDING' /*商品自动化审核结果[GoodsQualificationAuditResultEnum]*/;
  goodsSnapshotId?: string /*商品自动化审核id*/;
  id?: string /*审核记录主键*/;
  isSpecialMaterial?: boolean /*是否特殊材质(1:是,0:否,无默认值)*/;
  isSupplierBodySpecialAuditPass?: boolean /*是否商家主体特批通过*/;
  riskLevel?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*风险等级:高-HIGH,中-MIDDLE,低-LOW,通过-PASS,待处理-NONE[QualificationRiskLevelEnum]*/;
  selectionRoundExtensions?: Array<{
    id?: string /*关联记录主键*/;
    promotionLink?: string /*上播链接*/;
  }> /*场次货盘扩展请求*/;
  trademarkNumber?: string /*商标注册号/申请号*/;
  version?: string /*版本号-乐观锁*/;
  versionId?: string /*资质版本ID*/;
};

export type NewQualificationAuditResubmitResult = {
  auditorName?: string /*当前认领审核人*/;
  errorCode?:
    | 'INVALID_PARAM'
    | 'EXPORT_EMPTY_DATA_ERROR'
    | 'DATA_ERROR'
    | 'UNKNOWN_ERROR'
    | 'DOWNLOAD_ERROR'
    | 'LEAF_KEY_IS_NONE'
    | 'GENERATE_LEAF_ID_ERROR'
    | 'INST_GRANT_ERROR'
    | 'ACCESS_TOKEN_IS_NULL'
    | 'KUAI_SHOU_ACCESS_TOKEN_IS_NULL'
    | 'UPLOAD_TEMPLATE_ERROR'
    | 'UPLOAD_DATA_EMPTY'
    | 'IMPORT_OUT_OF_LIMIT'
    | 'QUALIFICATION_LOCKED'
    | 'QUALIFICATION_AUDITED'
    | 'QUALIFICATION_CHANGED'
    | 'QUALIFICATION_UN_SUPPORT_RESUBMIT'
    | 'ONE_OF_THESE_APTITUDE_MUST_CONFIGURED'
    | 'QUALIFICATION_EXPIRATION_DATE_INVALID'
    | 'CATE_CHECK_ERROR'
    | 'DY_CATE_CHECK_ERROR'
    | 'TB_CATE_CHECK_ERROR'
    | 'INVOKE_IASM_SERVICE_FAIL'
    | 'SELECTION_ROUND_IS_EXISTS'
    | 'LIVE_ROUND_NOT_EXISTS'
    | 'INVOKE_OFTEN'
    | 'DO_CREATE_SELECTION_ROUND_SHOP_ID_IS_NULL'
    | 'DO_CREATE_SELECTION_ROUND_EXIST_DUPLICATES'
    | 'DO_CREATE_SELECTION_ROUND_NOT_IN_INVESTMENT_PLAN_TIME'
    | 'APPLY_CHANNEL_NOT_EQ_INVESTMENT_PLAN_LIVE_ROUND_INVITE'
    | 'EXISTS_BLACKLIST'
    | 'BUSINESS_QUANTITY_LIMIT_REACHED'
    | 'FAVORABLE_RATE_BELOW_STANDARD'
    | 'SHOP_POINT_BELOW_STANDARD'
    | 'EXISTENCE_AUDIT_RULE'
    | 'EXISTENCE_HIT_CONDITION'
    | 'INVOKE_TOOLS_SERVICE_FAIL'
    | 'OA_MAPPING_ERROR'
    | 'LOW_OF_EXPORT_ZERO'
    | 'EXCEEDED_MAX_EXPORT_LIMIT'
    | 'BUSINESS_TERM_DATE_IS_NOT_NULL'
    | 'DEPOSIT_AMOUNT_IS_NOT_NULL'
    | 'SERVICE_TYPE_OPTION_IS_NOT_EXIST'
    | 'QUALIFICATION_WHITE_LIST_CATE_RULE_EXIST'
    | 'CURR_AUDIT_IS_WAIT_AUDIT'
    | 'INVOKE_BIG_DATA_SERVICE_FAIL'
    | 'SHOP_QUALITY_RETURN_RATE_GE_THAN_CONFIG_VALUE'
    | 'BRAND_AUTOMATED_QUALIFICATION_NON_EXIST'
    | 'EXCEEDED_MAX_EXPORT_LIMIT_HUNDRED'
    | 'EXPORT_FAILURE'
    | 'SELECTION_ROUND_ADD_QUOTA_OVER_THE_CONFIG'
    | 'SELECT_GOODS_POOL_ID_OR_NO_IS_EMPTY'
    | 'INEFFICIENCY_RATIO_CONFIG'
    | 'INEFFICIENT_PRODUCTS_PAY_GMV_BELOW_STANDARD'
    | 'GET_SENSITIVE_WORDS_CONFIG_ERROR'
    | 'PAY_GMV_BELOW_STANDARD'
    | 'SENSITIVE_WORDS_CHECK_FAIL'
    | 'DEPT_NAME_ERROR'
    | 'LIVE_ROOM_NAME_ERROR'
    | 'CREATOR_ERROR'
    | 'GMT_CREATED_ERROR'
    | 'MODIFIER_ERROR'
    | 'AUDIT_TIME_ERROR'
    | 'SUPPLIER_NO_ERROR'
    | 'SUPPLIER_NAME_ERROR'
    | 'SAVE_SUPPLIER_BODY_SPECIAL_AUDIT_ERROR'
    | 'CATEGORY_ID_EMPTY'
    | 'WORD_SEGMENTATION_RESULT_INCORRECT'
    | 'AI_ASSIST_AUDIT_IS_NULL'
    | 'SPECIAL_MATERIAL_NOT_ALLOWED'
    | 'SPECIAL_AUDIT_LINK_QUOTA_INSUFFICIENT'
    | 'LUXURY_BRAND_MIN_PRICE'
    | 'LUXURY_BRAND_MAX_COMMISSION'
    | 'SPOKEN_SCRIPT_HTML_CHECK_FAIL'
    | 'UNREPORTED_OFF_SHELF_GOODS_DOWN_FAIL'
    | 'OA_REQUEST_FAIL' /*错误码, 视交互情况再定义更多错误码[BizErrorEnum]*/;
  result?: boolean /*提交结果, true:成功, false:失败*/;
};

/**
 *掉品并通知业务
 */
export const newQualificationAuditResubmit = (params: NewQualificationAuditResubmitRequest) => {
  return Fetch<ResponseWithResult<NewQualificationAuditResubmitResult>>(
    '/pim/public/qualificationAudit/submitAndNoticeBusiness',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/qualificationAudit/submitAndNoticeBusiness') },
    },
  );
};

export type GetTrademarkRegInfoRequest = {
  trademarkNumber?: string /*商标注册号/申请号*/;
};

export type GetTrademarkRegInfoResult = {
  applicant?: string /*申请人/注册人*/;
  brandName?: string /*商标名称*/;
  dataAcquisitionTime?: string /*数据获取时间*/;
  effectiveGroup?: string /*有效群组*/;
  exclusivePermissionPeriod?: string /*商品有效期*/;
  internationalCategory?: string /*国际类别*/;
  trademarkStatus?: string /*商标状态*/;
  trademarkingNumber?: string /*商标注册号/申请号*/;
};

/**
 *获取商标注册证信息-rpa
 */
export const getTrademarkRegInfo = (params: GetTrademarkRegInfoRequest) => {
  return Fetch<ResponseWithResult<GetTrademarkRegInfoResult>>(
    '/befriend-service-goods/public/brandRpa/getTrademarkRegInfo',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/goods/public/brandRpa/getTrademarkRegInfo') },
    },
  );
};

export type RegenerateAiAuditOrGetResultRequest = {
  id?: string /*审核记录主键*/;
  isRegenerate?: boolean /*是否重新生成AI审核不能为空 true 是 false 否 默认false*/;
};

export type RegenerateAiAuditOrGetResultResult = {
  brandLinkAiHandleResult?: string /*品牌链路AI审核结果*/;
  brandLinkAiHandleState?:
    | 'PENDING'
    | 'NO_ACTION_REQUIRED'
    | 'PROCESSING'
    | 'COMPLETED'
    | 'FAILED' /*品牌链路AI审核状态[BrandLinkAiHandleStateEnum]*/;
  brandLinkAiThoughtProcessResult?: string /*品牌链路思考过程结果*/;
  hasCompleteAuthorization?: boolean /*是否完成品牌授权*/;
};

/**
 *重新生成AI审核结果或获取结果
 */
export const regenerateAiAuditOrGetResult = (params: RegenerateAiAuditOrGetResultRequest) => {
  return Fetch<ResponseWithResult<RegenerateAiAuditOrGetResultResult>>(
    '/pim/public/qualificationAudit/regenerateAiAuditOrGetResult',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/pim/public/qualificationAudit/regenerateAiAuditOrGetResult'),
      },
    },
  );
};
