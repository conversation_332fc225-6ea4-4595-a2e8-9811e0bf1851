import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type TrendPredictionProductPageListRequest = {
  brandName?: string /*'品牌名称'*/;
  catePath?: Array<string> /*目路径ID，例如:1->23->456*/;
  current?: number /*当前页码,从1开始*/;
  eliminateHighProportion?: boolean /*是否剔除高占比影响 true:剔除,false:不剔除*/;
  eliminateWhiteCard?: boolean /*是否剔除白牌 true:剔除,false:不剔除*/;
  highPriceEnd?: string /*'最高价结束'*/;
  highPriceStart?: string /*'最高价开始'*/;
  lowPriceEnd?: string /*'商品券后价最低价结束'*/;
  lowPriceStart?: string /*'商品券后价最低价开始'*/;
  ptDateEnd?: string /*推送日期结束查询时间*/;
  ptDateStart?: string /*推送日期开始查询时间*/;
  size?: number /*分页大小*/;
  sortEntityList?: Array<{
    orderBy?: string /*排序字段 gmv:GMV,sale_cnt:销量,ranking:排名*/;
    orderType?: string /*排序方式 asc:升序,desc:降序*/;
  }> /*排序*/;
  trendPredictionWordSegmentationId?: Array<string> /*爆品趋势预测分词id*/;
};

export type TrendPredictionProductPageListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    brandName?: string /*'品牌名称'*/;
    cateId?: number /*类目ID，关联类目表*/;
    cateName?: string /*类目名称路径,例如:一级->二级->三级*/;
    catePath?: string /*目路径ID，例如:1->23->456*/;
    firstTalentGmv?: string /*销量排行第一达人销售额*/;
    firstTalentName?: string /*销量排行第一达人名称*/;
    gmv?: string /*销售额*/;
    gmvGrowthRate?: string /*平均GMV增长率*/;
    goodsId?: string /*商品ID，用于关联商品表*/;
    goodsImage?: string /*商品图片*/;
    goodsName?: string /*商品标题*/;
    highPrice?: string /*'最高价'*/;
    hitRule?: string /*命中规则*/;
    id?: number;
    kolNum?: number /*带货达人数*/;
    liveStreamingSalesNum?: number /*直播带货数*/;
    lowPrice?: string /*'商品券后价最低价'*/;
    productPromotionRate?: string /*达人带货占比(%)*/;
    ptDate?: string /*数据推送时间*/;
    ranking?: number /*排名*/;
    saleCnt?: string /*销量*/;
    saleGrowthRate?: string /*平均销量增长率*/;
    shopName?: string /*店铺名称*/;
    trendExtraction?: string /*趋势提取*/;
    url?: string /*商品链接*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *爆品趋势预测商品分页查询
 */
export const trendPredictionProductPageList = (params: TrendPredictionProductPageListRequest) => {
  return Fetch<ResponseWithResult<TrendPredictionProductPageListResult>>(
    '/pim/public/trendPredictionProductController/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/trendPredictionProductController/page') },
    },
  );
};

export type TrendPredictionProductListRequest = {
  cateIdList?: Array<string> /*爆品趋势预测类目二级类目ids*/;
  ptDateEnd?: string /*推送日期结束查询时间*/;
  ptDateStart?: string /*推送日期开始查询时间*/;
};

export type TrendPredictionProductListResult = {
  trendPredictionWordSegmentationVOS?: Array<{
    id?: number;
    predictionWordEntry?: string /*分词词条*/;
    predictionWordEntryNum?: number /*词条数量*/;
  }> /*爆品趋势预测分词返回值集合*/;
};

/**
 *爆品趋势预测分词查询
 */
export const trendPredictionProductList = (params: TrendPredictionProductListRequest) => {
  return Fetch<ResponseWithResult<TrendPredictionProductListResult>>(
    '/pim/public/trendPredictionProductController/list',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/trendPredictionProductController/list') },
    },
  );
};

export type TrendPredictionProductGetLinkRequest = {
  goodsId?: string /*平台商品id*/;
  id?: string /*商品id*/;
};

export type TrendPredictionProductGetLinkResult = {
  goodsId?: string /*平台商品id*/;
  goodsImage?: string /*商品图片*/;
};

/**
 *爆品趋势预测商品获取主图链接
 */
export const trendPredictionProductGetLink = (params: TrendPredictionProductGetLinkRequest) => {
  return Fetch<ResponseWithResult<TrendPredictionProductGetLinkResult>>(
    '/pim/public/trendPredictionProductController/getLink',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/trendPredictionProductController/getLink') },
    },
  );
};

export type TrendPredictionProductExportRequest = {
  brandName?: string /*'品牌名称'*/;
  catePath?: Array<string> /*目路径ID，例如:1->23->456*/;
  current?: number /*当前页码,从1开始*/;
  eliminateHighProportion?: boolean /*是否剔除高占比影响 true:剔除,false:不剔除*/;
  eliminateWhiteCard?: boolean /*是否剔除白牌 true:剔除,false:不剔除*/;
  highPriceEnd?: string /*'最高价结束'*/;
  highPriceStart?: string /*'最高价开始'*/;
  lowPriceEnd?: string /*'商品券后价最低价结束'*/;
  lowPriceStart?: string /*'商品券后价最低价开始'*/;
  ptDateEnd?: string /*推送日期结束查询时间*/;
  ptDateStart?: string /*推送日期开始查询时间*/;
  size?: number /*分页大小*/;
  sortEntityList?: Array<{
    orderBy?: string /*排序字段 gmv:GMV,sale_cnt:销量,ranking:排名*/;
    orderType?: string /*排序方式 asc:升序,desc:降序*/;
  }> /*排序*/;
  trendPredictionWordSegmentationId?: Array<string> /*爆品趋势预测分词id*/;
};

export type TrendPredictionProductExportResult = string;

/**
 *爆品趋势预测商品-导出
 */
export const trendPredictionProductExport = (params: TrendPredictionProductExportRequest) => {
  return Fetch<ResponseWithResult<TrendPredictionProductExportResult>>(
    '/pim/public/trendPredictionProductController/export',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/trendPredictionProductController/export') },
    },
  );
};
