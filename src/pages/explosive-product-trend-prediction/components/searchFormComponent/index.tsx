import React, { useEffect, useState } from 'react';
import { Button, Form, Icon } from 'antd';
import { GetFieldDecoratorOptions, WrappedFormUtils } from 'antd/es/form/Form';
import './index.less';
import { FormItemProps, FormProps } from 'antd/lib/form';
import { Const } from 'qmkit';
import { SYS_TYPE } from '@/common/constants/moduleConstant';
import styles from './index.module.less';

export interface searchItem {
  span?: number;
  double?: boolean;
  hide?: boolean;
  label?: string;
  hocOptions?: GetFieldDecoratorOptions;
  formItemOptions?: FormItemProps;
  renderNode?: React.ReactNode;
}

export interface searchItemAll extends searchItem {
  key: string;
}

export interface SearchFormProps {
  options?: Record<string, searchItem>;
  source?: searchItemAll[];
  form: WrappedFormUtils;
  loading: boolean;
  showRow?: number;
  rowShowNum?: number;
  onSearch: () => void;
  onReset: () => void;
  formOptions?: FormProps;
  needMore?: boolean;
  noResize?: boolean;
  type?: string;
  getTableHeight?: any;
}

const getLen = (finSource: any[], rowNum = 0) =>
  finSource?.reduce((sum: number, item, index, arr) => {
    if (item.double) {
      sum += 2;
    } else if (item.span) {
      sum += item.span;
    } else {
      sum += 1;
    }
    const next = arr[index + 1];
    const nextNum = next?.span ? next.span : next?.double ? 2 : 1;
    if (
      Math.floor((sum + nextNum) / rowNum) > Math.floor(sum / rowNum) &&
      (sum + nextNum) % rowNum !== 0
    ) {
      sum = Math.floor((sum + nextNum) / rowNum) * rowNum;
    }
    return sum;
  }, 0);

const isBtnMove = (finSource: searchItemAll[], num: number) => {
  const len = getLen(finSource, num);
  let needMoveTop: boolean;
  needMoveTop = len % num === 0;

  return needMoveTop;
};

const SearchFormComponent = (props: SearchFormProps) => {
  const { getFieldDecorator } = props.form;
  const {
    source = [],
    options = {},
    loading,
    onSearch,
    onReset,
    formOptions,
    needMore,
    showRow = 1,
    rowShowNum,
    noResize = false,
    getTableHeight,
    type,
  } = props;
  const finSource = (
    source?.length > 0
      ? source
      : Object.keys(options).map((e) => (options[e] ? { key: e, ...options[e] } : undefined))
  )?.filter((e) => e && !e?.hide);
  const [needMoveTop, setNeedMoveTop] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const [rowNum, setRowNum] = useState(4);
  const finRowNum = rowShowNum || rowNum;
  const finNeedMore = needMore && getLen(finSource) > finRowNum * showRow;
  const [showLength, setShowLength] = useState(4);

  const initSetMoveTop = (rowNumNow: number) => {
    let showNum = rowNumNow - 2,
      isfill = false,
      init = false;
    finSource?.reduce((sum, item, index) => {
      if (item?.double) {
        sum += 2;
      } else if (item?.span) {
        sum += item.span;
      } else {
        sum += 1;
      }
      if (
        sum === rowNumNow * showRow &&
        ((item?.span && item.span > 1) || index === finSource?.length - 1)
      ) {
        !isfill && (showNum = index);
        isfill = true;
      } else if (sum >= rowNumNow * showRow && !init) {
        showNum = index - 1;
        init = true;
      }
      return sum;
    }, 0);
    setShowLength(showNum);
    setNeedMoveTop(isfill ? true : false);
  };

  const handleResize = () => {
    const allWidth = window.outerWidth;
    const delCount = [SYS_TYPE.INVEST, SYS_TYPE.ORGANIZATION].includes(Const.SYS_TYPE) ? 1 : 0;
    setRowNum(4 - delCount);
    if (!needMore)
      return setNeedMoveTop(
        !showMore &&
          isBtnMove(
            finSource as {
              span?: number | undefined;
              double?: boolean | undefined;
              hide?: boolean | undefined;
              label?: string | undefined;
              hocOptions?: GetFieldDecoratorOptions | undefined;
              formItemOptions?: FormItemProps | undefined;
              renderNode?: React.ReactNode;
              key: string;
            }[],
            rowNum,
          ),
      );
    !showMore && initSetMoveTop(rowShowNum || rowNum);
  };

  useEffect(() => {
    handleResize();
  }, [options]);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const items = finSource.map((item, index) => {
    return (
      <div
        className={
          noResize
            ? ''
            : `common-form-item simple-${finRowNum} ${
                (!finNeedMore || (finNeedMore && index <= showLength) || showMore) && 'show'
              } ${(item?.span?.toString() === '2' || item?.double) && `double-${finRowNum}`}`
        }
        key={index}
      >
        <Form.Item
          colon={false}
          {...item?.formItemOptions}
          // style={noResize ? { textAlign: 'left' } : { width: '100%', textAlign: 'left' }}
          label={item?.label}
          labelAlign="right"
        >
          {getFieldDecorator(item!.key, item?.hocOptions)(item?.renderNode)}
        </Form.Item>
      </div>
    );
  });
  const onShowMoreChange = () => {
    setShowMore(!showMore);
    !showMore
      ? setNeedMoveTop(
          isBtnMove(
            finSource as {
              span?: number | undefined;
              double?: boolean | undefined;
              hide?: boolean | undefined;
              label?: string | undefined;
              hocOptions?: GetFieldDecoratorOptions | undefined;
              formItemOptions?: FormItemProps | undefined;
              renderNode?: React.ReactNode;
              key: string;
            }[],
            finRowNum,
          ),
        )
      : initSetMoveTop(finRowNum);
    setTimeout(() => {
      getTableHeight && getTableHeight();
    }, 0);
  };
  return (
    <div className={styles.tableBox}>
      <Form
        layout="inline"
        className="inline-searchform"
        {...formOptions}
        labelAlign="right"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        onSubmit={(e) => {
          e?.preventDefault();
          onSearch();
        }}
      >
        {items}
        <div
          className={`common-form-item simple-${finRowNum} show search-btn ${
            finNeedMore ? undefined : styles['other-btn']
          }`}
        >
          <Form.Item
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            className={`${needMoveTop && finNeedMore && showMore ? 'move-top' : undefined} ${
              styles['lableBtn']
            }`}
            style={{
              marginRight: 0,
              width: [SYS_TYPE.INVEST, SYS_TYPE.ORGANIZATION].includes(Const.SYS_TYPE)
                ? '180px'
                : '',
              height: '40px',
            }}
            label={<div style={{ height: 20 }}></div>}
          >
            <Button
              loading={loading}
              type="primary"
              //onClick={() => onSearch()}
              htmlType="submit"
            >
              查询
            </Button>
            <Button
              loading={loading}
              onClick={() => {
                onReset();
              }}
              style={{ borderColor: '#999999', color: '#444444' }}
            >
              重置
            </Button>
            {finNeedMore && (
              <a onClick={onShowMoreChange} className="common-form-item-more">
                {showMore ? '收起' : '更多'}{' '}
                <Icon style={{ fontSize: '9px' }} type={showMore ? 'up' : 'down'} />
              </a>
            )}
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default SearchFormComponent;
