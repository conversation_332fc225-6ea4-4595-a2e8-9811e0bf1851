import { ColumnProps } from 'antd/lib/table';
import React, { useMemo } from 'react';
import moment from 'moment';
import { toDecimal } from '@/utils/string';
import { Icon, message, Popover } from 'antd';
import PopoverRowText from '@/components/PopoverRowText';
import { copyText } from '@/utils/moduleUtils';
import { TrendPredictionProductPageListInfoType } from './hook';
import FileUpload from '@/components/FileUpload';
import RefreshImage from '../components/RefreshImage';
import { TrendPredictionProductGetLinkResult } from '../services';
import copyPng from '@/assets/copy.png';
import copy from 'copy-to-clipboard';

export const formatFee = (val?: string | number | null) => {
  if (val !== '' && val !== null && val !== undefined) {
    return toDecimal(Number(val), 2);
  }
  return '-';
};

export const useTable = (
  refreshImage: (id?: string) => Promise<TrendPredictionProductGetLinkResult | null | undefined>,
) => {
  const columns = useMemo<ColumnProps<TrendPredictionProductPageListInfoType>[]>(
    () => [
      {
        dataIndex: 'sortColumn',
        // title: '#',
        width: 60,
        render: (_, record, index) => index + 1,
      },

      {
        dataIndex: 'goodsImage',
        title: '图片',
        width: 150,
        render: (val: string, record) =>
          val ? (
            <FileUpload
              value={val ? [val] : []}
              maxLen={1}
              typeCode="SPU_QUALIFICATION"
              isImage
              maxSize={20 * 1024 * 1024}
              multiple
              accept={'.jpg,.jpeg,.png'}
              disabled={true}
            />
          ) : (
            <RefreshImage refreshImage={refreshImage} id={record?.id?.toString()} />
          ),
      },
      {
        title: '商品信息',
        key: 'goodsMsg',
        dataIndex: 'goodsMsg',
        width: 204,
        render: (_, records) => {
          return (
            <div>
              <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                {/* 显示详情 */}
                <Popover title="商品名称" content={records?.goodsName}>
                  <p style={{ marginRight: '4px' }}>
                    【{records?.brandName || '-'}】{records?.goodsName || '-'}
                  </p>
                </Popover>
                <Popover
                  title="商品信息"
                  content={
                    <div>
                      <p>
                        平台商品ID: {records?.goodsId || '-'}
                        <Icon
                          onClick={() => {
                            copyText(records?.goodsId || '');
                          }}
                          type="copy"
                          style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                        />
                      </p>
                    </div>
                  }
                >
                  <img
                    src={
                      'https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images/icon/id.png'
                    }
                    style={{ width: '16px', height: '16px', marginLeft: '4px' }}
                    alt=""
                  />
                </Popover>
              </div>
            </div>
          );
        },
      },
      {
        title: '商品链接',
        width: 150,
        dataIndex: 'url',
        render: (val) =>
          val ? (
            <section
              style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'flex-start' }}
            >
              {/* ai生成 */}
              <Popover content={val} title="商品链接">
                <a
                  href={val}
                  target="_blank"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    wordBreak: 'break-all',
                    maxWidth: '120px',
                  }}
                >
                  {val}
                </a>
              </Popover>
              {/* 2024年7月2日 开山ai结尾共生成14行代码 */}
              <img
                src={copyPng}
                style={{ width: '16px', height: '16px', marginLeft: '4px', cursor: 'pointer' }}
                alt=""
                onClick={() => {
                  copy(val);
                  message.success('复制成功');
                }}
              />
            </section>
          ) : (
            '-'
          ),
      },
      {
        title: '店铺信息',
        width: 150,
        dataIndex: 'shopName',
        render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
      },
      {
        title: '商品类目',
        width: 150,
        dataIndex: 'cateName',
        render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
      },
      {
        title: 'GMV',
        width: 150,
        dataIndex: 'gmv',
        render: (val) => formatFee(val),
        sorter: true,
      },

      {
        title: '销量',
        width: 100,
        dataIndex: 'sale_cnt',
        render: (_, record) => formatFee(record?.saleCnt),
        sorter: true,
      },
      {
        title: '排名',
        width: 150,
        dataIndex: 'ranking',
        render: (val) => val ?? '-',
        sorter: true,
      },
      // {
      //   title: '券后最低价',
      //   width: 150,
      //   dataIndex: 'lowPrice',
      //   render: (val) => formatFee(val),
      // },
      {
        title: '售价',
        width: 150,
        dataIndex: 'highPrice',
        render: (val) => formatFee(val),
      },
      {
        title: '命中规则',
        width: 200,
        dataIndex: 'hitRuleName',
        render: (val) => val ?? '-',
      },
      {
        title: '平均销量增长率',
        width: 150,
        dataIndex: 'saleGrowthRate',
        render: (val) => (isNaN(val) || val === null ? '-' : val + '%'),
      },
      {
        title: '平均GMV增长率',
        width: 150,
        dataIndex: 'gmvGrowthRate',
        render: (val) => (isNaN(val) || val === null ? '-' : val + '%'),
      },

      {
        title: '趋势提取',
        width: 200,
        dataIndex: 'trendExtraction',
        render: (val) =>
          val ? (
            <article
              style={{ whiteSpace: 'pre-wrap' }}
              dangerouslySetInnerHTML={{ __html: val }}
            ></article>
          ) : (
            '-'
          ),
      },
      {
        title: '直播带货数',
        width: 150,
        dataIndex: 'liveStreamingSalesNum',
        render: (val) => val ?? 0,
      },
      {
        title: '带货达人数',
        width: 180,
        dataIndex: 'kolNum',
        render: (val) => val ?? 0,
      },
      {
        title: '达人带货占比（抖音）',
        width: 280,
        dataIndex: 'productPromotionRate',
        render: (val) => (val ? val + '%' : '0%'),
      },
      {
        title: '数据推送时间',
        width: 150,
        dataIndex: 'ptDate',
        render: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
    ],
    [refreshImage],
  );
  return { columns };
};
