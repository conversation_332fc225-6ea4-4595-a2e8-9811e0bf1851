import { Button, Form, message, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { AuthWrapper, checkAuth, history } from 'qmkit';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PaginationProxy from '@/common/constants/Pagination';
import PageLayout from '@/components/PageLayout/index';
import styles from './index.module.less';
import { useTableHeight } from '@/common/constants/hooks/index';
import SearchForm from './components/SearchForm';
import { useTable } from './utils/getColumns';
import { SearchType, useList } from './utils/hook';

import { PaginationConfig, SorterResult } from 'antd/lib/table';
import { useLocation } from 'react-router-dom';
import SortColumnTable from '@/components/SortColumnTable';
import { SortBizTypeEnum } from '@/components/SortColumnTable/type';
import HotWordsBox from './components/HotWord';
import { trendPredictionProductGetLink } from './services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import download from '@/assets/download.png';
import { ExportModal } from 'web-common-modules/biz';
import { trendPredictionProductExport } from './services';

const AnchorInformation: React.FC<FormComponentProps<SearchType>> = ({ form }) => {
  const {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
    setSelectWords,
    selectWords,
    priedictionList,
    setList,
  } = useList(form);
  const [selectedKeys, setSelectKeys] = useState<string[]>([]);

  const onReset = () => {
    form.resetFields();
    setSelectKeys([]);
    getList({
      current: 1,
      size: 20,
    });
  };
  const onRefresh = () => {
    getList({});
    setSelectKeys([]);
  };
  useEffect(() => {
    onRefresh();
  }, []);
  const { getHeight, tableHeight } = useTableHeight(100);

  const handleTableChange = (
    pagination: PaginationConfig,
    filters: Partial<Record<keyof any, string[]>>,
    sorter: SorterResult<any>,
  ) => {
    const sortMap = {
      ascend: 'asc',
      descend: 'desc',
    };
    console.log(sorter, filters);
    // sortOrder?:string/*排序：类型：asc=升序，desc=降序*/,
    const sortObj = {
      orderBy: sorter.field,
      orderType: sorter?.order ? sortMap[sorter.order] : undefined,
    };
    getList(
      {
        sortEntityList: sortObj?.orderBy && sortObj?.orderType ? [sortObj] : undefined,
      },
      true,
    );
    setSelectKeys([]);
  };
  const location = useLocation();
  useEffect(() => {
    if (location?.pathname === '/marketing-data') {
      onRefresh();
    }
  }, [location?.pathname]);

  // const handleSelectWord = (words: TrendPredictionProductListInfoType[]) => {
  //   setSelectWords(words);
  // };
  const refreshImage = useCallback(
    async (id?: string) => {
      const { res } = await trendPredictionProductGetLink({ id });
      const result = res?.result;
      if (res?.code !== '200') {
        message.error(res?.message || '刷新失败');
        return;
      }
      if (result?.goodsImage) {
        message.success('刷新成功');
        setList(
          list?.map?.((item) =>
            item?.id === id ? { ...item, goodsImage: result?.goodsImage } : item,
          ) ?? [],
        );
        return result;
      }
      message.error('刷新失败');
      return result;
    },
    [list],
  );
  const { columns } = useTable(refreshImage);
  return (
    <PageLayout
      className={styles['cooperation-report-contain']}
      routePath="/explosive-product-trend-prediction"
    >
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        {checkAuth('f_explosive-product-trend-prediction_list') && (
          <div className="formHeight">
            <SearchForm
              form={form}
              loading={loading}
              onSearch={() => {
                getList({ current: 1 });
                setSelectKeys([]);
              }}
              onReset={onReset}
            />

            <div className="flex items-center mb-16">
              <HotWordsBox
                hotWords={priedictionList!}
                // onSelectWords={handleSelectWord}
                multiSelect={true}
                selectedIds={selectWords}
                setSelectedIds={setSelectWords}
              />
            </div>
            <div className="flex items-center mb-16">
              <AuthWrapper functionName="f_explosive-product-trend-prediction_export">
                <ExportModal
                  condition={condition}
                  requestFunc={trendPredictionProductExport}
                  content={
                    <div>
                      <p>
                        <span>导出完成后可在</span>
                        <a
                          href="javascript:;"
                          onClick={() => {
                            //
                          }}
                        >
                          导出记录
                        </a>
                        <span>中查看并下载。</span>
                      </p>
                    </div>
                  }
                  onOk={() => null}
                >
                  <Button className="mr-8" style={{ borderColor: '#999999', color: '#444444' }}>
                    <img
                      src={download}
                      style={{
                        width: '14px',
                        height: '14px',
                        verticalAlign: 'sub',
                        marginRight: '6px',
                        marginTop: '-2px',
                      }}
                    />
                    导出
                  </Button>
                </ExportModal>
              </AuthWrapper>
              <AuthWrapper functionName="f_explosive-product-trend-prediction_export_logs">
                <Button
                  className="mr-8"
                  style={{ borderColor: '#999999', color: '#444444' }}
                  onClick={() => {
                    history.push(
                      '/export-list-explosive-product-trend-prediction?configCode=TREND_PREDICTION_PRODUCT_EXPORT',
                    );
                  }}
                >
                  导出记录
                </Button>
              </AuthWrapper>
            </div>
          </div>
        )}

        <div className={styles.boardTable} style={{ flex: 1 }}>
          <AuthWrapper functionName="f_explosive-product-trend-prediction_list">
            <SortColumnTable
              disSortColumns={['index']}
              disabledColumns={['sortColumn']}
              rowKey="id"
              loading={loading}
              columns={columns}
              dataSource={list}
              pagination={false}
              scroll={{ y: tableHeight, x: '100%' }}
              onChange={handleTableChange}
              bizType={SortBizTypeEnum.EXPLOSIVE_PRODUCT_TREND_PREDICTION}
              setLoading={setLoading}
            />
          </AuthWrapper>
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <AuthWrapper functionName="f_explosive-product-trend-prediction_list">
            <PaginationProxy
              current={pagination?.current}
              pageSize={pagination?.size}
              total={pagination?.total}
              // @ts-ignore
              onChange={(current, size) => {
                setSelectKeys([]);
                getList({
                  current,
                  size,
                });
              }}
              valueType="flatten"
              pageSizeOptions={['5', '10', '20', '50', '100']}
            />
          </AuthWrapper>
        </div>
      </div>
    </PageLayout>
  );
};

export default Form.create()(AnchorInformation);
