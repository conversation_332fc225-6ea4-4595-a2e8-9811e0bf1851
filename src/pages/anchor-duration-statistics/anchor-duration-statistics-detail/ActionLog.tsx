import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Table, Input, Form, Popover } from 'antd';
import { durationActionLog, DurationActionLogRequest } from '../services/index';
import PopoverRowText from '@/components/PopoverRowText/index';

import Title from './Title';
type propsType = {
  form?: any;
  info?: any;
  type?: string;
};

import moment from 'moment';
const Gift: React.FC<propsType> = (props) => {
  const { type, info, form, entry } = props;
  const [dataSource, setDataSource] = useState([]);
  useEffect(() => {
    if (info?.id) {
      const params: DurationActionLogRequest = {
        bizOrderId: info.id,
        bizOrderType: 'LIVE_SCHEDULE',
      };
      durationActionLog(params)
        .then(({ res }) => {
          console.log(res);
          if (res.code === '200') {
            if (res.result && res.result.length) {
              const arr = res.result || [];
              arr?.forEach((item, index) => {
                item.index = index + 1;
              });
              setDataSource(arr);
            } else {
              setDataSource([]);
            }
          } else {
            setDataSource([]);
          }
        })
        .catch(() => {
          setDataSource([]);
        });
      // setDataSource(arr.length ? [...arr] : []);
    }
  }, [info?.id]);
  const adjustTypeEnum = {
    ADJUST_SERVICE_FEE: '调整基础服务费',
    ADJUST_CHANGE_BINDING: '调整解绑支付单',
  };
  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (t) => t || '-',
      width: 50,
    },
    {
      title: '操作内容',
      dataIndex: 'operatorContent',
      key: 'operatorContent',
      render: (t) => {
        return <PopoverRowText text={t} />;
      },
      width: 300,
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      align: 'right',
      render: (t) => t || '-',
      width: 150,
    },

    {
      title: '操作时间',
      dataIndex: 'operateTime',
      key: 'operateTime',
      align: 'right',
      width: 150,
      render: (t) => moment(t).format('YYYY-MM-DD HH:mm:ss') || '-',
    },
  ];
  return (
    <Descriptions style={{ marginBottom: 16 }}>
      <Descriptions.Item label="" span={3}>
        <Table
          rowClassName="skuTable-row"
          // style={
          //   dataSource?.length > 5
          //     ? { height: '440px', overflowY: 'scroll', textAlign: 'center' }
          //     : { textAlign: 'center' }
          // }
          scroll={{ y: 500 }}
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </Descriptions.Item>
    </Descriptions>
  );
};
export default Gift;
