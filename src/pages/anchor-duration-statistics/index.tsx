import React, { useEffect, useState } from 'react';
import styles from './index.module.less';
import { AuthWrapper, history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import PaginationProxy from '@/common/constants/Pagination';
import { Button, Table, message, Modal, Icon } from 'antd';
import SearchForm from './components/SearchForm';
import { getColumns } from './utils/getColumns';
import Form, { FormComponentProps } from 'antd/lib/form';
import { useTableHeight } from '@/common/constants/hooks';
import ImportModal from './components/importModal';
import { LiveScheduleListInfoType, useList } from './utils/hook';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  liveScheduleAudit,
  liveScheduleDelete,
  liveScheduleRevocation,
  liveScheduleSubmit,
  liveSchedulePushCost,
  liveScheduleVoidForce,
  LiveScheduleBatchPassRequest,
  liveScheduleBatchPass,
  liveScheduleBatchSubmit,
  LiveScheduleBatchSubmitRequest,
  LiveScheduleBatchSubmitResult,
  LiveScheduleBatchPassResult,
  getOnePageForFinanceAuth,
  GetOnePageForFinanceAuthRequest,
  liveScheduleExport,
  LiveScheduleExportRequest,
  updateCostInfoReject,
  UpdateCostInfoRejectRequest,
} from './services';
import { Moment } from 'moment';
import ResShowInfo from './components/ResShowInfo';
import BatchChangeFields from './components/BatchChangeFields';
import { useLocation } from 'react-router-dom';
import EditMoneyModal from './components/EditMoneyModal';
import { PaginationConfig } from 'antd/lib/pagination';
import { SorterResult } from 'antd/lib/table';
import { SORT_ENUM } from './utils/enum';
import { handlePrecision } from '@/common/common';
export type FormType = {
  buId?: string /*所属事业部id*/;
  costType?: string /*费用种类 枚举：直播 live、短视频 video*/;
  liveRoomId?: string /*达人账号,直播间ID*/;
  planLiveDate?: Moment[] /*预计直播日期*/;
  search?: string /*主播名称/编号*/;
  statusList?: number[] /*流程状态，1.排期中、2.记录中、3.待审核、4.已驳回、5.审核通过*/;
};
const AnchorDurationStatistics: React.FC<FormComponentProps<FormType>> = ({ form }) => {
  const { loading, getList, pagination, list, condition, dataSummary, setSortField } =
    useList(form);
  const [visible, setVisible] = useState(false);
  const [info, setInfo] = useState<LiveScheduleListInfoType>();
  const { confirm } = Modal;
  // 列表合计
  const location = useLocation();

  useEffect(() => {
    getList({});
    setSelectedKeys([]);
    setSelectedFullKeys([]);
  }, [location]);

  const onReset = () => {
    form.resetFields();
    setSelectedKeys([]);
    setSelectedFullKeys([]);
    getList({
      current: 1,
      size: 20,
    });
  };
  const refresh = () => {
    setInfo(undefined);
    setSelectedKeys([]);
    setSelectedFullKeys([]);
    getList({});
    setBatchFildsInfo({ list: [] });
  };
  const handleDelete = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveScheduleDelete,
      params: { id },
    });
    if (result) {
      refresh();
      message.success('操作成功');
    }
  };
  //作废
  const handleVoidForce = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveScheduleVoidForce,
      params: { id },
    });
    if (result) {
      refresh();
      message.success('操作成功');
    }
  };
  //推送成本
  const handlePushCost = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveSchedulePushCost,
      params: { id },
    });
    if (result) {
      refresh();
      message.success('操作成功');
    }
  };
  const handlePass = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveScheduleAudit,
      params: { id, status: 0 },
    });
    if (result) {
      refresh();
      message.success('操作成功');
    }
  };
  const handleReject = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveScheduleAudit,
      params: { id, status: 1 },
    });
    if (result) {
      refresh();
      message.success('操作成功');
    }
  };
  const handleWithdraw = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveScheduleRevocation,
      params: { id },
    });
    if (result) {
      refresh();
      message.success('操作成功');
    }
  };
  const handleSubmit = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveScheduleSubmit,
      params: { id },
    });
    if (result) {
      refresh();
      message.success('操作成功');
    }
  };

  const { getHeight, tableHeight } = useTableHeight(80);
  const goRecord = (id?: string) => {
    history.push(`/anchor-duration-statistics-record?id=${id}`);
  };
  const handleGoCreate = (id?: string) => {
    history.push(`/anchor-duration-statistics-create${id ? `?id=${id}` : ''}`);
  };
  const handleGoCopy = (value?: Record<string, any>) => {
    console.log('value', value);
    history.push({
      pathname: '/anchor-duration-statistics-create',
      state: value,
    });
  };
  const handleGoDetail = (recordCode?: string) => {
    history.push(`/anchor-duration-statistics-detail${recordCode ? `?no=${recordCode}` : ''}`);
  };
  const [selectedKeys, setSelectedKeys] = useState<Array<string>>([]);
  const [selectedFullKeys, setSelectedFullKeys] = useState<Array<any>>([]);
  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedKeys(selectedRowKeys);
      setSelectedFullKeys(selectedRows);
    },
    columnWidth: 80,
    selectedRowKeys: selectedKeys,
  };
  const [batchResInfo, setBatchResInfo] = useState<LiveScheduleBatchPassResult>();

  const batchAction = (value: 'submit' | 'pass' | 'nonCostFields' | 'reject') => {
    const type = {
      submit: '批量提交主播管理审批',
      pass: '批量审批通过',
      nonCostFields: '批量修改非成本字段',
      reject: '批量提交结果',
    };
    setBatchResInfo(undefined);
    if (selectedFullKeys.length) {
      const recordCodeList: Array<string> = [];
      selectedFullKeys.forEach((i) => {
        // [3].includes(record.status)
        if ([2, 4].includes(i.status) && value === 'submit') {
          recordCodeList.push(i.recordCode);
        }
        if ([3].includes(i.status) && value === 'pass') {
          recordCodeList.push(i.recordCode);
        }
        if (['AUDIT_PASS'].includes(i.costStatus) && value === 'reject') {
          recordCodeList.push(i.recordCode);
        }
      });
      if (['submit', 'pass', 'reject'].includes(value)) {
        confirm({
          title: `当前选中数据${selectedFullKeys.length}条，其中${recordCodeList.length}条流程可${type[value]}，请确认是否操作`,
          icon: <Icon type="exclamation-circle" theme="filled" style={{ color: '#FAAD14' }} />,
          onOk() {
            if (recordCodeList.length) {
              if (value === 'submit') {
                batchSubmit(recordCodeList);
              }
              if (value === 'pass') {
                batchPass(recordCodeList);
              }
              if (value === 'reject') {
                batchReject(recordCodeList);
              }
            }
          },
          onCancel() {
            console.log('Cancel');
          },
        });
      }
      if (['nonCostFields'].includes(value)) {
        batchChangeNonCostFields(selectedFullKeys);
      }
    } else {
      if (['nonCostFields'].includes(value)) {
        message.warn('请勾选需要变更非成本字段的数据');
      } else {
        message.warn('请选择数据');
      }
    }
  };

  const batchSubmit = async (value: Array<string>) => {
    try {
      const params: LiveScheduleBatchSubmitRequest = { recordCodeList: value };
      const { res } = await liveScheduleBatchSubmit(params);
      if (res.code === '200') {
        setBatchResInfo(res?.result);
        setSelectedFullKeys([]);
        setSelectedKeys([]);
        refresh();
      }
    } catch {
      console.log('批量提交');
    }
  };
  const batchPass = async (value: Array<string>) => {
    try {
      const params: LiveScheduleBatchPassRequest = { recordCodeList: value };
      const { res } = await liveScheduleBatchPass(params);
      if (res.code === '200') {
        setBatchResInfo(res?.result);
        setSelectedFullKeys([]);
        setSelectedKeys([]);
        refresh();
      }
    } catch {
      console.log('批量提交');
    }
  };
  const [batchFildsInfo, setBatchFildsInfo] = useState<{
    list: Array<any>;
  }>({
    list: [],
  });
  const batchChangeNonCostFields = (value) => {
    console.log('batchChangeNonCostFields', value);
    setBatchFildsInfo({
      list: value,
    });
  };
  const batchReject = async (value: Array<string>) => {
    //等接口
    try {
      const params: UpdateCostInfoRejectRequest = {
        recordCodeList: value,
        expenseTypeEnum: 'LIVE_SCHEDULE',
      };
      const { res } = await updateCostInfoReject(params);
      if (res.code === '200') {
        setBatchResInfo(res?.result);
        setSelectedFullKeys([]);
        setSelectedKeys([]);
        refresh();
      }
    } catch {
      console.log('批量提交');
    }
  };
  const exportList = async (params: LiveScheduleExportRequest) => {
    const result = await responseWithResultAsync({
      request: liveScheduleExport,
      params,
    });
    if (result) {
      message.success('导出成功');
    }
  };

  const handleTableChange = (
    pagination: PaginationConfig,
    filters: any,
    sorter: SorterResult<LiveScheduleListInfoType>,
  ) => {
    console.log('sorter.order', sorter.order);
    const sortValue = SORT_ENUM?.[sorter.order] ?? undefined;
    console.log(sortValue);
    setSelectedKeys([]);
    setSelectedFullKeys([]);
    switch (sorter?.columnKey) {
      case 'realLiveDate':
        setSortField({
          actualLiveCount: sortValue,
          actualLiveEnd: undefined,
          actualLiveStart: undefined,
        });
        break;
      case 'realLiveStartTime':
        setSortField({
          actualLiveCount: undefined,
          actualLiveEnd: undefined,
          actualLiveStart: sortValue,
        });
        break;
      case 'realLiveEndTime':
        setSortField({
          actualLiveCount: undefined,
          actualLiveEnd: sortValue,
          actualLiveStart: undefined,
        });
    }
  };
  return (
    <PageLayout
      className={styles['cooperation-report-contain']}
      routePath="/anchor-duration-statistics"
    >
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            form={form}
            loading={loading}
            onSearch={() => {
              setSelectedKeys([]);
              setSelectedFullKeys([]);
              getList({ current: 1 });
            }}
            onReset={onReset}
          />

          <div className="flex items-center mb-16">
            <AuthWrapper functionName="f_anthor_duration_add">
              <Button
                type="primary"
                icon="plus"
                onClick={() => {
                  handleGoCreate();
                }}
              >
                排期
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anthor_duration_import">
              <ImportModal onRefresh={getList} />
            </AuthWrapper>{' '}
            <AuthWrapper functionName="f_anthor_duration_submit">
              <Button
                className="ml-8"
                onClick={() => {
                  batchAction('submit');
                }}
              >
                提交
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anthor_duration_pass">
              <Button
                className="ml-8"
                onClick={() => {
                  batchAction('pass');
                }}
              >
                通过
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anthor_duration_export">
              <Button
                className="ml-8"
                onClick={() => {
                  exportList({ idList: selectedKeys, ...condition });
                }}
              >
                <span className="iconfont icon-daochu"></span>
                导出
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anthor_duration_export_record">
              <Button
                className="ml-8"
                onClick={() => {
                  history.push(
                    '/export-list-anchor-duration-statistics?configCode=LIVE_SCHEDULE_EXPORT',
                  );
                }}
              >
                导出记录
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anthor_duration_non_cost_fields">
              <Button
                className="ml-8"
                onClick={() => {
                  batchAction('nonCostFields');
                }}
              >
                非成本字段
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anthor_duration_reject_batch">
              <Button
                className="ml-8"
                onClick={() => {
                  batchAction('reject');
                }}
              >
                审核驳回
              </Button>
            </AuthWrapper>
          </div>
        </div>
        <div className={styles.boardTable} style={{ flex: 1 }}>
          <AuthWrapper functionName="f_anthor_duration_list">
            <Table
              rowKey="id"
              loading={loading}
              columns={getColumns({
                onRefresh: refresh,
                onDelete: handleDelete,
                onReject: handleReject,
                onPass: handlePass,
                onWithDraw: handleWithdraw,
                onSubmit: handleSubmit,
                goRecord: goRecord,
                goCreate: handleGoCreate,
                goDetail: handleGoDetail,
                onVoidForce: handleVoidForce,
                onPushCost: handlePushCost,
                setInfo: setInfo,
                handleGoCopy: handleGoCopy,
              })}
              dataSource={list}
              pagination={false}
              scroll={{ y: tableHeight, x: '100%' }}
              rowSelection={rowSelection}
              onChange={handleTableChange}
            />
          </AuthWrapper>
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <AuthWrapper functionName="f_anthor_duration_list">
            <PaginationProxy
              current={pagination?.current}
              pageSize={pagination?.size}
              total={pagination?.total}
              // @ts-ignore
              onChange={(current, size) => {
                getList({
                  current,
                  size,
                });
              }}
              valueType="flatten"
            />
          </AuthWrapper>
        </div>
        {/* <AuthWrapper functionName="f_selection_flow_board_bottom_total"> */}
        <div style={{ position: 'absolute', bottom: '20px', zIndex: '100' }}>
          <p className={styles.totalP}>
            结算金额:
            <span className={styles.totalSpan}>
              &yen;
              {dataSummary?.settlementAmount
                ? handlePrecision(dataSummary?.settlementAmount, 2, 1) + '元'
                : 0}
            </span>
          </p>
          <p className={styles.totalP}>
            结算时长:
            <span className={styles.totalSpan}>
              &yen;{dataSummary?.chargingDuration ? dataSummary?.chargingDuration + 'h' : 0}
            </span>
          </p>
        </div>
        {/* </AuthWrapper> */}
        <ResShowInfo info={batchResInfo}></ResShowInfo>
        <BatchChangeFields info={batchFildsInfo} getList={refresh}></BatchChangeFields>
        <EditMoneyModal info={info} getList={refresh}></EditMoneyModal>
      </div>
    </PageLayout>
  );
};

export default Form.create()(AnchorDurationStatistics);
