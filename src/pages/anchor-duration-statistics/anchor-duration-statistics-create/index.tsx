import { useSetState } from 'ahooks';
import { Button, Icon, message, Modal, Spin } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useEffect, useMemo } from 'react';
import BasicForm from './BasicForm';
import VenuetForm from './VenuetForm';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  liveScheduleAdd,
  LiveScheduleAddRequest,
  liveScheduleDetail,
  LiveScheduleDetailResult,
  liveScheduleEdit,
} from '../services';
import moment from 'moment';
import { formatConcatDate } from '../utils/enum';
import { PageLayout } from 'web-common-modules/components';
import Space from 'web-common-modules/antd-pro-components/Space';
import styles from './index.module.less';
import { getQueryParams } from '@/pages/anchor-commercial-order/utils/utils';
import { history } from 'qmkit';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Card, FormBottomCard, FormContentLayout } from '@/components/DetailFormCompoments';
import LivescheTable from '../anchor-duration-statistics-record/LivescheTable';
type PropsType = FormComponentProps<FormType>;
type ModalState = {
  loading?: boolean;
  detail?: LiveScheduleDetailResult;
};
type FormType = {
  anchorCode?: string /*主播编号*/;
  anchorName?: string /*主播名称*/;
  anchorRating?: string /*主播评级 枚举：S 、A 、B+ 、B 、C+、 C 、D 、E*/;
  anchorType?: string /*艺人类别 枚举：内部internal、外部external*/;
  assistServiceFee?: string /*助播服务费*/;
  deptInfo?: { label?: string; key?: string };
  costType?: string /*费用种类 枚举：直播 live、短视频 video*/;
  creator?: string /*创建人*/;
  liveFlight?: string /*直播班次 枚举：早场 morning、晚场 evening、整场 full*/;
  liveRole?: string /*直播角色 枚举：主播 primary、副主播 secondary 、助播 assist、试播 try、单播主播 single */;
  liveRoomInfo?: { label?: string; key?: string };
  mainServiceFee?: string /*主/副播服务费*/;
  modifier?: string /*修改人*/;
  planLiveDate?: string /*预计直播日期*/;
  planLiveEndTime?: string /*预计直播结束时间*/;
  planLiveStartTime?: string /*预计直播开始时间*/;
  realLiveDate?: string /*实际直播日期*/;
  realLiveEndTime?: string /*实际直播结束时间*/;
  realLiveStartTime?: string /*实际直播开始时间*/;
  singleServiceFee?: string /*单播服务费*/;
  originServiceFee?: string /*溯源服务费*/;
  tryServiceFee?: string /*试播服务费*/;
  matchAreaControllerInfo?: { label?: string; key?: string };
};
interface LOCATION_TYPE {
  location: {
    state: Record<string, any>;
  };
}
const AnchorDurationStatisticsCreate: React.FC<PropsType & LOCATION_TYPE> = ({
  form,
  location: { state },
}) => {
  console.log('state', state);
  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const id = useMemo(() => getQueryParams().id, []);
  const { delRoutetag } = useCloseAndJump();
  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };
  const initPage = async () => {
    setState((state) => ({ ...state, loading: true }));
    const result = await responseWithResultAsync({
      request: liveScheduleDetail,
      params: { id: id || state?.id },
    });
    setState((state) => ({ ...state, detail: result ?? undefined, loading: false }));
    if (result) {
      initForm(result);
    }
  };
  const handleCreate = async (values: FormType) => {
    setState((state) => ({ ...state, loading: true }));
    const params = {
      ...values,
      liveRoomId: values.liveRoomInfo?.key,
      liveRoomName: values?.liveRoomInfo?.label,
      buId: values.deptInfo?.key,
      buName: values?.deptInfo?.label,
      planLiveDate: moment(values?.planLiveDate).format('YYYY-MM-DD'),
      planLiveEndTime: moment(values.planLiveEndTime).format('YYYY-MM-DD HH:mm:ss'),
      planLiveStartTime: moment(values.planLiveStartTime).format('YYYY-MM-DD HH:mm:ss'),
      matchAreaControllerId: values?.matchAreaControllerInfo?.key,
      matchAreaController: values?.matchAreaControllerInfo?.label,
    };
    delete params?.liveRoomInfo;
    delete params?.deptInfo;
    delete params?.matchAreaControllerInfo;
    const result = await responseWithResultAsync({
      request: liveScheduleAdd,
      params,
    });
    setState((state) => ({ ...state, loading: false }));
    return result;
  };
  const handleEdit = async (values: FormType) => {
    setState((state) => ({ ...state, loading: true }));
    const params = {
      ...values,
      liveRoomId: values.liveRoomInfo?.key,
      liveRoomName: values?.liveRoomInfo?.label,
      buId: values.deptInfo?.key,
      buName: values?.deptInfo?.label,
      planLiveDate: moment(values?.planLiveDate).format('YYYY-MM-DD'),
      planLiveEndTime: moment(values.planLiveEndTime).format('YYYY-MM-DD HH:mm:00'),
      planLiveStartTime: moment(values.planLiveStartTime).format('YYYY-MM-DD HH:mm:00'),
      matchAreaControllerId: values?.matchAreaControllerInfo?.key,
      matchAreaController: values?.matchAreaControllerInfo?.label,
      id,
    };
    delete params?.liveRoomInfo;
    delete params?.deptInfo;
    delete params?.matchAreaControllerInfo;
    const result = await responseWithResultAsync({
      request: liveScheduleEdit,
      params,
    });
    setState((state) => ({ ...state, loading: false }));
    return result;
  };
  const handleOk = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const params: LiveScheduleAddRequest = {
        ...values,
        planLiveStartTime: values.planLiveStartTime
          ? moment(values.planLiveStartTime).format('YYYY-MM-DD HH:mm')
          : undefined,
        planLiveEndTime: values.planLiveEndTime
          ? moment(values.planLiveEndTime).format('YYYY-MM-DD HH:mm')
          : undefined,
      };
      const result = !id ? await handleCreate(params) : await handleEdit(params);
      if (result) {
        message.success('操作成功');
        handleCancel();
      }
    });
  };
  const initForm = (detail: LiveScheduleDetailResult) => {
    console.log('detail', detail);
    form.setFieldsValue({
      anchorCode: detail?.anchorCode,
      anchorName: detail?.anchorName,
      anchorRating: detail?.anchorRating,
      anchorType: detail?.anchorType,
      assistServiceFee: detail?.assistServiceFee,
      deptInfo: { key: detail?.buId, label: detail?.buName },
      matchAreaControllerInfo: {
        key: detail?.matchAreaControllerId,
        label: detail?.matchAreaController,
      },
      costType: detail?.costType,
      liveFlight: detail?.liveFlight,
      liveRole: detail?.liveRole,
      liveRoomInfo: { key: detail?.liveRoomId, label: detail?.liveRoomName },
      mainServiceFee: detail?.mainServiceFee,
      planLiveDate: detail?.planLiveDate ? moment(detail?.planLiveDate) : undefined,
      planLiveEndTime: detail?.planLiveEndTime ? moment(detail?.planLiveEndTime) : undefined,
      planLiveStartTime: detail?.planLiveEndTime ? moment(detail?.planLiveStartTime) : undefined,
      singleServiceFee: detail?.singleServiceFee,
      originServiceFee: detail?.originServiceFee,
      tryServiceFee: detail?.tryServiceFee,
      grossProfitTarget: detail?.grossProfitTarget,
    });
    setTimeout(() => {
      form.setFieldsValue({ grossProfitTarget: detail?.grossProfitTarget });
    }, 1000);
  };
  useEffect(() => {
    id && initPage();
  }, [id]);
  useEffect(() => {
    state && initPage();
  }, [state]);
  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="基本信息">
            <BasicForm form={form} />
          </Card>
          <Card title="场次信息">
            <VenuetForm form={form} detail={modalState?.detail} />
          </Card>
          <LivescheTable form={form} info={modalState?.detail} />
        </Spin>
        <FormBottomCard>
          <Button type="primary" onClick={handleOk}>
            保存
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AnchorDurationStatisticsCreate);
