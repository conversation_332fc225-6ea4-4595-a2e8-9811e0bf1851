import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, Col, Row, Checkbox, DatePicker, Icon, Popover } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { history } from 'qmkit';
import './index.less';
import './index.module.less';
import { Link } from 'react-router-dom';
import moment from 'moment';
import { getrepeatValid, liveroomList } from '@/services/yml/goods-assorting/index';
import { selectInvestmentDetail, reverseCreate } from '@/services/yml/investment-plan-list';
const { Option } = Select;
const { RangePicker } = DatePicker;
const dateFormat = 'YYYY-MM-DD';
interface PROPS extends FormComponentProps {
  form?: any;
  onRef?: any;
  startDate?: any;
  endDate?: any;
  expirationNameList?: any;
  info?: any;
  liveIds?: any;
}

const signupModal: React.FC<PROPS> = ({
  form,
  onRef,
  startDate,
  expirationNameList,
  endDate,
  info,
  planData,
  save,
  saveParams,
  liveIds,
  minDate,
  maxDate,
}) => {
  const [data, setData] = useState('');
  const [liveList, setLiveList] = useState([]); //直播间列表
  const [isAll, setIsAll] = useState(false);
  const { getFieldDecorator } = form;
  const [signupVisible, setSignupVisible] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const selectPlan = (value: any, option: any) => {
    selectInvestmentDetail({ id: value }).then((res) => {
      if (res?.res?.code === '200') {
        const msg = res.res.result;
        setData(msg);
        const arr = [];
        msg?.liveTalentList.map((item) => {
          arr.push(item.liveRoomId);
        });
        setSelectedIds(arr);
      }
    });
  };
  const handleOk = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }
      const idList = [];
      data?.liveTalentList?.map((item) => {
        idList.push(parseInt(item.liveRoomId));
      });
      const strNo = planData.filter((i) => i.id === value.investmentNo)[0]?.investmentPlanNo;
      const params = {
        applyRecordId: parseInt(info?.applyRecordId),
        investmentNo: strNo,
        liveRoomIdList: [liveIds],
      };
      //二次校验报名单
      const field = {
        deptId: parseInt(info?.deptId),
        liveRoomId: parseInt(liveIds),
        spuId: parseInt(info?.spuId),
        startTime: minDate,
        endTime: maxDate,
        commissionRate: info?.commissionRate,
        brandFee: info?.brandFee,
        applyTypeEnum: info?.applyBillType,
      };
      getrepeatValid(field).then((res) => {
        if (res.res.result) {
          reverseCreate(params).then((res) => {
            if (res?.res?.code === '200') {
              setSignupVisible(false);
              setData('');
              save(saveParams);
            }
          });
        } else {
          Modal.success({
            content: res.res.message,
            // onOk() {
            //   save(params);
            // },
          });
        }
      });
    });
  };
  const onOpen = () => {
    setSignupVisible(true);
    getLiveRoomList();
  };
  const onClose = () => {
    setSignupVisible(false);
    setData('');
  };
  React.useImperativeHandle(onRef, () => ({
    onOpen,
    onClose,
  }));

  const getLiveRoomList = () => {
    liveroomList({ platformEnum: info?.platformSource, deptId: info?.deptId }).then((res) => {
      if (res?.res?.code === '200') {
        res?.res?.result.length > 0 ? setLiveList(res?.res?.result) : setLiveList([]);
      } else {
        setLiveList([]);
      }
    });
  };
  return (
    <Modal
      destroyOnClose={true}
      title="邀请商家报名"
      visible={signupVisible}
      onCancel={onClose}
      onOk={handleOk}
      width={650}
    >
      <div className="planSign">
        <p className="sign-top">
          你选择的场次
          {expirationNameList?.map((item) => {
            return `"${item}"，`;
          })}
          不在当前商品的合作有效期内（{startDate + '-' + endDate}），请邀约商家报名新的招商计划:
        </p>
        <Form style={{ marginTop: '16px' }} labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
          <Row>
            <Col span={12}>
              <Form.Item label="招商计划">
                {getFieldDecorator('investmentNo', {
                  rules: [{ required: true, message: `请选择招商计划` }],
                })(
                  <Select
                    dropdownMatchSelectWidth={false}
                    placeholder="请选择"
                    onChange={selectPlan}
                    dropdownRender={(menu) => (
                      <div>
                        <a
                          href="/investment-plan-add?goods-assorting"
                          target="_blank"
                          style={{
                            padding: '4px 8px',
                            cursor: 'pointer',
                            height: '30px',
                            display: 'inline-block',
                          }}
                          onMouseDown={(e) => e.preventDefault()}
                        >
                          <Icon type="plus-circle" theme="twoTone" /> 创建招商计划
                        </a>
                        {menu}
                      </div>
                    )}
                  >
                    {planData?.map((item) => {
                      return (
                        <Option value={item.id} key={item.id}>
                          {item.title}
                        </Option>
                      );
                    })}
                  </Select>,
                )}
              </Form.Item>
            </Col>
            {data && (
              <Col span={12}>
                <Form.Item label="有效期">
                  <RangePicker
                    disabled
                    value={
                      data && [
                        moment(data?.startTime, dateFormat),
                        moment(data?.endTime, dateFormat),
                      ]
                    }
                  />
                </Form.Item>
              </Col>
            )}
          </Row>
          {data && (
            <Row>
              <Col span={12}>
                <Form.Item label="直播间">
                  <Select
                    showArrow={liveList?.length <= 1 ? false : true}
                    value={liveList.filter((i) => i.id == liveIds)[0]?.name}
                    disabled
                    maxTagCount={1}
                    // mode="multiple"
                    dropdownMatchSelectWidth={false}
                  >
                    {liveList?.map((i, index) => {
                      return (
                        <Select.Option value={i?.id} key={index}>
                          {i?.name}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          )}
        </Form>
        <div className="goods-box">
          <div className="goods-content-box">
            <div className="goods-box-left">
              <img className="goods-box-img" src={info?.image} alt="" />
            </div>
            <div className="goods-box-right">
              <p className="goods-box-title">
                <Popover placement="topLeft" content={info?.spuName}>
                  {info?.spuName}
                </Popover>
              </p>
              <p className="goods-box-price">
                {' '}
                ¥
                {info?.maxSalePrice === info?.minSalePrice
                  ? info?.minSalePrice
                  : info?.minSalePrice + '-' + info?.maxSalePrice}
              </p>
              <div className="goods-box-div">
                <span style={{ marginRight: 47 }}>
                  线上佣金:{Math.round(info?.commissionRate * 10000) / 100 + '%'}
                </span>
                <span>固定服务费:¥{info?.brandFee || 0}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default Form.create()(signupModal);
