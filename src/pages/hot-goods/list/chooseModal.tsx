import React, { useEffect, useState, forwardRef, useRef } from 'react';
import { Modal, Form, Input, Drawer, Button, message, Select } from 'antd';
import './goodsCard.less';
import { ActionType } from './type';
import ChooseLive from 'web-common-modules/components/GoodsInfoEdit/component/chooseLive';
import { cascadeList, liveGoodsInfo, chEdit } from '@/services/yml/choiceList/index';
const { TextArea } = Input;
const { Option } = Select;
const ChooseModal = React.forwardRef((props, ref) => {
  const { type, info, form, onRef, search } = props;
  const { getFieldDecorator } = form;
  const [visible, setVisible] = useState(false);
  const [goodsInfo, setGoodsInfo] = useState();
  const [cascadeArr, setCascadeArr] = useState([]);
  useEffect(() => {}, []);
  //主播
  const getInfo = () => {
    liveGoodsInfo({ id: info.id }).then((res) => {
      if (res?.res?.code === '200') {
        const value = res.res.result;
        setGoodsInfo(value);
        getCascadeList(value);
      }
    });
  };
  //直播类型

  //   标签;
  const getCascadeList = (value) => {
    cascadeList({
      deptId: value.deptId,
      labelStatus: 'ENABLE',
      talentIdList: [value.talentId],
    }).then((res) => {
      if (res?.res?.code === '200') {
        const list = res.res.result.cascadeList;
        setCascadeArr(list);
      }
    });
  };
  const [labelArr, setlabelArr] = useState([]);
  const labelObj = useRef({});
  const onOpen = () => {
    setVisible(true);
    getInfo(info);
    // console.log(info);
    labelObj.current = {};
    const arr: Array<any> = [];
    info.labelList?.forEach((i) => {
      labelObj.current[i.id] = [];
      i.labelOptionList?.forEach((t) => {
        arr.push(t.labelOption);
        labelObj.current[i.id].push(t.labelOption);
      });
    });
    setlabelArr(arr);
  };
  const onClose = () => {
    setVisible(false);
  };
  const save = () => {
    form.validateFields((err, values) => {
      const labelOptionContent = [];
      if (cascadeArr?.length) {
        cascadeArr?.forEach((item) => {
          if (values[item.id]) {
            const obj = {
              chooseMethod: item?.chooseMethod,
              id: item?.id,
              labelGroupName: item?.labelGroupName,
              requiredFlag: item?.requiredFlag,
              labelOptionList: [],
            };
            item?.labelOptionList.forEach((i) => {
              if (Array.isArray(values[item?.id])) {
                if (values[item?.id].indexOf(i.labelOption) > -1) {
                  obj.labelOptionList.push({
                    id: i.optionId,
                    labelOption: i.labelOption,
                  });
                }
              } else {
                if (values[item?.id] === i.labelOption) {
                  obj.labelOptionList.push({
                    id: i.optionId,
                    labelOption: i.labelOption,
                  });
                }
              }
            });
            labelOptionContent.push(obj);
          }
        });
      }

      if (!err) {
        chEdit({ id: info?.id, ...values, labelOptionContent, version: goodsInfo?.version }).then(
          (res) => {
            if (res?.res.code === '200') {
              message.success('操作成功');
              search();
            } else {
              message.warn(res?.res?.message);
            }
            setVisible(false);
          },
        );
      }
    });
  };
  React.useImperativeHandle(onRef, () => ({
    onOpen,
    onClose,
  }));
  return (
    <Drawer
      title={<div>选品编号</div>}
      placement="right"
      closable={false}
      width={600}
      onClose={onClose}
      visible={visible}
    >
      <div>
        <Form layout="inline">
          <Form.Item label="直播服务类型">
            {getFieldDecorator('liveServiceType', {
              initialValue: info?.liveServiceType,
              rules: [{ required: true, message: '直播服务类型' }],
            })(<ChooseLive></ChooseLive>)}
          </Form.Item>
          <br />
          {cascadeArr?.map((i) => {
            return (
              <div>
                <Form.Item label={i.labelGroupName} key={i.id}>
                  {getFieldDecorator(String(i.id), {
                    initialValue:
                      i.chooseMethod === 'RADIO'
                        ? labelObj.current?.[i.id]?.[0]
                        : labelObj.current?.[i.id],
                    rules: [
                      { required: i.requiredFlag === 1 ? true : false, message: '请选择标签' },
                    ],
                  })(
                    <Select
                      mode={i.chooseMethod === 'RADIO' ? '' : 'multiple'}
                      style={{ width: 250 }}
                    >
                      {i?.labelOptionList.map((t) => {
                        return (
                          <Option key={t.labelOption} value={t.labelOption}>
                            {t.labelOption}
                          </Option>
                        );
                      })}
                    </Select>,
                  )}
                </Form.Item>
                <br />
              </div>
            );
          })}

          <Form.Item label="商品主要卖点">
            {getFieldDecorator('sellingPoints', {
              initialValue: info?.sellingPoints,
              rules: [{ required: true, message: '商品主要卖点' }],
            })(
              <TextArea
                style={{ width: '500px' }}
                maxLength={2000}
                placeholder="商品主要卖点"
                allowClear
              />,
            )}
          </Form.Item>

          <Form.Item label="重点展示需求">
            {getFieldDecorator('spuFocus', {
              initialValue: info?.spuFocus,
              rules: [{ required: false, message: '重点展示需求' }],
            })(
              <TextArea
                style={{ width: '500px' }}
                maxLength={500}
                placeholder="重点展示需求"
                allowClear
              />,
            )}
          </Form.Item>
        </Form>
        <div className="footerBox">
          <div className="footerButton">
            <Button
              className="cancel"
              onClick={() => {
                setVisible(false);
              }}
            >
              取消
            </Button>
            <Button
              className="cancel"
              type="primary"
              onClick={() => {
                save();
              }}
            >
              保存
            </Button>
          </div>
        </div>
      </div>
    </Drawer>
  );
});
export default Form.create({ name: 'chooseModal' })(ChooseModal);
