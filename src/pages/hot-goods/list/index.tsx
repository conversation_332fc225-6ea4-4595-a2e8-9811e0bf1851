import React, { useEffect, useRef, useState } from 'react';
import { List } from 'antd';
import GoodsCard from './goodsCard';
import './index.less';
import { debounce } from 'lodash';
import { useSelector } from 'react-redux';
import { GoodsAssortingListInfo } from './type';
interface PropsType {
  list?: GoodsAssortingListInfo[];
  search?: any;
  formData?: any;
  getLiveCountList?: any;
  onSetPage?: any;
  tableHeight?: number;
  onSearch: () => void;
}
const GoodsList = (props: PropsType) => {
  const { list, formData, getLiveCountList, tableHeight } = props;
  const [cardWidth, setCardWidth] = useState(206); // 初始卡片宽度为 206
  const choiceListRightCardRef = useRef(null);
  const collapsedData = useSelector((state: any) => state.collapsedData);
  const pageOnResize = () => {
    //1. 找到div的宽度
    //2. 算以最小的宽度可以放几个。
    if (choiceListRightCardRef.current) {
      const width = choiceListRightCardRef.current.getBoundingClientRect().width - 24;
      // console.log('width', width, width % 214, width / 214, Math.floor(width / 214));
      const cardNumber = Math.floor(width / 214);

      const nowWidth = width / cardNumber;
      // console.log('cardNumber', cardNumber, nowWidth);
      setCardWidth(nowWidth - 8);
    }
  };
  const handleResize = debounce(pageOnResize, 300);
  useEffect(() => {
    handleResize(); // 初始化时计算卡片数量
    window.addEventListener('resize', handleResize); // 监听窗口大小变化
    return () => {
      window.removeEventListener('resize', handleResize); // 清除 resize 事件监听
    };
  }, []);

  useEffect(() => {
    handleResize();
  }, [collapsedData]);
  const breadcrumb = useSelector((state: any) => state.breadcrumb);
  useEffect(() => {
    pageOnResize();
  }, [breadcrumb]);

  return (
    <div
      className="gs_list"
      ref={choiceListRightCardRef}
      style={{
        flexWrap: 'wrap',
        width: 'auto',
        height: tableHeight + 'px',
        display: 'flex',
        justifyContent: 'flex-start',
      }}
    >
      {/* <List
        grid={{
          gutter: 16,
          xs: 3,
          sm: 3,
          md: 3,
          lg: 4,
          xl: 6,
          xxl: 6,
        }}
        dataSource={list}
        renderItem={(item) => (
          <List.Item>
            <GoodsCard info={item}></GoodsCard>
          </List.Item>
        )}
      /> */}

      {list?.map((item) => {
        return <GoodsCard info={item} cardWidth={cardWidth} onSearch={props.onSearch}></GoodsCard>;
      })}
    </div>
  );
};

export default GoodsList;
