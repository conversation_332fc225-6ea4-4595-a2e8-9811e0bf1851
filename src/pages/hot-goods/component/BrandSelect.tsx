import React, { useEffect, useRef, useState } from 'react';
import { Button, Row, Col, Input, Select } from 'antd';
import { getSpuBrand } from '@/services/yml/goods-assorting/index';

const BrandSelect = (props) => {
  const { clear, value, onChange } = props;
  const { Option } = Select;

  const brandList = useRef([]);
  const [id, setId] = useState(undefined);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    setId(undefined);
  }, [clear]);

  useEffect(() => {
    searchBrand('');
  }, []);
  const [list, setList] = useState([]);
  const searchBrand = (value) => {
    setSearchValue(value);

    getSpuBrand({
      status: 'ENABLE',
      name: value,
      current: 1,
      size: 10,
    }).then((res) => {
      if (res?.res?.code === '200') {
        const result = res?.res?.result;
        if (result?.records?.length) {
          setList(result.records);
        } else {
          setList([]); // 清空品牌列表
        }
      }
    });
  };

  return (
    <Select
      value={value}
      allowClear
      showSearch
      onSearch={searchBrand}
      style={{ minWidth: '200px' }}
      onChange={onChange}
      placeholder="请选择"
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      notFoundContent={null}
    >
      {list?.map((i, index) => {
        return (
          <Option value={i?.id} key={index}>
            {i?.name}
          </Option>
        );
      })}
    </Select>
  );
};

export default BrandSelect;
