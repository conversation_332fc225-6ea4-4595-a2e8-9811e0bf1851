import React, { useEffect, useRef, useState } from 'react';
import { platformList, sortList } from '../constant';
import { Button, Row, Col, Input, Select } from 'antd';
import { getSupplierList } from '@/services/gql/supplier-list/list';
import { liveroomList, getSupplier } from '@/services/yml/goods-assorting/index';

const SupplierSelect = (props) => {
  const { value, onChange } = props;
  const [dataList, setDataList] = useState([]); //直播间列表
  const getList = (name) => {
    const params = {
      size: 10,
      current: 1,
      name,
    };
    getSupplier({ ...params }).then((res) => {
      // console.log(res);
      if (res?.res?.code === '200') {
        const resData = res?.res?.result;
        resData?.length > 0 ? setDataList([...resData]) : setDataList([]);
      } else {
        setDataList([]);
      }
    });
  };
  useEffect(() => {
    getList('');
  }, []);

  return (
    <Select
      value={value}
      allowClear
      showSearch
      onSearch={getList}
      onChange={onChange}
      placeholder="请选择"
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      notFoundContent={null}
    >
      {dataList?.map((i, index) => {
        return (
          <Option value={i?.supplierId} key={index}>
            {i?.supplierCompanyCode}
          </Option>
        );
      })}
    </Select>
  );
};
export default SupplierSelect;
