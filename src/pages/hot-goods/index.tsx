import React, { useEffect, useReducer, useRef, useState } from 'react';
import { AuthWrapper, Const } from 'qmkit';
import PageLayout from '@/components/PageLayout';
import Styles from './index.module.less';
import './index.less';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import List from './list';
import FormSearch from './formSearch';
import SearchForm from './formSearch/SearchForm';
import { Button, Form, message, Spin } from 'antd';
import { explosivePage } from '@/services/yml/goods-assorting/index';
import { useTableHeight } from '@/common/constants/hooks/index';
import { GoodsAssortingListInfo } from './list/type';
import GoodsAssortingContext, { contextReducer, initContextState } from './GoodsAssortingContext';

export const debounce = <T extends (...args: any[]) => void>(func: T, delay: number) => {
  let timerId: ReturnType<typeof setTimeout>;

  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timerId);

    timerId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};
interface PropsType {
  form: WrappedFormUtils;
}
const HotGoods: React.FC<PropsType> = (props) => {
  const { form } = props;
  const formData = useRef({ deptIds: '', platformSource: '', sortField: '' });
  const page = useRef({
    current: 1,
    size: 50,
    hasMore: true,
  });
  const [dataSource, setDataSource] = useState<GoodsAssortingListInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isClear, setIsClear] = useState(false);
  const timer = null;
  const [sortField, setSortField] = useState(''); //排序
  const sortFidldRef = useRef(sortField);
  const getGoodsAssortingList = () => {
    const values = form.getFieldsValue();

    if (sortFidldRef.current) {
      const params = {
        ...values,
        minSalePrice: values?.price?.min,
        maxSalePrice: values?.price?.max,
        spuNames: values?.spuNames ? values?.spuNames.split(' ') : [],
        platformSpuIds: values?.platformSpuIds ? values?.platformSpuIds.split(' ') : [],
        startGmtCreated: values?.time?.startDate,
        endGmtCreated: values?.time?.endDate,
        explosiveFlag: 1,
        sortField: sortFidldRef.current,
        size: page.current.size,
        current: 1,
      };
      setIsLoading(true);
      setIsClear(false);
      explosivePage(params)
        .then((res) => {
          const result = res?.res;
          if (result?.code === '200') {
            if (result?.result?.records) {
              setDataSource(result?.result?.records);
              dispatch({ type: 'infoList', value: result.result.records });
            }
            const { size, current, total } = result?.result;
            if (size * current >= total) {
              page.current.hasMore = false;
            } else {
              page.current.hasMore = true;
            }
          } else {
            setDataSource([]);
            message.error(res?.res?.message);
          }
          setTimeout(() => {
            setIsLoading(false);
          }, 300);
        })
        .catch((err) => {
          setIsLoading(false);
        });
    }
  };

  const handleScroll = debounce(() => {
    const {
      scrollTop,
      clientHeight,
      scrollHeight,
    }: {
      scrollTop: number | undefined;
      clientHeight: number;
      scrollHeight: number;
    } = document.querySelector('.gs_list');
    console.log('scrollTop + clientHeight', scrollTop, clientHeight, scrollHeight);
    if (scrollTop !== undefined && scrollTop + clientHeight >= scrollHeight - 10) {
      // debugger;
      if (isLoading) {
        return;
      }

      if (page.current.hasMore) {
        page.current.size += 24;
        getGoodsAssortingList();
      }
    }
  }, 200);
  useEffect(() => {
    // 模拟加载数据的函数
    // 监听滚动事件
    const element = document.querySelector('.gs_list');
    if (element) {
      element?.addEventListener('scroll', handleScroll);
    }

    return () => {
      const element = document.querySelector('.gs_list');
      if (element) {
        element?.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);
  useEffect(() => {
    sortFidldRef.current = sortField;
    getGoodsAssortingList();
  }, [sortField]);
  const { getHeight, tableHeight } = useTableHeight(10);
  const [contextState, dispatch] = useReducer(contextReducer, initContextState);
  return (
    // <AuthWrapper functionName="f_goods_assorting_list" showType="page">
    <PageLayout routePath="/hot-goods">
      <GoodsAssortingContext.Provider value={{ ...contextState, dispatch, form }}>
        <div className="goodsAssorting" id="page-layout-content">
          {/* <div className={Styles.logoBox}>
            <img
              width="50"
              height="50"
              style={{ marginRight: '20px' }}
              src="https://befriend-rss-static-dev.oss-cn-hangzhou.aliyuncs.com/goods/spuImg/8c9c172a7ccc445cad278e60d11bfe98/logo_white.png"
            ></img>
            <div className="txt">交个朋友爆品池</div>
          </div> */}
          <SearchForm
            isClear={isClear}
            setSortField={setSortField}
            form={form}
            setIsClear={setIsClear}
            onSearch={() => {
              page.current.size = 50;
              getGoodsAssortingList();
            }}
            onReset={getGoodsAssortingList}
            getTableHeight={getHeight}
          />
          <Spin spinning={isLoading}>
            <List
              list={dataSource}
              tableHeight={tableHeight}
              onSearch={() => {
                page.current.size = 50;
                getGoodsAssortingList();
              }}
            ></List>
          </Spin>
        </div>
      </GoodsAssortingContext.Provider>
    </PageLayout>
    // </AuthWrapper>
  );
};
export default Form.create({ name: 'HotGoods' })(HotGoods);
