import React, { useEffect, useState } from 'react';
import './index.less';

interface PropsType {
  list: Array<any>;
  onChange: (value?: any) => void;
  clear?: boolean;
}
const FilterGroup: React.FC<PropsType> = (props) => {
  const { list, onChange, clear } = props;
  const [listTab, setListTab] = useState<any[]>([]);
  useEffect(() => {
    const arr = list.map((i, index) => {
      return { ...i, isActive: index ? false : true };
    });
    setListTab(arr);
    onChange(list[0]?.value);
  }, [list, clear]);
  const singleChange = (index: number) => {
    const arr = listTab;
    arr.forEach((item, key) => {
      if (key === index) {
        item.isActive = true;
      } else {
        item.isActive = false;
      }
    });
    setListTab([...arr]);
  };
  return (
    <div className="sourtGroup">
      {listTab.length > 0 &&
        listTab.map((i, index) => {
          return (
            <span
              className={`tabtxt ${i.isActive ? 'activeTab' : ''}`}
              key={i.value}
              onClick={() => {
                singleChange(index);
                onChange(i.value);
              }}
            >
              {i.label}
              <span style={{ fontSize: '10px' }} className="iconfont icon-xia1"></span>
            </span>
          );
        })}
    </div>
  );
};
export default FilterGroup;
