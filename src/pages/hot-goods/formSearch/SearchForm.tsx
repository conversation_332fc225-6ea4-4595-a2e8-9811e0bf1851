import { Form, Input, Select, Cascader } from 'antd';
import React, { useContext, useEffect, useState } from 'react';
import SearchFormComponentSet, { searchItemSet } from '@/components/SearchForm/index';

import { sortList } from '../constant';
import { useDeptList } from '../hooks/index';
import { FormComponentProps } from 'antd/es/form';
import './index.moudule.less';
import BrandSelect from '../component/BrandSelect';
import SupplierSelect from '../component/SupplierSelect';
import SortGroup from './sortGroup/index';
import LiveRoomSelect from '../component/LiveRoomSelect';
import UserList from '../component/UserList';
import PriceRangeInput from '@/components/PriceRangeInput/PriceRangeInput';
import DateRangeSelect from '../component/DateRangeSelect';
import CateSelect from '@/components/CateSelect';
import { useSettingData, useVisibleLiveRoom } from '@/common/constants/hooks/index';

import BatchInput from '@/components/BatchInput';
import BtnGroup from './btnGroup/index';
import BusinessClaims from './BusinessClaims';
import EmployeeSelect from '../component/EmployeeSelect';
import GoodsAssortingContext from '../GoodsAssortingContext';
const Option = Select.Option;

interface SearchFormProps extends FormComponentProps {
  onSearch: Function;
  loading?: boolean;
  onReset: () => void;
  getTableHeight?: any;
  setData?: any;
  setSortField?: any;
  isClear?: any;
  setIsClear?: any;
}
const SearchForm = ({
  form,
  onSearch,
  onReset,
  loading,
  getTableHeight,
  isClear,
  setData,
  setSortField,
  setIsClear,
}: SearchFormProps) => {
  const { liveList, liveAllRoomLoading, handleSearch, handleChane, liveAllRoomRun, initialValue } =
    useVisibleLiveRoom();
  const { deptList } = useDeptList(); //获取事业部数据
  const [platfom, setPlatform] = useState('');
  const [deptId, setDeptId] = useState('');
  const { dispatch } = useContext(GoodsAssortingContext);
  const options: Record<string, searchItemSet> = {
    spuNames: {
      label: '商品名称',
      span: 1,
      draggable: true,
      renderNode: <BatchInput label={'商品名称'} />,
    },
    explosiveBpId: {
      label: '归属商务',
      span: 1,
      draggable: true,
      renderNode: <EmployeeSelect deptId={deptId} bizRoleType="BUSINESS" />,
    },
    explosiveOperatorAuditor: {
      label: '归属运营',
      span: 1,
      draggable: true,
      renderNode: <EmployeeSelect deptId={deptId} bizRoleType="BUSINESS" />,
    },
    explosiveDeptId: {
      label: '来源事业部',
      span: 1,
      draggable: true,
      renderNode: (
        <Select allowClear>
          {deptList?.map((item) => (
            <Option value={item.value}>{item.label}</Option>
          ))}
        </Select>
      ),
    },
    explosiveLiveRoomId: {
      label: '来源直播间',
      span: 1,
      draggable: true,
      renderNode: (
        <Select
          loading={liveAllRoomLoading}
          placeholder="请输入直播间关键词"
          allowClear
          filterOption={true}
          optionFilterProp="children"
          showSearch
          onBlur={() => {
            handleSearch('');
          }}
        >
          {liveList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    supplierId: {
      label: '商家名称',
      span: 1,
      draggable: true,
      renderNode: <SupplierSelect></SupplierSelect>,
    },
    platformSpuIds: {
      label: '平台商品ID',
      span: 1,
      draggable: true,
      renderNode: <BatchInput label={'平台商品ID'} />,
    },
    brandId: {
      label: '品牌',
      span: 1,
      draggable: true,
      renderNode: <BrandSelect></BrandSelect>,
    },

    price: {
      label: '价格段',
      span: 1,
      draggable: true,
      renderNode: <PriceRangeInput></PriceRangeInput>,
    },

    standardCateIds: {
      label: '行业大类',
      span: 1,
      draggable: true,
      renderNode: <CateSelect mode={'multiple'}></CateSelect>,
    },

    status: {
      label: '商品状态',
      span: 1,
      draggable: true,
      renderNode: (
        <Select allowClear>
          <Option value="SELECTING">选品中</Option>
          <Option value="INVALID">已失效</Option>
          <Option value="DISABLED">已禁用</Option>
        </Select>
      ),
    },
    spokenIsComplete: {
      label: '手卡是否完整',
      span: 1,
      draggable: true,
      renderNode: (
        <Select allowClear placeholder="请选择手卡是否完整">
          <Option value={true}>是</Option>
          <Option value={false}>否</Option>
        </Select>
      ),
    },
  };
  const { run, optionsOrigin } = useSettingData({
    name: 'hot-goods-form',
    options: options,
    getTableHeight,
  });

  const onResetTable = () => {
    form.resetFields();
    setIsClear(true);
    setSortField('batch_apply_record_created_time');
    if (deptList.length) {
      form.setFieldsValue({
        deptIdsPlatform: [deptList[0].value, deptList[0].children[0].value],
      });
    }
    onReset();
  };
  useEffect(() => {
    if (deptList.length) {
      setTimeout(() => {
        form.setFieldsValue({
          deptIdsPlatform: [deptList[0].value, deptList[0].children[0].value],
        });
        onSearch();
        getTableHeight();
      }, 500);
    }
  }, [deptList]);
  useEffect(() => {
    const arr = form.getFieldsValue()?.deptIdsPlatform;
    // console.log('form.getFieldsValue()?.deptIdsPlatform', arr);
    if (arr?.length) {
      setPlatform(arr[1]);
      setDeptId(arr[0]);
      dispatch && dispatch({ type: 'deptId', value: arr[0] });
      run();
    }
  }, [form.getFieldsValue()?.deptIdsPlatform]);
  return (
    <div className="gs_box formHeight">
      <SearchFormComponentSet
        onSearch={onSearch}
        onReset={onResetTable}
        needMore={true}
        form={form}
        options={optionsOrigin}
        getTableHeight={getTableHeight}
        loading={loading}
        showRow={2}
        bizType="hot-goods-form"
      />

      <div className="btnSortGroup">
        <SortGroup list={sortList} isClear={isClear} onChange={setSortField} />
      </div>
    </div>
  );
};

export default Form.create<SearchFormProps>()(SearchForm);
