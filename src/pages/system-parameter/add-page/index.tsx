import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import { Spin, Button, Form, Row, Col, Input, Select, Table, message, Modal } from 'antd';
import styles from '../index.module.less';
import { DetailTitle } from '@/pages/report-sheet/components';
import { FormComponentProps } from 'antd/lib/form';
import { STATUS_LIST, PARAM_TYPE_LIST, PARAM_TYPE } from '../types';
import { useAddTable } from '../hooks';
import { history } from 'qmkit';
import { getQueryParams } from 'web-common-modules/utils/params';
import {
  systemParameterTypeUpsert,
  systemParameterTypeDetail,
  // SystemParameterTypeDetailResult,
} from '../services/yml/index';
import { useRequest } from 'ahooks';
import moment from 'moment';
import { useDepartment } from '../hooks/index'; // 事业部
import { fliterData, containDuplicate } from '@/utils/util';
import { liveroomList } from '@/services/yml/goods-assorting/index'; // 直播间
const { confirm } = Modal;
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
export type MenuListType = {
  id: string;
  pid: string;
  realId: string;
  title: string;
  grade: number;
  icon: string;
  authNm: string | null;
  url: string;
  reqType: string | null;
  authRemark: string | null;
  isMenuUrl: string | null;
  sort: number;
  unionCode: string;
  children: {
    id: string;
    pid: string;
    realId: string;
    title: string;
    grade: number;
    icon: string;
    authNm: string | null;
    url: string;
    reqType: string | null;
    authRemark: string | null;
    isMenuUrl: string | null;
    sort: number;
    unionCode: string;
    children: {
      id: string;
      pid: string;
      realId: string;
      title: string;
      grade: number;
      icon: string;
      authNm: string | null;
      url: string;
      reqType: string | null;
      authRemark: string | null;
      isMenuUrl: string | null;
      sort: number;
      unionCode: string;
    }[];
  }[];
}[];
export type SelectOption = MenuListType[number]['children'][number]['children'][number];
const AddPage = ({ form }: FormComponentProps) => {
  // 从local拿一二级菜单
  const All_MENUS_LIST = localStorage.getItem('jgpy-crm@menus')
    ? (JSON.parse(localStorage.getItem('jgpy-crm@menus') as string) as MenuListType)
    : [];
  const [secondMenusList, setSecondMenusList] = useState<SelectOption[]>([]); // 二级菜单
  //
  const id = getQueryParams()?.id;
  const { getFieldDecorator } = form;
  const { paramType } = form.getFieldsValue();
  const [detail, setDetail] = useState<any>();
  const { closeAndJumpToPage } = useCloseAndJump();
  const validateInput = (rule: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    // 使用正则表达式来检查是否只包含字母、数字及下划线
    // if (!/^[a-zA-Z0-9_]*$/.test(value)) {
    //   return Promise.reject(new Error('请输入字母、数字或下划线'));
    // }
    // 匹配除汉字之外的，产品要求可输入字母、数字、符号
    if (!/^[^\u4e00-\u9fa5]+$/.test(value)) {
      return Promise.reject(new Error('请输入字母、数字或符号'));
    }
    return Promise.resolve();
  };
  const validateParamInput = (rule: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    // 当参数类似为Y/N类型时，只能填Y,N
    if (form.getFieldsValue()?.paramType === 'Y_OR_N_TYPE' && value !== 'Y' && value !== 'N') {
      return Promise.reject(new Error('参数类型为Y/N类型时只能输入Y,N'));
    }
    return Promise.resolve();
  };
  const {
    columns,
    dataSource,
    AddItem,
    rowSelection,
    selectedKeys,
    setDataSource,
    setSelecteKeys,
  } = useAddTable(getFieldDecorator, {}, id as any as boolean, validateInput, form);
  const { departmentList } = useDepartment(); // 事业部

  // 获取每行的直播间下拉数据
  async function fetchLiveList(item: any): Promise<any> {
    if (item.platformSource === 'ALL' || item.deptId === 'All') {
      return [{ id: 'ALL', name: '*' }];
    } else {
      // 假定有个API可以根据item的一些属性获取liveList，这里仅为示例
      const response = await liveroomList({
        platformEnum: item.platformSource,
        deptId: item.deptId,
      });
      return response.res?.result.length
        ? [{ id: 'ALL', name: '*' }, ...response.res.result]
        : [{ id: 'ALL', name: '*' }];
    }
  }
  // 获取每行的直播间下拉数据
  async function generateFormSystemParameterValues(rules: any[]): Promise<any[]> {
    try {
      // 并行调用fetchLiveList为每个item获取liveList
      const liveListsPromises = rules.map((item) => fetchLiveList(item));
      const liveLists = await Promise.all(liveListsPromises);
      // 使用映射结合获取到的liveList值创建新对象数组

      const formSystemParameterValue = rules.map((item, index) => ({
        deptId: item?.deptId,
        deptName: item?.deptName,
        platformSource: item?.platformSource,
        liveRoomId: item?.liveRoomId,
        liveRoomName: item?.liveRoomName,
        roleType: item?.roleType,
        paramType: item?.paramType,
        paramValue: item?.paramValue,
        status: item?.status,
        sort: Number(item?.sort), // 由于后端存储未字符串，前端绑定数据为数字，统一类型，不然会导致重复校验失效
        id: `${Math.random()}${moment().valueOf()}`,
        liveList: liveLists[index], // 使用从API获取到的liveList
        catePath: item?.catePath
          ? item?.catePath === '*'
            ? '*'
            : JSON.parse(item?.catePath)
          : undefined,
        standardCate: item?.standardCate,
      }));
      return formSystemParameterValue;
    } catch (error) {
      console.error('Error fetching live lists:', error);
      throw error; // 或者处理错误，返回默认值等
    }
  }

  //新增
  const { run: createRun, loading: creatreLoading } = useRequest(systemParameterTypeUpsert, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('新增成功');
        closeAndJumpToPage('/system-parameter');
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  // 详情
  const { run: detailRun, loading: detailLoading } = useRequest(systemParameterTypeDetail, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        setDetail(res?.result);
        // 回显
        const {
          code,
          description,
          firstLevelMenu,
          secondLevelMenu,
          paramType,
          paramValue,
          status,
          remark,
          rules,
        } = res?.result || {};
        const newRules = rules?.map((item: any) => {
          const { standardCateId, standardCateName, ...otherItem } = item;
          return {
            ...otherItem,
            standardCate: { label: standardCateName, key: standardCateId },
          };
        });
        // 异步获取每行的直播间下拉数据
        generateFormSystemParameterValues(newRules || []).then((formSystemParameterValue) => {
          setDataSource((formSystemParameterValue as any) || []);
        });
        form.setFieldsValue({
          code,
          description,
          firstLevelMenu,
          secondLevelMenu,
          paramType,
          paramValue,
          status,
          remark,
          // rules: newRules,
        });
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  // 编辑
  const { run: updateRun, loading: updateLoading } = useRequest(systemParameterTypeUpsert, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('编辑成功');
        closeAndJumpToPage('/system-parameter');
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const handleSubmit = () => {
    form.validateFields((err, value) => {
      if (err) return;
      if (!value?.rules || !value?.rules?.length) {
        value.rules = [];
      } else {
        value.rules.forEach((item: any, index: number) => {
          item.deptName =
            item.deptId === 'ALL' ? '*' : fliterData(departmentList, item.deptId, 'id', 'deptName');
          item.liveRoomName =
            item.liveRoomId === 'ALL'
              ? '*'
              : fliterData(dataSource[index]?.liveList, item.liveRoomId, 'id', 'name');
          console.log(item?.catePath);
          const secondLevelList = item?.catePath?.second?.map((secondItem: any) =>
            secondItem?.children?.length
              ? secondItem?.children?.map(
                  (child: any) =>
                    `${item?.catePath?.firstLevel?.cateId}->${secondItem.cateId}->${child.cateId}`,
                )
              : [`${item?.catePath?.firstLevel?.cateId}->${secondItem.cateId}`],
          );
          const catePath = secondLevelList?.length
            ? secondLevelList?.flat()
            : item?.catePath?.firstLevel?.cateId
            ? [item?.catePath?.firstLevel?.cateId]
            : undefined;
          item.catePath = catePath ? JSON.stringify(catePath) : '*';
        });
      }
      // 判断sort排序字段有无重复
      if (containDuplicate(value.rules, 'sort')) {
        message.warning('存在重复的排序字段，请检查');
        return;
      }

      const newRules = value.rules.map((item: any) => {
        const { standardCate, ...otherItem } = item;
        return {
          ...otherItem,
          standardCateId: standardCate?.key === '*' ? 'ALL' : standardCate?.key,
          standardCateName: standardCate?.label,
        };
      });

      if (id) {
        // 编辑
        updateRun({ id: detail?.id, ...value, rules: newRules });
      } else {
        // 新增
        createRun({ ...value, rules: newRules });
      }
    });
  };

  const handleDelete = () => {
    if (!selectedKeys.length) {
      message.warning('请选择至少一条明细');
      return;
    }
    const rules = form.getFieldValue('rules');
    const valueArray = dataSource.map((item: any, index: number) => ({
      ...item,
      ...(rules[index] || {}),
    }));
    const filterArr = [...valueArray]?.filter((item) => !selectedKeys.includes(item?.id)) || [];
    // dataSource里有缓存，先清空再赋值，不然删除后不会更新数据
    setDataSource([...filterArr]);
    form.setFieldsValue({
      rules: [...filterArr],
    });
    setSelecteKeys([]);
  };
  useEffect(() => {
    if (id) {
      detailRun({ id: id });
    }
  }, []);
  useEffect(() => {
    const firstMenuVal = form.getFieldsValue()?.firstLevelMenu;
    // 一级菜单改变，改变二级菜单
    if (firstMenuVal) {
      All_MENUS_LIST?.map((item) => {
        if (firstMenuVal === item.title) {
          setSecondMenusList(item.children);
          // return
        } else if (firstMenuVal === '首页') {
          setSecondMenusList([{ title: '首页' } as SelectOption]);
        }
      });
    } else {
      setSecondMenusList([]);
    }
    // form.resetFields(['secondLevelMenu'])
  }, [form.getFieldsValue()?.firstLevelMenu]);

  const handleCancel = () => {
    confirm({
      title: '关闭确认',
      content: '关闭后不会保留本次变更，是否关闭',
      onOk() {
        closeAndJumpToPage('/system-parameter');
      },
      onCancel() {
        //
      },
    });
  };

  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={creatreLoading || detailLoading || updateLoading}>
          <Form>
            <Card title="全局生效">
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="参数代码"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 14 }}
                  >
                    {getFieldDecorator('code', {
                      rules: [
                        {
                          required: true,
                          message: '请填写参数代码',
                        },
                        {
                          validator: validateInput,
                        },
                      ],
                    })(<Input placeholder="请输入" maxLength={100} />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="参数描述"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 14 }}
                  >
                    {getFieldDecorator('description', {
                      rules: [
                        {
                          required: true,
                          message: '请填写参数描述',
                        },
                      ],
                    })(<Input placeholder="请输入" maxLength={100} />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="参数一级分类"
                    labelCol={{ span: 8 }}
                    required
                    wrapperCol={{ span: 14 }}
                  >
                    {getFieldDecorator('firstLevelMenu', {
                      rules: [
                        {
                          required: true,
                          message: '请选择参数一级分类',
                        },
                      ],
                    })(
                      <Select
                        allowClear
                        placeholder="请选择"
                        onChange={() => form.resetFields(['secondLevelMenu'])}
                      >
                        {All_MENUS_LIST?.map((item) => (
                          <Select.Option key={item.title} value={item.title}>
                            {item.title}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="参数二级分类"
                    labelCol={{ span: 8 }}
                    required
                    wrapperCol={{ span: 14 }}
                  >
                    {getFieldDecorator('secondLevelMenu', {
                      rules: [
                        {
                          required: true,
                          message: '请选择参数二级分类',
                        },
                      ],
                    })(
                      <Select allowClear placeholder="请选择">
                        {secondMenusList?.map((item) => (
                          <Select.Option key={item.title} value={item.title}>
                            {item.title}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="参数类型"
                    labelCol={{ span: 8 }}
                    required
                    wrapperCol={{ span: 14 }}
                  >
                    {getFieldDecorator('paramType', {
                      rules: [
                        {
                          required: true,
                          message: '请选择参数类型',
                        },
                      ],
                    })(
                      <Select
                        allowClear
                        placeholder="请选择"
                        onChange={() => form.resetFields(['paramValue'])}
                      >
                        {PARAM_TYPE_LIST?.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="参数值"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 14 }}
                  >
                    {getFieldDecorator('paramValue', {
                      rules: [
                        {
                          required: true,
                          message: '请填写参数值',
                        },
                        {
                          validator:
                            paramType !== PARAM_TYPE.FREE_CONTENT ? validateParamInput : undefined,
                        },
                        {
                          validator:
                            paramType !== PARAM_TYPE.FREE_CONTENT ? validateInput : undefined,
                        },
                      ],
                    })(<Input placeholder="请输入" maxLength={100} />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="状态" labelCol={{ span: 8 }} required wrapperCol={{ span: 14 }}>
                    {getFieldDecorator('status', {
                      rules: [
                        {
                          required: true,
                          message: '请选择状态',
                        },
                      ],
                    })(
                      <Select allowClear placeholder="请选择">
                        {STATUS_LIST?.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="备注" labelCol={{ span: 8 }} wrapperCol={{ span: 14 }}>
                    {getFieldDecorator('remark')(
                      // <TextArea maxLength={200} />,
                      <Input placeholder="请输入" maxLength={300} />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </Card>
            <Card title="规则生效">
              <div style={{ padding: '0 16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                  <Button type="primary" style={{ marginRight: '6px' }} onClick={AddItem}>
                    新增
                  </Button>
                  <Button type="danger" ghost onClick={handleDelete}>
                    删除
                  </Button>
                </div>
                <Table
                  pagination={false}
                  columns={columns}
                  dataSource={dataSource}
                  rowKey={'id'}
                  rowSelection={rowSelection}
                ></Table>
              </div>
            </Card>
          </Form>
        </Spin>
        <FormBottomCard>
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={creatreLoading || detailLoading || updateLoading}
          >
            保存
          </Button>
          <Button style={{ marginRight: '6px' }} onClick={handleCancel}>
            取消
          </Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddPage);
