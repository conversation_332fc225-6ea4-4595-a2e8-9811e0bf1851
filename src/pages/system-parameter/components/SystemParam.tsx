import React from 'react';
import { Select } from 'antd';
import { useAllStandardGoodsCateList } from '../hooks';

interface SystemParamProps {
  [key: string]: any;
}

export const SystemParam = (props: SystemParamProps) => {
  const { onChange, ...rest } = props;
  const { systemList, systemLoading, onSearchSystem, handleChangeSystem, handleBlurSystem } =
    useAllStandardGoodsCateList('ENABLE');

  return (
    <Select
      allowClear
      placeholder="请选择"
      {...rest}
      filterOption={false}
      onSearch={onSearchSystem}
      showSearch
      onChange={(value) => {
        onChange?.(value);
        handleChangeSystem(value);
      }}
      loading={systemLoading}
      onBlur={handleBlurSystem}
    >
      <Select.Option key={'*'} value={'*'}>
        *
      </Select.Option>
      {systemList.map((item) => (
        <Select.Option key={item.id} value={item.id}>
          {item.name}
        </Select.Option>
      ))}
    </Select>
  );
};

export default SystemParam;
