import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type PageParameterQueryRequest = {
  code?: string /*参数代码*/;
  current?: number /*当前页码,从1开始*/;
  description?: string /*参数描述*/;
  firstLevelMenu?: string /*参数一级分类*/;
  secondLevelMenu?: string /*参数二级分类*/;
  size?: number /*分页大小*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
};

export type PageParameterQueryResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    code?: string /*参数代码*/;
    creator?: string /*创建人*/;
    creatorName?: string /*创建者名称*/;
    description?: string /*参数描述*/;
    firstLevelMenu?: string /*参数一级分类*/;
    gmtCreated?: string /*创建时间*/;
    id?: string /*id*/;
    paramType?: 'Y_OR_N_TYPE' | 'FINAL_VALUE' | 'FREE_CONTENT' /*参数类型[ParamTypeEnum]*/;
    paramValue?: string /*参数值*/;
    remark?: string /*备注*/;
    rules?: Array<{ [key: string]: string }> /*规则*/;
    secondLevelMenu?: string /*参数二级分类*/;
    status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询
 */
export const pageParameterQuery = (params: PageParameterQueryRequest) => {
  return Fetch<ResponseWithResult<PageParameterQueryResult>>(
    '/tools/public/systemParam/pageQuery',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemParam/pageQuery') },
    },
  );
};

export type SystemParameterTypeUpsertRequest = {
  code?: string /*参数代码*/;
  description?: string /*参数描述*/;
  firstLevelMenu?: string /*参数一级分类*/;
  id?: string /*id*/;
  paramType?: 'Y_OR_N_TYPE' | 'FINAL_VALUE' | 'FREE_CONTENT' /*参数类型[ParamTypeEnum]*/;
  paramValue?: string /*参数值*/;
  remark?: string /*备注*/;
  rules?: Array<{ [key: string]: string }> /*规则*/;
  secondLevelMenu?: string /*参数二级分类*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
};

export type SystemParameterTypeUpsertResult = boolean;

/**
 *新增和编辑系统参数
 */
export const systemParameterTypeUpsert = (params: SystemParameterTypeUpsertRequest) => {
  return Fetch<ResponseWithResult<SystemParameterTypeUpsertResult>>(
    '/tools/public/systemParam/upsert',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemParam/upsert') },
    },
  );
};

export type SystemParameterTypeDetailRequest = {
  id?: string /*业务ID*/;
};

export type SystemParameterTypeDetailResult = {
  code?: string /*参数代码*/;
  creator?: string /*创建人*/;
  creatorName?: string /*创建者名称*/;
  description?: string /*参数描述*/;
  firstLevelMenu?: string /*参数一级分类*/;
  gmtCreated?: string /*创建时间*/;
  id?: string /*id*/;
  paramType?: 'Y_OR_N_TYPE' | 'FINAL_VALUE' | 'FREE_CONTENT' /*参数类型[ParamTypeEnum]*/;
  paramValue?: string /*参数值*/;
  remark?: string /*备注*/;
  rules?: Array<{ [key: string]: string }> /*规则*/;
  secondLevelMenu?: string /*参数二级分类*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
};

/**
 *系统参数详情
 */
export const systemParameterTypeDetail = (params: SystemParameterTypeDetailRequest) => {
  return Fetch<ResponseWithResult<SystemParameterTypeDetailResult>>(
    '/tools/public/systemParam/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemParam/detail') },
    },
  );
};

export type GetAllDeptListRequest = {
  deptPlatformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台 DY:抖音 TB：淘宝 JD:京东 PDD:拼多多 KS:快手[PlatformEnum]*/;
  isVisible?: boolean /*是否需要权限*/;
  liveRoomName?: string /*直播间名称*/;
};

export type GetAllDeptListResult = Array<{
  deptName?: string /*事业部名称*/;
  deptNo?: string /*事业部编号*/;
  id?: string /*id*/;
  status?: number /*是否启用 1：是 0：否*/;
}>;

/**
 *获取所有事业部
 */
export const getAllDeptList = (params: GetAllDeptListRequest) => {
  return Fetch<ResponseWithResult<GetAllDeptListResult>>('/iasm/public/dept/getDeptList', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/dept/getDeptList') },
  });
};

async function gql<T>(
  gqlDocument: string,
  operationName: string,
  sha256Hash: string,
  accessAuth: string,
  variables: {},
  path: string,
): Promise<ResponseWithResult<T>> {
  let requestBody = undefined;

  // 非线上环境不启用缓存
  if (Const.NODE_SERVER_ENV !== 'production') {
    requestBody = {
      variables: variables,
      extensions: {
        persistedQuery: {
          version: 1,
        },
      },
      operationName: operationName,
      query: gqlDocument,
    };
  } else {
    requestBody = {
      variables: variables,
      extensions: {
        persistedQuery: {
          version: 1,
        },
      },
      operationName: operationName,
    };
  }

  let response = await Fetch<T>(path, {
    method: 'POST',
    body: JSON.stringify(requestBody),
    headers: { accessAuth: accessAuth },
  });

  if ('errors' in response.res) {
    // @ts-ignore
    if (response.res.errors[0].message === 'PersistedQueryNotFound') {
      // 二次请求
      let requestBody = {
        variables: variables,
        extensions: {
          persistedQuery: {
            version: 1,
          },
        },
        operationName: operationName,
        query: gqlDocument,
      };
      response = await Fetch<T>(path, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { accessAuth: accessAuth },
      });
    }
  }
  // 封装返回结果
  if ('errors' in response.res) {
    return {
      success: false,
      // @ts-ignore
      message: response.res.errors[0].message,
      code: '400',
      // @ts-ignore
      result: response.res.errors[0].message,
    };
  } else {
    return {
      success: true,
      message: '',
      code: '200',
      // @ts-ignore
      result: response.res.data,
    };
  }
}
export const GetCategoryListApiDocument = `query _public_supplier_standardGoodsCate_allStandardGoodsCateList($name: String, $status: String) {
  allStandardGoodsCateList(name: $name, status: $status) {
    id
    name
    no
    status
    version
  }
}
`;

export type GetCategoryListApiVariables = {
  name?: string;
  status?: string;
};

export type GetCategoryListApiResult = {
  allStandardGoodsCateList: Array<{
    /**平台商品分类主键 */ id: string;
    /**分类名称 */
    name: string;
    /**分类编号 */
    no: string;
    /**状态 */
    status: string;
    /**乐观锁 */
    version: number;
  }>;
};

//getCategoryListApi:/gql/public/supplier/standardGoodsCate/allStandardGoodsCateList/76f29b92de12e702fb1540bec4962e877ccc2ea40df770f2ca8ac4999bded5ee
const getCategoryListApiHash = '76f29b92de12e702fb1540bec4962e877ccc2ea40df770f2ca8ac4999bded5ee';
export async function getCategoryListApi(
  variables: GetCategoryListApiVariables,
): Promise<ResponseWithResult<GetCategoryListApiResult>> {
  return gql<GetCategoryListApiResult>(
    GetCategoryListApiDocument,
    '_public_supplier_standardGoodsCate_allStandardGoodsCateList',
    getCategoryListApiHash,
    getSign(
      '/gql/public/supplier/standardGoodsCate/allStandardGoodsCateList/76f29b92de12e702fb1540bec4962e877ccc2ea40df770f2ca8ac4999bded5ee',
    ),
    variables,
    '/gql/public/supplier/standardGoodsCate/allStandardGoodsCateList/76f29b92de12e702fb1540bec4962e877ccc2ea40df770f2ca8ac4999bded5ee',
  );
}

export type SystemParameterTypeEnableRequest = {
  id?: string /*id*/;
  status?: 'DISABLE' | 'ENABLE' /*状态（0：禁用；1：启用）[StatusEnum]*/;
};

export type SystemParameterTypeEnableResult = boolean;

/**
 *启用或禁用系统参数
 */
export const systemParameterTypeEnable = (params: SystemParameterTypeEnableRequest) => {
  return Fetch<ResponseWithResult<SystemParameterTypeEnableResult>>(
    '/tools/public/systemParam/enableOrDisable',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/systemParam/enableOrDisable') },
    },
  );
};
