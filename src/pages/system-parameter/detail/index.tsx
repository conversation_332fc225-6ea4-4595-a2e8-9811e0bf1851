/*
 * @Author: 用户名
 * @Date: 2024-05-09 10:30:29
 * @LastEditTime: 2025-08-19 16:50:46
 * @Description: file content
 */
import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import { Spin, Button, Form, Row, Col, Input, Select, Table, message, Popover } from 'antd';
import styles from '../index.module.less';
import { FormComponentProps } from 'antd/lib/form';
import { STATUS_LIST, STATUS_NAME, PARAM_TYPE_NAME } from '../types';
import { useDetailTable } from '../hooks';
import { history } from 'qmkit';
import { getQueryParams } from 'web-common-modules/utils/params';
import {
  systemParameterTypeDetail,
  // SystemParameterTypeDetailResult,
} from '../services/yml/index';
import { useRequest } from 'ahooks';
import moment from 'moment';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import {
  DetailContentLayout,
  DetailTitle,
  SpinCard,
  Card,
  Title,
} from '@/components/DetailFormCompoments';
import {
  DetailContextBox,
  DetailContentItem,
} from '@/components/DetailFormCompoments/DetailContentItem';
const Item = DetailContentItem;

const AddPage = ({ form }: FormComponentProps) => {
  const id = getQueryParams()?.id;
  const { getFieldDecorator } = form;
  const [detail, setDetail] = useState<any>();
  const { closeAndJumpToPage } = useCloseAndJump();
  const validateInput = (rule: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    // 使用正则表达式来检查是否只包含字母、数字及下划线
    if (!/^[a-zA-Z0-9_]*$/.test(value)) {
      return Promise.reject(new Error('请输入字母、数字或下划线'));
    }
    return Promise.resolve();
  };
  const { columns, dataSource, setDataSource } = useDetailTable(
    getFieldDecorator,
    {},
    id as any as boolean,
    validateInput,
  );

  // 详情
  const { run: detailRun, loading: detailLoading } = useRequest(systemParameterTypeDetail, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        setDetail(res?.result);
        // 回显
        const {
          code,
          description,
          firstLevelMenu,
          secondLevelMenu,
          paramType,
          paramValue,
          status,
          remark,
          rules,
        } = res?.result || {};
        const formSystemParameterValue = rules?.map((item) => ({
          deptId: item?.deptId,
          deptName: item?.deptName,
          platformSource: item?.platformSource,
          liveRoomId: item?.liveRoomId,
          liveRoomName: item?.liveRoomName,
          roleType: item?.roleType,
          paramType: item?.paramType,
          paramValue: item?.paramValue,
          status: item?.status,
          sort: item?.sort,
          id: `${Math.random()}${moment().valueOf()}`,
          catePath: item?.catePath
            ? item?.catePath === '*'
              ? '*'
              : JSON.parse(item?.catePath)
            : undefined,
          // disabled: true,
          standardCateName: item?.standardCateName,
        }));
        setDataSource(formSystemParameterValue as any);
        form.setFieldsValue({
          code,
          description,
          firstLevelMenu,
          secondLevelMenu,
          paramType,
          paramValue,
          status,
          remark,
        });
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  useEffect(() => {
    if (id) {
      detailRun({ id: id });
    }
  }, []);

  // const handleCancel = () => {
  //   history.replace('/system-parameter');
  // };

  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText={detail?.code}>
          <Button
            type="primary"
            onClick={() => {
              closeAndJumpToPage(`system-parameter-add?id=${detail?.id}`);
            }}
          >
            编辑
          </Button>
        </DetailTitle>
        <SpinCard spinning={detailLoading}>
          <Card>
            <Title>全局生效</Title>
            <DetailContextBox>
              <Item label="参数代码">
                <p>{detail?.code ?? '-'}</p>
              </Item>
              <Item label="参数描述">
                <p>{detail?.description ?? '-'}</p>
              </Item>
              <Item label="参数一级分类">
                <p>{detail?.firstLevelMenu ?? '-'}</p>
              </Item>
              <Item label="参数二级分类">
                <p>{detail?.secondLevelMenu ?? '-'}</p>
              </Item>
              <Item label="参数类型">
                <p>
                  {detail?.paramType
                    ? PARAM_TYPE_NAME[detail?.paramType as keyof typeof PARAM_TYPE_NAME]
                    : '-'}
                </p>
              </Item>
              <Item label="参数值">
                <p>{detail?.paramValue ?? '-'}</p>
              </Item>
            </DetailContextBox>
          </Card>
          <Card>
            <Title>规则生效</Title>
            <Table
              pagination={false}
              columns={columns}
              dataSource={dataSource}
              rowKey={'id'}
            ></Table>
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddPage);
