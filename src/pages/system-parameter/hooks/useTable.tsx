import React, { useMemo, useState, useEffect } from 'react';
import styles from '@/styles/index.module.less';
import { Tag, Form, Select, Input, InputNumber, message, Popover } from 'antd';
import {
  STATUS,
  STATUS_NAME,
  STATUS_LIST,
  PARAM_TYPE,
  PARAM_TYPE_NAME,
  PARAM_TYPE_LIST,
  ROLE_TYPE_MAP,
  ROLE_TYPE,
} from '../types';
import moment from 'moment';
import { AuthWrapper, history } from 'qmkit';
import { debounce } from 'lodash';
import { useRequest } from 'ahooks';
import { systemParameterTypeEnable } from '../services/yml';
import { useDepartment } from './index'; // 事业部
import { plantformListAll, PlantformName } from '../../../../web_modules/types/index'; // 平台
import { liveroomList } from '@/services/yml/goods-assorting/index';
import PopoverRowText from '@/components/PopoverRowText/index';
import Category from '@/pages/explosive-product-trend-prediction/components/Category';
import { ColumnProps } from 'antd/lib/table';
import { SystemParam } from '../components';

export const useTable = (onRefresh: () => void) => {
  const { run: enableOrDisbleRun, loading: enableLoading } = useRequest(systemParameterTypeEnable, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('操作成功');
        onRefresh?.();
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const columns = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 30,
    },
    {
      title: '参数代码',
      key: 'code',
      dataIndex: 'code',
      width: 110,
      render: (code: string, record: any) => {
        return (
          <a
            onClick={() => {
              history.push(`system-parameter-detail?id=${record?.id}`);
            }}
          >
            <PopoverRowText text={code || '-'} />
          </a>
        );
      },
    },
    {
      title: '参数描述',
      key: 'description',
      dataIndex: 'description',
      width: 110,
      render: (description: string) => {
        return <PopoverRowText text={description || '-'} />;
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      width: 70,
      render: (status: STATUS) => {
        return status === STATUS.DISABLE ? (
          <Tag color="red">{STATUS_NAME[status] || '-'}</Tag>
        ) : (
          <Tag color="green">{STATUS_NAME[status] || '-'}</Tag>
        );
      },
    },
    {
      title: '参数一级分类',
      key: 'firstLevelMenu',
      dataIndex: 'firstLevelMenu',
      width: 110,
      render: (firstLevelMenu: string) => firstLevelMenu || '-',
    },
    {
      title: '参数二级分类',
      key: 'secondLevelMenu',
      dataIndex: 'secondLevelMenu',
      width: 110,
      render: (secondLevelMenu: string) => secondLevelMenu || '-',
    },
    {
      title: '创建人',
      key: 'creatorName',
      dataIndex: 'creatorName',
      width: 80,
      render: (creatorName: string) => creatorName || '-',
    },
    {
      title: '创建时间',
      key: 'gmtCreated',
      dataIndex: 'gmtCreated',
      width: 130,
      render: (gmtCreated: string) =>
        gmtCreated ? moment(gmtCreated).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '操作',
      key: 'other',
      dataIndex: 'other',
      width: 140,
      fixed: 'right',
      render: (_: string, record: any) => {
        return (
          <div style={{ display: 'flex', flexWrap: 'wrap' }}>
            <AuthWrapper functionName="f_system_parameter_add">
              <a
                onClick={() => {
                  history.push(`system-parameter-add?id=${record?.id}`);
                }}
              >
                编辑
              </a>
            </AuthWrapper>
            <AuthWrapper functionName="f_system_parameter_close_open">
              {record?.status === STATUS.DISABLE ? (
                <a
                  style={{ marginLeft: '6px' }}
                  onClick={() => {
                    enableOrDisbleRun({ id: record?.id, status: 'ENABLE' });
                  }}
                >
                  启用
                </a>
              ) : (
                <a
                  style={{ color: 'red', marginLeft: '6px' }}
                  onClick={() => {
                    enableOrDisbleRun({ id: record?.id, status: 'DISABLE' });
                  }}
                >
                  禁用
                </a>
              )}
            </AuthWrapper>
          </div>
        );
      },
    },
  ];

  return {
    columns,
    enableLoading,
  };
};

export const useAddTable = (
  getFieldDecorator: any,
  initValue: any,
  codeType: boolean,
  validateInput: any,
  form: any,
) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedKeys, setSelecteKeys] = useState<string[]>([]);
  const { departmentListLoading, departmentList } = useDepartment();

  const AddItem = debounce(() => {
    setDataSource((prev: any) => [
      ...prev,
      {
        id: `${Math.random()}${moment().valueOf()}`,
        deptId: 'ALL',
        platformSource: 'ALL',
        liveRoomId: 'ALL',
        roleType: 'ALL',
        liveList: [{ id: 'ALL', name: '*' }],
      },
    ]);
  }, 500);
  const formPlatformSource = useMemo(() => {
    return form.getFieldsValue()?.rules?.map((item: any) => item.platformSource);
  }, [form?.getFieldsValue()?.rules]);

  const getLiveRoomList = (index: any) => {
    setTimeout(() => {
      const updatedDataSource = [...dataSource];
      if (
        form.getFieldsValue()?.rules[index]?.platformSource === 'ALL' ||
        form.getFieldsValue()?.rules[index]?.deptId === 'ALL'
      ) {
        updatedDataSource[index].liveList = [{ id: 'ALL', name: '*' }];
        setDataSource([...updatedDataSource]);
      } else {
        liveroomList({
          platformEnum: form.getFieldsValue()?.rules[index]?.platformSource,
          deptId: form.getFieldsValue()?.rules[index]?.deptId,
        }).then((res) => {
          if (res?.res?.code === '200') {
            res?.res?.result.length > 0
              ? (updatedDataSource[index].liveList = [
                  { id: 'ALL', name: '*' },
                  ...(res?.res?.result as any),
                ])
              : (updatedDataSource[index].liveList = [{ id: 'ALL', name: '*' }]);
          } else {
            // form.setFieldsValue({[`rules[${index}].liveList`]: []})
            updatedDataSource[index].liveList = [{ id: 'ALL', name: '*' }];
          }
          setDataSource([...updatedDataSource]);
        });
      }
      form.resetFields([`rules[${index}].liveRoomId`]);
    }, 500);
  };

  const columns = useMemo<ColumnProps<any>[]>(() => {
    return [
      {
        title: '#',
        align: 'center',
        key: 'number',
        className: styles['table-number'],
        render: (_, __, index) => {
          return <span>{index + 1}</span>;
        },
        width: 20,
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>事业部
          </span>
        ),
        key: 'deptId',
        dataIndex: 'deptId',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].deptId`, {
                rules: [
                  {
                    required: true,
                    message: '请选择事业部',
                  },
                ],
                initialValue: dataSource?.[index]?.deptId,
              })(
                <Select
                  style={{ width: '150px' }}
                  allowClear
                  loading={departmentListLoading}
                  placeholder="请选择"
                  onChange={() => {
                    getLiveRoomList(index);
                  }}
                >
                  <Select.Option value={'ALL'}>*</Select.Option>
                  {departmentList?.map((item: any) => (
                    <Select.Option value={item.id} key={item.id}>
                      {item.deptName}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>平台类型
          </span>
        ),
        key: 'platformSource',
        dataIndex: 'platformSource',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].platformSource`, {
                rules: [
                  {
                    required: true,
                    message: '请选择平台类型',
                  },
                ],
                initialValue: dataSource?.[index]?.platformSource,
              })(
                <Select
                  style={{ width: '150px' }}
                  allowClear
                  placeholder="请选择"
                  onChange={() => {
                    getLiveRoomList(index);
                  }}
                >
                  <Select.Option value={'ALL'}>*</Select.Option>
                  {plantformListAll.map((item) => (
                    <Select.Option value={item.value} key={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>类目
          </span>
        ),
        key: 'catePath',
        dataIndex: 'catePath',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].catePath`, {
                // rules: [
                //   {
                //     required: true,
                //     message: '请选择类目',
                //   },
                // ],
                initialValue: dataSource?.[index]?.catePath,
              })(
                <Category
                  width={150}
                  allowClear
                  placeholder="请先选择平台类型"
                  source={
                    formPlatformSource?.[index] === 'TB'
                      ? 'TAOBAO'
                      : formPlatformSource?.[index] === 'KS'
                      ? 'KSXD'
                      : formPlatformSource?.[index]
                  }
                  defaultApi={true}
                  notSelectAll={true}
                  defaultValues={dataSource?.[index]?.catePath}
                />,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>行业大类
          </span>
        ),
        key: 'standardCate',
        dataIndex: 'standardCate',
        width: 150,
        render: (_, __, index) => {
          // SystemParam
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].standardCate`, {
                rules: [
                  {
                    required: true,
                    message: '请选择行业大类',
                  },
                ],
                initialValue: dataSource?.[index]?.standardCate || { label: '*', key: '*' },
              })(<SystemParam labelInValue={true} style={{ width: '140px' }} />)}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>直播间
          </span>
        ),
        key: 'liveRoomId',
        dataIndex: 'liveRoomId',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].liveRoomId`, {
                rules: [
                  {
                    required: true,
                    message: '请选择直播间',
                  },
                ],
                initialValue: dataSource?.[index]?.liveRoomId,
              })(
                <Select style={{ width: '150px' }} allowClear placeholder="请选择">
                  {dataSource[index]?.liveList?.map((item: any) => (
                    <Select.Option key={item.id} value={item.id}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>角色
          </span>
        ),
        key: 'roleType',
        dataIndex: 'roleType',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].roleType`, {
                rules: [
                  {
                    required: true,
                    message: '请选择角色',
                  },
                ],
                initialValue: dataSource?.[index]?.roleType,
              })(
                <Select style={{ width: '150px' }} allowClear placeholder="请选择">
                  <Select.Option value={'ALL'}>*</Select.Option>
                  {Object.keys(ROLE_TYPE_MAP).map((key) => (
                    <Select.Option value={key}>{ROLE_TYPE_MAP[key as ROLE_TYPE]}</Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>参数类型
          </span>
        ),
        key: 'paramType',
        dataIndex: 'paramType',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].paramType`, {
                rules: [
                  {
                    required: true,
                    message: '请选择参数类型',
                  },
                ],
                initialValue: dataSource?.[index]?.paramType,
              })(
                <Select
                  style={{ width: '150px' }}
                  allowClear
                  placeholder="请选择"
                  onChange={() => {
                    // 清空对应的参数值
                    form.setFieldsValue({
                      [`rules[${index}].paramValue`]: '',
                    });
                  }}
                >
                  {PARAM_TYPE_LIST?.map((item) => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>参数值
          </span>
        ),
        key: 'paramValue',
        dataIndex: 'paramValue',
        width: 120,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].paramValue`, {
                rules: [
                  {
                    required: true,
                    message: '请输入参数值',
                  },
                  {
                    validator: (_: any, value: string, callback: any) => {
                      // 当参数类似为Y/N类型时，只能填Y,y,N,n
                      if (
                        form.getFieldsValue()?.rules[index]?.paramType === 'Y_OR_N_TYPE' &&
                        value !== 'Y' &&
                        value !== 'N'
                      ) {
                        callback('参数类型为Y/N类型时只能输入Y,N');
                      }
                      callback();
                    },
                  },
                  {
                    validator: validateInput,
                  },
                ],
                initialValue: dataSource?.[index]?.paramValue,
              })(<Input placeholder="请输入" maxLength={100} />)}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>状态
          </span>
        ),
        key: 'status',
        dataIndex: 'status',
        width: 120,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].status`, {
                rules: [
                  {
                    required: true,
                    message: '请选择状态',
                  },
                ],
                initialValue: dataSource?.[index]?.status,
              })(
                <Select style={{ width: '120px' }} allowClear placeholder="请选择">
                  {STATUS_LIST?.map((item) => (
                    <Select.Option key={item.value} value={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: (
          <span>
            <span style={{ color: 'red', marginRight: '2px' }}>*</span>排序
          </span>
        ),
        key: 'sort',
        dataIndex: 'sort',
        width: 100,
        render: (_, __, index) => {
          return (
            <Form.Item>
              {getFieldDecorator(`rules[${index}].sort`, {
                rules: [
                  {
                    required: true,
                    message: '请填写排序',
                  },
                ],
                initialValue: dataSource?.[index]?.sort,
              })(
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入"
                  min={0}
                  max={9999999999}
                  precision={0}
                />,
              )}
            </Form.Item>
          );
        },
      },
    ];
  }, [initValue, getFieldDecorator, dataSource, formPlatformSource]);

  const rowSelection = {
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
    onChange: (selectedRowKeys: string | number | (string | number)[]) => {
      setSelecteKeys(selectedRowKeys as string[]);
    },
    getCheckboxProps: (record: any) => {
      return {
        disabled: record?.disabled,
      };
    },
  };

  return {
    columns,
    dataSource,
    setDataSource,
    AddItem,
    rowSelection,
    selectedKeys,
    setSelecteKeys,
  };
};

export const useDetailTable = (
  getFieldDecorator: any,
  initValue: any,
  codeType: boolean,
  validateInput: any,
) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedKeys, setSelecteKeys] = useState<string[]>([]);
  const AddItem = debounce(() => {
    setDataSource((prev: any) => [...prev, { id: `${Math.random()}${moment().valueOf()}` }]);
  }, 500);
  const columns = useMemo<ColumnProps<any>[]>(
    () => [
      {
        title: '#',
        align: 'center',
        key: 'number',
        className: styles['table-number'],
        render: (_, __, index) => {
          return <span>{index + 1}</span>;
        },
        width: 20,
      },
      {
        title: <span>事业部</span>,
        key: 'deptName',
        dataIndex: 'deptName',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>{_ || '-'}</span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>平台类型</span>,
        key: 'platformSource',
        dataIndex: 'platformSource',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>
                {_ ? (_ === 'ALL' ? '*' : PlantformName[_ as keyof typeof PlantformName]) : '-'}
              </span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>类目</span>,
        key: 'catePath',
        dataIndex: 'catePath',
        width: 150,
        render: (val, __, index) => {
          return val === '*' || val === "'*'" || !val ? (
            '*'
          ) : (
            <Form.Item>
              <Category
                width={150}
                allowClear
                placeholder="请先选择平台类型"
                source={
                  dataSource?.[index]?.platformSource === 'TB'
                    ? 'TAOBAO'
                    : dataSource?.[index]?.platformSource === 'KS'
                    ? 'KSXD'
                    : dataSource?.[index]?.platformSource === 'ALL'
                    ? undefined
                    : dataSource?.[index]?.platformSource
                }
                defaultApi={true}
                notSelectAll={true}
                defaultValues={dataSource?.[index]?.catePath}
                isDetail={true}
              />
            </Form.Item>
          );
        },
      },
      {
        title: <span>行业大类</span>,
        key: 'standardCateName',
        dataIndex: 'standardCateName',
        width: 150,
        render: (_, __, index) => {
          return <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>{_ || '-'}</span>;
        },
      },
      {
        title: <span>直播间</span>,
        key: 'liveRoomName',
        dataIndex: 'liveRoomName',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>{_ || '-'}</span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>角色</span>,
        key: 'roleType',
        dataIndex: 'roleType',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>
                {_ ? (_ === 'ALL' ? '*' : ROLE_TYPE_MAP[_ as keyof typeof ROLE_TYPE_MAP]) : '-'}
              </span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>参数类型</span>,
        key: 'paramType',
        dataIndex: 'paramType',
        width: 150,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>
                {_ ? PARAM_TYPE_NAME[_ as keyof typeof PARAM_TYPE_NAME] : '-'}
              </span>
            </Form.Item>
          );
        },
      },
      {
        title: <span>参数值</span>,
        key: 'paramValue',
        dataIndex: 'paramValue',
        width: 120,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <PopoverRowText text={_ || '-'} />
              {/* <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>{_ || '-'}</span> */}
              {/* <Popover
                content={
                  _ ? (
                    <span
                      style={
                        _.length > 50
                          ? { display: 'inline-block', width: '400px', overflowWrap: 'break-word' }
                          : {}
                      }
                    >
                      {_}
                    </span>
                  ) : (
                    '-'
                  )
                }
              >
                <span className="two-line-ellipsis" style={{ width: '100%' }}>
                  {_ ? _ : '-'}
                </span>
              </Popover> */}
            </Form.Item>
          );
        },
      },

      {
        title: <span>状态</span>,
        key: 'status',
        dataIndex: 'status',
        width: 120,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>
                {_ ? STATUS_NAME[_ as keyof typeof STATUS_NAME] : '-'}
              </span>
            </Form.Item>
          );
        },
      },

      {
        title: <span>排序</span>,
        key: 'sort',
        dataIndex: 'sort',
        width: 100,
        render: (_, __, index) => {
          return (
            <Form.Item>
              <span style={{ wordBreak: 'break-all', lineHeight: '24px' }}>{_ || '-'}</span>
            </Form.Item>
          );
        },
      },
    ],
    [initValue, getFieldDecorator, dataSource],
  );

  const rowSelection = {
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
    onChange: (selectedRowKeys: string | number | (string | number)[]) => {
      setSelecteKeys(selectedRowKeys as string[]);
    },
    getCheckboxProps: (record: any) => {
      return {
        disabled: record?.disabled,
      };
    },
  };

  return { columns, dataSource, setDataSource, AddItem, rowSelection, selectedKeys };
};
