import { useRequest } from 'ahooks';
import React, { useState, useEffect } from 'react';
import { getCategoryListApi, GetCategoryListApiResult } from '../services/yml';
import { handleResponse } from '@/utils/response';
import { debounce } from 'lodash';

type Item = Required<GetCategoryListApiResult>['allStandardGoodsCateList'][number];

export const useAllStandardGoodsCateList = (status?: string) => {
  const [systemList, setSystemList] = useState<Item[]>([]);

  const { run, loading } = useRequest(getCategoryListApi, {
    manual: true,
    onSuccess(res) {
      handleResponse(res).then((res) => {
        const { allStandardGoodsCateList } = res?.result || {};
        setSystemList(allStandardGoodsCateList || []);
      });
    },
  });

  const onSearchSystem = debounce((name: string) => {
    run({ name, status });
  }, 500);

  const handleChangeSystem = (v: string) => {
    if (!v) {
      run({ status });
    }
  };

  const handleBlurSystem = () => {
    run({ status });
  };

  useEffect(() => {
    run({ status });
  }, []);

  return {
    systemList,
    systemLoading: loading,
    onSearchSystem,
    handleChangeSystem,
    handleBlurSystem,
  };
};
