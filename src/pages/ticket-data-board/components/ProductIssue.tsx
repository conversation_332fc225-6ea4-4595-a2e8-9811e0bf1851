import React, { useImperativeHandle } from 'react';
import styles from '../index.module.less';
import { ItemBox, DetailTitle, CirqueCom, DiscountedCom } from './index';
import { useProductIssueTable } from '../hooks';
import { Spin, Table } from 'antd';
import style from '../../../styles/index.module.less';
import PaginationProxy from '@/common/constants/Pagination';
import usePagination from '@/hooks/usePagination';
import { useList, APIKEY } from '../hooks';
import {
  WorkOrderCountQuestionClassificationResult,
  WorkOrderCountQuestionClassificationRequest,
} from '../services';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

interface IProps {
  onRef: React.RefObject<any>;
}

const ProductIssue = (props: IProps) => {
  const { onRef } = props;

  const { columns } = useProductIssueTable();
  const { dataSource, pagination, onPageChange, onSearch, loading } = useList<
    WorkOrderCountQuestionClassificationRequest,
    WorkOrderCountQuestionClassificationResult
  >(APIKEY.WORK_ORDER_COUNT_QUESTION_CLASSIFICATION, (records) => {
    const data = records.map((item: any) => {
      return {
        ...item,
        ratio: isNullOrUndefined(item.ratio) ? 0 : Number(item.ratio),
        all: isNullOrUndefined(item.all) ? 0 : Number(item.all),
      };
    });
    return data;
  });

  useImperativeHandle(onRef, () => ({
    initData: (params: any) => {
      onSearch(params);
    },
  }));

  return (
    <ItemBox
      boxStyle={{
        boxShadow: '4px 4px 16px 4px #e4eaf2',
        padding: '0 12px 12px 12px',
        borderRadius: '8px',
        marginBottom: '8px',
      }}
      isDivider={false}
    >
      <DetailTitle title="问题分类统计" style={{ padding: '16px 0' }} />
      <Spin spinning={loading}>
        <div style={{ display: 'flex' }}>
          <div
            style={{
              width: '70%',
              paddingRight: '12px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}
          >
            <Table
              columns={columns}
              dataSource={dataSource as any}
              pagination={false}
              rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
              scroll={{ y: 260, x: '100%' }}
              rowKey={(record, i) => `${i}`}
            />
            <div
              className={`${style['pagination-box']} pageHeight`}
              style={{ marginBottom: '-4px' }}
            >
              {/* @ts-ignore */}
              <PaginationProxy
                {...pagination}
                onChange={(current: number, size: number) => {
                  onPageChange(current, size);
                  // clearSelection();
                }}
                valueType="flatten"
              />
            </div>
          </div>
          <div style={{ width: '30%' }}>
            <CirqueCom dataSource={dataSource} />
          </div>
        </div>
        <div>
          <DiscountedCom dataSource={dataSource} />
        </div>
      </Spin>
    </ItemBox>
  );
};

export default ProductIssue;
