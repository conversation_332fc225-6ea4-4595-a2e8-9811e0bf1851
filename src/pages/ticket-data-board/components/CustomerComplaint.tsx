import React, { useState, useImperativeHandle } from 'react';
import { ItemBox, DetailTitle } from './index';
import { useCustomerComplaintTable, useList, APIKEY } from '../hooks';
import { Table, Select, Spin } from 'antd';
import style from '../../../styles/index.module.less';
import PaginationProxy from '@/common/constants/Pagination';
import usePagination from '@/hooks/usePagination';
import { USER_IDENTIFIER_LIST, USER_IDENTIFIER_ENUM } from '../types';
import {
  WorkOrderCountCustomerComplaintResult,
  WorkOrderCountCustomerComplaintRequest,
} from '../services';

interface IProps {
  onRef: React.RefObject<any>;
  conditionAll: any;
  deptIdList: string[] | undefined;
}

const CustomerComplaint = (props: IProps) => {
  const { onRef, conditionAll, deptIdList } = props;

  const { columns } = useCustomerComplaintTable();
  const { dataSource, pagination, onPageChange, onSearch, loading } = useList<
    WorkOrderCountCustomerComplaintRequest,
    WorkOrderCountCustomerComplaintResult
  >(APIKEY.WORK_ORDER_COUNT_CUSTOMER_COMPLAINT);
  console.log('🚀 ~ CustomerComplaint ~ dataSource:', dataSource);

  const [customerService, setCustomerService] = useState<USER_IDENTIFIER_ENUM>(
    USER_IDENTIFIER_ENUM.USER_ID,
  );

  const handleChangeCustomerService = (value: USER_IDENTIFIER_ENUM) => {
    setCustomerService(value);
    onSearch({
      ...conditionAll,
      deptIdList,
      type: value,
    });
  };

  useImperativeHandle(onRef, () => ({
    initData: (params: any) => {
      onSearch({
        ...params,
        type: customerService,
      });
    },
  }));

  return (
    <ItemBox
      boxStyle={{
        boxShadow: '4px 4px 16px 4px #e4eaf2',
        padding: '0 12px 12px 12px',
        borderRadius: '8px',
        marginBottom: '8px',
      }}
      isDivider={false}
    >
      <DetailTitle
        title="客诉统计"
        style={{ padding: '16px 0' }}
        rightRender={
          <div style={{ width: '100px' }}>
            <Select
              placeholder="请选择"
              value={customerService}
              onChange={handleChangeCustomerService}
            >
              {USER_IDENTIFIER_LIST.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </div>
        }
      />
      <Spin spinning={loading}>
        <div style={{ width: '100%' }}>
          <Table
            columns={columns}
            dataSource={dataSource as AnyValues}
            pagination={false}
            loading={loading}
            rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
            rowKey={(record, index) => `${index}`}
          />
        </div>
        <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
          {/* @ts-ignore */}
          <PaginationProxy
            {...pagination}
            onChange={(current: number, size: number) => {
              onPageChange(current, size);
            }}
            valueType="flatten"
          />
        </div>
      </Spin>
    </ItemBox>
  );
};

export default CustomerComplaint;
