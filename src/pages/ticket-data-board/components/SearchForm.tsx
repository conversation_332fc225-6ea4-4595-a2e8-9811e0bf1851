import React, { useCallback, useImperativeHandle } from 'react';
import { FormComponentProps } from 'antd/es/form';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { Form } from 'antd';
import { useSearch } from '../hooks';
import moment from 'moment';

interface IProps extends FormComponentProps {
  onSearch: (value: any) => void;
  getTableHeight?: any;
  onRef: any;
}

const SearchForm: React.FC<IProps> = ({ form, onSearch, getTableHeight, onRef }) => {
  const { options } = useSearch();
  const onSubmit = useCallback(
    (init?: boolean) => {
      form.validateFields((err, values) => {
        // console.log('🚀 ~ form.validateFields ~ values:', values);
        const { createTime, ...rest } = values;
        if (createTime?.length) {
          const [createdTimeStart, createdTimeEnd] = createTime;
          rest.createdTimeStart = createdTimeStart
            ? moment(createdTimeStart).format('YYYY-MM-DD')
            : undefined;
          rest.createdTimeEnd = createdTimeEnd
            ? moment(createdTimeEnd).format('YYYY-MM-DD')
            : undefined;
        }
        // console.log('🚀 ~ onSubmit ~ rest:', rest);
        onSearch({ ...rest });
      });
    },
    [onSearch, form],
  );

  const onReset = () => {
    form.resetFields();
    onSubmit();
  };

  useImperativeHandle(onRef, () => ({
    resetForm: () => {
      form.resetFields();
    },
    setValues: (values: any) => {
      form.setFieldsValue(values);
      onSubmit();
    },
  }));

  return (
    <div style={{ marginTop: '12px' }}>
      <SearchFormComponent
        form={form}
        options={options}
        loading={false}
        onSearch={onSubmit}
        onReset={onReset}
        needMore
        getTableHeight={getTableHeight}
      />
    </div>
  );
};

export default Form.create<IProps>()(SearchForm);
