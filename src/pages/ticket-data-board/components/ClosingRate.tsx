import React, { useState, useImperativeHandle } from 'react';
import { ItemBox, DetailTitle } from './index';
import { useClosingRateTable } from '../hooks/useTable';
import { Table, Select, Spin } from 'antd';
import style from '../../../styles/index.module.less';
import PaginationProxy from '@/common/constants/Pagination';
import { PERIOD_LIST, PERIOD_ENUM } from '../types';
import { useAllList, APIKEY_ALL } from '../hooks';
import { WorkOrderCountCloseResult, WorkOrderCountCloseRequest } from '../services';

interface IProps {
  onRef: React.RefObject<any>;
  conditionAll: any;
  deptIdList: string[] | undefined;
}

const ClosingRate = (props: IProps) => {
  const { onRef, conditionAll, deptIdList } = props;

  const { columns } = useClosingRateTable();

  const [customerService, setCustomerService] = useState<PERIOD_ENUM>(PERIOD_ENUM.WEEK);

  const { dataSource, onSearch, loading } = useAllList<
    WorkOrderCountCloseRequest,
    WorkOrderCountCloseResult
  >(APIKEY_ALL.WORK_ORDER_COUNT_CLOSE);

  const handleChangeCustomerService = (value: PERIOD_ENUM) => {
    setCustomerService(value);
    onSearch({
      ...conditionAll,
      deptIdList,
      type: value,
    });
  };

  useImperativeHandle(onRef, () => ({
    initData: (params: any) => {
      onSearch({
        ...params,
        type: customerService,
      });
    },
  }));

  return (
    <ItemBox
      boxStyle={{
        boxShadow: '4px 4px 16px 4px #e4eaf2',
        padding: '0 12px 12px 12px',
        borderRadius: '8px',
        marginBottom: '8px',
      }}
      isDivider={false}
    >
      <DetailTitle
        title="关单率分析"
        style={{ padding: '16px 0' }}
        rightRender={
          <div style={{ width: '100px' }}>
            <Select
              placeholder="请选择"
              value={customerService}
              onChange={handleChangeCustomerService}
            >
              {PERIOD_LIST.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </div>
        }
      />
      <Spin spinning={loading}>
        <div style={{ width: '100%' }}>
          <Table
            columns={columns}
            dataSource={dataSource as AnyValues}
            pagination={false}
            loading={loading}
            rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
            rowKey={(record, i) => `${i}`}
          />
        </div>
      </Spin>
    </ItemBox>
  );
};

export default ClosingRate;
