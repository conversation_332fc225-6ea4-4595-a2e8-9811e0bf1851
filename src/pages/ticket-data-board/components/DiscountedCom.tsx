import React, { useRef, useEffect } from 'react';
import { Chart } from '@antv/g2';

const data = [
  { type: '发货-(赠品)漏发货', value: '9.33%' },
  { type: '催发货-赠品催发货', value: '6.67%' },
];

interface IProps {
  dataSource: any;
}

const DiscountedCom = (props: IProps) => {
  const { dataSource } = props;
  const chartRef = useRef<HTMLDivElement>(null);
  const g2Chart = useRef<Chart | null>(null);

  const initChart = () => {
    if (!chartRef.current) return;
    if (g2Chart.current) {
      g2Chart.current.destroy();
      g2Chart.current = null;
    }
    const chart = new Chart({
      container: chartRef.current,
      autoFit: true,
      height: 300,
      padding: [10, 30, 30, 30],
    });

    chart.data(dataSource || []);
    chart.scale({
      year: {
        range: [0, 1],
      },
      value: {
        min: 0,
        nice: true,
      },
    });

    chart.axis('all', {
      // label: {
      //   formatter: (val) => {
      //     const num = val.slice(0, -1);
      //     return num;
      //   },
      // },
    });

    chart.axis('classification', {
      label: {
        formatter: (val) => {
          if (!val) {
            return val;
          }
          return val?.length > 6 ? `${val.slice(0, 6)}...` : val;
        },
      },
    });

    chart.tooltip({
      showTitle: false,
      itemTpl: '<p style="height: 26px">{classification}数量（{ratio}%）: {all}</p>',
    });

    chart.area().position('classification*all').style({
      fill: 'l(90) 0:#4A74FF 1:#EDF4FF',
    });
    chart
      .line()
      .position('classification*all')
      .tooltip('classification*all*ratio', (classification, all, ratio) => {
        return {
          classification,
          all,
          ratio,
        };
      });

    chart.render();
    g2Chart.current = chart;
  };

  useEffect(() => {
    initChart();
    return () => {
      if (g2Chart.current) {
        g2Chart.current.destroy();
        g2Chart.current = null;
      }
    };
  }, [dataSource]);

  return <div ref={chartRef} />;
};

export default DiscountedCom;
