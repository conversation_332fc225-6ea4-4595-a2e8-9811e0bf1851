import React, { useState, useImperativeHandle } from 'react';
import styles from '../index.module.less';
import { ItemBox, DetailTitle } from './index';
import { Select, Table } from 'antd';
import { CUSTOMER_SERVICE_LIST, CUSTOMER_SERVICE_ENUM } from '../types';
import { useCUstomerTable, useAllList, APIKEY_ALL } from '../hooks';
import style from '../../../styles/index.module.less';
import {
  WorkOrderCountCustomerServiceRequest,
  WorkOrderCountCustomerServiceResult,
} from '../services';
import { useSetState } from 'ahooks';

interface IProps {
  onRef: React.RefObject<any>;
  conditionAll: any;
  deptIdList: string[] | undefined;
}

const sortMap = {
  create: 1,
  averageDuration: 2,
  hours24CompletedRatio: 3,
  hours48CompletedRatio: 4,
};

const orderMap = {
  ascend: 1,
  descend: 2,
};

const CustomerData = (props: IProps) => {
  const { onRef, conditionAll, deptIdList } = props;

  const { dataSource, onSearch, loading } = useAllList<
    WorkOrderCountCustomerServiceRequest,
    WorkOrderCountCustomerServiceResult
  >(APIKEY_ALL.WORK_ORDER_COUNT_CUSTOMER_SERVICE, undefined, 1000);

  const [sorter, setSorter] = useSetState<{
    sortField?: number;
    sortType?: number;
  }>({});

  const [customerService, setCustomerService] = useState<CUSTOMER_SERVICE_ENUM>(
    CUSTOMER_SERVICE_ENUM.CREATOR,
  );

  const handleChangeCustomerService = (value: CUSTOMER_SERVICE_ENUM) => {
    setCustomerService(value);
    onSearch({
      ...conditionAll,
      deptIdList,
      type: value,
      sortField: sorter.sortField,
      sortType: sorter.sortType,
    });
  };

  const { columns } = useCUstomerTable();

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    const { field, order } = sorter;
    if (order) {
      setSorter({
        sortField: sortMap[field as keyof typeof sortMap],
        sortType: orderMap[order as keyof typeof orderMap],
      });
      onSearch({
        ...conditionAll,
        deptIdList,
        type: customerService,
        sortField: sortMap[field as keyof typeof sortMap],
        sortType: orderMap[order as keyof typeof orderMap],
      });
    } else {
      setSorter({});
      onSearch({
        ...conditionAll,
        deptIdList,
        type: customerService,
      });
    }
  };

  useImperativeHandle(onRef, () => ({
    initData: (params: any) => {
      onSearch({
        ...params,
        type: customerService,
      });
    },
  }));

  return (
    <ItemBox
      boxStyle={{
        boxShadow: '4px 4px 16px 4px #e4eaf2',
        padding: '0 12px 12px 12px',
        borderRadius: '8px',
        marginBottom: '8px',
      }}
      isDivider={false}
    >
      <DetailTitle
        title="客服数据统计"
        style={{ padding: '16px 0' }}
        rightRender={
          <div style={{ width: '100px' }}>
            <Select
              placeholder="请选择"
              value={customerService}
              onChange={handleChangeCustomerService}
            >
              {CUSTOMER_SERVICE_LIST.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </div>
        }
      />
      <div style={{ width: '100%' }}>
        <Table
          columns={columns}
          dataSource={dataSource as any}
          pagination={false}
          loading={loading}
          rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
          rowKey={(record, i) => `${i}`}
          onChange={handleTableChange}
        />
      </div>
    </ItemBox>
  );
};

export default CustomerData;
