import React, { useRef, useEffect } from 'react';
import { Chart } from '@antv/g2';

const data = [
  { type: '发货-(赠品)漏发货', value: 9.33 },
  { type: '催发货-赠品催发货', value: 6.67 },
  { type: '物流-疑似丢件/退件/拦截', value: 4.77 },
  { type: '质量-商品瑕疵/性能故障', value: 1.44 },
  { type: '发货-发错货', value: 1.12 },
  { type: '福袋/抽奖-疑似登记有误/漏登记', value: 1.05 },
];

interface IProps {
  dataSource: any;
}

const CirqueCom = (props: IProps) => {
  const { dataSource } = props;
  const chartRef = useRef<HTMLDivElement>(null);
  const g2Chart = useRef<Chart | null>(null);

  const initChart = () => {
    if (!chartRef.current) return;
    // 销毁旧图表
    if (g2Chart.current) {
      g2Chart.current.destroy();
      g2Chart.current = null;
    }
    const chart = new Chart({
      container: chartRef.current,
      autoFit: true,
      height: 300,
      padding: [10, 10, 10, 10],
    });
    chart.coordinate('theta', {
      radius: 0.75,
      innerRadius: 0.8,
    });
    chart.data(dataSource || []);
    chart.tooltip({
      showTitle: false,
      itemTpl: '<p style="height: 26px">{classification}数量（{ratio}%）: {all}</p>',
    });

    chart
      .interval()
      .position('all')
      .color('classification')
      .label('classification*all', {
        layout: [
          { type: 'pie-spider' },
          { type: 'limit-in-plot', cfg: { action: 'ellipsis' /** 或 translate */ } },
        ],
        labelHeight: 20,
        content: (obj) => `${obj.classification} \n(${obj.ratio}%)${obj.all}`,
        labelLine: {
          style: {
            lineWidth: 0.5,
          },
        },
      })
      .adjust('stack')
      .tooltip('classification*all*ratio', (classification, all, ratio) => {
        return {
          classification,
          all,
          ratio,
        };
      });
    chart.legend(false);
    chart.interaction('element-active');
    chart.render();
    g2Chart.current = chart;
  };

  useEffect(() => {
    initChart();
    return () => {
      if (g2Chart.current) {
        g2Chart.current.destroy();
        g2Chart.current = null;
      }
    };
  }, [dataSource]);

  return (
    <>
      <div ref={chartRef}></div>
    </>
  );
};

export default CirqueCom;
