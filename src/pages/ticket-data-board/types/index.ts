export enum TICK_TYPE {
  UPDATE = 'UPGRADE',
  UNUPDATE = 'NON_UPGRADE',
}

export const TICK_TYPE_MAP = {
  [TICK_TYPE.UPDATE]: '升级工单',
  [TICK_TYPE.UNUPDATE]: '非升级工单',
};

export const TICK_TYPE_OPTIONS = [
  {
    label: TICK_TYPE_MAP[TICK_TYPE.UPDATE],
    value: TICK_TYPE.UPDATE,
  },
  {
    label: TICK_TYPE_MAP[TICK_TYPE.UNUPDATE],
    value: TICK_TYPE.UNUPDATE,
  },
];

export enum PRIORITY_ENUM {
  ORDINARY = 'NORMAL',
  SIGNIFICANT = 'IMPORTANT',
  URGENT = 'URGENT',
}

export const PRIORITY_NAME = {
  [PRIORITY_ENUM.ORDINARY]: '普通',
  [PRIORITY_ENUM.SIGNIFICANT]: '重要',
  [PRIORITY_ENUM.URGENT]: '紧急',
};

export const PRIORITY_OPTIONS = [
  {
    label: PRIORITY_NAME[PRIORITY_ENUM.ORDINARY],
    value: PRIORITY_ENUM.ORDINARY,
  },
  {
    label: PRIORITY_NAME[PRIORITY_ENUM.SIGNIFICANT],
    value: PRIORITY_ENUM.SIGNIFICANT,
  },
  {
    label: PRIORITY_NAME[PRIORITY_ENUM.URGENT],
    value: PRIORITY_ENUM.URGENT,
  },
];

// 客服人员工
//ai生成
// 工单创建人/工单关单人枚举
export enum CUSTOMER_SERVICE_ENUM {
  CREATOR = 1,
  CLOSER = 2,
}

// 客服人员工名字映射
export const CUSTOMER_SERVICE_NAME: Record<CUSTOMER_SERVICE_ENUM, string> = {
  [CUSTOMER_SERVICE_ENUM.CREATOR]: '工单创建人',
  [CUSTOMER_SERVICE_ENUM.CLOSER]: '工单关单人',
};

// 客服人员工列表
export const CUSTOMER_SERVICE_LIST = [
  {
    value: CUSTOMER_SERVICE_ENUM.CREATOR,
    label: CUSTOMER_SERVICE_NAME[CUSTOMER_SERVICE_ENUM.CREATOR],
  },
  {
    value: CUSTOMER_SERVICE_ENUM.CLOSER,
    label: CUSTOMER_SERVICE_NAME[CUSTOMER_SERVICE_ENUM.CLOSER],
  },
];
// 2024-05-19 zhouby -> cursor ai结尾共18行代码

// 用户ID或手机号
//ai生成
// 用户ID或手机号枚举
export enum USER_IDENTIFIER_ENUM {
  USER_ID = 1,
  MOBILE,
}

// 用户ID或手机号名称映射
export const USER_IDENTIFIER_NAME: Record<USER_IDENTIFIER_ENUM, string> = {
  [USER_IDENTIFIER_ENUM.USER_ID]: '用户ID',
  [USER_IDENTIFIER_ENUM.MOBILE]: '手机号',
};

// 用户ID或手机号列表
export const USER_IDENTIFIER_LIST = [
  {
    value: USER_IDENTIFIER_ENUM.USER_ID,
    label: USER_IDENTIFIER_NAME[USER_IDENTIFIER_ENUM.USER_ID],
  },
  {
    value: USER_IDENTIFIER_ENUM.MOBILE,
    label: USER_IDENTIFIER_NAME[USER_IDENTIFIER_ENUM.MOBILE],
  },
];
// 2024-05-19 zhouby -> cursor ai结尾共18行代码

// 按周、按月
//ai生成
// 按周、按月枚举
export enum PERIOD_ENUM {
  WEEK = 1,
  MONTH = 2,
}

// 按周、按月名称映射
export const PERIOD_NAME: Record<PERIOD_ENUM, string> = {
  [PERIOD_ENUM.WEEK]: '按周',
  [PERIOD_ENUM.MONTH]: '按月',
};

// 按周、按月列表
export const PERIOD_LIST = [
  {
    value: PERIOD_ENUM.WEEK,
    label: PERIOD_NAME[PERIOD_ENUM.WEEK],
  },
  {
    value: PERIOD_ENUM.MONTH,
    label: PERIOD_NAME[PERIOD_ENUM.MONTH],
  },
];
// 2024-05-19 zhouby -> cursor ai结尾共18行代码
