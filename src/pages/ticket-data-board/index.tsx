import React, { useRef, useState, useEffect } from 'react';
import PageLayout from '@/components/PageLayout';
import style from '@/styles/index.module.less';
import styles from './index.module.less';
import { Tabs } from 'antd';
import {
  SearchForm,
  TicketNumber,
  Trend,
  CustomerData,
  ProductIssue,
  CommodityTicket,
  StoreTickets,
  CustomerComplaint,
  ClosingRate,
} from './components';
import { useTableHeight } from '@/common/constants/hooks/index';
import { useDeptList } from './hooks';
import moment from 'moment';

const TicketDataBoard: React.FC = () => {
  const searchRef = useRef<any>(null);
  const TicketNumberRef = useRef<any>(null);
  const TrendRef = useRef<any>(null);
  const CustomerDataRef = useRef<any>(null);
  const ProductIssueRef = useRef<any>(null);
  const CommodityTicketRef = useRef<any>(null);
  const StoreTicketsRef = useRef<any>(null);
  const CustomerComplaintRef = useRef<any>(null);
  const ClosingRateRef = useRef<any>(null);

  const [keys, setKeys] = useState<any>('all');

  const [conditionAll, setConditionAll] = useState<any>({
    createdTimeStart: moment().startOf('month').format('YYYY-MM-DD'),
    createdTimeEnd: moment().endOf('month').format('YYYY-MM-DD'),
  });

  const { tableHeight, getHeight } = useTableHeight(20);

  const { deptList } = useDeptList();

  const handleTabChange = (value: any) => {
    setKeys(value);
  };

  const initAll = () => {
    const params = {
      ...conditionAll,
      deptIdList: keys === 'all' ? undefined : [keys],
    };
    getAllData(params);
  };

  const getAllData = (params: any) => {
    TicketNumberRef.current?.initData(params);
    TrendRef.current?.initData(params);
    CustomerDataRef.current?.initData(params);
    ClosingRateRef.current?.initData(params);
    CustomerComplaintRef.current?.initData(params);
    ProductIssueRef.current?.initData(params);
    CommodityTicketRef.current?.initData(params);
    StoreTicketsRef.current?.initData(params);
  };

  const setSerachForm = (value: any) => {
    searchRef.current?.setValues(value);
  };

  useEffect(() => {
    initAll();
  }, [keys]);

  return (
    <PageLayout className={styles.ticketDataBoardContainer}>
      <div
        className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
        style={{ display: 'flex', flexDirection: 'column', height: '100%' }}
      >
        <div
          className="formHeight"
          style={{
            borderBottom: '1px solid #e4eaf2',
            marginBottom: '8px',
          }}
        >
          <Tabs style={{ marginTop: '-10px' }} onChange={handleTabChange} activeKey={keys}>
            <Tabs.TabPane key="all" tab="全部"></Tabs.TabPane>
            {deptList.map((item) => (
              <Tabs.TabPane key={item.id} tab={item.deptName}></Tabs.TabPane>
            ))}
          </Tabs>
          <SearchForm
            onRef={searchRef}
            onSearch={(value) => {
              setConditionAll(value);
              //所有模块重新调用接口
              const params = {
                ...value,
                deptIdList: keys === 'all' ? undefined : [keys],
              };
              getAllData(params);
            }}
            getTableHeight={getHeight}
          />
        </div>
        <div
          style={{ overflowY: 'auto', height: tableHeight, padding: '0 4px' }}
          className={styles['content-box']}
        >
          <TicketNumber onRef={TicketNumberRef} />
          <Trend
            onRef={TrendRef}
            conditionAll={conditionAll}
            deptIdList={keys === 'all' ? undefined : [keys]}
          />
          <CustomerData
            onRef={CustomerDataRef}
            conditionAll={conditionAll}
            deptIdList={keys === 'all' ? undefined : [keys]}
          />
          <ProductIssue onRef={ProductIssueRef} />
          <CommodityTicket
            onRef={CommodityTicketRef}
            conditionAll={conditionAll}
            deptIdList={keys === 'all' ? undefined : [keys]}
            setSerachForm={setSerachForm}
          />
          <StoreTickets
            onRef={StoreTicketsRef}
            conditionAll={conditionAll}
            deptIdList={keys === 'all' ? undefined : [keys]}
          />
          <CustomerComplaint
            onRef={CustomerComplaintRef}
            conditionAll={conditionAll}
            deptIdList={keys === 'all' ? undefined : [keys]}
          />
          <ClosingRate
            onRef={ClosingRateRef}
            conditionAll={conditionAll}
            deptIdList={keys === 'all' ? undefined : [keys]}
          />
        </div>
      </div>
    </PageLayout>
  );
};

export default TicketDataBoard;
