import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
// import usePagination from '@/hooks/usePagination';
import { handleResponse } from '@/utils/response';
import {
  workOrderCountClose,
  workOrderCountCustomerService,
} from '@/pages/ticket-data-board/services';

export enum APIKEY_ALL {
  WORK_ORDER_COUNT_CLOSE = 'WORK_ORDER_COUNT_CLOSE',
  WORK_ORDER_COUNT_CUSTOMER_SERVICE = 'WORK_ORDER_COUNT_CUSTOMER_SERVICE',
}

const apiMap = {
  [APIKEY_ALL.WORK_ORDER_COUNT_CLOSE]: workOrderCountClose,
  [APIKEY_ALL.WORK_ORDER_COUNT_CUSTOMER_SERVICE]: workOrderCountCustomerService,
};

export const useAllList = <Req, Res>(
  type: APIKEY_ALL,
  formatList: undefined | ((value: any) => any) = undefined,
  size?: number,
  subCurrent?: boolean,
) => {
  // 列表数据
  const [dataSource, setDataSource] = useState<Res>();
  // 保存搜索数据
  const [condition, setCondition] = useState<Req>();
  // const { pagination, setPagination } = usePagination({
  //   current: 1,
  //   size: size || 20,
  // });

  const { loading, run } = useRequest(apiMap[type], {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        const records = res.result || [];
        // setPagination({
        //   total,
        //   current: subCurrent ? current + 1 : current,
        //   size,
        // });
        if (formatList) {
          setDataSource(formatList(records) || []);
          return;
        }
        setDataSource((records as Res) || []);
      });
    },
  });

  // 分页修改
  // const onPageChange = (current: number, size: number) => {
  //   // setPagination({ current, size, total: pagination.total });
  //   run({ ...condition, current: subCurrent ? current - 1 : current, size });
  // };

  // 搜索
  const onSearch = (value: Req) => {
    console.log('value', value);
    setCondition(value);

    run({ ...value, current: subCurrent ? 0 : 1 });
  };

  // 刷新当前页面
  const onRefresh = () => {
    run({
      ...condition,
    });
  };

  // useEffect(() => {
  //   run({ current: pagination.current, size: pagination.size });
  // }, []);

  return {
    dataSource,
    // setDataSource,
    condition,
    // setCondition,
    // setPagination,
    loading,
    // run,,
    onSearch,
    onRefresh,
  };
};

// 2025-05-15zhouby -> cursor ai结尾共生成10行代码
