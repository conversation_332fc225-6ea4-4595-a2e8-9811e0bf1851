import React, { useEffect, useState } from 'react';
import { TICK_TYPE_OPTIONS, PRIORITY_OPTIONS } from '../types';
import { Select, Input, DatePicker, AutoComplete, message } from 'antd';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { useLiveRoomList } from '@/hooks/useLiveRoomList';
import moment from 'moment';
import { getSpuBrand } from '@/pages/work-order-manager/services';
import { useRequest } from 'ahooks';
import { debounce } from 'lodash';
import { useEmployeeList } from '@/hooks/useEmployeeList';

const { RangePicker } = DatePicker;

export const useSearch = () => {
  const [brandNameList, setBrandNameList] = useState<any[]>([]);

  const { codeList: ProblemList } = useCode(CODE_ENUM.WORK_ORDER_PROBLEM_CLASSIFICATION, {
    able: true,
  });
  const { codeList } = useCode(CODE_ENUM.WORK_ORDER_FEEDBACK_CHANNEL, { able: true });

  const { liveList, loading: liveRoomLoaindg, getLiveRoomList, handleSearch } = useLiveRoomList();

  const {
    list: manaList,
    getList: getManaList,
    loading: manaLoading,
    handleSearch: manaSearch,
  } = useEmployeeList();

  const { run: getSpuBrandList, loading: getSpuBrandLoading } = useRequest(getSpuBrand, {
    manual: true,
    onSuccess: ({ res }) => {
      if (!res?.success) {
        return message.error(res?.message || '网络异常');
      }
      const result = res?.result?.records || [];
      const nameList = result?.map((item) => item?.name);
      setBrandNameList(nameList);
    },
  });

  const handleSearchBrandName = debounce((value: string) => {
    getSpuBrandList({
      name: value,
      current: 1,
      size: 10,
    });
  }, 300);

  useEffect(() => {
    getLiveRoomList({});
  }, []);

  const options = {
    orderType: {
      label: '工单类型',
      renderNode: (
        <Select allowClear placeholder="请选择">
          {TICK_TYPE_OPTIONS.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    priorityList: {
      label: '优先级',
      renderNode: (
        <Select allowClear placeholder="请选择" mode="multiple">
          {PRIORITY_OPTIONS.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    issueCategoryList: {
      label: '问题分类',
      renderNode: (
        <Select
          allowClear
          placeholder="请选择"
          mode="multiple"
          showSearch
          filterOption={true}
          optionFilterProp="children"
        >
          {ProblemList.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    handleList: {
      label: '处理人',
      renderNode: (
        <Select
          placeholder="请选择"
          allowClear
          showSearch
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          onSearch={manaSearch}
          mode="multiple"
          maxTagCount={1}
          onBlur={() => {
            getManaList({});
          }}
        >
          {manaList?.map((item) => (
            <Select.Option value={item?.employeeId} key={item?.employeeId}>
              {item?.employeeName}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    liveRoomIdList: {
      label: '直播间',
      renderNode: (
        <Select
          placeholder="请选择"
          allowClear
          loading={liveRoomLoaindg}
          showSearch
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          onSearch={handleSearch}
          onBlur={() => {
            getLiveRoomList({});
          }}
          // labelInValue
          mode="multiple"
        >
          {liveList?.map((item) => (
            <Select.Option value={item.id} key={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    brand: {
      label: '品牌名称',
      renderNode: (
        <AutoComplete
          dataSource={brandNameList}
          onSearch={handleSearchBrandName}
          placeholder="请输入"
        />
      ),
    },
    shopName: {
      label: '店铺名称',
      renderNode: <Input placeholder="请输入" />,
    },
    productId: {
      label: '商品ID',
      renderNode: <Input placeholder="请输入" />,
    },
    feedbackChannelList: {
      label: '反馈渠道',
      renderNode: (
        <Select placeholder="请选择" showSearch optionFilterProp="children" mode="multiple">
          {codeList.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    createTime: {
      label: '创建时间',
      hocOptions: {
        initialValue: [moment().startOf('month'), moment().endOf('month')],
      },
      renderNode: (
        <RangePicker
          ranges={{
            本周: [moment().startOf('week'), moment().endOf('week')],
            本月: [moment().startOf('month'), moment().endOf('month')],
            本季度: [moment().startOf('quarter'), moment().endOf('quarter')],
          }}
        />
      ),
    },
  };
  return { options };
};
