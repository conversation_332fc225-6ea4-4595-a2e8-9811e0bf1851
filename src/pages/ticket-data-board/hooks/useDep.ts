import { useRequest } from 'ahooks';
import { getDeptList, GetDeptListResult } from '../services';
import { useEffect, useState } from 'react';
import { message } from 'antd';

export const useDeptList = () => {
  const [deptList, setDeptList] = useState<GetDeptListResult>([]);

  const { run: getDeptListRun, loading: getDeptListLoading } = useRequest(getDeptList, {
    manual: true,
    onSuccess: ({ res }) => {
      if (!res?.success) {
        message.error(res?.message || '网络异常');
        return;
      }

      const { result } = res;
      if (!result?.length) {
        setDeptList(result || []);
        return;
      }

      // 定义事业部排序优先级
      const deptPriority: Record<string, number> = {
        杭州抖音事业部: 1,
        杭州综合事业部: 2,
        北京抖音事业部: 3,
        代运营事业部: 4,
      };

      // 自定义排序函数
      const sortedResult = result.sort((a, b) => {
        const priorityA = deptPriority[a.deptName || ''] || 999;
        const priorityB = deptPriority[b.deptName || ''] || 999;
        return priorityA - priorityB;
      });

      setDeptList(sortedResult);
    },
  });

  useEffect(() => {
    getDeptListRun({});
  }, []);

  return { deptList, getDeptListLoading };
};
