import React from 'react';
import { ColumnProps } from 'antd/lib/table';
import styles from '@/styles/index.module.less';
import PopoverRowText from '@/components/PopoverRowText';
import { Icon } from 'antd';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

export const useCUstomerTable = () => {
  const columns: ColumnProps<any>[] = [
    {
      title: '姓名',
      key: 'name',
      dataIndex: 'name',
      width: 120,
      render: (_: string) => _ || '-',
    },
    {
      title: '工单数量',
      key: 'create',
      dataIndex: 'create',
      width: 140,
      sorter: true,
      render: (_: number) => _ || '-',
    },
    {
      title: '排名',
      key: 'workOrderSort',
      dataIndex: 'workOrderSort',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '待处理',
      key: 'pending',
      dataIndex: 'pending',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '处理中',
      key: 'processing',
      dataIndex: 'processing',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '已完成',
      key: 'completed',
      dataIndex: 'completed',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '平均用时',
      key: 'averageDuration',
      dataIndex: 'averageDuration',
      width: 130,
      sorter: true,
      render: (_: string) => _ || '-',
    },
    {
      title: '排名',
      key: 'averageDurationSort',
      dataIndex: 'averageDurationSort',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '24小时关单率',
      key: 'hours24CompletedRatio',
      dataIndex: 'hours24CompletedRatio',
      width: 150,
      sorter: true,
      render: (_: string) => (isNullOrUndefined(_) ? '-' : `${_}%`),
    },
    {
      title: '排名',
      key: 'hours24CompletedRatioSort',
      dataIndex: 'hours24CompletedRatioSort',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '48小时关单率',
      key: 'hours48CompletedRatio',
      dataIndex: 'hours48CompletedRatio',
      width: 150,
      sorter: true,
      render: (_: string) => (isNullOrUndefined(_) ? '-' : `${_}%`),
    },
    {
      title: '排名',
      key: 'hours48CompletedRatioSort',
      dataIndex: 'hours48CompletedRatioSort',
      width: 100,
      render: (_: number) => _ || '-',
    },
  ];
  return { columns };
};

export const useProductIssueTable = () => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '问题分类',
      key: 'classification',
      dataIndex: 'classification',
      width: 120,
      render: (val: string) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '工单数量',
      key: 'all',
      dataIndex: 'all',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '占比',
      key: 'ratio',
      dataIndex: 'ratio',
      width: 80,
      render: (_: string) => (!isNullOrUndefined(_) ? `${_}%` : '-'),
    },
    {
      title: '升级工单',
      key: 'upgrade',
      dataIndex: 'upgrade',
      width: 90,
      render: (_: number) => _ || '-',
    },
    {
      title: '非升级工单',
      key: 'nonUpgrade',
      dataIndex: 'nonUpgrade',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '待梳理',
      key: 'pending',
      dataIndex: 'pending',
      width: 90,
      render: (_: number) => _ || '-',
    },
    {
      title: '处理中',
      key: 'processing',
      dataIndex: 'processing',
      width: 90,
      render: (_: number) => _ || '-',
    },
    {
      title: '已完成',
      key: 'completed',
      dataIndex: 'completed',
      width: 90,
      render: (_: number) => _ || '-',
    },
  ];
  return { columns };
};

export const useCommodityTicketTable = (props: any) => {
  const { setSerachForm } = props;
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '商品ID',
      key: 'productId',
      dataIndex: 'productId',
      width: 120,
      render: (_: string) => <a onClick={() => setSerachForm({ productId: _ })}>{_ || '-'}</a>,
    },
    {
      title: '商品名称',
      key: 'productName',
      dataIndex: 'productName',
      width: 120,
      render: (val: string) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '好评率',
      key: 'applauseRate',
      dataIndex: 'applauseRate',
      width: 100,
      render: (_: string) => (_ ? `${_}%` : '-'),
    },
    {
      title: '工单数量',
      key: 'all',
      dataIndex: 'all',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '占比',
      key: 'ratio',
      dataIndex: 'ratio',
      width: 90,
      render: (_: string) => (_ ? `${_}%` : '-'),
    },
    {
      title: '升级工单',
      key: 'upgrade',
      dataIndex: 'upgrade',
      width: 110,
      render: (_: number) => _ || '-',
    },
    {
      title: '非升级工单',
      key: 'nonUpgrade',
      dataIndex: 'nonUpgrade',
      width: 120,
      render: (_: number) => _ || '-',
    },
    {
      title: '待处理',
      key: 'pending',
      dataIndex: 'pending',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '处理中',
      key: 'processing',
      dataIndex: 'processing',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '已完成',
      key: 'completed',
      dataIndex: 'completed',
      width: 100,
      render: (_: number) => _ || '-',
    },
  ];

  return { columns };
};

export const useStoreTicketsTable = () => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '店铺名称',
      key: 'shopName',
      dataIndex: 'shopName',
      width: 120,
      render: (val: string) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '工单数量',
      key: 'all',
      dataIndex: 'all',
      width: 120,
      render: (_: number) => _ || '-',
    },
    {
      title: '占比',
      key: 'ratio',
      dataIndex: 'ratio',
      width: 100,
      render: (_: string) => (_ ? `${_}%` : '-'),
    },
    {
      title: '升级工单',
      key: 'upgrade',
      dataIndex: 'upgrade',
      width: 110,
      render: (_: number) => _ || '-',
    },
    {
      title: '非升级工单',
      key: 'nonUpgrade',
      dataIndex: 'nonUpgrade',
      width: 120,
      render: (_: number) => _ || '-',
    },
    {
      title: '待处理',
      key: 'pending',
      dataIndex: 'pending',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '处理中',
      key: 'processing',
      dataIndex: 'processing',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '已完成',
      key: 'completed',
      dataIndex: 'completed',
      width: 100,
      render: (_: number) => _ || '-',
    },
  ];

  return { columns };
};

export const useCustomerComplaintTable = () => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '用户昵称',
      key: 'nickName',
      dataIndex: 'nickName',
      width: 120,
      render: (val: string) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '用户ID',
      key: 'userId',
      dataIndex: 'userId',
      width: 120,
      render: (_: string) => (_ ? <PopoverRowText text={_} /> : '-'),
    },
    {
      title: '手机号',
      key: 'phone',
      dataIndex: 'phone',
      width: 150,
      render: (_: string) => _ || '-',
    },
    {
      title: '反馈渠道',
      key: 'feedbackChannel',
      dataIndex: 'feedbackChannel',
      width: 110,
      render: (_: string) => _ || '-',
    },
    {
      title: '客诉数量',
      key: 'num',
      dataIndex: 'num',
      width: 100,
      render: (_: number) => _ || '-',
    },
  ];

  return { columns };
};

export const useClosingRateTable = () => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      align: 'center',
      key: 'number',
      className: styles['table-number'],
      render: (text: any, red: any, index: any) => {
        return <span>{index + 1}</span>;
      },
      width: 40,
    },
    {
      title: '月份/周',
      key: 'time',
      dataIndex: 'time',
      width: 150,
      render: (_: string) => _ || '-',
    },
    {
      title: '工单数量',
      key: 'all',
      dataIndex: 'all',
      width: 100,
      render: (_: number) => _ || '-',
    },
    {
      title: '升级工单数量',
      key: 'upgrade',
      dataIndex: 'upgrade',
      width: 120,
      render: (_: number) => _ || '-',
    },
    {
      title: '24小时关单率',
      key: 'hours24CompletedRatio',
      dataIndex: 'hours24CompletedRatio',
      width: 140,
      render: (_: string) => _ || '-',
    },
    {
      title: '同比',
      key: 'onYear24',
      dataIndex: 'onYear24',
      width: 110,
      render: (_: number) => {
        return (
          <span>
            {_ > 0 ? (
              <Icon type="arrow-up" style={{ color: '#73D13D' }} />
            ) : (
              <Icon type="arrow-down" style={{ color: '#D9363E' }} />
            )}
            <span style={{ color: _ > 0 ? '#73D13D' : '#D9363E' }}>{`${_}%`}</span>
          </span>
        );
      },
    },
    {
      title: '环比',
      key: 'qoq24',
      dataIndex: 'qoq24',
      width: 110,
      render: (_: number) => {
        return (
          <span>
            {_ > 0 ? (
              <Icon type="arrow-up" style={{ color: '#73D13D' }} />
            ) : (
              <Icon type="arrow-down" style={{ color: '#D9363E' }} />
            )}
            <span style={{ color: _ > 0 ? '#73D13D' : '#D9363E' }}>{`${_}%`}</span>
          </span>
        );
      },
    },
    {
      title: '48小时关单率',
      key: 'hours48CompletedRatio',
      dataIndex: 'hours48CompletedRatio',
      width: 140,
      render: (_: string) => _ || '-',
    },
    {
      title: '同比',
      key: 'onYear48',
      dataIndex: 'onYear48',
      width: 110,
      render: (_: number) => {
        return (
          <span>
            {_ > 0 ? (
              <Icon type="arrow-up" style={{ color: '#73D13D' }} />
            ) : (
              <Icon type="arrow-down" style={{ color: '#D9363E' }} />
            )}
            <span style={{ color: _ > 0 ? '#73D13D' : '#D9363E' }}>{`${_}%`}</span>
          </span>
        );
      },
    },
    {
      title: '环比',
      key: 'qoq48',
      dataIndex: 'qoq48',
      width: 110,
      render: (_: number) => {
        return (
          <span>
            {_ > 0 ? (
              <Icon type="arrow-up" style={{ color: '#73D13D' }} />
            ) : (
              <Icon type="arrow-down" style={{ color: '#D9363E' }} />
            )}
            <span style={{ color: _ > 0 ? '#73D13D' : '#D9363E' }}>{`${_}%`}</span>
          </span>
        );
      },
    },
  ];
  return { columns };
};
