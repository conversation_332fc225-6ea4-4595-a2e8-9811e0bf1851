import { Button, Table, Modal, Input, Row, Col, Icon, message, Select } from 'antd';
import React from 'react';
import Form, { FormComponentProps } from 'antd/lib/form';
import WithToggleModal from '@/components/WithToggleModal';
import { WithToggleModalProps } from '@/components/WithToggleModal';

import { brandOutBlack } from '../services/index';
import Styles from '../index.module.less';
import { debounce } from 'lodash';
import { useDeptList } from '@/hooks/useDeptList';
interface IProps extends WithToggleModalProps {
  selectSpuData: any;
  info?: any;
  onSearch?: () => void;
}
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 16,
  },
};
const AddSupplierBlack: React.FC<IProps> = ({ selectSpuData, form, info, onSearch, ...rest }) => {
  const { getFieldDecorator, validateFields } = form;
  const { deptList } = useDeptList();
  // console.log('info', info);
  const onOk = debounce(() => {
    validateFields((errors, values) => {
      if (!errors) {
        const params = {
          ...values,
          id: info?.id || null,
        };
        brandOutBlack({ ...params }).then(({ res }) => {
          // console.log('res', res);
          if (res.code === '200') {
            message.success(res.message);
            onSearch();
            rest?.onCancel();
          } else {
            message.error(res.message);
          }
        });
      }
    });
  }, 300);
  return (
    <Modal
      width={500}
      footer={
        <div>
          <Button onClick={rest?.onCancel}>取消</Button>
          <Button
            type="primary"
            onClick={() => {
              onOk();
            }}
          >
            确定
          </Button>
        </div>
      }
      {...rest}
    >
      <Form {...formItemLayout} labelAlign="right">
        {/* <Title title="基本信息"></Title> */}
        <p className={'title_add_modal_crm'}>
          <Icon
            style={{ color: '#FF4D4F', margin: '24px 16px' }}
            type="exclamation-circle"
            theme="filled"
          />
          确认对于当前品牌解除黑名单？
        </p>
        <Form.Item label="解黑事业部" style={{ marginBottom: 0 }}>
          {getFieldDecorator('relieveDeptIds', {
            initialValue: info?.relieveDeptIds || undefined,
            rules: [{ required: true, message: '解黑事业部' }],
          })(
            <Select
              style={{ width: 300 }}
              allowClear={true}
              placeholder="请选择"
              mode={'multiple'}
              maxTagCount={1}
            >
              {deptList?.map((i, index) => {
                return (
                  <Select.Option value={i?.value} key={index}>
                    {i?.label}
                  </Select.Option>
                );
              })}
            </Select>,
          )}
        </Form.Item>
        <Form.Item label="解黑原因" style={{ marginBottom: 0 }}>
          {getFieldDecorator('relieveReason', {
            rules: [{ required: false, message: '解黑原因' }],
          })(
            <Input.TextArea
              maxLength={200}
              allowClear
              autoSize={{ minRows: 4, maxRows: 6 }}
              style={{ width: 300 }}
            ></Input.TextArea>,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default WithToggleModal(Form.create({ name: 'AddSupplierBlack' })(AddSupplierBlack));
