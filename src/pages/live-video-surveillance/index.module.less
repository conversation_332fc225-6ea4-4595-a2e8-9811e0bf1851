.publishFeeManageContainer {
  background-color: white;

  :global {
    .page-layout-body-wrap {
      margin: 0;
    }

    .ant-tabs-bar {
      margin: 0;
      background-color: white;
    }

    .layout-content-item {
      margin: 20px;
    }

    .page-layout-body-wrap {
      margin: 0;
    }

    .ant-tabs {
      .ant-tabs-bar {
        border-bottom: 0 !important;
        // margin-bottom: 16px;
      }

      .ant-tabs-ink-bar {
        background: #204eff;
      }

      .ant-tabs-nav .ant-tabs-tab {
        padding: 12px 6px;
      }

      .ant-tabs-tab {
        margin-right: 16px;
      }
    }

    .ant-table-fixed-header .ant-table-scroll .ant-table-header {
      overflow-x: auto !important;
      padding-bottom: 15px !important;
      width: 101%;
      height: 44px !important;
    }

    .ant-table-header {
      background-color: #f0f4f7 !important;
    }
  }
  .liveVideoSurveillanceContainer {
    display: flex;
    padding: 5px;
    height: calc(100vh - 50px);
    gap: 10px;

    .liveVideoBox {
      flex: 2;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      margin: 5px;

      .videoPlayer {
        border-radius: 8px;
        overflow: hidden;
      }
    }

    .liveWordsBox {
      flex: 1;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      margin: 5px;

      .placeholder {
        padding: 20px;
        text-align: center;
        color: #666;
        background: #f5f5f5;
        border-radius: 8px;
        margin: 10px 0;

        p {
          margin: 10px 0;
          font-size: 14px;
        }
      }
    }
  }

  // ai生成
  // 自定义Card样式，保持与原Card组件title样式一致
  .customCard {
    padding: 16px 0;
    background: #fff;

    .customTitle {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 16px;

      .titleIcon {
        width: 4px;
        height: 20px;
        background: #204eff;
        border-radius: 0px 2px 2px 0px;
        margin-right: 12px;
      }

      .titleContent {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #111111;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        margin: 0;
      }
    }
  }

  // 无视频状态样式
  .noVideoContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 130px);
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e8e8e8;

    .videoIcon {
      font-size: 80px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .noVideoText {
      font-size: 16px;
      color: #999;
      margin: 0;
    }

    .noVideoTip {
      font-size: 14px;
      color: #ccc;
      margin: 8px 0 0 0;
    }
  }
  // 2025年1月2日 开山ai结尾共生成41行代码

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .liveVideoSurveillanceContainer {
      flex-direction: column;
      height: auto;

      .liveVideoBox,
      .liveWordsBox {
        flex: none;
        width: 100%;
      }
    }
  }
}
