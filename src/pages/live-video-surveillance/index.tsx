// ai生成
import React, { useRef, useState } from 'react';
import { Icon, message, Spin } from 'antd';
import styles from './index.module.less';
import PageLayout from '@/components/PageLayout';
import VideoPlayer, { VideoPlayerRef } from './components/VideoPlayer';
import InteractiveInfo from './components/InteractiveInfo';
import { useSharedDanmakuData } from './hooks/useSharedDanmakuData';

const LiveVideoSurveillance = () => {
  const videoPlayerRef = useRef<VideoPlayerRef>(null);

  // 视频相关状态
  const [videoSrc, setVideoSrc] = useState<string>('');
  const [hasVideo, setHasVideo] = useState<boolean>(false);

  // 统一管理弹幕数据
  const sharedDanmakuData = useSharedDanmakuData();

  // 调试信息
  console.log(`主页面: 弹幕数据状态`, {
    原始数据: sharedDanmakuData.originalDanmakuList?.length || 0,
    视频弹幕: sharedDanmakuData.videoPlayDanmakus?.length || 0,
    过滤弹幕: sharedDanmakuData.filteredDanmakuList?.length || 0,
    当前视频时间: sharedDanmakuData.currentVideoTime,
    loading: sharedDanmakuData.loading,
  });

  // 处理弹幕点击定位视频
  const handleSeekToTime = (time: number) => {
    if (hasVideo && videoPlayerRef.current) {
      videoPlayerRef.current.seekTo(time);
    } else {
      message.warning('请先查询视频');
    }
  };

  // 处理查询视频成功 - 从InteractiveInfo组件触发
  const handleVideoQuery = (videoUrl: string) => {
    if (videoUrl) {
      setVideoSrc(videoUrl);
      setHasVideo(true);
      // message.success('视频加载成功');
    } else {
      message.error('视频地址无效');
    }
  };

  // 处理重置视频 - 从InteractiveInfo组件触发
  const handleVideoReset = () => {
    setVideoSrc('');
    setHasVideo(false);
  };

  return (
    <PageLayout className={styles.publishFeeManageContainer}>
      <div className={styles.liveVideoSurveillanceContainer}>
        <div className={styles.liveVideoBox}>
          <div className={styles.customCard}>
            <div className={styles.customTitle}>
              <div className={styles.titleIcon}></div>
              <p className={styles.titleContent}>直播画面</p>
            </div>

            {hasVideo && !sharedDanmakuData.loading ? (
              <VideoPlayer
                ref={videoPlayerRef}
                videoSrc={videoSrc}
                height={'calc(100vh - 130px)'}
                className={styles.videoPlayer}
                danmakuData={sharedDanmakuData.videoPlayDanmakus}
                onVideoTimeUpdate={sharedDanmakuData.setCurrentVideoTime}
              />
            ) : sharedDanmakuData.loading ? (
              <Spin spinning={sharedDanmakuData.loading} tip="数据加载中...">
                <div className={styles.noVideoContainer}>
                  {/* <Icon type="video-camera" className={styles.videoIcon} />
                  <p className={styles.noVideoText}>暂无视频</p>
                  <p className={styles.noVideoTip}>请在右侧选择直播间、日期和片段，然后点击查询</p> */}
                </div>
              </Spin>
            ) : (
              <div className={styles.noVideoContainer}>
                <Icon type="video-camera" className={styles.videoIcon} />
                <p className={styles.noVideoText}>暂无视频</p>
                <p className={styles.noVideoTip}>请在右侧选择直播间、日期和片段，然后点击查询</p>
              </div>
            )}
          </div>
        </div>
        <div className={styles.liveWordsBox}>
          <div className={styles.customCard}>
            <div className={styles.customTitle}>
              <div className={styles.titleIcon}></div>
              <p className={styles.titleContent}>互动信息</p>
            </div>
            <InteractiveInfo
              onSeekToTime={handleSeekToTime}
              onVideoQuery={handleVideoQuery}
              onVideoReset={handleVideoReset}
              sharedDanmakuData={sharedDanmakuData}
            />
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default LiveVideoSurveillance;
// 2025年1月2日 开山ai结尾共生成70行代码
