// ai生成
export interface DanmakuItem {
  id: string;
  content: string;
  time: number; // 视频时间，单位秒
  color?: string;
  fontSize?: number;
  speed?: number; // 滚动速度
  username?: string; // 新增：用户名
}

// 互动信息列表数据结构
export interface InteractiveMessageItem {
  id: string;
  username: string;
  content: string;
  time: number; // 视频时间，单位秒
  timestamp: string; // 发送时间戳格式
}

// 生成大量弹幕数据的工具函数
const generateMockData = () => {
  const usernames = [
    '苍茫的天涯',
    '天外来客',
    '风吹麦浪',
    '星辰大海',
    '梦想飞翔',
    '夜幕降临',
    '阳光明媚',
    '流水潺潺',
    '青山绿水',
    '白云朵朵',
    '花开富贵',
    '春暖花开',
    '月圆之夜',
    '海阔天空',
    '万里无云',
    '桃花满园',
    '绿水青山',
    '秋高气爽',
    '雪花飞舞',
    '微风徐来',
    '碧海蓝天',
    '晨曦初露',
    '暮色苍茫',
    '繁星点点',
    '彩虹桥下',
    '朝霞满天',
    '夕阳西下',
    '明月当空',
    '烟雨江南',
    '塞外风光',
    '大漠孤烟',
    '小桥流水',
    '竹林深处',
    '梅花香雪',
    '荷塘月色',
    '枫叶正红',
    '樱花飞舞',
    '牡丹盛开',
    '菊花满园',
    '兰花幽香',
    '竹子清影',
    '松涛阵阵',
    '海浪拍岸',
    '山峰巍峨',
    '森林深幽',
    '草原广阔',
    '湖水清澈',
    '河流奔腾',
    '瀑布飞流',
    '云雾缭绕',
    '雷电交加',
    '雨后彩虹',
    '雪山巍峨',
    '沙漠玫瑰',
    '海市蜃楼',
    '极光绚烂',
    '晨露晶莹',
    '黄昏时分',
    '午夜时光',
    '破晓时刻',
    '正午阳光',
    '傍晚余晖',
    '深夜星空',
  ];

  const contents = [
    '这个功能太棒了！',
    '666666',
    '主播讲得很清楚',
    '学到了很多知识',
    '支持主播！',
    '这个直播间氛围很好',
    '前排围观',
    '弹幕效果不错',
    '继续加油！',
    '技术分享很棒',
    '感谢主播分享',
    '直播质量很高',
    '哇，好厉害！',
    '学习了',
    '点赞！',
    '这个产品看起来不错',
    '价格如何？',
    '有优惠吗？',
    '包邮吗？',
    '质量怎么样？',
    '什么时候发货？',
    '有现货吗？',
    '多种颜色可选吗？',
    '尺寸标准吗？',
    '可以退换吗？',
    '太实用了',
    '性价比很高',
    '推荐购买',
    '值得拥有',
    '心动了',
    '主播人真好',
    '解答很详细',
    '服务态度好',
    '专业知识丰富',
    '推荐靠谱',
    '直播很精彩',
    '内容丰富',
    '时间过得真快',
    '舍不得结束',
    '明天还来',
    '新人报到',
    '老粉丝了',
    '每天必看',
    '从不缺席',
    '忠实观众',
    '画质很清晰',
    '声音很清楚',
    '网络很流畅',
    '体验很好',
    '技术一流',
    '产品展示很棒',
    '角度很好',
    '光线充足',
    '细节清楚',
    '效果真实',
    '互动很有趣',
    '回答及时',
    '很有耐心',
    '态度亲切',
    '服务周到',
    '优惠力度大',
    '活动很给力',
    '福利满满',
    '价格美丽',
    '超值购买',
    '包装很精美',
    '快递很快',
    '服务很好',
    '商品不错',
    '下次再来',
    '感谢推荐',
    '很有帮助',
    '解决了问题',
    '增长知识',
    '受益匪浅',
    '直播间很温馨',
    '大家都很友好',
    '氛围很棒',
    '像一家人',
    '很有爱',
    '主播很用心',
    '准备充分',
    '内容详实',
    '讲解清晰',
    '专业可靠',
    '时间安排合理',
    '节奏很好',
    '不拖沓',
    '重点突出',
    '条理清晰',
    '互动环节棒',
    '回复很快',
    '很贴心',
    '考虑周全',
    '服务到位',
    '产品种类丰富',
    '选择很多',
    '总有喜欢的',
    '眼花缭乱',
    '挑选困难',
    '质量有保证',
    '品牌可信',
    '口碑很好',
    '值得信赖',
    '放心购买',
    '发货速度快',
    '物流给力',
    '包装完好',
    '没有损坏',
    '满意收货',
    '客服很好',
    '售后贴心',
    '问题解决快',
    '态度很棒',
    '五星好评',
  ];

  const danmakuData: DanmakuItem[] = [];
  const interactiveData: InteractiveMessageItem[] = [];

  // 生成大量数据（模拟30分钟直播，约1800秒）
  for (let i = 0; i < 5000; i++) {
    const time = Math.random() * 1800; // 0-1800秒随机时间
    const username = usernames[Math.floor(Math.random() * usernames.length)];
    const content = contents[Math.floor(Math.random() * contents.length)];
    const color = 'white'; // 所有弹幕都使用白色
    const fontSize = 12 + Math.floor(Math.random() * 8); // 12-19px
    const speed = 0.5 + Math.random() * 1.5; // 0.5-2倍速

    // 生成时间戳（基于视频时间）
    const baseDate = new Date('2024-07-05 11:00:00');
    const timestampDate = new Date(baseDate.getTime() + time * 1000);
    const timestamp = timestampDate
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })
      .replace(/\//g, '-');

    const danmakuItem: DanmakuItem = {
      id: `danmaku_${i + 1}`,
      content,
      time: parseFloat(time.toFixed(1)),
      color,
      fontSize,
      speed,
      username,
    };

    const interactiveItem: InteractiveMessageItem = {
      id: `interactive_${i + 1}`,
      username,
      content,
      time: parseFloat(time.toFixed(1)),
      timestamp,
    };

    danmakuData.push(danmakuItem);
    interactiveData.push(interactiveItem);
  }

  // 按时间排序
  danmakuData.sort((a, b) => a.time - b.time);
  interactiveData.sort((a, b) => a.time - b.time);

  return { danmakuData, interactiveData };
};

// 生成mock数据
const { danmakuData, interactiveData } = generateMockData();

// Mock 弹幕数据
export const mockDanmakuData: DanmakuItem[] = danmakuData;

// 互动信息mock数据
export const mockInteractiveMessages: InteractiveMessageItem[] = interactiveData;

// 直播间mock数据
export const mockLiveRooms = [
  { value: 'room1', label: '主播间001 - 美妆直播' },
  { value: 'room2', label: '主播间002 - 服装直播' },
  { value: 'room3', label: '主播间003 - 数码直播' },
  { value: 'room4', label: '主播间004 - 美食直播' },
  { value: 'room5', label: '主播间005 - 家居直播' },
  { value: 'room6', label: '主播间006 - 母婴直播' },
  { value: 'room7', label: '主播间007 - 运动健身' },
  { value: 'room8', label: '主播间008 - 图书文具' },
  { value: 'room9', label: '主播间009 - 汽车用品' },
  { value: 'room10', label: '主播间010 - 宠物用品' },
];

// 片段mock数据
export const mockSegments = [
  { value: 'segment1', label: '片段001 - 开场介绍' },
  { value: 'segment2', label: '片段002 - 产品展示' },
  { value: 'segment3', label: '片段003 - 互动环节' },
  { value: 'segment4', label: '片段004 - 优惠活动' },
  { value: 'segment5', label: '片段005 - 结尾总结' },
  { value: 'segment6', label: '片段006 - 用户答疑' },
  { value: 'segment7', label: '片段007 - 产品试用' },
  { value: 'segment8', label: '片段008 - 抽奖环节' },
  { value: 'segment9', label: '片段009 - 特别活动' },
  { value: 'segment10', label: '片段010 - 合作推广' },
];

// 弹幕配置
export const danmakuConfig = {
  maxShowCount: 10, // 最大同时显示弹幕数量
  defaultSpeed: 1, // 默认滚动速度
  defaultFontSize: 16, // 默认字体大小
  defaultColor: 'white', // 默认颜色
  trackHeight: 30, // 弹幕轨道高度
  trackCount: 8, // 弹幕轨道数量
  duration: 8000, // 弹幕滚动持续时间（毫秒）
};
// 2025年1月2日 开山ai结尾共生成123行代码
