import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type GetByRoomIdAndDateRequest = {
  current?: number /*当前页码,从1开始*/;
  liveDate?: string /*直播日期*/;
  roomId?: string /*直播间ID*/;
  sessionId?: string /*片段*/;
  size?: number /*分页大小*/;
};

export type GetByRoomIdAndDateResult = {
  endTime?: string /*片段结束时间*/;
  id?: string /*主键ID*/;
  liveDate?: string /*直播日期*/;
  roomId?: string /*直播间ID*/;
  segmentIndex?: number /*片段序号，从1开始*/;
  startTime?: string /*片段开始时间*/;
  videoUrl?: string /*视频地址 (OSS等)*/;
};

/**
 *根据直播间ID和日期查询视频片段
 */
export const getByRoomIdAndDate = (params: GetByRoomIdAndDateRequest) => {
  return Fetch<ResponseWithResult<GetByRoomIdAndDateResult>>(
    '/iasm/public/liveVideoSegment/getByRoomIdAndDate',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/liveVideoSegment/getByRoomIdAndDate') },
    },
  );
};

export type GetLiveDanmakuListRequest = {
  content?: string /*弹幕内容*/;
  current?: number /*当前页码,从1开始*/;
  liveDate?: string /*直播日期*/;
  sendTimeEnd?: string /*发送结束时间*/;
  sendTimeStart?: string /*发送开始时间*/;
  senderName?: string /*发送人昵称*/;
  sessionId?: string /*片段ID*/;
  size?: number /*分页大小*/;
};

export type GetLiveDanmakuListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    content?: string /*弹幕内容*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键ID*/;
    liveDate?: string /*直播日期*/;
    modifier?: string /*修改人*/;
    openId?: string /*直播间开放ID*/;
    relativeTime?: string /*视频中的秒数位置*/;
    roomId?: string /*直播间ID*/;
    segmentIndex?: number /*对应片段序号*/;
    sendTime?: string /*发送自然时间*/;
    senderName?: string /*发送人昵称*/;
    sessionId?: string /*片段ID*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *直播弹幕分页查询
 */
export const getLiveDanmakuList = (params: GetLiveDanmakuListRequest) => {
  return Fetch<ResponseWithResult<GetLiveDanmakuListResult>>('/iasm/public/liveDanmaku/list', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/liveDanmaku/list') },
  });
};

export type GetLiveVideoSegmentListRequest = {
  current?: number /*当前页码,从1开始*/;
  liveDate?: string /*直播日期*/;
  roomId?: string /*直播间ID*/;
  size?: number /*分页大小*/;
};

export type GetLiveVideoSegmentListResult = Array<{
  creator?: string /*创建人*/;
  endTime?: string /*片段结束时间*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*更新时间*/;
  id?: string /*主键ID*/;
  liveDate?: string /*直播日期*/;
  modifier?: string /*修改人*/;
  roomId?: string /*直播间ID*/;
  segmentIndex?: number /*片段序号，从1开始*/;
  segmentIndexName?: string /*片段序号中文名*/;
  sessionId?: string /*直播ID*/;
  startTime?: string /*片段开始时间*/;
  videoUrl?: string /*视频地址 (OSS等)*/;
}>;

/**
 *直播视频片段分页查询
 */
export const getLiveVideoSegmentList = (params: GetLiveVideoSegmentListRequest) => {
  return Fetch<ResponseWithResult<GetLiveVideoSegmentListResult>>(
    '/iasm/public/liveVideoSegment/list',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/liveVideoSegment/list') },
    },
  );
};
