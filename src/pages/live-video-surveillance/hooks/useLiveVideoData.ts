// ai生成
import { useState, useCallback } from 'react';
import { message } from 'antd';
import {
  getLiveVideoSegmentList,
  getByRoomIdAndDate,
  GetLiveVideoSegmentListRequest,
  GetByRoomIdAndDateRequest,
  GetLiveVideoSegmentListResult,
  GetByRoomIdAndDateResult,
} from '../services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

export interface UseLiveVideoDataReturn {
  segments: GetLiveVideoSegmentListResult;
  segmentsLoading: boolean;
  videoData: GetByRoomIdAndDateResult | null;
  videoLoading: boolean;
  error: string | null;
  fetchSegments: (params: GetLiveVideoSegmentListRequest) => Promise<void>;
  fetchVideo: (params: GetByRoomIdAndDateRequest) => Promise<string | null>;
  resetVideoData: () => void;
  formatSegmentOption: (segment: GetLiveVideoSegmentListResult[0]) => {
    value: string;
    label: string;
  };
}

/**
 * 直播视频数据管理Hook
 */
export const useLiveVideoData = (): UseLiveVideoDataReturn => {
  const [segments, setSegments] = useState<GetLiveVideoSegmentListResult>([]);
  const [segmentsLoading, setSegmentsLoading] = useState(false);
  const [videoData, setVideoData] = useState<GetByRoomIdAndDateResult | null>(null);
  const [videoLoading, setVideoLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取片段列表
  const fetchSegments = useCallback(async (params: GetLiveVideoSegmentListRequest) => {
    if (!params.roomId || !params.liveDate) {
      return;
    }

    setSegmentsLoading(true);
    setError(null);
    const result = await responseWithResultAsync({
      request: getLiveVideoSegmentList,
      params,
    });
    setSegments(result ?? []);
    setSegmentsLoading(false);
  }, []);

  // 获取视频数据
  const fetchVideo = useCallback(
    async (params: GetByRoomIdAndDateRequest): Promise<string | null> => {
      if (!params.roomId || !params.liveDate || !params.sessionId) {
        return null;
      }

      setVideoLoading(true);
      setError(null);
      const result = await responseWithResultAsync({
        request: getByRoomIdAndDate,
        params,
      });
      setVideoLoading(false);
      setVideoData(result ?? null);
      return result?.videoUrl || null;
    },
    [],
  );

  // 重置视频数据
  const resetVideoData = useCallback(() => {
    setVideoData(null);
    setSegments([]);
    setError(null);
  }, []);

  // 格式化片段选项
  const formatSegmentOption = useCallback(
    (segment: GetLiveVideoSegmentListResult[0]) => ({
      value: segment.id || '',
      label: `片段${segment.segmentIndex} - ${segment.startTime} ~ ${segment.endTime}`,
    }),
    [],
  );

  return {
    segments,
    segmentsLoading,
    videoData,
    videoLoading,
    error,
    fetchSegments,
    fetchVideo,
    resetVideoData,
    formatSegmentOption,
  };
};
// 2025年1月2日 开山ai结尾共生成92行代码
