// ai生成
import { useState, useCallback, useMemo } from 'react';
import {
  getLiveDanmakuList,
  GetLiveDanmakuListRequest,
  GetLiveDanmakuListResult,
} from '../services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

// 弹幕项类型定义 - 用于视频播放
export interface DanmakuItem {
  id: string;
  content: string;
  time: number; // 视频中的毫秒位置
  senderName: string;
  sendTime: string;
  fontSize?: number;
  color?: string;
}

export interface UseSharedDanmakuDataReturn {
  // 原始数据
  originalDanmakuList: GetLiveDanmakuListResult['records'];
  loading: boolean;
  error: string | null;

  // 用于视频播放的弹幕数据（基于时间截断）
  videoPlayDanmakus: DanmakuItem[];
  currentVideoTime: number;
  setCurrentVideoTime: (time: number) => void;

  // 用于右侧列表的弹幕数据（基于关键词过滤）
  filteredDanmakuList: GetLiveDanmakuListResult['records'];
  filterKeyword: string;
  setFilterKeyword: (keyword: string) => void;

  // 共享方法
  fetchDanmakuList: (params: GetLiveDanmakuListRequest) => Promise<void>;
  resetDanmakuData: () => void;
  formatVideoTime: (seconds: number) => string;
}

/**
 * 共享弹幕数据管理Hook
 * 为左边的视频弹幕和右边的弹幕列表提供统一的数据源
 */
export const useSharedDanmakuData = (): UseSharedDanmakuDataReturn => {
  // 原始弹幕数据
  const [originalDanmakuList, setOriginalDanmakuList] = useState<
    GetLiveDanmakuListResult['records']
  >([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 视频相关状态
  const [currentVideoTime, setCurrentVideoTime] = useState(0); // 当前视频时间（秒）

  // 过滤相关状态
  const [filterKeyword, setFilterKeyword] = useState('');

  // 获取弹幕列表 - 一次获取所有数据
  const fetchDanmakuList = useCallback(
    async (
      params: GetLiveDanmakuListRequest,
      DanmakuList?: GetLiveDanmakuListResult['records'],
    ) => {
      if (!params.sessionId) {
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const result = await responseWithResultAsync({
          request: getLiveDanmakuList,
          params: {
            ...params,
            size: 1000,
            current: (params?.current ?? 0) + 1, // 获取所有数据
          },
        });
        const newDanmakuList = [...(DanmakuList ?? []), ...(result?.records ?? [])];
        if (result?.total && result?.total > newDanmakuList.length) {
          fetchDanmakuList({ ...params, current: (params?.current ?? 0) + 1 }, newDanmakuList);
        } else {
          setOriginalDanmakuList(newDanmakuList);
          setLoading(false);
        }
      } catch (err) {
        setError('获取弹幕数据失败');
        console.error('获取弹幕数据失败:', err);
        setLoading(false);
      }
    },
    [],
  );

  // 转换为视频播放用的弹幕数据格式
  const convertToVideoDanmaku = useCallback(
    (danmakuRecord: NonNullable<GetLiveDanmakuListResult['records']>[number]): DanmakuItem => {
      // relativeTime是秒级别的字符串，转换为毫秒用于视频播放
      const timeInSeconds = danmakuRecord?.relativeTime ? Number(danmakuRecord.relativeTime) : 0;
      const timeInMs = timeInSeconds * 1000;

      return {
        id: danmakuRecord?.id || '',
        content: danmakuRecord?.content || '',
        time: timeInMs, // 转换为毫秒级别用于视频播放
        senderName: danmakuRecord?.senderName || '',
        sendTime: danmakuRecord?.sendTime || '',
        fontSize: 16,
        color: '#ffffff',
      };
    },
    [],
  );

  // 用于视频播放的弹幕数据（基于当前视频时间截断）
  const videoPlayDanmakus = useMemo(() => {
    if (!originalDanmakuList || originalDanmakuList.length === 0) {
      return [];
    }

    const currentTimeMs = currentVideoTime * 1000; // 转换为毫秒

    // 转换格式并根据当前视频时间进行截断
    return originalDanmakuList
      .map(convertToVideoDanmaku)
      .filter((danmaku) => {
        // 只显示当前时间前后30秒内的弹幕
        const timeDiff = Math.abs(danmaku.time - currentTimeMs);
        return timeDiff <= 30000; // 30秒 = 30000毫秒
      })
      .sort((a, b) => a.time - b.time); // 按时间排序
  }, [originalDanmakuList, currentVideoTime, convertToVideoDanmaku]);

  // 用于右侧列表的弹幕数据（基于关键词过滤）
  const filteredDanmakuList = useMemo(() => {
    if (!filterKeyword.trim()) {
      return originalDanmakuList;
    }

    const keyword = filterKeyword.toLowerCase();
    return originalDanmakuList?.filter(
      (item) =>
        item?.content?.toLowerCase().includes(keyword) ||
        item?.senderName?.toLowerCase().includes(keyword),
    );
  }, [originalDanmakuList, filterKeyword]);

  // 重置弹幕数据
  const resetDanmakuData = useCallback(() => {
    setOriginalDanmakuList([]);
    setFilterKeyword('');
    setCurrentVideoTime(0);
    setError(null);
  }, []);

  // 格式化视频时间显示 - 输入秒，输出mm:ss格式
  const formatVideoTime = useCallback((seconds: number): string => {
    const totalSeconds = Math.floor(seconds);
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    const padZero = (num: number): string => (num < 10 ? `0${num}` : `${num}`);
    return `${padZero(mins)}:${padZero(secs)}`;
  }, []);

  return {
    // 原始数据
    originalDanmakuList,
    loading,
    error,

    // 视频播放数据
    videoPlayDanmakus,
    currentVideoTime,
    setCurrentVideoTime,

    // 列表过滤数据
    filteredDanmakuList,
    filterKeyword,
    setFilterKeyword,

    // 共享方法
    fetchDanmakuList,
    resetDanmakuData,
    formatVideoTime,
  };
};

// 导出类型
export type { GetLiveDanmakuListRequest, GetLiveDanmakuListResult } from '../services';
// 2025年1月2日 开山ai结尾共生成154行代码
