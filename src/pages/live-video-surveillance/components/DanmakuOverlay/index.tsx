// ai生成
import React, { useEffect, useState, useRef } from 'react';
import { DanmakuItem } from '../../hooks/useSharedDanmakuData';
import { danmakuConfig } from '../../mock/danmaku';
import styles from './index.module.less';

interface DanmakuOverlayProps {
  videoTime: number; // 当前视频时间
  danmakuData: DanmakuItem[]; // 弹幕数据
  isPlaying: boolean; // 视频是否正在播放
  showDanmaku: boolean; // 是否显示弹幕
  playbackRate: number; // 视频播放倍速
}

interface ActiveDanmaku extends DanmakuItem {
  trackIndex: number; // 轨道索引
  startTime: number; // 开始显示的时间戳
  left: number; // 当前左边距离
}

const DanmakuOverlay: React.FC<DanmakuOverlayProps> = ({
  videoTime,
  danmakuData,
  isPlaying,
  showDanmaku,
  playbackRate,
}) => {
  const [activeDanmakus, setActiveDanmakus] = useState<ActiveDanmaku[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const trackUsage = useRef<boolean[]>(new Array(danmakuConfig.trackCount).fill(false));
  const lastVideoTime = useRef<number>(0);

  // 获取可用轨道
  const getAvailableTrack = (): number => {
    for (let i = 0; i < danmakuConfig.trackCount; i++) {
      if (!trackUsage.current[i]) {
        trackUsage.current[i] = true;
        return i;
      }
    }
    // 如果没有可用轨道，随机选择一个
    const randomTrack = Math.floor(Math.random() * danmakuConfig.trackCount);
    return randomTrack;
  };

  // 释放轨道
  const releaseTrack = (trackIndex: number) => {
    trackUsage.current[trackIndex] = false;
  };

  // 处理新弹幕
  useEffect(() => {
    if (!isPlaying || !showDanmaku) return;

    const currentTimeMs = videoTime * 1000; // 将视频时间转换为毫秒
    const timeDiff = Math.abs(currentTimeMs - lastVideoTime.current);

    // 如果时间跳跃太大（比如拖拽进度条），清空当前弹幕
    if (timeDiff > 1000) {
      // 1秒 = 1000毫秒
      setActiveDanmakus([]);
      trackUsage.current.fill(false);
    }

    // 查找需要显示的新弹幕
    const newDanmakus = danmakuData.filter((danmaku) => {
      const showTime = danmaku.time; // 已经是毫秒级
      return showTime >= lastVideoTime.current && showTime <= currentTimeMs;
    });

    // 调试日志
    if (danmakuData.length > 0 && newDanmakus.length > 0) {
      console.log(
        `发现新弹幕: ${newDanmakus.length}条, 当前时间: ${currentTimeMs}ms, 弹幕总数: ${danmakuData.length}`,
      );
    }

    // 添加新弹幕
    if (newDanmakus.length > 0 && containerRef.current) {
      const containerWidth = containerRef.current.clientWidth;

      newDanmakus.forEach((danmaku) => {
        const trackIndex = getAvailableTrack();
        const newActiveDanmaku: ActiveDanmaku = {
          ...danmaku,
          trackIndex,
          startTime: Date.now(),
          left: containerWidth,
        };

        setActiveDanmakus((prev) => [...prev, newActiveDanmaku]);
      });
    }

    lastVideoTime.current = currentTimeMs;
  }, [videoTime, isPlaying, showDanmaku, danmakuData]);

  // 动画更新弹幕位置
  useEffect(() => {
    if (!isPlaying || !showDanmaku) return;

    const animationFrame = () => {
      setActiveDanmakus((prev) => {
        const now = Date.now();
        const containerWidth = containerRef.current?.clientWidth || 0;

        return prev
          .map((danmaku) => {
            const elapsed = now - danmaku.startTime;
            // 根据播放倍速调整弹幕滚动速度
            const adjustedDuration = danmakuConfig.duration / playbackRate;
            const progress = elapsed / adjustedDuration;
            const newLeft = containerWidth - (containerWidth + 200) * progress; // 200是文字宽度估算

            return {
              ...danmaku,
              left: newLeft,
            };
          })
          .filter((danmaku) => {
            const shouldKeep = danmaku.left > -200; // 超出左边界则移除
            if (!shouldKeep) {
              releaseTrack(danmaku.trackIndex);
            }
            return shouldKeep;
          });
      });
    };

    let animationId: number;
    const animate = () => {
      animationFrame();
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [isPlaying, showDanmaku, playbackRate]);

  // 清空弹幕当暂停或隐藏弹幕时
  useEffect(() => {
    if (!isPlaying || !showDanmaku) {
      setActiveDanmakus([]);
      trackUsage.current.fill(false);
    }
  }, [isPlaying, showDanmaku]);

  if (!showDanmaku) {
    return null;
  }

  return (
    <div ref={containerRef} className={styles.danmakuOverlay}>
      {activeDanmakus.map((danmaku) => (
        <div
          key={`${danmaku.id}-${danmaku.startTime}`}
          className={styles.danmakuItem}
          style={{
            top: danmaku.trackIndex * danmakuConfig.trackHeight,
            left: danmaku.left,
            fontSize: danmaku.fontSize || danmakuConfig.defaultFontSize,
            color: 'white', // 强制使用白色文字
          }}
        >
          {danmaku.content}
        </div>
      ))}
    </div>
  );
};

export default DanmakuOverlay;
// 2025年1月2日 开山ai结尾共生成127行代码
