// ai生成
.danmakuOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  z-index: 10;
}

.danmakuItem {
  position: absolute;
  white-space: nowrap;
  font-weight: 500;
  color: white;
  text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;
  user-select: none;
  pointer-events: none;
  line-height: 30px;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  transition: none;
  will-change: transform;
}

/* 弹幕轨道可视化（开发时调试用，生产环境可注释） */
.danmakuOverlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 可以添加轨道线用于调试 */
  /* 
  background-image: repeating-linear-gradient(
    transparent,
    transparent 29px,
    rgba(255, 0, 0, 0.1) 29px,
    rgba(255, 0, 0, 0.1) 30px
  );
  */
  pointer-events: none;
}
// 2025年1月2日 开山ai结尾共生成36行代码
