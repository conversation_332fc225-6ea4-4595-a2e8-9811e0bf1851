// ai生成
.videoControls {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.6) 50%,
    transparent 100%
  );
  padding: 10px 15px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 20;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}

.progressContainer {
  margin-bottom: 10px;
}

.progressSlider {
  margin-bottom: 5px;

  :global(.ant-slider-rail) {
    background-color: rgba(255, 255, 255, 0.3);
    height: 4px;
  }

  :global(.ant-slider-track) {
    background-color: #1890ff;
    height: 4px;
  }

  :global(.ant-slider-handle) {
    border: 2px solid #1890ff;
    background-color: #fff;
    width: 12px;
    height: 12px;
    margin-top: -4px;

    &:focus {
      box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.2);
    }
  }
}

.timeDisplay {
  color: white;
  font-size: 12px;
  text-align: center;

  span {
    margin: 0 2px;
  }
}

.controlsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leftControls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rightControls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.playButton {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  :global(.anticon) {
    font-size: 16px;
  }
}

.danmakuButton {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 40px;
  padding: 0 12px;

  :global(.anticon) {
    font-size: 14px;
  }
}

.volumeControl {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volumeButton {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
  }
}

.volumeSliderContainer {
  width: 80px;
}

.volumeSlider {
  :global(.ant-slider-rail) {
    background-color: rgba(255, 255, 255, 0.3);
    height: 3px;
  }

  :global(.ant-slider-track) {
    background-color: #1890ff;
    height: 3px;
  }

  :global(.ant-slider-handle) {
    border: 2px solid #1890ff;
    background-color: #fff;
    width: 10px;
    height: 10px;
    margin-top: -3.5px;
  }
}

.speedControl {
  display: flex;
  align-items: center;
  gap: 6px;
}

.speedLabel {
  color: white;
  font-size: 12px;
  white-space: nowrap;
}

.speedSelect {
  width: 70px;
  height: 40px;

  :global(.ant-select-selector) {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    height: 40px !important;
    line-height: 38px !important;
  }

  :global(.ant-select-selection-search) {
    color: white !important;
  }

  :global(.ant-select-selection-item) {
    color: white !important;
    line-height: 38px !important;
  }

  :global(.ant-select-arrow) {
    color: white !important;
  }

  &:hover :global(.ant-select-selector) {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  &:focus :global(.ant-select-selector) {
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  }
}

.downloadButton {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0 12px;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controlsRow {
    flex-direction: column;
    gap: 10px;
  }

  .leftControls,
  .rightControls {
    width: 100%;
    justify-content: center;
  }

  .volumeSliderContainer {
    width: 60px;
  }
}
// 2025年1月2日 开山ai结尾共生成153行代码
