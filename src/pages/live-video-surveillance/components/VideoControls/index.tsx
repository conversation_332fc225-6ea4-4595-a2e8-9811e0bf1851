// ai生成
import React from 'react';
import { But<PERSON>, Slider, Select, Icon, Tooltip } from 'antd';
import styles from './index.module.less';

const { Option } = Select;

interface VideoControlsProps {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  showDanmaku: boolean;
  isMuted: boolean;
  onPlayPause: () => void;
  onSeek: (time: number) => void;
  onVolumeChange: (volume: number) => void;
  onPlaybackRateChange: (rate: number) => void;
  onToggleDanmaku: () => void;
  onToggleMute: () => void;
  onDownload: () => void;
}

const VideoControls: React.FC<VideoControlsProps> = ({
  isPlaying,
  currentTime,
  duration,
  volume,
  playbackRate,
  showDanmaku,
  isMuted,
  onPlayPause,
  onSeek,
  onVolumeChange,
  onPlaybackRateChange,
  onToggleDanmaku,
  onToggleMute,
  onDownload,
}) => {
  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    const padZero = (num: number): string => (num < 10 ? `0${num}` : `${num}`);
    return `${padZero(mins)}:${padZero(secs)}`;
  };

  // 进度条格式化显示
  const formatTooltip = (value?: number): React.ReactNode => {
    if (value === undefined) return '';
    return formatTime(value);
  };

  return (
    <div className={styles.videoControls}>
      {/* 进度条 */}
      <div className={styles.progressContainer}>
        <Slider
          className={styles.progressSlider}
          min={0}
          max={duration || 100}
          value={currentTime}
          onChange={(value) => {
            if (typeof value === 'number') {
              onSeek(value);
            }
          }}
          step={0.1}
          tipFormatter={formatTooltip}
          tooltipVisible={false}
        />
        <div className={styles.timeDisplay}>
          <span>{formatTime(currentTime)}</span>
          <span>/</span>
          <span>{formatTime(duration || 0)}</span>
        </div>
      </div>

      {/* 控制按钮区域 */}
      <div className={styles.controlsRow}>
        {/* 左侧控制组 */}
        <div className={styles.leftControls}>
          {/* 播放/暂停按钮 */}
          <Tooltip title={isPlaying ? '暂停' : '播放'}>
            <Button
              type="primary"
              shape="circle"
              icon={isPlaying ? 'pause' : 'caret-right'}
              onClick={onPlayPause}
              className={styles.playButton}
            />
          </Tooltip>

          {/* 弹幕开关 */}
          <Tooltip title={showDanmaku ? '关闭弹幕' : '开启弹幕'}>
            <Button
              type={showDanmaku ? 'primary' : 'default'}
              onClick={onToggleDanmaku}
              className={styles.danmakuButton}
            >
              <Icon type="message" />
              弹幕
            </Button>
          </Tooltip>

          {/* 音量控制 */}
          <div className={styles.volumeControl}>
            <Tooltip title={isMuted ? '取消静音' : '静音'}>
              <Button
                type="ghost"
                icon={isMuted ? 'sound' : volume > 50 ? 'sound' : volume > 0 ? 'sound' : 'sound'}
                onClick={onToggleMute}
                className={styles.volumeButton}
              />
            </Tooltip>
            <div className={styles.volumeSliderContainer}>
              <Slider
                className={styles.volumeSlider}
                min={0}
                max={100}
                value={isMuted ? 0 : volume}
                onChange={(value) => {
                  if (typeof value === 'number') {
                    onVolumeChange(value);
                  }
                }}
                tipFormatter={(value) => `${value}%`}
                tooltipVisible={false}
              />
            </div>
          </div>
        </div>

        {/* 右侧控制组 */}
        <div className={styles.rightControls}>
          {/* 倍速选择 */}
          <div className={styles.speedControl}>
            <span className={styles.speedLabel}>倍速:</span>
            <Select
              value={playbackRate}
              onChange={onPlaybackRateChange}
              className={styles.speedSelect}
              size="large"
            >
              <Option value={1}>1.0x</Option>
              <Option value={1.5}>1.5x</Option>
              <Option value={2}>2.0x</Option>
              <Option value={3}>3.0x</Option>
            </Select>
          </div>

          {/* 下载按钮 */}
          <Tooltip title="下载视频">
            <Button
              type="default"
              icon="download"
              onClick={onDownload}
              className={styles.downloadButton}
            >
              下载
            </Button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default VideoControls;
// 2025年1月2日 开山ai结尾共生成171行代码
