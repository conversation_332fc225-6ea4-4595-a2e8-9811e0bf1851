// ai生成
.videoPlayerContainer {
  position: relative;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  user-select: none;

  &:focus {
    outline: 2px solid #1890ff;
  }
}

.videoElement {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: contain;
  background-color: #000;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
}

.loadingSpinner {
  color: white;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;

  &::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.controlsContainer {
  opacity: 0;
  transition: opacity 0.3s ease;

  &.visible {
    opacity: 1;
  }
}

.centerPlayButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 25;
  backdrop-filter: blur(4px);

  &:hover {
    background: rgba(0, 0, 0, 0.8);
    border-color: #1890ff;
    transform: translate(-50%, -50%) scale(1.1);
  }

  &:active {
    transform: translate(-50%, -50%) scale(0.95);
  }
}

.playIcon {
  color: white;
  font-size: 24px;
  margin-left: 4px; /* 视觉居中调整 */
  line-height: 1;
  transition: color 0.3s ease;

  .centerPlayButton:hover & {
    color: #1890ff;
  }
}

/* 全屏模式样式 */
.videoPlayerContainer:fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0;
}

.videoPlayerContainer:-webkit-full-screen {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0;
}

.videoPlayerContainer:-moz-full-screen {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0;
}

.videoPlayerContainer:-ms-fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .centerPlayButton {
    width: 60px;
    height: 60px;
  }

  .playIcon {
    font-size: 18px;
  }

  .loadingSpinner {
    font-size: 14px;

    &::before {
      width: 16px;
      height: 16px;
    }
  }
}

/* 键盘导航提示 */
.videoPlayerContainer:focus::after {
  content: '空格键: 播放/暂停 | ←→: 快退/快进 | ↑↓: 音量 | D: 弹幕 | M: 静音';
  position: absolute;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 40;
  opacity: 0;
  animation: fadeInOut 3s ease;
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0;
  }
  10%,
  90% {
    opacity: 1;
  }
}
// 2025年1月2日 开山ai结尾共生成143行代码
