// ai生成
import React, { useRef, useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { message } from 'antd';
import DanmakuOverlay from '../DanmakuOverlay';
import VideoControls from '../VideoControls';
import { DanmakuItem } from '../../hooks/useSharedDanmakuData';
import styles from './index.module.less';

interface VideoPlayerProps {
  videoSrc?: string;
  width?: number | string;
  height?: number | string;
  className?: string;
  onSeekToTime?: (time: number) => void; // 新增：外部控制定位时间的回调
  danmakuData?: DanmakuItem[]; // 弹幕数据
  onVideoTimeUpdate?: (time: number) => void; // 视频时间更新回调
}

export interface VideoPlayerRef {
  seekTo: (time: number) => void;
}

const VideoPlayer = forwardRef<VideoPlayerRef, VideoPlayerProps>(
  (
    {
      videoSrc = 'src/pages/live-video-surveillance/mock/mockVideo.mp4', // 默认使用 mock 视频
      width = '100%',
      height = 400,
      className,
      onSeekToTime,
      danmakuData = [],
      onVideoTimeUpdate,
    },
    ref,
  ) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // 播放状态
    const [isPlaying, setIsPlaying] = useState<boolean>(false);
    const [currentTime, setCurrentTime] = useState<number>(0);
    const [duration, setDuration] = useState<number>(0);
    const [volume, setVolume] = useState<number>(80);
    const [playbackRate, setPlaybackRate] = useState<number>(1);
    const [showDanmaku, setShowDanmaku] = useState<boolean>(true);
    const [isMuted, setIsMuted] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [showControls, setShowControls] = useState<boolean>(false);

    // 弹幕数据从props接收

    // 暴露给外部组件的方法
    useImperativeHandle(ref, () => ({
      seekTo: (time: number) => {
        handleSeek(time);
      },
    }));

    // 初始化视频
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      const handleLoadedMetadata = () => {
        setDuration(video.duration);
        setIsLoading(false);
      };

      const handleTimeUpdate = () => {
        setCurrentTime(video.currentTime);
        // 同步视频时间到父组件
        onVideoTimeUpdate?.(video.currentTime);
      };

      const handleEnded = () => {
        setIsPlaying(false);
      };

      const handleError = () => {
        message.error('视频加载失败');
        setIsLoading(false);
      };

      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('timeupdate', handleTimeUpdate);
      video.addEventListener('ended', handleEnded);
      video.addEventListener('error', handleError);

      // 设置初始音量
      video.volume = volume / 100;

      return () => {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('timeupdate', handleTimeUpdate);
        video.removeEventListener('ended', handleEnded);
        video.removeEventListener('error', handleError);
      };
    }, [videoSrc, volume]);

    // 播放/暂停控制
    const handlePlayPause = () => {
      const video = videoRef.current;
      if (!video) return;

      if (isPlaying) {
        video.pause();
        setIsPlaying(false);
      } else {
        video
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch(() => {
            message.error('视频播放失败');
          });
      }
    };

    // 进度条拖拽
    const handleSeek = (time: number) => {
      const video = videoRef.current;
      if (!video) return;

      video.currentTime = time;
      setCurrentTime(time);

      // 触发外部回调
      onSeekToTime?.(time);
    };

    // 音量控制
    const handleVolumeChange = (newVolume: number) => {
      const video = videoRef.current;
      if (!video) return;
      setVolume(newVolume);
      video.volume = newVolume / 100;

      // 如果设置音量大于0，取消静音状态
      if (newVolume > 0 && isMuted) {
        handleToggleMute();
      }
    };

    // 倍速控制
    const handlePlaybackRateChange = (rate: number) => {
      const video = videoRef.current;
      if (!video) return;

      setPlaybackRate(rate);
      video.playbackRate = rate;
    };

    // 弹幕开关
    const handleToggleDanmaku = () => {
      setShowDanmaku(!showDanmaku);
      message.success(showDanmaku ? '弹幕已关闭' : '弹幕已开启');
    };

    // 静音切换
    const handleToggleMute = () => {
      const video = videoRef.current;
      if (!video) return;

      const newMutedState = !isMuted;
      setIsMuted(newMutedState);
      video.muted = newMutedState;
    };

    // 下载视频
    const handleDownload = () => {
      try {
        const link = document.createElement('a');
        link.href = videoSrc;
        link.download = 'video.mp4';
        link.click();
        message.success('开始下载视频');
      } catch (error) {
        message.error('下载失败');
      }
    };

    // 鼠标移动显示控制器
    const handleMouseMove = () => {
      setShowControls(true);
    };

    const handleMouseLeave = () => {
      setShowControls(false);
    };

    // 键盘快捷键
    useEffect(() => {
      const handleKeyPress = (e: KeyboardEvent) => {
        if (!containerRef.current?.contains(document.activeElement)) return;

        switch (e.code) {
          case 'Space':
            e.preventDefault();
            handlePlayPause();
            break;
          case 'ArrowLeft':
            e.preventDefault();
            handleSeek(Math.max(0, currentTime - 10));
            break;
          case 'ArrowRight':
            e.preventDefault();
            handleSeek(Math.min(duration, currentTime + 10));
            break;
          case 'ArrowUp':
            e.preventDefault();
            handleVolumeChange(Math.min(100, volume + 10));
            break;
          case 'ArrowDown':
            e.preventDefault();
            handleVolumeChange(Math.max(0, volume - 10));
            break;
          case 'KeyM':
            e.preventDefault();
            handleToggleMute();
            break;
          case 'KeyD':
            e.preventDefault();
            handleToggleDanmaku();
            break;
        }
      };

      document.addEventListener('keydown', handleKeyPress);
      return () => document.removeEventListener('keydown', handleKeyPress);
    }, [currentTime, duration, volume, isPlaying]);

    return (
      <div
        ref={containerRef}
        className={`${styles.videoPlayerContainer} ${className || ''}`}
        style={{ width, height }}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        tabIndex={0}
      >
        {/* 视频元素 */}
        <video
          ref={videoRef}
          className={styles.videoElement}
          src={videoSrc}
          preload="metadata"
          onDoubleClick={handlePlayPause}
        />

        {/* 加载状态 */}
        {isLoading && (
          <div className={styles.loadingOverlay}>
            <div className={styles.loadingSpinner}>加载中...</div>
          </div>
        )}

        {/* 弹幕层 */}
        {/* 调试信息：确认弹幕数据传递 */}
        {danmakuData.length > 0 &&
          console.log(`VideoPlayer: 收到弹幕数据 ${danmakuData.length} 条`)}
        <DanmakuOverlay
          videoTime={currentTime}
          danmakuData={danmakuData}
          isPlaying={isPlaying}
          showDanmaku={showDanmaku}
          playbackRate={playbackRate}
        />

        {/* 控制器 */}
        <div
          className={`${styles.controlsContainer} ${
            showControls || !isPlaying ? styles.visible : ''
          }`}
        >
          <VideoControls
            isPlaying={isPlaying}
            currentTime={currentTime}
            duration={duration}
            volume={volume}
            playbackRate={playbackRate}
            showDanmaku={showDanmaku}
            isMuted={isMuted}
            onPlayPause={handlePlayPause}
            onSeek={handleSeek}
            onVolumeChange={handleVolumeChange}
            onPlaybackRateChange={handlePlaybackRateChange}
            onToggleDanmaku={handleToggleDanmaku}
            onToggleMute={handleToggleMute}
            onDownload={handleDownload}
          />
        </div>

        {/* 中央播放按钮 */}
        {!isPlaying && !isLoading && (
          <div className={styles.centerPlayButton} onClick={handlePlayPause}>
            <div className={styles.playIcon}>▶</div>
          </div>
        )}
      </div>
    );
  },
);

export default VideoPlayer;
// 2025年1月2日 开山ai结尾共生成214行代码
