// ai生成
import React, { useState, useEffect } from 'react';
import { message } from 'antd';
// import { useLiveRoomData } from '../../hooks/useLiveRoomData';
import { useLiveVideoData } from '../../hooks/useLiveVideoData';
import { UseSharedDanmakuDataReturn } from '../../hooks/useSharedDanmakuData';
import SearchForm, { SearchFormValues } from '../SearchForm';
import DanmakuList from '../DanmakuList';
import styles from './index.module.less';
import { useLiveRoomList } from '@/hooks/useLiveRoomList';
import { LiveRoomListAuthResult } from '@/pages/selection-flow-board/services/yml';

interface InteractiveInfoProps {
  onSeekToTime?: (time: number) => void; // 点击弹幕定位视频的回调
  onVideoQuery?: (videoUrl: string) => void; // 查询视频成功的回调，传递视频URL
  onVideoReset?: () => void; // 重置视频的回调
  sharedDanmakuData: UseSharedDanmakuDataReturn; // 共享弹幕数据
}

const InteractiveInfo: React.FC<InteractiveInfoProps> = ({
  onSeekToTime,
  onVideoQuery,
  onVideoReset,
  sharedDanmakuData,
}) => {
  const [searchValues, setSearchValues] = useState<SearchFormValues>({});

  // 使用自定义hooks
  const {
    liveList,
    loading: liveRoomsLoading,
    getLiveRoomList,
    handleSearch: handleSearchLiveRoom,
  } = useLiveRoomList();
  const formatRoomOption = (item: LiveRoomListAuthResult[number]) => {
    return {
      value: item?.id,
      label: item?.buName + '-' + item?.name,
    };
  };

  const {
    segments,
    segmentsLoading,
    videoLoading,
    fetchSegments,
    fetchVideo,
    resetVideoData,
    formatSegmentOption,
  } = useLiveVideoData();

  // 从props中获取弹幕数据
  const {
    filteredDanmakuList,
    loading: danmakuLoading,
    filterKeyword,
    fetchDanmakuList,
    setFilterKeyword,
    resetDanmakuData,
    formatVideoTime,
  } = sharedDanmakuData;

  // 处理搜索 - 同时查询视频和弹幕
  const handleSearch = async () => {
    if (!searchValues.roomId || !searchValues.liveDate || !searchValues.sessionId) {
      message.warning('请选择直播间、日期和片段');
      return;
    }

    try {
      // 先查询视频
      const videoUrl = await fetchVideo({
        roomId: searchValues.roomId,
        liveDate: searchValues.liveDate,
        sessionId: searchValues.sessionId,
      });

      if (videoUrl && onVideoQuery) {
        onVideoQuery(videoUrl);
      }

      // 查询弹幕数据
      await fetchDanmakuList({
        sessionId: searchValues.sessionId,
        liveDate: searchValues.liveDate,
      });
    } catch (error) {
      message.error('查询失败，请重试');
    }
  };

  // 处理重置 - 同时重置视频和弹幕
  const handleReset = () => {
    setSearchValues({});
    setFilterKeyword('');
    resetVideoData();
    resetDanmakuData();

    // 重置视频
    if (onVideoReset) {
      onVideoReset();
    }

    message.info('已重置查询条件');
  };

  // 处理表单值变化
  const handleValuesChange = (newValues: Partial<SearchFormValues>) => {
    setSearchValues((prev) => {
      const updated = { ...prev, ...newValues };

      // 如果是更新过滤关键词，直接设置到弹幕hook中
      if (newValues.filterKeyword !== undefined) {
        setFilterKeyword(newValues.filterKeyword);
      }

      return updated;
    });
  };

  // 当直播间或日期变化时，自动获取片段列表
  useEffect(() => {
    if (searchValues.roomId && searchValues.liveDate) {
      fetchSegments({
        roomId: searchValues.roomId,
        liveDate: searchValues.liveDate,
      });
    }
  }, [searchValues.roomId, searchValues.liveDate, fetchSegments]);

  // 处理弹幕定位视频
  const handleSeekToTime = (time: number) => {
    if (onSeekToTime) {
      onSeekToTime(time);
      message.success(`已定位到视频 ${time.toFixed(1)} 秒处`);
    }
  };

  const isLoading = videoLoading || danmakuLoading;

  return (
    <div className={styles.interactiveInfoContainer}>
      {/* 查询条件区域 */}
      <SearchForm
        values={{ ...searchValues, filterKeyword }}
        liveList={liveList}
        segments={segments}
        liveRoomsLoading={liveRoomsLoading}
        segmentsLoading={segmentsLoading}
        loading={isLoading}
        onValuesChange={handleValuesChange}
        onSearch={handleSearch}
        onReset={handleReset}
        formatRoomOption={formatRoomOption}
        formatSegmentOption={formatSegmentOption}
        getLiveRoomList={getLiveRoomList}
        handleSearchLiveRoom={handleSearchLiveRoom}
      />

      {/* 弹幕信息区域 */}
      <DanmakuList
        danmakuList={filteredDanmakuList}
        loading={danmakuLoading}
        onSeekToTime={handleSeekToTime}
        formatVideoTime={formatVideoTime}
      />
    </div>
  );
};

export default InteractiveInfo;
// 2025年1月2日 开山ai结尾共生成223行代码
