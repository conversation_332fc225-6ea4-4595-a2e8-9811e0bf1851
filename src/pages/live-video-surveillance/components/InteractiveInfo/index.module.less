// ai生成
.interactiveInfoContainer {
  // height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 8px;
}

.searchSection {
  flex-shrink: 0;
  margin-bottom: 16px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.searchForm {
  .formRow {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    &:last-of-type {
      margin-bottom: 8px;
    }
  }

  .formItem {
    flex: 1;
    margin-bottom: 0;

    :global(.ant-form-item-label) {
      padding-bottom: 4px;
    }

    :global(.ant-form-item-label > label) {
      font-weight: 500;
      color: #333;
    }
  }

  .buttonRow {
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    margin-top: 8px;
  }

  .searchButton {
    min-width: 80px;
  }

  .resetButton {
    min-width: 80px;
  }
}

.messageSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;

  .resultCount {
    font-size: 14px;
    font-weight: 400;
    color: #666;
  }
}

// 原有的messageList样式保留作为备用
.messageList {
  // flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: white;
  height: calc(100vh - 410px);

  :global(.ant-list-item) {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }
}

// ai生成 - 虚拟滚动容器样式
.virtualMessageContainer {
  flex: 1;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background: white;
  overflow: hidden;
}

.virtualMessageList {
  width: 100%;

  // 自定义虚拟滚动的滚动条样式
  :global(.virtual-list-container) {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 虚拟列表项样式
  :global(.virtual-list-item) {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f5f5f5;
    }
  }
}
// 2025年1月2日 开山ai结尾共生成37行代码

.messageItem {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  height: 80px; // 固定高度，与虚拟滚动设置一致
  display: flex;
  align-items: center;

  &:hover {
    background-color: #f5f5f5;
    transform: translateX(2px);
  }

  &:active {
    background-color: #e6f7ff;
  }
}

.messageContent {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.messageMain {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

.username {
  font-weight: 600;
  color: #1890ff;
  flex-shrink: 0;
  font-size: 14px;
}

.content {
  color: #333;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  // 限制最大行数，避免高度不一致
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.messageTime {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  margin-left: 12px;
}

.videoTime {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #91d5ff;
}

.timestamp {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.emptyState {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  min-height: 200px;

  p {
    margin: 4px 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .searchForm {
    .formRow {
      flex-direction: column;
      gap: 8px;
    }

    .buttonRow {
      justify-content: center;
    }
  }

  .messageContent {
    flex-direction: column;
    gap: 8px;
  }

  .messageTime {
    align-items: flex-start;
    flex-direction: row;
    gap: 8px;
    margin-left: 0;
  }

  .messageItem {
    height: auto; // 移动端允许自适应高度
    min-height: 80px;
  }
}

/* 原有滚动条样式 - 备用 */
.messageList {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
// 2025年1月2日 开山ai结尾共生成235行代码
