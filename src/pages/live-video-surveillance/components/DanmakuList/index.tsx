// ai生成
import React from 'react';
import { VirtualList } from '@/components/VirtualList';
import { GetLiveDanmakuListResult } from '../../services';
import styles from './index.module.less';
import moment from 'moment';

interface DanmakuListProps {
  danmakuList: GetLiveDanmakuListResult['records'];
  loading: boolean;
  onSeekToTime?: (time: number) => void;
  formatVideoTime: (seconds: number) => string;
}

const DanmakuList: React.FC<DanmakuListProps> = ({
  danmakuList,
  loading,
  onSeekToTime,
  formatVideoTime,
}) => {
  // 处理点击弹幕项
  const handleDanmakuClick = (item: NonNullable<GetLiveDanmakuListResult['records']>[number]) => {
    if (onSeekToTime) {
      // relativeTime是秒级，直接传递给视频播放器
      const timeInSeconds = item?.relativeTime ? Number(item?.relativeTime) : 0;
      onSeekToTime(timeInSeconds);
    }
  };

  // 虚拟列表项渲染函数
  const renderDanmakuItem = (item: NonNullable<GetLiveDanmakuListResult['records']>[number]) => {
    return (
      <div key={item.id} className={styles.danmakuItem} onClick={() => handleDanmakuClick(item)}>
        <div className={styles.danmakuContent}>
          <div className={styles.danmakuMain}>
            <span className={styles.username}>{item.senderName}：</span>
            <span className={styles.content}>{item.content}</span>
          </div>
          <div className={styles.danmakuTime}>
            <span className={styles.videoTime}>
              {formatVideoTime(item?.relativeTime ? Number(item?.relativeTime) : 0)}
            </span>
            <span className={styles.timestamp}>
              {item.sendTime ? moment(item.sendTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </span>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={styles.loadingState}>
        <div className={styles.loadingIcon}>⏳</div>
        <p>正在加载弹幕...</p>
      </div>
    );
  }

  if (danmakuList?.length === 0) {
    return (
      <div className={styles.emptyState}>
        <div className={styles.emptyIcon}>💬</div>
        <p>暂无弹幕数据</p>
        <p>请先选择直播间、日期和片段，然后点击查询</p>
      </div>
    );
  }

  return (
    <div className={styles.danmakuListContainer}>
      <div className={styles.sectionTitle}>
        弹幕信息
        <span className={styles.resultCount}>（共 {danmakuList?.length} 条）</span>
      </div>

      <div className={styles.virtualDanmakuContainer}>
        <VirtualList
          dataSource={danmakuList ?? []}
          height={window.innerHeight - 450} // 动态计算高度
          itemHeight={80} // 每个弹幕项的高度
          itemRender={renderDanmakuItem}
          bufferSize={10} // 缓冲区大小
          className={styles.virtualDanmakuList}
        />
      </div>
    </div>
  );
};

export default DanmakuList;
// 2025年1月2日 开山ai结尾共生成72行代码
