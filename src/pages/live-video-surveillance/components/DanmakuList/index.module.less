// ai生成
.danmakuListContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sectionTitle {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;

  .resultCount {
    font-size: 12px;
    color: #666;
    font-weight: normal;
  }
}

.virtualDanmakuContainer {
  flex: 1;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fafafa;
}

.virtualDanmakuList {
  height: 100%;
}

.danmakuItem {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  background-color: #fff;

  &:hover {
    background-color: #e6f7ff;
  }

  &:last-child {
    border-bottom: none;
  }
}

.danmakuContent {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.danmakuMain {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  line-height: 1.4;

  .username {
    color: #1890ff;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
  }

  .content {
    color: #333;
    word-break: break-word;
    flex: 1;
  }
}

.danmakuTime {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;

  .videoTime {
    background-color: #1890ff;
    color: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: 500;
  }

  .timestamp {
    color: #ccc;
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
  text-align: center;

  .emptyIcon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 4px 0;

    &:first-of-type {
      font-size: 16px;
      color: #666;
    }

    &:last-of-type {
      font-size: 14px;
      color: #999;
    }
  }
}

.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
  text-align: center;

  .loadingIcon {
    font-size: 32px;
    margin-bottom: 12px;
    animation: rotate 2s linear infinite;
  }

  p {
    color: #666;
    font-size: 14px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
// 2025年1月2日 开山ai结尾共生成124行代码
