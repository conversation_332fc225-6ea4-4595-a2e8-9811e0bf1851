// ai生成
import React, { useEffect } from 'react';
import { Form, Select, DatePicker, Input, Button } from 'antd';
import moment from 'moment';
import { GetLiveVideoSegmentListResult } from '../../services';
import styles from './index.module.less';
import {
  LiveRoomListAuthRequest,
  LiveRoomListAuthResult,
} from '@/pages/selection-flow-board/services/yml';

const { Option } = Select;
const FormItem = Form.Item;

export interface SearchFormValues {
  roomId?: string;
  liveDate?: string;
  sessionId?: string;
  filterKeyword?: string;
}

interface SearchFormProps {
  values: SearchFormValues;
  liveList: LiveRoomListAuthResult;
  segments: GetLiveVideoSegmentListResult;
  liveRoomsLoading: boolean;
  segmentsLoading: boolean;
  loading: boolean;
  onValuesChange: (values: Partial<SearchFormValues>) => void;
  onSearch: () => void;
  onReset: () => void;
  formatRoomOption: (room: LiveRoomListAuthResult[number]) => { value: string; label: string };
  formatSegmentOption: (segment: GetLiveVideoSegmentListResult[0]) => {
    value: string;
    label: string;
  };
  getLiveRoomList: (params: LiveRoomListAuthRequest) => void;
  handleSearchLiveRoom: (name: string) => void;
}

const SearchForm: React.FC<SearchFormProps> = ({
  values,
  liveList,
  segments,
  liveRoomsLoading,
  segmentsLoading,
  loading,
  onValuesChange,
  onSearch,
  onReset,
  formatRoomOption,
  formatSegmentOption,
  getLiveRoomList,
  handleSearchLiveRoom,
}) => {
  const handleLiveRoomChange = (value: string) => {
    onValuesChange({ roomId: value, sessionId: undefined }); // 重置片段选择
  };

  const handleDateChange = (date: moment.Moment | null) => {
    onValuesChange({
      liveDate: date ? date.format('YYYY-MM-DD') : undefined,
      sessionId: undefined, // 重置片段选择
    });
  };

  const handleSegmentChange = (value: string) => {
    onValuesChange({ sessionId: value });
  };

  const handleFilterKeywordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onValuesChange({ filterKeyword: e.target.value });
  };

  const canSearch = values.roomId && values.liveDate && values.sessionId;
  useEffect(() => {
    getLiveRoomList({});
  }, []);

  return (
    <div className={styles.searchFormContainer}>
      <Form layout="vertical" className={styles.searchForm}>
        <div className={styles.formRow}>
          <FormItem label="直播间" className={styles.formItem}>
            <Select
              placeholder="请选择直播间"
              showSearch
              filterOption={false}
              allowClear
              loading={liveRoomsLoading}
              value={values.roomId}
              onChange={handleLiveRoomChange}
              onSearch={handleSearchLiveRoom}
            >
              {liveList?.map((item) => {
                const option = formatRoomOption(item);
                return (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                );
              })}
            </Select>
          </FormItem>

          <FormItem label="直播日期" className={styles.formItem}>
            <DatePicker
              placeholder="请选择日期"
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              value={values.liveDate ? moment(values.liveDate) : undefined}
              onChange={handleDateChange}
            />
          </FormItem>
        </div>

        <div className={styles.formRow}>
          <FormItem label="片段" className={styles.formItem}>
            <Select
              placeholder="请选择片段"
              allowClear
              loading={segmentsLoading}
              disabled={!values.roomId || !values.liveDate}
              value={values.sessionId}
              onChange={handleSegmentChange}
            >
              {segments.map((segment) => (
                <Option key={segment?.sessionId} value={segment?.sessionId}>
                  片段{segment?.segmentIndex}
                </Option>
              ))}
            </Select>
          </FormItem>

          <FormItem label="互动信息" className={styles.formItem}>
            <Input
              placeholder="请输入关键词过滤弹幕"
              allowClear
              // value={values.filterKeyword}
              onInput={handleFilterKeywordChange}
            />
          </FormItem>
        </div>

        <div className={styles.buttonRow}>
          <Button
            type="primary"
            icon="search"
            onClick={onSearch}
            loading={loading}
            disabled={!canSearch}
            className={styles.searchButton}
          >
            查询
          </Button>
          <Button icon="reload" onClick={onReset} className={styles.resetButton} disabled={loading}>
            重置
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default SearchForm;
// 2025年1月2日 开山ai结尾共生成102行代码
