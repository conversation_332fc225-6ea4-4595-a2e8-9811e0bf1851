// ai生成
.searchFormContainer {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.searchForm {
  .formRow {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .formItem {
      flex: 1;
      margin-bottom: 0;
    }
  }

  .buttonRow {
    display: flex;
    gap: 12px;
    justify-content: flex-start;

    .searchButton {
      background-color: #1890ff;
    }

    .resetButton {
      background-color: #f5f5f5;
      color: #666;
      border-color: #d9d9d9;
    }
  }
}

@media (max-width: 768px) {
  .searchForm {
    .formRow {
      flex-direction: column;
      gap: 8px;
    }

    .buttonRow {
      flex-direction: column;
      align-items: stretch;
    }
  }
}
// 2025年1月2日 开山ai结尾共生成35行代码
