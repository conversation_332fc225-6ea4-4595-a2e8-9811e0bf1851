import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type GetAttendanceListRequest = {
  attendanceDateEnd?: string /*考勤日期结束*/;
  attendanceDateStart?: string /*考勤日期开始*/;
  attendanceStatus?:
    | 'NORMAL'
    | 'INSUFFICIENT_TIME'
    | 'MISSING_CARD'
    | 'ABSENT' /*考勤标签[AttendanceStatusEnum]*/;
  current?: number /*当前页码,从1开始*/;
  departmentId?: string /*部门（精确）*/;
  employeeName?: string /*员工姓名（模糊）*/;
  jobNo?: string /*工号（精确）*/;
  size?: number /*分页大小*/;
};

export type GetAttendanceListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    adjustmentEmployeeId?: string /*调整人(调整操作的员工ID)*/;
    adjustmentEmployeeName?: string /*调整人名称*/;
    adjustmentRemark?: string /*调整备注*/;
    adjustmentTime?: string /*调整时间*/;
    attendanceDate?: string /*日期*/;
    attendanceStatus?:
      | 'NORMAL'
      | 'INSUFFICIENT_TIME'
      | 'MISSING_CARD'
      | 'ABSENT' /*考勤标签[AttendanceStatusEnum]*/;
    businessTripDays?: string /*出差天数*/;
    clockInTime?: string /*上班打卡时间*/;
    clockOutTime?: string /*下班打卡时间*/;
    department?: string /*部门*/;
    employeeId?: string /*员工ID*/;
    employeeName?: string /*员工姓名*/;
    employeeNo?: string /*工号*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*修改时间*/;
    id?: string /*主键ID*/;
    leaveDays?: string /*请假天数*/;
    outDays?: string /*外出天数*/;
    workDuration?: number /*打卡时长(分钟)*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *员工考勤分页查询
 */
export const getAttendanceList = (params: GetAttendanceListRequest) => {
  return Fetch<ResponseWithResult<GetAttendanceListResult>>(
    '/user/public/employeeAttendance/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/employeeAttendance/page') },
    },
  );
};

export type BatchAdjustRequest = {
  adjustmentRemark?: string /*调整备注*/;
  attendanceStatus?:
    | 'NORMAL'
    | 'INSUFFICIENT_TIME'
    | 'MISSING_CARD'
    | 'ABSENT' /*考勤状态[AttendanceStatusEnum]*/;
  ids?: Array<string> /*考勤记录ID列表*/;
};

export type BatchAdjustResult = boolean;

/**
 *批量调整考勤状态
 */
export const batchAdjust = (params: BatchAdjustRequest) => {
  return Fetch<ResponseWithResult<BatchAdjustResult>>(
    '/user/public/employeeAttendance/batchAdjust',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/employeeAttendance/batchAdjust') },
    },
  );
};

export type PageExportRequest = {
  attendanceDateEnd?: string /*考勤日期结束*/;
  attendanceDateStart?: string /*考勤日期开始*/;
  attendanceStatus?:
    | 'NORMAL'
    | 'INSUFFICIENT_TIME'
    | 'MISSING_CARD'
    | 'ABSENT' /*考勤标签[AttendanceStatusEnum]*/;
  current?: number /*当前页码,从1开始*/;
  departmentId?: string /*部门（精确）*/;
  dynamicHead?: string /*动态表头*/;
  employeeName?: string /*员工姓名（模糊）*/;
  ids?: Array<string> /*选择ids*/;
  jobNo?: string /*工号（精确）*/;
  size?: number /*分页大小*/;
};

export type PageExportResult = {
  fileId?: string /*导出任务ID*/;
};

/**
 *员工考勤数据导出
 */
export const pageExport = (params: PageExportRequest) => {
  return Fetch<ResponseWithResult<PageExportResult>>('/user/public/employeeAttendance/pageExport', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/user/public/employeeAttendance/pageExport') },
  });
};
