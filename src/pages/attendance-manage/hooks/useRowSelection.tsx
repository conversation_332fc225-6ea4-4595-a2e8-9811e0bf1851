/*
 * @Author: 用户名
 * @Date: 2025-08-14 11:31:15
 * @LastEditTime: 2025-08-14 11:31:18
 * @Description: file content
 */
import React, { useState } from 'react';
import { TableRowSelection } from 'antd/es/table/index';

export type Item = any;

export const useRowSelection = () => {
  const [selectedRows, setSelectedRows] = useState<Item[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const handleSelectedChane = (selectedRowKeys: string[], sRows: Item[]) => {
    setSelectedRowKeys(selectedRowKeys);
    const allRows = [...selectedRows, ...sRows].filter((i) => selectedRowKeys.includes(i.id));
    const allMap = allRows?.reduce((acc: any, cur) => {
      if (acc[cur.id]) {
        return { ...acc };
      } else {
        acc[cur.id] = cur;
        return { ...acc };
      }
    }, {});
    setSelectedRows(Object.values(allMap || {}));
  };

  const rowSelection: TableRowSelection<Item> = {
    selectedRowKeys,
    onChange: handleSelectedChane as any,
    columnWidth: 20,
    // getCheckboxProps: (record) => ({
    //   disabled: record?.status === 'FINISH',
    // }),
  };

  const clearSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  return { rowSelection, selectedRows, selectedRowKeys, clearSelection };
};
