import React, { useEffect } from 'react';
import { Input, TreeSelect, DatePicker, Select } from 'antd';
import { useDepartmentList } from './';
import moment from 'moment';
import { ATTENDANCE_LABEL_LIST } from '../types';

const { RangePicker } = DatePicker;

export const useSearch = () => {
  const {
    getDepartmentListLoading,
    departmentTreeList,
    debounceSearchDepartmentList,
    getDepartmentListRun,
  } = useDepartmentList();

  useEffect(() => {
    getDepartmentListRun();
  }, []);

  const options = {
    jobNo: {
      label: '工号',
      renderNode: <Input placeholder="请输入" />,
    },
    employeeName: {
      label: '员工姓名',
      renderNode: <Input placeholder="请输入" />,
    },
    departmentId: {
      label: '部门',
      renderNode: (
        <TreeSelect
          loading={getDepartmentListLoading}
          dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          showSearch
          placeholder="请选择"
          allowClear
          treeDefaultExpandAll
          treeData={departmentTreeList}
          filterTreeNode={(inputValue: string, treeNode: any) => false}
          onSearch={(value) => {
            debounceSearchDepartmentList(value);
          }}
          defaultActiveFirstOption={false}
          onChange={(value: string) => {
            if (!value) {
              getDepartmentListRun();
              return;
            }
          }}
        />
      ),
    },
    attendanceStatus: {
      label: '考勤标签',
      renderNode: (
        <Select placeholder="请选择" allowClear>
          {ATTENDANCE_LABEL_LIST.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    attendanceDate: {
      label: '考勤日期',
      hocOptions: {
        // 默认昨天
        initialValue: [moment().subtract(1, 'day'), moment().subtract(1, 'day')],
      },
      renderNode: <RangePicker />,
    },
  };
  return { options };
};
