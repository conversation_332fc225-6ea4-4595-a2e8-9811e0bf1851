import React from 'react';
import { ColumnProps } from 'antd/lib/table';
import {
  ATTENDANCE_LABEL_COLOR,
  ATTENDANCE_LABEL_ENUM,
  ATTENDANCE_LABEL_NAME,
  DATE_TYPE_ENUM,
  DATE_TYPE_NAME,
} from '../types';
import PopoverRowText from '@/components/PopoverRowText';
import { Tag } from 'antd';
import moment from 'moment';

export const useTable = () => {
  const columns: ColumnProps<any>[] = [
    {
      title: '#',
      width: 40,
      render: (text, record, index) => index + 1,
      align: 'center',
    },
    {
      title: '工号',
      dataIndex: 'employeeNo',
      width: 120,
      render: (_) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: '姓名',
      dataIndex: 'employeeName',
      width: 100,
      render: (_) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 100,
      render: (_) => {
        return _ ? <PopoverRowText text={_ || '-'} /> : '-';
      },
    },
    {
      title: '日期',
      dataIndex: 'attendanceDate',
      width: 100,
      render: (_) => {
        return <span>{_ ? moment(_).format('YYYY-MM-DD') : '-'}</span>;
      },
    },
    {
      title: '日期类型',
      dataIndex: 'attendanceDateType',
      width: 100,
      render: (_: DATE_TYPE_ENUM) => {
        return <span>{DATE_TYPE_NAME[_] || '-'}</span>;
      },
    },
    {
      title: '上班打卡时间',
      dataIndex: 'clockInTime',
      width: 130,
      render: (_) => {
        return <span>{_ ? moment(_).format('HH:mm:ss') : '-'}</span>;
      },
    },
    {
      title: '下班打卡时间',
      dataIndex: 'clockOutTime',
      width: 130,
      render: (_) => {
        return <span>{_ ? moment(_).format('HH:mm:ss') : '-'}</span>;
      },
    },
    {
      title: '打卡时长',
      dataIndex: 'workDurationHour',
      width: 100,
      render: (_) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: '请假天数',
      dataIndex: 'leaveDays',
      width: 100,
      render: (_) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: '外出天数',
      dataIndex: 'outDays',
      width: 100,
      render: (_) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: '出差天数',
      dataIndex: 'businessTripDays',
      width: 100,
      render: (_) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: '考勤标签',
      dataIndex: 'attendanceStatus',
      width: 100,
      render: (_: ATTENDANCE_LABEL_ENUM) => {
        return _ ? (
          <Tag
            style={{
              background: ATTENDANCE_LABEL_COLOR[_].background,
              color: ATTENDANCE_LABEL_COLOR[_].color,
            }}
          >
            {ATTENDANCE_LABEL_NAME[_]}
          </Tag>
        ) : (
          '-'
        );
      },
    },
    {
      title: '调整备注',
      dataIndex: 'adjustmentRemark',
      width: 200,
      render: (_) => {
        return <PopoverRowText text={_ || '-'} />;
      },
    },
    {
      title: '调整人',
      dataIndex: 'adjustmentEmployeeName',
      width: 100,
      render: (_) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: '调整时间(最新)',
      dataIndex: 'adjustmentTime',
      width: 180,
      render: (_) => {
        return <span>{_ ? moment(_).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>;
      },
    },
  ];
  return { columns };
};
