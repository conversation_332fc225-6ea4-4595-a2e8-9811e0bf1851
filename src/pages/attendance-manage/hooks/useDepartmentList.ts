import { getDepartmentListApi } from '@/services/yml/setting';
import { handleResponse } from '@/utils/response';
import { useRequest, useDebounceFn } from 'ahooks';
import { listToTree } from '@/utils/treeData';
import { useState } from 'react';
import { message } from 'antd';
import { departmentTypeParams } from '@/services/setting/employee';
import { DepartmentItem } from '@/services/setting/department';

// 以下代码是从员工列表copy来的

export const useDepartmentList = () => {
  const [departmentTreeList, setDepartmentTreeList] = useState<any[]>([]);

  const { run: debounceSearchDepartmentList } = useDebounceFn(
    (val) => {
      if (val) {
        getDepartmentListRun({
          departmentName: val,
          elderDepartmentId: '800001844804663f3882c835d7ce200e',
        });
        return;
      }
      getDepartmentListRun();
    },
    {
      wait: 500,
    },
  );

  const { loading: getDepartmentListLoading, run: getDepartmentListRun1 } = useRequest(
    (params?: departmentTypeParams) =>
      getDepartmentListApi({
        size: 500,
        ...params,
        departmentName: params?.departmentName || '',
      }),
    {
      manual: true,
      onSuccess: (data, params) => {
        if (data?.success) {
          const { records } = data?.result?.departmentByPage || [];
          const treeData = listToTree(records || [], {
            getFirstLevelData: (departmentList: DepartmentItem[]) => {
              return departmentList.filter(
                (item) => item?.parentDepartmentId == '800001844804663f3882c835d7ce200e',
              );
              // if (params?.[0]?.departmentName) {
              //   return departmentList;
              // } else {
              //   return departmentList.filter((item) => item?.parentDepartmentId == '0');
              // }
            },
            getTreeData: (item: DepartmentItem) => ({
              ...item,
              title: item?.departmentName,
              key: item?.departmentId,
              value: item?.departmentId,
            }),
            key: 'departmentId',
            parentKey: 'parentDepartmentId',
          });

          setDepartmentTreeList(treeData || []);

          return;
        }
        message.error(res?.message || '系统异常');
      },
    },
  );

  const doGetDepartmentList = (params) => {
    return new Promise((resolve) => {
      getDepartmentListApi({ ...params }).then((response) => {
        handleResponse(response, { errorMes: '获取部门列表失败' })
          .then((res) => {
            const dataSource = res.result?.departmentByPage?.records || [];
            resolve([...dataSource]);
          })
          .catch((e) => resolve([]));
      });
    });
  };

  const getAllDepartmentList = (params) => {
    Promise.all([
      doGetDepartmentList({
        ...params,
        current: 1,
        size: 500,
        elderDepartmentId: '800001844804663f3882c835d7ce200e',
      }),
      doGetDepartmentList({
        ...params,
        current: 2,
        size: 500,
        elderDepartmentId: '800001844804663f3882c835d7ce200e',
      }),
    ]).then(([res1, res2]) => {
      const records = [...res1, ...res2];
      const treeData = listToTree(records, {
        getFirstLevelData: (departmentList: DepartmentItem[]) => {
          return departmentList.filter(
            (item) => item?.parentDepartmentId == '800001844804663f3882c835d7ce200e',
          );
          // if (params?.[0]?.departmentName) {
          //   return departmentList;
          // } else {
          //   return departmentList.filter((item) => item?.parentDepartmentId == '0');
          // }
        },
        getTreeData: (item: DepartmentItem) => ({
          ...item,
          title: item?.departmentName,
          key: item?.departmentId,
          value: item?.departmentId,
        }),
        key: 'departmentId',
        parentKey: 'parentDepartmentId',
      });

      setDepartmentTreeList(treeData || []);

      return;
    });
  };

  const getDepartmentListRun = (params?: any) => {
    if (params?.departmentName) {
      return getDepartmentListRun1(params);
    }
    return getAllDepartmentList(params);
  };

  return {
    getDepartmentListLoading,
    departmentTreeList,
    debounceSearchDepartmentList,
    getDepartmentListRun,
  };
};
