import React from 'react';
import { Modal, Form, message, Select, Input } from 'antd';
import WithToggleModal from '@/components/WithToggleModal';
import { useRequest } from 'ahooks';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import style from '../../../../src/styles/index.module.less';
import { ATTENDANCE_LABEL_LIST } from '../types';
import { batchAdjust } from '../services';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

const ChangeModal = (props: IProps) => {
  const { form, visible, onRefresh, ids, ...rest } = props;
  const { getFieldDecorator } = form;

  const { run: batchAdjustRun, loading: batchAdjustLoading } = useRequest(batchAdjust, {
    manual: true,
    onSuccess: ({ res }) => {
      if (!res?.success) {
        message.error(res?.message || '批量调整失败');
        return;
      }
      message.success('批量调整成功');
      onRefresh();
      rest?.onCancel?.();
    },
  });
  const handleOk = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      batchAdjustRun({
        ids,
        ...values,
      });
    });
  };

  return (
    <Modal
      title="批量调整"
      {...rest}
      visible={visible}
      confirmLoading={batchAdjustLoading}
      className={style['modal-sty']}
      maskClosable={false}
      onOk={handleOk}
    >
      <Form labelAlign="right">
        <Form.Item label="考勤标签" {...formItemLayout}>
          {getFieldDecorator('attendanceStatus', {
            rules: [{ required: true, message: '请选择考勤标签' }],
          })(
            <Select placeholder="请选择" allowClear>
              {ATTENDANCE_LABEL_LIST.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item label="调整原因" {...formItemLayout}>
          {getFieldDecorator('adjustmentRemark', {
            rules: [{ required: true, message: '请输入调整原因' }],
          })(<Input.TextArea maxLength={200} placeholder="请输入" rows={3} />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(WithToggleModal(ChangeModal));
