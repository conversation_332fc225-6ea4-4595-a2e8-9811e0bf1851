import React, { useCallback } from 'react';
import { FormComponentProps } from 'antd/es/form';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { Form } from 'antd';
import { useSearch } from '../hooks';
import moment from 'moment';

interface IProps extends FormComponentProps {
  onSearch: (value: any) => void;
  getTableHeight?: any;
  clearSelection?: () => void;
}

const SearchForm: React.FC<IProps> = ({ form, onSearch, getTableHeight, clearSelection }) => {
  const { options } = useSearch();
  const onSubmit = useCallback(
    (init?: boolean) => {
      form.validateFields((err, values) => {
        const { attendanceDate, ...params } = values;
        if (attendanceDate?.length && attendanceDate?.[0] && attendanceDate?.[1]) {
          const [attendanceDateStart, attendanceDateEnd] = attendanceDate;
          params.attendanceDateStart = moment(attendanceDateStart).format('YYYY-MM-DD');
          params.attendanceDateEnd = moment(attendanceDateEnd).format('YYYY-MM-DD');
        }
        onSearch(params);
      });
    },
    [onSearch, form],
  );

  const onReset = () => {
    form.resetFields();
    clearSelection?.();
    onSubmit();
  };

  return (
    <div>
      <SearchFormComponent
        form={form}
        options={options}
        loading={false}
        onSearch={onSubmit}
        onReset={onReset}
        needMore
        getTableHeight={getTableHeight}
        showRow={2}
      />
    </div>
  );
};

export default Form.create<IProps>()(SearchForm);
