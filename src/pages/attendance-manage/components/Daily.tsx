import React, { useEffect } from 'react';
import { AuthWrapper, history } from 'qmkit';
import style from '../../../styles/index.module.less';
import { SearchForm, ChangeModal } from './';
import { useTableHeight } from '@/common/constants/hooks/index';
import { Button, Table, message } from 'antd';
import { ExportModal } from 'web-common-modules/biz';
import download from '@/assets/download.png';
import { ButtonProxy } from 'web-common-modules/components';
import { useTable, useRowSelection, useList, APIKEY } from '../hooks';
import { GetAttendanceListRequest, GetAttendanceListResult, pageExport } from '../services';
import moment from 'moment';
import PaginationProxy from '@/common/constants/Pagination';

const Daily = ({ currentTab }: { currentTab: string }) => {
  const { tableHeight, getHeight } = useTableHeight(120);
  const { columns } = useTable();
  const { rowSelection, selectedRows, selectedRowKeys, clearSelection } = useRowSelection();

  const { dataSource, pagination, onPageChange, onSearch, onRefresh, loading, condition } = useList<
    GetAttendanceListRequest,
    GetAttendanceListResult
  >(APIKEY.EMPLOYEE_ATTENDANCE);

  useEffect(() => {
    clearSelection();
  }, [currentTab]);

  useEffect(() => {
    onSearch({
      attendanceDateStart: moment().subtract(1, 'day').format('YYYY-MM-DD'),
      attendanceDateEnd: moment().subtract(1, 'day').format('YYYY-MM-DD'),
    });
  }, []);

  return (
    <AuthWrapper functionName="f_attendance_manage_day_list" showType="page">
      <div
        style={{
          marginTop: '12px',
          height: '100%',
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <div className="formHeight" style={{ marginBottom: '12px' }}>
          <SearchForm
            onSearch={(value: any) => {
              onSearch(value);
              clearSelection();
            }}
            clearSelection={clearSelection}
            getTableHeight={getHeight}
          />
          <div>
            <AuthWrapper functionName="f_attendance_manage_day_change">
              <ChangeModal
                onRefresh={onRefresh}
                ids={selectedRowKeys}
                beforeClick={(cb: any) => {
                  if (!selectedRowKeys?.length) {
                    message.warning('请选择要调整的考勤记录');
                    return false;
                  }
                  cb();
                }}
              >
                <Button type="primary">批量调整</Button>
              </ChangeModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_attendance_manage_day_export">
              <ExportModal
                condition={condition}
                requestFunc={pageExport}
                content={
                  <div>
                    <p>
                      <span>导出完成后可在</span>
                      <a
                        href="javascript:;"
                        onClick={() => {
                          //
                        }}
                      >
                        导出记录
                      </a>
                      <span>中查看并下载。</span>
                    </p>
                  </div>
                }
                onOk={() => null}
              >
                <ButtonProxy className="ml-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  <img
                    src={download}
                    style={{
                      width: '14px',
                      height: '14px',
                      verticalAlign: 'sub',
                      marginRight: '6px',
                      marginTop: '-2px',
                    }}
                  />
                  <span>导出</span>
                </ButtonProxy>
              </ExportModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_attendance_manage_day_export_log">
              <ButtonProxy
                className="ml-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={() => {
                  history.push(
                    `/attendance-manage-data-board?configCode=EMPLOYEE_ATTENDANCE_EXPORT`,
                  );
                }}
              >
                <span>导出记录</span>
              </ButtonProxy>
            </AuthWrapper>
          </div>
        </div>
        <div style={{ flex: 1 }}>
          <Table
            columns={columns}
            pagination={false}
            dataSource={dataSource as any[]}
            rowKey={(record: { [key: string]: any }) => `${record.id}`}
            rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
            scroll={{ y: tableHeight, x: '100%' }}
            rowSelection={rowSelection}
            loading={loading}
          />
        </div>
        <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
          {/* @ts-ignore */}
          <PaginationProxy
            {...pagination}
            onChange={(current: number, size: number) => {
              onPageChange(current, size);
              clearSelection();
            }}
            valueType="flatten"
          />
        </div>
      </div>
    </AuthWrapper>
  );
};

export default Daily;
