import React, { useState } from 'react';
import PageLayout from '@/components/PageLayout';
import style from '@/styles/index.module.less';
import styles from './index.module.less';
// import { useTableHeight } from '@/common/constants/hooks/index';
import { checkAuth } from 'qmkit';
import { Tabs } from 'antd';
import { Daily } from './components';

const AttendanceManage: React.FC = () => {
  const [keys, setKeys] = useState<'day' | 'mouth'>(
    checkAuth('f_attendance_manage_day_list') ? 'day' : 'mouth',
  );

  const handleChange = (value: any) => {
    setKeys(value);
  };
  return (
    <PageLayout className={styles.attendanceManageContainer}>
      <div
        className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
        style={{ display: 'flex', flexDirection: 'column' }}
      >
        <Tabs onChange={handleChange} style={{ marginTop: '-10px' }}>
          {checkAuth('f_attendance_manage_day_list') && (
            <Tabs.TabPane key="day" tab="考勤日报"></Tabs.TabPane>
          )}
        </Tabs>
        {keys === 'day' && <Daily currentTab={keys} />}
      </div>
    </PageLayout>
  );
};

export default AttendanceManage;
