import React, { useState, useRef, useEffect } from 'react';
import { DrawerProps } from 'antd/es/drawer';
import { WithToggleModal } from 'web-common-modules/components';
import { WithToggleModalProps } from 'web-common-modules/components/WithToggleModal';
import { Button, Drawer, Spin, Form, Row, Col, Input, Select, Radio, message } from 'antd';
// 请根据实际路径调整样式文件引用
import styles from '../index.module.less';
import { AREA_LIST, TYPE_LIST, plantformListAll, STATUS_LIST, TYPE_ENUM } from '../types';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { useDeptList, useDetail } from '../hooks';
import { CategoryTree } from './index';
import { EditorConfig } from '@/components/OralBroadcast';
import { plantformList } from '../../../../web_modules/types';
import { promptConfigurationUpsert } from '../services';
import { useRequest } from 'ahooks';
import BraftEditor from 'braft-editor';

interface IProps extends WithToggleModalProps, DrawerProps {
  id?: any;
  onRefresh?: any;
  isDetail?: boolean;
  form: WrappedFormUtils;
}

const AddEdit = (props: IProps) => {
  const { visible, isDetail = false, id, onRefresh, form, ...rest } = props;

  const { getFieldDecorator } = form;
  const { deptList, loading: deptLoading } = useDeptList();

  const { type, platform } = form.getFieldsValue();

  const { detail, detailLoading, detailRun, setDetail } = useDetail({
    setValue: (value) => {
      // 回显
      const {
        categoryDetails,
        categoryGroup,
        deptId,
        deptName,
        effectiveArea,
        platform,
        status,
        systemPrompt,
        type,
      } = value;

      form.setFieldsValue({
        effectiveArea,
        type,
        status,
        systemPrompt: BraftEditor.createEditorState(systemPrompt || null),
      });
      if (type === TYPE_ENUM.PERSONALIZED) {
        setTimeout(() => {
          const entries = Object.entries(categoryDetails || {});
          const newCategoryDetails = entries?.map((item: any) => {
            const [key, label] = item;
            return { key, label };
          });
          form.setFieldsValue({
            dept: { key: deptId, label: deptName },
            platform,
            categoryGroup,
            categoryDetails: newCategoryDetails,
          });
        }, 10);
      }
    },
  });

  const { run: upsertRun, loading: upsertLoading } = useRequest(promptConfigurationUpsert, {
    manual: true,
    onSuccess: ({ res }) => {
      if (!res?.success) {
        message.error(res?.message || '网络异常');
        return;
      }
      message.success('操作成功');
      onRefresh?.();
      rest?.onCancel?.();
    },
  });

  const handleCancel = () => {
    rest?.onCancel?.();
    onRefresh?.();
  };

  const handleSubmit = () => {
    form.validateFields((err, values) => {
      if (err) return;
      // console.log(values);
      const { categoryDetails, systemPrompt, dept, ...rest } = values;
      console.log(dept);
      if (!systemPrompt || systemPrompt?.toHTML?.() === '<p></p>') {
        message.warning('请输入系统提示词');
        return;
      }

      const params = {
        ...rest,
        systemPrompt: systemPrompt?.toHTML?.(),
        categoryDetails: categoryDetails?.map((item: any) => item.key),
        deptId: dept?.key,
        deptName: dept?.label,
        ...(type === TYPE_ENUM.COMMON
          ? {
              deptId: null,
              deptName: null,
              platform: null,
              categoryGroup: null,
              categoryDetails: null,
            }
          : {}),
      };
      console.log(params);
      if (detail?.id) {
        upsertRun({ ...params, id: detail?.id });
      } else {
        upsertRun(params);
      }
    });
  };

  useEffect(() => {
    if (id && visible) {
      detailRun({ id });
    }
    if (!visible) {
      form.resetFields();
      setDetail({});
    }
  }, [id, visible]);

  return (
    <Drawer
      width={800}
      maskClosable={true}
      className={styles['legal-check-drawer']}
      visible={visible}
      {...rest}
      style={{ transform: 'translateX(0)' }}
      headerStyle={{
        position: 'sticky',
        top: '0px',
        left: '0px',
        background: '#ffffff',
        zIndex: 2,
      }}
      onClose={handleCancel}
      title={
        <div className={styles['drawer-title']}>
          <div className={styles['drawer-title-text']}>
            <p>提示词新增/编辑页面</p>
          </div>
          {/* 详情的时候没有按钮 */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button type="primary" onClick={handleSubmit} loading={upsertLoading}>
              提交
            </Button>
            <Button onClick={handleCancel} style={{ marginLeft: '8px' }}>
              取消
            </Button>
          </div>
        </div>
      }
    >
      <Spin spinning={detailLoading || upsertLoading}>
        <Form>
          <div style={{ padding: '0 16px' }}>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="生效区域"
                  required
                  labelCol={{ span: 5 }}
                  wrapperCol={{ span: 19 }}
                >
                  {getFieldDecorator('effectiveArea', {
                    rules: [{ required: true, message: '请选择生效区域' }],
                  })(
                    <Select allowClear placeholder="请选择生效区域">
                      {AREA_LIST.map((item) => (
                        <Select.Option key={item.value} value={item.value}>
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="类型" required labelCol={{ span: 5 }} wrapperCol={{ span: 19 }}>
                  {getFieldDecorator('type', {
                    rules: [{ required: true, message: '请选择类型' }],
                  })(
                    <Select allowClear placeholder="请选择类型">
                      {TYPE_LIST.map((item) => (
                        <Select.Option key={item.value} value={item.value}>
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>,
                  )}
                </Form.Item>
              </Col>
            </Row>
            {type === TYPE_ENUM.PERSONALIZED ? (
              <>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="事业部"
                      labelCol={{ span: 5 }}
                      required
                      wrapperCol={{ span: 19 }}
                    >
                      {getFieldDecorator('dept', {
                        rules: [{ required: true, message: '请选择事业部' }],
                      })(
                        <Select
                          allowClear
                          placeholder="请选择事业部"
                          loading={deptLoading}
                          labelInValue={true}
                        >
                          {deptList.map((item) => (
                            <Select.Option key={item.value} value={item.value}>
                              {item.label}
                            </Select.Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>

                  <Col span={12}>
                    <Form.Item
                      label="平台"
                      labelCol={{ span: 5 }}
                      wrapperCol={{ span: 19 }}
                      required
                    >
                      {getFieldDecorator('platform', {
                        rules: [{ required: true, message: '请选择平台' }],
                      })(
                        <Select
                          allowClear
                          placeholder="请选择平台"
                          onChange={(value: string) => {
                            form.setFieldsValue({
                              categoryDetails: [],
                            });
                          }}
                        >
                          {plantformList.map((item) => (
                            <Select.Option key={item.value} value={item.value}>
                              {item.label}
                            </Select.Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="类目组"
                      labelCol={{ span: 5 }}
                      wrapperCol={{ span: 19 }}
                      required
                    >
                      {getFieldDecorator('categoryGroup', {
                        rules: [{ required: true, message: '请输入类目组' }],
                      })(<Input placeholder="请输入类目组" maxLength={12} />)}
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="状态" labelCol={{ span: 5 }} wrapperCol={{ span: 19 }}>
                      {getFieldDecorator(
                        'status',
                        {},
                      )(
                        <Radio.Group>
                          {STATUS_LIST.map((item) => (
                            <Radio key={item.value} value={item.value}>
                              {item.label}
                            </Radio>
                          ))}
                        </Radio.Group>,
                      )}
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={24} style={{ marginLeft: '-18px' }}>
                    <Form.Item label="类目细分" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                      <div style={{ marginLeft: '0px' }}>
                        {getFieldDecorator(
                          'categoryDetails',
                          {},
                        )(<CategoryTree platform={platform} width={'calc(100% + 22px)'} />)}
                      </div>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            ) : (
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item label="状态" labelCol={{ span: 5 }} wrapperCol={{ span: 19 }}>
                    {getFieldDecorator(
                      'status',
                      {},
                    )(
                      <Radio.Group>
                        {STATUS_LIST.map((item) => (
                          <Radio key={item.value} value={item.value}>
                            {item.label}
                          </Radio>
                        ))}
                      </Radio.Group>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
            )}
            <Row gutter={24} style={{ marginTop: '6px' }}>
              <Col span={24} style={{ marginLeft: '-18px' }}>
                <Form.Item label="系统提示词" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                  <div
                    style={{
                      height: '70vh',
                      border: '1px solid #E5E6ED',
                      width: 'calc(100% + 22px)',
                    }}
                  >
                    <EditorConfig
                      form={form}
                      labelKey="systemPrompt"
                      initialValue={''}
                      controls={['headings']}
                    />
                  </div>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </Spin>
    </Drawer>
  );
};

export default Form.create<IProps>()(WithToggleModal(AddEdit));
// 2025-05-14zhouby -> cursor ai结尾共生成35行代码
