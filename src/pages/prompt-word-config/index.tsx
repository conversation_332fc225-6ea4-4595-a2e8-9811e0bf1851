import { AuthWrapper } from 'qmkit';
import React, { useEffect } from 'react';
import PageLayout from '@/components/PageLayout';
import style from '@/styles/index.module.less';
import styles from './index.module.less';
import { SearchForm, AddEdit } from './components';
import { useTableHeight } from '@/common/constants/hooks/index';
import { Button, Table } from 'antd';
import { useTable, APIKEY, useList } from './hooks';
import { PromptConfigurationPageRequest, PromptConfigurationPageResult } from './services/index';
import PaginationProxy from '@/common/constants/Pagination';

const PromptWordConfig = () => {
  const { getHeight, tableHeight } = useTableHeight(70);

  const { dataSource, pagination, onPageChange, onSearch, onRefresh, loading } = useList<
    PromptConfigurationPageRequest,
    PromptConfigurationPageResult['records']
  >(APIKEY.PROMPT_WORD_CONFIG);

  const { columns } = useTable({ onRefresh });

  useEffect(() => {
    onSearch({});
  }, []);

  return (
    <AuthWrapper functionName="f_prompt_word_config_list" showType="page">
      <PageLayout className={`${styles.publishFeeManageContainer} ${style['publish-fee-page']}`}>
        <div
          className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
          style={{ display: 'flex', flexDirection: 'column' }}
        >
          <div className="formHeight">
            <SearchForm onSearch={onSearch} getTableHeight={getHeight} />
            <AuthWrapper functionName="f_prompt_word_config_add">
              <div style={{ marginTop: '-6px' }}>
                <AddEdit onRefresh={onRefresh}>
                  <Button type="primary" icon="plus">
                    新建
                  </Button>
                </AddEdit>
              </div>
            </AuthWrapper>
          </div>
          <div style={{ flex: 1, marginTop: '8px' }}>
            <Table
              columns={columns}
              pagination={false}
              dataSource={dataSource || []}
              rowKey={(record: { [key: string]: any }) => `${record.id}`}
              rowClassName={(record, i) => (i % 2 === 1 ? style.even : style.odd)}
              scroll={{ y: tableHeight, x: '100%' }}
              loading={loading}
            ></Table>
          </div>
          <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
            {/* @ts-ignore */}
            <PaginationProxy
              {...pagination}
              onChange={({ current, size }: any) => {
                onPageChange(current, size);
              }}
              valueType="merge"
            />
          </div>
        </div>
      </PageLayout>
    </AuthWrapper>
  );
};

export default PromptWordConfig;
// 2025-05-15zhouby -> cursor ai结尾共生成8行代码
