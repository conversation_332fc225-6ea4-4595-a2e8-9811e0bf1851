import { plantformList, PlantformName } from '../../../web_modules/types';

export enum TYPE_ENUM {
  PERSONALIZED = 'PERSONALIZED',
  COMMON = 'COMMON',
}

export const TYPE_NAME = {
  [TYPE_ENUM.PERSONALIZED]: '个性化',
  [TYPE_ENUM.COMMON]: '通用',
};

export const TYPE_LIST = [
  {
    label: TYPE_NAME[TYPE_ENUM.PERSONALIZED],
    value: TYPE_ENUM.PERSONALIZED,
  },
  {
    label: TYPE_NAME[TYPE_ENUM.COMMON],
    value: TYPE_ENUM.COMMON,
  },
];

export const plantformListAll = [
  ...plantformList,
  {
    label: '其他',
    value: 'OTHER',
  },
];

export const PlantformNameAll = {
  ...PlantformName,
  OTHER: '其他',
};

export enum STATUS {
  DISABLE = 1,
  ENABLE = 0,
}

export const STATUS_NAME = {
  [STATUS.DISABLE]: '禁用',
  [STATUS.ENABLE]: '启用',
};

export const STATUS_LIST = [
  {
    label: STATUS_NAME[STATUS.DISABLE],
    value: STATUS.DISABLE,
  },
  {
    label: STATUS_NAME[STATUS.ENABLE],
    value: STATUS.ENABLE,
  },
] as const;

export const STATUS_COLOR = {
  [STATUS.DISABLE]: 'red',
  [STATUS.ENABLE]: 'green',
};

export enum AREA_ENUM {
  //AI口播稿，支持后续延展，可配置
  SPOKEN_SCRIPT = 'SPOKEN_SCRIPT',
  //AI利益点，支持后续延展，可配置
  INTEREST_POINTS = 'INTEREST_POINTS',
}

export const AREA_NAME = {
  [AREA_ENUM.SPOKEN_SCRIPT]: 'AI口播稿',
  [AREA_ENUM.INTEREST_POINTS]: 'AI利益点',
};

export const AREA_LIST = [
  {
    label: AREA_NAME[AREA_ENUM.SPOKEN_SCRIPT],
    value: AREA_ENUM.SPOKEN_SCRIPT,
  },
  {
    label: AREA_NAME[AREA_ENUM.INTEREST_POINTS],
    value: AREA_ENUM.INTEREST_POINTS,
  },
];
