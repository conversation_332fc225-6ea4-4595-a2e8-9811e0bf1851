import React from 'react';
import { AREA_LIST, TYPE_LIST, plantformListAll } from '../types';
import { Select } from 'antd';

export const useSearch = () => {
  const options = {
    platform: {
      label: '平台',
      renderNode: (
        <Select placeholder="请选择平台" allowClear>
          {plantformListAll.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    type: {
      label: '类型',
      renderNode: (
        <Select placeholder="请选择类型" allowClear>
          {TYPE_LIST.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    effectiveArea: {
      label: '生效区域',
      renderNode: (
        <Select placeholder="请选择生效区域" allowClear>
          {AREA_LIST.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
  };
  return { options };
};
