import { Form, Table, Button } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { ModalProps } from 'antd/lib/modal';
import React, { useState, useMemo } from 'react';
import { InputNumberProxy } from 'web-common-modules/components';
import styles from './index.module.less';

import { getTalentNameList } from '@/services/gql/creator-list/list';
import { LazySelect } from 'web-common-modules/components';
import { toThousands } from '@/utils/string';

interface IProps extends ModalProps, FormComponentProps {
  supplierId: string;
  data: any;
  disable: boolean;
}

const formItemLayout = {
  labelCol: {
    span: 0,
  },
  wrapperCol: {
    span: 24,
  },
};

const SectionRule: React.FC<IProps> = (props) => {
  const { visible, form, supplierId, data, disable, ...rest } = props;
  const { getFieldDecorator } = form;
  const [dataSource, setDataSource] = useState(data?.sectionRule || []);

  const loadTalentList = (params) => {
    return getTalentNameList({ ...params, name: params?.keyword }).then((res) => {
      if (res?.code === '200') {
        const result = {
          ...res?.result.talentPageByName,
          options: [],
        };
        result.options = result?.records?.map((item) => ({
          key: item.talentId,
          value: item.talentId,
          title: item.talentNickName,
        }));
        return result;
      }
    });
  };

  const sectionRule = form.getFieldValue('sectionRule');

  const filterValues = [];
  if (!!sectionRule) {
    for (const i in sectionRule) {
      if (sectionRule[i]?.talent?.key) {
        filterValues.push(sectionRule[i]?.talent?.key);
      }
    }
  }
  const sectionRuleData = useMemo(() => {
    if (!data?.sectionRule?.length) return null;
    const val = [...data?.sectionRule];
    val.forEach((_) => {
      const talent = _.talent;
      if (talent) {
        talent.value = talent.id;
        talent.label = talent.name;
      }
    });
    return val;
  }, [data?.sectionRule]);

  const sumMoney = () => {
    const sectionRule = disable ? sectionRuleData : form.getFieldValue('sectionRule');
    if (!sectionRule?.length) return 0;
    const sum = sectionRule?.reduce((total, cur) => {
      if (!cur.sectionNum || !cur.singleSectionFees) return total;
      return total + cur.sectionNum * cur.singleSectionFees;
    }, 0);
    return toThousands(sum);
  };

  const getColumns = () => {
    return [
      {
        title: '达人名称',
        width: 200,
        key: 'talent',
        dataIndex: 'talent',
        render: (text: any, record: T, index: number) => {
          return (
            <Form.Item {...formItemLayout}>
              {getFieldDecorator(`sectionRule[${index}].talent`, {
                initialValue: sectionRuleData?.[index]?.talent,
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              })(
                <LazySelect
                  size={100}
                  disabled={disable}
                  labelInValue
                  style={{ width: '200px' }}
                  loadOptions={loadTalentList}
                  filterValues={filterValues}
                  placeholder="请选择"
                />,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: '单个授权服务费',
        width: 200,
        key: 'singleSectionFees',
        dataIndex: 'singleSectionFees',
        render: (text: any, record: T, index: number) => {
          return (
            <Form.Item label="单场基础服务费" {...formItemLayout}>
              {getFieldDecorator(`sectionRule[${index}].singleSectionFees`, {
                initialValue: sectionRuleData?.[index]?.singleSectionFees,
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              })(
                <InputNumberProxy.Money
                  style={{ width: '100%' }}
                  placeholder="请输入"
                  precision={2}
                  max={9999999.99}
                  disabled={disable}
                  min={0}
                />,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: '切片数',
        width: 200,
        key: 'sectionNum',
        dataIndex: 'sectionNum',
        render: (text: any, record: T, index: number) => {
          return (
            <Form.Item label="场次数" {...formItemLayout}>
              {getFieldDecorator(`sectionRule[${index}].sectionNum`, {
                initialValue: sectionRuleData?.[index]?.sectionNum,
                rules: [
                  {
                    required: true,
                    message: '请选择',
                  },
                ],
              })(
                <InputNumberProxy
                  style={{ width: '100%' }}
                  placeholder="请输入"
                  disabled={disable}
                  precision={0}
                  max={9999}
                  min={0}
                />,
              )}
            </Form.Item>
          );
        },
      },
      {
        title: '授权服务费',
        width: 200,
        key: 'fee',
        dataIndex: 'fee',
        render: (text: any, record: T, index: number) => {
          return (
            <span>
              {!isNaN(
                form.getFieldValue(`sectionRule[${index}].sectionNum`) *
                  form.getFieldValue(`sectionRule[${index}].singleSectionFees`),
              )
                ? (
                    form.getFieldValue(`sectionRule[${index}].sectionNum`) *
                    form.getFieldValue(`sectionRule[${index}].singleSectionFees`)
                  ).toFixed(2)
                : '-'}
            </span>
          );
        },
      },
      {
        title: '操作',
        width: 200,
        key: 'operator',
        dataIndex: 'operator',
        render: (text: any, record: T, index: number) => {
          if (dataSource.length <= 1) return null;
          return (
            <Button
              type="link"
              disabled={disable}
              onClick={() => {
                setDataSource((prev) => {
                  let sectionRule = form.getFieldValue('sectionRule');
                  if (Array.isArray(sectionRule)) {
                    sectionRule = [...sectionRule];
                    sectionRule.splice(index, 1);
                  } else {
                    sectionRule = { ...sectionRule };
                    delete sectionRule[index.toString()];
                  }
                  form.setFieldsValue({
                    sectionRule,
                  });
                  prev.splice(index, 1);
                  return [...prev];
                });
              }}
            >
              删除
            </Button>
          );
        },
      },
    ];
  };

  return (
    <div className={styles.liveRoundRuleWrapper}>
      <div className={styles.liveRoundRuleHeader}>
        <Button
          className="mb-12"
          type="primary"
          icon="plus"
          disabled={disable}
          onClick={() => {
            setDataSource((prev) => [...prev, {}]);
          }}
        >
          添加达人
        </Button>
        <p>{`总计：¥${sumMoney() || '-'}`}</p>
      </div>

      <Table
        dataSource={dataSource}
        style={{ width: 1000 }}
        pagination={false}
        columns={getColumns()}
      />
    </div>
  );
};

export default SectionRule;
