import React, { useEffect } from 'react';
import style from '../../styles/index.module.less';
import PageLayout from '@/components/PageLayout';
import { SearchForm } from './components';
import styles from './index.module.less';
import { AuthWrapper, history } from 'qmkit';
import { Button, message, Table } from 'antd';
import { useTableHeight } from '@/common/constants/hooks/index';
import { useList, useTable } from './hooks';
import { cateHitRuleConfigDelete, CateHitRuleConfigPageRequest } from './services/yml';
import PaginationProxy from '@/common/constants/Pagination';
import Form from 'antd/lib/form';
import { FormComponentProps } from 'antd/es/form';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

const TrendHitRules: React.FC<FormComponentProps<CateHitRuleConfigPageRequest>> = ({ form }) => {
  const { tableHeight } = useTableHeight(60);
  const { list, pagination, loading, onRefresh, getList } = useList(form);

  const handleDelete = async (id?: string) => {
    const result = await responseWithResultAsync({
      request: cateHitRuleConfigDelete,
      params: { id },
    });
    if (result) {
      onRefresh();
      message.success('删除成功');
    }
  };

  const { columns } = useTable(handleDelete);
  const toAddPage = () => {
    history.push('/trend-hit-rules-add');
  };
  useEffect(() => {
    getList({});
  }, []);
  return (
    <PageLayout className={styles.publishFeeManageContainer}>
      <AuthWrapper functionName="f_trend_hit_rules_list">
        <div
          className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
          style={{ display: 'flex', flexDirection: 'column' }}
        >
          <div className="formHeight">
            <SearchForm onSearch={getList} form={form}></SearchForm>
            <div className={style.btnGroup} style={{ marginBottom: '16px' }}>
              <AuthWrapper functionName="f_trend_hit_rules_create">
                <Button type="primary" onClick={toAddPage}>
                  新建
                </Button>
              </AuthWrapper>
            </div>
          </div>

          <div style={{ flex: 1 }}>
            <Table
              columns={columns}
              pagination={false}
              dataSource={list}
              scroll={{ y: tableHeight, x: '100%' }}
              loading={loading}
            ></Table>
          </div>

          <div className={`${style['pagination-box']} pageHeight`} style={{ marginBottom: '-4px' }}>
            <PaginationProxy
              current={pagination?.current}
              pageSize={pagination?.size}
              total={pagination?.total}
              // @ts-ignore
              onChange={(current, size) => {
                getList({
                  current: current,
                  size: size,
                });
              }}
              valueType="flatten"
              pageSizeOptions={['5', '10', '20', '50', '100']}
            />
          </div>
        </div>
      </AuthWrapper>
    </PageLayout>
  );
};

export default Form.create()(TrendHitRules);
