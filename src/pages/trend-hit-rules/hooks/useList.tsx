import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { useEffect, useState } from 'react';
import {
  cateHitRuleConfigDelete,
  cateHitRuleConfigPage,
  CateHitRuleConfigPageRequest,
  CateHitRuleConfigPageResult,
} from '../services/yml';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { useSetState } from 'ahooks';
import { message } from 'antd';

export const useList = (form: WrappedFormUtils<CateHitRuleConfigPageRequest>) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<CateHitRuleConfigPageResult['records']>([]);
  const [pagination, setPagination] = useSetState({ current: 1, size: 20, total: 0 });
  const [condition, setCondition] = useState<CateHitRuleConfigPageRequest>();
  const getList = async (data: CateHitRuleConfigPageRequest) => {
    setLoading(true);
    const formValue = form?.getFieldsValue() as CateHitRuleConfigPageRequest;

    const params = {
      ...data,
      ...formValue,
      current: data?.current ?? pagination.current,
      size: data?.size ?? pagination.size,
    };

    setCondition(params);
    const result = await responseWithResultAsync({
      request: cateHitRuleConfigPage,
      params,
    });
    setLoading(false);
    setList(result?.records ?? []);
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };
  const onRefresh = () => {
    getList({});
  };
  const handleDelete = async (id?: string) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: cateHitRuleConfigDelete,
      params: { id },
    });
    setLoading(false);
    if (result) {
      message.success('删除成功');
      onRefresh();
    }
  };
  useEffect(() => {
    getList({});
  }, []);
  return {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
    onRefresh,
    handleDelete,
  };
};

export type CateHitRuleConfigPageInfoType = NonNullable<
  CateHitRuleConfigPageResult['records']
>[number];
