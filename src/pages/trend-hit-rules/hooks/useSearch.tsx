import React from 'react';
import { Input, Select } from 'antd';
import { STATUS_LIST, HIT_RULE_LIST } from '../types';
import { useCategory } from './useCategory';

export const useSearch = () => {
  const { categoryOptions, loading: categoryLoading } = useCategory();

  const options = {
    cateIdList: {
      label: '类目',
      renderNode: (
        <Select
          mode="multiple"
          allowClear
          placeholder="请选择类目"
          style={{ width: '100%' }}
          loading={categoryLoading}
          maxTagCount={1}
        >
          {categoryOptions?.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    hitRuleTypeList: {
      label: '命中规则',
      renderNode: (
        <Select
          mode="multiple"
          allowClear
          placeholder="请选择命中规则"
          style={{ width: '100%' }}
          maxTagCount={1}
        >
          {HIT_RULE_LIST?.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    status: {
      label: '状态',
      renderNode: (
        <Select allowClear placeholder="请选择状态">
          {STATUS_LIST?.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
  };
  return {
    options,
  };
};
