import React, { useEffect, useMemo, useState } from 'react';
import styles from '@/styles/index.module.less';
import { Tag, Popconfirm, Input } from 'antd';
import { HIT_RULE_TYPE_KEY, HIT_RULE_TYPE_OPTIONS, STATUS, STATUS_NAME } from '../types';
import moment from 'moment';
import { AuthWrapper } from 'qmkit';
import PopoverRowText from '@/components/PopoverRowText/index';
import { ColumnProps } from 'antd/lib/table';
import { CateHitRuleConfigPageInfoType } from './useList';

export const useTable = (handleDelete: (id?: string) => void) => {
  const columns = useMemo<ColumnProps<CateHitRuleConfigPageInfoType>[]>(
    () => [
      {
        title: '#',
        align: 'center',
        key: 'number',
        className: styles['table-number'],
        render: (_, __, index) => {
          return <span>{index + 1}</span>;
        },
        width: 50,
      },
      {
        title: '类目',
        key: 'cateName',
        dataIndex: 'cateName',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '命中规则',
        key: 'hitRuleName',
        dataIndex: 'hitRuleName',
        width: 300,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        width: 100,
        render: (status: STATUS) => {
          return status === STATUS.DISABLE ? (
            <Tag color="red">{STATUS_NAME[status] || '-'}</Tag>
          ) : (
            <Tag color="green">{STATUS_NAME[status] || '-'}</Tag>
          );
        },
      },
      {
        title: '创建人',
        key: 'creatorName',
        dataIndex: 'creatorName',
        width: 150,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '创建时间',
        key: 'gmtCreated',
        dataIndex: 'gmtCreated',
        width: 200,
        render: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '操作',
        key: 'other',
        dataIndex: 'other',
        width: 200,
        fixed: 'right',
        render: (_, record) => {
          return (
            <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              <AuthWrapper functionName="f_trend_hit_rules_del">
                <Popconfirm
                  title="是否确定删除？"
                  okText="确定"
                  cancelText="取消"
                  onConfirm={() => handleDelete(record?.id)}
                >
                  <a style={{ color: 'red' }}>删除</a>
                </Popconfirm>
              </AuthWrapper>
            </div>
          );
        },
      },
    ],
    [handleDelete],
  );

  return {
    columns,
  };
};
const inputStyle = {
  width: 50,
  margin: '0 5px',
};
const dayInputProps = {
  min: 1,
  // 移除max限制，使用JavaScript验证代替HTML5限制
};
const percentInputProps = {
  min: 0,
  max: 100,
};
const longInputProps = {
  min: 0,
  // 移除max限制，使用JavaScript验证代替HTML5限制
};
export const useCreateColumns = (ruleInfoList: string[]) => {
  const [dataSource, setDataSource] = useState<
    { hitRuleType: HIT_RULE_TYPE_KEY; hitRuleVariableList: string[] }[]
  >([]);
  useEffect(() => {
    setDataSource(
      ruleInfoList.map((item) => {
        return {
          hitRuleType: item as HIT_RULE_TYPE_KEY,
          hitRuleVariableList: [],
        };
      }),
    );
  }, [ruleInfoList]);
  // ai生成
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    hitRuleType: HIT_RULE_TYPE_KEY,
    index: number,
  ) => {
    const newdata = [...dataSource];
    const value = e.target.value;

    // 判断是否为百分比输入框
    const isPercentInput =
      (hitRuleType === 'SHORT_TERM_RAPID_GROWTH' && index === 1) ||
      (hitRuleType === 'MEDIUM_TERM_STABLE_GROWTH' && index === 1);

    // 判断是否为天数输入框
    const isDayInput =
      (hitRuleType === 'SHORT_TERM_RAPID_GROWTH' && index === 0) ||
      (hitRuleType === 'MEDIUM_TERM_STABLE_GROWTH' && (index === 0 || index === 2)) ||
      (hitRuleType === 'RANKING_RAPID_IMPROVEMENT' && (index === 0 || index === 2)) ||
      (hitRuleType === 'DARK_HORSE_EMERGENCE' && index === 0) ||
      // (hitRuleType === 'LIST_TOP_GROWTH' && index === 0) ||
      (hitRuleType === 'LIST_STABILITY' && index === 0);

    // 判断是否为位数输入框
    const isRankInput = !isPercentInput && !isDayInput;

    if (isPercentInput) {
      // 百分比输入框：允许输入数字和小数点，不立即格式化
      // 只允许数字、小数点和空字符串
      if (value === '' || /^\d*\.?\d*$/.test(value)) {
        const numValue = parseFloat(value);
        // 验证范围：0-100
        if (value === '' || (numValue >= 0 && numValue <= 100)) {
          // 保持原始输入，不立即格式化
          // 注意：这里不使用 toFixed，让用户可以继续输入
        } else {
          // 超出范围，不更新值
          return;
        }
      } else {
        // 不符合数字格式，不更新值
        return;
      }
    } else if (isDayInput) {
      // 天数输入框：只允许1-10的正整数
      if (value === '' || /^\d+$/.test(value)) {
        const numValue = parseInt(value);
        if (value === '' || (numValue >= 1 && numValue <= 10)) {
          // 验证天数大小关系
          const currentItem = newdata.find((item) => item.hitRuleType === hitRuleType);
          if (currentItem && value !== '') {
            const currentList = [...currentItem.hitRuleVariableList];
            currentList[index] = numValue.toString();

            let isValid = true;

            // MEDIUM_TERM_STABLE_GROWTH: 第二个天数不能小于第一个天数
            if (hitRuleType === 'MEDIUM_TERM_STABLE_GROWTH') {
              const firstDay = parseInt(currentList[0] || '0');
              const secondDay = parseInt(currentList[2] || '0');
              if (secondDay > 0 && firstDay > 0 && secondDay > firstDay) {
                isValid = false;
              }
            }

            // RANKING_RAPID_IMPROVEMENT: 第二个天数不能小于第一个天数
            if (hitRuleType === 'RANKING_RAPID_IMPROVEMENT') {
              const firstDay = parseInt(currentList[0] || '0');
              const secondDay = parseInt(currentList[2] || '0');
              if (secondDay > 0 && firstDay > 0 && secondDay > firstDay) {
                isValid = false;
              }
            }

            // LIST_STABILITY: 现在只有一个天数输入，无需验证天数大小关系

            if (!isValid) {
              // 如果验证失败，不更新值
              return;
            }
          }
          // 保持原始输入值或空字符串
        } else {
          // 超出1-10范围，不更新值
          return;
        }
      } else {
        // 不符合数字格式，不更新值
        return;
      }
    } else if (isRankInput) {
      // 位数输入框：只允许0-5000的整数
      if (value === '' || /^\d+$/.test(value)) {
        const numValue = parseInt(value);
        if (value === '' || (numValue >= 0 && numValue <= 5000)) {
          // 保持原始输入值或空字符串
        } else {
          // 超出0-5000范围，不更新值
          return;
        }
      } else {
        // 不符合数字格式，不更新值
        return;
      }
    }

    newdata.forEach((item) => {
      if (item.hitRuleType === hitRuleType) {
        item.hitRuleVariableList[index] = value;
      }
    });
    setDataSource(newdata);
  };

  // 处理百分比输入框失去焦点时的格式化
  const handleBlur = (
    e: React.FocusEvent<HTMLInputElement>,
    hitRuleType: HIT_RULE_TYPE_KEY,
    index: number,
  ) => {
    const isPercentInput =
      (hitRuleType === 'SHORT_TERM_RAPID_GROWTH' && index === 1) ||
      (hitRuleType === 'MEDIUM_TERM_STABLE_GROWTH' && index === 1);

    if (isPercentInput) {
      const value = e.target.value;
      if (value !== '' && !isNaN(parseFloat(value))) {
        const formattedValue = parseFloat(value).toFixed(2);
        const newdata = [...dataSource];
        newdata.forEach((item) => {
          if (item.hitRuleType === hitRuleType) {
            item.hitRuleVariableList[index] = formattedValue;
          }
        });
        setDataSource(newdata);
      }
    }
  };
  const hitRuleVariableMap = {
    SHORT_TERM_RAPID_GROWTH: (list: string[]) => (
      <section>
        <span>连续</span>
        <span>
          <Input
            style={inputStyle}
            value={list[0]}
            onChange={(e) => handleChange(e, 'SHORT_TERM_RAPID_GROWTH', 0)}
            {...dayInputProps}
          />
        </span>
        <span>天销量增长率大于</span>
        <span>
          <Input
            style={inputStyle}
            value={list[1]}
            onChange={(e) => handleChange(e, 'SHORT_TERM_RAPID_GROWTH', 1)}
            onBlur={(e) => handleBlur(e, 'SHORT_TERM_RAPID_GROWTH', 1)}
            {...percentInputProps}
          />
        </span>
        <span>%且排名持续上升</span>
      </section>
    ),
    MEDIUM_TERM_STABLE_GROWTH: (list: string[]) => (
      <section>
        <span>
          <Input
            style={inputStyle}
            value={list[0]}
            onChange={(e) => handleChange(e, 'MEDIUM_TERM_STABLE_GROWTH', 0)}
            {...dayInputProps}
          />
        </span>
        <span>天内销量增长大于</span>
        <span>
          <Input
            style={inputStyle}
            value={list[1]}
            onChange={(e) => handleChange(e, 'MEDIUM_TERM_STABLE_GROWTH', 1)}
            onBlur={(e) => handleBlur(e, 'MEDIUM_TERM_STABLE_GROWTH', 1)}
            {...percentInputProps}
          />
        </span>
        <span>%,且最近</span>
        <span>
          <Input
            style={inputStyle}
            value={list[2]}
            onChange={(e) => handleChange(e, 'MEDIUM_TERM_STABLE_GROWTH', 2)}
            {...dayInputProps}
          />
        </span>
        <span>天加速度为正</span>
      </section>
    ),
    RANKING_RAPID_IMPROVEMENT: (list: string[]) => (
      <section>
        <span>排名在</span>
        <span>
          <Input
            style={inputStyle}
            value={list[0]}
            onChange={(e) => handleChange(e, 'RANKING_RAPID_IMPROVEMENT', 0)}
            {...dayInputProps}
          />
        </span>
        <span>天内提升超过</span>
        <span>
          <Input
            style={inputStyle}
            value={list[1]}
            onChange={(e) => handleChange(e, 'RANKING_RAPID_IMPROVEMENT', 1)}
            {...longInputProps}
          />
        </span>
        <span>位且近</span>
        <span>
          <Input
            style={inputStyle}
            value={list[2]}
            onChange={(e) => handleChange(e, 'RANKING_RAPID_IMPROVEMENT', 2)}
            {...dayInputProps}
          />
        </span>
        <span>天仍在加速提升</span>
      </section>
    ),
    DARK_HORSE_EMERGENCE: (list: string[]) => (
      <section>
        <span>排名在</span>
        <span>
          <Input
            style={inputStyle}
            value={list[0]}
            onChange={(e) => handleChange(e, 'DARK_HORSE_EMERGENCE', 0)}
            {...dayInputProps}
          />
        </span>
        <span>天内,从</span>
        <span>
          <Input
            style={inputStyle}
            value={list[1]}
            onChange={(e) => handleChange(e, 'DARK_HORSE_EMERGENCE', 1)}
            {...longInputProps}
          />
        </span>
        <span>以外快速提升到排名以内,提升幅度超过</span>
        <span>
          <Input
            style={inputStyle}
            value={list[2]}
            onChange={(e) => handleChange(e, 'DARK_HORSE_EMERGENCE', 2)}
            {...longInputProps}
          />
        </span>
        <span>位,能识别可能被忽视的潜力商品。</span>
      </section>
    ),
    LIST_TOP_GROWTH: (list: string[]) => (
      <section>
        <span>销量TOP前</span>
        <span>
          <Input
            style={inputStyle}
            value={list[0]}
            onChange={(e) => handleChange(e, 'LIST_TOP_GROWTH', 0)}
            {...dayInputProps}
          />
        </span>
        <span>的榜单商品</span>
      </section>
    ),
    LIST_STABILITY: (list: string[]) => (
      <section>
        <span>连续</span>
        <span>
          <Input
            style={inputStyle}
            value={list[0]}
            onChange={(e) => handleChange(e, 'LIST_STABILITY', 0)}
            {...dayInputProps}
          />
        </span>
        <span>天及以上,保持在TOP</span>
        <span>
          <Input
            style={inputStyle}
            value={list[1]}
            onChange={(e) => handleChange(e, 'LIST_STABILITY', 1)}
            {...longInputProps}
          />
        </span>
        <span>榜单的商品</span>
      </section>
    ),
  };
  const columns = useMemo<
    ColumnProps<{ hitRuleType: HIT_RULE_TYPE_KEY; hitRuleVariableList: string[] }>[]
  >(
    () => [
      {
        title: '类型',
        key: 'hitRuleType',
        dataIndex: 'hitRuleType',
        render: (val) =>
          val ? <PopoverRowText text={HIT_RULE_TYPE_OPTIONS[val as HIT_RULE_TYPE_KEY]} /> : '-',
      },
      {
        title: '规则',
        key: 'hitRuleVariableList',
        dataIndex: 'hitRuleVariableList',
        render: (val, record) => {
          return hitRuleVariableMap[record.hitRuleType as HIT_RULE_TYPE_KEY](val);
        },
      },
    ],
    [dataSource, hitRuleVariableMap],
  );
  return { columns, dataSource };
};
// 2024年12月26日 开山ai结尾共生成48行代码
