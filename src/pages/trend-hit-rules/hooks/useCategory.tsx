// ai生成
import { useEffect, useState } from 'react';
import { getQueryByParentId } from '@/pages/explosive-product-trend-prediction/components/Category/yml';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

export interface CategoryOption {
  label: string;
  value: string;
}

export const useCategory = () => {
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchCategories = async () => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: getQueryByParentId,
      params: {
        parentId: '0', // 获取一级类目
        source: 'TREND_PREDICTION',
        status: 1, // 只获取启用的类目
      },
    });
    setLoading(false);
    setCategoryOptions(
      result?.map((item) => ({ label: item?.cateName as string, value: item?.cateId as string })) ||
        [],
    );
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categoryOptions,
    loading,
    refetch: fetchCategories,
  };
};
// 2024年12月19日 开山ai结尾共生成35行代码
