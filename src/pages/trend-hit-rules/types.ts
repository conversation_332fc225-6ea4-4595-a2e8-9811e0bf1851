import { CateHitRuleConfigPageResult } from './services/yml';

export enum STATUS {
  DISABLE = 0,
  ENABLE = 1,
}

export const STATUS_NAME = {
  [STATUS.DISABLE]: '已失效',
  [STATUS.ENABLE]: '已启用',
};

export const STATUS_LIST = [
  {
    label: STATUS_NAME[STATUS.DISABLE],
    value: STATUS.DISABLE,
  },
  {
    label: STATUS_NAME[STATUS.ENABLE],
    value: STATUS.ENABLE,
  },
] as const;

export type HIT_RULE_TYPE_KEY =
  | 'SHORT_TERM_RAPID_GROWTH'
  | 'MEDIUM_TERM_STABLE_GROWTH'
  | 'RANKING_RAPID_IMPROVEMENT'
  | 'DARK_HORSE_EMERGENCE'
  | 'LIST_TOP_GROWTH'
  | 'LIST_STABILITY';
// ai生成
export enum HIT_RULE_TYPE {
  SHORT_TERM_RAPID_GROWTH = '短期快速增长型',
  MEDIUM_TERM_STABLE_GROWTH = '中期稳定增长型',
  RANKING_RAPID_IMPROVEMENT = '排名快速提升型',
  DARK_HORSE_EMERGENCE = '黑马崛起型',
  LIST_TOP_GROWTH = '榜单top型',
  LIST_STABILITY = '榜单稳定性',
}

export const HIT_RULE_LIST = [
  {
    label: HIT_RULE_TYPE.SHORT_TERM_RAPID_GROWTH,
    value: 'SHORT_TERM_RAPID_GROWTH',
  },
  {
    label: HIT_RULE_TYPE.MEDIUM_TERM_STABLE_GROWTH,
    value: 'MEDIUM_TERM_STABLE_GROWTH',
  },
  {
    label: HIT_RULE_TYPE.RANKING_RAPID_IMPROVEMENT,
    value: 'RANKING_RAPID_IMPROVEMENT',
  },
  {
    label: HIT_RULE_TYPE.DARK_HORSE_EMERGENCE,
    value: 'DARK_HORSE_EMERGENCE',
  },
  {
    label: HIT_RULE_TYPE.LIST_TOP_GROWTH,
    value: 'LIST_TOP_GROWTH',
  },
  {
    label: HIT_RULE_TYPE.LIST_STABILITY,
    value: 'LIST_STABILITY',
  },
] as const;
// 2024年12月19日 开山ai结尾共生成25行代码

export type CateHitRuleConfigPageInfoType = Required<CateHitRuleConfigPageResult>['records'];

// 短期快速增长:识别正处于爆发初期的商品
// 中期稳定增长:识别有持续增长动能的商品
// 排名快速提升:识别从长尾突破到热销的商品
// 张开山0484
// 黑马崛起:识别可能被忽视的潜力商品
// 榜单TOP型:识别正在爆发的商品
// 榜单稳定型:识别正在爆发且销量稳定的商品

export const HIT_RULE_TYPE_OPTIONS = {
  SHORT_TERM_RAPID_GROWTH: '短期快速增长:识别正处于爆发初期的商品',
  MEDIUM_TERM_STABLE_GROWTH: '中期稳定增长:识别有持续增长动能的商品',
  RANKING_RAPID_IMPROVEMENT: '排名快速提升:识别从长尾突破到热销的商品',
  DARK_HORSE_EMERGENCE: '黑马崛起:识别可能被忽视的潜力商品',
  LIST_TOP_GROWTH: '榜单TOP型:识别正在爆发的商品',
  LIST_STABILITY: '榜单稳定型:识别正在爆发且销量稳定的商品',
};
