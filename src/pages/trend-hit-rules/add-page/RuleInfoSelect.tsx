import { Button, Modal, Table } from 'antd';
import { TableRowSelection } from 'antd/lib/table';
import React, { useEffect, useState } from 'react';
import { HIT_RULE_TYPE_OPTIONS, HIT_RULE_TYPE_KEY } from '../types';

const columns = [
  {
    title: '类型选项',
    dataIndex: 'key',
    render: (val: HIT_RULE_TYPE_KEY) => HIT_RULE_TYPE_OPTIONS[val],
  },
];
const dataSource = Object.keys(HIT_RULE_TYPE_OPTIONS).map((key) => ({
  key,
  label: HIT_RULE_TYPE_OPTIONS[key as HIT_RULE_TYPE_KEY],
}));
const RuleInfoSelect: React.FC<{
  value?: string[];
  onChange?: (value?: string[]) => void;
}> = ({ value, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const rowSelection: TableRowSelection<typeof dataSource[number]> = {
    selectedRowKeys,
    onChange: (selectedRowKeys: string[] | number[]) => {
      setSelectedRowKeys(selectedRowKeys as string[]);
    },
  };
  const handleOk = () => {
    onChange?.(selectedRowKeys);
    setVisible(false);
  };
  useEffect(() => {
    visible && setSelectedRowKeys(value || []);
  }, [visible]);
  return (
    <>
      <Button type="primary" icon="plus" onClick={() => setVisible(true)}>
        添加
      </Button>
      <Modal title="添加类型" visible={visible} onCancel={() => setVisible(false)} onOk={handleOk}>
        <Table
          dataSource={dataSource}
          columns={columns}
          rowSelection={rowSelection}
          pagination={false}
        />
      </Modal>
    </>
  );
};

export default RuleInfoSelect;
