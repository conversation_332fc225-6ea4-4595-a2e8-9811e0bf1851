import React, { useMemo, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import { Spin, Button, Form, Row, Col, Select, message, Table } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import { useCategory, useCreateColumns } from '../hooks';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import RuleInfoSelect from './RuleInfoSelect';
import { cateHitRuleConfigCteate, CateHitRuleConfigCteateRequest } from '../services/yml';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';

const AddPage = ({ form }: FormComponentProps) => {
  const { getFieldDecorator } = form;

  const { closeAndJumpToPage } = useCloseAndJump();
  const [loading, setLoading] = useState(false);
  const { categoryOptions, loading: categoryLoading } = useCategory();
  const ruleInfoList = useMemo(
    () => form.getFieldsValue()?.ruleInfoList ?? [],
    [form?.getFieldsValue()?.ruleInfoList],
  );
  const { columns, dataSource } = useCreateColumns(ruleInfoList);

  const handleOk = async (params: CateHitRuleConfigCteateRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: cateHitRuleConfigCteate,
      params,
    });
    setLoading(false);
    if (result) {
      message.success('保存成功');
      closeAndJumpToPage('/trend-hit-rules');
    } else {
      message.error('保存失败');
    }
  };

  const handleSubmit = () => {
    form.validateFields((err, value) => {
      if (err) return;
      console.log('🚀 ~ form.validateFields ~ value:', value);
      if (!value?.cateIdList?.length) {
        message.warning('请选择类目');
        return;
      }
      if (!value?.ruleInfoList?.length) {
        message.warning('请选择命中规则');
        return;
      }

      // ai生成
      // 验证dataSource中所有input不能为空
      const emptyInputs = [];
      for (const item of dataSource) {
        const { hitRuleType, hitRuleVariableList } = item;

        // 根据不同规则类型验证不同数量的input
        let requiredInputCount = 0;
        switch (hitRuleType) {
          case 'SHORT_TERM_RAPID_GROWTH':
            requiredInputCount = 2;
            break;
          case 'MEDIUM_TERM_STABLE_GROWTH':
            requiredInputCount = 3;
            break;
          case 'RANKING_RAPID_IMPROVEMENT':
            requiredInputCount = 3;
            break;
          case 'DARK_HORSE_EMERGENCE':
            requiredInputCount = 3;
            break;
          case 'LIST_TOP_GROWTH':
            requiredInputCount = 1;
            break;
          case 'LIST_STABILITY':
            requiredInputCount = 2;
            break;
          default:
            break;
        }

        // 检查该规则类型的所有input是否都有值
        for (let i = 0; i < requiredInputCount; i++) {
          if (!hitRuleVariableList[i] || hitRuleVariableList[i].trim() === '') {
            emptyInputs.push(`${hitRuleType}_${i}`);
          }
        }
      }

      if (emptyInputs.length > 0) {
        message.warning('规则明细中的输入框不能为空，请完善所有规则参数');
        return;
      }
      // 2025年1月2日 开山ai结尾共生成33行代码
      handleOk({
        cateInfoList: value?.cateIdList?.map((item: { key: string; label: string }) => ({
          cateId: item.key,
          cateName: item.label,
        })),
        ruleInfoList: dataSource,
      });
    });
  };

  const handleCancel = () => {
    closeAndJumpToPage('/trend-hit-rules');
  };

  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={false}>
          <Form>
            <Card title="趋势命中规则配置">
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item label="类目" required labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {getFieldDecorator('cateIdList', {
                      rules: [
                        {
                          required: true,
                          message: '请选择类目',
                        },
                      ],
                    })(
                      <Select
                        mode="multiple"
                        allowClear
                        placeholder="请选择类目"
                        style={{ width: '100%' }}
                        loading={categoryLoading}
                        maxTagCount={1}
                        labelInValue
                      >
                        {categoryOptions?.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="命中规则"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('ruleInfoList', {
                      rules: [
                        {
                          required: true,
                          message: '请选择命中规则',
                        },
                      ],
                    })(<RuleInfoSelect />)}
                  </Form.Item>
                </Col>
              </Row>
            </Card>
            <Card title="规则明细">
              <div style={{ padding: '0 16px' }}>
                <Table
                  pagination={false}
                  columns={columns}
                  dataSource={dataSource}
                  rowKey={'hitRuleType'}
                ></Table>
              </div>
            </Card>
          </Form>
        </Spin>
        <FormBottomCard>
          <Button type="primary" onClick={handleSubmit}>
            保存
          </Button>
          <Button style={{ marginRight: '6px' }} onClick={handleCancel}>
            取消
          </Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddPage);
