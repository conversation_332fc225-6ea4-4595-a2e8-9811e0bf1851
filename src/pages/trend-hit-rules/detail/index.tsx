/*
 * @Author: 用户名
 * @Date: 2024-05-09 10:30:29
 * @LastEditTime: 2024-05-09 14:27:03
 * @Description: file content
 */
import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import { Spin, Button, Form, Row, Col, Input, Select, Table, message } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import { STATUS_LIST, STATUS_NAME } from '../types';
import { useDetailTable } from '../hooks';
import { history } from 'qmkit';
import { getQueryParams } from 'web-common-modules/utils/params';
import {
  systemCodeTypeCreate,
  systemCodeTypeDetail,
  systemCodeTypeUpdate,
  SystemCodeTypeDetailResult,
} from '../services/yml/index';
import { useRequest } from 'ahooks';
import moment from 'moment';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import {
  DetailContentLayout,
  DetailTitle,
  SpinCard,
  Card,
  Title,
} from '@/components/DetailFormCompoments';
import {
  DetailContextBox,
  DetailContentItem,
} from '@/components/DetailFormCompoments/DetailContentItem';
const Item = DetailContentItem;

const AddPage = ({ form }: FormComponentProps) => {
  const codeType = getQueryParams()?.codeType;
  const { getFieldDecorator } = form;
  const [detail, setDetail] = useState<SystemCodeTypeDetailResult>();
  const { closeAndJumpToPage } = useCloseAndJump();
  const validateInput = (rule: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    // 使用正则表达式来检查是否只包含字母、数字及下划线
    if (!/^[a-zA-Z0-9_]*$/.test(value)) {
      return Promise.reject(new Error('请输入字母、数字或下划线'));
    }
    return Promise.resolve();
  };
  const { columns, dataSource, setDataSource } = useDetailTable(
    getFieldDecorator,
    {},
    codeType as any as boolean,
    validateInput,
  );

  // 详情
  const { run: detailRun, loading: detailLoading } = useRequest(systemCodeTypeDetail, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        setDetail(res?.result);
        // 回显
        const { codeType, typeName, status, systemCodeValueModels } = res?.result || {};
        const formSystemCodeValue = systemCodeValueModels?.map((item) => ({
          codeValue: item?.codeValue,
          status: item?.status,
          valueName: item?.valueName,
          sort: item?.sort,
          id: `${Math.random()}${moment().valueOf()}`,
          disabled: true,
        }));
        setDataSource(formSystemCodeValue as any);
        form.setFieldsValue({
          codeType,
          typeName,
          status,
        });
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  useEffect(() => {
    if (codeType) {
      detailRun({ codeType });
    }
  }, []);

  // const handleCancel = () => {
  //   history.replace('/system-code');
  // };

  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText={detail?.codeType}>
          <Button
            type="primary"
            onClick={() => {
              closeAndJumpToPage(`system-code-add?codeType=${detail?.codeType}`);
            }}
          >
            编辑
          </Button>
        </DetailTitle>
        <SpinCard spinning={detailLoading}>
          <Card>
            <Title>代码类型</Title>
            <DetailContextBox>
              <Item label="代码code">
                <p>{detail?.codeType ?? '-'}</p>
              </Item>
              <Item label="代码名称">
                <p>{detail?.typeName ?? '-'}</p>
              </Item>
              <Item label="状态">
                <p>{detail?.status ? STATUS_NAME[detail?.status] : '-'}</p>
              </Item>
            </DetailContextBox>
          </Card>
          <Card>
            <Title>代码值</Title>
            <Table
              pagination={false}
              columns={columns}
              dataSource={dataSource}
              rowKey={'id'}
            ></Table>
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddPage);
