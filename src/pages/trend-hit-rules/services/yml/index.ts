import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type CateHitRuleConfigPageRequest = {
  cateIdList?: Array<string> /*类目ID*/;
  current?: number /*当前页码,从1开始*/;
  hitRuleTypeList?: Array<
    | 'SHORT_TERM_RAPID_GROWTH'
    | 'MEDIUM_TERM_STABLE_GROWTH'
    | 'RANKING_RAPID_IMPROVEMENT'
    | 'DARK_HORSE_EMERGENCE'
    | 'LIST_TOP_GROWTH'
    | 'LIST_STABILITY'
  > /*命中规则类型[RuleType]*/;
  size?: number /*分页大小*/;
  status?: number /*状态（0：禁用；1：启用）*/;
};

export type CateHitRuleConfigPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    cateId?: string /*类目ID*/;
    cateName?: string /*类目名称*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*修改时间*/;
    hitRuleName?: string /*命中规则名称-已拼装*/;
    hitRuleType?: string /*命中规则枚举*/;
    hitRuleVariableList?: string /*命中规则配置的变量集合*/;
    id?: string /*id*/;
    modifier?: string /*修改人*/;
    status?: number /*状态（0：禁用；1：启用）*/;
    creatorName?: string /*创建人名称*/;
    modifierName?: string /*修改人名称*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询类目命中规则
 */
export const cateHitRuleConfigPage = (params: CateHitRuleConfigPageRequest) => {
  return Fetch<ResponseWithResult<CateHitRuleConfigPageResult>>(
    '/tools/public/cateHitRuleConfig/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/cateHitRuleConfig/page') },
    },
  );
};

export type CateHitRuleConfigDeleteRequest = {
  id?: string /*配置ID*/;
};

export type CateHitRuleConfigDeleteResult = boolean;

/**
 *删除类目命中规则
 */
export const cateHitRuleConfigDelete = (params: CateHitRuleConfigDeleteRequest) => {
  return Fetch<ResponseWithResult<CateHitRuleConfigDeleteResult>>(
    '/tools/public/cateHitRuleConfig/delete',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/cateHitRuleConfig/delete') },
    },
  );
};

export type CateHitRuleConfigCteateRequest = {
  cateInfoList?: Array<{
    cateId?: string /*类目ID*/;
    cateName?: string /*类目名称*/;
  }> /*类目信息列表*/;
  ruleInfoList?: Array<{
    hitRuleType?:
      | 'SHORT_TERM_RAPID_GROWTH'
      | 'MEDIUM_TERM_STABLE_GROWTH'
      | 'RANKING_RAPID_IMPROVEMENT'
      | 'DARK_HORSE_EMERGENCE'
      | 'LIST_TOP_GROWTH'
      | 'LIST_STABILITY' /*命中规则类型[RuleType]*/;
    hitRuleVariableList?: Array<string> /*命中规则变量列表*/;
  }> /*规则信息列表*/;
};

export type CateHitRuleConfigCteateResult = boolean;

/**
 *新增类目命中规则
 */
export const cateHitRuleConfigCteate = (params: CateHitRuleConfigCteateRequest) => {
  return Fetch<ResponseWithResult<CateHitRuleConfigCteateResult>>(
    '/tools/public/cateHitRuleConfig/create',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/tools/public/cateHitRuleConfig/create') },
    },
  );
};
