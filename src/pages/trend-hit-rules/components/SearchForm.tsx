import React, { useCallback } from 'react';
import { FormComponentProps } from 'antd/es/form';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import { Form } from 'antd';
import { useSearch } from '../hooks';

interface IProps extends FormComponentProps {
  onSearch: (value: any) => void;
  getTableHeight?: any;
}

const SearchForm: React.FC<IProps> = ({ form, onSearch, getTableHeight }) => {
  const { options } = useSearch();
  const onSubmit = useCallback(
    (init?: boolean) => {
      form.validateFields((err, values) => {
        // console.log(values, '------>');
        // onSearch(values);
        if (!err) {
          onSearch(values);
        }
      });
    },
    [onSearch, form],
  );

  const onReset = () => {
    form.resetFields();
    onSubmit();
  };
  return (
    <div>
      <SearchFormComponent
        form={form}
        options={options}
        loading={false}
        onSearch={onSubmit}
        onReset={onReset}
        needMore
        getTableHeight={getTableHeight}
      />
    </div>
  );
};

export default SearchForm;
