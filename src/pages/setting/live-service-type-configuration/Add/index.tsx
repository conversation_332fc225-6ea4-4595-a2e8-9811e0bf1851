import { useRequest } from 'ahooks';
import {
  Button,
  Form,
  Input,
  message,
  Row,
  Col,
  Radio,
  Select,
  Table,
  Popconfirm,
  Modal,
  Spin,
  InputNumber,
} from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { history } from 'qmkit';
import Department from '../Department';
import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import AddServiceTypeModal from '../addServiceTypeModal';
import {
  createLiveServiceType,
  CreateLiveServiceTypeRequest,
  CreateServiceTypeOptionResult,
  editServiceType,
} from '@/services/yml/live-service-type-configuration';
import {
  iasmListAllLiveRoom,
  IasmListAllLiveRoomResult,
} from '@/services/yml/live-room-operation/pallet';
import styles from '../index.module.less';
import { getDetail } from '@/services/yml/live-service-type-configuration';

import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
interface IProps extends FormComponentProps {
  location: {
    state: { roleId: string };
  };
}
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};

const AddLiveType: React.FC<IProps> = ({ form, location }) => {
  const [numberSessionsList, setNumberSessionsList] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState([]);
  const [path, setPath] = useState('add');
  // 直播间列表
  const [liveList, setLiveList] = useState<IasmListAllLiveRoomResult>([]);
  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: '直播服务类型选项',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text, record) => {
        if (text === 'DISABLE') {
          return '停用';
        } else if (text === 'ENABLE') {
          return '启用';
        }
      },
    },
    {
      title: '排序',
      dataIndex: 'sort',
      render: (text, record) => (
        <InputNumber
          min={0}
          max={999}
          style={{ width: '200px' }}
          value={text}
          placeholder="请输入排序"
          onChange={(value) => handleSortChange(record.id, value)}
          precision={0}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) =>
        dataSource.length >= 1 ? (
          <Popconfirm title="确认删除吗?" onConfirm={() => handleDelete(record.id)}>
            <a style={{ color: '#FF4D4F' }}>删除</a>
          </Popconfirm>
        ) : null,
    },
  ];
  // 获取所有直播间信息(包含: 合作达人)
  const { run: listAllLiveRoomRun, loading: listAllLiveRoomLoading } = useRequest(
    iasmListAllLiveRoom,
    {
      manual: true,
      onSuccess: ({ res }) => {
        const { result } = res;
        setLiveList(result);
      },
    },
  );

  useEffect(() => {
    const path = location.pathname.split('/')[2];
    setPath(path);
    if (path === 'edit') {
      detail(location?.state?.recordid);
    }
  }, []);
  const detail = (id) => {
    getDetail({ id: id }).then(({ res }) => {
      const msg = res?.result;
      listAllLiveRoomRun({ deptId: msg.deptId });
      if (res?.code === '200') {
        form?.setFieldsValue({
          name: msg.name,
          deptId: msg.deptId,
          liveRoomIds: msg.liveRoomIds,
          required: msg.required,
          notes: msg.notes,
        });
        setDataSource(res?.result?.serviceTypeOptionConfigs);
      } else {
        message.error(res?.message);
      }
    });
  };
  const handleDelete = (id) => {
    const newData = dataSource.filter((item) => item.id !== id);
    setDataSource(newData);
  };
  const handleSortChange = (id, value) => {
    const newData = dataSource.map((item) => {
      if (item.id === id) {
        return { ...item, sort: value };
      }
      return item;
    });
    setDataSource(newData);
  };
  const handleOk = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }
      if (dataSource?.length === 0) {
        message.error('请至少添加一个选项');
        return;
      }
      const serviceTypeOptionSortRequests: CreateLiveServiceTypeRequest['serviceTypeOptionSortRequests'] =
        [];
      dataSource?.map((item: { id: string; sort: string }) => {
        serviceTypeOptionSortRequests.push({
          serviceTypeOptionId: item.id,
          sort: item.sort || 0,
        });
      });
      const params: CreateLiveServiceTypeRequest = {
        name: value.name,
        deptId: value?.deptId,
        notes: value.notes,
        required: value.required,
        liveRoomIds: value.liveRoomIds,
        serviceTypeOptionSortRequests,
      };
      const paramsEdit = {
        id: location?.state?.recordid,
        name: value.name,
        deptId: value?.deptId,
        notes: value.notes,
        required: value.required,
        liveRoomIds: value.liveRoomIds,
        serviceTypeOptionSortRequests,
      };
      path === 'add'
        ? createLiveServiceType(params).then((res) => {
            if (res?.res?.code === '200') {
              message.success('保存成功');
              setTimeout(() => {
                history.goBack();
              }, 1000);
              return;
            } else {
              Modal.warning({
                title: res?.res?.message,
              });
            }
          })
        : path === 'edit'
        ? editServiceType(paramsEdit).then((res) => {
            if (res?.res?.code === '200') {
              message.success('修改成功');
              setTimeout(() => {
                history.goBack();
              }, 1000);
              return;
            } else {
              Modal.warning({
                title: res?.res?.message,
              });
            }
          })
        : null;
    });
  };
  return (
    // <section className={styles.container}>
    <PageLayout>
      <FormContentLayout>
        <Card title="基本信息">
          <div className={styles.extra}>
            <Form {...formItemLayout}>
              <Row>
                <Col span={6}>
                  <Form.Item label="配置名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {form.getFieldDecorator('name', {
                      rules: [
                        {
                          required: true,
                          message: '请输入',
                        },
                        {
                          max: 50,
                          message: '最多输入50个字符',
                        },
                      ],
                    })(<Input style={{ width: '200px' }} placeholder="请输入" />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="应用事业部" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {form.getFieldDecorator('deptId', {
                      rules: [
                        {
                          required: true,
                          message: '请选择',
                        },
                      ],
                    })(
                      <Department
                        style={{ width: '200px' }}
                        disabled={false}
                        handelProjectTeam={(e: any) => {
                          setNumberSessionsList([]);
                          form.setFieldsValue({
                            liveRoomIds: [],
                          });
                          // 如果是场次货盘需要获取直播场次
                          listAllLiveRoomRun({ deptId: e });
                        }}
                      ></Department>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="直播间" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {form.getFieldDecorator('liveRoomIds', {
                      rules: [
                        {
                          required: true,
                          message: '请选择',
                        },
                      ],
                    })(
                      <Select
                        maxTagCount={1}
                        loading={listAllLiveRoomLoading}
                        allowClear
                        style={{ width: '200px' }}
                        mode="multiple"
                        placeholder="请选择"
                      >
                        {liveList.map((item) => (
                          <Select.Option key={String(item.id)} value={String(item.id)}>
                            {item.name}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="是否必填" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {form.getFieldDecorator('required', {
                      rules: [
                        {
                          required: true,
                          message: '请选择',
                        },
                      ],
                    })(
                      <Radio.Group>
                        <Radio value={1}>是</Radio>
                        <Radio value={0}>否</Radio>
                      </Radio.Group>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={6}>
                  <Form.Item label="说明" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {form.getFieldDecorator('notes', {
                      rules: [
                        {
                          max: 50,
                          message: '最多输入50个字符',
                        },
                      ],
                    })(<Input style={{ width: '200px' }} placeholder="请输入说明，最多50字" />)}
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </div>
        </Card>
        <Card title="服务类型选项配置">
          <div style={{ paddingLeft: '16px' }}>
            <section className={styles.configContent}>
              <AddServiceTypeModal getChildrenMsg={setDataSource} selectedData={dataSource} />
              <Table pagination={false} columns={columns} dataSource={dataSource} rowKey="id" />
            </section>
          </div>
        </Card>
        <FormBottomCard>
          <Button onClick={() => history.goBack()}>取消</Button>
          <Button type="primary" onClick={handleOk} className="ml-16">
            保存
          </Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
    // </section>
  );
};

export default Form.create()(AddLiveType);
