import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, message } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import moment from 'moment';
import { addSuppLink } from '@/services/yml/choiceList/index';
import CreateServiceTypeModal from './createServiceTypeModal';
import { queryServiceTypeOptionConfig } from '../../../services/yml/live-service-type-configuration';
import styles from './index.module.less';
interface IProps extends ModalProps {
  getChildrenMsg?: any;
  selectedData?: any;
}

const AddServiceTypeModal: React.FC<IProps> = ({ getChildrenMsg, selectedData }) => {
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    size: 20,
    total: 0,
  });
  const columns = [
    {
      title: '#',
      dataIndex: 'index',
      render: (text, record, index) => `${index + 1}`,
    },
    {
      title: '直播服务类型选项',
      dataIndex: 'name',
      width: 300,
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text, record) => {
        if (text === 'DISABLE') {
          return '停用';
        } else if (text === 'ENABLE') {
          return '启用';
        }
      },
    },
  ];
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRows(selectedRows);
      setSelectedRowKeys(selectedRowKeys);
    },
    getCheckboxProps: (record) => ({
      disabled: record.status === 'DISABLE',
      name: record.name,
    }),
  };
  const handleOk = () => {
    const arr = selectedRows ? [...selectedRows] : [];
    arr.forEach((item) => {
      selectedData.forEach((element) => {
        if (element.id === item.id) {
          item.sort = element.sort;
        }
      });
    });

    getChildrenMsg(arr);
    setVisible(false);
  };
  useEffect(() => {
    getTableList({
      ...pagination,
    });
  }, []);
  useEffect(() => {
    if (visible && data.length) {
    }
  }, [visible]);
  //获取列表数据
  const getTableList = (pagination) => {
    queryServiceTypeOptionConfig({ size: 1000 }).then((res) => {
      if (res?.res?.code === '200') {
        setData(res?.res?.result?.records);
      } else {
        message.warn(res?.res?.message);
      }
      setPagination({
        current: res?.res.result.current || 1,
        size: res?.res.result.size || 20,
        total: res?.res.result.total || 0,
      });
    });
  };
  return (
    <div>
      <Button
        size="small"
        type="primary"
        icon="plus"
        className="mb-8"
        style={{ marginTop: 8, height: '28px' }}
        onClick={() => {
          setVisible(true);
          const selectedId = [];
          selectedData?.map((item) => {
            selectedId.push(item.id);
          });
          setSelectedRows(selectedData);
          setSelectedRowKeys(selectedId);
        }}
      >
        添加选项
      </Button>
      {/* 添加选项弹框*/}
      <Modal
        visible={visible}
        title="添加选项"
        onOk={handleOk}
        onCancel={() => {
          setVisible(false);
          setSelectedRows([]);
        }}
        destroyOnClose={true}
        width={600}
      >
        <CreateServiceTypeModal getTableList={getTableList} data={data} />
        <Table
          columns={columns}
          rowSelection={rowSelection}
          dataSource={data}
          pagination={{ total: pagination.total, pageSize: 10 }}
          rowKey="id"
        />
      </Modal>
    </div>
  );
};

export default AddServiceTypeModal;
