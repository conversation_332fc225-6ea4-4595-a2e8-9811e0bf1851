import React, { useState, useEffect, useCallback } from 'react';
import { Const, history, AuthWrapper } from 'qmkit';
import { Button, Form, message, Table } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import { useRequest, useSetState } from 'ahooks';
import SearchForm from '../components/SearchForm';
import styles from '@/styles/index.module.less';
import PageLayout from '@/components/PageLayout/index';
import PaginationProxy from '@/common/constants/Pagination';
import { defaultRender } from '@/utils/string';
import { FormComponentProps } from 'antd/es/form';
import { ButtonProxy } from 'web-common-modules/components';
import {
  getRoleListApi,
  RoleItemType,
  updateRoleApi,
  deleteRoleApi,
} from '@/services/setting/role';
import { RoleStatus, RoleStatusMap } from '@/common/constants/setting/role';
import { useTableHeight } from '@/common/constants/hooks/index';
import PopoverRowText from '@/components/PopoverRowText/index';
const EmployeeList: React.FC<FormComponentProps> = ({ form }) => {
  const [datasource, setDatasource] = useState<any[]>([]);
  const [pagination, setPagination] = useSetState({
    current: 1,
    size: 20,
    total: 0,
  });

  const getColumns: ColumnProps<RoleItemType>[] = [
    {
      dataIndex: 'roleName',
      title: '角色名称',
      width: 120,
      render: (val) => <PopoverRowText text={val || '-'} />,
    },
    {
      dataIndex: 'roleDescription',
      title: '描述',
      width: 200,
      render: (val: string) => <PopoverRowText text={val || '-'} />,
    },
    {
      title: '权限',
      width: 200,
      render: (_, record) => {
        const { topMenuInfos } = record;
        return (
          <PopoverRowText text={topMenuInfos.map((item) => item?.menuName).join('、') || '-'} />
        );
      },
    },
    {
      dataIndex: 'roleStatus',
      title: '状态',
      width: 120,
      render: (val: RoleStatus) => RoleStatusMap[val],
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      render: (_, record) => {
        const { roleStatus, roleInfoId } = record;
        return (
          <div className="action-wrap">
            <AuthWrapper functionName="f_jgpy_role_edit">
              <Button
                type="link"
                className="action-item"
                style={{ marginRight: '6px' }}
                onClick={() =>
                  history.push({
                    pathname: `/setting/role-edit/${roleInfoId}`,
                    // state: {
                    //   roleId: roleInfoId,
                    // },
                  })
                }
              >
                编辑
              </Button>
            </AuthWrapper>

            <AuthWrapper functionName="f_jgpy_role_delete">
              <ButtonProxy.Confirm
                title="确认是否删除？"
                type="link"
                style={{ padding: '3px' }}
                onClick={() => {
                  deleteRoleRun({
                    roleInfoId,
                  });
                }}
              >
                删除
              </ButtonProxy.Confirm>
            </AuthWrapper>

            <AuthWrapper functionName="f_jgpy_role_enable_disable">
              {roleStatus === RoleStatus.ENABLE && (
                <ButtonProxy.Confirm
                  title="确认是否停用？"
                  type="link"
                  style={{ padding: '3px' }}
                  onClick={() => {
                    updateRoleRun({
                      roleInfoId,
                      roleStatus: RoleStatus.DISABLE,
                    });
                  }}
                >
                  停用
                </ButtonProxy.Confirm>
              )}

              {roleStatus === RoleStatus.DISABLE && (
                <ButtonProxy.Confirm
                  title="确认是否启用？"
                  type="link"
                  style={{ padding: '3px' }}
                  onClick={() => {
                    updateRoleRun({
                      roleInfoId,
                      roleStatus: RoleStatus.ENABLE,
                    });
                  }}
                >
                  启用
                </ButtonProxy.Confirm>
              )}
            </AuthWrapper>
          </div>
        );
      },
    },
  ];

  const { loading: getRoleListLoading, run: getRoleListRun } = useRequest(getRoleListApi, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res?.success) {
        const { records, total } = res?.result?.roleInfoByPage || {};
        setDatasource(records || []);
        setPagination({
          total,
        });
        return;
      }
      message.error(res?.message || Const.ERR_MESSAGE);
    },
  });

  const { loading: updateRoleLoading, run: updateRoleRun } = useRequest(updateRoleApi, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res?.success) {
        message.success('操作成功');
        onSearch();
        return;
      }
      message.error(res?.message || Const.ERR_MESSAGE);
    },
  });

  const { loading: deleteRoleLoading, run: deleteRoleRun } = useRequest(deleteRoleApi, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res?.success) {
        message.success('操作成功');
        onSearch();
        return;
      }
      message.error(res?.message || Const.ERR_MESSAGE);
    },
  });

  const onSearch = useCallback(() => {
    const { current, size } = pagination;
    getRoleListRun({
      current,
      size,
      ...form.getFieldsValue(),
    });
  }, [getRoleListRun, form, pagination]);

  useEffect(() => {
    onSearch();
  }, [pagination.current, pagination.size]);

  const onReset = useCallback(() => {
    form.resetFields();
    const { current, size } = pagination;
    if (current === 1 && size === 20) {
      onSearch();
      return;
    }
    setPagination({
      current: 1,
      size: 20,
    });
  }, [form, setPagination, pagination]);
  const { getHeight, tableHeight } = useTableHeight(75);
  return (
    <AuthWrapper functionName="f_jgpy_role_list">
      <PageLayout>
        <div className={`${styles.publishFeeContainer} ${styles['publish-fee-page']} vh46Px`}>
          <div className="formHeight">
            <SearchForm
              form={form}
              loading={getRoleListLoading}
              onSearch={() => {
                if (pagination.current === 1) {
                  onSearch();
                } else {
                  setPagination({
                    current: 1,
                  });
                }
              }}
              onReset={onReset}
            />

            <AuthWrapper functionName="f_jgpy_role_add">
              <div className="mb-16 flex justify-start">
                <Button type="primary" onClick={() => history.push('/setting/role-add')}>
                  新增
                </Button>
              </div>
            </AuthWrapper>
          </div>
          <div style={{ flex: 1 }}>
            <Table
              rowKey="employeeId"
              loading={getRoleListLoading || updateRoleLoading || deleteRoleLoading}
              columns={getColumns}
              dataSource={datasource}
              pagination={false}
              scroll={{ y: tableHeight, x: '100%' }}
            />
          </div>
          <div className={styles['pagination-box'] + ' pageHeight'}>
            <PaginationProxy {...pagination} onChange={setPagination} valueType="merge" />
          </div>
        </div>
      </PageLayout>
    </AuthWrapper>
  );
};

export default Form.create()(EmployeeList);
