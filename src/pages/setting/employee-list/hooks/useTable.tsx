import { EmployeeItemType, IsBanStatus } from '@/services/setting/employee';
import React, { useMemo } from 'react';
import styles from '@/styles/index.module.less';
import { ColumnProps } from 'antd/lib/table';
import { AuthWrapper, checkAuth, history } from 'qmkit';
import PopoverRowText from '@/components/PopoverRowText';
import { ROLE_TYPE, ROLE_TYPE_MAP } from '../components/UpdateRoleData';
import { AccountStatus, AccountStatusMap } from '@/common/constants/setting/employee';
import { Icon, Popconfirm, Tooltip } from 'antd';
import Deactivate from '../components/Deactivate';
export const useTable = ({
  updateLoading,
  updateRun,
  onSearch,
}: {
  updateLoading: boolean;
  updateRun: (params: {
    employeeIdList: string[];
    accountState?: 0 | 1;
    departmentIds?: string[];
    accountDisableReason?: string;
  }) => Promise<IAsyncResult<BaseResult<string>>>;
  onSearch: () => void;
}) => {
  const columns = useMemo<ColumnProps<EmployeeItemType>[]>(
    () => [
      {
        title: '#',
        align: 'center',
        key: 'number',
        className: styles['table-number'],
        render: (text: any, red: any, index: any) => {
          return <span>{index + 1}</span>;
        },
        width: 28,
      },
      {
        dataIndex: 'employeeName',
        title: '员工姓名',
        width: 130,
        render: (val) => val,
      },
      {
        dataIndex: 'employeeMobile',
        title: '员工手机号',
        width: 120,
        render: (val: number | string) => val,
      },
      {
        dataIndex: 'email',
        title: '员工邮箱',
        width: 230,
        render: (val: number | string) => val,
      },
      {
        dataIndex: 'jobNo',
        title: '工号',
        width: 120,
        render: (val: number | string, record) => {
          return (
            <a
              onClick={() => {
                checkAuth('f_jgpy_employee_info') &&
                  history.push('/setting/employee-detail/' + record.employeeId);
              }}
            >
              {val}
            </a>
          );
        },
      },
      {
        dataIndex: 'position',
        title: '岗位',
        width: 120,
        render: (val: number | string) => val,
      },
      {
        dataIndex: 'jobTitle',
        title: '职务',
        width: 120,
        render: (val: number | string) => val,
      },
      {
        dataIndex: 'leaderEmployeeName',
        title: '上级',
        width: 120,
        render: (val: number | string) => val,
      },
      {
        dataIndex: 'departments',
        title: '部门',
        width: 150,
        render: (_, record) => {
          return (
            <PopoverRowText
              text={record?.departments?.map((item) => item?.departmentName)?.join('、')}
            />
          );
        },
      },
      {
        dataIndex: 'jpgyDept',
        title: '事业部',
        width: 120,
        render: (jpgyDept: string, record) => {
          return jpgyDept ?? '-';
        },
      },
      {
        dataIndex: 'bizRoleType',
        title: '角色',
        width: 120,
        render: (bizRoleType: ROLE_TYPE, record) => {
          return bizRoleType ? ROLE_TYPE_MAP[bizRoleType] : '-';
        },
      },
      {
        dataIndex: 'roleInfos',
        title: '角色权限',
        width: 120,
        render: (_, record) => {
          const { roleInfos = [] } = record;

          const len = roleInfos.length;

          if (len === 0) {
            return '-';
          }

          return roleInfos?.map((item, index) => {
            if (item?.roleStatus === IsBanStatus.ENABLE) {
              return (
                <>
                  {item?.roleName}
                  {index !== len - 1 ? '、' : ''}
                </>
              );
            }
            return (
              <>
                {item?.roleName} <span style={{ color: '#f00' }}>(已停用)</span>
                {index !== len - 1 ? '、' : ''}
              </>
            );
          });
        },
      },
      {
        dataIndex: 'dataRoleInfos',
        title: '数据权限',
        width: 120,
        render: (_, record) => {
          const { dataRoleInfos = [] } = record;

          const len = dataRoleInfos.length;

          if (len === 0) {
            return '-';
          }

          return dataRoleInfos?.map((item, index) => {
            if (item?.status === IsBanStatus.ENABLE) {
              return (
                <>
                  {item?.name}
                  {index !== len - 1 ? '、' : ''}
                </>
              );
            }
            return (
              <>
                {item?.name} <span style={{ color: '#f00' }}>(已停用)</span>
                {index !== len - 1 ? '、' : ''}
              </>
            );
          });
        },
      },
      {
        dataIndex: 'accountState',
        title: '状态',
        width: 120,
        render: (val: AccountStatus, { accountDisableReason }) => {
          return (
            <div>
              <span>{AccountStatusMap[val]}</span>
              {val === AccountStatus.BAN && accountDisableReason && (
                <Tooltip className="ml-5" title={accountDisableReason}>
                  <Icon type="question-circle" />
                </Tooltip>
              )}
            </div>
          );
        },
      },
      {
        title: '操作',
        fixed: 'right' as const,
        width: 120,
        render: (_, record) => {
          return (
            <div className="action-wrap">
              <AuthWrapper functionName="f_jgpy_employee_edit">
                <a
                  style={{ marginRight: '8px' }}
                  onClick={() => {
                    history.push('/setting/employee-edit/' + record.employeeId);
                  }}
                >
                  编辑
                </a>
              </AuthWrapper>

              <AuthWrapper functionName="f_jgpy_employee_enable_disable">
                {record?.accountState === AccountStatus.OPEN && (
                  <Deactivate loading={updateLoading} record={record} onOk={onSearch}>
                    <a type="link">停用</a>
                  </Deactivate>
                )}

                {record?.accountState === AccountStatus.BAN && (
                  <Popconfirm
                    getPopupContainer={() => document.body}
                    title="确认是否启用账号？"
                    onConfirm={() => {
                      updateRun({
                        employeeIdList: [record?.employeeId as string],
                        accountState: AccountStatus.OPEN as 0 | 1,
                      });
                    }}
                  >
                    <a type="link">启用</a>
                  </Popconfirm>
                )}
              </AuthWrapper>
            </div>
          );
        },
      },
    ],
    [],
  );
  return { columns };
};
