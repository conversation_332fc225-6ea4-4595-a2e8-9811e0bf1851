import { Form, Row, Col, Table, Spin, Button } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.module.less';
import {
  CreateConfigRequest,
  selectionProcessLabelConfigDetail,
  SelectionProcessLabelConfigDetailResult,
} from './services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { ColumnProps } from 'antd/lib/table';
import { RequiredEnum } from '.';
import PageLayout from '@/components/PageLayout';
import {
  Card,
  DetailContentLayout,
  DetailTitle,
  SpinCard,
} from '@/components/DetailFormCompoments';
import {
  DetailContentItem,
  DetailContextBox,
} from '@/components/DetailFormCompoments/DetailContentItem';
import { history } from 'qmkit';
import { getQueryParams } from '@/pages/anchor-commercial-order/utils/utils';
const Item = DetailContentItem;
type IProps = FormComponentProps<CreateConfigRequest>;

export type labelOptionConfigInfoType = NonNullable<
  SelectionProcessLabelConfigDetailResult['labelOptionConfigs']
>[number];
const AddSelectTabPage: React.FC<IProps> = ({ form }) => {
  const [detailData, setDetail] = useState<SelectionProcessLabelConfigDetailResult>();
  const [loading, setLoading] = useState(false);
  const columns: ColumnProps<labelOptionConfigInfoType>[] = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: '标签选项',
      dataIndex: 'labelOptionName',
      key: 'labelOptionName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (status: labelOptionConfigInfoType['status']) => {
        if (status === 'DISABLE') {
          return '停用';
        } else if (status === 'ENABLE') {
          return '启用';
        }
      },
    },
  ];
  const detail = async (id: string) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: selectionProcessLabelConfigDetail,
      params: { id },
    });
    setLoading(false);
    if (result) {
      setDetail(result);
    }
  };
  const recordId = useMemo(() => getQueryParams().recordId, []);
  const initPage = (recordId: string) => {
    detail(recordId);
  };
  useEffect(() => {
    recordId && initPage(recordId);
  }, []);
  const handleGoEdit = () => {
    history.push(`/selection-tag-configuration/add?recordId=${recordId}`);
  };
  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText={detailData?.labelName}>
          <Button type="primary" onClick={handleGoEdit}>
            编辑
          </Button>
        </DetailTitle>
        <SpinCard spinning={loading}>
          <Card title="基本信息">
            <DetailContextBox>
              <Item label="标签名称">{detailData?.labelName ?? '-'}</Item>
              <Item label="应用事业部">{detailData?.deptName ?? '-'}</Item>
              <Item label="直播间">{detailData?.liveRoomNames ?? '-'}</Item>
              <Item label="是否必填">
                {detailData?.required ? RequiredEnum[detailData.required] : '-'}
              </Item>
              <Item label="说明">{detailData?.notes ?? '-'}</Item>
            </DetailContextBox>
          </Card>
          <Card title="标签选项配置">
            <div style={{ padding: '0 16px' }}>
              <section className={styles.configContent}>
                <Table
                  loading={loading}
                  pagination={false}
                  columns={columns}
                  dataSource={detailData?.labelOptionConfigs ?? []}
                  rowKey="id"
                />
              </section>
            </div>
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
