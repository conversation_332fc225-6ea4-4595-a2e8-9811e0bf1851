import {
  Button,
  Form,
  Input,
  message,
  Row,
  Col,
  Radio,
  Select,
  Table,
  Popconfirm,
  Modal,
  Spin,
} from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { history } from 'qmkit';
import React, { useEffect, useMemo, useState } from 'react';
import AddSelectionTagModal from '../modules/AddSelectionTagModal';
import styles from '../index.module.less';
import Space from '@/components/Space/index';
import {
  createConfig,
  CreateConfigRequest,
  editConfig,
  EditConfigRequest,
  selectionProcessLabelConfigDetail,
  SelectionProcessLabelConfigDetailResult,
} from '../services';
import { useLiveRoomList } from '@/hooks/useLiveRoomList';
import { useDeptList } from '@/hooks/useDeptList';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { ColumnProps } from 'antd/lib/table';
import { uniqBy } from 'lodash';
import PageLayout from '@/components/PageLayout';
import { Card, FormBottomCard, FormContentLayout } from '@/components/DetailFormCompoments';
import { getQueryParams } from '@/pages/anchor-information/utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';

const { Option } = Select;

type IProps = FormComponentProps<CreateConfigRequest>;
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 18 },
  },
};
export type labelOptionConfigInfoType = NonNullable<
  SelectionProcessLabelConfigDetailResult['labelOptionConfigs']
>[number];
const AddSelectTabPage: React.FC<IProps> = ({ form }) => {
  const { delRoutetag } = useCloseAndJump();
  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };
  const [dataSource, setDataSource] = useState<labelOptionConfigInfoType[]>([]);
  // 直播间列表
  const { liveList, loading: liveRoomeLoading, handleSearch, getLiveRoomList } = useLiveRoomList();
  const { deptList, loading: deptLoading, getDeptList } = useDeptList();
  const [loading, setLoading] = useState(false);
  const recordId = useMemo(() => getQueryParams().recordId, []);
  const handleDeptChange = (e: string) => {
    getLiveRoomList({ deptId: e });
    form.setFieldsValue({ liveRoomIds: undefined });
  };
  const columns: ColumnProps<labelOptionConfigInfoType>[] = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      render: (_, record, index) => `${index + 1}`,
    },
    {
      title: '标签选项',
      dataIndex: 'labelOptionName',
      key: 'labelOptionName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (status: labelOptionConfigInfoType['status']) => {
        if (status === 'DISABLE') {
          return '停用';
        } else if (status === 'ENABLE') {
          return '启用';
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) =>
        dataSource?.length ? (
          <Popconfirm title="确认删除吗?" onConfirm={() => handleDelete(record.id!)}>
            <a style={{ color: '#FF4D4F' }}>删除</a>
          </Popconfirm>
        ) : null,
    },
  ];
  const detail = async (id: string) => {
    const result = await responseWithResultAsync({
      request: selectionProcessLabelConfigDetail,
      params: { id },
    });
    if (result) {
      result?.deptId && getLiveRoomList({ deptId: result.deptId });
      form?.setFieldsValue({
        deptId: result?.deptId,
        id,
        labelName: result?.labelName,
        labelOptionIds: result?.labelOptionConfigs?.map((item) => item.id),
        liveRoomIds: result?.liveRoomIds,
        notes: result?.notes,
        required: result?.required,
      });
      setDataSource(result?.labelOptionConfigs ?? []);
    }
  };
  const initPage = (recordId: string) => {
    detail(recordId);
  };
  useEffect(() => {
    recordId && initPage(recordId);
  }, [recordId]);

  const concatLabelOptionConfigs = (
    value: SelectionProcessLabelConfigDetailResult['labelOptionConfigs'],
  ) => {
    if (value) {
      const labelOptionConfigs = uniqBy([...dataSource, ...value], 'id');
      setDataSource(labelOptionConfigs);
      form.setFieldsValue({ labelOptionConfigs });
    }
  };
  const handleDelete = (id: string) => {
    const newData = dataSource?.filter((item) => item.id !== id);
    setDataSource(newData);
  };
  const edit = async (params: EditConfigRequest) => {
    const result = await responseWithResultAsync({
      request: editConfig,
      params,
    });
    return result;
  };
  const add = async (params: CreateConfigRequest) => {
    const result = await responseWithResultAsync({
      request: createConfig,
      params,
    });
    return result;
  };
  const handleOk = () => {
    form.validateFields(async (err, value) => {
      if (err) {
        return;
      }
      if (dataSource?.length === 0) {
        message.error('请至少添加一个选项');
        return;
      }
      setLoading(true);
      const id = recordId;
      const result = id ? await edit({ ...value, id }) : await add(value);
      if (result) {
        message.success(id ? '修改成功' : '保存成功');
        setTimeout(() => {
          handleCancel();
        }, 1000);
      }
      setLoading(false);
    });
  };

  useEffect(() => {
    form.setFieldsValue({ labelOptionIds: dataSource.map((item) => item.id!) });
  }, [dataSource]);
  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={loading}>
          <Card title="基本信息">
            <div className={styles.extra}>
              <Form {...formItemLayout}>
                <Row>
                  <Col span={6}>
                    <Form.Item label="标签名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                      {form.getFieldDecorator('labelName', {
                        rules: [
                          {
                            required: true,
                            message: '请输入',
                          },
                          {
                            max: 50,
                            message: '最多输入50个字符',
                          },
                        ],
                      })(<Input style={{ width: '200px' }} placeholder="请输入" />)}
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="应用事业部" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                      {form.getFieldDecorator('deptId', {
                        rules: [
                          {
                            required: true,
                            message: '请选择应用事业部',
                          },
                        ],
                      })(
                        <Select
                          loading={deptLoading}
                          placeholder="请选择应用事业部"
                          allowClear
                          style={{ width: '200px' }}
                          onChange={handleDeptChange}
                        >
                          {deptList.map((item) => (
                            <Option key={item.value} value={item.value}>
                              {item.label}
                            </Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="直播间" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                      {form.getFieldDecorator('liveRoomIds', {
                        rules: [
                          {
                            required: true,
                            message: '请选择直播间',
                          },
                        ],
                      })(
                        <Select
                          maxTagCount={1}
                          loading={liveRoomeLoading}
                          placeholder="请选择直播间"
                          allowClear
                          filterOption={false}
                          onSearch={handleSearch}
                          showSearch
                          mode="multiple"
                          style={{ width: '200px' }}
                        >
                          {liveList.map((item) => (
                            <Option key={item.id} value={item.id}>
                              {item.name}
                            </Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="是否必填" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                      {form.getFieldDecorator('required', {
                        rules: [
                          {
                            required: true,
                            message: '请选择',
                          },
                        ],
                      })(
                        <Radio.Group>
                          <Radio value="REQUIRED">是</Radio>
                          <Radio value="NON_REQUIRED">否</Radio>
                        </Radio.Group>,
                      )}
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={6}>
                    <Form.Item label="说明" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                      {form.getFieldDecorator('notes', {
                        rules: [
                          {
                            max: 50,
                            message: '最多输入50个字符',
                          },
                        ],
                      })(<Input style={{ width: '200px' }} placeholder="请输入说明，最多50字" />)}
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
          </Card>
          <Card title="标签选项配置">
            <div className={styles.extra}>
              <section className={styles.configContent} style={{ padding: '0 16px' }}>
                <Form.Item>
                  {form.getFieldDecorator('labelOptionIds', {
                    rules: [
                      {
                        required: true,
                        message: '请至少添加一个选项',
                      },
                    ],
                  })(
                    <AddSelectionTagModal
                      concatLabelOptionConfigs={concatLabelOptionConfigs}
                      selectedData={dataSource}
                    />,
                  )}
                </Form.Item>
                <Table
                  pagination={false}
                  columns={columns}
                  dataSource={dataSource}
                  rowKey="id"
                  loading={loading}
                />
              </section>
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleOk} className="ml-16">
            保存
          </Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
