import React, { useEffect, useMemo, useReducer, useState } from 'react';
import { Button, Form, Select, message, Radio, Table, Spin } from 'antd';

import { FormComponentProps } from 'antd/es/form';

import { getCreateColunms } from '../../utils';

import {
  lowCommissionConfigDetail,
  LowCommissionConfigDetailResult,
} from '@/services/yml/low-commission-config';

import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import PageLayout from '@/components/PageLayout';
import { history } from 'qmkit';

import { DetailContentLayout, DetailTitle, SpinCard } from '@/components/DetailFormCompoments';
import { LowCommissionReasonTypeEnum, LowCommissionTriggerConditionEnum } from '../../utils';
import {
  DetailContextBox,
  DetailContentItem,
} from '@/components/DetailFormCompoments/DetailContentItem';
const Item = DetailContentItem;
type PropsType = FormComponentProps;

const AddSelectTabPage: React.FC<PropsType> = ({ form }) => {
  const [id, setId] = useState<string>('');
  const [state, setState] = useState<LowCommissionConfigDetailResult | undefined>({
    type: undefined,
  });
  const { closeAndJumpToPage } = useCloseAndJump();
  const initForm = async () => {
    try {
      const { res } = await lowCommissionConfigDetail({ id: id });
      if (res.code !== '200') {
        message.error(res.message || '网络错误');
        return;
      }
      const { result } = res;
      console.log('result', result);
      setState(result);
      form?.setFieldsValue({
        deptId: result.deptId,
        exampleContent: result.exampleContent,
        needAudit: result.needAudit,
        triggerCondition: result.triggerCondition,
        type: result.type,
      });
    } catch (error) {
      message.error('网络错误');
    }
  };
  useEffect(() => {
    id && initForm();
  }, [id]);
  useEffect(() => {
    const idN = location.pathname.split('/')[3];
    setId(idN);
  }, []);
  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle
          titleText={LowCommissionReasonTypeEnum[state?.type as keyof LowCommissionReasonTypeEnum]}
        >
          <Button
            type="primary"
            onClick={() => {
              closeAndJumpToPage('/system-setting/low-commission-config-edit/' + id);
            }}
          >
            编辑
          </Button>
        </DetailTitle>
        <SpinCard spinning={false}>
          <Card title="低佣配置">
            <DetailContextBox>
              <Item label="低佣原因">
                {LowCommissionReasonTypeEnum[state?.type as keyof LowCommissionReasonTypeEnum]}
              </Item>
              <Item label="事业部">{state?.deptName}</Item>
              <Item label="是否触发审核">{state?.needAudit ? '是' : '否'}</Item>
              <Item label="触发条件">
                {
                  LowCommissionTriggerConditionEnum[
                    state?.triggerCondition as keyof LowCommissionTriggerConditionEnum
                  ]
                }
              </Item>
              <Item label="详细原因示例">{state?.exampleContent}</Item>
            </DetailContextBox>
          </Card>
          <Card title="新增字段">
            <section style={{ margin: 12 }}>
              <Table
                columns={getCreateColunms({ disabled: true })}
                dataSource={state?.content}
              ></Table>
            </section>
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
