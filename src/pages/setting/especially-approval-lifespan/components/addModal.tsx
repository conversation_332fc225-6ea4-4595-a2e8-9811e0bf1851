import React, { useEffect, useState } from 'react';
import { Form, Modal, Input, message, Spin, Select, InputNumber } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { useRequest } from 'ahooks';
import { plantformListAll } from '../../../../../web_modules/types';
import { getQueryByParentId } from '@/services/yml/goods/index';
import { specialAuditCateExpireDateAdd } from '../services';
// import { returnSampleRegister } from '../services';

const { Option } = Select;
interface IProps extends ModalProps {
  form: WrappedFormUtils;
  [key: string]: any;
  // defaultTab: string;
}

// 表单layout
const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 16,
  },
};

const AddModal = (props: IProps) => {
  const { form, onGetList, visible, ...rest } = props;
  const { getFieldDecorator } = form;
  const [firstCateList, setFirstCateList] = useState([]);

  const searchfirstCate = (value: any) => {
    getQueryByParentId({
      parentId: '0',
      source: value,
    }).then((res) => {
      if (res?.res?.code === '200') {
        const result = res?.res?.result;
        if (result?.length) {
          setFirstCateList(result);
        } else {
          setFirstCateList([]); // 清空列表
        }
      } else {
        setFirstCateList([]); // 清空列表
      }
    });
  };
  const { run, loading: loading } = useRequest(specialAuditCateExpireDateAdd, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('提交成功');
        onGetList();
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });
  const handleOk = () => {
    form.validateFields((err, value) => {
      if (err) {
        return;
      }
      console.log('value', value);

      // const { returnSampleRegister } = value;
      run({
        ...value,
      });
    });
  };
  useEffect(() => {
    if (form.getFieldValue('platform')) {
      const value =
        form.getFieldValue('platform') === 'TB' ? 'TAOBAO' : form.getFieldValue('platform');
      searchfirstCate(value);
    } else {
      setFirstCateList([]); // 清空列表
    }
  }, [form.getFieldValue('platform')]);
  useEffect(() => {
    if (visible) {
      form.resetFields();
      // 设置默认值
      form.setFieldsValue({
        firstCateId: '*',
        type: '*',
        specialMaerial: '*',
        needQualificationCompletion: '*',
      });
    }
  }, [visible]);
  return (
    <Modal
      title="特批有效期配置"
      {...rest}
      width={500}
      onOk={handleOk}
      onCancel={() => onGetList()}
      visible={visible}
      confirmLoading={loading}
    >
      <Form labelAlign="right">
        <Form.Item {...formItemLayout} required label="平台">
          {getFieldDecorator('platform', {
            rules: [{ required: true, message: '请选择平台' }],
          })(
            <Select
              allowClear
              placeholder="请选择"
              onChange={() => {
                // 重置一级类目选择
                form.setFieldsValue({
                  firstCateId: '*',
                });
              }}
            >
              {plantformListAll?.map((item) => (
                <Option value={item.value} key={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="一级类目">
          {getFieldDecorator('firstCateId', {
            rules: [{ required: true, message: '请选择一级类目' }],
          })(
            <Select
              allowClear
              placeholder="请选择"
              showSearch
              filterOption={true}
              optionFilterProp="children"
            >
              <Option value={'*'}>{'*'}</Option>
              {firstCateList?.map((item) => (
                <Option value={item.id} key={item.id}>
                  {item.cateName}
                </Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="资质特批方式">
          {getFieldDecorator('type', {
            rules: [{ required: true, message: '请选择资质特批方式' }],
          })(
            <Select allowClear placeholder="请选择">
              <Option value={'*'}>{'*'}</Option>
              <Option value={'ONLINE'}>{'线上特批'}</Option>
              <Option value={'OFFLINE'}>{'线下特批'}</Option>
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="是否特殊材质">
          {getFieldDecorator('specialMaerial', {
            rules: [{ required: true, message: '请选择是否特殊材质' }],
          })(
            <Select allowClear placeholder="请选择">
              <Option value={'*'}>{'*'}</Option>
              <Option value={'1'}>{'是'}</Option>
              <Option value={'0'}>{'否'}</Option>
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="后续补足资质">
          {getFieldDecorator('needQualificationCompletion', {
            rules: [{ required: true, message: '请选择后续补足资质' }],
          })(
            <Select allowClear placeholder="请选择">
              <Option value={'*'}>{'*'}</Option>
              <Option value={'1'}>{'是'}</Option>
              <Option value={'0'}>{'否'}</Option>
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="有效期范围(天)">
          {getFieldDecorator('expireDate', {
            rules: [{ required: true, message: '请输入' }],
          })(
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入"
              min={1}
              max={30000}
              precision={0}
            />,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="状态">
          {getFieldDecorator('status', {
            rules: [{ required: true, message: '请选择状态' }],
          })(
            <Select allowClear placeholder="请选择">
              <Option value={1}>{'启用'}</Option>
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} required label="排序">
          {getFieldDecorator('sort', {
            rules: [{ required: true, message: '请输入' }],
          })(
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入"
              min={1}
              max={30000}
              precision={0}
            />,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(AddModal);
