import { useRequest } from 'ahooks';
import { Button, Form, Input, message, Radio, Spin, Tree, Checkbox } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { Const, history } from 'qmkit';
import React, { useEffect, useState, useMemo } from 'react';
import LayoutContentItem from '@/components/PageLayout/ContentItem';
import PageLayout from '@/components/PageLayout/index';

import { CanSeeWorkScope } from '@/common/constants/setting/dataRole';
// import { addDataRoleApi, updateDataRoleApi } from '@/services/setting/dataRole';
// import { getDataRoleById } from '@/services/gql/data-role';

// import { getLiveRoom } from '@/pages/polymerization-goods/services';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { useDeptList } from '@/hooks/useDeptList';
import CheckboxGroup from 'antd/lib/checkbox/Group';
import {
  addData<PERSON><PERSON><PERSON><PERSON>,
  getDataRoleById,
  GetDataRoleByIdResult,
  getLiveRoom,
  updateData<PERSON>ole<PERSON><PERSON>,
} from '@/services/yml/user';
import {
  SEE_BUSINESS_OPPORTUNITY_SCOPE,
  SEE_BUSINESS_OPPORTUNITY_SCOPE_LIST,
  SEE_BUSINESS_OPPORTUNITY_PUBLIC_POOL_SCOPE,
  SEE_BUSINESS_OPPORTUNITY_PUBLIC_POOL_SCOPE_LIST,
  OLAL_BROADCAST_LIBRARY_ENUM,
  OLAL_BROADCAST_LIBRARY_NAME,
} from '../types';
import { useWarehouseList } from '@/pages/warehouse-management/area-manager/utils/hook';

interface IProps extends FormComponentProps {
  location: {
    state: { dataRoleId: string };
  };
}

const AddDataRole: React.FC<IProps> = ({ form, location }) => {
  const [selectedKeys, setSelectedKeys] = useState<GetDataRoleByIdResult['seeLiveRoomList']>([]);

  const [buNames, setBuNames] = useState<string[]>([]);

  const [liveRoomList, setLiveRoomList] = useState<{ title: string; key: string; children: any }[]>(
    [],
  );
  const { closeAndJumpToPage } = useCloseAndJump();

  const { deptList, loading: deptLoading } = useDeptList();

  const [selectedDeptKeys, setSelectedDeptKeys] = useState<string[]>([]);

  const [marketingDeptKeys, setMarketingDeptKeys] = useState<string[]>([]);
  const [anchorBuIdScopeList, setAnchorBuIdScopeList] = useState<string[]>([]);
  const [olalBroadcastLibraryDeptList, setOlalBroadcastLibraryDeptList] = useState<string[]>([]);
  const [trafficConfigurationBuIds, setTrafficConfigurationBuIds] = useState<string[]>([]);
  const [seeWarehouseIdList, setWarehouseIdList] = useState<string[]>([]);
  // const [isCheckTag, setIsCheckTag] = useState(true);

  const { loading: addDataRoleLoading, run: addDataRoleRun } = useRequest(addDataRoleApi, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res?.success) {
        message.success('操作成功');
        setTimeout(() => {
          closeAndJumpToPage('/setting/data-management');
        }, 1000);
        return;
      }
      message.error(res?.message || Const.ERR_MESSAGE);
    },
  });

  const { loading: updateDataRoleLoading, run: updateDataRoleRun } = useRequest(updateDataRoleApi, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res?.success) {
        message.success('操作成功');
        setTimeout(() => {
          closeAndJumpToPage('/setting/data-management');
        }, 1000);
        return;
      }
      message.error(res?.message || Const.ERR_MESSAGE);
    },
  });

  const { run: getDataRoleDetailRun } = useRequest(() => getDataRoleById({ id: dataRoleId }), {
    manual: true,
    onSuccess: (res) => {
      // setIsCheckTag(false);
      if (res?.res?.success) {
        const data = res?.res?.result;
        //@TODO: 保量回显
        const {
          description,
          name,
          seeBUList,
          seeFinanceScope,
          seeSupplierScope,
          seeTalentScope,
          seeCommissionScope,
          seeCooperationGuaranteedScope,
          seeFinancialSettlementScope,
          seeBuIdList,
          seeMarketingDataScope,
          seeMarketingDataBuIdList,
          seeFinancialContractsScope,
          seeAnchorScope,
          seeAnchorBuIdScopeList,
          trafficConfiguration,
          trafficConfigurationBuIds,
          seeBusinessOpportunityScopeList,
          seeBusinessOpportunityPublicPoolScope,
          seeBusinessOpportunityScope,
          seeDeptSpokenScriptInfoScopeList,
          seeSpokenScriptInfoScope,
          seeWarehouseIdList,
        } = data || {};
        setSelectedKeys(data?.seeLiveRoomList || []);
        form.setFieldsValue({
          name,
          description,
          seeFinanceScope:
            seeFinanceScope === CanSeeWorkScope.NONE ? CanSeeWorkScope.ALL : seeFinanceScope,
          seeSupplierScope:
            seeSupplierScope === CanSeeWorkScope.NONE ? CanSeeWorkScope.ALL : seeSupplierScope,
          seeTalentScope:
            seeTalentScope === CanSeeWorkScope.NONE ? CanSeeWorkScope.ALL : seeTalentScope,
          seeCommissionScope:
            seeCommissionScope === CanSeeWorkScope.NONE ? CanSeeWorkScope.ALL : seeCommissionScope,
          seeBUList,
          seeCooperationGuaranteedScope:
            seeCooperationGuaranteedScope === CanSeeWorkScope.NONE
              ? CanSeeWorkScope.ALL
              : seeCooperationGuaranteedScope,
          seeFinancialSettlementScope:
            seeFinancialSettlementScope === CanSeeWorkScope.NONE
              ? CanSeeWorkScope.SELF_AND_SUB
              : seeFinancialSettlementScope,
          seeMarketingDataScope:
            seeMarketingDataScope === CanSeeWorkScope.NONE
              ? CanSeeWorkScope.SELF_AND_SUB
              : seeMarketingDataScope,
          seeFinancialContractsScope:
            seeFinancialContractsScope === CanSeeWorkScope.NONE
              ? CanSeeWorkScope.ALL
              : seeFinancialContractsScope,
          seeMarketingDataBuIdList,
          seeBuIdList,
          seeAnchorScope,
          seeAnchorBuIdScopeList,
          trafficConfiguration,
          trafficConfigurationBuIds,
          seeBusinessOpportunityScopeList,
          seeBusinessOpportunityPublicPoolScope,
          seeBusinessOpportunityScope,
          seeSpokenScriptInfoScope,
          seeWarehouseIdList,
        });
        setSelectedDeptKeys(seeBuIdList || []);
        setMarketingDeptKeys(seeMarketingDataBuIdList || []);
        setAnchorBuIdScopeList(seeAnchorBuIdScopeList || []);
        setTrafficConfigurationBuIds(trafficConfigurationBuIds || []);
        setOlalBroadcastLibraryDeptList(seeDeptSpokenScriptInfoScopeList || []);
        setWarehouseIdList(seeWarehouseIdList || []);
        return;
      }
      message.error(res?.res?.message || Const.ERR_MESSAGE);
    },
  });

  const { loading: getLiveRoomLoading, run: getLiveRoomRun } = useRequest(getLiveRoom, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        const list = Object.keys(res?.result)?.map((key) => {
          return {
            title: key,
            key: key,
            children: res?.result[key].map((_val) => ({ title: _val.name, key: _val.openId })),
          };
        });
        setLiveRoomList(list);
        setBuNames(Object.keys(res?.result) || []);
      }
    },
  });

  const dataRoleId = useMemo(() => {
    return window.location.pathname.split('/')[3];
  }, [window.location.pathname]);

  useEffect(() => {
    if (dataRoleId) {
      getDataRoleDetailRun();
    }
  }, [dataRoleId]);

  // useEffect(() => {
  //   setIsCheckTag(true);
  // }, [selectedDeptKeys]);

  useEffect(() => {
    getLiveRoomRun({});
    // if (!!location?.state?.dataRoleId) {
    //   getDataRoleDetailRun();
    // }
  }, []);

  const submit = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }

      if (dataRoleId) {
        // 编辑接口
        updateDataRoleRun({
          id: dataRoleId,
          ...values,
          seeLiveRoomList: selectedKeys?.filter((_) => !buNames.includes(_)),
          // seeBuIdList: selectedDeptKeys,
        });
        return;
      }
      addDataRoleRun({ ...values, seeLiveRoomList: selectedKeys });
    });
  };
  const { list: warehouseList, loading: warehouseLoading } = useWarehouseList(true);

  return (
    <PageLayout
      footer={
        <div className="flex justify-end">
          <Button
            onClick={() => closeAndJumpToPage('/setting/data-management')}
            loading={addDataRoleLoading || updateDataRoleLoading}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={submit}
            className="ml-16"
            loading={addDataRoleLoading || updateDataRoleLoading}
          >
            确定
          </Button>
        </div>
      }
    >
      <Spin spinning={false}>
        <LayoutContentItem>
          <Form style={{ width: '80%' }}>
            <Form.Item label="数据权限名称">
              {form.getFieldDecorator('name', {
                rules: [
                  {
                    required: true,
                    message: '请输入数据权限名称，最多20字',
                  },
                  {
                    max: 20,
                    message: '最多输入20个字符',
                  },
                ],
              })(<Input placeholder="请输入数据权限名称，最多20字" />)}
            </Form.Item>
            <Form.Item label="数据权限描述">
              {form.getFieldDecorator('description', {
                rules: [
                  {
                    max: 50,
                    message: '最多输入50个字符',
                  },
                ],
              })(
                <Input.TextArea
                  rows={3}
                  placeholder="请输入数据权限描述，最多50字"
                  style={{ resize: 'none' }}
                />,
              )}
            </Form.Item>
            <Form.Item label="商家相关数据">
              {form.getFieldDecorator('seeSupplierScope', {
                initialValue: CanSeeWorkScope.ALL,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看本人及下属所负责的</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="达人相关数据">
              {form.getFieldDecorator('seeTalentScope', {
                initialValue: CanSeeWorkScope.ALL,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看本人及下属所负责的</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="业财端相关数据">
              {form.getFieldDecorator('seeFinanceScope', {
                initialValue: CanSeeWorkScope.ALL,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看本人及下属所负责的</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="佣金和固定服务费相关数据">
              {form.getFieldDecorator('seeCommissionScope', {
                initialValue: CanSeeWorkScope.ALL,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看本人及下属所负责的</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio>
                  <Radio value={CanSeeWorkScope.ASSISTANT}>仅查看商务助理映射的商务负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="保量合同数据">
              {form.getFieldDecorator('seeCooperationGuaranteedScope', {
                initialValue: CanSeeWorkScope.ALL,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看本人及下属所负责的</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio>
                  <Radio value={CanSeeWorkScope.ASSISTANT}>仅查看商务助理映射的商务负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            {/* 0全部，1本人负责，2事业部 */}
            <Form.Item label="财务结算相关数据">
              {form.getFieldDecorator('seeFinancialSettlementScope', {
                initialValue: CanSeeWorkScope.SELF_AND_SUB,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>查看事业部数据</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>仅查看本人所负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            {form?.getFieldsValue()?.seeFinancialSettlementScope === CanSeeWorkScope.SELF && (
              <Form.Item label="事业部数据">
                {form.getFieldDecorator('seeBuIdList', {
                  rules: [
                    {
                      required: true,
                      message: '请选择事业部',
                    },
                  ],
                  initialValue: selectedDeptKeys,
                })(<Checkbox.Group options={deptList} />)}
              </Form.Item>
            )}
            <Form.Item label="营销管理相关数据">
              {form.getFieldDecorator('seeMarketingDataScope', {
                initialValue: CanSeeWorkScope.ALL,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>查看事业部数据</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>仅查看本人所负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            {form?.getFieldsValue()?.seeMarketingDataScope === CanSeeWorkScope.SELF && (
              <Form.Item label="营销管理相关事业部数据">
                {form.getFieldDecorator('seeMarketingDataBuIdList', {
                  rules: [
                    {
                      required: true,
                      message: '请选择事业部',
                    },
                  ],
                  initialValue: marketingDeptKeys,
                })(<CheckboxGroup options={deptList} />)}
              </Form.Item>
            )}
            <Form.Item label="支出合同管理数据">
              {form.getFieldDecorator('seeFinancialContractsScope', {
                initialValue: CanSeeWorkScope.ALL,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看部门及下属部门所负责的</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="主播管理相关数据">
              {form.getFieldDecorator('seeAnchorScope', {
                initialValue: CanSeeWorkScope.SELF,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看事业部数据</Radio>
                  <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            {form?.getFieldsValue()?.seeAnchorScope === CanSeeWorkScope.SELF_AND_SUB && (
              <Form.Item label="主播管理相关事业部数据">
                {form.getFieldDecorator('seeAnchorBuIdScopeList', {
                  initialValue: anchorBuIdScopeList,
                  rules: [{ required: true, message: '至少选择一个' }],
                })(<CheckboxGroup options={deptList} />)}
              </Form.Item>
            )}
            <Form.Item label="流量投放配置数据权限">
              {form.getFieldDecorator('trafficConfiguration', {
                initialValue: CanSeeWorkScope.SELF,
              })(
                <Radio.Group>
                  <Radio value={CanSeeWorkScope.ALL}>查看全部</Radio>
                  <Radio value={CanSeeWorkScope.SELF_AND_SUB}>查看事业部数据</Radio>
                  {/* <Radio value={CanSeeWorkScope.SELF}>仅查看本人所负责的</Radio> */}
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="商机全部">
              {form.getFieldDecorator('seeBusinessOpportunityScopeList', {
                initialValue: [SEE_BUSINESS_OPPORTUNITY_SCOPE.SELF],
              })(
                <Checkbox.Group>
                  {SEE_BUSINESS_OPPORTUNITY_SCOPE_LIST.map((item) => (
                    <Checkbox
                      value={item.value}
                      key={item.value}
                      style={{ marginBottom: '4px', marginLeft: 0 }}
                    >
                      {item.label}
                    </Checkbox>
                  ))}
                </Checkbox.Group>,
              )}
            </Form.Item>
            {/* seeBusinessOpportunityPublicPoolScope */}
            <Form.Item label="商机公海池">
              {form.getFieldDecorator('seeBusinessOpportunityPublicPoolScope', {
                initialValue: SEE_BUSINESS_OPPORTUNITY_PUBLIC_POOL_SCOPE.DEPT,
              })(
                <Radio.Group>
                  {SEE_BUSINESS_OPPORTUNITY_PUBLIC_POOL_SCOPE_LIST.map((item) => (
                    <Radio value={item.value} key={item.value}>
                      {item.label}
                    </Radio>
                  ))}
                </Radio.Group>,
              )}
            </Form.Item>
            <Form.Item label="口播稿库数据">
              {form.getFieldDecorator('seeSpokenScriptInfoScope', {
                initialValue: OLAL_BROADCAST_LIBRARY_ENUM.ALL,
              })(
                <Radio.Group>
                  <Radio value={OLAL_BROADCAST_LIBRARY_ENUM.ALL}>
                    {OLAL_BROADCAST_LIBRARY_NAME[OLAL_BROADCAST_LIBRARY_ENUM.ALL]}
                  </Radio>
                  <Radio value={OLAL_BROADCAST_LIBRARY_ENUM.SELF_DEPT}>
                    {OLAL_BROADCAST_LIBRARY_NAME[OLAL_BROADCAST_LIBRARY_ENUM.SELF_DEPT]}
                  </Radio>
                </Radio.Group>,
              )}
            </Form.Item>
            {form?.getFieldsValue()?.seeSpokenScriptInfoScope ===
              OLAL_BROADCAST_LIBRARY_ENUM.SELF_DEPT && (
              <Form.Item label="口播稿库相关事业部数据">
                {form.getFieldDecorator('seeDeptSpokenScriptInfoScopeList', {
                  initialValue: olalBroadcastLibraryDeptList,
                  rules: [{ required: true, message: '至少选择一个' }],
                })(<CheckboxGroup options={deptList} />)}
              </Form.Item>
            )}
            {form?.getFieldsValue()?.trafficConfiguration === CanSeeWorkScope.SELF_AND_SUB && (
              <Form.Item label="流量投放相关事业部数据">
                {form.getFieldDecorator('trafficConfigurationBuIds', {
                  initialValue: trafficConfigurationBuIds,
                  rules: [{ required: true, message: '至少选择一个' }],
                })(<CheckboxGroup options={deptList} />)}
              </Form.Item>
            )}
            {!!liveRoomList.length && (
              <Form.Item label="直播间数据">
                <Tree
                  checkable={true}
                  onCheck={(val) => {
                    setSelectedKeys(val as string[]);
                  }}
                  checkedKeys={selectedKeys}
                  treeData={liveRoomList}
                  defaultExpandAll={true}
                />
              </Form.Item>
            )}

            {warehouseList && (
              <Form.Item label="仓库数据权限">
                {form.getFieldDecorator('seeWarehouseIdList', {
                  initialValue: seeWarehouseIdList,
                  // rules: [{ required: true, message: '至少选择一个' }],
                })(
                  <CheckboxGroup
                    options={
                      warehouseList?.map((_) => ({
                        label: _.name,
                        value: _.id!.toString(),
                      })) || []
                    }
                  />,
                )}
              </Form.Item>
            )}
          </Form>
        </LayoutContentItem>
      </Spin>
    </PageLayout>
  );
};

export default Form.create()(AddDataRole);
