import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type LiveCalendarListRequest = {
  deptId?: string /*事业部id*/;
  firstDayOfMonth?: string /*开始时间*/;
  lastDayOfMonth?: string /*结束时间*/;
  liveRoomId?: string /*直播间id*/;
  yearMonth?: string /*当前月份*/;
};

export type LiveCalendarListResult = Array<{
  id?: string /*主键*/;
  linkNum?: string /*挂链数量*/;
  liveCalendarDetails?: Array<{ [key: string]: string }> /*直播日历详情*/;
  liveDate?: string /*直播日期*/;
  remark?: string /*备注*/;
  roundDesc?: string /*货盘简述*/;
  roundTag?: string /*场次标签*/;
  specialSchedule?: string /*专场排期*/;
  subject?: string /*场次主题*/;
  talkNum?: string /*讲解数量*/;
}>;

/**
 *直播日历列表
 */
export const liveCalendarList = (params: LiveCalendarListRequest) => {
  return Fetch<ResponseWithResult<LiveCalendarListResult>>(
    '/iasm/public/web/liveRound/liveCalendar',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/web/liveRound/liveCalendar') },
    },
  );
};

export type EditByCalendarRequest = {
  anchorType?: 'PRIMARY_ANCHOR' | 'SECONDARY_ANCHOR' | 'TALENT_ANCHOR' /*主播类型[AnchorTypeEnum]*/;
  brandFeeGoal?: string /*目标基础服务费数-单位元*/;
  commissionGoal?: string /*目标佣金数-单位元*/;
  gmvGoal?: string /*目标gmv数-单位元*/;
  holeNum?: number /*坑位数*/;
  id?: string /*场次id*/;
  incomeGoal?: string /*目标收入数-单位元*/;
  linkNum?: string /*挂链数量汇总*/;
  liveCalendarDetails?: Array<{ [key: string]: string }> /*直播日历详情*/;
  name?: string /*场次名称*/;
  personInCharge?: string /*负责人id*/;
  remark?: string /*备注*/;
  roundDesc?: string /*货盘简述*/;
  roundTag?: string /*场次标签*/;
  specialSchedule?: string /*专场排期*/;
  subject?: string /*直播主题*/;
  talkNum?: string /*讲解数量汇总*/;
};

export type EditByCalendarResult = boolean;

/**
 *直播场次编辑-直播日历维度
 */
export const editByCalendar = (params: EditByCalendarRequest) => {
  return Fetch<ResponseWithResult<EditByCalendarResult>>(
    '/iasm/public/web/liveRound/editByCalendar',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/web/liveRound/editByCalendar') },
    },
  );
};

export type QueryTalkLinkNumRequest = {
  deptId?: string /*事业部ID*/;
  liveDate?: string /*直播日期*/;
  liveRoomId?: string /*直播间ID*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*来源平台[PlatformEnum]*/;
};

export type QueryTalkLinkNumResult = {
  linkNum?: string /*挂链数量-上限*/;
  linkNumUsed?: string /*挂链数量-已使用*/;
  talkNum?: string /*讲解数量-上限*/;
  talkNumUsed?: string /*讲解数量-已使用*/;
};

/**
 *查询讲解、挂链坑位
 */
export const queryTalkLinkNum = (params: QueryTalkLinkNumRequest) => {
  return Fetch<ResponseWithResult<QueryTalkLinkNumResult>>(
    '/iasm/public/selection/queryTalkLinkNum',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/queryTalkLinkNum') },
    },
  );
};

export type LiveRoundBatchEditByCalendarRequest = {
  dataList?: Array<{
    anchorType?:
      | 'PRIMARY_ANCHOR'
      | 'SECONDARY_ANCHOR'
      | 'TALENT_ANCHOR' /*主播类型[AnchorTypeEnum]*/;
    brandFeeGoal?: string /*目标基础服务费数-单位元*/;
    commissionGoal?: string /*目标佣金数-单位元*/;
    gmvGoal?: string /*目标gmv数-单位元*/;
    holeNum?: number /*坑位数*/;
    id?: string /*场次id*/;
    incomeGoal?: string /*目标收入数-单位元*/;
    linkNum?: string /*挂链数量汇总*/;
    liveCalendarDetails?: Array<{ [key: string]: string }> /*直播日历详情*/;
    name?: string /*场次名称*/;
    personInCharge?: string /*负责人id*/;
    remark?: string /*备注*/;
    roundDesc?: string /*货盘简述*/;
    roundTag?: string /*场次标签*/;
    specialSchedule?: string /*专场排期*/;
    subject?: string /*直播主题*/;
    talkNum?: string /*讲解数量汇总*/;
  }> /*挂链数量汇总*/;
};

export type LiveRoundBatchEditByCalendarResult = boolean;

/**
 *直播场次编辑-直播日历维度-批量编辑
 */
export const liveRoundBatchEditByCalendar = (params: LiveRoundBatchEditByCalendarRequest) => {
  return Fetch<ResponseWithResult<LiveRoundBatchEditByCalendarResult>>(
    '/iasm/public/web/liveRound/batchEditByCalendar',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/web/liveRound/batchEditByCalendar') },
    },
  );
};
