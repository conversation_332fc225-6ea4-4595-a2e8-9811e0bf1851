// ai生成
.timeSlotContainer {
  width: 100%;
  position: relative;
}

.timeSlotArea {
  position: relative;
  width: 100%;
  background: transparent;

  &:hover {
    .timeAxisItem {
      background-color: rgba(24, 144, 255, 0.02);
    }
  }
}

.timeAxisItem {
  position: absolute;
  display: flex;
  align-items: flex-start;
  padding-left: 8px;
  transition: background-color 0.3s ease;

  .timeLabel {
    font-size: 12px;
    color: #999;
    background: #fff;
    padding: 2px 4px;
    border-radius: 2px;
    position: absolute;
    top: -8px;
    left: 8px;
    z-index: 0;
  }
}

// 编辑状态下的特殊样式
.timeSlotArea[data-editing='true'] {
  .timeAxisItem {
    &:hover {
      background-color: rgba(24, 144, 255, 0.05);

      .timeLabel {
        color: #1890ff;
        font-weight: 500;
      }
    }
  }
}

// 时间轴响应式调整
@media (max-width: 768px) {
  .timeAxisItem {
    padding-left: 4px;

    .timeLabel {
      font-size: 11px;
      left: 4px;
    }
  }

  .actions {
    padding: 0 4px;

    .ant-btn {
      height: 32px;
      font-size: 13px;
    }
  }
}
// 2025年01月23日 开山ai结尾共生成80行代码
