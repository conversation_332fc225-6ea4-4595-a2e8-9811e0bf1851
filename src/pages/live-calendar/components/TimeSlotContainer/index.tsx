// ai生成
import React, { useState, useCallback, useMemo } from 'react';
import moment from 'moment';
import TimeSlotItem, { TimeSlotData } from '../TimeSlotItem';
import { LIVE_TIME_LIST } from '../../utils/enum';
import styles from './index.module.less';
// 2025年1月2日 开山ai结尾共生成2行代码

interface TimeSlotContainerProps {
  timeSlots: TimeSlotData[];
  onUpdate?: (timeSlots: TimeSlotData[]) => void;
  isEditing?: boolean;
  containerHeight?: number; // 每个时间段的高度
}

const TimeSlotContainer: React.FC<TimeSlotContainerProps> = ({
  timeSlots = [],
  onUpdate,
  isEditing = false,
  containerHeight = 200,
}) => {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newItems, setNewItems] = useState<Set<string>>(new Set()); // 追踪新创建的项目

  // 计算总容器高度
  const totalHeight = useMemo(() => {
    return (LIVE_TIME_LIST.length - 1) * containerHeight;
  }, [containerHeight]);

  // 开始编辑某个时间段
  const handleEdit = useCallback(
    (id?: string) => {
      if (!isEditing) return;
      setEditingId(id || null);
    },
    [isEditing],
  );

  // 取消编辑
  const handleCancel = useCallback(() => {
    setEditingId(null);
  }, []);

  // ai生成
  // 实时保存单个时间段（不关闭编辑框）- 增强冲突检查
  const handleRealtimeSave = useCallback(
    (data: TimeSlotData) => {
      // 在这里也进行一次冲突检查，确保不保存有冲突的数据
      if (!data.timeList || data.timeList.length < 2) {
        return; // 时间段不完整，不保存
      }

      const [newStart, newEnd] = data.timeList;
      if (!newStart || !newEnd) {
        return; // 时间段不完整，不保存
      }

      // 检查与其他时间段的重叠
      const hasConflict = timeSlots.some((otherSlot) => {
        if (otherSlot.id === editingId) return false; // 跳过当前编辑的项目
        if (!otherSlot.timeList || otherSlot.timeList.length < 2) return false;

        const [existStart, existEnd] = otherSlot.timeList;
        if (!existStart || !existEnd) return false;

        const newStartMoment = moment(newStart, 'HH:mm');
        const newEndMoment = moment(newEnd, 'HH:mm');
        const existStartMoment = moment(existStart, 'HH:mm');
        const existEndMoment = moment(existEnd, 'HH:mm');

        // 处理跨天情况
        if (newEndMoment.isBefore(newStartMoment) || newEndMoment.isSame(newStartMoment)) {
          newEndMoment.add(1, 'day');
        }
        if (existEndMoment.isBefore(existStartMoment) || existEndMoment.isSame(existStartMoment)) {
          existEndMoment.add(1, 'day');
        }

        return newStartMoment.isBefore(existEndMoment) && newEndMoment.isAfter(existStartMoment);
      });

      // 只有没有冲突的情况下才保存
      if (!hasConflict) {
        const updatedTimeSlots = timeSlots.map((slot) =>
          slot.id === editingId ? { ...data, id: editingId } : slot,
        );

        if (onUpdate) {
          onUpdate(updatedTimeSlots);
        }
      }
      // 注意：不设置 setEditingId(null)，保持编辑状态
    },
    [timeSlots, editingId, onUpdate],
  );
  // 2025年1月2日 开山ai结尾共生成25行代码

  // ai生成
  // 保存单个时间段（关闭编辑框） - 保留以备将来使用
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSave = useCallback(
    (data: TimeSlotData) => {
      const updatedTimeSlots = timeSlots.map((slot) =>
        slot.id === editingId ? { ...data, id: editingId } : slot,
      );

      // 保存成功后，从新项目标记中移除
      if (editingId) {
        setNewItems((prev) => {
          const newSet = new Set(prev);
          newSet.delete(editingId);
          return newSet;
        });
      }

      if (onUpdate) {
        onUpdate(updatedTimeSlots);
      }

      setEditingId(null);
    },
    [timeSlots, editingId, onUpdate],
  );
  // 2025年1月2日 开山ai结尾共生成2行代码

  // 删除时间段
  const handleDelete = useCallback(
    (id: string) => {
      const updatedTimeSlots = timeSlots.filter((slot) => slot.id !== id);

      // 从新项目集合中移除
      setNewItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });

      if (onUpdate) {
        onUpdate(updatedTimeSlots);
      }

      if (editingId === id) {
        setEditingId(null);
      }
    },
    [timeSlots, editingId, onUpdate],
  );

  /**
   * 检查点击位置是否与现有时间段重叠
   */
  const isClickInExistingSlot = useCallback(
    (clickY: number) => {
      return timeSlots.some((slot) => {
        if (!slot.timeList || slot.timeList.length < 2) return false;

        const startTime = slot.timeList[0];
        const endTime = slot.timeList[1];

        // 计算时间段的像素位置
        const startIndex = LIVE_TIME_LIST.indexOf(startTime);
        const endIndex = LIVE_TIME_LIST.indexOf(endTime);

        if (startIndex === -1 || endIndex === -1) return false;

        const slotTop = startIndex * containerHeight;
        const slotBottom = endIndex * containerHeight;

        return clickY >= slotTop && clickY <= slotBottom;
      });
    },
    [timeSlots, containerHeight],
  );

  /**
   * 处理容器点击，添加新时间段
   * 在编辑模式下，点击空白区域添加时间段
   */
  const handleContainerClick = useCallback(
    (e: React.MouseEvent) => {
      if (!isEditing) return;
      if (editingId) return; // 正在编辑时不添加新的

      // 检查是否点击的是时间段本身的元素（通过事件目标检查）
      const target = e.target as HTMLElement;
      if (target.closest('.timeSlotItem, [class*="displayMode"]')) {
        return; // 如果点击的是时间段元素，不创建新的
      }

      const rect = (e.currentTarget as HTMLDivElement).getBoundingClientRect();
      const clickY = e.clientY - rect.top;

      // 检查点击位置是否与现有时间段重叠
      if (isClickInExistingSlot(clickY)) {
        return; // 如果点击位置有现有时间段，不创建新的
      }

      // 基于LIVE_TIME_LIST计算点击位置对应的时间段
      const timeIndex = Math.floor(clickY / containerHeight);

      if (timeIndex >= 0 && timeIndex < LIVE_TIME_LIST.length - 1) {
        const startTime = LIVE_TIME_LIST[timeIndex];
        let endTime = LIVE_TIME_LIST[timeIndex + 1];

        // ai生成
        // 特殊处理：04:00-06:00区域双击时，默认设置为04:00-05:59
        if (startTime === '04:00' && endTime === '06:00') {
          endTime = '05:59';
        }
        // 2025年1月2日 开山ai结尾共生成3行代码

        const newId = `timeslot_${Date.now()}`;
        const newTimeSlot: TimeSlotData = {
          id: newId,
          timeList: [startTime, endTime],
          content: '',
        };

        const updatedTimeSlots = [...timeSlots, newTimeSlot];

        // 标记为新项目
        setNewItems((prev) => new Set([...prev, newId]));

        if (onUpdate) {
          onUpdate(updatedTimeSlots);
        }

        // 立即编辑新添加的时间段
        setTimeout(() => {
          setEditingId(newId);
        }, 100);
      }
    },
    [timeSlots, onUpdate, isEditing, editingId, containerHeight, isClickInExistingSlot],
  );

  // ai生成
  // 渲染时间轴背景
  const renderTimeAxis = () => {
    return LIVE_TIME_LIST.slice(0, -1).map((_, index) => (
      <div
        key={index}
        className={styles.timeAxisItem}
        style={{
          position: 'absolute',
          top: index * containerHeight,
          height: containerHeight,
          left: 0,
          right: 0,
          borderTop: '1px solid #f0f0f0',
        }}
      >
        {/* <span className={styles.timeLabel}>{time}</span> */}
      </div>
    ));
  };

  return (
    <div className={styles.timeSlotContainer}>
      <div
        className={styles.timeSlotArea}
        style={{
          height: totalHeight,
          position: 'relative',
          cursor: isEditing && !editingId ? 'pointer' : 'default',
        }}
        onDoubleClick={handleContainerClick}
      >
        {/* 时间轴背景 */}
        {renderTimeAxis()}

        {/* 时间段项目 */}
        {/* ai生成 */}
        {timeSlots.map((slot) => (
          <TimeSlotItem
            key={slot.id}
            data={slot}
            isEditing={isEditing}
            isEditingThis={editingId === slot.id}
            onEdit={handleEdit}
            onSave={handleRealtimeSave}
            onCancel={handleCancel}
            onDelete={(id) => id && handleDelete(id)}
            allTimeSlots={timeSlots}
            containerHeight={containerHeight}
            isNewItem={newItems.has(slot.id || '')}
          />
        ))}
        {/* 2025年1月2日 开山ai结尾共生成4行代码 */}
      </div>
    </div>
  );
};

export default TimeSlotContainer;
// 2025年01月23日 开山ai结尾共生成214行代码
