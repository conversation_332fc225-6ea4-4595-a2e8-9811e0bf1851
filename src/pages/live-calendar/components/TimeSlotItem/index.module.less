// ai生成
.timeSlotItem {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #40a9ff;
  }

  &.editing {
    z-index: 1000 !important;
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    cursor: default;
  }

  &.conflict {
    border-color: #ff4d4f;
    background-color: #fff2f0;
  }
}

.displayMode {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 6px;
  position: relative;
  cursor: pointer;

  .timeDisplay {
    font-size: 12px;
    font-weight: 600;
    color: #1890ff;
    margin-bottom: 4px;
    text-align: left;
    line-height: 1.2;
  }

  .contentDisplay {
    font-size: 12px;
    color: #333;
    text-align: left;
    overflow-y: auto;
    text-overflow: ellipsis;
    // display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 1.3;
    flex: 1;
    white-space: pre-wrap; // 支持换行显示
    word-break: break-word;
  }

  .deleteBtn {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 77, 79, 0.1);
    border-radius: 2px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    z-index: 10;

    .anticon {
      font-size: 10px;
      color: #ff4d4f;
    }

    &:hover {
      background: rgba(255, 77, 79, 0.2);
      transform: scale(1.1);
    }
  }

  &:hover .deleteBtn {
    opacity: 1;
  }
}

.editMode {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .timeSelector {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;

    .timeSeparator {
      font-size: 14px;
      color: #999;
    }

    .conflictInput {
      border-color: #ff4d4f !important;

      &:focus {
        border-color: #ff4d4f !important;
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
      }
    }

    .conflictMessage {
      position: absolute;
      right: -120px;
      top: 50%;
      transform: translateY(-50%);
      color: #ff4d4f;
      font-size: 12px;
      white-space: nowrap;
      background: #fff;
      padding: 2px 6px;
      border: 1px solid #ff4d4f;
      border-radius: 4px;
      z-index: 10;

      &::before {
        content: '';
        position: absolute;
        left: -6px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-right: 6px solid #ff4d4f;
      }

      &::after {
        content: '';
        position: absolute;
        left: -5px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-bottom: 5px solid transparent;
        border-right: 5px solid #fff;
      }
    }
  }

  // 时间重叠提示容器
  .conflictMessageWrapper {
    margin-top: 4px;

    .conflictMessage {
      position: static;
      transform: none;
      color: #ff4d4f;
      font-size: 12px;
      background: #fff2f0;
      padding: 4px 8px;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      display: inline-block;
      max-width: 100%;
      word-wrap: break-word;

      &::before,
      &::after {
        display: none;
      }
    }
  }

  .contentInput {
    resize: none;
    overflow-y: auto;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    // 确保滚动条样式美观
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .editActions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .ant-btn {
      height: 24px;
      padding: 0 8px;
      font-size: 12px;
      line-height: 22px;
    }
  }
}

// 时间选择器样式调整
:global {
  .ant-time-picker {
    width: 80px;

    .ant-time-picker-input {
      font-size: 12px;
      padding: 2px 8px;
      height: 28px;
    }
  }

  // 编辑状态时的时间选择器下拉框层级调整
  .ant-time-picker-dropdown {
    z-index: 1050;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .timeSlotItem {
    padding: 6px;
  }

  .editMode {
    .timeSelector {
      flex-direction: column;
      align-items: stretch;
      gap: 4px;

      .timeSeparator {
        text-align: center;
        margin: 2px 0;
      }

      .conflictMessage {
        position: static;
        transform: none;
        margin-top: 4px;
        text-align: center;
      }
    }
  }

  .displayMode {
    .timeDisplay {
      font-size: 13px;
    }

    .contentDisplay {
      font-size: 11px;
    }
  }
}
// 2025年01月23日 开山ai结尾共生成276行代码
