// ai生成
import React, { useState, useCallback, useRef, useEffect } from 'react';
// ai生成
import { Input, message, Icon, Tooltip } from 'antd';
// 2025年1月2日 开山ai结尾共生成1行代码
import moment from 'moment';
import CustomTimePicker from '../CustomTimePicker';
import styles from './index.module.less';

export interface TimeSlotData {
  timeList: string[]; // [开始时间, 结束时间]
  content: string;
  id?: string;
}

/**
 * 时间工具函数
 */
const timeUtils = {
  /**
   * 转换时间为分钟数（从当天6:00开始计算）
   */
  timeToMinutes: (time: string): number => {
    const [hour, minute] = time.split(':').map(Number);
    let totalMinutes = hour * 60 + minute;

    // 如果时间小于6:00，认为是次日的时间
    if (hour < 6) {
      totalMinutes += 24 * 60;
    }

    // 减去起始时间6:00的分钟数
    return totalMinutes - 6 * 60;
  },

  /**
   * 格式化时间范围显示
   */
  formatTimeRange: (timeList: string[]): string => {
    if (!timeList || timeList.length < 2) return '';
    return `${timeList[0]}-${timeList[1]}`;
  },

  /**
   * 验证时间格式
   */
  validateTimeFormat: (time: string): boolean => {
    const [hour, minute] = time.split(':').map(Number);
    return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
  },
};

/**
 * 内容渲染工具函数
 */
const contentUtils = {
  /**
   * 将换行符转换为 JSX 元素进行渲染
   */
  renderContentWithLineBreaks: (content: string) => {
    if (!content) return null;

    return content.split('\n').map((line, index, array) => (
      <React.Fragment key={index}>
        {line}
        {index < array.length - 1 && <br />}
      </React.Fragment>
    ));
  },

  /**
   * 为 Tooltip 格式化内容（保留换行符用于样式处理）
   */
  formatContentForTooltip: (content: string): string => {
    if (!content) return '';
    return content;
  },
};

interface TimeSlotItemProps {
  data: TimeSlotData;
  isEditing?: boolean;
  isEditingThis?: boolean;
  onEdit?: (id?: string) => void;
  onSave?: (data: TimeSlotData) => void;
  onCancel?: () => void;
  onDelete?: (id?: string) => void; // 新增删除回调
  allTimeSlots?: TimeSlotData[]; // 用于检测时间重叠
  containerHeight?: number; // 容器高度，用于计算位置
  isNewItem?: boolean; // 标识是否为新创建的项目
}

const TimeSlotItem: React.FC<TimeSlotItemProps> = ({
  data,
  isEditing = false,
  isEditingThis = false,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  allTimeSlots = [],
  containerHeight = 200, // 每个时间段默认200px
  isNewItem = false,
}) => {
  const [editData, setEditData] = useState<TimeSlotData>(data);
  const [hasTimeConflict, setHasTimeConflict] = useState(false);
  // ai生成
  const [conflictMessage, setConflictMessage] = useState('');
  const [originalData, setOriginalData] = useState<TimeSlotData>(data); // 保存原始数据用于比较
  const inputRef = useRef<React.ComponentRef<typeof Input.TextArea>>(null);
  const itemRef = useRef<HTMLDivElement>(null); // 用于检测外部点击
  // 2025年1月2日 开山ai结尾共生成2行代码

  // 当data变化时，更新编辑数据和原始数据
  useEffect(() => {
    setEditData(data);
    setOriginalData(data);
    setHasTimeConflict(false);
    setConflictMessage('');
  }, [data]);

  // 当开始编辑时，重新初始化编辑数据
  useEffect(() => {
    if (isEditingThis) {
      // 确保content字段有值，避免undefined导致的受控组件问题
      const newEditData = {
        ...data,
        content: data.content || '',
        timeList: data.timeList || ['', ''],
      };
      setEditData(newEditData);
      setHasTimeConflict(false);
      setConflictMessage('');
    }
  }, [isEditingThis, data]);

  /**
   * 计算时间段在时间轴中的位置
   * 基于6:00开始的24小时制，支持跨天时间段
   */
  const calculatePosition = useCallback(
    (timeList: string[]) => {
      if (!timeList || timeList.length < 2) return { top: 0, height: 0 };

      const [startTime, endTime] = timeList;
      const startMinutes = timeUtils.timeToMinutes(startTime);
      const endMinutes = timeUtils.timeToMinutes(endTime);

      // 计算像素位置（每小时对应的像素高度）
      const pixelsPerHour = containerHeight / 2; // 每2小时一个时间段
      const top = (startMinutes / 60) * pixelsPerHour;
      const height = ((endMinutes - startMinutes) / 60) * pixelsPerHour;

      return { top, height: Math.max(height, 40) }; // 最小高度40px
    },
    [containerHeight],
  );

  // ai生成
  /**
   * 检查时间重叠和验证 - 用于标红提示和阻止保存
   */
  const checkTimeConflict = useCallback(
    (newTimeList: string[]) => {
      if (!newTimeList || newTimeList.length < 2) {
        setConflictMessage('时间段不完整');
        return true;
      }

      const [newStart, newEnd] = newTimeList;

      // 验证时间格式
      // if (!timeUtils.validateTimeFormat(newStart) || !timeUtils.validateTimeFormat(newEnd)) {
      //   setConflictMessage('时间格式无效');
      //   return true;
      // }

      // 验证开始时间不能等于结束时间
      if (newStart === newEnd) {
        setConflictMessage('开始时间不能等于结束时间');
        return true;
      }

      const newStartMoment = moment(newStart, 'HH:mm');
      const newEndMoment = moment(newEnd, 'HH:mm');

      // 处理跨天情况
      if (newEndMoment.isBefore(newStartMoment) || newEndMoment.isSame(newStartMoment)) {
        newEndMoment.add(1, 'day');
      }

      // 检查与其他时间段的重叠
      for (const slot of allTimeSlots) {
        if (slot === data) continue; // 跳过当前正在编辑的项目

        if (slot.timeList && slot.timeList.length >= 2) {
          const [existStart, existEnd] = slot.timeList;
          const existStartMoment = moment(existStart, 'HH:mm');
          const existEndMoment = moment(existEnd, 'HH:mm');

          // 处理跨天情况
          if (
            existEndMoment.isBefore(existStartMoment) ||
            existEndMoment.isSame(existStartMoment)
          ) {
            existEndMoment.add(1, 'day');
          }

          // 检查是否有重叠
          const hasOverlap =
            newStartMoment.isBefore(existEndMoment) && newEndMoment.isAfter(existStartMoment);

          if (hasOverlap) {
            setConflictMessage(
              `与时间段 ${timeUtils.formatTimeRange([existStart, existEnd])} 重叠`,
            );
            return true;
          }
        }
      }

      setConflictMessage('');
      return false;
    },
    [allTimeSlots, data],
  );

  // 检查编辑状态下的初始时间重叠
  useEffect(() => {
    if (isEditingThis && editData.timeList && editData.timeList.length >= 2) {
      const hasConflict = checkTimeConflict(editData.timeList);
      setHasTimeConflict(hasConflict);
    }
  }, [isEditingThis, editData.timeList, checkTimeConflict]);

  /**
   * 禁用小时逻辑 - 针对自定义时间选择器
   * 时间范围：06:00-05:59（次日）
   */
  const getDisabledHours = useCallback(
    (timeType: 'start' | 'end') => {
      const disabledHours: number[] = [];

      if (timeType === 'start') {
        // 开始时间的限制
        if (editData.timeList[1]) {
          const endTime = moment(editData.timeList[1], 'HH:mm');
          const endHour = endTime.hour();

          if (endHour >= 6) {
            // 结束时间是当天（6-23点），开始时间不能晚于结束时间
            for (let i = endHour + 1; i <= 23; i++) {
              disabledHours.push(i);
            }
            // 禁用次日0-5点，因为结束时间是当天
            for (let i = 0; i <= 5; i++) {
              disabledHours.push(i);
            }
          } else {
            // 结束时间是次日（0-5点），开始时间可以是6-23点，或者0-结束小时之前的时间
            for (let i = endHour + 1; i <= 5; i++) {
              disabledHours.push(i);
            }
          }
        }
      } else if (timeType === 'end') {
        // 结束时间的限制
        if (editData.timeList[0]) {
          const startTime = moment(editData.timeList[0], 'HH:mm');
          const startHour = startTime.hour();

          if (startHour >= 6) {
            // 开始时间是当天（6-23点），结束时间可以是当天开始时间之后，或次日0-5点
            for (let i = 6; i < startHour; i++) {
              disabledHours.push(i); // 禁用开始时间之前的当天时间
            }
          } else {
            // ai生成
            // 开始时间是次日（0-5点），结束时间只能是次日开始时间之后的时间
            // 业务规则：开始时间为24:00(00:00)或之后时，结束时间最多选择到05:59
            for (let i = 6; i <= 23; i++) {
              disabledHours.push(i); // 禁用当天6-23点，确保结束时间最多到05:59
            }
            for (let i = 0; i < startHour; i++) {
              disabledHours.push(i); // 禁用开始时间之前的次日时间
            }
            // 2025年1月2日 开山ai结尾共生成5行代码
          }
        }
      }

      return disabledHours;
    },
    [editData.timeList],
  );

  /**
   * 禁用分钟逻辑
   */
  const getDisabledMinutes = useCallback(
    (selectedHour: number, timeType: 'start' | 'end') => {
      const disabledMinutes: number[] = [];

      if (timeType === 'start' && editData.timeList[1]) {
        const endTime = moment(editData.timeList[1], 'HH:mm');
        const endHour = endTime.hour();
        const endMinute = endTime.minute();

        // 同一小时时，开始时间的分钟不能大于等于结束时间的分钟
        if (selectedHour === endHour) {
          for (let i = endMinute; i <= 59; i++) {
            disabledMinutes.push(i);
          }
        }
      } else if (timeType === 'end' && editData.timeList[0]) {
        const startTime = moment(editData.timeList[0], 'HH:mm');
        const startHour = startTime.hour();
        const startMinute = startTime.minute();

        // 同一小时时，结束时间的分钟不能小于等于开始时间的分钟
        if (selectedHour === startHour) {
          for (let i = 0; i <= startMinute; i++) {
            disabledMinutes.push(i);
          }
        }
      }

      return disabledMinutes;
    },
    [editData.timeList],
  );

  // ai生成
  /**
   * 处理时间选择变化 - 实时保存不关闭编辑框
   */
  const handleTimeChange = useCallback(
    (timeType: 'start' | 'end', time: moment.Moment | null) => {
      if (!time) return;

      const timeString = time.format('HH:mm');
      const newTimeList = [...editData.timeList];

      if (timeType === 'start') {
        newTimeList[0] = timeString;
      } else {
        newTimeList[1] = timeString;
      }

      const newEditData = { ...editData, timeList: newTimeList };
      setEditData(newEditData);

      // 检查时间重叠
      const hasConflict = checkTimeConflict(newTimeList);
      setHasTimeConflict(hasConflict);

      // ai生成
      // 如果没有冲突，实时保存时间
      if (!hasConflict && onSave) {
        onSave(newEditData);
      }
      // 2025年1月2日 开山ai结尾共生成2行代码
    },
    [editData, checkTimeConflict, onSave],
  );
  // 2025年1月2日 开山ai结尾共生成13行代码

  // ai生成
  // 处理内容变化 - 有冲突时不保存
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    const newEditData = {
      ...editData,
      content: newContent,
    };
    setEditData(newEditData);

    // 只有在没有时间冲突的情况下才实时保存内容
    if (!hasTimeConflict && onSave) {
      onSave(newEditData);
    }
  };
  // 2025年1月2日 开山ai结尾共生成8行代码

  // ai生成
  // 保存数据 - 保留以备将来使用
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSave = () => {
    if (hasTimeConflict) {
      message.error('存在时间冲突，无法保存');
      return;
    }

    if (!editData.timeList[0] || !editData.timeList[1]) {
      message.error('请选择完整的时间段');
      return;
    }

    if (onSave) {
      onSave(editData);
    }
  };
  // 2025年1月2日 开山ai结尾共生成2行代码

  // 检查数据是否有变化
  const hasDataChanged = useCallback(() => {
    return (
      editData.content !== originalData.content ||
      editData.timeList[0] !== originalData.timeList[0] ||
      editData.timeList[1] !== originalData.timeList[1]
    );
  }, [editData, originalData]);

  // ai生成
  // 取消编辑 - 保留以备将来使用
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleCancel = () => {
    // 如果是新创建的项目且没有做任何修改，则删除该项目
    if (isNewItem && !hasDataChanged()) {
      if (onDelete && data.id) {
        onDelete(data.id);
        return;
      }
    }

    // 否则恢复到原始数据
    setEditData(originalData);
    setHasTimeConflict(false);
    setConflictMessage('');
    if (onCancel) {
      onCancel();
    }
  };
  // 2025年1月2日 开山ai结尾共生成2行代码

  // 开始编辑
  const handleEdit = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation(); // 阻止事件冒泡到容器
    }
    if (onEdit) {
      onEdit(data.id);
    }
  };

  // 计算当前位置
  const position = calculatePosition(isEditingThis ? editData.timeList : data.timeList);

  // ai生成
  // 编辑状态时聚焦输入框
  useEffect(() => {
    if (isEditingThis && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditingThis]);

  // ai生成
  // 点击外部关闭编辑框 - 有冲突时删除卡片
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isEditingThis && itemRef.current && !itemRef.current.contains(event.target as Node)) {
        // 如果有时间冲突，删除这个卡片
        if (hasTimeConflict) {
          if (onDelete && data.id) {
            onDelete(data.id);
            return;
          }
        }

        // 否则正常关闭编辑框
        if (onCancel) {
          onCancel();
        }
      }
    };

    if (isEditingThis) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isEditingThis, onCancel, hasTimeConflict, onDelete, data.id]);
  // 2025年1月2日 开山ai结尾共生成12行代码

  return (
    // ai生成
    <div
      ref={itemRef}
      className={`${styles.timeSlotItem} ${isEditingThis ? styles.editing : ''} ${
        hasTimeConflict ? styles.conflict : ''
      }`}
      style={{
        position: 'absolute',
        top: position.top,
        height: Math.max(position.height, 60), // 最小高度60px
        left: 0,
        right: 0,
        zIndex: isEditingThis ? 1000 : 1,
      }}
    >
      {/* 2025年1月2日 开山ai结尾共生成4行代码 */}
      {isEditingThis ? (
        // 编辑模式
        <div className={styles.editMode}>
          <div className={styles.timeSelector}>
            {/* ai生成 */}
            <CustomTimePicker
              value={editData.timeList[0] ? moment(editData.timeList[0], 'HH:mm') : undefined}
              onChange={(time) => handleTimeChange('start', time)}
              placeholder="开始时间"
              className={hasTimeConflict ? styles.conflictInput : ''}
              disabledHours={() => getDisabledHours('start')}
              disabledMinutes={(selectedHour) => getDisabledMinutes(selectedHour, 'start')}
            />
            {/* 2025年1月2日 开山ai结尾共生成4行代码 */}
            <span className={styles.timeSeparator}>-</span>
            {/* ai生成 */}
            <CustomTimePicker
              value={editData.timeList[1] ? moment(editData.timeList[1], 'HH:mm') : undefined}
              onChange={(time) => handleTimeChange('end', time)}
              placeholder="结束时间"
              className={hasTimeConflict ? styles.conflictInput : ''}
              disabledHours={() => getDisabledHours('end')}
              disabledMinutes={(selectedHour) => getDisabledMinutes(selectedHour, 'end')}
            />
            {/* 2025年1月2日 开山ai结尾共生成4行代码 */}
          </div>
          {hasTimeConflict && (
            <div className={styles.conflictMessageWrapper}>
              <span className={styles.conflictMessage}>{conflictMessage}</span>
            </div>
          )}
          <Input.TextArea
            ref={inputRef}
            value={editData.content}
            onChange={handleContentChange}
            placeholder="请输入内容，支持换行"
            className={styles.contentInput}
            style={{
              height: Math.max(position.height - 80, 50),
              maxHeight: Math.max(position.height - 80, 50),
            }}
            autoSize={false}
            maxLength={3000}
          />
          {/* ai生成 - 移除保存取消按钮，改为实时保存 */}
          {/* 2025年1月2日 开山ai结尾共生成1行代码 */}
        </div>
      ) : (
        // 显示模式 - 双击打开编辑框
        <div className={styles.displayMode} onDoubleClick={isEditing ? handleEdit : undefined}>
          {/* 删除按钮 */}
          {isEditing && (
            <Tooltip title="删除">
              <div
                className={styles.deleteBtn}
                onClick={(e) => {
                  e.stopPropagation();
                  if (onDelete && data.id) {
                    onDelete(data.id);
                  }
                }}
              >
                <Icon type="close" />
              </div>
            </Tooltip>
          )}

          {/* 时间显示 */}
          <div className={styles.timeDisplay}>{timeUtils.formatTimeRange(data.timeList)}</div>

          {/* 内容显示 */}
          <Tooltip
            title={
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {contentUtils.formatContentForTooltip(data.content || '')}
              </div>
            }
            placement="left"
          >
            <div className={styles.contentDisplay}>
              {contentUtils.renderContentWithLineBreaks(data.content || '')}
            </div>
          </Tooltip>
        </div>
      )}
    </div>
  );
};

export default TimeSlotItem;
// 2025年01月23日 开山ai结尾共生成523行代码
