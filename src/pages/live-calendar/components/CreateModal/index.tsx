import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.module.less';
import Table, { ColumnProps } from 'antd/lib/table';
import { Icon, Input, InputNumber, message, Popconfirm } from 'antd';
import { editByCalendar, LiveCalendarListResult } from '../../services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { projectGroupList, ProjectGroupListResult } from '@/services/yml/live-room-manage';
import PopoverRowText from '@/components/PopoverRowText';

type PropsType = {
  info: LiveCalendarListResult[number];
  search: (params?: {
    firstDayOfMonth?: string;
    lastDayOfMonth?: string;
    yearMonth?: string;
  }) => void;
  projectGroups: ProjectGroupListResult['records'];
};
type DataSourceType = {
  projectGroup?: string;
  talkNum?: string;
  linkNum?: string;
  allNum?: string;
}[];
const COLUMNS = (onchange: (key: 'talkNum' | 'linkNum', value: string, index: number) => void) =>
  [
    {
      title: '项目组',
      dataIndex: 'projectGroup',
      key: 'projectGroup',
      width: 100,
      render: (val: string) => {
        return val ? <PopoverRowText text={val} /> : '-';
      },
    },
    {
      title: '讲解数量',
      dataIndex: 'talkNum',
      key: 'talkNum',
      width: 120,
      render: (val: number, record, index) => {
        return record.projectGroup !== '服务类型汇总' ? (
          <InputNumber
            value={val}
            max={999999}
            min={0}
            onChange={(e) => {
              onchange('talkNum', e?.toString() ?? '', index);
            }}
          />
        ) : (
          <p>{val}</p>
        );
      },
    },
    {
      title: '挂链数量',
      dataIndex: 'linkNum',
      key: 'linkNum',
      width: 120,
      render: (val: number, record, index) => {
        return record.projectGroup !== '服务类型汇总' ? (
          <InputNumber
            value={val}
            max={999999}
            min={0}
            onChange={(e) => onchange('linkNum', e?.toString() ?? '', index)}
          />
        ) : (
          <p>{val}</p>
        );
      },
    },
    {
      title: '类目汇总',
      dataIndex: 'allNum',
      key: 'allNum',
      width: 100,
      render: (val: string) => {
        return <p>{val ?? 0}</p>;
      },
    },
  ] as ColumnProps<DataSourceType[number]>[];
const CreateModal: React.FC<PropsType> = ({ info, search, projectGroups }) => {
  const [dataSource, setDataSource] = useState<DataSourceType>([]);
  const [loading, setLoading] = useState(false);

  const initDataSource = async () => {
    setLoading(true);
    const dataSource = projectGroups?.map((item) => ({
      projectGroup: item.projectGroupName,
      talkNum: info?.liveCalendarDetails?.find(
        (detail) => detail.projectGroup === item.projectGroupName,
      )?.talkNum,
      linkNum: info?.liveCalendarDetails?.find(
        (detail) => detail.projectGroup === item.projectGroupName,
      )?.linkNum,
      allNum: info?.liveCalendarDetails?.find(
        (detail) => detail.projectGroup === item.projectGroupName,
      )?.allNum,
    }));
    setDataSource(dataSource ?? []);
    setLoading(false);
  };
  useEffect(() => {
    initDataSource();
  }, [info, projectGroups]);
  const handleChange = (key: 'talkNum' | 'linkNum', value: string, index: number) => {
    const floorNumber = isNaN(Number(value ?? 0))
      ? Number(dataSource[index][key])
      : Math.floor(Number(value));
    const num = floorNumber < 0 ? 0 : floorNumber > 999999 ? 999999 : floorNumber;
    const newDataSource = [...dataSource];
    newDataSource[index][key] = num.toString();
    newDataSource[index]['allNum'] = (
      Number(value ?? 0) +
      Number(newDataSource[index][key === 'linkNum' ? 'talkNum' : 'linkNum'] ?? 0)
    ).toString();
    setDataSource(newDataSource);
  };
  const list = useMemo(
    () => [
      ...dataSource,
      {
        projectGroup: '服务类型汇总',
        talkNum: dataSource.reduce((acc, curr) => acc + Number(curr.talkNum ?? 0), 0)?.toString(),
        linkNum: dataSource.reduce((acc, curr) => acc + Number(curr.linkNum ?? 0), 0)?.toString(),
        allNum: dataSource.reduce((acc, curr) => acc + Number(curr.allNum ?? 0), 0)?.toString(),
      },
    ],
    [dataSource],
  );
  const confirm = async () => {
    const result = await responseWithResultAsync({
      request: editByCalendar,
      params: {
        id: info?.id,
        roundDesc: info?.roundDesc,
        liveCalendarDetails: dataSource,
        talkNum: list?.reduce((acc, curr) => acc + Number(curr.talkNum ?? 0), 0)?.toString(),
        linkNum: list?.reduce((acc, curr) => acc + Number(curr.linkNum ?? 0), 0)?.toString(),
      },
    });
    console.log({
      id: info?.id,
      roundDesc: info?.roundDesc,
      liveCalendarDetails: dataSource,
      talkNum: list?.reduce((acc, curr) => acc + Number(curr.talkNum ?? 0), 0)?.toString(),
      linkNum: list?.reduce((acc, curr) => acc + Number(curr.linkNum ?? 0), 0)?.toString(),
    });
    if (result) {
      message.success('操作成功');
      search();
    }
  };
  return (
    <Popconfirm
      placement="bottomRight"
      title={
        <section className={styles['create-modal-box']}>
          <section className={styles['create-modal-top']}>
            <article className={styles['create-modal-top-item']}>
              <div className={styles['icon']}></div>
              <p style={{ marginRight: '4px', whiteSpace: 'nowrap', width: '70px' }}>主题</p>
              {info?.subject ? <PopoverRowText text={info?.subject} /> : '-'}
            </article>
            <article className={styles['create-modal-top-item']}>
              <div className={styles['icon']}></div>
              <p style={{ marginRight: '4px', whiteSpace: 'nowrap', width: '70px' }}>货盘简述</p>
              {info?.roundDesc ? <PopoverRowText text={info?.roundDesc} /> : '-'}
            </article>
            <article className={styles['create-modal-top-item']}>
              <div className={styles['icon']}></div>
              <p style={{ marginRight: '4px', whiteSpace: 'nowrap', width: '70px' }}>备注</p>
              {info?.remark ? <PopoverRowText text={info?.remark} /> : '-'}
            </article>
          </section>
          <article className={styles['create-modal-content']}>
            <div className={styles['create-modal-content-title']}>
              <div className={styles['icon']}></div>
              <p>坑位数量</p>
            </div>
          </article>
          <Table
            columns={COLUMNS(handleChange)}
            dataSource={list}
            loading={loading}
            pagination={false}
            size="small"
            scroll={{ y: 300, x: '100%' }}
          />
        </section>
      }
      onConfirm={confirm}
      okText="确定"
      cancelText="取消"
      icon={false}
    >
      <div
        className="calendar-body-card-plus"
        onClick={() => {
          //
        }}
      >
        <Icon type="plus-circle" theme="filled" />
      </div>
    </Popconfirm>
  );
};

export default CreateModal;
