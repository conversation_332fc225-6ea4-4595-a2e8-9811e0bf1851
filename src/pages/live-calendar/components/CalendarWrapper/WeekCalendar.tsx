import React, { useState, useEffect } from 'react';
import moment from 'moment';
import styles from './WeekCalendar.module.less';
import { formatDay } from '@/common/common';
import { Icon, DatePicker, Button, Spin } from 'antd';
import { LiveCalendarListResult } from '../../services';
import PopoverRowText from '@/components/PopoverRowText';
import { ProjectGroupListResult } from '@/services/yml/live-room-manage';
import SwitchWeekMonth from './SwitchWeekMonth';
import Table from 'antd/lib/table';
import { DataSourceType, useBatchEdit } from '../../utils/useBatchEdit';
import { LIVE_TIME_LIST } from '../../utils/enum';
import TimeSlotContainer from '../TimeSlotContainer';
import { TimeSlotData } from '../TimeSlotItem';

// ai生成
// 扩展 LiveCalendarListResult 的单个项目类型
interface ExtendedCalendarItem {
  id?: string;
  linkNum?: string;
  liveCalendarDetails?: Array<{ [key: string]: string }>;
  liveDate?: string;
  roundDesc?: string;
  subject?: string;
  talkNum?: string;
  // 扩展字段
  liveType?: string;
  trafficSource?: string;
  remark?: string;
  explainNum?: string;
  selfSellNum?: string;
  foodGroupSlots?: string;
  foodGroupLinks?: string;
  foodGroupTotal?: string;
  lifeGroupSlots?: string;
  lifeGroupLinks?: string;
  lifeGroupTotal?: string;
  clothingGroupSlots?: string;
  clothingGroupLinks?: string;
  clothingGroupTotal?: string;
  beautyGroupSlots?: string;
  beautyGroupLinks?: string;
  beautyGroupTotal?: string;
  totalSlots?: string;
  totalLinks?: string;
  grandTotal?: string;
  roundTag?: string;
}
// 2024年05月21日 开山ai结尾共生成29行代码

interface WeekCalendarProps {
  selectedDate?: moment.Moment;
  setYearMonth: (value: string) => void;
  datalist: LiveCalendarListResult;
  search: (params?: {
    firstDayOfMonth?: string;
    lastDayOfMonth?: string;
    yearMonth?: string;
  }) => void;
  projectGroups: ProjectGroupListResult['records'];
  viewType?: 'week' | 'month';
  onViewChange?: (type: 'week' | 'month') => void;
  onDateChange?: (date: moment.Moment) => void;
}

const LeftItem = ({
  title,
  marginTop,
  paddingBottom,
}: {
  title: string;
  marginTop?: number;
  paddingBottom?: number;
}) => {
  return (
    <article
      className={styles['create-modal-top-item']}
      style={{ marginTop: marginTop, paddingBottom: paddingBottom }}
    >
      <div className={styles['icon']}></div>
      <p style={{ marginRight: '4px', whiteSpace: 'nowrap', width: '70px' }}>{title}</p>
    </article>
  );
};

const WeekCalendar: React.FC<WeekCalendarProps> = ({
  selectedDate,
  setYearMonth,
  datalist,
  search,
  projectGroups,
  onViewChange,
  onDateChange,
}) => {
  const [currentWeek, setCurrentWeek] = useState<moment.Moment | null>(selectedDate || moment());
  const [selected, setSelected] = useState<moment.Moment | null>(selectedDate || moment());

  const {
    editLoading,
    isEditing,
    setIsEditing,
    tableData,
    setTableData,
    COLUMNS,
    batchEdit,
    specialSchedule,
    setSpecialSchedule,
  } = useBatchEdit();

  const { WeekPicker } = DatePicker;

  const handleEdit = () => {
    setIsEditing(true);
  };

  // ai生成
  const handleSave = () => {
    setIsEditing(false);
    // TODO: 这里添加保存数据的逻辑

    batchEdit({
      dataList: datalist?.map((item) => ({
        ...item,
        liveCalendarDetails: tableData[item.id as string]?.map((data) => ({
          ...data,
          allNum: (Number(data.linkNum ?? 0) + Number(data.talkNum ?? 0)).toString(),
        })),
        talkNum:
          tableData?.[item.id as string]
            ?.reduce((acc, curr) => acc + Number(curr.talkNum ?? 0), 0)
            ?.toString() ?? '0',
        linkNum:
          tableData?.[item.id as string]
            ?.reduce((acc, curr) => acc + Number(curr.linkNum ?? 0), 0)
            ?.toString() ?? '0',
        // 添加专场排期数据，转换为 JSON 字符串格式
        specialSchedule: JSON.stringify(
          specialSchedule?.[item.id as string]?.map((slot: TimeSlotData) => ({
            timeList: slot.timeList,
            content: slot.content,
          })) || [],
        ),
      })),
    });
  };
  // 2025年01月23日 开山ai结尾共生成30行代码

  useEffect(() => {
    const newTableDataArr = datalist?.reduce((acc: { [idKey: string]: DataSourceType[] }, item) => {
      const id = item.id;
      const list = projectGroups?.map((group) => ({
        key: group.projectGroupId,
        projectGroup: group.projectGroupName,
        talkNum: item.liveCalendarDetails?.find(
          (detail) => detail.projectGroup === group.projectGroupName,
        )?.talkNum,
        linkNum: item.liveCalendarDetails?.find(
          (detail) => detail.projectGroup === group.projectGroupName,
        )?.linkNum,
        allNum: item.liveCalendarDetails?.find(
          (detail) => detail.projectGroup === group.projectGroupName,
        )?.allNum,
      }));
      if (id) {
        acc[id] = list ?? [];
      }
      return acc;
    }, {});
    setTableData(newTableDataArr);
    setSpecialSchedule(
      datalist?.reduce(
        (acc: { [idKey: string]: { timeList: string[]; content: string }[] }, item) => {
          acc[item.id as string] = JSON.parse(item.specialSchedule ?? '[]')?.map(
            (specialScheduleData: { timeList: string[]; content: string }) => ({
              timeList: specialScheduleData?.timeList,
              content: specialScheduleData?.content,
            }),
          );
          return acc;
        },
        {},
      ),
    );
  }, [projectGroups, datalist]);

  // 切换到上一周
  const handlePrevWeek = () => {
    const newDate = currentWeek?.clone()?.subtract?.(1, 'week') ?? null;
    setCurrentWeek(newDate);
    if (newDate && onDateChange) {
      onDateChange(newDate);
    }
  };

  // 切换到下一周
  const handleNextWeek = () => {
    const newDate = currentWeek?.clone()?.add?.(1, 'week') ?? null;
    setCurrentWeek(newDate);
    if (newDate && onDateChange) {
      onDateChange(newDate);
    }
  };

  // 日期选择器变化处理
  const onChange = (date: moment.Moment | null) => {
    setCurrentWeek(date);
    if (date && onDateChange) {
      onDateChange(date);
    }
  };

  // 处理视图切换
  const handleViewTypeChange = (type: 'week' | 'month') => {
    if (onViewChange) {
      onViewChange(type);
    }
  };

  // 当selectedDate从外部更新时，同步更新内部状态
  useEffect(() => {
    if (selectedDate) {
      setCurrentWeek(selectedDate);
      setSelected(selectedDate);
    }
  }, [selectedDate]);

  useEffect(() => {
    if (setYearMonth && currentWeek) {
      // 获取当前周的起始日期和结束日期
      const startOfWeek = moment(currentWeek).startOf('week');
      const endOfWeek = moment(currentWeek).endOf('week');
      // 根据需求，可能需要调整为以周一为一周的开始

      // 格式化日期范围，用于显示和数据请求
      const yearMonth = moment(currentWeek).format('YYYY-MM');
      setYearMonth(yearMonth);

      search({
        firstDayOfMonth: startOfWeek.format('YYYY-MM-DD'),
        lastDayOfMonth: endOfWeek.format('YYYY-MM-DD'),
      });
    }
  }, [currentWeek]);

  // 渲染星期标题
  const renderDaysOfWeek = () => {
    const daysOfWeek = ['一', '二', '三', '四', '五', '六', '日'];
    return daysOfWeek.map((day, index) => (
      <div className={styles['calendar-header-card']} key={index}>
        {day}
      </div>
    ));
  };

  // 渲染当前周的日期
  const renderWeekDates = () => {
    // 获取当前周的起始日（周一）和结束日（周日）
    const startOfWeek = moment(currentWeek).startOf('isoWeek');

    const days = [];

    // 生成周一到周日的日期
    for (let i = 0; i < 7; i++) {
      const date = startOfWeek.clone().add(i, 'days');
      const dayOfMonth = date.date();

      // 获取当前日期是否有数据
      const data = datalist.filter(
        (item) => moment(date).format('YYYY-MM-DD') === moment(item.liveDate).format('YYYY-MM-DD'),
      )?.[0] as ExtendedCalendarItem; // ai生成 - 使用扩展类型

      const isSelected = date.isSame(selected, 'day');
      const idTableData = tableData[data?.id as string] ?? [];
      const tableDataList = [
        ...idTableData,
        {
          key: 'allNum',
          projectGroup: '汇总',
          talkNum: idTableData
            ?.reduce((acc, item) => acc + Number(item.talkNum ?? 0), 0)
            .toString(),
          linkNum: idTableData
            ?.reduce((acc, item) => acc + Number(item.linkNum ?? 0), 0)
            .toString(),
          allNum: idTableData?.reduce((acc, item) => acc + Number(item.allNum ?? 0), 0).toString(),
        },
      ];

      days.push(
        <div
          key={i}
          // onClick={() => handleDateClick(date)}
          className={`${isSelected ? styles.selected : ''} ${styles['calendar-body-card']} ${
            styles['week-view']
          }`}
          style={{
            backgroundColor: 'white',
            borderRadius: '8px',
          }}
        >
          <section>
            {/* ai生成 */}
            <div className={styles['calendar-date-header']}>
              <div className={styles['calendar-date-number']}>{formatDay(dayOfMonth)}</div>
            </div>
            {data && (
              <div className={styles['calendar-content']}>
                <div className={styles['calendar-content-item']}>
                  {data?.roundTag ? <PopoverRowText text={data?.roundTag} /> : '-'}
                </div>
                <div className={styles['calendar-content-item']}>
                  {data?.subject ? <PopoverRowText text={data?.subject} /> : '-'}
                </div>
                <div className={styles['calendar-content-item']}>
                  {data?.roundDesc ? <PopoverRowText text={data?.roundDesc} /> : '-'}
                </div>
                <div className={styles['calendar-content-item']}>
                  {data?.remark ? <PopoverRowText text={data?.remark} /> : '-'}
                </div>
                <Table
                  columns={COLUMNS(data?.id as string)}
                  dataSource={tableDataList}
                  pagination={false}
                  size="small"
                  scroll={{ x: '100%' }}
                  style={{ marginBottom: '38px' }}
                />
                {/* ai生成 */}
                <article className={styles['date-time-box']}>
                  <TimeSlotContainer
                    timeSlots={(specialSchedule?.[data?.id as string] || []).map(
                      (
                        item: { timeList: string[]; content: string; id?: string },
                        index: number,
                      ) => ({
                        ...item,
                        id: item.id || `slot_${data?.id}_${index}`,
                      }),
                    )}
                    onUpdate={(updatedTimeSlots) => {
                      const newSpecialSchedule = {
                        ...specialSchedule,
                        [data?.id as string]: updatedTimeSlots,
                      };
                      setSpecialSchedule(newSpecialSchedule);
                    }}
                    isEditing={isEditing}
                    containerHeight={200}
                  />
                </article>
                {/* 2025年01月23日 开山ai结尾共生成16行代码 */}
              </div>
            )}
          </section>
          {/* 2024年05月21日 开山ai结尾共生成89行代码 */}
        </div>,
      );
    }

    return <div className={styles['calendar-body-line']}>{days}</div>;
  };

  // 格式化当前周的显示
  const formatWeekRange = () => {
    const startOfWeek = moment(currentWeek).startOf('isoWeek');
    const endOfWeek = moment(currentWeek).endOf('isoWeek');

    const startMonth = startOfWeek.format('MM');
    const endMonth = endOfWeek.format('MM');

    if (startMonth === endMonth) {
      // 如果同一个月，只显示一次月份
      return `${startOfWeek.format('YYYY年MM月DD日')} - ${endOfWeek.format('DD日')}`;
    } else {
      // 不同月份，显示完整日期
      return `${startOfWeek.format('YYYY年MM月DD日')} - ${endOfWeek.format('MM月DD日')}`;
    }
  };

  return (
    <div className={styles['calendar-customer']}>
      <div className={styles['calendar-header']}>
        <div className={styles['calendar-header-box']}>
          <div className={styles['calendar-header-title']}>
            <Icon
              type="caret-left"
              className={styles['calendar-header-title-icon']}
              onClick={handlePrevWeek}
            />
            <div className={styles['calendar-header-title-box']}>
              <div className={styles['calendar-header-title-text']}>{formatWeekRange()}</div>
              <WeekPicker
                className={styles['calendar-header-title-month']}
                onChange={onChange}
                placeholder="选择周"
                value={currentWeek}
              />
            </div>
            <Icon
              type="caret-right"
              className={styles['calendar-header-title-icon']}
              onClick={handleNextWeek}
            />
            <section style={{ display: 'flex', alignItems: 'center' }}>
              <SwitchWeekMonth defaultType="week" onChange={handleViewTypeChange} />
              {isEditing ? (
                <Button type="primary" style={{ marginLeft: '10px' }} onClick={handleSave}>
                  保存
                </Button>
              ) : (
                <Button style={{ marginLeft: '10px' }} onClick={handleEdit}>
                  编辑
                </Button>
              )}
            </section>
          </div>
        </div>
      </div>
      <section className={styles['calendar-body-box']}>
        <article className={styles['calendar-body-left']}>
          <LeftItem title="场次标签：" />
          <LeftItem title="主题：" />
          <LeftItem title="货盘简述：" />
          <LeftItem title="备注：" />
          <LeftItem title="坑位数量：" marginTop={13} paddingBottom={0} />
          {projectGroups?.map((item) => (
            <p className={styles['group-title']} key={item.projectGroupId}>
              {item.projectGroupName}
            </p>
          ))}
          <p className={styles['group-title']}>汇总</p>
          <LeftItem title="专场排期：" />
          {LIVE_TIME_LIST.map((item, index) => (
            <div
              className={styles['date-time']}
              key={index}
              style={{
                minHeight: index === LIVE_TIME_LIST.length - 1 ? 0 : '200px',
                // height:
                //   index === LIVE_TIME_LIST.length - 1
                //     ? 0
                //     : document.getElementById(
                //         item + 'crm-befriends-manus-seek-date-time-item' + index,
                //       )?.clientHeight + 'px',
              }}
            >
              <span className={styles['date-time-text']}>{item}</span>
            </div>
          ))}
        </article>

        <article className={styles['calendar-body-right']}>
          <div className={styles.weeks}>{renderDaysOfWeek()}</div>
          <Spin spinning={editLoading}>
            <div className={styles['calendar-body']}>{renderWeekDates()}</div>
          </Spin>
        </article>
      </section>
    </div>
  );
};

export default WeekCalendar;
