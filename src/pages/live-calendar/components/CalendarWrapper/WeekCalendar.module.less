// ai生成
// 从index.less复制过来的样式
.calendar-customer {
  width: 100%;
  background: rgb(240, 242, 245);
  overflow: auto;
  //   padding: 0px 16px 0;

  .calendar-header {
    // background: rgb(240, 242, 245);
    // background: #fff;background: rgb(240, 242, 245);
    margin-bottom: 5px;
    border: 1px;
    // padding: 4px 0;
    text-align: center;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-direction: column;
    .calendar-header-box {
      background: #fff;
      width: 100%;
      display: flex;
      height: 50px;
      align-items: center;
      .calendar-header-title {
        font-size: 16px;
        color: #204eff;
        font-weight: bold;
        display: flex;
        align-items: center;
        margin-left: 12px;
        .calendar-header-title-box {
          position: relative;
          cursor: pointer;
          .calendar-header-title-month {
            position: absolute;
            width: 126x;
            left: 0;
            top: 0;
            opacity: 0;
          }
        }
        .calendar-header-title-text {
          background: rgb(239, 242, 255);
          padding: 0px 4px;
          // margin: 0 px;
          font-size: 15px;
        }
        .calendar-header-title-icon {
          font-size: 20px;
        }
      }
    }
  }
  .calendar-body-box {
    display: flex;
    // flex-direction: column;
    // height: 100%;
    .calendar-body-left {
      padding-left: 16px;
      // width: 200px;
      // background: #fff;
      padding-top: 142px; // 对齐右侧日期头部
      .group-title {
        // margin-top: 12px;
        height: 45px;
      }
      .date-time {
        // margin-bottom: 80px;
        position: relative;
        .date-time-text {
          position: absolute;
          top: -5px;
        }
      }

      // 调整左侧标题项的样式以与右侧对齐
      .create-modal-top-item {
        height: 28px;
        margin-bottom: 0;
      }

      // 调整左侧项目组的高度以与表格行对齐
      p {
        height: 28px;
        line-height: 28px;
        margin-bottom: 0;
        display: flex;
        align-items: center;
      }
    }
    .calendar-body-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
      .weeks {
        background: #fff;
        margin: 10px 16px;
        display: flex;
        height: 26px;
        align-items: center;
        border-radius: 12px;
        .calendar-header-card {
          flex: 1;
          display: flex;
          justify-content: center;
        }
      }
      .calendar-body {
        margin: 10px 16px;
        .opacityTd {
          opacity: 0;
          border: none;
        }
        .calendar-body-card {
          padding: 6px 10px 10px;
          text-align: center;
          border: none;
          //   height: 100%;
          flex: 1;
          margin: 2px;
          //   border: 4px solid #f9fbfe;
          border-radius: 12px;
          overflow: hidden;
          .calendar-body-card-top {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 30px;
            line-height: 30px;
            .calendar-body-card-day {
              font-size: 20px;
              color: rgb(157, 157, 157);
              font-weight: bold;
              line-height: 1;
              display: flex;
              align-items: baseline;
              .calendar-body-card-count {
                display: flex;
                font-size: 12px;

                color: #204eff;
                font-weight: normal;
                align-items: center;
                .calendar-body-card-icon {
                  color: #c7dff7;
                  transform: scale(0.8);
                  margin: 0 0px 0 6px;
                }
                .calendar-body-card-number {
                  font-size: 14px;
                }
              }
            }
          }
        }
        .calendar-body-card:hover {
          cursor: pointer;
          background-color: #e6e6e6;
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
          .calendar-body-card-plus {
            opacity: 1;
            // display: inline-block;
          }
        }
        .calendar-body-line {
          width: 100%;
          display: flex;
        }
        .prev-month,
        .next-month {
          color: #999;
        }
        .selected {
          //   background-color: #007bff !important;
          //   color: #fff;
        }
      }
    }
  }

  .calendar-body-card-plus {
    // display: none;
    opacity: 0;
    color: #204eff;
    font-size: 24px;
  }

  // ai生成
  // 周视图特殊样式
  .week-view {
    // height: 120px !important; // 周视图卡片高度增加

    // 周视图里更明显的日期显示
    .calendar-body-card-day {
      font-size: 22px !important;
    }

    // 周视图中更多内容的样式
    .calendar-body-content {
      margin-top: 5px;
      text-align: left;

      .content-item {
        margin-bottom: 5px;
        font-size: 13px;
        color: #333;
      }
    }
  }

  // 日历包装容器
  .calendar-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

// 内联样式部分
.calendar-customer {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
}

.calendar-body {
  flex: 1;
  overflow: hidden;
}

.calendar-body-line {
  display: flex;
  height: 100%;
}

.calendar-date-header {
  font-size: 36px;
  font-weight: bold;
  padding: 8px;
  // border-bottom: 1px solid #eee;
}

.calendar-content {
  padding: 8px 0px;
  height: calc(100% - 45px);
  overflow: auto; // 调整antd表格行高
  text-align: left;
  .calendar-content-item {
    padding-bottom: 9px;
  }
  .date-time-box {
    position: relative;
    .date-time-item {
      border-top: 1px solid #f0f0f0;
    }
  }
  :global {
    .ant-table-small {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        height: 28px;
        line-height: 28px;
        padding-top: 0;
        padding-bottom: 0;
      }

      // 去掉表格外边框
      border: none;

      .ant-table-container {
        border: none;
        border-top: none;
        border-bottom: none;

        &::before,
        &::after {
          display: none !important;
        }
      }
      .ant-table-row-cell-break-word {
        padding: 0 2px !important;
        height: 44px !important;
      }
      // 去掉表头和表格内容的边框
      .ant-table-thead > tr > th {
        border-bottom: 1px solid #f0f0f0;
      }

      .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }
}

.calendar-detail {
  font-size: 12px;
}

.calendar-item {
  margin-bottom: 8px;
}

.calendar-label {
  font-weight: bold;
  margin-right: 4px;
}

.calendar-statistics {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 11px;
  color: #666;
}

.stat-value {
  font-weight: bold;
}

.calendar-data-table {
  margin-top: 8px;
  font-size: 11px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #eee;
}

.table-cell {
  flex: 1;
  padding: 2px 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.summary-row {
  font-weight: bold;
}

.calendar-actions {
  margin-top: 8px;
  text-align: right;
}

.calendar-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.calendar-body-card {
  //   height: 100% !important;
  min-height: 120px;
  flex: 1;
  margin: 0 4px;
}

.week-view {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.calendar-date-number {
  text-align: left;
}
// 2024年05月21日 开山ai结尾共生成231行代码

.create-modal-top-item {
  display: flex;
  align-items: center;
  padding-bottom: 6px;
  width: 100%;
  font-weight: bold;
  .icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #204eff;
    margin-right: 10px;
    flex-shrink: 0;
  }
}

// 2024年05月21日 开山ai结尾共生成32行代码
