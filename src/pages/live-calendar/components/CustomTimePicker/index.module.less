// ai生成
.customTimePicker {
  position: relative;
  display: inline-block;

  .timeInput {
    width: 80px;
    font-size: 12px;
    padding: 2px 8px;
    height: 28px;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
  }

  .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    // min-width: 200px;
    margin-top: 4px;

    .dropdownContent {
      display: flex;
      max-height: 200px;

      .timeColumn {
        flex: 1;

        .columnTitle {
          padding: 8px 12px;
          font-size: 12px;
          font-weight: 600;
          color: #666;
          background: #fafafa;
          border-bottom: 1px solid #f0f0f0;
          text-align: center;
        }

        .optionsList {
          max-height: 160px;
          overflow-y: auto;

          &::-webkit-scrollbar {
            width: 4px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;

            &:hover {
              background: #a8a8a8;
            }
          }

          .timeOption {
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            line-height: 1.5;
            transition: background-color 0.2s ease;

            &:hover:not(.disabled) {
              background-color: #f5f5f5;
            }

            &.selected {
              background-color: #1890ff;
              color: #fff;

              &:hover {
                background-color: #40a9ff;
              }
            }

            &.disabled {
              color: #d9d9d9;
              cursor: not-allowed;
              background-color: transparent;
            }
          }
        }

        &:not(:last-child) {
          border-right: 1px solid #f0f0f0;
        }
      }
    }

    .dropdownFooter {
      border-top: 1px solid #f0f0f0;
      padding: 8px 12px;
      text-align: right;

      .confirmBtn {
        background: #1890ff;
        color: #fff;
        border: none;
        border-radius: 4px;
        padding: 4px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover:not(:disabled) {
          background: #40a9ff;
        }

        &:disabled {
          background: #d9d9d9;
          cursor: not-allowed;
        }
      }
    }
  }
}

// 冲突状态样式
.customTimePicker.conflict {
  .timeInput {
    border-color: #ff4d4f;

    &:focus {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .customTimePicker {
    .dropdown {
      min-width: 180px;

      .dropdownContent {
        .timeColumn {
          .columnTitle {
            font-size: 11px;
            padding: 6px 8px;
          }

          .optionsList {
            .timeOption {
              padding: 4px 8px;
              font-size: 11px;
            }
          }
        }
      }

      .dropdownFooter {
        padding: 6px 8px;

        .confirmBtn {
          padding: 3px 8px;
          font-size: 11px;
        }
      }
    }
  }
}
// 2025年01月23日 开山ai结尾共生成150行代码
