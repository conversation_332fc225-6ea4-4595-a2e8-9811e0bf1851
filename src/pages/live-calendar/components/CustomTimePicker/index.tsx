// ai生成
import React, { useState, useCallback, useEffect } from 'react';
import { Input } from 'antd';
import moment from 'moment';
import styles from './index.module.less';

interface CustomTimePickerProps {
  value?: moment.Moment;
  onChange?: (time: moment.Moment | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  disabledHours?: () => number[];
  disabledMinutes?: (hour: number) => number[];
}

/**
 * 自定义时间选择器
 * 支持06:00-05:59的时间范围选择
 * 支持任意分钟选择
 */
const CustomTimePicker: React.FC<CustomTimePickerProps> = ({
  value,
  onChange,
  placeholder,
  className,
  disabled = false,
  disabledHours = () => [],
  disabledMinutes = () => [],
}) => {
  const [hour, setHour] = useState<number | undefined>();
  const [minute, setMinute] = useState<number | undefined>();
  const [inputValue, setInputValue] = useState<string>('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 处理滚动穿透问题
  // useEffect(() => {
  //   if (isDropdownOpen) {
  //     // 禁用body滚动
  //     const originalStyle = window.getComputedStyle(document.body).overflow;
  //     document.body.style.overflow = 'hidden';

  //     return () => {
  //       // 恢复body滚动
  //       document.body.style.overflow = originalStyle;
  //     };
  //   }
  // }, [isDropdownOpen]);

  // 从value初始化小时和分钟
  useEffect(() => {
    if (value && value.isValid()) {
      const h = value.hour();
      const m = value.minute();
      setHour(h);
      setMinute(m);
      setInputValue(value.format('HH:mm'));
    } else {
      setHour(undefined);
      setMinute(undefined);
      setInputValue('');
    }
  }, [value]);

  // 生成小时选项 (06:00-05:59)
  const generateHourOptions = useCallback(() => {
    const options = [];
    const disabledHoursList = disabledHours();

    // 6-23点 (当天)
    for (let h = 6; h <= 23; h++) {
      options.push({
        value: h,
        label: (h < 10 ? '0' : '') + h.toString(),
        disabled: disabledHoursList.includes(h),
      });
    }

    // 0-5点 (次日)
    for (let h = 0; h <= 5; h++) {
      options.push({
        value: h,
        label: (h < 10 ? '0' : '') + h.toString(),
        disabled: disabledHoursList.includes(h),
      });
    }

    return options;
  }, [disabledHours]);

  // 生成分钟选项 (00-59)
  const generateMinuteOptions = useCallback(() => {
    const options = [];
    const disabledMinutesList = hour !== undefined && disabledMinutes ? disabledMinutes(hour) : [];

    for (let m = 0; m <= 59; m++) {
      options.push({
        value: m,
        label: (m < 10 ? '0' : '') + m.toString(),
        disabled: disabledMinutesList.includes(m),
      });
    }

    return options;
  }, [hour, disabledMinutes]);

  // 处理小时变化
  const handleHourChange = useCallback(
    (h: number) => {
      setHour(h);
      // 如果分钟已选择，立即触发onChange
      if (minute !== undefined) {
        const newTime = moment().hour(h).minute(minute);
        setInputValue(newTime.format('HH:mm'));
        onChange?.(newTime);
      }
    },
    [minute, onChange],
  );

  // 处理分钟变化
  const handleMinuteChange = useCallback(
    (m: number) => {
      setMinute(m);
      // 如果小时已选择，立即触发onChange
      if (hour !== undefined) {
        const newTime = moment().hour(hour).minute(m);
        setInputValue(newTime.format('HH:mm'));
        onChange?.(newTime);
      }
    },
    [hour, onChange],
  );

  // 处理输入框变化
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;
      setInputValue(val);

      // 验证时间格式
      const timeRegex = /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/;
      if (timeRegex.test(val)) {
        const [h, m] = val.split(':').map(Number);

        // 验证是否在有效范围内（06:00-05:59）
        const isValidTime = (h >= 6 && h <= 23) || (h >= 0 && h <= 5);
        if (isValidTime) {
          const newTime = moment().hour(h).minute(m);
          setHour(h);
          setMinute(m);
          onChange?.(newTime);
        }
      } else if (val === '') {
        setHour(undefined);
        setMinute(undefined);
        onChange?.(null);
      }
    },
    [onChange],
  );

  // 处理输入框失焦
  const handleInputBlur = useCallback(() => {
    // 如果输入不完整或无效，恢复到上一个有效值
    if (value && value.isValid()) {
      setInputValue(value.format('HH:mm'));
    } else if (!inputValue) {
      setInputValue('');
    }
  }, [value, inputValue]);

  // 处理下拉框内滚动事件，防止滚动穿透
  const handleListScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    e.stopPropagation();

    const target = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // 防止滚动到顶部或底部时穿透到页面滚动
    if (scrollTop === 0) {
      target.scrollTop = 1;
    } else if (scrollTop + clientHeight >= scrollHeight) {
      target.scrollTop = scrollHeight - clientHeight - 1;
    }
  }, []);

  return (
    <div className={`${styles.customTimePicker} ${className || ''}`}>
      <Input
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        placeholder={placeholder || '请选择时间'}
        disabled={disabled}
        className={styles.timeInput}
        onClick={() => setIsDropdownOpen(true)}
        style={{ width: '60px' }}
      />

      {isDropdownOpen && !disabled && (
        <div className={styles.dropdown}>
          <div className={styles.dropdownContent}>
            <div className={styles.timeColumn}>
              {/* <div className={styles.columnTitle}>小时</div> */}
              <div className={styles.optionsList} onScroll={handleListScroll}>
                {generateHourOptions().map(({ value: h, label, disabled: isDisabled }) => (
                  <div
                    key={h}
                    className={`${styles.timeOption} ${hour === h ? styles.selected : ''} ${
                      isDisabled ? styles.disabled : ''
                    }`}
                    onClick={!isDisabled ? () => handleHourChange(h) : undefined}
                  >
                    {label}
                  </div>
                ))}
              </div>
            </div>

            <div className={styles.timeColumn}>
              {/* <div className={styles.columnTitle}>分钟</div> */}
              <div className={styles.optionsList} onScroll={handleListScroll}>
                {generateMinuteOptions().map(({ value: m, label, disabled: isDisabled }) => (
                  <div
                    key={m}
                    className={`${styles.timeOption} ${minute === m ? styles.selected : ''} ${
                      isDisabled ? styles.disabled : ''
                    }`}
                    onClick={!isDisabled ? () => handleMinuteChange(m) : undefined}
                  >
                    {label}
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className={styles.dropdownFooter}>
            <button
              className={styles.confirmBtn}
              onClick={() => setIsDropdownOpen(false)}
              disabled={hour === undefined || minute === undefined}
            >
              确定
            </button>
          </div>
        </div>
      )}

      {/* 点击外部区域关闭下拉框 */}
      {/* {isDropdownOpen && (
        <div className={styles.overlay} onClick={() => setIsDropdownOpen(false)} />
      )} */}
    </div>
  );
};

export default CustomTimePicker;
// 2025年01月23日 开山ai结尾共生成253行代码
