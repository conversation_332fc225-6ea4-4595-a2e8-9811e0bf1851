import React, { useState } from 'react';
import { LiveRoundBatchEditByCalendarRequest } from '../services';
import { liveRoundBatchEditByCalendar } from '../services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { ColumnProps } from 'antd/lib/table';
import { Input } from 'antd';

export type DataSourceType = {
  key?: string;
  projectGroup?: string;
  talkNum?: string;
  linkNum?: string;
  allNum?: string;
};
export const useBatchEdit = () => {
  const [editLoading, setEditLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [tableData, setTableData] = useState<{ [idKey: string]: DataSourceType[] }>({});
  const [specialSchedule, setSpecialSchedule] = useState<{
    [idKey: string]: {
      timeList: string[];
      content: string;
    }[];
  }>();

  const handleInputChange = (key: string, field: string, value: string, idKey: string) => {
    // 只允许输入数字
    const numericValue = value.replace(/[^\d]/g, '');
    const newTableData = {
      ...tableData,
      [idKey]: tableData[idKey].map((item) =>
        item.key === key ? { ...item, [field]: numericValue } : item,
      ),
    };

    setTableData(newTableData);
  };
  const COLUMNS = (idKey: string) =>
    [
      {
        title: '讲解数量',
        dataIndex: 'talkNum',
        key: 'talkNum',
        width: 50,
        render: (val: string, record) => {
          if (isEditing) {
            return (
              <Input
                value={val}
                onChange={(e) => handleInputChange(record.key!, 'talkNum', e.target.value, idKey)}
                style={{ width: '100%' }}
              />
            );
          }
          return <p>{val ?? 0}</p>;
        },
      },
      {
        title: '挂链数量',
        dataIndex: 'linkNum',
        key: 'linkNum',
        width: 50,
        render: (val: string, record) => {
          if (isEditing) {
            return (
              <Input
                value={val}
                onChange={(e) => handleInputChange(record.key!, 'linkNum', e.target.value, idKey)}
                style={{ width: '100%' }}
              />
            );
          }
          return <p>{val ?? 0}</p>;
        },
      },
      {
        title: '类目汇总',
        dataIndex: 'allNum',
        key: 'allNum',
        width: 50,
        render: (val: string, record) => (
          <p>{Number(record?.linkNum ?? 0) + Number(record?.talkNum ?? 0)}</p>
        ),
      },
    ] as ColumnProps<DataSourceType>[];
  const batchEdit = async (params: LiveRoundBatchEditByCalendarRequest) => {
    setEditLoading(true);
    const result = await responseWithResultAsync({
      request: liveRoundBatchEditByCalendar,
      params,
    });
    setEditLoading(false);
    return result;
  };
  return {
    editLoading,
    isEditing,
    setIsEditing,
    tableData,
    setTableData,
    handleInputChange,
    COLUMNS,
    batchEdit,
    specialSchedule,
    setSpecialSchedule,
  };
};
