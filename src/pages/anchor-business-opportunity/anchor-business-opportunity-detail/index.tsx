import { useSetState } from 'ahooks';
import { Button, Tag, message, Spin } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useEffect, useMemo, useState } from 'react';
import BasicForm from './BasicForm';

import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  getBusinessClueDetail,
  GetBusinessClueDetailRequest,
  GetBusinessClueDetailResult,
  editBusinessClueDetail,
  EditBusinessClueDetailRequest,
} from '../services';
import { CreateFormType } from '../utils/type';
import moment from 'moment';
import styles from './index.module.less';

import { AuthWrapper, history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Title, Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import { STATUS, STATUS_COLOR } from '../utils/enum';
import OrderLog from './OrderLog';
import { set } from 'lodash';
type PropsType = FormComponentProps<CreateFormType>;
type ModalState = {
  loading?: boolean;
  detail?: any;
};

const AddSelectTabPage: React.FC<PropsType> = ({ form }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const [id, setId] = useState('');
  const [type, setType] = useState<'info' | 'edit'>('info');
  const { delRoutetag } = useCloseAndJump();
  const [detail, setDetail] = useState();
  const [dataSource, setDataSource] = useState([]);
  const handleCancel = () => {
    if (type === 'edit') {
      setType('info');
      handleDetail(id);
    } else {
      delRoutetag();
      history.goBack();
    }
  };

  const handleDetail = async (id: string) => {
    const res = await responseWithResultAsync({
      request: getBusinessClueDetail,
      params: { id },
    });

    if (res) {
      const info = {
        ...res,
        brand: { label: res?.brandName, key: res.brandId },
        supplier: { label: res?.supplierName, key: res.supplierId },
        attachments: res?.attachments?.map((item) => ({
          uid: item,
          name: item,
          status: 'done',
          url: item,
          resourceId: item,
        })),
      };
      form.setFieldsValue({
        ...info,
      });
      if (info?.dispatchList) {
        const arr = info?.dispatchList?.map((item, index) => {
          return {
            ...item,
            index: index + 1,
            required: item.convertTime ? true : false,
          };
        });
        setDataSource(arr);
      }
      setDetail(info);
      console.log('detail', res);
    }
  };
  const checkData = () => {
    let isOk = true;
    dataSource.forEach((item) => {
      console.log('item', item);
      if (item?.required) {
        if (
          !item?.bpId ||
          !item?.deptId ||
          !item?.contractType ||
          !item?.projectGroup ||
          !item?.anchorBusinessOrder
        ) {
          message.error(`第${item.index}条信息未填写完整`);
          isOk = false;
          return isOk;
        }
      } else {
        if (!item?.bpId) {
          isOk = false;
          message.error(`第${item.index}条信息商务未选择`);
          return isOk;
        }
      }
    });
    return isOk;
  };
  const handleOk = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const params: EditBusinessClueDetailRequest = {
        ...values,
        attachments: values?.attachments?.map((i) => i.url),
        dispatchEditRequest: dataSource?.map((item) => {
          return {
            id: item?.id,
            bpId: item?.bpId,
            anchorBusinessOrder: item?.anchorBusinessOrder,
            deptId: item?.deptId,
            projectGroup: item?.projectGroup,
            contractType: item?.contractType,
          };
        }),
        brandId: detail?.brandId,
        supplierId: values?.supplier?.key,
        clueId: id,
      };
      // editBusinessClueDetail
      console.log('dataSource', dataSource);

      const isOk = checkData();

      if (!isOk) {
        return;
      }
      const result = await responseWithResultAsync({
        request: editBusinessClueDetail,
        params,
      });
      console.log('res', result);
      if (result) {
        message.success('操作成功');
        setType('info');
        handleDetail(id);
      }
    });
  };
  const handleEdit = () => {
    setType('edit');
  };

  useEffect(() => {
    const search = window.location.search;
    console.log('search', search);
    setType('info');
    if (search) {
      try {
        const value = search.split('=')[1];
        setId(value);
        handleDetail(value);
      } catch {
        console.log('error,商机详情页获取id失败');
      }
    } else {
      setId('');
    }
  }, []);

  return (
    <PageLayout
      className={styles['cooperation-report-contain']}
      routePath="/business-opportunity-info"
    >
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card
            title={
              <span>
                商机信息
                {detail?.status && (
                  <Tag
                    color={STATUS_COLOR[detail?.status as keyof typeof STATUS_COLOR]}
                    style={{ marginLeft: '8px' }}
                  >
                    {STATUS[detail?.status as keyof typeof STATUS]}
                  </Tag>
                )}
              </span>
            }
          >
            <div className={styles.extra}>
              <BasicForm form={form} detail={detail} type={type} />
            </div>
          </Card>
          <Card title="派单情况">
            <div className={styles.extra}>
              <OrderLog
                form={form}
                detail={detail}
                type={type}
                dataSource={dataSource}
                setDataSource={setDataSource}
              />
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          {type === 'info' && (
            <AuthWrapper functionName="f_business_opportunity_master_edit">
              <Button type="primary" onClick={handleEdit}>
                编辑
              </Button>
            </AuthWrapper>
          )}
          {type === 'edit' && (
            <Button type="primary" onClick={handleOk}>
              保存
            </Button>
          )}
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create()(AddSelectTabPage);
