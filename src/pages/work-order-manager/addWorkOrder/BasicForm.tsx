import {
  DatePicker,
  Input,
  InputNumber,
  Radio,
  Select,
  Spin,
  Modal,
  Table,
  AutoComplete,
  message,
} from 'antd';
import Form, { WrappedFormUtils } from 'antd/lib/form/Form';
import React, { MutableRefObject, Ref, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.less';
import { useDeptList } from '@/hooks/useDeptList';
import { useLiveRoomList } from '@/hooks/useLiveRoomList';
import { useEmployeeList } from '@/hooks/useEmployeeList';
import FileUploadSouceId from '@/components/FileUploadSouceId';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { GetUserByPhoneUserIdRequest } from '../services/index';
import { CreateFormType } from '../utils/type';
import AddEditNoticeModal from '../components/AddEditNoticeModal';
import moment from 'moment';
import { useNotice } from '../components/AddEditNoticeModal/noticeHooks';
import SelectModal from './SelectModal';
import { useOrderInfo } from '../utils/hook';
import { getSpuBrand } from '../services';
import { useRequest } from 'ahooks';
import { debounce } from 'lodash';

const { Item } = Form;
const { Option } = Select;
type PropsType = {
  form: WrappedFormUtils<CreateFormType>;
  type: 'edit' | 'create';
  id?: string;
  onGetRecord: (params: GetUserByPhoneUserIdRequest) => Promise<void>;
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 12,
  },
};
const formItemStyle: React.CSSProperties = {
  width: '200px',
};
const formStyle: React.CSSProperties = {
  width: '25%',
};

const BasicForm: React.FC<PropsType> = ({ form, type, id, onGetRecord }) => {
  const { getFieldDecorator } = form;
  const [brandNameList, setBrandNameList] = useState<any[]>([]);
  // 'WORK_ORDER_FEEDBACK_CHANNEL',  反馈渠道  'WORK_ORDER_PROBLEM_CLASSIFICATION', 问题分类
  const { codeList } = useCode(CODE_ENUM.WORK_ORDER_FEEDBACK_CHANNEL, { able: true });
  const { codeList: ProblemList } = useCode(CODE_ENUM.WORK_ORDER_PROBLEM_CLASSIFICATION, {
    able: true,
  });
  const {
    list: manaList,
    getList: getManaList,
    loading: manaLoading,
    handleSearch: manaSearch,
  } = useEmployeeList({ bizRoleType: 'BUSINESS' });

  const { liveList, loading: liveRoomLoaindg, getLiveRoomList, handleSearch } = useLiveRoomList();
  useEffect(() => {
    getLiveRoomList({});
  }, []);
  const isUpGrade = useMemo(() => {
    return form?.getFieldsValue()?.orderType === 'UPGRADE';
  }, [form?.getFieldsValue()?.orderType]);

  const { orderInfoLoading, handleGetOrderInfo, selectModalRef, setFormByOrderInfo } =
    useOrderInfo(form);
  const { noticeRef, noticeExpire, noticeList, loading } = useNotice();

  const { run: getSpuBrandList, loading: getSpuBrandLoading } = useRequest(getSpuBrand, {
    manual: true,
    onSuccess: ({ res }) => {
      if (!res?.success) {
        return message.error(res?.message || '网络异常');
      }
      const result = res?.result?.records || [];
      const nameList = result?.map((item) => item?.name);
      console.log('nameList', nameList);
      setBrandNameList(nameList?.filter((item) => item !== ''));
    },
  });

  const handleSearchBrandName = debounce((value: string) => {
    getSpuBrandList({
      name: value,
      current: 1,
      size: 10,
    });
  }, 300);
  return (
    <>
      <Form {...formItemLayout} className={styles['form-box']}>
        <Item label="工单类型" style={formStyle}>
          {getFieldDecorator('orderType', {
            initialValue: 'UPGRADE',
            rules: [
              {
                required: true,
                message: '请选择工单类型',
              },
            ],
          })(
            <Radio.Group style={formItemStyle}>
              <Radio value="UPGRADE">升级工单</Radio>
              <Radio value="NON_UPGRADE">非升级工单</Radio>
            </Radio.Group>,
          )}
        </Item>
        <Item label="优先级" style={formStyle}>
          {getFieldDecorator('priority', {
            initialValue: 'NORMAL',
            rules: [
              {
                required: true,
                message: '请选择优先级',
              },
            ],
          })(
            <Radio.Group style={formItemStyle}>
              <Radio value="NORMAL">普通</Radio>
              <Radio value="IMPORTANT">重要</Radio>
              <Radio value="URGENT">紧急</Radio>
            </Radio.Group>,
          )}
        </Item>
        <Item label="" style={formStyle}></Item>
        <Item label="" style={formStyle}></Item>
        <Item label="反馈渠道" style={formStyle}>
          {getFieldDecorator('feedbackChannel', {
            rules: [
              {
                required: true,
                message: '请选择反馈渠道',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              showSearch
              optionFilterProp="children"
              style={formItemStyle}
            >
              {codeList.map((item) => (
                <Option value={item.value} key={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="问题分类" style={formStyle} className={styles['select-box']}>
          {getFieldDecorator('issueCategory', {
            rules: [
              {
                required: true,
                message: '请选择问题分类',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              showSearch
              filterOption={true}
              optionFilterProp="children"
              style={{ ...formItemStyle }}
              dropdownMatchSelectWidth={false}
            >
              {ProblemList.map((item) => (
                <Option
                  style={{ width: 'auto', whiteSpace: 'nowrap' }}
                  value={item.value}
                  key={item.value}
                >
                  {item.label}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="直播间" style={formStyle}>
          {getFieldDecorator('liveRoomInfo', {
            rules: [
              {
                required: isUpGrade,
                message: '请选择直播间',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              allowClear
              loading={liveRoomLoaindg}
              style={formItemStyle}
              showSearch
              defaultActiveFirstOption={false}
              showArrow={false}
              filterOption={false}
              onSearch={handleSearch}
              onBlur={() => {
                getLiveRoomList({});
              }}
              labelInValue
            >
              {liveList?.map((item) => (
                <Option value={item.id} key={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="订单编号" style={formStyle}>
          <Spin spinning={orderInfoLoading}>
            {getFieldDecorator('orderNumber', {
              rules: [
                {
                  required: isUpGrade,
                  message: '请输入订单编号',
                },
                {
                  max: 100,
                  message: '字符限制≤100',
                },
              ],
            })(
              <Input
                placeholder="订单编号"
                style={formItemStyle}
                onBlur={handleGetOrderInfo}
                maxLength={100} // 添加 maxLength 属性限制字符长度
              />,
            )}
          </Spin>
        </Item>
        <Item label="用户ID" style={formStyle}>
          <Spin spinning={loading}>
            {getFieldDecorator('userId', {
              rules: [
                {
                  required: isUpGrade,
                  message: '请输入用户ID',
                },
              ],
            })(
              <Input
                placeholder="用户ID"
                style={formItemStyle}
                maxLength={100}
                onBlur={(e) => {
                  if (e.target.value === undefined || e.target.value === '') return;
                  noticeExpire({
                    // phone: form?.getFieldsValue()?.phoneNumber,
                    userId: e.target.value,
                    workOrderId: id,
                  });
                  onGetRecord({
                    phone: form?.getFieldsValue()?.phoneNumber,
                    userId: e.target.value,
                    workOrderId: id,
                  });
                }}
              />,
            )}
          </Spin>
        </Item>
        <Item label="用户昵称" style={formStyle}>
          {getFieldDecorator('userNickname', {
            rules: [
              {
                required: isUpGrade,
                message: '请输入用户昵称',
              },
            ],
          })(<Input placeholder="用户昵称" style={formItemStyle} maxLength={100} />)}
        </Item>
        <Item label="手机号码" style={formStyle}>
          <Spin spinning={loading}>
            {getFieldDecorator('phoneNumber', {
              rules: [
                {
                  required: isUpGrade,
                  message: '请输入手机号码',
                },
              ],
            })(
              <Input
                placeholder="手机号码"
                style={formItemStyle}
                maxLength={20} // 添加 maxLength 属性限制字符长度
                onBlur={(e) => {
                  if (e.target.value === undefined || e.target.value === '') return;
                  noticeExpire({
                    phone: e.target.value,
                    // userId: form?.getFieldsValue()?.userId,
                    workOrderId: id,
                  });
                  onGetRecord({
                    phone: e.target.value,
                    userId: form?.getFieldsValue()?.userId,
                    workOrderId: id,
                  });
                }}
              />,
            )}
          </Spin>
        </Item>
        <Item label="商品ID" style={formStyle}>
          {getFieldDecorator('productId', {
            rules: [
              {
                required: isUpGrade,
                message: '请输入商品ID',
              },
              {
                max: 100,
                message: '字符限制≤100',
              },
            ],
          })(
            <Input
              placeholder="商品ID"
              style={formItemStyle}
              maxLength={100} // 添加 maxLength 属性限制字符长度
            />,
          )}
        </Item>
        <Item label="商品名称" style={formStyle}>
          {getFieldDecorator('productName', {
            rules: [
              {
                required: isUpGrade,
                message: '请输入商品名称',
              },
              {
                max: 100,
                message: '字符限制≤100',
              },
            ],
          })(
            <Input
              placeholder="商品名称"
              style={formItemStyle}
              maxLength={100} // 添加 maxLength 属性限制字符长度
            />,
          )}
        </Item>
        <Item label="店铺名称" style={formStyle}>
          {getFieldDecorator('shopName', {
            rules: [
              {
                required: isUpGrade,
                message: '请输入店铺名称',
              },
              {
                max: 100,
                message: '字符限制≤100',
              },
            ],
          })(
            <Input
              placeholder="店铺名称"
              style={formItemStyle}
              maxLength={100} // 添加 maxLength 属性限制字符长度
            />,
          )}
        </Item>
        <Item label="品牌名称" style={formStyle}>
          {getFieldDecorator('brandName', {
            rules: [
              {
                required: isUpGrade,
                message: '请输入品牌名称',
              },
              {
                max: 100,
                message: '字符限制≤100',
              },
            ],
          })(
            // <Input
            //   placeholder="品牌名称"
            //   style={formItemStyle}
            //   maxLength={100} // 添加 maxLength 属性限制字符长度
            // />,
            <AutoComplete
              dataSource={brandNameList}
              onSearch={handleSearchBrandName}
              placeholder="请输入"
              style={formItemStyle}
            />,
          )}
        </Item>
        <Item label="付款日期" style={formStyle}>
          {getFieldDecorator('paymentTime', {
            rules: [
              {
                required: isUpGrade,
                message: '请选择付款日期',
              },
            ],
          })(<DatePicker placeholder="请选择付款日期" style={formItemStyle} format="YYYY-MM-DD" />)}
        </Item>
        <Item label="实付金额" style={formStyle}>
          {getFieldDecorator('amountPaid', {
            rules: [
              {
                required: false,
                message: '请输入实付金额',
              },
              {
                pattern: /^\d+(\.\d+)?$/,
                message: '仅能输入数字和小数点',
              },
            ],
          })(
            <Input
              placeholder="实付金额"
              style={formItemStyle}
              maxLength={100} // 添加 maxLength 属性限制字符长度
            />,
          )}
        </Item>
        <Item label="商务" style={formStyle}>
          {getFieldDecorator('business', {
            rules: [
              {
                required: false,
                message: '请选择商务',
              },
            ],
          })(
            <Select
              showSearch
              allowClear
              onSearch={manaSearch}
              filterOption={false}
              placeholder="请选择"
              style={formItemStyle}
              labelInValue
              loading={manaLoading}
            >
              {manaList?.map((item) => (
                <Select.Option value={item.employeeId} key={item.employeeId}>
                  {item.employeeName}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item
          label="上传附件"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 10 }}
          style={{ width: '50%' }}
        >
          {getFieldDecorator('attachments', {
            rules: [
              {
                required: false,
                message: '请上传附件',
              },
            ],
          })(
            <FileUploadSouceId
              // style={{ width: '400px !important' }}
              maxLen={10}
              multiple
              isImage
              typeCode="SPU_IMG"
              maxSize={200 * 1024 * 1024}
              // multiple
              accept={
                '.doc,.ppt,.docx,.jpg,.jpeg,.png,.png,.avi,.mp4,.mov,.wmv,.flv,.mkv,.rmvb,.pdf,.word,.xls,.xlsx,.csv'
              }
            />,
          )}
        </Item>
        <Item label="" style={formStyle}></Item>
        <Item
          label="问题描述"
          style={{ width: '100%' }}
          labelCol={{ span: 2 }}
          wrapperCol={{ span: 22 }}
        >
          {getFieldDecorator('issueDescription', {
            rules: [
              {
                required: true,
                message: '请输入问题描述',
              },
              {
                max: 1000,
                message: '字符限制≤1000',
              },
            ],
          })(
            <Input.TextArea
              placeholder="问题描述"
              style={{ width: '50%' }}
              maxLength={1000} // 添加 maxLength 属性限制字符长度
              rows={4}
            />,
          )}
        </Item>
        <Item
          label="处理结果"
          style={{ width: '100%' }}
          labelCol={{ span: 2 }}
          wrapperCol={{ span: 22 }}
        >
          {getFieldDecorator('processingResult', {
            rules: [
              {
                required: !isUpGrade,
                message: '请输入处理结果',
              },
              {
                max: 1000,
                message: '字符限制≤1000',
              },
            ],
          })(
            <Input.TextArea
              placeholder="处理结果"
              style={{ width: '50%' }}
              maxLength={1000} // 添加 maxLength 属性限制字符长度
              rows={4}
            />,
          )}
        </Item>
      </Form>
      <AddEditNoticeModal onRef={noticeRef} noticeList={noticeList} />
      <SelectModal onSetInfo={setFormByOrderInfo} ref={selectModalRef} />
    </>
  );
};

export default BasicForm;
