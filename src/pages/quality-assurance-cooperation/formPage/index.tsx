import React, { useEffect, useRef, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import {
  Button,
  Spin,
  Form,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Popover,
  Icon,
  InputNumber,
  Radio,
  message,
} from 'antd';
import { history, Const } from 'qmkit';
import styles from './index.module.less';
import { DetailTitle } from '@/pages/report-sheet/components';
import { GuaranteedTypeEnum, GuaranteedTypeKeysEnum } from '../dataSource';
import { plantformListAll } from '../../../../web_modules/types';
import { useSelectList, useApi } from './hooks';
import LiveModal from '../components/LiveModal';
import { ConditionConfig, EmployeeSelect } from './components';
import {
  PAY_DAY_LIST,
  LIVE_ROOM_POSSESS_TYPE_ENUM_List,
  LIVE_ROOM_POSSESS_TYPE_ENUM,
  ACHIEVED_CALCULATION_TYPE_ENUM_List,
  GUARANTEED_GMV_FAIL_ACHIEVED_RULE_ENUM_List,
  GUARANTEED_GMV_ADVANCE_ACHIEVED_RULE_ENUM_List,
  PAYMENT_TYPE_ENUM_List,
  ACHIEVED_CALCULATION_TYPE_ENUM,
  PAY_DAY,
  PAY_DAY_NAME,
  SETTLEMENT_INTERVAL_TYPE_ENUM_LIST,
  GUARANTEED_GMV_ADVANCE_ACHIEVED_RULE_ENUM,
} from '../types';
import { formatParams, foramtDetailValue } from './utils';
import { getQueryParams } from 'web-common-modules/utils/params';
import { GuaranteedDetailResult, bankAccountQuery, BankAccountQueryResult } from './services/yml';
import { invoiceRateList, invoiceContentList } from '../types';
import { useRequest } from 'ahooks';
import detail from '@/pages/system-code/detail';
import { Title } from '@/components/DetailFormCompoments';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { isNullOrUndefined } from 'web-common-modules/utils/type';

console.log(__ENVINFO__.MY_ENV, '------------->');
type REF_TYPE = {
  getValue: () => {
    [key: string]: any;
  };
  setValue: (list: any) => void;
};

const institutionIdInit =
  __ENVINFO__.MY_ENV === 'test'
    ? '*********'
    : __ENVINFO__.MY_ENV === 'dev'
    ? '15552'
    : '*********';

/**
 * @NOTE
 * 此页面是新增和编辑和重新创建共用
 * 新增和编辑页面包含保存和保存并提交两个逻辑按钮
 * 保存: 视为草稿 点击保存时基本信息必填 此条数据的合同审批状态是空 可以再次编辑
 * 保存并提交: 视为真正创建 所有数据都是必填后才能保存 此条数据的合同状态是待审核 不能在编辑
 *
 * 重新创建的时候路由携带id和type=again 点击保存或者保持并提交的时候调用编辑接口
 */

const { RangePicker } = DatePicker;

const handleValidator = (_: unknown, value: number, callback: any) => {
  if (!value && value != 0) {
    return callback();
  }
  if (Number.isNaN(Number(value))) {
    return callback(`请输入数字 支持2位小数`);
  }

  if (Number(value) < 0) {
    return callback('不能输入负数');
  }
  return callback();
};

const FormPage = (props: any) => {
  const { form } = props;
  const {
    type,
    platform,
    deptInfoDTOS,
    institutionIds,
    paymentType,
    guaranteedGmvFailAchievedRule,
    liveRoomPossessType,
    achievedCalculationType,
    guaranteedGmvAdvanceAchievedRule,
    brandFee,
    guaranteedGmv,
  } = form.getFieldsValue();
  const id = getQueryParams()?.id;
  // const queryType = getQueryParams()?.type;
  const ConditionConfigRef = useRef<REF_TYPE>(null);
  const LiveRef = useRef<{
    clearValue: (value: any) => void;
  }>(null);
  const { closeAndJumpToPage } = useCloseAndJump();
  const handleCancel = () => {
    closeAndJumpToPage('/quality-assurance-cooperation');
    // history.replace('/quality-assurance-cooperation');
  };
  const [bpName, setBpName] = useState<string | undefined>();
  const [bankValue, setBankValue] = useState<BankAccountQueryResult>({});
  const { run: bankAccountQueryRun, loading: bankAccountQueryLoading } = useRequest(
    bankAccountQuery,
    {
      manual: true,
      onSuccess({ res }) {
        if (res?.success) {
          setBankValue(res?.result || {});
        } else {
          message.warning(res?.message || '网络异常');
        }
      },
    },
  );
  const handleDetailShow = (detailValue: GuaranteedDetailResult) => {
    const {
      supplierInfoDTOS,
      shopInfoDTOS,
      brandInfoDTOS,
      goodsKeyWordInfoDTOS,
      thirdPartyPayerUscc,
      thirdPartyPayerName,
      coopExtendEndTime,
      liveRoomInfos,
      payDurationType,
      // achievedCalculationType,
      stimulateCommissionRate,
      institutionInfoDTOS, // 选中的收款机构信息
      ...formValue
    } = foramtDetailValue(detailValue);
    form.setFieldsValue(formValue);
    ConditionConfigRef?.current?.setValue({
      supplierInfoDTOS,
      shopInfoDTOS,
      brandInfoDTOS,
      goodsKeyWordInfoDTOS,
    });
    setBpName(detailValue.bpName);
    // bankAccountQueryRun({
    //   institutionId: formValue?.institutionId,
    //   paymentMethod: 'OFFLINE',
    //   status: 1,
    // });
    setBankValue({
      accountNo: detailValue?.accountNo, // 银行账号
      bankName: detailValue?.bankName, // 开户银行
    });
    if (formValue?.guaranteedGmvFailAchievedRule === 'EXTEND_COOPERATION_PERIOD') {
      setTimeout(() => {
        form.setFieldsValue({
          coopExtendEndTime,
        });
      }, 100);
    }
    if (formValue.paymentType === 'THIRD_PARTY') {
      setTimeout(() => {
        form.setFieldsValue({ thirdPartyPayerUscc, thirdPartyPayerName });
      }, 100);
    }
    if (formValue.liveRoomPossessType === LIVE_ROOM_POSSESS_TYPE_ENUM.INCLUDE) {
      setTimeout(() => {
        form.setFieldsValue({ liveRoomInfos });
      }, 100);
    }
    if (formValue.achievedCalculationType === ACHIEVED_CALCULATION_TYPE_ENUM.BY_PAYMENT) {
      setTimeout(() => {
        form.setFieldsValue({ payDurationType });
      }, 100);
    }
    if (
      formValue.guaranteedGmvAdvanceAchievedRule === GUARANTEED_GMV_ADVANCE_ACHIEVED_RULE_ENUM.GO_ON
    ) {
      setTimeout(() => {
        form.setFieldsValue({
          stimulateCommissionRate,
          commissionRate: formValue?.commissionRate,
        });
      }, 100);
    }
    if (institutionInfoDTOS?.length) {
      const institutionList = institutionInfoDTOS
        .filter((item) => item)
        .map((item) => ({
          institutionId: item.institutionId!,
          institutionName: item.institutionName!,
        }));

      setSelectInstitution(institutionList);
    }
  };
  const {
    guaranteedSaveDraftLoading,
    guaranteedCreateLoading,
    guaranteedUpdateLoading,
    guaranteedDetailLoading,
    guaranteedDetailRun,
    submitEnd,
    detail,
  } = useApi(handleCancel, handleDetailShow);
  const handleSubmit = (isSubmit?: boolean) => {
    form.validateFieldsAndScroll((err: any, value: any) => {
      if (err) {
        return;
      }
      const conditionConfigValue = ConditionConfigRef?.current?.getValue();
      const { supplierInfoDTOS, shopInfoDTOS, brandInfoDTOS, goodsKeyWordInfoDTOS } =
        conditionConfigValue as {
          [key: string]: any[];
        };
      if (!supplierInfoDTOS?.length) {
        message.warning('请添加主体');
        return;
      }
      // 如果是按店铺或者按店铺品牌 店铺列表不能为空
      if (
        [GuaranteedTypeKeysEnum.SHOP, GuaranteedTypeKeysEnum.SHOP_BRAND].includes(value?.type) &&
        !shopInfoDTOS?.length
      ) {
        message.warning('请添加店铺');
        return;
      }
      //如果是按品牌或者按店铺品牌 品牌列表不能为空
      if (
        [GuaranteedTypeKeysEnum.BRAND, GuaranteedTypeKeysEnum.SHOP_BRAND].includes(value?.type) &&
        !brandInfoDTOS?.length
      ) {
        message.warning('请添加品牌');
        return;
      }
      // 如果是按商品关键词 商品列表不能为空
      if (
        [GuaranteedTypeKeysEnum.GOODS_KEYWORD].includes(value?.type) &&
        !goodsKeyWordInfoDTOS?.length
      ) {
        message.warning('请添加商品关键词');
        return;
      }
      const params = formatParams({
        ...value,
        supplierInfoDTOS,
        shopInfoDTOS,
        brandInfoDTOS,
        goodsKeyWordInfoDTOS,
      });
      submitEnd({
        params: { ...params, bankName: bankValue.bankName, accountNo: bankValue.accountNo },
        id,
        isSubmit,
      });
    });
  };
  const { getFieldDecorator } = form;
  const {
    departmentListLoading,
    departmentList,
    institutionListLoading,
    institutionList,
    debounceInstitutionListRun,
  } = useSelectList();
  // 平台,事业部,机构主体改变的时候清空直播间
  const handleChangeClearLive = () => {
    form.resetFields(['liveRoomInfos']);
    LiveRef?.current?.clearValue();
  };

  const handlePlatform = (value: string) => {
    const { appointReceiptInstitutionId } = form.getFieldsValue();
    if (
      (value === 'DY' || value === 'WECHAT_VIDEO') &&
      appointReceiptInstitutionId === institutionIdInit
    ) {
      setBankValue({
        accountNo: '***************', // 银行账号
        bankName: '招商银行股份有限公司杭州滨康支行', // 开户银行
      });
      return;
    }

    if (value === 'TB' && appointReceiptInstitutionId === institutionIdInit) {
      setBankValue({
        accountNo: '*****************', // 银行账号
        bankName: '宁波银行股份有限公司杭州分行', // 开户银行
      });
      return;
    }
    if (appointReceiptInstitutionId === institutionIdInit) {
      setBankValue({
        accountNo: '', // 银行账号
        bankName: '', // 开户银行
      });
    }
  };

  const handleInstitutionId = (value: string) => {
    const { platform } = form.getFieldsValue();
    if (value === institutionIdInit && platform) {
      if (platform === 'DY' || platform === 'WECHAT_VIDEO') {
        setBankValue({
          accountNo: '***************', // 银行账号
          bankName: '招商银行股份有限公司杭州滨康支行', // 开户银行
        });
        return;
      }
      if (platform === 'TB') {
        setBankValue({
          accountNo: '*****************', // 银行账号
          bankName: '宁波银行股份有限公司杭州分行', // 开户银行
        });
        return;
      }
      setBankValue({
        accountNo: '', // 银行账号
        bankName: '', // 开户银行
      });
      return;
    }

    bankAccountQueryRun({
      institutionId: value,
      paymentMethod: 'OFFLINE',
      status: 1,
    });
  };

  // 平台和指定收款机构变化的时候银行账号和开户银行在特定场景下的显示
  const handleBank = (value: string, type: 'platform' | 'appointReceiptInstitutionId') => {
    if (type === 'platform') {
      // 平台Change
      handlePlatform(value);
    } else {
      // 机构主体
      handleInstitutionId(value);
    }
  };

  // 指定收款机构下拉列表
  const [selectInstitution, setSelectInstitution] = useState<
    { institutionId?: string; institutionName?: string }[]
  >([]);
  // 选择的机构主体作为指定收款机构的下拉选项
  const handleSelectInstitution = (value: any[]) => {
    console.log('value', value, institutionList);
    setSelectInstitution([]);
    // 根据机构主体所选的id填充指定收款机构下拉数据
    const selectedInstitutions = value
      .map((ele) => institutionList.find((item) => item.institutionId === ele))
      .filter((institution) => institution !== undefined); // 过滤掉未找到的项
    console.log('selectedInstitutions', selectedInstitutions);

    setSelectInstitution(selectedInstitutions);
    form.setFieldsValue({
      appointReceiptInstitutionId: undefined,
    });
  };

  // 编辑的时候回显
  useEffect(() => {
    if (id) {
      guaranteedDetailRun({ id });
    }
  }, []);

  return (
    <PageLayout
      footer={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
          <Button style={{ marginRight: '6px' }} onClick={handleCancel}>
            取消
          </Button>
          <Button
            style={{ marginRight: '6px' }}
            type="primary"
            onClick={() => {
              handleSubmit(false);
            }}
            loading={guaranteedSaveDraftLoading}
          >
            保存
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleSubmit(true);
            }}
            loading={guaranteedCreateLoading || guaranteedUpdateLoading}
          >
            保存并提交
          </Button>
        </div>
      }
    >
      <Spin
        spinning={
          guaranteedSaveDraftLoading ||
          guaranteedCreateLoading ||
          guaranteedUpdateLoading ||
          guaranteedDetailLoading ||
          bankAccountQueryLoading
        }
      >
        <div className={styles['form-page']}>
          <Form>
            <div className={styles['form-page-center']}>
              <DetailTitle title="基本信息" />
              {/* 补充协议进入的话展示原保量合同编号  */}
              {detail?.originalCoopGuaranteedNo ? (
                <Row gutter={24}>
                  <p style={{ padding: '0 0 16px 16px' }}>
                    <span style={{ color: 'rgba(0,0,0,0.8)' }}>原保量合同编号: </span>
                    <span style={{ marginLeft: '2px' }}>
                      {detail?.originalCoopGuaranteedNo || '-'}
                    </span>
                  </p>
                </Row>
              ) : (
                <></>
              )}
              <Row gutter={24} style={{ marginTop: '16px' }}>
                <Col span={6}>
                  <Form.Item
                    label="保量名称"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('name', {
                      rules: [
                        {
                          required: true,
                          message: '请填写保量名称',
                        },
                      ],
                    })(<Input maxLength={50} placeholder="请填写保量名称" />)}
                    <p style={{ color: '#999999', fontSize: '12px', lineHeight: '14px' }}>
                      建议命名规则: 事业部+商家主体/品牌/品类+合作开始日期
                    </p>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="保量类型"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {/* 根据选择的保留类型显示保量条件配置内容 */}
                    {getFieldDecorator('type', {
                      rules: [
                        {
                          required: true,
                          message: '请填写保量类型',
                        },
                      ],
                    })(
                      <Select allowClear placeholder="请选择">
                        {Object.keys(GuaranteedTypeEnum).map((item) => (
                          <Select.Option value={item} key={item}>
                            {GuaranteedTypeEnum[item as keyof typeof GuaranteedTypeEnum]}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="合作时间"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('coopTime', {
                      rules: [
                        {
                          required: true,
                          message: '请填写合作时间',
                        },
                      ],
                    })(<RangePicker allowClear></RangePicker>)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="平台" required labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {getFieldDecorator('platform', {
                      rules: [
                        {
                          required: true,
                          message: '请填写平台',
                        },
                      ],
                    })(
                      <Select
                        placeholder="请选择平台"
                        allowClear
                        onChange={(value: any) => {
                          handleChangeClearLive();
                          handleBank(value, 'platform');
                        }}
                      >
                        {plantformListAll?.map((item) => (
                          <Select.Option value={item.value} key={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="事业部"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('deptInfoDTOS', {
                      rules: [
                        {
                          required: true,
                          message: '请填写事业部',
                        },
                      ],
                    })(
                      <Select
                        placeholder="请选择事业部"
                        allowClear
                        loading={departmentListLoading}
                        mode="multiple"
                        filterOption={false}
                        onChange={handleChangeClearLive}
                        labelInValue={true}
                        maxTagCount={1}
                      >
                        {departmentList?.map((item) => (
                          <Select.Option value={item.id} key={item.id}>
                            {item.deptName}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label={
                      <span>
                        <span>机构主体</span>
                        {/* <Popover
                          title="提示"
                          content={
                            <div>
                              <p>{`机构主体与直播间有对应关系，如：`}</p>
                              <p style={{ fontWeight: 'bold' }}>
                                杭抖/北抖的直播间请选择交友优选；杭州综合事业部请选择智慧科技；
                              </p>
                              <p>不能跨机构多选</p>
                            </div>
                          }
                        >
                          <Icon
                            style={{ fontSize: '14px', marginLeft: '2px' }}
                            type="question-circle"
                          />
                        </Popover> */}
                      </span>
                    }
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('institutionIds', {
                      rules: [
                        {
                          required: true,
                          message: '请填写机构主体',
                        },
                        {
                          validator: (rule: any, value: any, callback: any) => {
                            if (value && value?.length > 5) {
                              callback('最多选择5个');
                              return;
                            }
                            callback();
                          },
                        },
                      ],
                    })(
                      <Select
                        placeholder="请填写机构主体(最多5个)"
                        // placeholder="请填写机构主体"
                        allowClear
                        loading={institutionListLoading}
                        showSearch
                        onSearch={debounceInstitutionListRun}
                        onChange={(value: string) => {
                          if (!value) {
                            debounceInstitutionListRun();
                          }
                          handleChangeClearLive();
                          // handleBank(value, 'institutionId');
                          handleSelectInstitution(value);
                        }}
                        mode="multiple"
                        filterOption={false}
                        onBlur={() => {
                          debounceInstitutionListRun();
                        }}
                      >
                        {institutionList?.map((item) => (
                          <Select.Option value={item.institutionId} key={item.institutionId}>
                            {item.institutionName}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>

                {/* </Row>
              <Row gutter={24}> */}
                <Col span={6}>
                  <Form.Item label="商务" required labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
                    {getFieldDecorator('bpId', {
                      rules: [
                        {
                          required: true,
                          message: '请选择商务',
                        },
                      ],
                    })(<EmployeeSelect bizRoleType="BUSINESS" bpName={bpName} />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label={'直播间类型'}
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('liveRoomPossessType', {
                      rules: [
                        {
                          required: true,
                          message: '请选择直播间',
                        },
                      ],
                    })(
                      <Select placeholder="请选择" allowClear>
                        {LIVE_ROOM_POSSESS_TYPE_ENUM_List?.map((item) => (
                          <Select.Option key={item.value} value={item?.value}>
                            {item?.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>

                <Col span={6}>
                  <Form.Item
                    label="发票内容"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('invoiceContent', {
                      rules: [
                        {
                          required: true,
                          message: '请选择发票内容',
                        },
                      ],
                    })(
                      <Select allowClear placeholder="请选择">
                        {invoiceContentList?.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="发票税率"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('invoiceRate', {
                      rules: [
                        {
                          required: true,
                          message: '请选择发票税率',
                        },
                      ],
                    })(
                      <Select allowClear placeholder="请选择">
                        {invoiceRateList?.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="合同描述"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('description', {
                      rules: [
                        {
                          required: true,
                          message: '请填写合同描述',
                        },
                      ],
                    })(<Input maxLength={500} placeholder="请填写合同描述" />)}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                {liveRoomPossessType === LIVE_ROOM_POSSESS_TYPE_ENUM.INCLUDE ? (
                  <Col span={6}>
                    {/* 直播间是根据权限,平台,事业部,机构主体显示的 */}
                    <Form.Item
                      label="直播间"
                      required
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                    >
                      {getFieldDecorator('liveRoomInfos', {
                        rules: [
                          {
                            required: true,
                            message: '请选择直播间',
                          },
                        ],
                      })(
                        <LiveModal
                          platform={platform}
                          deptInfoDTOS={deptInfoDTOS}
                          institutionIds={institutionIds}
                          onRef={LiveRef}
                        />,
                      )}
                    </Form.Item>
                  </Col>
                ) : (
                  <></>
                )}
              </Row>
            </div>
            <div className={styles['form-page-center']}>
              <Title>保量条件配置</Title>
              <div style={{ marginTop: '16px' }}>
                <ConditionConfig ref={ConditionConfigRef} type={type} platform={platform} />
              </div>
            </div>
            <div className={styles['form-page-center']}>
              <DetailTitle title="目标及达成" />
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="基础佣金"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('brandFee', {
                      rules: [
                        {
                          required: true,
                          message: '请填写基础佣金',
                        },
                        {
                          validator: handleValidator,
                        },
                      ],
                    })(
                      <InputNumber
                        placeholder="请填写基础佣金"
                        precision={2}
                        style={{ width: '100%' }}
                        max={999999999.99}
                      />,
                    )}
                    {!isNullOrUndefined(brandFee) && brandFee < 1000 ? (
                      <p style={{ color: 'red', fontSize: '12px', lineHeight: '14px' }}>
                        录入金额较低，请留意金额单位为元
                      </p>
                    ) : (
                      <></>
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label={<span className={styles['label-box']}>基础佣金最晚支付日期</span>}
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('brandFeeLatestPaymentTime', {
                      rules: [
                        {
                          required: true,
                          message: '请选择',
                        },
                      ],
                    })(<DatePicker style={{ width: '100%' }} />)}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="目标GMV"
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('guaranteedGmv', {
                      rules: [
                        {
                          required: true,
                          message: '请填写目标GMV',
                        },
                        {
                          validator: handleValidator,
                        },
                      ],
                    })(
                      <InputNumber
                        placeholder="请填写目标GMV"
                        precision={2}
                        style={{ width: '100%' }}
                        max={999999999.99}
                      />,
                    )}
                    {!isNullOrUndefined(guaranteedGmv) && guaranteedGmv < 1000 ? (
                      <p style={{ color: 'red', fontSize: '12px', lineHeight: '14px' }}>
                        录入金额较低，请留意金额单位为元
                      </p>
                    ) : (
                      <></>
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label={<span className={styles['label-box']}>达成计算方式</span>}
                    required
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                  >
                    {getFieldDecorator('achievedCalculationType', {
                      rules: [
                        {
                          required: true,
                          message: '请选择达成计算方式',
                        },
                      ],
                    })(
                      <Select
                        placeholder="请选择"
                        allowClear
                        onChange={(value: any) => {
                          if (value === 'BY_PAYMENT') {
                            form.setFieldsValue({
                              payDurationType: undefined,
                            });
                          } else {
                            form.setFieldsValue({
                              payDurationType: PAY_DAY.SETTLEMENT,
                            });
                          }
                        }}
                      >
                        {ACHIEVED_CALCULATION_TYPE_ENUM_List?.map((item) => (
                          <Select.Option key={item.value} value={item.value}>
                            {item.label}
                          </Select.Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                {achievedCalculationType ? (
                  achievedCalculationType === ACHIEVED_CALCULATION_TYPE_ENUM.BY_PAYMENT ? (
                    <Col span={6}>
                      {/* PAY_DAY_LIST */}
                      <Form.Item
                        label="支付天数"
                        required
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}
                      >
                        {getFieldDecorator('payDurationType', {
                          rules: [
                            {
                              required: true,
                              message: '请选择支付天数',
                            },
                          ],
                        })(
                          <Select placeholder="请选择" allowClear>
                            {PAY_DAY_LIST?.map((item) => (
                              <Select.Option value={item.value} key={item.value}>
                                {item.label}
                              </Select.Option>
                            ))}
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                  ) : (
                    <Col span={6}>
                      <Form.Item
                        label="支付天数"
                        required
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}
                      >
                        {getFieldDecorator('payDurationType', {
                          rules: [
                            {
                              required: true,
                              message: '请选择支付天数',
                            },
                          ],
                          initialValue: PAY_DAY.SETTLEMENT,
                        })(
                          <Select placeholder="请选择" allowClear disabled>
                            <Select.Option value={PAY_DAY.SETTLEMENT}>
                              {PAY_DAY_NAME[PAY_DAY.SETTLEMENT]}
                            </Select.Option>
                          </Select>,
                        )}
                      </Form.Item>
                    </Col>
                  )
                ) : (
                  <></>
                )}
              </Row>
              <Row gutter={24}>
                <Col span={18}>
                  <Form.Item
                    label="目标GMV未能达成计算规则"
                    required
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 19 }}
                    labelAlign="left"
                  >
                    {getFieldDecorator('guaranteedGmvFailAchievedRule', {
                      rules: [
                        {
                          required: true,
                          message: '请选择',
                        },
                      ],
                    })(
                      <Radio.Group>
                        {GUARANTEED_GMV_FAIL_ACHIEVED_RULE_ENUM_List?.map((item) => (
                          <Radio
                            style={{ display: 'flex', paddingTop: '12px', alignItems: 'center' }}
                            value={item.value}
                            key={item.value}
                          >
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              {item.label}
                              {guaranteedGmvFailAchievedRule === 'EXTEND_COOPERATION_PERIOD' &&
                              item.value === 'EXTEND_COOPERATION_PERIOD' ? (
                                <>
                                  {/* <Col span={12}> */}
                                  <Form.Item
                                    label=""
                                    required
                                    labelCol={{ span: 4 }}
                                    wrapperCol={{ span: 20 }}
                                    // style={{ marginTop: '16px' }}
                                  >
                                    {getFieldDecorator('coopExtendEndTime', {
                                      rules: [
                                        {
                                          required: true,
                                          message: '请选择',
                                        },
                                      ],
                                    })(<DatePicker style={{ width: '200px' }} />)}
                                  </Form.Item>
                                  <p
                                    style={{
                                      color: '#999999',
                                      fontSize: '12px',
                                      lineHeight: '14px',
                                    }}
                                  >
                                    （以下称“延长期”），如延长期内仍未达成目标GMV的，乙方向甲方退还未消耗的基础佣金
                                  </p>
                                  {/* </Col> */}
                                </>
                              ) : (
                                <></>
                              )}
                            </div>
                          </Radio>
                        ))}
                      </Radio.Group>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={18}>
                  <Form.Item
                    label="目标GMV提前达成计算规则"
                    required
                    labelCol={{ span: 5 }}
                    wrapperCol={{ span: 19 }}
                    labelAlign="left"
                  >
                    {getFieldDecorator('guaranteedGmvAdvanceAchievedRule', {
                      rules: [
                        {
                          required: true,
                          message: '请选择',
                        },
                      ],
                    })(
                      <Radio.Group>
                        {GUARANTEED_GMV_ADVANCE_ACHIEVED_RULE_ENUM_List?.map((item) => (
                          <Radio
                            value={item?.value}
                            key={item?.value}
                            style={{ paddingTop: '12px' }}
                          >
                            {item.label}
                          </Radio>
                        ))}
                      </Radio.Group>,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                {guaranteedGmvAdvanceAchievedRule ===
                GUARANTEED_GMV_ADVANCE_ACHIEVED_RULE_ENUM.GO_ON ? (
                  <>
                    <Col span={6}>
                      <Form.Item
                        label={<span className={styles['label-box']}>激励佣金比例</span>}
                        required
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}
                      >
                        {getFieldDecorator('stimulateCommissionRate', {
                          rules: [
                            {
                              required: true,
                              message: '请选择',
                            },
                          ],
                        })(
                          <InputNumber
                            placeholder="请填写激励佣金比例"
                            precision={2}
                            style={{ width: '100%' }}
                            max={100}
                            min={0.1}
                            formatter={(value) => `${value}%`}
                            parser={(value) => value.replace('%', '')}
                          />,
                        )}
                      </Form.Item>
                    </Col>
                  </>
                ) : (
                  <></>
                )}
              </Row>
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    label={<span className={styles['label-box']}>佣金比例超出</span>}
                  >
                    {/* <span style={{ color: 'rgba(0,0,0,0.8)' }}>线上佣金比例超出</span> */}
                    {getFieldDecorator('commissionRate')(
                      <InputNumber
                        placeholder="请填写"
                        precision={2}
                        style={{ width: '80px', margin: '0 2px 0 0' }}
                        max={100}
                        min={0}
                        formatter={(value) => `${value}%`}
                        parser={(value) => value.replace('%', '')}
                      />,
                    )}
                    <span style={{ color: 'rgba(0,0,0,0.8)', fontSize: '12px' }}>
                      不计入保量计划
                    </span>
                  </Form.Item>
                </Col>
              </Row>
            </div>
            <div className={styles['form-page-center']}>
              <Title>付款方式及结算周期</Title>
              <div style={{ marginTop: '16px' }}>
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item
                      label="付款方式"
                      required
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                    >
                      {getFieldDecorator('paymentType', {
                        rules: [
                          {
                            required: true,
                            message: '请选择付款方式',
                          },
                        ],
                      })(
                        <Radio.Group>
                          {PAYMENT_TYPE_ENUM_List?.map((item) => (
                            <Radio value={item.value} key={item.value}>
                              {item.label}
                            </Radio>
                          ))}
                        </Radio.Group>,
                      )}
                    </Form.Item>
                  </Col>
                  {/* 付款方式 选择代付显示一下内容 */}
                  {paymentType === 'THIRD_PARTY' ? (
                    <>
                      <Col span={6}>
                        <Form.Item
                          label="代付方名称"
                          required
                          labelCol={{ span: 8 }}
                          wrapperCol={{ span: 16 }}
                        >
                          {getFieldDecorator('thirdPartyPayerName', {
                            rules: [
                              {
                                required: true,
                                message: '请填写代付方名称',
                              },
                            ],
                          })(<Input maxLength={50} placeholder="请填写" />)}
                        </Form.Item>
                      </Col>
                      {/* 代付方统一社会信用代码 */}
                      <Col span={6}>
                        <Form.Item
                          label={
                            <span className={styles['label-box']}>代付方统一社会信用代码</span>
                          }
                          required
                          labelCol={{ span: 8 }}
                          wrapperCol={{ span: 16 }}
                        >
                          {getFieldDecorator('thirdPartyPayerUscc', {
                            rules: [
                              {
                                required: true,
                                message: '请填写',
                              },
                            ],
                          })(<Input maxLength={50} placeholder="请填写" />)}
                        </Form.Item>
                      </Col>
                    </>
                  ) : (
                    <></>
                  )}
                </Row>
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item
                      label="结算周期"
                      required
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                    >
                      {getFieldDecorator('settlementIntervalType', {
                        rules: [
                          {
                            required: true,
                            message: '请选择结算周期',
                          },
                        ],
                      })(
                        <Select allowClear placeholder="请选择">
                          {SETTLEMENT_INTERVAL_TYPE_ENUM_LIST?.map((item) => (
                            <Select.Option key={item?.value} value={item?.value}>
                              {item?.label}
                            </Select.Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item
                      label="指定收款机构"
                      required
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                    >
                      {getFieldDecorator('appointReceiptInstitutionId', {
                        rules: [
                          {
                            required: true,
                            message: '请填写指定收款机构',
                          },
                        ],
                      })(
                        <Select
                          placeholder="请填写指定收款机构"
                          allowClear
                          showSearch
                          onChange={(value: string) => {
                            handleBank(value, 'appointReceiptInstitutionId');
                          }}
                          filterOption={false}
                        >
                          {selectInstitution?.map((item) => (
                            <Select.Option value={item.institutionId} key={item.institutionId}>
                              {item.institutionName}
                            </Select.Option>
                          ))}
                        </Select>,
                      )}
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="银行账号"
                      required
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                    >
                      {bankValue?.accountNo || '-'}
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="开户银行"
                      required
                      labelCol={{ span: 8 }}
                      wrapperCol={{ span: 16 }}
                    >
                      <p style={{ lineHeight: '24px', paddingTop: '8px' }}>
                        {bankValue?.bankName || '-'}
                      </p>
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </div>
          </Form>
          <div style={{ height: '60px' }}></div>
        </div>
      </Spin>
    </PageLayout>
  );
};

export default Form.create()(FormPage);
