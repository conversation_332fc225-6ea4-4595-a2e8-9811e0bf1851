import React, { useEffect, useState } from 'react';
import styles from '../index.module.less';
import { Form, message, Button, Spin } from 'antd';
import {
  guaranteedDetail,
  GuaranteedDetailResult,
  guaranteedUpdate,
  GuaranteedUpdateRequest,
} from '@/services/yml/quality-assurance-cooperation';
import {
  BasicInfo,
  RuleInfo,
  CompleteInfo,
  ConfirmInfo,
  PayType,
  OperationLog,
} from './conponents/index';
import { history, AuthWrapper } from 'qmkit';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import moment from 'moment';
import { DetailTitle } from '@/pages/report-sheet/components';
import { GuaranteedTypeKeysEnum } from '../dataSource';
import PageLayout from '@/components/PageLayout/index';
import { useRequest } from 'ahooks';
import { pageByCoopId, PageByCoopIdResult } from '../services/yml';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import GuaranteedExtensionLog from './conponents/GuaranteedExtensionLog';
import { useDispatch, useSelector } from 'react-redux';
import _ from 'lodash';

interface InfoEditProps {
  form?: WrappedFormUtils;
}
type EditType = 'WAIT_START' | 'IN_COOPERATION' | ''; //待合作编辑/合作中编辑/空
const InfoEdit: React.FC<InfoEditProps> = (props) => {
  const { form } = props;
  const [editType, setEditType] = useState<EditType>('');
  const [info, setInfo] = useState<GuaranteedDetailResult>({});
  const [loading, setLoading] = useState(false);
  const [pageByCoopIdResult, setGE] = useState<PageByCoopIdResult>();
  const { run, loading: gELoading } = useRequest(pageByCoopId, {
    manual: true,
    onSuccess({ res }) {
      if (res?.result) {
        setGE(res?.result);
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const getInfo = async () => {
    setLoading(true);
    // console.log('详情id', history, location.search.split('?')[1].split('=')[1], form);
    const id = location.search.split('?')[1].split('=')[1];
    await guaranteedDetail({ id: id })
      .then(({ res }) => {
        // console.log(res);
        if (res.code === '200') {
          res.result && setInfo(res?.result);
          // setEditType(res?.result?.status);
          run({ size: 10, current: 1, cooperationId: res?.result?.id });
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getInfo();
  }, []);

  const handleGoDetail = (id?: string) => {
    history.push('/quality-assurance-cooperation-list/form?id=' + id);
  };

  return (
    <PageLayout>
      <div className={styles.qualityBox}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            backgroundColor: '#fff',
            boxShadow: '0 1px 4px 0 rgba(0,21,41,.12)',
            padding: '14px 16px 14px',
            position: 'sticky',
            top: '2px',
            left: '0px',
            zIndex: 80,
          }}
        >
          <div style={{ color: '#111', fontSize: '18px', lineHeight: '24px', height: '24px' }}>
            {info?.name || '-'}
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <AuthWrapper functionName="quality-assurance-cooperation-edit">
              {/* 合作状态是暂存的时候可以编辑 */}
              {info?.status === 'DRAFT' ? (
                <Button
                  type="primary"
                  style={{ marginRight: '6px' }}
                  onClick={() => {
                    handleGoDetail(info?.id);
                  }}
                >
                  编辑
                </Button>
              ) : (
                <></>
              )}
            </AuthWrapper>
            {/* <AuthWrapper functionName="quality-assurance-cooperation-edit">
              {!['FINISHED', 'DRAFT'].includes(info?.status as '') &&
              info?.type === GuaranteedTypeKeysEnum.GOODS_KEYWORD ? (
                <Button
                  type="primary"
                  style={{ marginRight: '6px' }}
                  onClick={() => {
                    ///quality-assurance-cooperation-list/supplierMainEdit
                    history.push(
                      '/quality-assurance-cooperation-list/supplierMainEdit?id=' + info?.id,
                    );
                  }}
                >
                  编辑
                </Button>
              ) : (
                <></>
              )}
            </AuthWrapper> */}
          </div>
        </div>
        <section className={styles.infoContainer}>
          <Spin spinning={loading}>
            <div className={styles.titleContainer} style={{ flex: 1, paddingBottom: '16px' }}>
              {/* <div className={styles.title}>
              <div className={styles.titleIcon} />
              <div className={styles.titleText}>基本信息</div>
            </div> */}
              <DetailTitle title="基本信息" />
              <BasicInfo info={info} editType={editType} form={form} />
            </div>
            <div className={styles.titleContainer} style={{ flex: 1, paddingBottom: '16px' }}>
              {/* <div className={styles.title}>
              <div className={styles.titleIcon} />
              <div className={styles.titleText}>保量条件配置</div>
            </div> */}
              <DetailTitle title="保量条件配置" />
              <ConfirmInfo info={info}></ConfirmInfo>
            </div>
            <div className={styles.titleContainer} style={{ flex: 1, paddingBottom: '16px' }}>
              {/* <div className={styles.title}>
              <div className={styles.titleIcon} />
              <div className={styles.titleText}>目标及达成</div>
            </div> */}
              <DetailTitle title="目标及达成" />
              <RuleInfo info={info} editType={editType} form={form} />
            </div>
            <div className={styles.titleContainer} style={{ flex: 1, paddingBottom: '16px' }}>
              {/* <div className={styles.title}>
              <div className={styles.titleIcon} />
              <div className={styles.titleText}>付款方式及结算周期</div>
            </div> */}
              <DetailTitle title="付款方式及结算周期" />
              <PayType info={info}></PayType>
            </div>
            {info?.status !== 'DRAFT' ? (
              <div className={styles.titleContainer} style={{ flex: 1, paddingBottom: '16px' }}>
                {/* <div className={styles.title}>
                <div className={styles.titleIcon} />
                <div className={styles.titleText}>保量完成情况</div>
              </div> */}
                <DetailTitle title="保量完成情况" />
                <CompleteInfo info={info} editType={editType} form={form} />
              </div>
            ) : (
              <></>
            )}
            {!!pageByCoopIdResult?.records?.length && (
              <div className={styles.titleContainer} style={{ flex: 1, paddingBottom: '16px' }}>
                <div className={styles.title}>
                  <div className={styles.titleIcon} />
                  <div className={styles.titleText}>延期记录</div>
                </div>
                <GuaranteedExtensionLog
                  pageByCoopIdResult={pageByCoopIdResult}
                  loading={gELoading}
                  getList={run}
                  id={info?.id}
                />
              </div>
            )}
            <div className={styles.titleContainer} style={{ flex: 1, paddingBottom: '16px' }}>
              <div className={styles.title}>
                <div className={styles.titleIcon} />
                <div className={styles.titleText}>操作日志</div>
              </div>
              <OperationLog bizOrderId={info!.id!} />
            </div>

            {/* <p className={styles.bottomBtnGroup}>
          <div>
            <Button
              onClick={() => {
                history.go(-1);
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                onSubmit();
              }}
              style={{ marginLeft: '12px' }}
            >
              保存
            </Button>
          </div>
        </p> */}
          </Spin>
        </section>
      </div>
    </PageLayout>
  );
};
export default Form.create({ name: 'InfoEdit' })(InfoEdit);
