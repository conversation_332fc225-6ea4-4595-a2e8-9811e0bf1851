import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Form, Select, Input, Modal, Button, message, Alert } from 'antd';
import UserList from '@/components/UserList';
import PlatformSupplier from '@/components/PlatformSupplier';
import InvestmentPlanList from '@/components/InvestmentPlanList';
import { newPlatformSupplier } from '@/services/yml/supplier/index';
import { useDeptList } from '../hooks/index';
import { createLink } from '@/services/yml/goods-assorting/index';
import '../index.module.less';
import moment from 'moment';
import copy from 'copy-to-clipboard';
import EmployeeSelect from './EmployeeSelect';
import { WrappedFormUtils } from 'antd/lib/form/Form';

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 5 },
};
interface FormProps {
  form: WrappedFormUtils; // 根据实际情况替换为正确的类型
  onRef: any; // 根据实际情况替换为正确的类型
  onSearch?: () => void;
}
const NewPlatformSupplier: React.FC<FormProps> = (props) => {
  const { form, onRef, onSearch } = props;
  const { getFieldDecorator, getFieldsValue } = form;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const { deptList } = useDeptList(); //获取事业部数据
  const showModal = () => {
    setIsModalOpen(true);
    setLink('');
  };

  const onCreateLink = () => {
    form.validateFields((errors, values) => {
      if (!errors) {
        setLoading(true);
        const params = {
          invitee: values?.supplier?.label,
          inviteeId: values?.supplier?.key,
          departmentId: values?.department?.key,
          departmentName: values?.department?.label,
          investmentId: values?.investmentId,
          inviterId: values?.user?.key,
          inviter: values?.user?.label,
        };
        createLink({ ...params }).then(({ res }) => {
          if (res.code === '200') {
            message.success('新建成功');
            setLink(
              `${__ENVINFO__.crmHost}platform-supplier-newGoods?code=${res?.result.code || ''}`,
            );
            setEndtime(moment(res?.result?.validEndTime).format('YYYY-MM-DD'));
            // setIsModalOpen(false);
            // platform-supplier-newGoods
            // onSearch();
          } else {
            message.error(res.message);
          }
        });
        setLoading(false);
      }
    });
    // setIsModalOpen(false);
  };
  const handleOk = () => {
    setIsModalOpen(false);
  };
  const [link, setLink] = useState('');
  const onCopy = () => {
    if (copy(`${link}`)) {
      message.success('复制成功');
    }
  };
  const handleCancel = () => {
    setIsModalOpen(false);
    setEndtime('');
    form.resetFields();
  };
  const [endTime, setEndtime] = useState('');
  const [images, setImages] = useState(null);
  const onReset = () => {
    setEndtime('');
    form.resetFields();
  };
  useImperativeHandle(onRef, () => ({
    open: showModal,
  }));
  const [deptId, setDeptId] = useState();
  useEffect(() => {
    setDeptId(form?.getFieldsValue()?.department?.key);
    form?.setFieldsValue({ user: undefined });
  }, [form?.getFieldsValue()?.department]);
  return (
    <div>
      <Modal
        title="商机链接"
        visible={isModalOpen}
        width={500}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <Form.Item {...formItemLayout} label="商家主体名称">
          {getFieldDecorator('supplier', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(<PlatformSupplier labelInValue={true} providerType={3} className="modalInput300" />)}
        </Form.Item>
        <Form.Item {...formItemLayout} label="事业部">
          {getFieldDecorator('department', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(
            <Select className="modalInput300" labelInValue allowClear>
              {deptList.map((i) => {
                return <Option value={i.value}>{i.label}</Option>;
              })}
            </Select>,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="商务">
          {getFieldDecorator('user', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(
            <EmployeeSelect
              linkage={true}
              labelInValue={true}
              className="modalInput300"
              bizRoleType="BUSINESS"
              deptId={deptId}
            />,
          )}
        </Form.Item>
        <Form.Item {...formItemLayout} label="招商计划">
          {getFieldDecorator('investmentId', {
            rules: [
              {
                required: true,
                message: '请输入',
              },
            ],
          })(
            <InvestmentPlanList
              buIds={
                form.getFieldValue('department')?.key && [form.getFieldValue('department')?.key]
              }
              className="modalInput300"
            />,
          )}
        </Form.Item>
        <div className="cardLink">
          {!endTime ? (
            <Button loading={loading} onClick={onCreateLink} type="primary">
              生成链接
            </Button>
          ) : (
            <div>
              {/* saveTips */}
              <p className="mb-10 font-body-emphasize color-font-neutral-black-2">{link}</p>
              <div className="flex justify-between items-center">
                <p>请确保完成复制后关闭弹窗</p>
                <div>
                  <Button
                    type="primary"
                    size="small"
                    style={{ marginRight: '8px' }}
                    onClick={onCopy}
                  >
                    复 制
                  </Button>
                  <Button size="small" onClick={onReset}>
                    更改信息
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default Form.create({ name: 'new_supplier' })(NewPlatformSupplier);
