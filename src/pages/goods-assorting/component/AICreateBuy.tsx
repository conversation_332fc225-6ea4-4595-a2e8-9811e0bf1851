import { Button, Modal } from 'antd';
import React, { useContext } from 'react';
import { AuthWrapper } from 'qmkit';
import GoodsAssortingContext from '../GoodsAssortingContext';
import { message } from 'antd';
import { batchCreateScript } from '../services/yml';
import { useRequest } from 'ahooks';
import { usePoint } from '@/components/OralBroadcast/src/hooks';

interface AICreateBuyProps {
  onSearch: () => void;
}

//ai生成
const AICreateBuy: React.FC<AICreateBuyProps> = ({ onSearch }) => {
  const { infoIdList, infoList } = useContext(GoodsAssortingContext);

  const { goodsAICreate } = usePoint();

  const { run: batchCreateScriptRun, loading: batchCreateScriptLoading } = useRequest(
    batchCreateScript,
    {
      manual: true,
      onSuccess({ res }) {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        message.success('操作成功');
        onSearch();
      },
    },
  );

  const handleClick = () => {
    console.log(infoIdList, infoList);
    goodsAICreate();
    if (!infoIdList.length) {
      message.warning('请选择商品');
      return;
    }

    if (infoIdList.length > 10) {
      message.warning('最多选择10个商品');
      return;
    }

    Modal.confirm({
      title: '请确认是否通过AI生成口播稿?',
      content: 'AI生成的口播稿信息仅供参考,并且生成存在一定时间加载,请确认是否执行该操作',
      onOk: () => {
        // console.log('确认');
        // 调用接口
        const selectGoodsPoolNos = infoList
          .filter((item: any) => infoIdList.includes(item?.id))
          .map((item) => item?.no);
        console.log('🚀 ~ handleClick ~ selectGoodsPoolNos:', selectGoodsPoolNos);
        batchCreateScriptRun({
          selectGoodsPoolNos,
        });
      },
    });
  };
  return (
    <AuthWrapper functionName="f_goods-assorting_ai_create_buy">
      <Button
        type="primary"
        size="small"
        style={{ marginLeft: '8px' }}
        onClick={handleClick}
        loading={batchCreateScriptLoading}
      >
        AI生成口播稿
      </Button>
    </AuthWrapper>
  );
};

export default AICreateBuy;

// 2025-06-16zhouby -> cursor ai结尾共生成50行代码
