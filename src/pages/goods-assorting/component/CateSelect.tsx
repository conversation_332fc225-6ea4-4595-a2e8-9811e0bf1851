// /befriend-service-goods/open/goods/cate/queryForTalentGoods
import React, { useEffect, useRef, useState } from 'react';
import { platformList, sortList } from '../constant';
import { Button, Row, Col, Input, Select } from 'antd';
import { getCateTree } from '@/services/goods/index';

const CateSelect = (props) => {
  const { value, onChange } = props;
  const [dataList, setDataList] = useState([]); //直播间列表
  const getList = () => {
    getCateTree().then((res) => {
      console.log(res);
      if (res?.res?.code === '200') {
        const resData = res?.res?.result;
        resData?.length > 0 ? setDataList([...resData]) : setDataList([]);
      } else {
        setDataList([]);
      }
    });
  };
  useEffect(() => {
    getList();
  }, []);

  return (
    <Select value={value} allowClear onChange={onChange} placeholder="请选择">
      {dataList?.map((i, index) => {
        return (
          <Option value={i?.cateId} key={index}>
            {i?.cateName}
          </Option>
        );
      })}
    </Select>
  );
};
export default CateSelect;
