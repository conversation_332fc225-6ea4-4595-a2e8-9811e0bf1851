import React, { Ref, useEffect, useMemo, useState } from 'react';
import { Select, Form, InputNumber, message, Input } from 'antd';
import {
  JoinLiveRoundRequest,
  liveroomList,
  LiveroomListRequest,
  LiveroomListResult,
} from '@/services/yml/goods-assorting/index';
import {
  getServiceType,
  GetServiceTypeResult,
} from '@/services/yml/live-service-type-configuration/index';
import styles from '../index.module.less';
import EmployeeSelect from './EmployeeSelect';
import { BatchAddSelectionRoundRequest } from '../formSearch/BatchAddSelectionRound/fetchApi';
import { FormComponentProps } from 'antd/es/form';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  getLabelConfigInfo,
  GetLabelConfigInfoRequest,
  GetLabelConfigInfoResult,
} from '@/pages/setting/selection-tag-configuration/services';
import { checkRedEnvelopeDept } from '../services/yml';
import { useRequest } from 'ahooks';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';

export interface LiveRoomSelectProps
  extends FormComponentProps<BatchAddSelectionRoundRequest & JoinLiveRoundRequest> {
  platformSource?: string;
  id?: string;
  deptId?: string;
  setLiveIds?: (ids: string) => void;
  clear?: boolean;
  onRef: Ref<LiveRoomSelectRefProps>;
  bpId?: string;
  bpName?: string;
  getIsrequired?: (data: GetServiceTypeResult) => void;
  info?: any; // 判断是不是单个加入
  liveRoomId?: string; // 添加直播间ID
}
export type LiveRoomSelectRefProps = {
  form: LiveRoomSelectProps['form'];
  getLabelOptionList: () => JoinLiveRoundRequest['labelConfigInfoList'];
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 12,
  },
};
const formItemStyle: React.CSSProperties = {
  minWidth: '320px',
};
const LiveRoomSelect = React.forwardRef<LiveRoomSelectRefProps, LiveRoomSelectProps>(
  ({ platformSource, deptId, setLiveIds, clear, id, form, bpId, bpName, onRef, liveRoomId }) => {
    const [liveList, setLiveList] = useState<LiveroomListResult>(); //直播间列表
    const [childrenMsg, setChildMsg] = useState<GetServiceTypeResult>();
    const [liveServiceTypeName, setLiveServiceTypeName] = useState<string>('');
    const getLiveRoomList = () => {
      liveroomList({
        platformEnum: platformSource as LiveroomListRequest['platformEnum'],
        deptId,
      }).then((res) => {
        if (res?.res?.code === '200') {
          res?.res?.result.length > 0 ? setLiveList(res?.res?.result) : setLiveList([]);
        } else {
          setLiveList([]);
        }
      });
    };
    const [liveIds, setIds] = useState<string>();
    const [isShowRed, setIsShowRed] = useState<boolean>(false);
    const { run: checkRedEnvelopeDeptRun } = useRequest(checkRedEnvelopeDept, {
      manual: true,
      onSuccess({ res }) {
        if (!res?.success) {
          message.warning(res?.message || '网络异常');
          return;
        }
        setIsShowRed(res?.result);
      },
    });
    const { codeList: FUNDING_PARTY_LIST } = useCode(CODE_ENUM.FUNDING_PARTY, {
      able: true,
    });
    useEffect(() => {
      // console.log('platformSource, deptId, id', platformSource, deptId, id);
      if (platformSource && deptId) {
        getLiveRoomList();
        checkRedEnvelopeDeptRun({
          platform: platformSource,
          deptId,
        });
      }
    }, [platformSource, deptId]);

    // 在组件加载时，如果有传入liveRoomId，尝试设置初始值
    useEffect(() => {
      if (liveRoomId && liveList?.length) {
        const hasRoom = liveList.some((room) => room.id === liveRoomId);
        if (hasRoom) {
          selectLive(liveRoomId);
          setIds(liveRoomId);
          form.setFieldsValue({ liveIds: liveRoomId });
        }
      }
    }, [liveRoomId, liveList]);

    useEffect(() => {
      liveIds && setLiveIds && setLiveIds(liveIds);
    }, [liveIds]);
    useEffect(() => {
      setIds('');
    }, [clear]);
    // useEffect(() => {
    //   if (id && clear && deptId && info) {
    //     // 调用接口
    //     checkRedEnvelopeDeptRun({
    //       platform: info?.platformSource,
    //       deptId,
    //     });
    //   }
    //   // getBusinessInfo();
    // }, [id, clear, deptId, info]);

    const selectLive = (val: string) => {
      setIds(val);
      setLiveServiceTypeName('');
      getServiceType({ deptId: deptId, liveRoomId: val }).then((res) => {
        if (res?.res?.code === '200') {
          setChildMsg(res?.res?.result);
        }
      });
    };
    const getLabelOptionList = () => {
      const formValues = form.getFieldsValue();
      const findBetweenDashes = (str: string) => {
        const parts = str.split('----');
        if (parts.length > 1) {
          return parts[1];
        }
        return undefined;
      };
      return Object.keys(formValues)
        .filter((item) => item.includes('labelOpTion_label_value'))
        .map((item) => ({
          labelName: findBetweenDashes(item),
          labelOptionId: formValues[item]?.key ?? null,
          labelOptionName: formValues[item]?.label ?? null,
        }));
    };
    React.useImperativeHandle(onRef, () => ({
      form,
      getLabelOptionList,
    }));
    const [labelOpTionList, setLabelOptionList] = useState<GetLabelConfigInfoResult>([]);
    const getLabelItem = async (params: GetLabelConfigInfoRequest) => {
      const result = await responseWithResultAsync({
        request: getLabelConfigInfo,
        params,
      });
      console.log(result);
      setLabelOptionList(result ?? []);
    };
    useEffect(() => {
      const liveRoomId = form?.getFieldsValue().liveIds;
      liveRoomId && getLabelItem({ deptId, liveRoomId });
    }, [form?.getFieldsValue().liveIds]);

    const handleValidator = (_: unknown, value: number, callback: any) => {
      if (!value && value != 0) {
        return callback();
      }
      if (Number.isNaN(Number(value))) {
        return callback(`请输入数字 支持2位小数`);
      }
      if (Number(value) < 0) {
        return callback('不能输入负数');
      }
      if (Number(value) > 9999999999.99) {
        return callback('最大值9999999999.99');
      }

      return callback();
    };

    const { buyReturnRedEnvelopeFlag } = form.getFieldsValue();

    return (
      <div style={{ display: 'flex', flex: 1 }} className={styles.ServiceType}>
        <Form style={{ display: 'flex', flex: 1, flexWrap: 'wrap' }} {...formItemLayout}>
          <Form.Item required={false} label="直播间" style={formItemStyle}>
            {form?.getFieldDecorator('liveIds', {
              initialValue: null,
              rules: [{ required: false, message: '' }],
            })(
              <Select
                value={liveIds}
                style={{ width: '200px' }}
                maxTagCount={1}
                showSearch
                filterOption={true}
                optionFilterProp="children"
                onChange={selectLive}
                placeholder="请选择"
              >
                {liveList?.map((i, index) => {
                  return (
                    <Select.Option value={i?.id} key={index}>
                      {i?.name}
                    </Select.Option>
                  );
                })}
              </Select>,
            )}
          </Form.Item>
          {!!childrenMsg ? (
            <Form.Item required={!!childrenMsg.required} label="直播服务类型" style={formItemStyle}>
              {form?.getFieldDecorator('liveServiceTypeId', {
                initialValue: null,
                rules: [{ required: !!childrenMsg.required, message: '请选择直播服务类型' }],
              })(
                <Select
                  style={{ width: 200 }}
                  allowClear={!childrenMsg.required}
                  onChange={(value, option: any) => {
                    console.log('🚀 ~ e:', value, option);
                    const { props } = option || {};
                    setLiveServiceTypeName(props?.children || '');
                  }}
                >
                  {childrenMsg?.serviceTypeOptions?.map((item) => {
                    return <Select.Option value={item.id}>{item.name}</Select.Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
          ) : null}
          {['预售讲解', '预售挂链', '预热挂链', '预热讲解'].includes(liveServiceTypeName) ? (
            <Form.Item required label="定金金额" style={formItemStyle}>
              {form?.getFieldDecorator('depositAmount', {
                initialValue: null,
                rules: [
                  { required: true, message: '请填写定金金额' },
                  { validator: handleValidator },
                ],
              })(
                <InputNumber max={9999999999.99} min={0.0} precision={2} style={{ width: 200 }} />,
              )}
            </Form.Item>
          ) : null}
          <Form.Item label="商务" style={formItemStyle}>
            {form?.getFieldDecorator('bpId', {
              initialValue: bpId,
              rules: [{ required: true, message: '请选择商务' }],
            })(
              <EmployeeSelect
                style={{ width: '200px' }}
                deptId={deptId}
                bpName={bpName}
                bizRoleType="BUSINESS"
              />,
            )}
          </Form.Item>
          {labelOpTionList?.map((item, index) => (
            <Form.Item label={item.labelName} style={formItemStyle}>
              {form?.getFieldDecorator(
                'labelOpTion_label_value----' + item.labelName + '----' + index,
                {
                  rules: [
                    { required: item.required === 'REQUIRED', message: '请选择' + item.labelName },
                  ],
                },
              )(
                <Select
                  style={{ width: 200 }}
                  labelInValue
                  allowClear={item.required === 'NON_REQUIRED'}
                >
                  {item?.labelOptionConfigs?.map((i) => {
                    return <Select.Option value={i.id}>{i.labelOptionName}</Select.Option>;
                  })}
                </Select>,
              )}
            </Form.Item>
          ))}
          {/*换字段 */}
          {isShowRed ? (
            <Form.Item label="是否买返红包" required style={formItemStyle}>
              {form?.getFieldDecorator('buyReturnRedEnvelopeFlag', {
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select style={{ width: '200px' }} allowClear placeholder="请选择">
                  <Select.Option value={true}>是</Select.Option>
                  <Select.Option value={false}>否</Select.Option>
                </Select>,
              )}
            </Form.Item>
          ) : (
            <></>
          )}

          {buyReturnRedEnvelopeFlag && isShowRed ? (
            <Form.Item label="出资方" required style={formItemStyle}>
              {form?.getFieldDecorator('fundingParty', {
                rules: [{ required: true, message: '请选择' }],
              })(
                <Select style={{ width: '200px' }} allowClear placeholder="请选择">
                  {FUNDING_PARTY_LIST?.map((i, index) => {
                    return (
                      <Select.Option value={i?.value} key={index}>
                        {i?.label}
                      </Select.Option>
                    );
                  })}
                </Select>,
              )}
            </Form.Item>
          ) : (
            <></>
          )}
          {buyReturnRedEnvelopeFlag && isShowRed ? (
            <Form.Item label="红包金额" required style={formItemStyle}>
              {form?.getFieldDecorator('redEnvelopeAmount', {
                rules: [{ required: true, message: '请填写' }],
              })(
                <InputNumber
                  style={{ width: '200px' }}
                  placeholder="请选择"
                  min={1}
                  max={9999}
                  precision={0}
                />,
              )}
            </Form.Item>
          ) : (
            <></>
          )}
          {buyReturnRedEnvelopeFlag && isShowRed ? (
            <Form.Item label="备注" style={formItemStyle}>
              {form?.getFieldDecorator('remark')(
                <Input style={{ width: '200px' }} maxLength={500} />,
              )}
            </Form.Item>
          ) : (
            <></>
          )}
        </Form>
      </div>
    );
  },
);
export default Form.create<LiveRoomSelectProps>()(LiveRoomSelect);
