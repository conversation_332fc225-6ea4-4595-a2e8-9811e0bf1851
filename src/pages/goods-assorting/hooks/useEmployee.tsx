import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  getEmployeePageByRole,
  GetEmployeePageByRoleRequest,
  GetEmployeePageByRoleResult,
} from '@/services/yml/employee-list';
import { useSetState } from 'ahooks';

export default function useEmployee() {
  const [state, setState] = useSetState<{
    loading: boolean;
    list: GetEmployeePageByRoleResult['records'];
  }>({
    loading: false,
    list: [],
  });
  const getList = async (params: GetEmployeePageByRoleRequest) => {
    setState((defaultState) => ({ ...defaultState, loading: true }));
    const result = await responseWithResultAsync({
      request: getEmployeePageByRole,
      params: { ...params, size: 100, accountState: 0 },
    });
    setState(() => ({
      list: result?.records ?? [],
      loading: false,
    }));
    return result;
  };

  const onSearch = (params?: GetEmployeePageByRoleRequest) => {
    console.log('accountState', params);
    return getList(
      params
        ? {
            ...params,
            employeeName: !!params?.employeeName ? params?.employeeName : undefined,
            accountState: 0,
          }
        : {},
    );
  };

  return {
    loading: state.loading,
    list: state.list,
    setList: (value: GetEmployeePageByRoleResult['records']) => {
      setState((defaultState) => ({ ...defaultState, list: value }));
    },
    onSearch,
  };
}
