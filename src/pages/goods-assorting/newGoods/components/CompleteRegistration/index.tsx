import React, { useEffect, useState, useImperativeHandle, useRef } from 'react';
import styles from '../../index.module.less';
import { DetailTitle } from '../index';
import BusinessInfo from './businessInfo';
import SkuTable from './skuTable';
import { useTableHeight } from '@/common/constants/hooks/index';
import { getGoodsInfo, completeCreateRecord } from '@/services/yml/goods-assorting/index';
import { message } from 'antd';
import { history } from 'qmkit';
import { handlePrecision } from '@/common/common';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
interface CompleteRegistrationProps {
  deptId?: string; // deptId 是一个字符串类型
  source?: string; // source 是一个字符串类型
  supplierId?: string; // supplierId 是一个字符串类型
  platformSpuId?: string; // platformSpuId 是一个字符串类型
  onRef?: React.Ref<any>;
  pageSource?: string;
  codeDetail?: any;
  investmentPlanInfo?: any;
  bpId?: any;
}

interface GoodsInfo {
  brandId?: string;
  brandName?: string;
  cateId?: string;
  cateName?: string;
  img?: string;
  platformShopCode?: string;
  platformShopName?: string;
  platformSpuId?: string;
  reference?: string;
  supplySource?: string;
  id?: string;
  name?: string;
  no?: string;
  standardGoodsCateId?: string;
  standardGoodsCateName?: string;
  platformShopId?: string;
  supplierSkuList?: Array<{
    sellPrice?: number;
    no?: string;
    stockNum?: number;
    img?: string;
  }>;
}

const CompleteRegistration: React.FC<CompleteRegistrationProps> = (props) => {
  const {
    deptId,
    bpId,
    source,
    supplierId,
    codeDetail,
    investmentPlanInfo,
    platformSpuId,
    pageSource,
    onRef,
  } = props;
  // console.log('props', props);
  const sourceEnum = {
    DY: 'SELF',
    JD: 'JD_SELF_SPU',
    TB: 'TAOBAO_SUPPLIER_SPU',
    WECHAT_VIDEO: 'VIDEO_SELF_SPU',
    BAIDU: 'BAIDU_SELF_SPU',
    KS: 'KUAISHOU_SELF_SPU',
    RED: 'RED_SELF_SPU',
  };
  const [dataSource, setDataSource] = useState([]);
  const { getHeight, tableHeight } = useTableHeight(300);
  const [goodsInfo, setGoodsInfo] = useState<GoodsInfo>({});
  const { closeAndJumpToPage } = useCloseAndJump();
  const [isLuckyProduct, setIsLuckyProduct] = useState(false);
  const getSkuList = () => {
    getGoodsInfo({
      platformSpuId: platformSpuId,
      source: source && sourceEnum[source],
      supplierId: supplierId,
    }).then(({ res }) => {
      // console.log('res', res);
      if (res.code === '200' && res.result) {
        setGoodsInfo(res.result);
        const arr = res.result?.supplierSkuList?.map((i, index) => {
          return {
            ...i,
            index: index + 1,
            price: i.sellPrice,
            skuNo: i.no,
            stock: i.stockNum,
            image: i.img,
          };
        });
        setDataSource([...arr]);
      }
    });
  };
  const onSubmit = () => {
    BusinessInfoRef?.current?.form?.validateFields((err, values) => {
      console.log('err', values);
      let isCompleteSkus = false;
      dataSource.forEach((i) => {
        if (
          (!i.salePrice && i.salePrice !== 0 && values.luckyProductFlag === false) ||
          (!i.stock && i.stock !== 0)
        ) {
          isCompleteSkus = true;
        }
        if (values.luckyProductFlag === true) {
          if (!i.purchasePrice || !i.taxRate) {
            isCompleteSkus = true;
          }
        }
      });

      if (isCompleteSkus) {
        message.warn('sku信息未填写完成，请检查');
        return;
      }

      // if (!isLuckyProduct) {
      //   if (!values.commissionRate || !values.commissionRateOffline || !values.brandFee) {
      //     message.warn('请填写线上佣金比例、线下佣金比例和固定服务费');
      //     return;
      //   }
      // }

      const investment = investmentPlanInfo
        ? investmentPlanInfo
        : values?.investmentNo && JSON.parse(values?.investmentNo?.key); //招商计划的信息
      const params = {
        bpId: bpId,
        afterSaleContent: null,
        applyTypeEnum: 'PLATFORM_SUPPLIER_FORWARD',
        brandFee: values?.brandFee,
        brandId: goodsInfo?.brandId,
        brandName: goodsInfo?.brandName,
        cateId: goodsInfo?.cateId,
        cateName: goodsInfo?.cateName,
        commissionRate: handlePrecision(values.commissionRate, 4, 0.01),
        commissionRateOffline: handlePrecision(values.commissionRateOffline, 4, 0.01),
        deptId: deptId,
        image: goodsInfo?.img,
        investmentId: investment.id,
        investmentNo: investment?.investmentPlanNo,
        investmentName: investment?.investmentTitle || investment?.title,
        investmentInfo: {
          liveRoundIds: [],
          talentInfos: investment.liveTalentList.map((i) => {
            return {
              liveRoomId: i.liveRoomId,
              liveRoomName: i.liveRoomName,
              serviceModes: ['LIVE'],
              talentId: i.talentId,
            };
          }),
        },
        investmentType: 'LIMIT_TIME',
        isCashBack: false,
        link: goodsInfo?.reference,
        logisticsContent: null,
        platformShopId: goodsInfo?.platformShopCode,
        platformShopName: goodsInfo?.platformShopName,
        platformSource: source,
        platformSpuId: goodsInfo?.platformSpuId,
        selectionContent: null,
        shopId: goodsInfo?.platformShopId,
        skus: dataSource,
        source: goodsInfo?.supplySource, //,goodsInfo?.supplySource
        spuId: goodsInfo?.id,
        spuName: goodsInfo?.name,
        spuNo: goodsInfo?.no,
        standardCateId: goodsInfo?.standardGoodsCateId,
        standardGoodsCateName: goodsInfo?.standardGoodsCateName,
        luckyProductFlag: values.luckyProductFlag, // 传递参数
        // supplierOrgName: '天津市华水清洗服务有限公司',
        promotionLink: values.commissionRate === 0 && source === 'TB' ? values.promotionLink : '',
        supplierId: supplierId,
      };

      completeCreateRecord({ list: [params], supplierId }).then(({ res }) => {
        // console.log('res', res);
        if (res.code === '200') {
          message.success('提交成功');
          const newlLoginInfo =
            localStorage.getItem('jgpy-crm@loginInfo') &&
            JSON.parse(localStorage.getItem('jgpy-crm@loginInfo'));
          // isSupplierLoginƒ
          setTimeout(() => {
            if (pageSource === 'newGoods') {
              // history.push('goods-assorting');
              closeAndJumpToPage('/goods-assorting');
            }
            if (pageSource === 'newGoodsLink') {
              window.location.reload();
            }
          }, 300);
        } else {
          message.error(res.message);
        }
      });
    });
  };
  useEffect(() => {
    getHeight();
    // console.log('参数', source, supplierId, platformSpuId);
    if (source && supplierId && platformSpuId) {
      getSkuList();
    }
  }, [source, supplierId, platformSpuId]);
  const BusinessInfoRef = useRef<{ form: any }>();
  useImperativeHandle(onRef, () => ({
    onSubmit,
  }));
  const handleLuckyProductChange = (value) => {
    setIsLuckyProduct(value);
  };
  return (
    <div className={`${styles['add-goods']}`}>
      <DetailTitle title="基本信息"></DetailTitle>
      <BusinessInfo
        onRef={BusinessInfoRef}
        deptId={deptId}
        investmentId={codeDetail?.investmentId}
        source={source}
        onLuckyProductChange={handleLuckyProductChange} // 传递回调函数
      />
      <DetailTitle title="SKU信息"></DetailTitle>
      <SkuTable
        dataSource={dataSource}
        setDataSource={setDataSource}
        tableHeight={tableHeight}
        isLuckyProduct={isLuckyProduct} // 传递字段
      ></SkuTable>
    </div>
  );
};

export default CompleteRegistration;
