/*
 * @Author: zhouby
 * @Date: 2024-03-25 14:40:44
 * @LastEditTime: 2024-03-25 21:42:07
 * @Description: 基本信息
 */
import React, { useEffect, useState } from 'react';
import { Table, InputNumber } from 'antd';
import { handlePrecision } from '@/common/common';
import debounce from 'lodash/debounce';
import Title from './Title';
import styles from '../../index.module.less';
interface PROPS {
  [key: string]: any;
  deptId?: string;
  tableHeight: number;
  dataSource?: any[];
  setDataSource?: any;
  isLuckyProduct: boolean; // 新增的参数
}

const BasicInformation: React.FC<PROPS> = (props) => {
  const { deptId, dataSource, tableHeight, setDataSource, isLuckyProduct } = props;
  const [loading, setLoading] = useState(false);
  interface Info {
    [key: string]: string | number; // info 对象的键值对应为字符串或数字类型
  }
  type ValueType = string | number; // value 的类型为字符串或数字
  const changeDataSource = debounce((info: Info, type: string, value: ValueType): void => {
    const arr = dataSource ? [...dataSource] : [];
    arr?.forEach((i) => {
      if (i.id === info.id) {
        i[type] = value;
      }
    });
    setDataSource(arr);
  });
  const copyData = debounce((type: string): void => {
    const arr = dataSource ? [...dataSource] : [];
    const number = arr[0][type];
    console.log(number);
    arr?.forEach((i) => {
      i[type] = number;
    });
    setDataSource(arr);
  });

  useEffect(() => {
    if (isLuckyProduct) {
      const arr = dataSource ? [...dataSource] : [];
      arr.forEach((i) => {
        i.salePrice = 0;
      });
      setDataSource(arr);
    } else {
      const arr = dataSource ? [...dataSource] : [];
      arr.forEach((i) => {
        i.salePrice = 0.01;
      });
      setDataSource(arr);
    }
  }, [isLuckyProduct]);

  const columns = [
    // {
    //   title: '#',
    //   align: 'center',
    //   key: 'index',
    //   dataIndex: 'index',
    //   render: (t) => {
    //     return <span>{t}</span>;
    //   },
    //   width: 28,
    // },

    {
      title: '规格图片',
      width: 130,
      dataIndex: 'img',
      key: 'img',
      render: (img: string) => {
        return <img src={img} width={50} height={50} alt="" />;
      },
    },
    {
      title: '日常价格',
      width: 130,
      dataIndex: 'sellPrice',
      key: 'sellPrice',
      render: (sellPrice: string) => {
        return <InputNumber disabled value={handlePrecision(sellPrice, 2, 0.01)} />;
      },
    },
    {
      title: (
        <Title
          required={!isLuckyProduct}
          title={'直播价(元)'}
          copy={() => {
            copyData('salePrice');
          }}
        />
      ),
      width: 130,
      dataIndex: 'salePrice',
      key: 'salePrice',
      render: (salePrice: string, info: Info) => {
        return (
          <InputNumber
            // className={styles['sku-table-amountInput']}
            style={{ width: '100%' }}
            min={isLuckyProduct ? 0 : 0.01}
            max={9999999.99}
            precision={2}
            value={salePrice}
            onChange={(e) => {
              changeDataSource(info, 'salePrice', e as number);
            }}
            disabled={isLuckyProduct} // 根据参数禁用
          />
        );
      },
    },
    {
      title: (
        <Title
          required={true}
          title={'库存'}
          copy={() => {
            copyData('stock');
          }}
        />
      ),
      width: 130,
      dataIndex: 'stock',
      key: 'stock',
      render: (stock: string, info: Info) => {
        return (
          <InputNumber
            style={{ width: '100%' }}
            min={0.01}
            max={9999999.99}
            precision={0}
            value={stock}
            onChange={(e) => {
              changeDataSource(info, 'stock', e as number);
            }}
          />
        );
      },
    },
    // 新增的两列，仅当 isLuckyProduct 为 true 时展示
    ...(isLuckyProduct
      ? [
          {
            title: (
              <Title
                required={true}
                title={'采购价（含税）'}
                copy={() => {
                  copyData('purchasePrice');
                }}
              />
            ),
            width: 130,
            dataIndex: 'purchasePrice',
            key: 'purchasePrice',
            render: (purchasePrice: string, info: Info) => {
              return (
                <InputNumber
                  className={styles['sku-table-amountInput']}
                  style={{ width: '100%' }}
                  min={0.01}
                  max={9999999.99}
                  precision={2}
                  value={purchasePrice}
                  onChange={(e) => {
                    changeDataSource(info, 'purchasePrice', e as number);
                  }}
                />
              );
            },
          },
          {
            title: (
              <Title
                required={true}
                title={'税率（%）'}
                copy={() => {
                  copyData('taxRate');
                }}
              />
            ),
            width: 130,
            dataIndex: 'taxRate',
            key: 'taxRate',
            render: (taxRate: string, info: Info) => {
              return (
                <InputNumber
                  className={styles['sku-table-amountInput']}
                  style={{ width: '100%' }}
                  min={0.0}
                  max={100}
                  precision={2}
                  value={taxRate}
                  onChange={(e) => {
                    changeDataSource(info, 'taxRate', e as number);
                  }}
                />
              );
            },
          },
        ]
      : []),
    {
      title: (
        <Title
          title={'历史最低价'}
          copy={() => {
            copyData('hisLowestPrice');
          }}
        />
      ),
      width: 130,
      dataIndex: 'hisLowestPrice',
      key: 'hisLowestPrice',
      render: (hisLowestPrice: string, info: Info) => {
        return (
          <InputNumber
            // className={styles['sku-table-amountInput']}
            style={{ width: '100%' }}
            min={0.01}
            max={9999999.99}
            precision={2}
            value={hisLowestPrice}
            onChange={(e) => {
              changeDataSource(info, 'hisLowestPrice', e as number);
            }}
          />
        );
      },
    },
    {
      title: (
        <Title
          title={'最高售卖价'}
          copy={() => {
            copyData('hisHighestPrice');
          }}
        />
      ),
      width: 130,
      dataIndex: 'hisHighestPrice',
      key: 'hisHighestPrice',
      render: (hisHighestPrice: string, info: any) => {
        return (
          <InputNumber
            // className={styles['sku-table-amountInput']}
            style={{ width: '100%' }}
            min={0.01}
            max={9999999.99}
            precision={2}
            value={hisHighestPrice}
            onChange={(e) => {
              changeDataSource(info, 'hisHighestPrice', e as number);
            }}
          />
        );
      },
    },
  ];

  return (
    <div style={{ flex: 1, padding: '0 24px' }}>
      <Table
        rowKey="index"
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        scroll={{ y: tableHeight, x: '100%' }}
      />
    </div>
  );
};

export default BasicInformation;
