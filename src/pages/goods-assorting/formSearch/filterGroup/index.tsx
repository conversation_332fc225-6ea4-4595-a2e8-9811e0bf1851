import React, { useEffect, useState } from 'react';
import './index.less';

// mode  multiple 多选 不传单选
interface PropsType {
  list: Array<any>;
  mode?: string;
  onChange: (value?: any) => void;
  defaultValue?: string;
  clear?: boolean;
}
const FilterGroup: React.FC<PropsType> = (props) => {
  const { list, onChange, mode, defaultValue, clear } = props;
  const [listTab, setListTab] = useState<any[]>([]);
  const [isClear, setIsClear] = useState(false);
  useEffect(() => {
    setIsClear(clear as false);
  }, [clear]);
  useEffect(() => {
    const arr = [...list];
    arr.forEach((i) => {
      if (defaultValue) {
        if (defaultValue === i.value) {
          i.isActive = true;
        } else {
          i.isActive = false;
        }
      } else {
        i.isActive = false;
        arr[0].isActive = true;
      }
    });
    setListTab(arr);
    onChange(arr[0]?.value);
    setIsClear(false);
  }, [list, isClear]);

  const multipleChange = (index: number) => {
    const arr = listTab;
    arr[index].isActive = !arr[index].isActive;
    setListTab([...arr]);
  };
  const singleChange = (index: number) => {
    const arr = listTab;
    arr.forEach((item, key) => {
      if (key === index) {
        item.isActive = true;
      } else {
        item.isActive = false;
      }
    });
    setListTab([...arr]);
  };
  return (
    <div className="filterGroup">
      {listTab.length > 0 &&
        listTab.map((i, index) => {
          return (
            <span
              className={`tabBtn ${i.isActive ? 'activeTab' : ''}`}
              key={i.value}
              onClick={() => {
                mode === 'multiple' ? multipleChange(index) : singleChange(index);
                onChange(i.value);
              }}
            >
              {i.label}
            </span>
          );
        })}
    </div>
  );
};
export default FilterGroup;
