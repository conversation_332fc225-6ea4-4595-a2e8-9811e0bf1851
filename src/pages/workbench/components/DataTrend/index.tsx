import React, { useEffect, useRef, useState } from 'react';
import styles from '../../index.module.less';
import { classNames } from '@/utils/moduleUtils';
import { Empty, message, Spin } from 'antd';
import { HAS_NO_DEPARTMENT, IS_MASTER_ACCOUNT, SaleEnum } from '@/common/constants/partnerManage';
import { IEmployee } from '../../utils';
import { Chart } from '@antv/g2';
import { getWorkbenchSaleDFApi } from '@/services/partnerManage';
import { useRequest } from 'ahooks';
import { OSSImagePre } from '@/common/constants/moduleConstant';

enum TAB_TYPE {
  DEPARTMENT = 'department',
  EMPLOYEE = 'employee',
}
interface IProps {
  departmentName: string;
  employeeInfo: IEmployee;
}

const color = ['#4F78F5', '#15C089', '#FAAD14', '#974ADD', '#F76217'];

const DataChart = (props: IProps) => {
  const [data, setData] = useState<any>([]);
  const chartRef = useRef<Chart | null>(null);
  const [tab, setTab] = useState(TAB_TYPE.DEPARTMENT);

  const { loading, run: getTalentRankDFRun } = useRequest(getWorkbenchSaleDFApi, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res.errMsg === 'success') {
        const list = res.data || [];
        setData(list);
        renderChart(list);
        return;
      }
      message.error('销售趋势获取失败！');
    },
  });

  const handleGetData = (_tab = tab) => {
    const param = {} as any;
    if (_tab === TAB_TYPE.DEPARTMENT) {
      param.departmentId =
        props.departmentName === IS_MASTER_ACCOUNT
          ? '7ffffe7d55124ea313fe606b4d3f4cd2'
          : props?.employeeInfo?.currentDepartmentId;
    } else {
      param.employeeId = props?.employeeInfo?.employeeId;
    }
    getTalentRankDFRun({ ...param });
  };

  const renderChart = (list = data) => {
    if (chartRef?.current && list?.length > 0) {
      chartRef.current.destroy();
    }
    const _data: any[] = [];
    list?.map((i) => {
      const time = `${i.liveDate.split('-')[1]}.${i.liveDate.split('-')[2]}`;
      _data.push({ label: time, type: '支付GMV', value: i.gmv });
      _data.push({ label: time, type: 'GMV（T+2）', value: i.gmvT2 });
      _data.push({ label: time, type: '预估结算GMV（T+2）', value: i.settleGmvT2 });
      _data.push({ label: time, type: '总佣金（T+2）', value: i.totalCommissionT2 });
      _data.push({ label: time, type: '品牌费', value: i.brandFee });
    });
    const chart = new Chart({
      container: 'dataChart',
      autoFit: true,
      height: 320,
      padding: [45, 20, 20, 70],
    });
    chartRef.current = chart;
    chart.data(_data);
    chart.axis('label', {
      label: {
        style: {
          fontSize: 12,
          autoHide: false,
        },
      },
    });
    chart.axis('value', {
      grid: {
        line: {
          style: {
            lineDash: [10],
            fill: 'rgba(210, 214, 221, 1)',
          },
        },
      },
    });
    chart.scale({
      label: {
        range: [0, 1],
      },
      value: {
        nice: true,
      },
    });

    chart.tooltip({
      showCrosshairs: true, // 展示 Tooltip 辅助线
      shared: true,
      showTitle: false,
      domStyles: {
        'g2-tooltip': {
          boxShadow: '0px 0px 0px rgba(0, 0, 0, 0.1), 0px 1px 12px rgba(0, 0, 0, 0.08)',
          borderRadius: '2px',
        },
      },
      crosshairs: {
        type: 'x',
      },
      itemTpl:
        '<li style="margin-bottom:4px;list-style-type:none;padding: 0;">' +
        `<span style="padding-left: 16px;line-height: 16px;">{type}：{value}</span><br/>` +
        '</li>',
    });
    chart.legend({
      position: 'top',
      marker: (name, index, item) => {
        return {
          style: {
            // fill: color[index],
          },
        };
      },
    });
    chart
      .line()
      .position('label*value')
      .color('type', color)
      .tooltip('label*type*value', (label, type, value) => {
        return {
          label: label,
          type: type,
          value: value,
        };
      })
      .shape('smooth');
    chart.render();
  };

  useEffect(() => {
    if (!props.departmentName || !props?.employeeInfo?.employeeId) {
      return;
    }
    if (props.departmentName === HAS_NO_DEPARTMENT) {
      setTab(TAB_TYPE.EMPLOYEE);
      handleGetData(TAB_TYPE.EMPLOYEE);
      return;
    }
    if (props.departmentName === IS_MASTER_ACCOUNT) {
      handleGetData();
      return;
    }
    handleGetData();
  }, [props.departmentName, props.employeeInfo]);

  return (
    <div className={styles.DataChart}>
      <div className={styles.condition}>
        <div className={classNames(styles.title, 'font-title-2')}>销售趋势</div>
      </div>
      <div className={styles.divide}></div>
      <div className={styles.dataChart}>
        {data?.length > 0 ? (
          <Spin spinning={loading}>
            <div className={styles.wrapper} id="dataChart"></div>
          </Spin>
        ) : (
          <div className={styles.emptyText}>
            <img className="" src={`${OSSImagePre}/jgpy-crm/noData.png`} alt="" />
            <p>暂无相关数据</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataChart;
