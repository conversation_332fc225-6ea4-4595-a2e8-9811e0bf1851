import { Input, Select } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useMemo, useState } from 'react';
import SearchFormComponent, { searchItem } from '../searchFormComponent';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { STATUS_ENUM } from '../../utils/enum';
import { useAreaList, useWarehouseList } from '../../utils/hook';
interface IProps extends FormComponentProps {
  onSearch: () => void;
  onReset: () => void;
  loading: boolean;
}

const { Option } = Select;

const SearchForm: React.FC<IProps> = ({ form, onSearch, onReset, loading }) => {
  // ai生成
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<number | undefined>(undefined);
  // 2024年6月25日

  const reset = () => {
    // ai生成
    setSelectedWarehouseId(undefined);
    // 2024年6月25日
    onReset();
  };
  const { codeList } = useCode(CODE_ENUM.WAREHOUSE_LOCATION_TYPE, {
    able: false,
  });

  // ai生成
  const {
    list: warehouseList,
    loading: warehouseLoading,
    hanelSearch: hanelWarehouseSearch,
  } = useWarehouseList();
  // 2024年6月25日

  const {
    list: areaList,
    loading: areaLoading,
    getList: getAreaList,
    hanelSearch: hanelAreaSearch,
  } = useAreaList();

  // ai生成
  // 当仓库ID变化时，重新获取对应的库区列表
  const handleWarehouseChange = (warehouseId: number) => {
    setSelectedWarehouseId(warehouseId);
    if (warehouseId) {
      getAreaList({
        warehouseId,
        pageNum: 1,
        pageSize: 20,
      });
    } else {
      getAreaList({
        pageNum: 1,
        pageSize: 20,
      });
    }
    // 清空已选择的库区
    form.setFieldsValue({ areaIdList: undefined });
  };
  // 2024年6月25日

  const options: Record<string, searchItem> = useMemo(() => {
    return {
      code: {
        label: '库位代码',
        renderNode: <Input placeholder="请输入库位代码" maxLength={200} />,
      },
      name: {
        label: '库位名称',
        renderNode: <Input placeholder="请输入库位名称" maxLength={200} />,
      },

      // 2024年6月25日
      typeList: {
        label: '库位类型',
        renderNode: (
          <Select placeholder="请选择" mode="multiple" maxTagCount={1} allowClear>
            {codeList?.map((item) => (
              <Option value={item.value} key={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
        ),
      },
      // ai生成
      warehouseId: {
        label: '所属仓库',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            onSearch={hanelWarehouseSearch}
            loading={warehouseLoading}
            onChange={handleWarehouseChange}
            onBlur={() => {
              hanelWarehouseSearch(undefined);
            }}
          >
            {warehouseList?.map((item) => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        ),
      },
      areaIdList: {
        label: '所属库区',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            onSearch={hanelAreaSearch}
            loading={areaLoading}
            onBlur={() => {
              hanelAreaSearch(undefined);
            }}
            mode="multiple"
            maxTagCount={1}
            // ai生成
            disabled={!selectedWarehouseId}
            // 2024年6月25日
          >
            {areaList?.map((item) => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        ),
      },
      status: {
        label: '状态',
        span: 1,
        draggable: true,
        renderNode: (
          <Select allowClear placeholder="请选择状态">
            {Object.entries(STATUS_ENUM)
              .filter((item) => typeof item[1] === 'string')
              .map(([key, value]) => (
                <Option value={key} key={key}>
                  {value}
                </Option>
              ))}
          </Select>
        ),
      },
    };
  }, [codeList, areaList, areaLoading, warehouseList, warehouseLoading, selectedWarehouseId]);

  return (
    <SearchFormComponent
      form={form}
      options={options}
      loading={loading}
      onSearch={onSearch}
      onReset={reset}
    />
  );
};

export default SearchForm;
