import { Const, Fetch } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: number;
  data: T;
}

export type LocationPageRequest = {
  operatorId?: string /* 操作员ID*/;
  operatorName?: string /* 操作员名称*/;
  pageNum?: number /* 页码*/;
  pageSize?: number /* 每页记录数*/;
  code?: string /* 编码*/;
  name?: string /* 名称*/;
  typeList?: number[] /* 类型*/;
  areaIdList?: number[] /* 库区ID*/;
  warehouseId?: number /* 仓库ID*/;
  status?: number /* 状态*/;
};

export type LocationPageResult = {
  size?: number;
  current?: number;
  total?: number;
  records?: {
    id?: number;
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    code?: string;
    name?: string;
    type?: number;
    typeName?: string;
    areaId?: number;
    areaCode?: string;
    areaName?: string;
    warehouseId?: number;
    floor?: number;
    status?: number;
    remark?: string;
  }[];
};

/**
 * 库区管理分页查询
 */
export const locationPage = (params: LocationPageRequest) => {
  return Fetch<ResponseWithResult<LocationPageResult>>('/warehouse/warehouse/location/page', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
    },
  });
};

export type LocationImportRequest = {
  file: File;
};
export type LocationImportResult = {
  successCount: number;
  failCount: number;
  failureDetails: {
    rowNum: number;
    code: string;
    reason: string;
  }[];
};
export const locationImport = (
  params: LocationImportRequest,
): Promise<ResponseWithResult<LocationImportResult>> => {
  const formData = new FormData();
  formData.append('file', params.file);

  return new Promise((resolve, reject) => {
    fetch(Const.baseHost + '/warehouse/warehouse/location/import', {
      method: 'POST',
      body: formData,
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
        // ai生成
        operatorId: encodeURIComponent(
          JSON.parse(localStorage.getItem('jgpy-crm@login') || '{}').employeeId || '',
        ),
        operatorName: encodeURIComponent(
          JSON.parse(localStorage.getItem('jgpy-crm@login') || '{}').employeeName || '',
        ),
        // 2024年12月29日 开山ai结尾共生成2行代码
      },
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => resolve(data))
      .catch((error) => reject(error));
  });
};

export type LocationAddRequest = {
  id?: number;
  code?: string;
  name?: string;
  type?: number;
  areaId?: number;
  floor?: number;
  status?: number;
  remark?: string;
};
export type LocationAddResult = boolean;
export const locationAdd = (params: LocationAddRequest) => {
  return Fetch<ResponseWithResult<LocationAddResult>>('/warehouse/warehouse/location/add', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
  });
};

export type LocationUpdateRequest = {
  id?: number;
  code?: string;
  name?: string;
  type?: number;
  areaId?: number;
  floor?: number;
  status?: number;
  remark?: string;
};

export type LocationUpdateResult = boolean;
export const locationUpdate = (params: LocationUpdateRequest) => {
  return Fetch<ResponseWithResult<LocationUpdateResult>>('/warehouse/warehouse/location/update', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
  });
};
