import { Form, message, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useEffect, useMemo, useState } from 'react';
import PaginationProxy from '@/common/constants/Pagination';
import PageLayout from '@/components/PageLayout/index';
import styles from './index.module.less';
import { CODE_ENUM, useCode, useTableHeight } from '@/common/constants/hooks/index';
import SearchForm from './components/SearchForm';
import { useTable } from './utils/getColumns';
import { LocationPageListInfoType, useAreaList, useList, useWarehouseList } from './utils/hook';
import ImportData from './components/ImportData';
import CreateEditModal from './CreateEditModal';
import { LocationPageRequest, LocationPageResult, locationUpdate } from './services';
import { responseWithResultAsync } from '@/pages/warehouse-management/utils/utils';

const LocationManager: React.FC<FormComponentProps<LocationPageRequest>> = ({ form }) => {
  const { list, loading, getList, pagination, condition, setLoading } = useList(form);

  const onReset = () => {
    form.resetFields();

    getList({
      pageNum: 1,
      pageSize: 20,
    });
  };
  const onRefresh = () => {
    getList({});
  };
  useEffect(() => {
    onRefresh();
  }, []);
  const { getHeight, tableHeight } = useTableHeight(80);

  const handleDisable = async (info?: LocationPageListInfoType) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: locationUpdate,
      params: {
        id: info?.id,
        name: info?.name,
        code: info?.code,
        type: info?.type,
        areaId: info?.areaId,
        floor: info?.floor,
        status: info?.status === 1 ? 0 : 1,
      },
    });
    setLoading(false);
    if (result) {
      message.success(info?.status === 1 ? '禁用成功' : '启用成功');
      onRefresh();
    }
  };
  const { codeList } = useCode(CODE_ENUM.WAREHOUSE_LOCATION_TYPE, {
    able: true,
  });
  const {
    list: areaList,
    loading: areaLoading,
    getList: getAreaList,
    hanelSearch: hanelAreaSearch,
  } = useAreaList();
  const {
    list: warehouseList,
    loading: warehouseLoading,
    getList: getWarehouseList,
    hanelSearch: hanelWarehouseSearch,
  } = useWarehouseList();
  const { columns } = useTable({
    onRefresh,
    onDisable: handleDisable,
    codeList,
    areaList,
    areaLoading,
    warehouseList,
    warehouseLoading,
    hanelAreaSearch,
    hanelWarehouseSearch,
    getAreaList,
    getWarehouseList,
  });
  return (
    <PageLayout className={styles['cooperation-report-contain']} routePath="/location-manager">
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            form={form}
            loading={loading}
            onSearch={() => {
              getList({ pageNum: 1 });
            }}
            onReset={onReset}
          />

          <div className="flex items-center mb-16">
            <CreateEditModal
              type="create"
              onRefresh={() => onRefresh()}
              codeList={codeList}
              warehouseList={warehouseList}
              warehouseLoading={warehouseLoading}
              areaList={areaList}
              areaLoading={areaLoading}
              hanelAreaSearch={hanelAreaSearch}
              getAreaList={getAreaList}
              getWarehouseList={getWarehouseList}
              hanelWarehouseSearch={hanelWarehouseSearch}
            />
            <ImportData onRefresh={() => onRefresh()} />
          </div>
        </div>
        <div className={styles.boardTable} style={{ flex: 1 }}>
          <Table
            rowKey="id"
            loading={loading}
            columns={columns}
            dataSource={list}
            pagination={false}
            scroll={{ y: tableHeight, x: '100%' }}
          />
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <PaginationProxy
            current={pagination?.current}
            pageSize={pagination?.size}
            total={pagination?.total}
            // @ts-ignore
            onChange={(current, size) => {
              getList({
                pageNum: current,
                pageSize: size,
              });
            }}
            valueType="flatten"
            pageSizeOptions={['5', '10', '20', '50', '100']}
          />
        </div>
      </div>
    </PageLayout>
  );
};

export default Form.create()(LocationManager);
