import React, { useEffect, useState } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { locationPage, LocationPageRequest, LocationPageResult } from '../services';

import { useSetState } from 'ahooks';
import { responseWithResultAsync } from '@/pages/warehouse-management/utils/utils';
import {
  areaPage,
  AreaPageRequest,
  AreaPageResult,
  warehousePage,
  WarehousePageRequest,
  WarehousePageResult,
} from '../../area-manager/services';

export const useList = (form: WrappedFormUtils<LocationPageRequest>) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<LocationPageResult['records']>([]);
  const [pagination, setPagination] = useSetState({ current: 1, size: 20, total: 0 });
  const [condition, setCondition] = useState<LocationPageRequest>();
  const getList = async (data: LocationPageRequest) => {
    setLoading(true);
    const formValue = form?.getFieldsValue();

    const params = {
      ...data,
      ...(formValue as LocationPageRequest),
      pageNum: data?.pageNum ?? pagination.current,
      pageSize: data?.pageSize ?? pagination.size,
    };

    setCondition(params);
    const result = await responseWithResultAsync({
      request: locationPage,
      params,
    });
    setLoading(false);
    setList(result?.records ?? []);
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };

  useEffect(() => {
    getList({});
  }, []);
  return {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
  };
};
export type LocationPageListInfoType = NonNullable<LocationPageResult['records']>[number];

export const useAreaList = () => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<AreaPageResult['records']>([]);

  const getList = async (data: AreaPageRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: areaPage,
      params: data,
    });
    console.log(result);
    setLoading(false);
    setList(result?.records ?? []);
  };
  const hanelSearch = (name?: string) => {
    getList({
      name,
      pageNum: 1,
      pageSize: 20,
    });
  };
  useEffect(() => {
    getList({
      pageNum: 1,
      pageSize: 20,
    });
  }, []);
  return {
    list,
    loading,
    getList,
    setLoading,
    hanelSearch,
  };
};

export const useWarehouseList = () => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<WarehousePageResult['records']>([]);

  const getList = async (data: WarehousePageRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: warehousePage,
      params: data,
    });
    setLoading(false);
    setList(result?.records ?? []);
  };

  const hanelSearch = (name?: string) => {
    getList({
      name,
      pageNum: 1,
      pageSize: 1000,
    });
  };

  useEffect(() => {
    getList({
      pageNum: 1,
      pageSize: 1000,
    });
  }, []);

  return {
    list,
    loading,
    getList,
    setLoading,
    hanelSearch,
  };
};
