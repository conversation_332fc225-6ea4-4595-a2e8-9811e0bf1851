import { ColumnProps } from 'antd/lib/table';
import React, { useMemo } from 'react';
import moment from 'moment';
import { toDecimal } from '@/utils/string';
import PopoverRowText from '@/components/PopoverRowText';
import { Space } from 'web-common-modules/antd-pro-components';
import CreateEditModal from '../CreateEditModal';
import { LocationPageListInfoType, useWarehouseList } from './hook';
import { STATUS_ENUM } from './enum';
import { AreaPageResult, WarehousePageResult } from '../../area-manager/services';

// ai生成
export const formatFee = (val?: string | number | null) => {
  if (val !== '' && val !== null && val !== undefined) {
    return toDecimal(Number(val), 2);
  }
  return '-';
};

export const useTable = (params: {
  onRefresh: () => void;
  onDisable: (info?: LocationPageListInfoType) => void;
  codeList?: {
    value?: string;
    label?: string;
  }[];
  warehouseList?: WarehousePageResult['records'];
  warehouseLoading?: boolean;
  hanelWarehouseSearch?: (name?: string) => void;
  areaList?: AreaPageResult['records'];
  areaLoading?: boolean;
  hanelAreaSearch?: (name?: string) => void;
  getWarehouseList?: (data: any) => void;
  getAreaList?: (data: any) => void;
}) => {
  const {
    onRefresh,
    onDisable,
    codeList,
    warehouseList,
    warehouseLoading,
    hanelWarehouseSearch,
    areaList,
    areaLoading,
    hanelAreaSearch,
    getAreaList,
    getWarehouseList,
  } = params;
  // ai生成

  const columns = useMemo<ColumnProps<LocationPageListInfoType>[]>(
    () => [
      {
        dataIndex: 'index',
        title: '#',
        width: 40,
        render: (_, __, index) => index + 1,
      },

      {
        title: '状态',
        width: 100,
        dataIndex: 'status',
        render: (val: LocationPageListInfoType['status']) => STATUS_ENUM?.[val!] ?? '-',
      },
      {
        dataIndex: 'code',
        title: '库位代码',
        width: 150,
        render: (val: string) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '库位名称',
        width: 150,
        dataIndex: 'name',
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '库位类型',
        width: 150,
        dataIndex: 'typeName',
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '层数',
        width: 150,
        dataIndex: 'floor',
        render: (val) => val ?? '-',
      },
      {
        title: '所属仓库',
        width: 150,
        dataIndex: 'warehouseId',
        // ai生成
        render: (warehouseId: number) => {
          const warehouse = warehouseList?.find((item) => item.id === warehouseId);
          return warehouse?.name ? <PopoverRowText text={warehouse.name} /> : '-';
        },
      },
      {
        title: '所属库区',
        width: 150,
        dataIndex: 'areaName',
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '创建人',
        width: 150,
        dataIndex: 'createBy',
        render: (val) => val ?? '-',
      },
      {
        title: '创建时间',
        width: 150,
        dataIndex: 'createTime',
        render: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '操作',
        width: 150,
        dataIndex: 'action',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              {record.status === 1 ? (
                <a style={{ color: '#ff4d4f' }} onClick={() => onDisable(record)}>
                  禁用
                </a>
              ) : (
                <a onClick={() => onDisable(record)}>启用</a>
              )}
              <CreateEditModal
                type="edit"
                onRefresh={() => onRefresh()}
                info={record}
                codeList={codeList}
                warehouseList={warehouseList}
                warehouseLoading={warehouseLoading}
                hanelWarehouseSearch={hanelWarehouseSearch}
                areaList={areaList}
                areaLoading={areaLoading}
                hanelAreaSearch={hanelAreaSearch}
                getAreaList={getAreaList}
                getWarehouseList={getWarehouseList}
              />
            </Space>
          );
        },
      },
    ],
    [onRefresh, warehouseList, codeList, areaList, warehouseLoading, areaLoading],
  );
  return { columns };
};
// 2024年12月31日 开山ai结尾共生成7行代码
