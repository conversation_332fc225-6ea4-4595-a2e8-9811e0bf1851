import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { Modal, Button, Form, Input, Select, message, InputNumber } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import React, { useState, useEffect } from 'react';
import { locationAdd, LocationAddRequest, locationUpdate } from '../services';
import { LocationPageListInfoType, useAreaList, useWarehouseList } from '../utils/hook';
import { responseWithResultAsync } from '@/pages/warehouse-management/utils/utils';
import { AreaPageResult, WarehousePageResult } from '../../area-manager/services';

const { Option } = Select;

interface PropsType
  extends FormComponentProps<
    LocationAddRequest & {
      typeInfo?: { label: string; key: number };
      areaIdInfo?: { label: string; key: number };
      warehouseIdInfo?: { label: string; key: number };
    }
  > {
  type: 'create' | 'edit';
  onRefresh: () => void;
  info?: LocationPageListInfoType;
  codeList?: {
    value?: string;
    label?: string;
  }[];
  warehouseList?: WarehousePageResult['records'];
  warehouseLoading?: boolean;
  hanelWarehouseSearch?: (name?: string) => void;
  hanelAreaSearch?: (name?: string) => void;
  areaList?: AreaPageResult['records'];
  areaLoading?: boolean;
  getWarehouseList?: (data: any) => void;
  getAreaList?: (data: any) => void;
}

const titleMap = {
  create: '新建库位',
  edit: '编辑库位',
};
const CreateEditModal: React.FC<PropsType> = ({
  type,
  form,
  onRefresh,
  info,
  codeList,
  warehouseList,
  warehouseLoading,
  hanelWarehouseSearch,
  areaList,
  areaLoading,
  hanelAreaSearch,
  getWarehouseList,
  getAreaList,
}) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  // ai生成
  const [selectedWarehouseId, setSelectedWarehouseId] = useState<number | undefined>(undefined);
  const [warehouseName, setWarehouseName] = useState<string>('');
  // 2024年06月17日 开山ai结尾共生成2行代码

  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
  };
  const handleOpen = () => {
    setVisible(true);
    if (type === 'edit') {
      // ai生成
      setSelectedWarehouseId(info?.warehouseId);
      // 2024年06月17日 开山ai结尾共生成1行代码
      form.setFieldsValue({
        name: info?.name,
        code: info?.code,
        typeInfo: { label: info?.typeName, key: info?.type },
        // ai生成
        warehouseIdInfo: { label: warehouseName, key: info?.warehouseId },
        // 2024年06月17日 开山ai结尾共生成1行代码
        areaIdInfo: { label: info?.areaName, key: info?.areaId },
        floor: info?.floor,
      });
    }
  };
  const handleEdit = async (
    values: LocationAddRequest & {
      typeInfo?: { label: string; key: number };
      areaIdInfo?: { label: string; key: number };
      warehouseIdInfo?: { label: string; key: number };
    },
  ) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: locationUpdate,
      params: {
        ...values,
        status: info?.status,
        id: info?.id,
        type: values.typeInfo?.key,
        areaId: values.areaIdInfo?.key,
        // ai生成
        warehouseId: values.warehouseIdInfo?.key,
        warehouseIdInfo: undefined,
        // 2024年06月17日 开山ai结尾共生成2行代码
        typeInfo: undefined,
        areaIdInfo: undefined,
      },
    });
    if (result) {
      message.success('编辑成功');
      handleCancel();
      onRefresh();
    }
    setLoading(false);
  };

  const handleCreate = async (
    values: LocationAddRequest & {
      typeInfo?: { label: string; key: number };
      areaIdInfo?: { label: string; key: number };
      warehouseIdInfo?: { label: string; key: number };
    },
  ) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: locationAdd,
      params: {
        ...values,
        status: 1,
        type: values.typeInfo?.key,
        areaId: values.areaIdInfo?.key,
        // ai生成
        warehouseId: values.warehouseIdInfo?.key,
        warehouseIdInfo: undefined,
        // 2024年06月17日 开山ai结尾共生成2行代码
        typeInfo: undefined,
        areaIdInfo: undefined,
      },
    });
    if (result) {
      message.success('新建成功');
      handleCancel();
      onRefresh();
    }
    setLoading(false);
  };

  const handleOk = () => {
    form?.validateFields((err, values) => {
      if (err) return;
      if (type === 'create') {
        handleCreate(values);
        return;
      }
      handleEdit(values);
    });
  };

  // ai生成

  // 获取仓库名称
  useEffect(() => {
    if (type === 'edit' && info?.warehouseId && visible) {
      getWarehouseList?.({
        pageNum: 1,
        pageSize: 100,
      });
    }
  }, [type, info?.warehouseId, visible]);

  // 当仓库列表加载完成后，找到对应的仓库名称
  useEffect(() => {
    if (type === 'edit' && info?.warehouseId && warehouseList?.length) {
      const warehouse = warehouseList.find((item) => item.id === info.warehouseId);
      if (warehouse) {
        setWarehouseName(warehouse.name || '');
        // 更新表单中的仓库名称
        if (visible) {
          form.setFieldsValue({
            warehouseIdInfo: { label: warehouse.name, key: info.warehouseId },
          });
        }
      }
    }
  }, [warehouseList, info?.warehouseId, type]);
  // 2024年06月17日 开山ai结尾共生成24行代码

  // ai生成
  // 当选择仓库时，更新库区列表
  useEffect(() => {
    if (selectedWarehouseId) {
      getAreaList?.({
        warehouseId: selectedWarehouseId,
        pageNum: 1,
        pageSize: 20,
      });
    }
  }, [selectedWarehouseId]);

  // 处理仓库选择变化
  const handleWarehouseChange = (value: any) => {
    form.resetFields(['areaIdInfo']);
    setSelectedWarehouseId(value.key);
  };
  // 2024年06月17日 开山ai结尾共生成13行代码

  return (
    <>
      {type === 'create' && (
        <Button type="primary" icon="plus" onClick={handleOpen}>
          新建
        </Button>
      )}
      {type === 'edit' && <a onClick={handleOpen}>编辑</a>}
      <Modal
        visible={visible}
        title={titleMap[type]}
        onCancel={handleCancel}
        onOk={handleOk}
        confirmLoading={loading}
      >
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 12 }}>
          <Form.Item label="库位名称">
            {form?.getFieldDecorator('name', {
              rules: [{ required: true, message: '请输入库位名称' }],
            })(<Input maxLength={50} style={{ width: '300px' }} />)}
          </Form.Item>
          <Form.Item label="库位代码">
            {form?.getFieldDecorator('code', {
              rules: [
                { required: true, message: '请输入库位代码' },
                // ai生成
                {
                  pattern: /^[a-zA-Z0-9\-_]+$/,
                  message: '只能输入字母、数字、连字符(-)和下划线(_)',
                },
                // 2024年06月17日 开山ai结尾共生成1行代码
              ],
            })(<Input disabled={type === 'edit'} maxLength={50} style={{ width: '300px' }} />)}
          </Form.Item>
          <Form.Item label="库位类型">
            {form?.getFieldDecorator('typeInfo', {
              rules: [{ required: true, message: '请选择库位类型' }],
            })(
              <Select style={{ width: '300px' }} labelInValue>
                {codeList?.map((item) => (
                  <Option value={Number(item.value)} key={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>,
            )}
          </Form.Item>

          {/* ai生成 */}
          <Form.Item label="所属仓库">
            {form?.getFieldDecorator('warehouseIdInfo', {
              rules: [{ required: true, message: '请选择所属仓库' }],
            })(
              <Select
                style={{ width: '300px' }}
                onSearch={hanelWarehouseSearch}
                loading={warehouseLoading}
                onChange={handleWarehouseChange}
                onBlur={() => {
                  hanelWarehouseSearch?.(undefined);
                }}
                labelInValue
              >
                {warehouseList
                  ?.filter((item) => item.status === 1)
                  ?.map((item) => (
                    <Option value={item.id} key={item.id}>
                      {item.name}
                    </Option>
                  ))}
              </Select>,
            )}
          </Form.Item>
          {/* 2024年06月17日 开山ai结尾共生成19行代码 */}

          <Form.Item label="所属库区">
            {form?.getFieldDecorator('areaIdInfo', {
              rules: [{ required: true, message: '请选择所属库区' }],
            })(
              <Select
                style={{ width: '300px' }}
                onSearch={hanelAreaSearch}
                loading={areaLoading}
                onBlur={() => {
                  hanelAreaSearch?.(undefined);
                }}
                labelInValue
                // ai生成
                disabled={!selectedWarehouseId}
                placeholder={selectedWarehouseId ? '请选择库区' : '请先选择所属仓库'}
                // 2024年06月17日 开山ai结尾共生成2行代码
              >
                {areaList
                  ?.filter((item) => item.status === 1)
                  ?.map((item) => (
                    <Option value={item.id} key={item.id}>
                      {item.name}
                    </Option>
                  ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="层数">
            {form?.getFieldDecorator('floor', {
              rules: [
                { required: true, message: '请输入层数' },
                { pattern: /^[1-9]$|^10$/, message: '只能输入1-10的整数' },
              ],
            })(<InputNumber min={1} max={10} style={{ width: '300px' }} />)}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default Form.create<PropsType>()(CreateEditModal);
