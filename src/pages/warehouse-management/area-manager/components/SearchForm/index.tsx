import { Input, Select } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useMemo } from 'react';
import SearchFormComponent, { searchItem } from '../searchFormComponent';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { STATUS_ENUM } from '../../utils/enum';
import { useWarehouseList } from '../../utils/hook';
interface IProps extends FormComponentProps {
  onSearch: () => void;
  onReset: () => void;
  loading: boolean;
}

const { Option } = Select;

const SearchForm: React.FC<IProps> = ({ form, onSearch, onReset, loading }) => {
  const reset = () => {
    onReset();
  };
  const { codeList } = useCode(CODE_ENUM.WAREHOUSE_AREA_TYPE, {
    able: false,
  });
  const { list: warehouseList, loading: warehouseLoading, hanelSearch } = useWarehouseList();
  const options: Record<string, searchItem> = useMemo(() => {
    return {
      code: {
        label: '库区代码',
        renderNode: <Input placeholder="请输入库区代码" maxLength={200} />,
      },
      name: {
        label: '库区名称',
        renderNode: <Input placeholder="请输入库区名称" maxLength={200} />,
      },
      typeList: {
        label: '库区类型',
        renderNode: (
          <Select placeholder="请选择" mode="multiple" maxTagCount={1} allowClear>
            {codeList?.map((item) => (
              <Option value={item.value} key={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
        ),
      },
      warehouseIdList: {
        label: '所属仓库',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            loading={warehouseLoading}
            // onChange={hanelSearch}
            // onBlur={() => {
            //   hanelSearch(undefined);
            // }}
            optionFilterProp="children"
            filterOption={true}
            showSearch
            mode="multiple"
            maxTagCount={1}
          >
            {warehouseList
              ?.filter((item) => item.status === 1)
              ?.map((item) => (
                <Option value={item.id} key={item.id}>
                  {item.name}
                </Option>
              ))}
          </Select>
        ),
      },
      status: {
        label: '状态',
        span: 1,
        draggable: true,
        renderNode: (
          <Select allowClear placeholder="请选择状态">
            {Object.entries(STATUS_ENUM)
              .filter((item) => typeof item[1] === 'string')
              .map(([key, value]) => (
                <Option value={key} key={key}>
                  {value}
                </Option>
              ))}
          </Select>
        ),
      },
    };
  }, [codeList, warehouseList, warehouseLoading]);

  return (
    <SearchFormComponent
      form={form}
      options={options}
      loading={loading}
      onSearch={onSearch}
      onReset={reset}
    />
  );
};

export default SearchForm;
