import { Const, Fetch } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: number;
  data: T;
}

export type AreaPageRequest = {
  operatorId?: string /* 操作员ID*/;
  operatorName?: string /* 操作员名称*/;
  pageNum?: number /* 页码*/;
  pageSize?: number /* 每页记录数*/;
  code?: string /* 编码*/;
  name?: string /* 名称*/;
  typeList?: number[] /* 类型*/;
  warehouseIdList?: number[] /* 仓库ID*/;
  warehouseId?: number /* 仓库ID*/;
  status?: number /* 状态*/;
};

export type AreaPageResult = {
  size?: number;
  current?: number;
  total?: number;
  records?: {
    id?: number;
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    code?: string;
    name?: string;
    type?: number;
    typeName?: string;
    warehouseId?: number;
    warehouseCode?: string;
    warehouseName?: string;
    status?: number;
    remark?: string;
  }[];
};

/**
 * 库区管理分页查询
 */
export const areaPage = (params: AreaPageRequest) => {
  return Fetch<ResponseWithResult<AreaPageResult>>('/warehouse/warehouse/area/page', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
  });
};

export type AreaImportRequest = {
  file: File;
};

export type AreaImportResult = {
  successCount: number;
  failCount: number;
  failureDetails: {
    rowNum: number;
    code: string;
    reason: string;
  }[];
};

export const areaImport = (
  params: AreaImportRequest,
): Promise<ResponseWithResult<AreaImportResult>> => {
  const formData = new FormData();
  formData.append('file', params.file);

  return new Promise((resolve, reject) => {
    fetch(Const.baseHost + '/warehouse/warehouse/area/import', {
      method: 'POST',
      body: formData,
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
        operatorId: encodeURIComponent(
          JSON.parse(localStorage.getItem('userInfo') || '{}').userId || '',
        ),
        operatorName: encodeURIComponent(
          JSON.parse(localStorage.getItem('userInfo') || '{}').userName || '',
        ),
      },
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => resolve(data))
      .catch((error) => reject(error));
  });
};

export type AreaAddRequest = {
  id?: number;
  code?: string;
  name?: string;
  type?: number;
  warehouseId?: number;
  status?: number;
  remark?: string;
};
export type AreaAddResult = boolean;
export const areaAdd = (params: AreaAddRequest) => {
  return Fetch<ResponseWithResult<AreaAddResult>>('/warehouse/warehouse/area/add', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
  });
};

export type AreaUpdateRequest = {
  id?: number;
  code?: string;
  name?: string;
  type?: number;
  warehouseId?: number;
  status?: number;
  remark?: string;
};

export type AreaUpdateResult = boolean;
export const areaUpdate = (params: AreaUpdateRequest) => {
  return Fetch<ResponseWithResult<AreaUpdateResult>>('/warehouse/warehouse/area/update', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
  });
};

export type WarehousePageRequest = {
  operatorId?: string /* 操作员ID*/;
  operatorName?: string /* 操作员名称*/;
  pageNum?: number /* 页码*/;
  pageSize?: number /* 每页记录数*/;
  code?: string /* 编码*/;
  name?: string /* 名称*/;
  status?: number /* 状态*/;
  skipPermission?: boolean;
};

export type WarehousePageResult = {
  size?: number;
  current?: number;
  total?: number;
  pages?: number;
  records?: {
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    id?: number;
    code?: string;
    name?: string;
    address?: string;
    contact?: string;
    phone?: string;
    status?: number;
    statusName?: string;
    remark?: string;
  }[];
};

export const warehousePage = (params: WarehousePageRequest) => {
  return Fetch<ResponseWithResult<WarehousePageResult>>('/warehouse/warehouse/page', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
  });
};
