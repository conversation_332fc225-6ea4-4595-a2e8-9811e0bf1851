import React, { useEffect, useState } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import {
  areaPage,
  AreaPageRequest,
  AreaPageResult,
  warehousePage,
  WarehousePageRequest,
  WarehousePageResult,
} from '../services';

import { useSetState } from 'ahooks';
import { responseWithResultAsync } from '@/pages/warehouse-management/utils/utils';

export const useList = (form: WrappedFormUtils<AreaPageRequest>) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<AreaPageResult['records']>([]);
  const [pagination, setPagination] = useSetState({ current: 1, size: 20, total: 0 });
  const [condition, setCondition] = useState<AreaPageRequest>();
  const getList = async (data: AreaPageRequest) => {
    setLoading(true);
    const formValue = form?.getFieldsValue();

    const params = {
      ...data,
      ...(formValue as AreaPageRequest),
      pageNum: data?.pageNum ?? pagination.current,
      pageSize: data?.pageSize ?? pagination.size,
    };

    setCondition(params);
    const result = await responseWithResultAsync({
      request: areaPage,
      params,
    });
    setLoading(false);
    setList(result?.records ?? []);
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };

  useEffect(() => {
    getList({});
  }, []);
  return {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
  };
};
export type AreaPageListInfoType = NonNullable<AreaPageResult['records']>[number];

export const useWarehouseList = (skipPermission = false) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<WarehousePageResult['records']>([]);

  const getList = async (data: WarehousePageRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: warehousePage,
      params: {
        ...data,
        skipPermission,
      },
    });
    console.log(result);
    setLoading(false);
    setList(result?.records ?? []);
  };
  const hanelSearch = (name?: string) => {
    getList({
      name,
      pageNum: 1,
      pageSize: 100,
    });
  };
  useEffect(() => {
    getList({
      pageNum: 1,
      pageSize: 100,
    });
  }, []);
  return {
    list,
    loading,
    getList,
    setLoading,
    hanelSearch,
  };
};
