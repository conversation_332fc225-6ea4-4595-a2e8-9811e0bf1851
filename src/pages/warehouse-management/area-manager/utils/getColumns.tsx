import { ColumnProps } from 'antd/lib/table';
import React, { useMemo } from 'react';
import moment from 'moment';
import { toDecimal } from '@/utils/string';
import PopoverRowText from '@/components/PopoverRowText';
import { Space } from 'web-common-modules/antd-pro-components';
import CreateEditModal from '../CreateEditModal';
import { AreaPageListInfoType } from './hook';
import { STATUS_ENUM } from './enum';
import { WarehousePageResult } from '../services';
export const formatFee = (val?: string | number | null) => {
  if (val !== '' && val !== null && val !== undefined) {
    return toDecimal(Number(val), 2);
  }
  return '-';
};

export const useTable = (params: {
  onRefresh: () => void;
  onDisable: (info?: AreaPageListInfoType) => void;
  codeList?: {
    value?: string;
    label?: string;
  }[];
  warehouseList?: WarehousePageResult['records'];
  warehouseLoading?: boolean;
  hanelSearch?: (name?: string) => void;
}) => {
  const { onRefresh, onDisable, codeList, warehouseList, warehouseLoading, hanelSearch } = params;
  const columns = useMemo<ColumnProps<AreaPageListInfoType>[]>(
    () => [
      {
        dataIndex: 'index',
        title: '#',
        width: 40,
        render: (_, __, index) => index + 1,
      },

      {
        title: '状态',
        width: 100,
        dataIndex: 'status',
        render: (val: AreaPageListInfoType['status']) => STATUS_ENUM?.[val!] ?? '-',
      },
      {
        dataIndex: 'code',
        title: '库区代码',
        width: 150,
        render: (val: string) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '库区名称',
        width: 150,
        dataIndex: 'name',
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '库区类型',
        width: 150,
        dataIndex: 'typeName',
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '所属仓库',
        width: 150,
        dataIndex: 'warehouseName',
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '创建人',
        width: 150,
        dataIndex: 'createBy',
        render: (val) => val ?? '-',
      },
      {
        title: '创建时间',
        width: 150,
        dataIndex: 'createTime',
        render: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '操作',
        width: 150,
        dataIndex: 'action',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              {record.status === 1 ? (
                <a style={{ color: '#ff4d4f' }} onClick={() => onDisable(record)}>
                  禁用
                </a>
              ) : (
                <a onClick={() => onDisable(record)}>启用</a>
              )}
              <CreateEditModal
                type="edit"
                onRefresh={() => onRefresh()}
                info={record}
                codeList={codeList}
                warehouseList={warehouseList}
                warehouseLoading={warehouseLoading}
                hanelSearch={hanelSearch}
              />
            </Space>
          );
        },
      },
    ],
    [onRefresh, warehouseList, codeList, warehouseLoading],
  );
  return { columns };
};
