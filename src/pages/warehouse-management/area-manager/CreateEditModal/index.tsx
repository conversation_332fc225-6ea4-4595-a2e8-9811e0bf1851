import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { Modal, Button, Form, Input, Select, message } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import React, { useState } from 'react';
import { areaAdd, AreaAddRequest, areaUpdate } from '../services';
import { AreaPageListInfoType } from '../utils/hook';
import { responseWithResultAsync } from '../../utils/utils';
import { useWarehouseList } from '../utils/hook';
import { WarehousePageResult } from '../services';
const { Option } = Select;

interface PropsType
  extends FormComponentProps<
    AreaAddRequest & {
      typeInfo?: { label: string; key: number };
      warehouseIdInfo?: { label: string; key: number };
    }
  > {
  type: 'create' | 'edit';
  onRefresh: () => void;
  info?: AreaPageListInfoType;
  codeList?: {
    value?: string;
    label?: string;
  }[];
  warehouseList?: WarehousePageResult['records'];
  warehouseLoading?: boolean;
  hanelSearch?: (name?: string) => void;
}
const titleMap = {
  create: '新建库区',
  edit: '编辑库区',
};
const CreateEditModal: React.FC<PropsType> = ({
  type,
  form,
  onRefresh,
  info,
  codeList,
  warehouseList,
  warehouseLoading,
  hanelSearch,
}) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
  };
  const handleOpen = () => {
    setVisible(true);
    if (type === 'edit') {
      form.setFieldsValue({
        name: info?.name,
        code: info?.code,
        typeInfo: { label: info?.typeName, key: info?.type },
        warehouseIdInfo: { label: info?.warehouseName, key: info?.warehouseId },
      });
    }
  };
  const handleEdit = async (
    values: AreaAddRequest & {
      typeInfo?: { label: string; key: number };
      warehouseIdInfo?: { label: string; key: number };
    },
  ) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: areaUpdate,
      params: {
        ...values,
        status: info?.status,
        id: info?.id,
        type: values.typeInfo?.key,
        warehouseId: values.warehouseIdInfo?.key,
        typeInfo: undefined,
        warehouseIdInfo: undefined,
      },
    });
    if (result) {
      message.success('编辑成功');
      handleCancel();
      onRefresh();
    }
    setLoading(false);
  };

  const handleCreate = async (
    values: AreaAddRequest & {
      typeInfo?: { label: string; key: number };
      warehouseIdInfo?: { label: string; key: number };
    },
  ) => {
    console.log({
      ...values,
      status: 1,
      type: values.typeInfo?.key,
      warehouseId: values.warehouseIdInfo?.key,
      typeInfo: undefined,
      warehouseIdInfo: undefined,
    });
    setLoading(true);
    const result = await responseWithResultAsync({
      request: areaAdd,
      params: {
        ...values,
        status: 1,
        type: values.typeInfo?.key,
        warehouseId: values.warehouseIdInfo?.key,
        typeInfo: undefined,
        warehouseIdInfo: undefined,
      },
    });
    if (result) {
      message.success('新建成功');
      handleCancel();
      onRefresh();
    }
    setLoading(false);
  };

  const handleOk = () => {
    form?.validateFields((err, values) => {
      if (err) return;
      if (type === 'create') {
        handleCreate(values);
        return;
      }
      handleEdit(values);
    });
  };
  return (
    <>
      {type === 'create' && (
        <Button type="primary" icon="plus" onClick={handleOpen}>
          新建
        </Button>
      )}
      {type === 'edit' && <a onClick={handleOpen}>编辑</a>}
      <Modal
        visible={visible}
        title={titleMap[type]}
        onCancel={handleCancel}
        onOk={handleOk}
        confirmLoading={loading}
      >
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 12 }}>
          <Form.Item label="库区名称">
            {form?.getFieldDecorator('name', {
              rules: [{ required: true, message: '请输入库区名称' }],
            })(<Input style={{ width: '300px' }} maxLength={50} />)}
          </Form.Item>
          <Form.Item label="库区代码">
            {form?.getFieldDecorator('code', {
              rules: [
                { required: true, message: '请输入库区代码' },
                { pattern: /^[^\u4e00-\u9fa5]+$/, message: '不能有汉字' },
              ],
            })(<Input disabled={type === 'edit'} style={{ width: '300px' }} maxLength={50} />)}
          </Form.Item>
          <Form.Item label="库区类型">
            {form?.getFieldDecorator('typeInfo', {
              rules: [{ required: true, message: '请选择库区类型' }],
            })(
              <Select style={{ width: '300px' }} labelInValue>
                {codeList?.map((item) => (
                  <Option value={Number(item.value)} key={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="所属仓库">
            {form?.getFieldDecorator('warehouseIdInfo', {
              rules: [{ required: true, message: '请选择所属仓库' }],
            })(
              <Select
                style={{ width: '300px' }}
                loading={warehouseLoading}
                onBlur={() => {
                  hanelSearch?.(undefined);
                }}
                labelInValue
              >
                {warehouseList
                  ?.filter((item) => item.status === 1)
                  ?.map((item) => (
                    <Option value={item.id} key={item.id}>
                      {item.name}
                    </Option>
                  ))}
              </Select>,
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default Form.create<PropsType>()(CreateEditModal);
