import { Const, Fetch } from 'qmkit';
import { exportFile } from '../../utils';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: number;
  data: T;
}

export type SampleOutboundOrderPageRequest = {
  operatorId?: string;
  operatorName?: string;
  pageNum?: number;
  pageSize?: number;
  productName?: string;
  brand?: string;
  expressNo?: string;
  barcode?: string;
  orderNo?: string;
  type?: number;
  businessUserId?: string;
  status?: number;
  createBy?: string;
  sourceOrderNo?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  updateBy?: string;
  updateTimeStart?: string;
  updateTimeEnd?: string;
};

export type SampleOutboundOrderPageResult = {
  size?: number;
  current?: number;
  total?: number;
  records?: {
    id?: number;
    orderNo?: string;
    type?: number;
    typeName?: string;
    warehouseId?: number;
    warehouseCode?: string;
    warehouseName?: string;
    expressNo?: string;
    receiver?: string;
    contactInfo?: string;
    sender?: string;
    senderAddress?: string;
    businessUserId?: string;
    businessUserName?: string;
    status?: number;
    statusName?: string;
    sourceOrderNo?: string;
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
    planQuantity?: number;
    actualQuantity?: number;
    itemList?: {
      id?: number;
      orderId?: number;
      orderNo?: string;
      imageUrl?: string;
      productName?: string;
      barcode?: string;
      productCode?: string;
      brand?: string;
      specification?: string;
      quantity?: number;
      areaId?: number;
      areaCode?: string;
      areaName?: string;
      locationId?: number;
      locationCode?: string;
      locationName?: string;
      status?: number;
      statusName?: string;
      createTime?: string;
      updateTime?: string;
      createBy?: string;
      updateBy?: string;
    }[];
  }[];
  pages?: number;
};

/**
 * 库区管理分页查询
 */
export const sampleOutboundOrderPage = (params: SampleOutboundOrderPageRequest) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderPageResult>>(
    '/warehouse/warehouse/sample-delivery-orders/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};

// /warehouse/sample-storage-orders/batch-confirm-storage

export type SampleOutboundOrderBatchConfirmDeliveryRequest = {
  ids?: number[];
};

export type SampleOutboundOrderBatchConfirmDeliveryResult = boolean;

export const sampleOutboundOrderBatchConfirmDelivery = (
  params: SampleOutboundOrderBatchConfirmDeliveryRequest,
) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderBatchConfirmDeliveryResult>>(
    '/warehouse/warehouse/sample-delivery-orders/batch-confirm-delivery',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};

export type SampleOutboundOrderAddRequest = {
  id?: number;
  type?: number;
  warehouseId?: number;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: string;
  businessUserName?: string;
  sourceOrderNo?: string;
  itemList?: {
    id?: number;
    imageUrl?: string;
    productName?: string;
    brand?: string;
    barcode?: string;
    productCode?: string;
    specification?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    quantity?: number;
    areaId?: number;
    locationId?: number;
    sourceOrderNo?: string;
  }[];
};
export type SampleOutboundOrderAddResult = boolean;
export const sampleOutboundOrderAdd = (params: SampleOutboundOrderAddRequest) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderAddResult>>(
    '/warehouse/warehouse/sample-delivery-orders/add',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};

export type SampleOutboundOrderUpdateRequest = {
  id?: number;
  type?: number;
  warehouseId?: number;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: string;
  businessUserName?: string;
  sourceOrderNo?: string;
  itemList?: {
    id?: number;
    imageUrl?: string;
    productName?: string;
    brand?: string;
    barcode?: string;
    productCode?: string;
    specification?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    quantity?: number;
    areaId?: number;
    locationId?: number;
    sourceOrderNo?: string;
  }[];
};
export type SampleOutboundOrderUpdateResult = boolean;
export const sampleOutboundOrderUpdate = (params: SampleOutboundOrderUpdateRequest) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderUpdateResult>>(
    '/warehouse/warehouse/sample-delivery-orders/update',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};

export type SampleOutboundOrderDetailRequest = {
  operatorId?: string;
  operatorName?: string;
  id?: number;
};

export type SampleOutboundOrderDetailResult = {
  id?: number;
  orderNo?: string;
  type?: number;
  typeName?: string;
  warehouseId?: number;
  warehouseCode?: string;
  warehouseName?: string;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: string;
  businessUserName?: string;
  status?: number;
  statusName?: string;
  totalPlanQuantity?: number;
  totalActualQuantity?: number;
  sourceOrderNo?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  remark?: string;
  itemList?: {
    id?: number;
    orderId?: number;
    orderNo?: string;
    imageUrl?: string;
    productName?: string;
    barcode?: string;
    productCode?: string;
    brand?: string;
    specification?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    quantity?: number;
    areaId?: number;
    areaCode?: string;
    areaName?: string;
    locationId?: number;
    locationCode?: string;
    locationName?: string;
    status?: number;
    statusName?: string;
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
    currentInventoryQuantity?: number;
    inventoryId?: number;
  }[];
};

export const sampleOutboundOrderDetail = (params: SampleOutboundOrderDetailRequest) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderDetailResult>>(
    '/warehouse/warehouse/sample-delivery-orders/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};

export type SampleOutboundOrderExportRequest = {
  operatorId?: string;
  operatorName?: string;
  pageNum?: number;
  pageSize?: number;
  productName?: string;
  brand?: string;
  expressNo?: string;
  barcode?: string;
  orderNo?: string;
  type?: number;
  businessUserId?: string;
  status?: number;
  createBy?: string;
  sourceOrderNo?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  updateBy?: string;
  updateTimeStart?: string;
  updateTimeEnd?: string;
};

export const exportSampleOutboundOrder = (params: SampleOutboundOrderExportRequest) => {
  return exportFile<SampleOutboundOrderExportRequest, Blob>({
    url: Const.baseHost + '/warehouse/warehouse/sample-delivery-orders/export',
    params,
    // ai生成
    header: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      'Content-Type': 'application/json',
    },
    // 2024年12月31日 开山ai结尾共生成5行代码
  });
};
// /warehouse/sample-delivery-orders/confirm-delivery

export type SampleOutboundOrderConfirmDeliveryRequest = {
  id?: number;
};

export type SampleOutboundOrderConfirmDeliveryResult = boolean;

export const sampleOutboundOrderConfirmDelivery = (
  params: SampleOutboundOrderConfirmDeliveryRequest,
) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderConfirmDeliveryResult>>(
    '/warehouse/warehouse/sample-delivery-orders/confirm-delivery',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};

export type SampleOutboundOrderCancelRequest = {
  id?: number;
};

export type SampleOutboundOrderCancelResult = boolean;

export const sampleOutboundOrderCancel = (params: SampleOutboundOrderCancelRequest) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderCancelResult>>(
    '/warehouse/warehouse/sample-delivery-orders/cancel',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};
// /add-and-confirm-delivery

// ai生成
export type SampleOutboundOrderAddAndConfirmDeliveryRequest = {
  id?: number;
  type?: number;
  warehouseId?: number;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: string;
  businessUserName?: string;
  sourceOrderNo?: string;
  itemList?: {
    id?: number;
    imageUrl?: string;
    productName?: string;
    brand?: string;
    barcode?: string;
    productCode?: string;
    specification?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    quantity?: number;
    areaId?: number;
    locationId?: number;
    sourceOrderNo?: string;
  }[];
};

export type SampleOutboundOrderAddAndConfirmDeliveryResult = boolean;

export const sampleOutboundOrderAddAndConfirmDelivery = (
  params: SampleOutboundOrderAddAndConfirmDeliveryRequest,
) => {
  return Fetch<ResponseWithResult<SampleOutboundOrderAddAndConfirmDeliveryResult>>(
    '/warehouse/warehouse/sample-delivery-orders/add-and-confirm-delivery',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};
// 2024年12月31日 开山ai结尾共生成45行代码
