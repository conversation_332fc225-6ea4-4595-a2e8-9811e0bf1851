import { Input, Select } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useMemo } from 'react';
import SearchFormComponent, { searchItem } from '../searchFormComponent';
import { useEmployeeList } from '@/hooks/useEmployeeList';
import { useAreaList } from '@/pages/warehouse-management/location-manager/utils/hook';
import { useLocationList } from '../../../../../warehouse-inventory-management/utils/hook';

interface IProps extends FormComponentProps {
  onSearch: () => void;
  onReset: () => void;
  loading: boolean;
}

const { Option } = Select;

const SearchForm: React.FC<IProps> = ({ form, onSearch, onReset, loading }) => {
  const {
    list: businessList,
    getList: getBusinessList,
    loading: businessLoading,
    handleSearch: businessSearch,
  } = useEmployeeList({ bizRoleType: 'BUSINESS' });
  const reset = () => {
    onReset();
  };
  const {
    list: locationList,
    handleSearch: handleLocationSearch,
    loading: locationLoading,
  } = useLocationList();
  const {
    list: areaList,
    loading: areaLoading,
    getList: getAreaList,
    hanelSearch: hanelAreaSearch,
  } = useAreaList();
  const options: Record<string, searchItem> = useMemo(() => {
    return {
      barcode: {
        label: '条码',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      // productCode: {
      //   label: '商品编码',
      //   renderNode: <Input placeholder="请输入" maxLength={100} />,
      // },
      areaId: {
        label: '库区',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            onSearch={hanelAreaSearch}
            loading={areaLoading}
            onBlur={() => {
              hanelAreaSearch(undefined);
            }}
            defaultActiveFirstOption={false}
            filterOption={false}
            showSearch
          >
            {areaList?.map((item) => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        ),
      },
      locationId: {
        label: '库位',
        renderNode: (
          <Select
            placeholder="请选择"
            allowClear
            onSearch={handleLocationSearch}
            loading={locationLoading}
            onBlur={() => {
              handleLocationSearch(undefined);
            }}
            defaultActiveFirstOption={false}
            filterOption={false}
            showSearch
          >
            {locationList?.map((item) => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        ),
      },
      brand: {
        label: '品牌',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      productName: {
        label: '商品名称',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
    };
  }, [businessList, businessLoading, locationList, locationLoading, areaList, areaLoading]);

  return (
    <SearchFormComponent
      form={form}
      options={options}
      loading={loading}
      onSearch={onSearch}
      onReset={reset}
    />
  );
};

export default SearchForm;
