import { ColumnProps } from 'antd/lib/table';
import React, { useMemo } from 'react';
import { toDecimal } from '@/utils/string';
import PopoverRowText from '@/components/PopoverRowText';
import FileUpload from '@/components/FileUpload';
import { WarehouseInventoryManagementPageListInfoType } from '@/pages/warehouse-management/warehouse-inventory-management/utils/hook';
export const formatFee = (val?: string | number | null) => {
  if (val !== '' && val !== null && val !== undefined) {
    return toDecimal(Number(val), 2);
  }
  return '-';
};

export const useTable = () => {
  const columns = useMemo<ColumnProps<WarehouseInventoryManagementPageListInfoType>[]>(
    () => [
      {
        title: '#',
        dataIndex: 'index',
        width: 50,
        render: (_, __, index) => index + 1,
      },
      {
        title: '图片',
        dataIndex: 'imageUrl',
        width: 100,
        render: (val) => (
          <FileUpload
            value={val ? [val] : []}
            maxLen={1}
            typeCode={'SPU_IMG'}
            maxSize={20 * 1024 * 1024}
            accept={'.jpg,.jpeg,.png,.pdf'}
            disabled={true}
          />
        ),
      },
      {
        title: '商品名称',
        dataIndex: 'productName',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '条码',
        dataIndex: 'barcode',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      // {
      //   title: '商品编码',
      //   dataIndex: 'productCode',
      //   width: 200,
      //   render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      // },
      {
        title: '品牌',
        dataIndex: 'brand',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '类目',
        dataIndex: 'category',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '单位',
        dataIndex: 'unit',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '型号',
        dataIndex: 'model',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '规格',
        dataIndex: 'specification',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '所属商务',
        dataIndex: 'businessUserName',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '库存数量',
        dataIndex: 'quantity',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
    ],
    [],
  );
  return { columns };
};
