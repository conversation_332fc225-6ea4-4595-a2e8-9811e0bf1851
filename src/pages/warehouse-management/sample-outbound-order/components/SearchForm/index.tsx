import { DatePicker, Input, Select } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useMemo } from 'react';
import SearchFormComponent, { searchItem } from '../searchFormComponent';
import { STATUS_ENUM, TYPE_ENUM } from '../../utils/enum';
import { useEmployeeList } from '@/hooks/useEmployeeList';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
interface IProps extends FormComponentProps {
  onSearch: () => void;
  onReset: () => void;
  loading: boolean;
}

const { Option } = Select;

const SearchForm: React.FC<IProps> = ({ form, onSearch, onReset, loading }) => {
  const {
    list: businessList,
    getList: getBusinessList,
    loading: businessLoading,
    handleSearch: businessSearch,
  } = useEmployeeList({ bizRoleType: 'BUSINESS' });
  const reset = () => {
    onReset();
  };
  const { codeList: outboundTypeList } = useCode(CODE_ENUM.OUTBOUND_TYPE, {
    able: false,
  });
  const options: Record<string, searchItem> = useMemo(() => {
    return {
      productName: {
        label: '商品名称',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      brand: {
        label: '品牌名称',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      expressNo: {
        label: '快递单号',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      barcode: {
        label: '条码',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      orderNo: {
        label: '样品出库单号',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      typeList: {
        label: '出库类型',
        renderNode: (
          <Select placeholder="请选择" mode="multiple" maxTagCount={1} allowClear>
            {outboundTypeList?.map((item) => (
              <Option value={item.value} key={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
        ),
      },
      // businessUserId: {
      //   label: '所属商务',
      //   renderNode: (
      //     <Select
      //       placeholder="请选择"
      //       onSearch={businessSearch}
      //       filterOption={false}
      //       allowClear
      //       showSearch
      //       onBlur={() => {
      //         businessSearch('');
      //       }}
      //       loading={businessLoading}
      //     >
      //       {businessList?.map((item) => (
      //         <Option value={item.employeeId} key={item.employeeId}>
      //           {item.employeeName}
      //         </Option>
      //       ))}
      //     </Select>
      //   ),
      // },
      status: {
        label: '状态',
        renderNode: (
          <Select placeholder="请选择" allowClear>
            {Object.entries(STATUS_ENUM)
              .map((key) => ({ label: key[1], key: key[0] }))
              ?.filter((item) => typeof item.label !== 'number')
              .map((item) => (
                <Option value={item.key} key={item.key}>
                  {item.label}
                </Option>
              ))}
          </Select>
        ),
      },
      createBy: {
        label: '创建人',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      // sourceOrderNo: {
      //   label: '来源单号',
      //   renderNode: <Input placeholder="请输入" maxLength={100} />,
      // },
      createDate: {
        label: '创建日期',
        renderNode: <DatePicker.RangePicker format={'YYYY-MM-DD'} />,
      },
      updateBy: {
        label: '修改人',
        renderNode: <Input placeholder="请输入" maxLength={100} />,
      },
      updateDate: {
        label: '修改日期',
        renderNode: <DatePicker.RangePicker format={'YYYY-MM-DD'} />,
      },
    };
  }, [businessList, businessLoading]);

  return (
    <SearchFormComponent
      form={form}
      options={options}
      loading={loading}
      onSearch={onSearch}
      onReset={reset}
    />
  );
};

export default SearchForm;
