import { useSetState } from 'ahooks';
import { Button, Icon, message, Spin } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import BasicForm from './BasicForm';
import DetailedList, { ItemListInfoType } from './DetailedList';
import styles from './index.module.less';
import { history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import {
  sampleOutboundOrderAdd,
  SampleOutboundOrderAddRequest,
  sampleOutboundOrderDetail,
  SampleOutboundOrderDetailResult,
  sampleOutboundOrderUpdate,
  // ai生成
  sampleOutboundOrderAddAndConfirmDelivery,
  // 2024年12月25日 开山ai结尾共生成1行代码
} from '../services';
import { responseWithResultAsync } from '../../utils';
import SpuListModal from '../components/SpuListModal';
import { WarehouseInventoryManagementPageListInfoType } from '../../warehouse-inventory-management/utils/hook';

interface PropsType extends FormComponentProps<SampleOutboundOrderAddRequest> {
  form: WrappedFormUtils<SampleOutboundOrderAddRequest>;
  location: {
    state: {
      warehouseList: WarehouseInventoryManagementPageListInfoType[];
    };
  };
}
type ModalState = {
  loading?: boolean;
  detail?: SampleOutboundOrderDetailResult;
  visible: boolean;
};

const AddMarketingData: React.FC<PropsType> = ({ form, location }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false, visible: false });
  const [itemList, setItemList] = useState<ItemListInfoType[]>([]);
  const id = useMemo(() => getQueryParams().id as number, []);
  const type = useMemo(() => (getQueryParams()?.type as 'edit' | 'create') ?? 'create', []);
  const { delRoutetag } = useCloseAndJump();
  const handleCancel = () => {
    delRoutetag();
    history.replace('/sample-outbound-order');
  };
  const handleDetail = async () => {
    setState((state) => ({ ...state, loading: true }));
    const detail = await responseWithResultAsync({
      request: sampleOutboundOrderDetail,
      params: {
        id,
      },
    });
    setState((state) => ({ ...state, loading: false }));
    if (detail) {
      initForm(detail);
      setState((state) => ({ ...state, detail }));
    }
  };
  const formatParams = (
    values: SampleOutboundOrderAddRequest & { typeInfo?: { label: string; key: string } },
  ) => {
    console.log(values);
    const params = {
      ...values,
      type: values.typeInfo?.key,
    };

    return params as SampleOutboundOrderAddRequest;
  };
  const handleCreate = useCallback(
    async (params: SampleOutboundOrderAddRequest) => {
      const result = await responseWithResultAsync({
        request: sampleOutboundOrderAdd,
        params: { ...params, itemList },
      });
      setState((state) => ({ ...state, loading: false }));
      return result;
    },
    [itemList],
  );
  const handleUpdate = useCallback(
    async (params: SampleOutboundOrderAddRequest) => {
      const result = await responseWithResultAsync({
        request: sampleOutboundOrderUpdate,
        params: { ...params, id, itemList },
      });
      setState((state) => ({ ...state, loading: false }));
      return result;
    },
    [id, itemList],
  );
  const handleOk = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      setState((state) => ({ ...state, loading: true }));
      const params = formatParams(values);
      console.log(params);
      const result = type === 'create' ? await handleCreate(params) : await handleUpdate(params);
      setState((state) => ({ ...state, loading: false }));
      if (result) {
        message.success('提交成功');
        handleCancel();
      }
    });
  };

  // ai生成
  const handleSaveAndOutbound = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      setState((state) => ({ ...state, loading: true }));
      const params = formatParams(values);
      console.log(params);

      try {
        if (type === 'create') {
          // 创建模式：直接使用保存并出库接口
          const result = await responseWithResultAsync({
            request: sampleOutboundOrderAddAndConfirmDelivery,
            params: { ...params, itemList },
          });

          setState((state) => ({ ...state, loading: false }));

          if (result) {
            message.success('保存并出库成功');
            handleCancel();
          }
        } else {
          // 编辑模式：先更新再出库
          const updateResult = await handleUpdate(params);

          if (updateResult) {
            // 使用保存并出库接口（对于编辑模式，传入id）
            const result = await responseWithResultAsync({
              request: sampleOutboundOrderAddAndConfirmDelivery,
              params: { ...params, id, itemList },
            });

            setState((state) => ({ ...state, loading: false }));

            if (result) {
              message.success('保存并出库成功');
              handleCancel();
            } else {
              message.error('保存成功，但出库失败');
            }
          } else {
            setState((state) => ({ ...state, loading: false }));
          }
        }
      } catch (error) {
        setState((state) => ({ ...state, loading: false }));
        message.error('操作失败');
      }
    });
  };
  // 2024年12月25日 开山ai结尾共生成39行代码

  const createLabelKey = (obj: { label?: string | number; key?: string | number }) => {
    return obj.label || obj.key ? obj : undefined;
  };
  const initForm = async (info: SampleOutboundOrderDetailResult) => {
    const formValues: any = { ...info };
    if (info.type && info.typeName) {
      formValues.typeInfo = {
        label: info.typeName,
        key: info.type,
      };
    }
    form.setFieldsValue({
      ...formValues,
    });
  };

  // ai生成
  const processItemData = (items: any[]) => {
    return items.map((item) => ({
      ...item,
      inventoryId: item.id,
      currentInventoryQuantity: item.quantity,
      planQuantity: item.quantity,
      actualQuantity: item.quantity,
    }));
  };
  // 2024年05月29日 开山ai结尾共生成3行代码

  useEffect(() => {
    if (type === 'edit' && id) {
      handleDetail();
    }
  }, [type]);
  const handleSelect = (rows: any[]) => {
    if (!rows || rows.length === 0) {
      message.warning('请选择商品');
      return;
    }
    const newRows = rows.filter((item) => !itemList.some((item2) => item2.inventoryId === item.id));
    if (newRows.length === 0) {
      message.warning('所选商品已全部存在于列表中，请勿重复添加');
      return;
    }
    if (newRows.length < rows.length) {
      message.info(
        `已过滤${rows.length - newRows.length}个重复商品，成功添加${newRows.length}个商品`,
      );
    }
    const processedRows = processItemData(newRows);
    setItemList([...itemList, ...processedRows]);
  };
  const warehouseId = useMemo(() => form.getFieldsValue()?.warehouseId, [form.getFieldsValue()]);

  useEffect(() => {
    if (warehouseId) {
      setItemList([]);
    }
  }, [warehouseId]);

  const handleOpenModal = () => {
    if (form.getFieldsValue()?.warehouseId) {
      setState((state) => ({ ...state, visible: true }));
    } else {
      message.warning('请先选择仓库');
    }
  };
  useEffect(() => {
    if (location.state?.warehouseList?.length) {
      const warehouseId = location.state.warehouseList[0].warehouseId;
      form.setFieldsValue({
        warehouseId,
      });
      setTimeout(() => {
        setItemList(processItemData(location.state?.warehouseList));
      }, 100);
    }
  }, [location.state?.warehouseList, type]);
  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="基本信息">
            <div className={styles.extra}>
              <BasicForm form={form} type={type} />
            </div>
          </Card>
          <Card title="明细信息">
            <div className={styles.extra}>
              <div className={styles.extra}>
                <DetailedList
                  detail={modalState?.detail}
                  setItemList={setItemList}
                  itemList={itemList}
                  handleOpenModal={handleOpenModal}
                />
              </div>
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          <Button
            type="primary"
            style={{ marginLeft: 8 }}
            onClick={() => {
              handleSaveAndOutbound();
            }}
          >
            保存并出库
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleOk();
            }}
          >
            保存
          </Button>

          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
      <SpuListModal
        visible={modalState.visible}
        onCancel={() => setState((state) => ({ ...state, visible: false }))}
        onSelect={handleSelect}
        onOpen={() => setState((state) => ({ ...state, visible: true }))}
        warehouseId={warehouseId}
      />
    </PageLayout>
  );
};

export default Form.create()(AddMarketingData);
