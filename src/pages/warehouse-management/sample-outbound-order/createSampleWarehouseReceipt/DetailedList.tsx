import { Button, Input, InputNumber, message, Select, Table } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { SampleOutboundOrderDetailResult } from '../services';
import styles from './index.module.less';
import Space from '@/components/Space';
import { ColumnProps, TableRowSelection } from 'antd/lib/table';
import { useLocationList } from '../../sample-warehouse-receipt/utils/hook';
import FileUpload from '@/components/FileUpload';
import PopoverRowText from '@/components/PopoverRowText';
const { Option } = Select;
type PropsType = {
  detail?: SampleOutboundOrderDetailResult;
  setItemList: React.Dispatch<React.SetStateAction<ItemListInfoType[]>>;
  itemList: ItemListInfoType[];
  handleOpenModal: () => void;
};
export type ItemListInfoType = NonNullable<SampleOutboundOrderDetailResult['itemList']>[number];
const DetailedList: React.FC<PropsType> = ({ detail, setItemList, itemList, handleOpenModal }) => {
  const [selectedKeys, setSelectKeys] = useState<number[]>([]);

  const { list: locationList, handleSearch } = useLocationList();

  const rowSelection: TableRowSelection<ItemListInfoType> = {
    onChange: (selectedRowKeys) => {
      setSelectKeys(selectedRowKeys as number[]);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
  };
  const changeDataSource = (key: keyof ItemListInfoType, index: number, value: any) => {
    const newDataSource = [...itemList];
    newDataSource[index] = { ...newDataSource[index], [key]: value };
    setItemList(newDataSource);
  };
  const columns = useMemo<ColumnProps<ItemListInfoType>[]>(
    () => [
      {
        title: '#',
        dataIndex: 'index',
        width: 50,
        render: (_, __, index) => index + 1,
      },
      {
        title: '图片',
        dataIndex: 'imageUrl',
        width: 100,
        render: (val, _, index) =>
          val ? (
            <FileUpload
              value={val ? [val] : []}
              onChange={(val) => {
                changeDataSource('imageUrl', index, val?.[0]);
              }}
              maxLen={1}
              typeCode={'SPU_IMG'}
              maxSize={20 * 1024 * 1024}
              accept={'.jpg,.jpeg,.png,.pdf'}
            />
          ) : (
            '-'
          ),
      },
      {
        title: '商品名称',
        dataIndex: 'productName',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '条码',
        dataIndex: 'barcode',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      // {
      //   title: '商品编码',
      //   dataIndex: 'productCode',
      //   width: 200,
      //   render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      // },
      {
        title: '品牌',
        dataIndex: 'brand',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '类目',
        dataIndex: 'category',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '单位',
        dataIndex: 'unit',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '型号',
        dataIndex: 'model',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '规格',
        dataIndex: 'specification',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      // {
      //   title: '计划出库数量',
      //   dataIndex: 'planQuantity',
      //   width: 200,
      //   render: (val, records, index) => (
      //     <InputNumber
      //       style={{ width: '100%' }}
      //       value={!val && val !== 0 ? records?.currentInventoryQuantity : val}
      //       placeholder="请输入"
      //       min={0}
      //       max={records?.currentInventoryQuantity ?? 0}
      //       precision={0}
      //       maxLength={8}
      //       onChange={(e) => {
      //         // console.log(e, defaultQuantityMap.get(itemList[index]?.id as number));
      //         const maxNum = records?.currentInventoryQuantity ?? 0;
      //         if (e && maxNum && e > maxNum) {
      //           message.warning('计划出库数量不能大于库存数量');
      //           return;
      //         }
      //         changeDataSource('planQuantity', index, e);
      //       }}
      //     />
      //   ),
      // },
      {
        title: '出库数量',
        dataIndex: 'actualQuantity',
        width: 200,
        render: (val, records, index) => (
          <InputNumber
            style={{ width: '100%' }}
            value={!val && val !== 0 ? records?.currentInventoryQuantity : val}
            placeholder="请输入"
            max={records?.currentInventoryQuantity ?? 0}
            min={0}
            precision={0}
            maxLength={8}
            onChange={(e) => {
              const maxNum = records?.currentInventoryQuantity ?? 0;
              if (e && maxNum && e > maxNum) {
                message.warning('实际出库数量不能大于库存数量');
                return;
              }
              changeDataSource('actualQuantity', index, e);
            }}
          />
        ),
      },
      {
        title: '库区/库位',
        dataIndex: 'locationName',
        width: 200,
        render: (_, record) =>
          record.locationName ? (
            <PopoverRowText text={record.areaName + '/' + record.locationName} />
          ) : (
            '-'
          ),
        // render: (val, _, index) => (
        //   <Select
        //     style={{ width: '100%' }}
        //     value={val}
        //     placeholder="请选择"
        //     onSearch={handleSearch}
        //     onBlur={() => {
        //       handleSearch('');
        //     }}
        //     onChange={(e) => {
        //       setItemList(
        //         itemList.map((item, i) => {
        //           if (i === index) {
        //             item.locationId = e;
        //             item.areaId = locationList?.find((item) => item.id === e)?.areaId;
        //           }
        //           return item;
        //         }),
        //       );
        //     }}
        //     showSearch
        //     allowClear
        //   >
        //     {locationList?.map((item) => (
        //       <Option key={item.id} value={item.id}>
        //         {item?.areaName + '/' + item?.name}
        //       </Option>
        //     ))}
        //   </Select>
        // ),
      },
      {
        title: '所属商务',
        dataIndex: 'businessUserName',
        width: 200,
        render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
      },
      {
        title: '库存数量',
        dataIndex: 'currentInventoryQuantity',
        width: 200,
        render: (_, records, index) => {
          const maxNum = records?.currentInventoryQuantity ?? 0;
          return maxNum ? <PopoverRowText text={String(maxNum)} /> : '-';
        },
      },
    ],
    [itemList, locationList],
  );
  useEffect(() => {
    if (detail) {
      setItemList(detail?.itemList ?? []);
    }
  }, [detail]);
  const handleAddNewList = () => {
    if (!handleOpenModal) {
      message.warning('请先选择仓库');
      return;
    }
    handleOpenModal();
  };
  const handleDeleteList = (index: number[]) => {
    if (index.length === 0) {
      message.warning('请选择要删除的商品');
      return;
    }
    setItemList(itemList.filter((_, i) => !index.includes(i)));
    setSelectKeys([]);
  };

  return (
    <section style={{ padding: '0 24px' }} className={styles['list-table']}>
      <Space style={{ marginBottom: 16 }}>
        <Button type="primary" onClick={handleAddNewList}>
          新增
        </Button>
        <Button type="danger" onClick={() => handleDeleteList(selectedKeys)} ghost>
          删除
        </Button>
      </Space>
      <Table
        columns={columns}
        dataSource={itemList}
        rowKey={'index'}
        pagination={false}
        rowSelection={rowSelection}
      />
    </section>
  );
};

export default DetailedList;
