import { Input, Select } from 'antd';
import Form, { WrappedFormUtils } from 'antd/lib/form/Form';
import React from 'react';
import styles from './index.module.less';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { useWarehouseList } from '../../area-manager/utils/hook';
import { SampleOutboundOrderAddRequest } from '../services';
const { Item } = Form;
const { Option } = Select;
type PropsType = {
  form: WrappedFormUtils<SampleOutboundOrderAddRequest>;
  type?: 'create' | 'edit';
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 12,
  },
};
const formItemStyle: React.CSSProperties = {
  width: '200px',
};
const formStyle: React.CSSProperties = {
  width: '25%',
};

const BasicForm: React.FC<PropsType> = ({ form, type }) => {
  const { getFieldDecorator } = form;

  const { codeList } = useCode(CODE_ENUM.OUTBOUND_TYPE, { able: true });
  const { list: warehouseList, loading: warehouseLoading, hanelSearch } = useWarehouseList();

  return (
    <>
      <Form {...formItemLayout} className={styles['form-box']}>
        <Item label="出库类型" style={formStyle}>
          {getFieldDecorator('typeInfo', {
            rules: [
              {
                required: true,
                message: '请选择出库类型',
              },
            ],
          })(
            <Select placeholder="请选择" allowClear style={formItemStyle} labelInValue>
              {codeList?.map((item) => (
                <Option value={Number(item.value)} key={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="仓库名称" style={formStyle}>
          {getFieldDecorator('warehouseId', {
            rules: [
              {
                required: true,
                message: '请选择仓库',
              },
            ],
          })(
            <Select
              placeholder="请选择"
              style={formItemStyle}
              showSearch
              onSearch={hanelSearch}
              filterOption={false}
              loading={warehouseLoading}
              onBlur={() => {
                hanelSearch('');
              }}
            >
              {warehouseList
                ?.filter((item) => item.status === 1)
                ?.map((item) => (
                  <Option value={item.id} key={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>,
          )}
        </Item>
        <Item label="快递单号" style={formStyle}>
          {getFieldDecorator('expressNo')(
            <Input style={formItemStyle} placeholder="请输入快递单号" maxLength={50} />,
          )}
        </Item>
        <Item label="收件人" style={formStyle}>
          {getFieldDecorator('receiver')(
            <Input style={formItemStyle} placeholder="请输入收件人" maxLength={20} />,
          )}
        </Item>
        <Item label="联系方式" style={formStyle}>
          {getFieldDecorator('contactInfo')(
            <Input style={formItemStyle} placeholder="请输入联系方式" maxLength={20} />,
          )}
        </Item>
        <Item label="发货人" style={formStyle}>
          {getFieldDecorator('sender')(
            <Input style={formItemStyle} placeholder="请输入发货人" maxLength={20} />,
          )}
        </Item>
        <Item label="发货地址" style={formStyle}>
          {getFieldDecorator('senderAddress')(
            <Input style={formItemStyle} placeholder="请输入发货地址" maxLength={100} />,
          )}
        </Item>
        <Item label="备注" style={formStyle}>
          {getFieldDecorator('remark')(
            <Input style={formItemStyle} placeholder="请输入备注" maxLength={200} />,
          )}
        </Item>
      </Form>
    </>
  );
};

export default BasicForm;
