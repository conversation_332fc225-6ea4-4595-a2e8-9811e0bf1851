import { Table } from 'antd';
import React, { useMemo } from 'react';
import { SampleOutboundOrderDetailResult } from '../services';
import { ColumnProps } from 'antd/lib/table';
import PopoverRowText from '@/components/PopoverRowText';
import FileUpload from '@/components/FileUpload';

type PropsType = {
  info?: SampleOutboundOrderDetailResult;
};
export type ItemListInfoType = NonNullable<SampleOutboundOrderDetailResult['itemList']>[number];
const DetailedList: React.FC<PropsType> = ({ info }) => {
  // ai生成
  const columns: ColumnProps<ItemListInfoType>[] = [
    {
      title: '#',
      dataIndex: 'index',
      width: 60,
      render: (_, __, index) => (index === info?.itemList?.length ? '合计' : index + 1),
    },
    {
      title: '图片',
      dataIndex: 'imageUrl',
      width: 100,
      render: (val) =>
        val ? (
          <FileUpload
            value={val ? [val] : []}
            maxLen={1}
            typeCode={'SPU_IMG'}
            maxSize={20 * 1024 * 1024}
            accept={'.jpg,.jpeg,.png,.pdf'}
            disabled={true}
          />
        ) : (
          '-'
        ),
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '条码',
      dataIndex: 'barcode',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '类目',
      dataIndex: 'category',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '型号',
      dataIndex: 'model',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '规格',
      dataIndex: 'specification',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '出库数量',
      dataIndex: 'actualQuantity',
      width: 200,
      render: (val) => val ?? 0,
    },
    {
      title: '库区/库位',
      dataIndex: 'locationId',
      width: 200,
      render: (val, record) => (val ? record?.areaName + '/' + record?.locationName : '-'),
    },
    {
      title: '所属商务',
      dataIndex: 'businessUserName',
      width: 200,
      render: (val) => val ?? '-',
    },
  ];
  const showList = useMemo(() => {
    return info?.itemList?.length
      ? [
          ...(info?.itemList ?? []),
          {
            actualQuantity: info?.totalActualQuantity,
          },
        ]
      : [];
  }, [info?.itemList]);
  // 2025年1月14日 开山ai结尾共生成43行代码
  return <Table columns={columns} dataSource={showList} rowKey={'index'} pagination={false} />;
};

export default DetailedList;
