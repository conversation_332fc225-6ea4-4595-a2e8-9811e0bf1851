import { Const, Fetch } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: number;
  data: T;
}

export type WarehousePageRequest = {
  operatorId?: string;
  operatorName?: string;
  pageNum?: number;
  pageSize?: number;
  code?: string;
  name?: string;
  status?: number;
};

export type WarehousePageResult = {
  size?: number;
  current?: number;
  total?: number;
  records?: {
    id: number;
    createBy: string;
    createTime: string;
    updateBy: string;
    updateTime: string;
    code: string;
    name: string;
    address: string;
    contact: string;
    phone: string;
    status: number;
    statusName: string;
    remark: string;
  }[];
};

/**
 * 仓库管理分页查询
 */
export const warehousePage = (params: WarehousePageRequest) => {
  return Fetch<ResponseWithResult<WarehousePageResult>>('/warehouse/warehouse/page', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
  });
};

export type WarehouseAddRequest = {
  code: string;
  name: string;
  address: string;
  contact: string;
  phone: string;
  status: number;
  remark: string;
  id?: number;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
};
export type WarehouseAddResult = boolean;
export const warehouseAdd = (params: WarehouseAddRequest) => {
  return Fetch<ResponseWithResult<WarehouseAddResult>>('/warehouse/warehouse/add', {
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
    method: 'POST',
    body: JSON.stringify(params),
  });
};

export type WarehouseUpdateRequest = {
  id?: number;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  code?: string;
  name?: string;
  address?: string;
  contact?: string;
  phone?: string;
  status?: number;
  remark?: string;
};

export type WarehouseUpdateResult = boolean;
export const warehouseUpdate = (params: WarehouseUpdateRequest) => {
  return Fetch<ResponseWithResult<WarehouseUpdateResult>>('/warehouse/warehouse/update', {
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
    method: 'POST',
    body: JSON.stringify(params),
  });
};

export type WarehouseDeleteRequest = {
  id?: number;
};
export type WarehouseDeleteResult = boolean;
export const warehouseDelete = (params: WarehouseDeleteRequest) => {
  return Fetch<ResponseWithResult<WarehouseDeleteResult>>('/warehouse/warehouse/delete', {
    headers: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
    },
    method: 'POST',
    body: JSON.stringify(params),
  });
};
