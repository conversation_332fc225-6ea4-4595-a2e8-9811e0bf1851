import { Modal, Button, Form, Input, Select, message, InputNumber } from 'antd';
import { FormComponentProps } from 'antd/lib/form';
import React, { useState } from 'react';
import { warehouseAdd, WarehouseAddRequest, warehouseUpdate } from '../services';
import { WarehousePageListInfoType, useAreaList } from '../utils/hook';
import { responseWithResultAsync } from '@/pages/warehouse-management/utils/utils';

interface PropsType extends FormComponentProps<WarehouseAddRequest> {
  type: 'create' | 'edit';
  onRefresh: () => void;
  info?: WarehousePageListInfoType;
}
const titleMap = {
  create: '新建仓库',
  edit: '编辑仓库',
};
const CreateEditModal: React.FC<PropsType> = ({ type, form, onRefresh, info }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
  };
  const handleOpen = () => {
    setVisible(true);
    if (type === 'edit') {
      form.setFieldsValue({
        name: info?.name,
        code: info?.code,
        address: info?.address,
        contact: info?.contact,
        phone: info?.phone,
        remark: info?.remark,
      });
    }
  };
  const handleEdit = async (values: WarehouseAddRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: warehouseUpdate,
      params: { ...values, status: info?.status, id: info?.id },
    });
    setLoading(false);
    if (result) {
      message.success('编辑成功');
      handleCancel();
      onRefresh();
    }
    !result && result !== undefined && message.error('编辑失败');
  };

  const handleCreate = async (values: WarehouseAddRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: warehouseAdd,
      params: { ...values, status: 1 },
    });
    setLoading(false);
    if (result) {
      message.success('新建成功');
      handleCancel();
      onRefresh();
      return;
    }
    !result && result !== undefined && message.error('新建失败');
  };

  const handleOk = () => {
    form?.validateFields((err, values) => {
      if (err) return;
      if (type === 'create') {
        handleCreate(values);
        return;
      }
      handleEdit(values);
    });
  };

  return (
    <>
      {type === 'create' && (
        <Button type="primary" icon="plus" onClick={handleOpen}>
          新建
        </Button>
      )}
      {type === 'edit' && <a onClick={handleOpen}>编辑</a>}
      <Modal
        visible={visible}
        title={titleMap[type]}
        onCancel={handleCancel}
        onOk={handleOk}
        confirmLoading={loading}
      >
        <Form labelCol={{ span: 5 }} wrapperCol={{ span: 12 }}>
          <Form.Item label="仓库名称">
            {form?.getFieldDecorator('name', {
              rules: [{ required: true, message: '请输入仓库名称' }],
            })(<Input maxLength={50} style={{ width: '300px' }} />)}
          </Form.Item>
          <Form.Item label="仓库编码">
            {form?.getFieldDecorator('code', {
              rules: [
                { required: true, message: '请输入仓库编码' },
                { pattern: /^[^\u4e00-\u9fa5]+$/, message: '不能有汉字' },
              ],
            })(<Input maxLength={50} style={{ width: '300px' }} disabled={type === 'edit'} />)}
          </Form.Item>

          <Form.Item label="仓库地址">
            {form?.getFieldDecorator('address', {
              rules: [{ required: true, message: '请输入仓库地址' }],
            })(<Input maxLength={100} style={{ width: '300px' }} />)}
          </Form.Item>
          <Form.Item label="联系人">
            {form?.getFieldDecorator('contact', {
              rules: [{ required: true, message: '请输入联系人' }],
            })(<Input maxLength={50} style={{ width: '300px' }} />)}
          </Form.Item>
          <Form.Item label="联系电话">
            {form?.getFieldDecorator('phone', {
              rules: [
                { required: true, message: '请输入联系电话' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
              ],
            })(<Input maxLength={50} style={{ width: '300px' }} />)}
          </Form.Item>
          <Form.Item label="备注">
            {form?.getFieldDecorator('remark')(
              <Input maxLength={200} style={{ width: '300px' }} />,
            )}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default Form.create<PropsType>()(CreateEditModal);
