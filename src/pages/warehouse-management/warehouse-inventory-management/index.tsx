import { Button, Form, message, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { useCallback, useEffect, useState } from 'react';
import PaginationProxy from '@/common/constants/Pagination';
import PageLayout from '@/components/PageLayout/index';
import styles from './index.module.less';
import { useTableHeight } from '@/common/constants/hooks/index';
import SearchForm from './components/SearchForm';
import { useTable } from './utils/getColumns';
import {
  WarehouseInventoryManagementPageListInfoType,
  SearchFormType,
  useList,
} from './utils/hook';
import { ButtonProxy } from 'web-common-modules/components';

import { TableRowSelection } from 'antd/lib/table';
import { downloadFile, responseWithResultAsync } from '../utils';
import {
  exportWarehouseInventoryManagement,
  ExportWarehouseInventoryManagementRequest,
} from './services';
import { history } from 'qmkit';
import SortColumnTable from '@/components/SortColumnTable';
import { SortBizTypeEnum } from '@/components/SortColumnTable/type';

const WarehouseInventoryManagement: React.FC<FormComponentProps<SearchFormType>> = ({ form }) => {
  const { list, loading, getList, pagination, condition, setLoading } = useList(form);
  const [selectedKeys, setSelectKeys] = useState<number[]>([]);
  const [selectedRows, setSelectRows] = useState<WarehouseInventoryManagementPageListInfoType[]>(
    [],
  );

  const onReset = () => {
    form.resetFields();
    setSelectKeys([]);
    setSelectRows([]);
    getList({
      pageNum: 1,
      pageSize: 20,
    });
  };
  const onRefresh = () => {
    getList({});
    setSelectKeys([]);
  };
  useEffect(() => {
    onRefresh();
  }, []);
  const { getHeight, tableHeight } = useTableHeight(80);

  const rowSelection: TableRowSelection<WarehouseInventoryManagementPageListInfoType> = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRowKeys);
      setSelectRows(selectedRows);
      setSelectKeys(selectedRowKeys as number[]);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
  };

  const { columns } = useTable();
  const handleExport = async (params: ExportWarehouseInventoryManagementRequest) => {
    try {
      const result = await exportWarehouseInventoryManagement(params);
      if (result) {
        if (result instanceof Blob) {
          downloadFile({ blob: result, name: '仓库库存管理' });
        }
        message.success('导出成功');
        return;
      }
      message.error('导出失败');
    } catch (error) {
      message.error('导出失败');
    }
  };
  const handleBatchOutbound = useCallback(() => {
    if (!selectedRows.length) {
      message.warning('请选择要出库的商品');
      return;
    }

    const firstWarehouseId = selectedRows[0].warehouseId;
    const isAllSameWarehouse = selectedRows.every((item) => item.warehouseId === firstWarehouseId);

    if (!isAllSameWarehouse) {
      message.error('只能选择同一仓库的商品进行批量出库');
      return;
    }

    history.push({
      pathname: 'sample-outbound-order-create',
      state: {
        type: 'create',
        warehouseList: selectedRows,
      },
    });
  }, [selectedRows]);

  return (
    <PageLayout
      className={styles['cooperation-report-contain']}
      routePath="/warehouse-inventory-management"
    >
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            form={form}
            loading={loading}
            onSearch={() => {
              getList({ pageNum: 1 });
              setSelectKeys([]);
            }}
            onReset={onReset}
          />

          <div className="flex items-center mb-16">
            <ButtonProxy
              className="mr-8"
              style={{ borderColor: '#999999', color: '#444444' }}
              onClick={() => {
                handleExport(condition as ExportWarehouseInventoryManagementRequest);
              }}
            >
              <span>导出</span>
            </ButtonProxy>
            <Button type="primary" onClick={handleBatchOutbound}>
              批量出库
            </Button>
          </div>
        </div>
        <div className={styles.boardTable} style={{ flex: 1 }}>
          <SortColumnTable
            // disSortColumns={['index']}
            disabledColumns={['sortColumn']}
            rowKey="id"
            loading={loading}
            columns={columns}
            dataSource={list}
            pagination={false}
            scroll={{ y: tableHeight, x: '100%' }}
            rowSelection={rowSelection}
            bizType={SortBizTypeEnum.WAREHOUSE_INVENTORY_MANAGEMENT_SORT}
            setLoading={setLoading}
          />
          {/* <Table
            rowKey="id"
            loading={loading}
            columns={columns}
            dataSource={list}
            pagination={false}
            scroll={{ y: tableHeight, x: '100%' }}
            rowSelection={rowSelection}
          /> */}
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <PaginationProxy
            current={pagination?.current}
            pageSize={pagination?.size}
            total={pagination?.total}
            // @ts-ignore
            onChange={(current, size) => {
              setSelectKeys([]);
              getList({
                pageNum: current,
                pageSize: size,
              });
            }}
            valueType="flatten"
            pageSizeOptions={['5', '10', '20', '50', '100']}
          />
        </div>
      </div>
    </PageLayout>
  );
};

export default Form.create()(WarehouseInventoryManagement);
