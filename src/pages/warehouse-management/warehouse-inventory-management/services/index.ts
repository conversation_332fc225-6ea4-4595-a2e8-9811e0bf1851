import { Const, Fetch } from 'qmkit';
import { exportFile } from '../../utils/utils';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: number;
  data: T;
}

export type WarehouseInventoryManagementPageRequest = {
  operatorId?: string;
  operatorName?: string;
  pageNum?: number;
  pageSize?: number;
  barcode?: string;
  productCode?: string;
  warehouseId?: number;
  areaId?: number;
  locationId?: number;
  brand?: string;
  productName?: string;
  expireDateStart?: string;
  expireDateEnd?: string;
  expressNo?: string;
};

export type WarehouseInventoryManagementPageResult = {
  size?: number;
  current?: number;
  total?: number;
  records?: {
    id?: number;
    barcode?: string;
    productCode?: string;
    productName?: string;
    warehouseId?: number;
    warehouseCode?: string;
    warehouseName?: string;
    areaId?: number;
    areaCode?: string;
    areaName?: string;
    locationId?: number;
    locationCode?: string;
    locationName?: string;
    specification?: string;
    brand?: string;
    businessUserId?: string;
    businessUserName?: string;
    quantity?: number;
    imageUrl?: string;
    sourceOrderNo?: string;
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
    expireDateStart?: string;
    expireDateEnd?: string;
  }[];
  pages?: number;
};

/**
 * 库区管理分页查询
 */
export const warehouseInventoryManagementPage = (
  params: WarehouseInventoryManagementPageRequest,
) => {
  return Fetch<ResponseWithResult<WarehouseInventoryManagementPageResult>>(
    '/warehouse/warehouse/inventory/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      // ai生成
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
      // 2024年12月31日 开山ai结尾共生成4行代码
    },
  );
};

export type ExportWarehouseInventoryManagementRequest = {
  operatorId?: string;
  operatorName?: string;
  pageNum?: number;
  pageSize?: number;
  barcode?: string;
  productCode?: string;
  warehouseId?: number;
  areaId?: number;
  locationId?: number;
  brand?: string;
  productName?: string;
};

export type ExportWarehouseInventoryManagementResponse = {
  success: boolean;
  message: string;
  code: number;
  data: string;
};

export const exportWarehouseInventoryManagement = (
  params: ExportWarehouseInventoryManagementRequest,
) => {
  return exportFile<ExportWarehouseInventoryManagementRequest, Blob>({
    url: Const.baseHost + '/warehouse/warehouse/inventory/export',
    params,
    // ai生成
    header: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      'Content-Type': 'application/json',
    },
    // 2024年12月31日 开山ai结尾共生成5行代码
  });
};
