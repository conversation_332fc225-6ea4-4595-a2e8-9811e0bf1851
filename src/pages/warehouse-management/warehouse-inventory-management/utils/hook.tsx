import React, { useEffect, useState } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import {
  warehouseInventoryManagementPage,
  WarehouseInventoryManagementPageRequest,
  WarehouseInventoryManagementPageResult,
} from '../services';

import { useSetState } from 'ahooks';
import { responseWithResultAsync } from '@/pages/warehouse-management/utils/utils';
import { Moment } from 'moment';
import {
  locationPage,
  LocationPageRequest,
  LocationPageResult,
} from '../../location-manager/services';
import moment from 'moment';

export type SearchFormType = WarehouseInventoryManagementPageRequest;

export const useList = (form: WrappedFormUtils<SearchFormType>) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<WarehouseInventoryManagementPageResult['records']>([]);
  const [pagination, setPagination] = useSetState({ current: 1, size: 20, total: 0 });
  const [condition, setCondition] = useState<WarehouseInventoryManagementPageRequest>();
  const getList = async (data: WarehouseInventoryManagementPageRequest) => {
    setLoading(true);
    const formValue = form?.getFieldsValue();
    const params = {
      ...data,
      ...(formValue as SearchFormType),
      pageNum: data?.pageNum ?? pagination.current,
      pageSize: data?.pageSize ?? pagination.size,
      expireDateStart: formValue?.expirationDate?.[0]
        ? moment(formValue?.expirationDate?.[0]).format('YYYY-MM-DD')
        : undefined,
      expireDateEnd: formValue?.expirationDate?.[1]
        ? moment(formValue?.expirationDate?.[1]).format('YYYY-MM-DD')
        : undefined,
    };

    setCondition(params);
    const result = await responseWithResultAsync({
      request: warehouseInventoryManagementPage,
      params,
    });
    setLoading(false);
    setList(result?.records ?? []);
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };

  useEffect(() => {
    getList({});
  }, []);
  return {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
  };
};
export type WarehouseInventoryManagementPageListInfoType = NonNullable<
  WarehouseInventoryManagementPageResult['records']
>[number];

export const useLocationList = () => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<LocationPageResult['records']>([]);

  const getList = async (data: LocationPageRequest) => {
    setLoading(true);
    const result = await responseWithResultAsync({
      request: locationPage,
      params: { ...data, pageNum: 1, pageSize: 20 },
    });
    console.log(result);
    setList(result?.records ?? []);
    setLoading(false);
  };
  const handleSearch = (name?: string) => {
    getList({ name, pageNum: 1, pageSize: 20 });
  };
  useEffect(() => {
    getList({});
  }, []);

  return {
    list,
    loading,
    handleSearch,
  };
};
