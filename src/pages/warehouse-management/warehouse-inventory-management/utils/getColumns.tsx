import { ColumnProps } from 'antd/lib/table';
import React, { useMemo, useState } from 'react';
import { Modal } from 'antd';
import { WarehouseInventoryManagementPageListInfoType } from './hook';
import { toDecimal } from '@/utils/string';
import PopoverRowText from '@/components/PopoverRowText';

export const formatFee = (val?: string | number | null) => {
  if (val !== '' && val !== null && val !== undefined) {
    return toDecimal(Number(val), 2);
  }
  return '-';
};

// ai生成
const ImagePreview: React.FC<{ imageUrl?: string }> = ({ imageUrl }) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  const handlePreview = (url: string) => {
    setPreviewImage(url);
    setPreviewVisible(true);
  };

  if (!imageUrl) {
    return <span>-</span>;
  }

  return (
    <>
      <img
        src={imageUrl}
        alt="商品图片"
        style={{
          width: 50,
          height: 50,
          objectFit: 'cover',
          cursor: 'pointer',
          borderRadius: 4,
        }}
        onClick={() => handlePreview(imageUrl)}
      />
      <Modal
        visible={previewVisible}
        title="查看大图"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        <img src={previewImage} alt="商品图片" style={{ width: '100%', height: 'auto' }} />
      </Modal>
    </>
  );
};
// 2024年12月 开山ai结尾共生成41行代码

export const useTable = () => {
  const columns: ColumnProps<WarehouseInventoryManagementPageListInfoType>[] = [
    {
      dataIndex: 'sortColumn',
      title: '#',
      width: 40,
      render: (_, __, index) => index + 1,
    },
    {
      dataIndex: 'imageUrl',
      title: '图片',
      width: 80,
      render: (val) => <ImagePreview imageUrl={val} />,
    },
    {
      dataIndex: 'productName',
      title: '商品名称',
      width: 150,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      dataIndex: 'barcode',
      title: '条码',
      width: 150,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      dataIndex: 'warehouseName',
      title: '所属仓库',
      width: 150,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '库区',
      width: 100,
      dataIndex: 'areaName',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '库位',
      width: 150,
      dataIndex: 'locationName',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },

    {
      title: '层数',
      width: 150,
      dataIndex: 'floor',
      render: (val) => val ?? 0,
    },
    {
      title: '类目',
      width: 100,
      dataIndex: 'category',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '型号',
      width: 100,
      dataIndex: 'model',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '规格',
      width: 150,
      dataIndex: 'specification',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },

    {
      title: '单位',
      width: 150,
      dataIndex: 'unit',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '品牌',
      width: 180,
      dataIndex: 'brand',
      render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
    },
    {
      title: '所属商务',
      width: 150,
      dataIndex: 'businessUserName',
      render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
    },
    {
      title: '库存数量',
      width: 150,
      dataIndex: 'quantity',
      render: (val) => val ?? 0,
    },
    // ai生成
    {
      title: '有效期',
      width: 150,
      dataIndex: 'expireDate',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '快递单号',
      width: 150,
      dataIndex: 'expressNo',
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    // 2024年12月 开山ai结尾共生成8行代码
  ];
  return { columns };
};
