import { Table } from 'antd';
import React, { useMemo } from 'react';
import { SampleWarehouseReceiptDetailResult } from '../services';
import { ColumnProps } from 'antd/lib/table';
import PopoverRowText from '@/components/PopoverRowText';
import FileUpload from '@/components/FileUpload';
import moment from 'moment';

type PropsType = {
  info?: SampleWarehouseReceiptDetailResult;
};
export type ItemListInfoType = NonNullable<SampleWarehouseReceiptDetailResult['itemList']>[number];

// ai生成
const DetailedList: React.FC<PropsType> = ({ info }) => {
  const columns: ColumnProps<ItemListInfoType>[] = [
    {
      title: '#',
      dataIndex: 'index',
      width: 60,
      render: (_, __, index) => (index === info?.itemList?.length ? '合计' : index + 1),
    },
    {
      title: '图片',
      dataIndex: 'imageUrl',
      width: 100,
      render: (val) => (
        <FileUpload
          value={val ? [val] : []}
          maxLen={1}
          typeCode={'SPU_IMG'}
          maxSize={20 * 1024 * 1024}
          accept={'.jpg,.jpeg,.png,.pdf'}
          disabled={true}
        />
      ),
    },
    {
      title: '条码',
      dataIndex: 'barcode',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '规格',
      dataIndex: 'specification',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '型号',
      dataIndex: 'model',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '类目',
      dataIndex: 'category',
      width: 200,
      render: (val) => (val ? <PopoverRowText text={val} /> : '-'),
    },
    {
      title: '有效期',
      dataIndex: 'expireDate',
      width: 200,
      render: (val) => (val ? moment(val).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '入库数量',
      dataIndex: 'actualQuantity',
      width: 200,
      render: (val) => val ?? 0,
    },
    {
      title: '库区/库位',
      dataIndex: 'locationId',
      width: 200,
      render: (val, record) => (val ? record?.areaName + '/' + record?.locationName : '-'),
    },
  ];
  const showList = useMemo(() => {
    if (!info?.itemList?.length) return [];

    const totalQuantity = info.itemList.reduce((sum, item) => sum + (item.actualQuantity || 0), 0);

    return [
      ...info.itemList,
      {
        actualQuantity: totalQuantity,
      },
    ];
  }, [info?.itemList]);
  return <Table columns={columns} dataSource={showList} rowKey={'index'} pagination={false} />;
};
// 2024年12月31日 开山ai结尾共生成89行代码

export default DetailedList;
