import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import {
  sampleWarehouseReceiptPage,
  SampleWarehouseReceiptPageRequest,
  SampleWarehouseReceiptPageResult,
} from '../services';

import { useSetState } from 'ahooks';
import { responseWithResultAsync } from '@/pages/warehouse-management/utils/utils';
import { Moment } from 'moment';
import {
  locationPage,
  LocationPageRequest,
  LocationPageResult,
} from '../../location-manager/services';

export type SearchFormType = SampleWarehouseReceiptPageRequest & {
  createDate?: Moment[];
  updateDate?: Moment[];
};

export const useList = (form: WrappedFormUtils<SearchFormType>) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<SampleWarehouseReceiptPageResult['records']>([]);
  const [pagination, setPagination] = useSetState({ current: 1, size: 20, total: 0 });
  const [condition, setCondition] = useState<SampleWarehouseReceiptPageRequest>();
  const getList = async (data: SampleWarehouseReceiptPageRequest) => {
    setLoading(true);
    const formValue = form?.getFieldsValue();
    const createTimeStart = formValue?.createDate?.[0]?.format('YYYY-MM-DD 00:00:00');
    const createTimeEnd = formValue?.createDate?.[1]?.format('YYYY-MM-DD 23:59:59');
    const updateTimeStart = formValue?.updateDate?.[0]?.format('YYYY-MM-DD 00:00:00');
    const updateTimeEnd = formValue?.updateDate?.[1]?.format('YYYY-MM-DD 23:59:59');
    const params = {
      ...data,
      ...(formValue as SearchFormType),
      createTimeStart,
      createTimeEnd,
      updateTimeStart,
      updateTimeEnd,
      pageNum: data?.pageNum ?? pagination.current,
      pageSize: data?.pageSize ?? pagination.size,
    };
    delete params.createDate;
    delete params.updateDate;
    setCondition(params);
    const result = await responseWithResultAsync({
      request: sampleWarehouseReceiptPage,
      params,
    });
    setLoading(false);
    setList(result?.records ?? []);
    setPagination({
      current: result?.current ?? 1,
      size: result?.size ?? pagination.size,
      total: result?.total ?? 0,
    });
  };

  useEffect(() => {
    getList({});
  }, []);
  return {
    list,
    loading,
    getList,
    pagination,
    condition,
    setLoading,
  };
};
export type SampleWarehouseReceiptPageListInfoType = NonNullable<
  SampleWarehouseReceiptPageResult['records']
>[number];

// ai生成
export const useLocationList = (form?: WrappedFormUtils<any>) => {
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<LocationPageResult['records']>([]);
  const [warehouseId, setWarehouseId] = useState<any>(null);

  // 监听仓库变化，避免无限循环
  useEffect(() => {
    const currentWarehouseId = form?.getFieldValue('warehouseInfo')?.key;
    if (currentWarehouseId !== warehouseId) {
      setWarehouseId(currentWarehouseId);
    }
  }, [form?.getFieldValue('warehouseInfo')?.key]);

  const getList = useCallback(
    async (data: LocationPageRequest) => {
      if (!warehouseId) {
        setList([]);
        return;
      }
      setLoading(true);
      const result = await responseWithResultAsync({
        request: locationPage,
        params: {
          ...data,
          pageNum: 1,
          pageSize: 1000,
          warehouseId: warehouseId,
        },
      });
      console.log(result);
      setList(result?.records ?? []);
      setLoading(false);
    },
    [warehouseId],
  );

  const handleSearch = (name?: string) => {
    getList({ name, pageNum: 1, pageSize: 1000 });
  };

  useEffect(() => {
    getList({});
  }, [warehouseId]);

  return {
    list,
    loading,
    handleSearch,
  };
};
// 2024年12月28日 开山ai结尾共生成39行代码
