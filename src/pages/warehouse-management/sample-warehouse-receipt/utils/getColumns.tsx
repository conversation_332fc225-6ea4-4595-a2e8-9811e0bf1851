import { ColumnProps } from 'antd/lib/table';
import React, { useMemo } from 'react';
import moment from 'moment';
import { SampleWarehouseReceiptPageListInfoType } from './hook';
import { toDecimal } from '@/utils/string';
import { Tag } from 'antd';
import PopoverRowText from '@/components/PopoverRowText';
import Space from '@/components/Space';
import { STATUS_COLOR_ENUM, STATUS_ENUM } from './enum';

export const formatFee = (val?: string | number | null) => {
  if (val !== '' && val !== null && val !== undefined) {
    return toDecimal(Number(val), 2);
  }
  return '-';
};

export const useTable = (params: {
  goEdit?: (params: { id?: number }) => void;
  goDetail?: (params: { id?: number }) => void;
}) => {
  const { goEdit, goDetail } = params;
  const columns = useMemo<ColumnProps<SampleWarehouseReceiptPageListInfoType>[]>(
    () => [
      {
        dataIndex: 'index',
        title: '#',
        width: 40,
        render: (_, __, index) => index + 1,
      },
      {
        dataIndex: 'orderNo',
        title: '样品入库单号',
        width: 150,
        render: (val: string, record) =>
          val ? (
            <PopoverRowText text={val}>
              <a
                onClick={() => {
                  goDetail &&
                    goDetail({
                      id: record?.id,
                    });
                }}
              >
                {val}
              </a>
            </PopoverRowText>
          ) : (
            '-'
          ),
      },
      // {
      //   dataIndex: 'sourceOrderNo',
      //   title: '来源单号',
      //   width: 150,
      //   render: (val: string) =>
      //     val ? (
      //       <PopoverRowText text={val}>
      //         <a>{val}</a>
      //       </PopoverRowText>
      //     ) : (
      //       '-'
      //     ),
      // },
      {
        title: '状态',
        width: 100,
        dataIndex: 'status',
        render: (val: SampleWarehouseReceiptPageListInfoType['status']) =>
          val ? <Tag color={STATUS_COLOR_ENUM[val]}>{STATUS_ENUM[val]}</Tag> : '-',
      },
      {
        title: '入库类型',
        width: 150,
        dataIndex: 'typeName',
        render: (val) => val ?? '-',
      },

      // {
      //   title: '计划入库数量',
      //   width: 150,
      //   dataIndex: 'totalPlanQuantity',
      //   render: (val) => val ?? 0,
      // },
      {
        title: '入库数量',
        width: 150,
        dataIndex: 'totalActualQuantity',
        render: (val) => val ?? '-',
      },
      {
        title: '所属商务',
        width: 150,
        dataIndex: 'businessUserName',
        render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
      },
      {
        title: '快递单号',
        width: 100,
        dataIndex: 'expressNo',
        render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
      },
      {
        title: '收件人',
        width: 100,
        dataIndex: 'receiver',
        render: (val) => val ?? '-',
      },
      {
        title: '联系方式',
        width: 150,
        dataIndex: 'contactInfo',
        render: (val) => val ?? '-',
      },

      {
        title: '发件人',
        width: 150,
        dataIndex: 'sender',
        render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
      },
      {
        title: '发货地址',
        width: 180,
        dataIndex: 'senderAddress',
        render: (val) => (val ? <PopoverRowText text={val}>{val}</PopoverRowText> : '-'),
      },

      {
        title: '创建人',
        width: 150,
        dataIndex: 'createBy',
        render: (val) => val ?? '-',
      },
      {
        title: '创建时间',
        width: 150,
        dataIndex: 'createTime',
        render: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '修改人',
        width: 150,
        dataIndex: 'updateBy',
        render: (val) => val ?? '-',
      },
      {
        title: '修改时间',
        width: 150,
        dataIndex: 'updateTime',
        render: (val) => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '操作',
        width: 150,
        dataIndex: 'action',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              <a
                onClick={() => {
                  goDetail &&
                    goDetail({
                      id: record?.id,
                    });
                }}
              >
                详情
              </a>
              {record.status == 1 && (
                <a
                  onClick={() => {
                    goEdit &&
                      goEdit({
                        id: record?.id,
                      });
                  }}
                >
                  编辑
                </a>
              )}
            </Space>
          );
        },
      },
    ],
    [],
  );
  return { columns };
};
