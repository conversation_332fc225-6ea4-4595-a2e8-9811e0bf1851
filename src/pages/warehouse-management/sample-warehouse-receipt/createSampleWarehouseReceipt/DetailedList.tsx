// ai生成
import { Button, Input, InputNumber, Select, Table, message, DatePicker } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { SampleWarehouseReceiptDetailResult } from '../services';
import styles from './index.module.less';
import Space from '@/components/Space';
import { ColumnProps, TableRowSelection } from 'antd/lib/table';
import { useLocationList } from '../utils/hook';
import FileUpload from '@/components/FileUpload';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { queryProductByBarcode } from '../components/SpuListModal/services';
import { responseWithResultAsync } from '../../utils';
import moment from 'moment';
const { Option } = Select;

type PropsType = {
  detail?: SampleWarehouseReceiptDetailResult;
  setItemList: React.Dispatch<React.SetStateAction<ItemListInfoType[]>>;
  itemList: ItemListInfoType[];
  handleOpenModal: () => void;
  form: WrappedFormUtils<any>;
  warehouseId?: string | number;
};
export type ItemListInfoType = NonNullable<
  SampleWarehouseReceiptDetailResult['itemList']
>[number] & {
  expireDate?: any;
};
const DetailedList: React.FC<PropsType> = ({
  detail,
  setItemList,
  itemList,
  handleOpenModal,
  form,
  warehouseId,
}) => {
  const [selectedKeys, setSelectKeys] = useState<number[]>([]);

  const { list: locationList, handleSearch } = useLocationList(form);
  const rowSelection: TableRowSelection<ItemListInfoType> = {
    onChange: (selectedRowKeys) => {
      setSelectKeys(selectedRowKeys as number[]);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
  };

  const changeDataSource = (key: keyof ItemListInfoType, index: number, value: any) => {
    const newDataSource = [...itemList];
    newDataSource[index] = { ...newDataSource[index], [key]: value };
    setItemList(newDataSource);
  };

  const handleBarcodeBlur = async (index: number, barcode: string) => {
    if (!barcode || !barcode.trim()) {
      return;
    }
    const reuslt = await responseWithResultAsync({
      request: queryProductByBarcode,
      params: { barcode: barcode.trim() },
    });
    if (reuslt) {
      const productData = reuslt;
      const newDataSource = [...itemList];
      newDataSource[index] = {
        ...newDataSource[index],
        productCode: productData.productCode || newDataSource[index].productCode,
        productName: productData.productName || newDataSource[index].productName,
        brand: productData.brand || newDataSource[index].brand,
        unit: productData.unit || newDataSource[index].unit,
        specification: productData.specification || newDataSource[index].specification,
        model: productData.model || newDataSource[index].model,
        category: productData.category || newDataSource[index].category,
        imageUrl: productData.imageUrl || newDataSource[index].imageUrl,
      };
      console.log('newDataSource', newDataSource);
      setItemList(newDataSource);
    }
  };

  const columns = useMemo<ColumnProps<ItemListInfoType>[]>(
    () => [
      {
        title: '#',
        dataIndex: 'index',
        width: 50,
        render: (_, __, index) => index + 1,
      },
      {
        title: '图片',
        dataIndex: 'imageUrl',
        width: 100,
        render: (val, _, index) => (
          <FileUpload
            value={val ? [val] : []}
            onChange={(val) => {
              changeDataSource('imageUrl', index, val?.[0]);
            }}
            maxLen={1}
            typeCode={'SPU_IMG'}
            maxSize={20 * 1024 * 1024}
            accept={'.jpg,.jpeg,.png,.pdf'}
          />
        ),
      },
      {
        title: '条码*',
        dataIndex: 'barcode',
        width: 200,
        render: (val, _, index) => (
          <Input
            value={val}
            placeholder="请输入条码(必填,限50字符)"
            onChange={(e) => {
              if (e.target.value.length <= 50) {
                changeDataSource('barcode', index, e.target.value);
              }
            }}
            onBlur={(e) => {
              handleBarcodeBlur(index, e.target.value);
            }}
            maxLength={50}
          />
        ),
      },
      {
        title: '商品名称*',
        dataIndex: 'productName',
        width: 200,
        render: (val, _, index) => (
          <Input
            value={val}
            placeholder="请输入商品名称(必填,限100字符)"
            onChange={(e) => {
              if (e.target.value.length <= 100) {
                changeDataSource('productName', index, e.target.value);
              }
            }}
            maxLength={100}
          />
        ),
      },
      {
        title: '品牌*',
        dataIndex: 'brand',
        width: 200,
        render: (val, _, index) => (
          <Input
            value={val}
            placeholder="请输入品牌(必填,限50字符)"
            onChange={(e) => {
              if (e.target.value.length <= 50) {
                changeDataSource('brand', index, e.target.value);
              }
            }}
            maxLength={50}
          />
        ),
      },
      {
        title: '单位*',
        dataIndex: 'unit',
        width: 200,
        render: (val, _, index) => (
          <Input
            value={val}
            placeholder="请输入单位(必填,限50字符)"
            maxLength={50}
            onChange={(e) => {
              if (e.target.value.length <= 50) {
                changeDataSource('unit', index, e.target.value);
              }
            }}
          />
        ),
      },
      {
        title: '规格',
        dataIndex: 'specification',
        width: 200,
        render: (val, _, index) => (
          <Input
            value={val}
            placeholder="请输入规格(限100字符)"
            onChange={(e) => {
              if (e.target.value.length <= 100) {
                changeDataSource('specification', index, e.target.value);
              }
            }}
            maxLength={100}
          />
        ),
      },
      {
        title: '型号',
        dataIndex: 'model',
        width: 200,
        render: (val, _, index) => (
          <Input
            value={val}
            placeholder="请输入型号(限100字符)"
            maxLength={100}
            onChange={(e) => {
              if (e.target.value.length <= 100) {
                changeDataSource('model', index, e.target.value);
              }
            }}
          />
        ),
      },
      {
        title: '类目*',
        dataIndex: 'category',
        width: 200,
        render: (val, _, index) => (
          <Input
            value={val}
            placeholder="请输入类目(必填,限100字符)"
            maxLength={100}
            onChange={(e) => {
              if (e.target.value.length <= 100) {
                changeDataSource('category', index, e.target.value);
              }
            }}
          />
        ),
      },
      {
        title: '有效期',
        dataIndex: 'expireDate',
        width: 200,
        render: (val, _, index) => (
          <DatePicker
            style={{ width: '100%' }}
            value={val ? moment(val) : undefined}
            placeholder="请选择有效期"
            onChange={(date) => {
              changeDataSource('expireDate', index, date);
            }}
            format="YYYY-MM-DD"
          />
        ),
      },
      {
        title: '入库数量*',
        dataIndex: 'actualQuantity',
        width: 200,
        render: (val, _, index) => (
          <InputNumber
            style={{ width: '100%' }}
            value={val}
            placeholder="请输入实际入库数量"
            onChange={(e) => {
              if (e && e > 0 && e < 100000000) {
                changeDataSource('actualQuantity', index, e);
              }
            }}
            min={1}
            max={99999999}
            precision={0}
            maxLength={8}
          />
        ),
      },
      {
        title: '库区/库位*',
        dataIndex: 'locationId',
        width: 200,
        render: (val, _, index) => (
          <Select
            style={{ width: '100%' }}
            value={
              val
                ? {
                    label: itemList[index]?.areaName + '/' + itemList[index]?.locationName,
                    key: val,
                  }
                : undefined
            }
            placeholder="请选择库区/库位(必填)"
            optionFilterProp="children"
            filterOption={true}
            onChange={(e) => {
              setItemList(
                itemList.map((item, i) => {
                  if (i === index) {
                    item.locationId = e.key;
                    item.areaId = locationList?.find((item) => item.id === e.key)?.areaId;
                  }
                  return item;
                }),
              );
            }}
            labelInValue
            showSearch
            allowClear
          >
            {locationList
              ?.filter((item) => item?.status === 1)
              ?.map((item) => (
                <Option key={item.id} value={item.id}>
                  {item?.areaName + '/' + item?.name}
                </Option>
              ))}
          </Select>
        ),
      },
    ],
    [itemList, locationList],
  );
  useEffect(() => {
    setItemList([]);
  }, [warehouseId]);
  useEffect(() => {
    if (detail) {
      setItemList(detail?.itemList ?? []);
    }
  }, [detail]);
  const handleAddNewList = () => {
    if (!warehouseId) {
      message.warning('请先选择仓库');
      return;
    }
    setItemList([...itemList, { actualQuantity: 1 }]);
  };
  const handleDeleteList = (index: number[]) => {
    setItemList(itemList.filter((_, i) => !index.includes(i)));
    setSelectKeys([]);
  };

  return (
    <section style={{ padding: '0 24px' }} className={styles['list-table']}>
      <Space style={{ marginBottom: 16 }}>
        <Button type="danger" onClick={() => handleDeleteList(selectedKeys)} ghost>
          删除
        </Button>
      </Space>
      <Table
        columns={columns}
        dataSource={itemList}
        rowKey={'index'}
        pagination={false}
        rowSelection={rowSelection}
      />
      <div style={{ display: 'flex', justifyContent: 'center', padding: '10px' }}>
        <a onClick={handleAddNewList}>+新增一行</a>
      </div>
    </section>
  );
};

export default DetailedList;
// 2024年12月31日 开山ai结尾共生成358行代码
