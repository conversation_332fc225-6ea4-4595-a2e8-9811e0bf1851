import { Input, Select } from 'antd';
import Form, { WrappedFormUtils } from 'antd/lib/form/Form';
import React, { useEffect } from 'react';
import styles from './index.module.less';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { useWarehouseList } from '../../area-manager/utils/hook';
import { useEmployeeList } from '@/hooks/useEmployeeList';
import { SampleWarehouseReceiptAddRequest } from '../services';
import { ItemListInfoType } from './DetailedList';
const { Item } = Form;
const { Option } = Select;
type PropsType = {
  form: WrappedFormUtils<SampleWarehouseReceiptAddRequest>;
  type?: 'create' | 'edit';
  setItemList: React.Dispatch<React.SetStateAction<ItemListInfoType[]>>;
};
const formItemLayout = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 12,
  },
};
const formItemStyle: React.CSSProperties = {
  width: '200px',
};
const formStyle: React.CSSProperties = {
  width: '25%',
};

const BasicForm: React.FC<PropsType> = ({ form, type, setItemList }) => {
  const { getFieldDecorator } = form;

  const { codeList } = useCode(CODE_ENUM.INBOUND_TYPE, { able: true });
  const { list: warehouseList, loading: warehouseLoading, hanelSearch } = useWarehouseList();
  const {
    list: businessList,
    getList: getBusinessList,
    loading: businessLoading,
    handleSearch: businessSearch,
  } = useEmployeeList({ bizRoleType: 'BUSINESS' });
  // ai生成
  useEffect(() => {
    if (type === 'create' && warehouseList?.length) {
      const currentWarehouseInfo = form.getFieldValue('warehouseInfo');
      if (!currentWarehouseInfo?.key) {
        const defaultWarehouse = warehouseList.find((item) => item.name === '云狐库房');
        if (defaultWarehouse) {
          form.setFieldsValue({
            warehouseInfo: {
              label: defaultWarehouse.name,
              key: defaultWarehouse.id,
            },
          });
          setTimeout(() => {
            setItemList([{ actualQuantity: 1 }]);
          }, 100);
        }
      }
    }
  }, [type, warehouseList]);
  // 2024年12月28日 开山ai结尾共生成19行代码
  return (
    <>
      <Form {...formItemLayout} className={styles['form-box']}>
        <Item label="入库类型" style={formStyle}>
          {getFieldDecorator('typeInfo', {
            rules: [
              {
                required: true,
                message: '请选择入库类型',
              },
            ],
          })(
            <Select placeholder="请选择" allowClear style={formItemStyle} labelInValue>
              {codeList?.map((item) => (
                <Option value={Number(item.value)} key={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="仓库名称" style={formStyle}>
          {getFieldDecorator('warehouseInfo', {
            rules: [
              {
                required: true,
                message: '请选择仓库',
              },
            ],
            // ai生成
            normalize: (value) => {
              // 确保值格式正确，避免Select组件警告
              if (
                value &&
                typeof value === 'object' &&
                (value.key !== undefined || value.label !== undefined)
              ) {
                return value;
              }
              return undefined;
            },
            // 2024年12月28日 开山ai结尾共生成6行代码
          })(
            <Select
              placeholder="请选择"
              style={formItemStyle}
              showSearch
              onSearch={hanelSearch}
              filterOption={false}
              loading={warehouseLoading}
              onBlur={() => {
                hanelSearch('');
              }}
              labelInValue
            >
              {warehouseList
                ?.filter((item) => item.status === 1)
                ?.map((item) => (
                  <Option value={item.id} key={item.id}>
                    {item.name}
                  </Option>
                ))}
            </Select>,
          )}
        </Item>
        <Item label="快递单号" style={formStyle}>
          {getFieldDecorator('expressNo')(
            <Input style={formItemStyle} placeholder="请输入快递单号" maxLength={50} />,
          )}
        </Item>
        <Item label="收件人" style={formStyle}>
          {getFieldDecorator('receiver')(
            <Input style={formItemStyle} placeholder="请输入收件人" maxLength={20} />,
          )}
        </Item>
        <Item label="联系方式" style={formStyle}>
          {getFieldDecorator('contactInfo')(
            <Input style={formItemStyle} placeholder="请输入联系方式" maxLength={20} />,
          )}
        </Item>
        <Item label="发货人" style={formStyle}>
          {getFieldDecorator('sender')(
            <Input style={formItemStyle} placeholder="请输入发货人" maxLength={20} />,
          )}
        </Item>
        <Item label="发货地址" style={formStyle}>
          {getFieldDecorator('senderAddress')(
            <Input style={formItemStyle} placeholder="请输入发货地址" maxLength={100} />,
          )}
        </Item>

        <Item label="所属商务" style={formStyle}>
          {getFieldDecorator('businessUserInfo', {
            rules: [
              {
                required: true,
                message: '请选择所属商务',
              },
            ],
            // ai生成
            normalize: (value) => {
              // 确保值格式正确，避免Select组件警告
              if (
                value &&
                typeof value === 'object' &&
                (value.key !== undefined || value.label !== undefined)
              ) {
                return value;
              }
              return undefined;
            },
            // 2024年12月28日 开山ai结尾共生成6行代码
          })(
            <Select
              style={formItemStyle}
              loading={businessLoading}
              placeholder="请选择"
              showSearch
              onSearch={businessSearch}
              filterOption={false}
              onBlur={() => {
                businessSearch('');
              }}
              labelInValue
            >
              {businessList?.map((item) => (
                <Option key={item?.employeeId} value={item?.employeeId}>
                  {item?.employeeName}
                </Option>
              ))}
            </Select>,
          )}
        </Item>
        <Item label="备注" style={formStyle}>
          {getFieldDecorator('remark')(
            <Input style={formItemStyle} placeholder="请输入备注" maxLength={100} />,
          )}
        </Item>
      </Form>
    </>
  );
};

export default BasicForm;
