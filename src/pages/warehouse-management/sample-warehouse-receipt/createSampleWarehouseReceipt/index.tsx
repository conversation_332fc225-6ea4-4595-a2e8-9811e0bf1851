import { useSetState } from 'ahooks';
import { Button, Icon, message, Modal, Spin } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import BasicForm from './BasicForm';
import DetailedList, { ItemListInfoType } from './DetailedList';
import styles from './index.module.less';
import { history } from 'qmkit';
import PageLayout from '@/components/PageLayout/index';
import { getQueryParams } from '../utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { Card, FormContentLayout, FormBottomCard } from '@/components/DetailFormCompoments';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import {
  sampleWarehouseReceiptAdd,
  SampleWarehouseReceiptAddRequest,
  sampleWarehouseReceiptDetail,
  SampleWarehouseReceiptDetailResult,
  sampleWarehouseReceiptUpdate,
  sampleWarehouseReceiptConfirmStorage,
  sampleWarehouseReceiptAddAndConfirmStorage,
} from '../services';
import { responseWithResultAsync } from '../../utils';
import SpuListModal from '../components/SpuListModal';
import { useLocation } from 'react-router-dom';

interface PropsType
  extends FormComponentProps<
    SampleWarehouseReceiptAddRequest & {
      businessUserInfo?: { label: string; key: string };
      warehouseInfo?: { label: string; key: string };
    }
  > {
  form: WrappedFormUtils<
    SampleWarehouseReceiptAddRequest & { businessUserInfo?: { label: string; key: string } }
  >;
}
type ModalState = {
  loading?: boolean;
  detail?: SampleWarehouseReceiptDetailResult;
  visible: boolean;
};

const AddMarketingData: React.FC<PropsType> = ({ form }) => {
  const [modalState, setState] = useSetState<ModalState>({ loading: false, visible: false });
  const [itemList, setItemList] = useState<ItemListInfoType[]>([]);
  const id = useMemo(() => getQueryParams().id as number, []);
  const type = useMemo(() => getQueryParams().type as 'edit' | 'create', []);
  const { delRoutetag } = useCloseAndJump();
  const location = useLocation();
  const handleCancel = () => {
    delRoutetag();
    history.goBack();
  };
  const handleDetail = async () => {
    setState((state) => ({ ...state, loading: true }));
    const detail = await responseWithResultAsync({
      request: sampleWarehouseReceiptDetail,
      params: {
        id,
      },
    });
    setState((state) => ({ ...state, loading: false }));
    if (detail) {
      initForm(detail);
      setState((state) => ({ ...state, detail }));
    }
  };
  const formatParams = (
    values: SampleWarehouseReceiptAddRequest & {
      businessUserInfo?: { label: string; key: string };
      warehouseInfo?: { label: string; key: string };
      typeInfo?: { label: string; key: string };
    },
  ) => {
    console.log(values);
    const params = {
      ...values,
      type: values.typeInfo?.key,
      businessUserId: values.businessUserInfo?.key,
      businessUserName: values.businessUserInfo?.label,
      warehouseId: values.warehouseInfo?.key,
    };
    params.businessUserInfo = undefined;
    params.warehouseInfo = undefined;
    return params as SampleWarehouseReceiptAddRequest;
  };
  // ai生成
  const formatItemListForSubmit = (list: ItemListInfoType[]) => {
    return list.map((item) => ({
      ...item,
      expireDate: item.expireDate ? item.expireDate.format('YYYY-MM-DD') : undefined,
    }));
  };
  // 2024年12月31日 开山ai结尾共生成5行代码

  const handleCreate = useCallback(
    async (params: SampleWarehouseReceiptAddRequest) => {
      const formattedItemList = formatItemListForSubmit(itemList);
      const result = await responseWithResultAsync({
        request: sampleWarehouseReceiptAdd,
        params: { ...params, itemList: formattedItemList },
      });
      setState((state) => ({ ...state, loading: false }));
      return result;
    },
    [itemList],
  );
  const handleUpdate = useCallback(
    async (params: SampleWarehouseReceiptAddRequest) => {
      const formattedItemList = formatItemListForSubmit(itemList);
      const result = await responseWithResultAsync({
        request: sampleWarehouseReceiptUpdate,
        params: { ...params, id, itemList: formattedItemList },
      });
      setState((state) => ({ ...state, loading: false }));
      return result;
    },
    [id, itemList],
  );

  // ai生成
  const validateSubmit = () => {
    let isValid = true;
    let errorMessage = '';

    if (itemList.length === 0) {
      message.error('请至少添加一条商品明细');
      return false;
    }

    for (let i = 0; i < itemList.length; i++) {
      const item = itemList[i];
      // 条码必填
      if (!item.barcode || item.barcode.length > 50) {
        errorMessage = `第${i + 1}行条码为必填且长度不能超过50字符`;
        isValid = false;
        break;
      }
      // 商品名称必填
      if (!item.productName || item.productName.length > 100) {
        errorMessage = `第${i + 1}行商品名称为必填且长度不能超过100字符`;
        isValid = false;
        break;
      }
      // 品牌必填
      if (!item.brand || item.brand.length > 50) {
        errorMessage = `第${i + 1}行品牌为必填且长度不能超过50字符`;
        isValid = false;
        break;
      }
      // 单位必填
      if (!item.unit || item.unit.length > 50) {
        errorMessage = `第${i + 1}行单位为必填且长度不能超过50字符`;
        isValid = false;
        break;
      }
      // 类目必填
      if (!item.category || item.category.length > 100) {
        errorMessage = `第${i + 1}行类目为必填且长度不能超过100字符`;
        isValid = false;
        break;
      }
      // 规格非必填，但有长度限制
      if (item.specification && item.specification.length > 100) {
        errorMessage = `第${i + 1}行规格长度不能超过100字符`;
        isValid = false;
        break;
      }
      // 型号非必填，但有长度限制
      if (item.model && item.model.length > 100) {
        errorMessage = `第${i + 1}行型号长度不能超过100字符`;
        isValid = false;
        break;
      }
      // 入库数量必填
      if (!item.actualQuantity || item.actualQuantity <= 0 || item.actualQuantity >= 100000000) {
        errorMessage = `第${i + 1}行入库数量为必填且必须为小于100000000的正整数`;
        isValid = false;
        break;
      }
      // 库区/库位必填
      if (!item.locationId) {
        errorMessage = `第${i + 1}行库区/库位为必填`;
        isValid = false;
        break;
      }
    }

    if (!isValid) {
      message.error(errorMessage);
    }
    return isValid;
  };
  // 2024年12月28日 开山ai结尾共生成48行代码

  const handleOk = () => {
    form.validateFields(async (err, values) => {
      if (err) return;

      if (!validateSubmit()) {
        return;
      }

      setState((state) => ({ ...state, loading: true }));
      const params = formatParams(values);

      const result =
        location.pathname === '/sample-warehouse-receipt-create'
          ? await handleCreate(params)
          : await handleUpdate(params);
      setState((state) => ({ ...state, loading: false }));
      if (result) {
        message.success('提交成功');
        handleCancel();
      }
    });
  };

  // ai生成
  const handleSaveAndInbound = () => {
    form.validateFields(async (err, values) => {
      if (err) return;

      if (!validateSubmit()) {
        return;
      }

      setState((state) => ({ ...state, loading: true }));
      const params = formatParams(values);
      const formattedItemList = formatItemListForSubmit(itemList);
      console.log(params);

      // 使用新的保存并入库接口
      const result = await responseWithResultAsync({
        request: sampleWarehouseReceiptAddAndConfirmStorage,
        params: {
          ...params,
          businessUserId: params.businessUserId?.toString(),
          itemList: formattedItemList,
        },
      });

      setState((state) => ({ ...state, loading: false }));

      if (result) {
        message.success('保存并入库成功');
        handleCancel();
      }
    });
  };
  // 2024年12月31日 开山ai结尾共生成24行代码
  const createLabelKey = (obj: { label?: string | number; key?: string | number }) => {
    return obj.label || obj.key ? obj : undefined;
  };
  // ai生成
  const initForm = async (info: SampleWarehouseReceiptDetailResult) => {
    // 安全地构建表单值，确保Select组件的labelInValue格式正确
    const formValues: any = { ...info };

    // 处理商务人员信息
    if (info.businessUserName && info.businessUserId) {
      formValues.businessUserInfo = {
        label: info.businessUserName,
        key: info.businessUserId,
      };
    } else {
      formValues.businessUserInfo = undefined;
    }

    // 处理仓库信息
    if (info.warehouseName && info.warehouseId) {
      formValues.warehouseInfo = {
        label: info.warehouseName,
        key: info.warehouseId,
      };
    } else {
      formValues.warehouseInfo = undefined;
    }
    if (info.type && info.typeName) {
      formValues.typeInfo = {
        label: info.typeName,
        key: info.type,
      };
    }

    form.setFieldsValue(formValues);
  };
  // 2024年12月28日 开山ai结尾共生成25行代码

  useEffect(() => {
    if (type === 'edit' && id) {
      handleDetail();
    }
  }, [type]);

  const handleSelect = (rows: any[]) => {
    setItemList([...itemList, ...rows]);
  };
  const handleOpenModal = () => {
    setState((state) => ({ ...state, visible: true }));
  };
  // ai生成
  const [warehouseId, setWarehouseId] = useState<any>(null);

  // 监听仓库变化
  useEffect(() => {
    const currentWarehouseInfo = form.getFieldValue('warehouseInfo');
    const currentWarehouseId = currentWarehouseInfo?.key;
    if (currentWarehouseId !== warehouseId) {
      setWarehouseId(currentWarehouseId);
    }
  }, [form.getFieldValue('warehouseInfo')?.key]);
  // 2024年12月28日 开山ai结尾共生成9行代码
  return (
    <PageLayout
      routePath={
        type === 'create' ? '/sample-warehouse-receipt-create' : '/sample-warehouse-receipt-edit'
      }
    >
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="基本信息">
            <div className={styles.extra}>
              <BasicForm form={form} type={type} setItemList={setItemList} />
            </div>
          </Card>
          <Card title="明细信息">
            <div className={styles.extra}>
              <div className={styles.extra}>
                <DetailedList
                  form={form}
                  detail={modalState?.detail}
                  setItemList={setItemList}
                  itemList={itemList}
                  handleOpenModal={handleOpenModal}
                  warehouseId={warehouseId}
                />
              </div>
            </div>
          </Card>
        </Spin>
        <FormBottomCard>
          <Button
            type="primary"
            onClick={() => {
              handleSaveAndInbound();
            }}
          >
            保存并入库
          </Button>

          <Button
            type={type === 'create' ? 'primary' : 'default'}
            onClick={() => {
              handleOk();
            }}
          >
            保存
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
      <SpuListModal
        visible={modalState.visible}
        onOpen={handleOpenModal}
        onCancel={() => setState((state) => ({ ...state, visible: false }))}
        onSelect={handleSelect}
      />
    </PageLayout>
  );
};

export default Form.create()(AddMarketingData);
