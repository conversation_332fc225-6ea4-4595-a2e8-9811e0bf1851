import { Const, Fetch } from 'qmkit';
import { exportFile } from '../../utils/utils';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: number;
  data: T;
}

export type SampleWarehouseReceiptPageRequest = {
  operatorId?: string /* 操作员ID*/;
  operatorName?: string /* 操作员名称*/;
  pageNum?: number /* 页码*/;
  pageSize?: number /* 每页记录数*/;
  orderNo?: string /* 样品入库单号*/;
  type?: number /* 入库类型*/;
  status?: number /* 状态*/;
  warehouseId?: number /* 仓库ID*/;
  createBy?: string /* 创建人*/;
  createTimeStart?: string /* 创建时间开始*/;
  createTimeEnd?: string /* 创建时间结束*/;
  updateBy?: string /* 更新人*/;
  updateTimeStart?: string /* 更新时间开始*/;
  updateTimeEnd?: string /* 更新时间结束*/;
  productName?: string /* 商品名称*/;
  brand?: string /* 品牌*/;
  barcode?: string /* 条码*/;
  sourceOrderNo?: string /* 源订单号*/;
};

export type SampleWarehouseReceiptPageResult = {
  size?: number;
  current?: number;
  total?: number;
  pages?: number;
  records?: {
    id?: number;
    orderNo?: string;
    type?: number;
    typeName?: string;
    warehouseId?: number;
    warehouseCode?: string;
    warehouseName?: string;
    expressNo?: string;
    receiver?: string;
    contactInfo?: string;
    sender?: string;
    senderAddress?: string;
    businessUserId?: string;
    businessUserName?: string;
    status?: number;
    statusName?: string;
    sourceOrderNo?: string;
    remark?: string;
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
    itemList?: {
      id?: number;
      orderId?: number;
      orderNo?: string;
      imageUrl?: string;
      productName?: string;
      barcode?: string;
      productCode?: string;
      brand?: string;
      specification?: string;
      quantity?: number;
      areaId?: number;
      areaCode?: string;
      areaName?: string;
      locationId?: number;
      locationCode?: string;
      locationName?: string;
      status?: number;
      statusName?: string;
      sourceOrderNo?: string;
      createTime?: string;
      updateTime?: string;
      createBy?: string;
      updateBy?: string;
    }[];
    pages?: number;
  }[];
};

/**
 * 库区管理分页查询
 */
export const sampleWarehouseReceiptPage = (params: SampleWarehouseReceiptPageRequest) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptPageResult>>(
    '/warehouse/warehouse/sample-storage-orders/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};

export type SampleWarehouseReceiptBatchConfirmStorageRequest = {
  ids?: number[];
};

export type SampleWarehouseReceiptBatchConfirmStorageResult = boolean;

export const sampleWarehouseReceiptBatchConfirmStorage = (
  params: SampleWarehouseReceiptBatchConfirmStorageRequest,
) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptBatchConfirmStorageResult>>(
    '/warehouse/warehouse/sample-storage-orders/batch-confirm-storage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};

export type SampleWarehouseReceiptAddRequest = {
  id?: number;
  type?: number;
  warehouseId?: number;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: number;
  businessUserName?: string;
  sourceOrderNo?: string;
  remark?: string;
  itemList?: {
    id?: number;
    orderId?: number;
    orderNo?: string;
    imageUrl?: string;
    productName?: string;
    barcode?: string;
    productCode?: string;
    brand?: string;
    specification?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    quantity?: number;
    expireDate?: string; // 有效期
    areaId?: number;
    areaCode?: string;
    areaName?: string;
    locationId?: number;
    locationCode?: string;
    locationName?: string;
    status?: number;
    statusName?: string;
    sourceOrderNo?: string;
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
  }[];
};
export type SampleWarehouseReceiptAddResult = boolean;
export const sampleWarehouseReceiptAdd = (params: SampleWarehouseReceiptAddRequest) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptAddResult>>(
    '/warehouse/warehouse/sample-storage-orders/add',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};

export type SampleWarehouseReceiptUpdateRequest = {
  id?: number;
  type?: number;
  warehouseId?: number;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: number;
  businessUserName?: string;
  sourceOrderNo?: string;
  remark?: string;
  itemList?: {
    id?: number;
    orderId?: number;
    orderNo?: string;
    imageUrl?: string;
    productName?: string;
    barcode?: string;
    productCode?: string;
    brand?: string;
    specification?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    quantity?: number;
    expireDate?: string; // 有效期
    areaId?: number;
    areaCode?: string;
    areaName?: string;
    locationId?: number;
    locationCode?: string;
    locationName?: string;
    status?: number;
    statusName?: string;
    sourceOrderNo?: string;
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
  }[];
};
export type SampleWarehouseReceiptUpdateResult = boolean;
export const sampleWarehouseReceiptUpdate = (params: SampleWarehouseReceiptUpdateRequest) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptUpdateResult>>(
    '/warehouse/warehouse/sample-storage-orders/update',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};

export type SampleWarehouseReceiptDetailRequest = {
  operatorId?: string;
  operatorName?: string;
  id?: number;
};

export type SampleWarehouseReceiptDetailResult = {
  id?: number;
  orderNo?: string;
  type?: number;
  typeName?: string;
  warehouseId?: number;
  warehouseCode?: string;
  warehouseName?: string;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: number;
  businessUserName?: string;
  status?: number;
  statusName?: string;
  sourceOrderNo?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  itemList?: {
    id?: number;
    orderId?: number;
    orderNo?: string;
    imageUrl?: string;
    productName?: string;
    barcode?: string;
    productCode?: string;
    brand?: string;
    specification?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    quantity?: number;
    expireDate?: string; // 有效期
    areaId?: number;
    areaCode?: string;
    areaName?: string;
    locationId?: number;
    locationCode?: string;
    locationName?: string;
    status?: number;
    statusName?: string;
    sourceOrderNo?: string;
    createTime?: string;
    updateTime?: string;
    createBy?: string;
    updateBy?: string;
  }[];
};

export const sampleWarehouseReceiptDetail = (params: SampleWarehouseReceiptDetailRequest) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptDetailResult>>(
    '/warehouse/warehouse/sample-storage-orders/detail',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};

export type SampleWarehouseReceiptExportRequest = {
  operatorId?: string;
  operatorName?: string;
  pageNum?: number;
  pageSize?: number;
  orderNo?: string;
  type?: number;
  expressNo?: string;
  businessUserId?: number;
  warehouseId?: number;
  status?: number;
  createBy?: number;
  createTimeStart?: string;
  createTimeEnd?: string;
  updateBy?: number;
  updateTimeStart?: string;
  updateTimeEnd?: string;
  productName?: string;
  brand?: string;
  barcode?: string;
  sourceOrderNo?: string;
};

export const exportSampleWarehouseReceipt = (params: SampleWarehouseReceiptExportRequest) => {
  return exportFile<SampleWarehouseReceiptExportRequest, Blob>({
    url: Const.baseHost + '/warehouse/warehouse/sample-storage-orders/export',
    params,
    header: {
      accessAuth: 'basic.x1.xxxx1',
      Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      'Content-Type': 'application/json',
    },
  });
};

export type SampleWarehouseReceiptConfirmStorageRequest = {
  id?: number;
};

export type SampleWarehouseReceiptConfirmStorageResult = boolean;

export const sampleWarehouseReceiptConfirmStorage = (
  params: SampleWarehouseReceiptConfirmStorageRequest,
) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptConfirmStorageResult>>(
    '/warehouse/warehouse/sample-storage-orders/confirm-storage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};

export type SampleWarehouseReceiptCancelRequest = {
  id?: number;
};

export type SampleWarehouseReceiptCancelResult = boolean;

export const sampleWarehouseReceiptCancel = (params: SampleWarehouseReceiptCancelRequest) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptCancelResult>>(
    '/warehouse/warehouse/sample-storage-orders/cancel',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};

export type SampleWarehouseReceiptAddAndConfirmStorageRequest = {
  id?: number;
  type?: number;
  warehouseId?: number;
  expressNo?: string;
  receiver?: string;
  contactInfo?: string;
  sender?: string;
  senderAddress?: string;
  businessUserId?: string;
  businessUserName?: string;
  sourceOrderNo?: string;
  remark?: string;
  itemList?: {
    id?: number;
    productName?: string;
    brand?: string;
    barcode?: string;
    productCode?: string;
    specification?: string;
    imageUrl?: string;
    category?: string;
    unit?: string;
    model?: string;
    planQuantity?: number;
    actualQuantity?: number;
    inventoryId?: number;
    areaId?: number;
    locationId?: number;
    expireDate?: string; // 有效期
    remark?: string;
  }[];
};

export type SampleWarehouseReceiptAddAndConfirmStorageResult = Record<string, never>;

export const sampleWarehouseReceiptAddAndConfirmStorage = (
  params: SampleWarehouseReceiptAddAndConfirmStorageRequest,
) => {
  return Fetch<ResponseWithResult<SampleWarehouseReceiptAddAndConfirmStorageResult>>(
    '/warehouse/warehouse/sample-storage-orders/add-and-confirm-storage',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: 'basic.x1.xxxx1',
        Authorization: 'Bearer' + ((window as any).token ? ' ' + (window as any).token : ''),
      },
    },
  );
};
// 2024年12月31日 开山ai结尾共生成468行代码
