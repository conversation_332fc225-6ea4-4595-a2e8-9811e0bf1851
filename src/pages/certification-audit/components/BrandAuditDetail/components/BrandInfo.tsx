import React from 'react';
import { SplitLayout, DetailTitle, renderQualification, HitResultsTag } from './index';
import styles from '../index.module.less';
import { Tag, Icon, Popover } from 'antd';
import auditStyle from '../../AuditDetail/index.module.less';
import { BrandQualificationDetailResult } from '../../../service';
import { MAP_TYPE, BRAND_QUALIFICATION, BP_BRAND_QUALIFICATION } from '../../../hooks';
import {
  RISK_LEVEL_NAME,
  RISK_LEVEL_ENUM,
  RISK_LEVEL_COLOR,
  BRAND_TYPE_NAME,
  BRAND_TYPE_ENUM,
} from '../../../types';
import moment from 'moment';

interface IProps {
  detail: BrandQualificationDetailResult;
  shopMap: {
    [key: string]: MAP_TYPE;
  };
  bpMap: {
    [key: string]: MAP_TYPE;
  };
  brandQualification: BRAND_QUALIFICATION;
  bpBrandQualification: BP_BRAND_QUALIFICATION;
  automatedAuditRulesResults: Required<BrandQualificationDetailResult>['automatedAuditRulesResults'];
}

const BrandInfo = (props: IProps) => {
  const {
    shopMap,
    bpMap,
    brandQualification,
    bpBrandQualification,
    automatedAuditRulesResults,
    detail,
  } = props;
  return (
    <div className={styles['info-box']}>
      <SplitLayout isLeftBorder={false}>
        <DetailTitle title="品牌信息" rightRender={<Tag color="blue">商家</Tag>} />
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            height: '100%',
          }}
        >
          <Tag color="volcano">商务</Tag>
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>品牌名称：</div>
          <div className={auditStyle['legal-detail']}>{brandQualification?.brandName || '-'}</div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>品牌名称：</div>
          <div className={auditStyle['legal-detail']}>{bpBrandQualification?.brandName || '-'}</div>
        </div>
        <div>
          {automatedAuditRulesResults['TRADEMARK_INCONSISTENT_WITH_BRAND'] ? (
            automatedAuditRulesResults['TRADEMARK_INCONSISTENT_WITH_BRAND']?.map(
              (item, index: number) => {
                return (
                  <div className={auditStyle['result-line']} key={index}>
                    <Tag color={RISK_LEVEL_COLOR[item?.riskLevel as RISK_LEVEL_ENUM]}>
                      {RISK_LEVEL_NAME[item?.riskLevel as RISK_LEVEL_ENUM]}
                    </Tag>
                    <HitResultsTag
                      hitResults={item?.hitResults}
                      riskLevel={item?.riskLevel as 'HIGH'}
                    />
                    <span>{item?.automatedAuditRulesResult || '-'}</span>
                  </div>
                );
              },
            )
          ) : (
            <></>
          )}
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>品牌类型：</div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.type ? BRAND_TYPE_NAME[brandQualification?.type] : '-'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>品牌类型：</div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.type ? BRAND_TYPE_NAME[bpBrandQualification?.type] : '-'}
          </div>
        </div>
        <div>
          {automatedAuditRulesResults['SPECIAL_BRAND_TYPE'] ? (
            automatedAuditRulesResults['SPECIAL_BRAND_TYPE']?.map((item, index: number) => {
              return (
                <div className={auditStyle['result-line']} key={index}>
                  <Tag color={RISK_LEVEL_COLOR[item?.riskLevel as RISK_LEVEL_ENUM]}>
                    {RISK_LEVEL_NAME[item?.riskLevel as RISK_LEVEL_ENUM]}
                  </Tag>
                  <HitResultsTag
                    hitResults={item?.hitResults}
                    riskLevel={item?.riskLevel as 'HIGH'}
                  />
                  <span>{item?.automatedAuditRulesResult || '-'}</span>
                </div>
              );
            })
          ) : (
            <></>
          )}
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>是否取得商标注册证：</div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.isReg ? '是' : '否'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>是否取得商标注册证：</div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.isReg ? '是' : '否'}
          </div>
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>商标注册证：</div>
          <div className={auditStyle['legal-detail']}>
            {renderQualification(shopMap['BRAND_REGISTRATION'] || [])}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>商标注册证：</div>
          <div className={auditStyle['legal-detail']}>
            {renderQualification(bpMap['BRAND_REGISTRATION'] || [])}
          </div>
        </div>
        <div>
          {automatedAuditRulesResults['INVALID_TRADEMARK_REGISTRATION'] ? (
            automatedAuditRulesResults['INVALID_TRADEMARK_REGISTRATION']?.map(
              (item, index: number) => {
                return (
                  <div className={auditStyle['result-line']} key={index}>
                    <Tag color={RISK_LEVEL_COLOR[item?.riskLevel as RISK_LEVEL_ENUM]}>
                      {RISK_LEVEL_NAME[item?.riskLevel as RISK_LEVEL_ENUM]}
                    </Tag>
                    <HitResultsTag
                      hitResults={item?.hitResults}
                      riskLevel={item?.riskLevel as 'HIGH'}
                    />
                    <span>{item?.automatedAuditRulesResult || '-'}</span>
                  </div>
                );
              },
            )
          ) : (
            <></>
          )}
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>
            {brandQualification?.isReg ? '注册号' : '申请号'}：
          </div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.isReg
              ? brandQualification?.regNum || '-'
              : brandQualification?.trademarkApplyNo || '-'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>
            {bpBrandQualification?.isReg ? '注册号' : '申请号'}：
          </div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.isReg
              ? bpBrandQualification?.regNum || '-'
              : bpBrandQualification?.trademarkApplyNo || '-'}
          </div>
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>
            {brandQualification?.isReg ? '注册人' : '申请人'}：
          </div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.isReg
              ? brandQualification?.regPerson || '-'
              : brandQualification?.trademarkApplicant || '-'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>
            {bpBrandQualification?.isReg ? '注册人' : '申请人'}：
          </div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.isReg
              ? bpBrandQualification?.regPerson || '-'
              : bpBrandQualification?.trademarkApplicant || '-'}
          </div>
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>
            {brandQualification?.isReg ? '注册日期' : '申请日期'}：
          </div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.isReg
              ? brandQualification?.brandStartTime
                ? moment(brandQualification?.brandStartTime).format('YYYY-MM-DD')
                : '-'
              : brandQualification?.trademarkApplyDate
              ? moment(brandQualification?.trademarkApplyDate).format('YYYY-MM-DD')
              : '-'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>
            {bpBrandQualification?.isReg ? '注册日期' : '申请日期'}：
          </div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.isReg
              ? bpBrandQualification?.brandStartTime
                ? moment(bpBrandQualification?.brandStartTime).format('YYYY-MM-DD')
                : '-'
              : bpBrandQualification?.trademarkApplyDate
              ? moment(bpBrandQualification?.trademarkApplyDate).format('YYYY-MM-DD')
              : '-'}
          </div>
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>有效期：</div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.brandEndTime
              ? moment(brandQualification?.brandEndTime).format('YYYY-MM-DD') === '2999-12-31'
                ? '长期有效'
                : moment(brandQualification?.brandEndTime).format('YYYY-MM-DD')
              : '-'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>有效期：</div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.brandEndTime
              ? moment(bpBrandQualification?.brandEndTime).format('YYYY-MM-DD') === '2999-12-31'
                ? '长期有效'
                : moment(bpBrandQualification?.brandEndTime).format('YYYY-MM-DD')
              : '-'}
          </div>
        </div>
        <div>
          {automatedAuditRulesResults['BROADCAST_DATE_OUT_OF_VALIDITY'] ? (
            automatedAuditRulesResults['BROADCAST_DATE_OUT_OF_VALIDITY']?.map(
              (item, index: number) => {
                return (
                  <div className={auditStyle['result-line']} key={index}>
                    <Tag color={RISK_LEVEL_COLOR[item?.riskLevel as RISK_LEVEL_ENUM]}>
                      {RISK_LEVEL_NAME[item?.riskLevel as RISK_LEVEL_ENUM]}
                    </Tag>
                    <HitResultsTag
                      hitResults={item?.hitResults}
                      riskLevel={item?.riskLevel as 'HIGH'}
                    />
                    <span>{item?.automatedAuditRulesResult || '-'}</span>
                    {/* {detail?.durationOfExclusiveRight ? (
                      <Popover
                        content={<>商标有效期以企查查数据为准 {detail?.durationOfExclusiveRight}</>}
                        overlayStyle={{ zIndex: '1101' }}
                      >
                        <Icon
                          type="question-circle"
                          theme="filled"
                          style={{ fontSize: '16px', marginLeft: '4px' }}
                        />
                      </Popover>
                    ) : (
                      <></>
                    )} */}
                  </div>
                );
              },
            )
          ) : (
            <></>
          )}
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>商标状态：</div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.trademarkStatus || '-'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>商标状态：</div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.trademarkStatus || '-'}
          </div>
        </div>
        <div>
          {automatedAuditRulesResults['REGISTRATION_STATUS_ERROR'] ? (
            automatedAuditRulesResults['REGISTRATION_STATUS_ERROR']?.map((item, index: number) => {
              return (
                <div className={auditStyle['result-line']} key={index}>
                  <Tag color={RISK_LEVEL_COLOR[item?.riskLevel as RISK_LEVEL_ENUM]}>
                    {RISK_LEVEL_NAME[item?.riskLevel as RISK_LEVEL_ENUM]}
                  </Tag>
                  <HitResultsTag
                    hitResults={item?.hitResults}
                    riskLevel={item?.riskLevel as 'HIGH'}
                  />
                  <span>{item?.automatedAuditRulesResult || '-'}</span>
                </div>
              );
            })
          ) : (
            <></>
          )}
        </div>
      </SplitLayout>
      <SplitLayout>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>类别：</div>
          <div className={auditStyle['legal-detail']}>
            {brandQualification?.trademarkCategory || '-'}
          </div>
        </div>
        <div className={auditStyle['legal-line']}>
          <div className={auditStyle['legal-line-title']}>类别：</div>
          <div className={auditStyle['legal-detail']}>
            {bpBrandQualification?.trademarkCategory || '-'}
          </div>
        </div>

        <div>
          {automatedAuditRulesResults['TRADEMARK_REGISTRATION_CERTIFICATE_DISCREPANCY'] ? (
            automatedAuditRulesResults['TRADEMARK_REGISTRATION_CERTIFICATE_DISCREPANCY']?.map(
              (item, index: number) => {
                return (
                  <div
                    className={auditStyle['result-line']}
                    key={index}
                    style={{ marginBottom: '10px' }}
                  >
                    <Tag color={RISK_LEVEL_COLOR[item?.riskLevel as RISK_LEVEL_ENUM]}>
                      {RISK_LEVEL_NAME[item?.riskLevel as RISK_LEVEL_ENUM]}
                    </Tag>
                    <HitResultsTag
                      hitResults={item?.hitResults}
                      riskLevel={item?.riskLevel as 'HIGH'}
                    />
                    <span>{item?.automatedAuditRulesResult || '-'}</span>
                  </div>
                );
              },
            )
          ) : (
            <></>
          )}
          <div>
            {automatedAuditRulesResults['TRADEMARK_REGISTRATION_NOT_GROUP'] ? (
              automatedAuditRulesResults['TRADEMARK_REGISTRATION_NOT_GROUP']?.map(
                (item, index: number) => {
                  return (
                    <div className={auditStyle['result-line']} key={index}>
                      <Tag color={RISK_LEVEL_COLOR[item?.riskLevel as RISK_LEVEL_ENUM]}>
                        {RISK_LEVEL_NAME[item?.riskLevel as RISK_LEVEL_ENUM]}
                      </Tag>
                      <HitResultsTag
                        hitResults={item?.hitResults}
                        riskLevel={item?.riskLevel as 'HIGH'}
                      />
                      <span>{item?.automatedAuditRulesResult || '-'}</span>
                    </div>
                  );
                },
              )
            ) : (
              <></>
            )}
          </div>
        </div>
      </SplitLayout>
    </div>
  );
};

export default BrandInfo;
