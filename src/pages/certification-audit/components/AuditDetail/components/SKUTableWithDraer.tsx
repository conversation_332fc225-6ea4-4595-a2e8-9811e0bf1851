import { Button, Table, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { DrawerProxy, WithToggleModal } from 'web-common-modules/components';
import { WithToggleModalProps } from 'web-common-modules/components/WithToggleModal';
import { qualificationAuditGetSkuInfo } from '@/pages/choice-list-new/components/LegalCheckDrawer/services/yml';
import { useRequest } from 'ahooks';

interface IProps extends WithToggleModalProps {
  selectionRoundNo: string;
  bizType: 'LIVE' | 'SHOP_WINDOW' | 'VIDEO_CLIP' | 'SLICE';
  snapshotId: string;
  type: 'BRAND' | 'SUPPLIER' | 'GOODS';
  autoType: 'GOODS' | 'BRAND' | 'SUPPLIER';
  visible?: boolean;
}

const AuditRecords: React.FC<IProps> = ({
  selectionRoundNo,
  visible,
  bizType,
  snapshotId,
  autoType,
  type,
  ...rest
}) => {
  const [selectSpuData, setSelectSpuData] = useState<any>([]);
  const { run, loading } = useRequest(qualificationAuditGetSkuInfo, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        const { result } = res;
        setSelectSpuData(result || []);
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });
  useEffect(() => {
    if (visible && selectionRoundNo) {
      //
      run({ selectionRoundNo, bizType, snapshotId, autoType });
    }
  }, [visible, selectionRoundNo, bizType]);
  return (
    <DrawerProxy
      width={600}
      visible={visible}
      footer={
        <Button type="primary" onClick={rest?.onCancel}>
          知道了
        </Button>
      }
      title="SKU"
      {...rest}
      zIndex={11000}
    >
      <Table
        dataSource={selectSpuData || []}
        rowKey={'id'}
        loading={false}
        columns={[
          {
            title: '规格',
            key: 'name',
            dataIndex: 'name',
            render: (_: string) => <span>{_ || '-'}</span>,
          },
          {
            title: '备用库存',
            key: 'stock',
            dataIndex: 'stock',
            render: (_: string) => <span>{_ || '-'}</span>,
          },
        ]}
        pagination={false}
      ></Table>
    </DrawerProxy>
  );
};
export default WithToggleModal<IProps>(AuditRecords);
