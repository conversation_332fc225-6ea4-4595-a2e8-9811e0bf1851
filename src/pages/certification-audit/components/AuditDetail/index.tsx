import React, { useEffect, useMemo, useState } from 'react';
import { DrawerProps } from 'antd/es/drawer';
import { WithToggleModal } from 'web-common-modules/components';
import { WithToggleModalProps } from 'web-common-modules/components/WithToggleModal';
import { Drawer, Spin, Button, Tag, message } from 'antd';
import style from '@/pages/choice-list-new/components/LegalCheckDrawer/index.module.less';
import {
  DetailTitle,
  ItemBox,
  AuditResult,
  RuleInfo,
  SelectionProcess,
  SplitLayout,
  BasicsInfo,
  LegalPerson,
  ExceptionMessage,
  AuditForm,
  Log,
} from './components';
import styles from './index.module.less';
import { useDetail } from '../../hooks';
import { AUDIT_METHOD_ENUM } from '../../types';
import { QualificationKeyEnum } from '../../../../common/constants/audit/legal-audit';
import { AuthWrapper } from 'qmkit';
import { manualAudit } from '../../service';
import { useRequest } from 'ahooks';
import moment from 'moment';

interface IProps extends WithToggleModalProps, DrawerProps {
  id?: any;
  onRefresh?: any;
}

const AuditDetail = (props: IProps) => {
  const { id, visible, onClose, onRefresh } = props;
  const [isManual, setIsManual] = useState<boolean>(false);
  const [businessTerm, setBusinessTerm] = useState<any[]>([]);

  const { detail, detailRun, detailLoading } = useDetail();

  useEffect(() => {
    if (visible) {
      detailRun({ id });
    }
  }, [visible]);

  // 判断人工审核按钮是否显示主要逻辑
  const btnShow = useMemo(() => {
    if (!detail?.qualificationSnapshot) {
      return false;
    }
    if (
      detail?.qualificationSnapshot?.auditResult === 'WAIT' ||
      (detail?.qualificationSnapshot?.auditResult === 'PASS' &&
        detail?.qualificationSnapshot?.auditType === AUDIT_METHOD_ENUM.MANUAL)
    ) {
      return false;
    }
    return true;
  }, [detail]);

  const automatedAuditRulesResults = useMemo(() => {
    return detail?.automatedAuditRulesResults || {};
  }, [detail]);

  const { run: manualAuditRun, loading: manualAuditLoading } = useRequest(manualAudit, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('审核成功');
        detailRun({ id });
        setIsManual(false);
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });

  const [auditValue, setAuditValue] = useState<any>({
    name: '',
    key: QualificationKeyEnum.SUPPLIER,
    init: false,
    done: false,
    timeMap: {},
    itemVersionId: 1,
  });

  // 人工审核确认
  const handleConfirm = () => {
    if (
      !auditValue?.auditState ||
      (auditValue?.auditState === 'NO_PASS' && !auditValue?.auditOpinion)
    ) {
      message.warning('请完善审核意见');
      return;
    }
    if (detail?.companyPlace === 'OUT_MAINLAND' && !businessTerm?.length) {
      message.warning('请选择营业期限');
      return;
    }
    const params = {
      id,
      auditOpinion: auditValue?.auditOpinion,
      auditResult: auditValue?.auditState,
    };
    if (detail?.companyPlace === 'OUT_MAINLAND') {
      const [businessTermStartDate, businessTermEndDate] = businessTerm;
      params.businessTermStartDate = moment(businessTermStartDate).format('YYYY-MM-DD');
      params.businessTermEndDate = moment(businessTermEndDate).format('YYYY-MM-DD');
    }
    manualAuditRun(params);
  };
  const handleClose = () => {
    onClose?.();
    onRefresh?.();
    setIsManual(false);
  };
  return (
    <Drawer
      onClose={handleClose}
      width={1350}
      maskClosable={true}
      className={style['legal-check-drawer']}
      visible={visible}
      style={{ transform: 'translateX(0)' }}
      title={
        <div className={styles['drawer-title']}>
          <div className={styles['drawer-title-text']}>
            <p>商家资质详情</p>
            {detail?.qualificationSnapshot &&
            !['REVIEW_PENDING', 'WAIT'].includes(
              detail?.qualificationSnapshot?.auditResult as '',
            ) ? (
              <Tag
                color={
                  detail?.qualificationSnapshot?.auditResult === 'PASS' ? '#52C41A' : '#ee0000'
                }
              >
                {detail?.qualificationSnapshot?.auditResult === 'PASS' ? '合格' : '不合格'}
              </Tag>
            ) : (
              <></>
            )}
          </div>
          {/* {} */}
          {!isManual ? (
            <AuthWrapper functionName="f_certification_audit_audit">
              <Button
                type="primary"
                onClick={() => {
                  setIsManual(true);
                  if (
                    detail?.companyPlace === 'OUT_MAINLAND' &&
                    detail?.supplierQualificationBasicsInfo?.businessTermEndDate &&
                    detail?.supplierQualificationBasicsInfo?.businessTermStartDate
                  ) {
                    setBusinessTerm([
                      moment(detail?.supplierQualificationBasicsInfo?.businessTermStartDate),
                      moment(detail?.supplierQualificationBasicsInfo?.businessTermEndDate),
                    ]);
                  }
                }}
              >
                人工审核
              </Button>
            </AuthWrapper>
          ) : (
            <></>
          )}
          {isManual ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Button
                onClick={() => {
                  setIsManual(false);
                }}
                loading={manualAuditLoading}
              >
                取消
              </Button>
              <Button
                type="primary"
                style={{ marginLeft: '6px' }}
                onClick={handleConfirm}
                loading={manualAuditLoading}
              >
                确定
              </Button>
            </div>
          ) : (
            <></>
          )}
        </div>
      }
      headerStyle={{
        position: 'sticky',
        top: '0px',
        left: '0px',
        background: '#ffffff',
        zIndex: 2,
      }}
    >
      <Spin spinning={detailLoading}>
        {/* 人工审核不显示审核结果 */}
        {isManual ? (
          <></>
        ) : (
          <ItemBox>
            <DetailTitle title="审核结果" style={{ padding: '16px 0' }} />
            <AuditResult detail={detail} />
          </ItemBox>
        )}

        <ItemBox>
          <DetailTitle title="审核规则信息" style={{ padding: '16px 0' }} />
          <RuleInfo detail={detail} />
        </ItemBox>
        <ItemBox>
          <DetailTitle title="选品流程" />
          <SelectionProcess
            dataSource={detail?.selectionRounds || []}
            id={detail?.id!}
            autoType="SUPPLIER"
            type="SUPPLIER"
          />
        </ItemBox>
        <ItemBox>
          <BasicsInfo
            detail={detail}
            automatedAuditRulesResults={automatedAuditRulesResults}
            isManual={isManual}
            setBusinessTerm={setBusinessTerm}
            businessTerm={businessTerm}
          />
        </ItemBox>
        <ItemBox>
          <SplitLayout>
            <LegalPerson detail={detail} />
          </SplitLayout>
        </ItemBox>
        <ItemBox>
          <ExceptionMessage
            detail={detail}
            automatedAuditRulesResults={automatedAuditRulesResults}
          />
        </ItemBox>
        {/* 人工审核不显示操作日志 */}
        {isManual ? (
          <></>
        ) : (
          <ItemBox>
            <SplitLayout>
              <Log detail={detail} visible={visible} />
            </SplitLayout>
          </ItemBox>
        )}
        {isManual ? (
          <ItemBox>
            <div style={{ padding: '16px' }} className={styles['audit-box']}>
              <AuditForm
                type="info"
                value={auditValue}
                onChange={(value: any) => {
                  console.log('🚀 ~ AuditDetail ~ value:', value);
                  setAuditValue({ ...auditValue, ...value });
                }}
                style={{ backgroundColor: '#ffffff' }}
              />
            </div>
          </ItemBox>
        ) : (
          <></>
        )}
      </Spin>
    </Drawer>
  );
};

export default WithToggleModal(AuditDetail);
