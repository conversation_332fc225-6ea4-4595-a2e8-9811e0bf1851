import React, { useState } from 'react';
import { getEmployeePageByRole, GetEmployeePageByRoleResult } from '../service/index';
import { useRequest } from 'ahooks';
import { handleResponse } from 'web-common-modules/utils/response';
import { message } from 'antd';
import { debounce } from 'lodash';

export type GetEmployeePageByRoleResultRecords = Required<GetEmployeePageByRoleResult>['records'];

export const useLegal = (accountState?: number) => {
  const [roleList, setRoleList] = useState<GetEmployeePageByRoleResultRecords>([]);
  const { run: roleRun, loading: roleLoading } = useRequest(getEmployeePageByRole, {
    manual: true,
    onSuccess({ res }) {
      handleResponse(res).then((res) => {
        if (res?.success) {
          const { records } = res?.result || {};
          setRoleList(records || []);
        } else {
          message.warning(res?.message || '网络异常');
        }
      });
    },
  });

  const handleRoleChange = (v: string) => {
    if (!v) {
      roleRun({ current: 1, size: 20, accountState });
    }
  };
  const handelRoleSearch = debounce((employeeName: string) => {
    roleRun({ current: 1, size: 20, employeeName, accountState });
  }, 500);
  const handleRoleBlur = () => {
    roleRun({ current: 1, size: 20, accountState });
  };
  return { roleList, roleRun, roleLoading, handleRoleChange, handelRoleSearch, handleRoleBlur };
};
