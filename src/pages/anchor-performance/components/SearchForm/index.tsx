import { Input, Select, DatePicker } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import SearchFormComponent, {
  searchItem,
} from '../../../anchor-information/components/searchFormComponent';
import { useDeptList } from '@/hooks/useDeptList';
import { COST_CATEGORY, COST_TYPE, STATUS, COSTSTATUS } from '../../utils/enum';
import { useLiveRoomList } from '@/hooks/useLiveRoomList';
import { FormType } from '../..';

interface IProps extends FormComponentProps<FormType> {
  onSearch: () => void;
  onReset: () => void;
  loading: boolean;
}

const { Option } = Select;

const SearchForm: React.FC<IProps> = ({ form, onSearch, onReset, loading }) => {
  const { deptList, loading: deptLoading } = useDeptList();
  const { liveList, loading: liveRoomLoaindg, getLiveRoomList, handleSearch } = useLiveRoomList();
  const handleDept = (value: string) => {
    form.setFieldsValue({ liveRoomId: undefined });
    getLiveRoomList({ deptId: value });
  };
  const options: Record<string, searchItem> = {
    search: {
      label: '主播名称/编号',
      renderNode: <Input placeholder="请输入" />,
    },
    buId: {
      label: '事业部',
      renderNode: (
        <Select placeholder="请选择" allowClear onChange={handleDept} loading={deptLoading}>
          {deptList?.map((item) => (
            <Option value={item.value} key={item.value}>
              {item.label}
            </Option>
          ))}
        </Select>
      ),
    },
    liveRoomIdList: {
      label: '直播间',
      renderNode: (
        <Select
          placeholder="请选择"
          allowClear
          // onChange={handleDept}
          loading={liveRoomLoaindg}
          mode="multiple"
          maxTagCount={1}
          showSearch
          defaultActiveFirstOption={false}
          showArrow={false}
          filterOption={false}
          onSearch={handleSearch}
          onBlur={() => {
            handleSearch('');
          }}
        >
          {(form.getFieldValue('buId') ? liveList : undefined)?.map((item) => (
            <Option value={item.id} key={item.id}>
              {item.name}
            </Option>
          ))}
        </Select>
      ),
    },
    realLiveDate: {
      label: '实际直播场次',
      renderNode: <DatePicker.RangePicker format={'YYYY-MM-DD'} />,
    },
    recordCode: {
      label: '记录编号',
      renderNode: <Input placeholder="请输入" />,
    },
    costType: {
      label: '费用种类',
      renderNode: (
        <Select placeholder="请选择" allowClear>
          {Object.entries(COST_TYPE)
            .map((key) => ({ label: key[1], key: key[0] }))
            .filter((item) => isNaN(Number(item.label)))
            .map((item) => (
              <Option value={item.key} key={item.key}>
                {item.label}
              </Option>
            ))}
        </Select>
      ),
    },
    statusList: {
      label: '流程状态',
      renderNode: (
        <Select placeholder="请选择" allowClear mode="multiple" maxTagCount={1}>
          {Object.entries(STATUS)
            .filter((item) => typeof item[1] !== 'number')
            .map((key) => ({ label: key[1], key: key[0] }))
            .map((item) => (
              <Option value={item.key} key={item.key}>
                {item.label}
              </Option>
            ))}
        </Select>
      ),
    },
    costStatusList: {
      label: '费用单据状态',
      renderNode: (
        <Select placeholder="请选择" allowClear mode="multiple" maxTagCount={1}>
          {Object.entries(COSTSTATUS)
            .filter((item) => typeof item[1] !== 'number')
            .map((key) => ({ label: key[1], key: key[0] }))
            .map((item) => (
              <Option value={item.key} key={item.key}>
                {item.label}
              </Option>
            ))}
        </Select>
      ),
    },
  };

  return (
    <SearchFormComponent
      form={form}
      options={options}
      loading={loading}
      onSearch={onSearch}
      onReset={onReset}
    />
  );
};

export default SearchForm;
