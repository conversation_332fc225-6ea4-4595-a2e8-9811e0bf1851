import { useEffect, useState } from 'react';
import Socket from './socket';
import { Const, history } from 'qmkit';
import { useMessages, useFriends, Friend } from '../indexDB';
import { sendNotification } from '../notice';
import { MessageType } from '../type';

export const getWebSocketUrl = (): string => {
  const login = localStorage.getItem('jgpy-crm@login');
  const employeeId = login ? JSON.parse(login)?.employeeId : '';
  return `${Const.baseHost}/workorder/ws/chat?userId=cs:${employeeId}`;
};

export const setNotification = async (message: MessageType) => {
  try {
    const notification = await sendNotification({
      title: message?.receiverName,
      body: message?.data,
      icon: message?.receiverHeadImg,
      requireInteraction: true,
      onClick: () => {
        history.push(
          `/private-message-task?sendUserId=${message?.receiverId}&sendUserName=${message?.receiverName}&sendUserAvatar=${message?.receiverHeadImg}&userId=${message?.sendUserId}`,
        );
      },
    });

    if (notification) {
      console.log('通知创建成功');
      // 5秒后自动关闭通知
      //   setTimeout(() => {
      //     notification.close();
      //   }, 5000);
    } else {
      console.log('通知创建失败');
    }
  } catch (error) {
    console.error('通知创建失败:', error);
  }
};
export const useWebSocket = (setFriend: (friend: Friend) => void) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const { getMessages, setMessage, setBatchMessages, messages } = useMessages();

  useEffect(() => {
    // ai生成
    console.log('初始化WebSocket连接...');
    const ws = Socket.getInstance().init({
      url: getWebSocketUrl(),
      onMessage: (message: MessageType) => {
        if (message?.messageType === 'heartbeat') {
          return;
        }
        if (message?.ack) {
          return;
        }
        setMessage({
          ...message,
          senderId: message?.receiverId,
          senderName: message?.receiverName,
          senderHeadImg: message?.receiverHeadImg,
        });
        // 回复消息
        ws?.send?.(
          JSON.stringify({
            messageType: 'ack',
            messageId: message?.id,
          }),
        );
        setFriend({
          userId: message?.receiverId,
          name: message?.receiverName,
          avatar: message?.receiverHeadImg,
        });

        setNotification(message);
      },
    });
    setSocket(ws);

    // 组件卸载时清理WebSocket连接
    return () => {
      console.log('组件卸载，关闭WebSocket连接');
      ws.disconnect(1000, '组件卸载');
    };
    // 2024年7月9日 开山ai结尾共生成9行代码
  }, []);

  return {
    socket,
    getMessages,
    setMessage,
    setBatchMessages,
    messages,
  };
};
