import React, { useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.less';
import { Avatar, message } from 'antd';
import { MessageType } from '../../utils/type';
import { AccountConfigListResult } from '@/pages/weibo/weibo-config-center/services';
import { Friend } from '../../utils/indexDB';
const HistoryItem: React.FC<{
  item: MessageType;
  user?: AccountConfigListResult[number];
  activeUser?: Friend;
}> = ({ item, user, activeUser }) => {
  const isSelf = useMemo(
    () => item?.senderId?.toString() !== activeUser?.userId?.toString(),
    [item, user, activeUser],
  );
  return (
    <div
      className={styles.historyItem}
      style={{ justifyContent: isSelf ? 'flex-end' : 'flex-start' }}
    >
      {isSelf && (
        <div className={styles.historyItemContent}>
          <div className={styles.historyItemHeader} style={{ justifyContent: 'flex-end' }}>
            <p className={styles.historyItemHeaderName}>{item?.senderName}</p>
            <p className={styles.historyItemHeaderTime}>{item.createdAt}</p>
          </div>
          <div className={styles.historyItemMessage} style={{ justifyContent: 'flex-end' }}>
            <p className={styles.historyItemMessageTextRight}>{item.data}</p>
          </div>
        </div>
      )}
      <Avatar
        src={
          !isSelf
            ? item?.senderHeadImg
            : 'https://befriend-static-dev.oss-cn-hangzhou.aliyuncs.com/images/0086JbLrly8glivvp8fqhj30ow0owt9w.jpg'
        }
      />
      {!isSelf && (
        <div className={styles.historyItemContent}>
          <div className={styles.historyItemHeader}>
            <p className={styles.historyItemHeaderName}>{item.senderName}</p>
            <p className={styles.historyItemHeaderTime}>{item.createdAt}</p>
          </div>
          <div className={styles.historyItemMessage}>
            <p className={styles.historyItemMessageTextLeft}>{item.data}</p>
          </div>
        </div>
      )}
    </div>
  );
};

const ChatHistory: React.FC<{
  messageList: MessageType[];
  user?: AccountConfigListResult[number];
  activeUser?: Friend;
}> = ({ messageList, user, activeUser }) => {
  const chatHistoryRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (messageList) {
      if (
        (chatHistoryRef.current?.scrollHeight ?? 0) -
          ((chatHistoryRef.current?.offsetHeight ?? 0) + (chatHistoryRef.current?.scrollTop ?? 0)) <
        200
      ) {
        setTimeout(() => {
          chatHistoryRef.current?.scrollTo({
            top: chatHistoryRef.current?.scrollHeight,
            behavior: 'smooth',
          });
        }, 100);
      }
    }
  }, [messageList]);
  useEffect(() => {
    if (chatHistoryRef.current) {
      setTimeout(() => {
        setTimeout(() => {
          chatHistoryRef.current?.scrollTo({
            top: chatHistoryRef.current?.scrollHeight,
          });
        }, 100);
      }, 1000);
    }
  }, [activeUser]);
  return (
    <div className={styles.chatHistory} ref={chatHistoryRef}>
      {messageList.map((item) => (
        <HistoryItem key={item.id} item={item} user={user} activeUser={activeUser} />
      ))}
    </div>
  );
};

export default ChatHistory;
