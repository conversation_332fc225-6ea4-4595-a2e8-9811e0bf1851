import { Button } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout';
import styles from './index.module.less';
import Space from '@/components/Space';
import ChatUserList from './modules/ChatUserList';
import ChatContent from './modules/ChatContent';
import { AuthWrapper } from 'qmkit';
import UserSelect from './components/UserSelect';
import { AccountConfigListResult } from '../weibo-config-center/services';
import { useWebSocket } from './utils/websocket';
import { MessageType } from './utils/type';
import { useFriends, Friend } from './utils/indexDB';
import { getQueryParams } from '../weibo-config-center/utils/utils';
import { useOnline } from '../weibo-tasks/utils/hook';
import moment from 'moment';

const buttonStyle = {
  marginTop: '-10px',
};

const PrivateMessageTask = () => {
  // const [onLineStatus, setOnlineStatus] = useState<'online' | 'offline'>('online');
  const [user, setUser] = useState<AccountConfigListResult[number]>();
  const [activeUser, setActiveUser] = useState<Friend>();
  const queryInfo = getQueryParams() as {
    sendUserId: string;
    sendUserName: string;
    sendUserAvatar: string;
    userId: string;
  };
  const { getFriends, friends, setFriend } = useFriends();
  const { socket, getMessages, setMessage, setBatchMessages, messages } = useWebSocket(setFriend);
  const { online, setOnline, setOffLine, loading: onlineLoading } = useOnline(103);
  const [friendsFirstMap, setFriendsFirstMap] = useState<Map<string | number, string>>(new Map());

  // 发送消息
  const sendMessage = useCallback(
    (message: MessageType) => {
      socket?.send(JSON.stringify(message));
      getFriends();
    },
    [socket],
  );
  useEffect(() => {
    setActiveUser({
      name: queryInfo.sendUserName,
      userId: Number(queryInfo.sendUserId),
      avatar: queryInfo.sendUserAvatar,
      description: '',
      key: queryInfo.sendUserId,
    });
    setFriend({
      name: queryInfo.sendUserName,
      userId: Number(queryInfo.sendUserId),
      avatar: queryInfo.sendUserAvatar,
      description: '',
      key: queryInfo.sendUserId,
    }).then(() => {
      getFriends();
    });
  }, []);

  useEffect(() => {
    // ai生成
    if (messages && messages.length > 0) {
      // 创建一个新的Map来存储最新的消息
      const newFriendsFirstMap = new Map<string | number, string>(friendsFirstMap);

      // 遍历所有消息，找出每个receiverId对应的最新消息
      friends?.forEach((item) => {
        if (!item || !item.userId) return;

        const userId = item.userId;
        const msg = messages
          ?.filter((item) => item.receiverId === userId)
          ?.map((item) => ({
            ...item,
            createdAt: new Date(moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss')),
          }))
          ?.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())?.[0]?.data;
        newFriendsFirstMap.set(userId, msg);
      });

      // 更新状态
      setFriendsFirstMap(newFriendsFirstMap);
    }
    // 2024年07月09日 开山ai结尾共生成25行代码
  }, [friends, messages]);

  return (
    <PageLayout className={styles.publishFeeManageContainer}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          padding: '20px 10px',
        }}
      >
        <section className={styles.header}>
          <Space>
            <AuthWrapper functionName="f_task_message_ready">
              <Button
                {...(online && { type: 'primary' })}
                style={buttonStyle}
                loading={onlineLoading}
                onClick={() => {
                  setOnline();
                }}
              >
                就绪
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_task_message_offline">
              <Button
                {...(!online && { type: 'primary' })}
                style={buttonStyle}
                loading={onlineLoading}
                onClick={() => {
                  setOffLine();
                }}
              >
                离线
              </Button>
            </AuthWrapper>
          </Space>
          <UserSelect user={user} setUser={setUser} />
        </section>
        <section className={styles.chatBox}>
          <article className={styles.chatContent}>
            <div className={styles.chatUserListBox}>
              <ChatUserList
                friends={friends?.filter((item) => item.userId !== queryInfo.userId)}
                activeUser={activeUser}
                setActiveUser={setActiveUser}
                friendsFirstMap={friendsFirstMap}
              />
            </div>
            <div className={styles.chatInputBox}>
              <ChatContent
                onSendMessage={sendMessage}
                user={user}
                activeUser={activeUser}
                messages={messages}
                getMessages={getMessages}
                setMessage={setMessage}
                setBatchMessages={setBatchMessages}
              />
            </div>
          </article>
        </section>
      </div>
    </PageLayout>
  );
};

export default PrivateMessageTask;
