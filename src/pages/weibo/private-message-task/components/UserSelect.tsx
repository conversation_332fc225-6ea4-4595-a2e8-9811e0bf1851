import { Button, Dropdown, Menu } from 'antd';
import React, { useEffect, useState } from 'react';
import { accountConfigList, AccountConfigListResult } from '../../weibo-config-center/services';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { ClickParam } from 'antd/lib/menu';
import { useLocation } from 'react-router-dom';

const UserSelect: React.FC<{
  user?: AccountConfigListResult[number];
  setUser: (user?: AccountConfigListResult[number]) => void;
}> = ({ user, setUser }) => {
  const [commentBtns, setCommentBtns] = useState<AccountConfigListResult>([]);
  const [privateVisible, setPrivateVisible] = useState(false);
  const location = useLocation();
  const handlePrivateMenuClick = (e: ClickParam) => {
    console.log(e);
    setUser(commentBtns.find((item) => item?.id === e?.key));
    setPrivateVisible(false);
  };
  const handlePrivateVisibleChange = (value: boolean) => {
    setPrivateVisible(value);
  };
  const getCommentBtns = async () => {
    const result = await responseWithResultAsync({
      request: accountConfigList,
      params: {},
    });
    if (result) {
      const userList = result?.filter((item) => item.isService === 1);
      setCommentBtns(userList);
      const userId = new URLSearchParams(location.search).get('userId');
      setUser(userList?.find((item) => item?.id === userId) ?? userList?.[0]);
      return;
    }
    setCommentBtns([]);
  };
  useEffect(() => {
    getCommentBtns();
  }, []);
  return (
    <Dropdown
      overlay={
        <Menu onClick={handlePrivateMenuClick}>
          {commentBtns.map((item) => (
            <Menu.Item key={item?.id}>{item?.nickname}</Menu.Item>
          ))}
        </Menu>
      }
      onVisibleChange={handlePrivateVisibleChange}
      visible={privateVisible}
    >
      <Button style={{ marginLeft: '20px' }} size="large">
        {user?.nickname ?? '请选择用户'}
      </Button>
    </Dropdown>
  );
};

export default UserSelect;
