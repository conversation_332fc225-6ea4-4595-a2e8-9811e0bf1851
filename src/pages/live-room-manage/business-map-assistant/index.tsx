import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import PaginationProxy from '@/common/constants/Pagination';
import { SearchForm, AddMap } from './components';
import { options, columns } from './dataSource';
import { AuthWrapper } from 'qmkit';
import { Button, message } from 'antd';
import { Table } from 'antd';
import styles from './index.module.less';
import { businessList, businessStatus } from '@/services/yml/business/sales-details/index';
import { useTableHeight } from '@/common/constants/hooks/index';
//商务映射助理
const BusinessMapAssistant = () => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const getList = (value) => {
    setLoading(true);
    businessList({ ...value })
      .then((res) => {
        if (res?.res?.code === '200') {
          const result = res?.res?.result;
          setDataSource(result?.records);
          setPagination({
            total: result.total,
            current: result.current,
            size: result.size,
          });
          setLoading(false);
        } else {
          message.error(res?.res?.message);
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  };
  const [pagination, setPagination] = useState({ current: 1, size: 20, total: 0 });
  const [formData, setFormData] = useState({});
  const onPageChange = (current: number, size: number) => {
    console.log(current, size);
    setPagination({
      ...pagination,
      current: current,
      size: size,
    });
    getList({ ...pagination, ...formData, current, size });
  };
  useEffect(() => {
    getList({ ...pagination, ...formData });
  }, []);
  const changeStatus = (value) => {
    console.log(value);
    businessStatus({
      id: value.id,
      businessMappingStatusEnum: value.status === 'ENABLE' ? 'DISABLE' : 'ENABLE',
    })
      .then((res) => {
        console.log(res);
        getList({ ...pagination, ...formData });
      })
      .catch((err: any) => {
        console.log(err);
      });
  };
  const edit = (value) => {
    console.log(value);
  };
  const { getHeight, tableHeight } = useTableHeight(75);
  return (
    <PageLayout>
      <div className={`${styles.publishFeeContainer} ${styles['publish-fee-page']} vh46Px`}>
        <div className="formHeight">
          <SearchForm
            options={options}
            onSearch={(value) => {
              console.log(value);
              setFormData({ ...formData, ...value });
              getList({ ...pagination, ...formData, ...value });

              //@TODO: 搜索之后获取列表
            }}
          />
          <div>
            {/* f_business_map_assistant_add */}
            <AuthWrapper functionName="f_business_map_assistant_add">
              <AddMap
                getList={() => {
                  getList({ ...pagination, ...formData });
                }}
              >
                <Button type="primary" icon="plus">
                  新建商务映射助理
                </Button>
              </AddMap>
            </AuthWrapper>
          </div>
        </div>
        <div className={styles['list-table']}>
          <Table
            rowKey={'id'}
            loading={loading}
            pagination={false}
            dataSource={dataSource}
            columns={columns({ changeStatus, edit })}
            scroll={{ y: tableHeight, x: '100%' }}
          ></Table>
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <PaginationProxy
            current={pagination?.current}
            pageSize={pagination?.size}
            total={pagination?.total}
            // @ts-ignore
            onChange={onPageChange}
            valueType="flatten"
          />
        </div>
      </div>
    </PageLayout>
  );
};

export default BusinessMapAssistant;
