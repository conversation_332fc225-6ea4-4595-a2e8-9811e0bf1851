import React, { useState, useEffect } from 'react';
import { DrawerProps } from 'antd/es/drawer';

import { Descriptions, Table, message } from 'antd';
import styles from '../index.module.less';
import { detailColumns } from '../dataSource';
import { businessInfo } from '@/services/yml/business/sales-details/index';
import { PageLayout } from 'web-common-modules/components';
import {
  DetailContentLayout,
  DetailTitle,
  SpinCard,
  Card,
  Title,
} from '@/components/DetailFormCompoments';
import {
  DetailContextBox,
  DetailContentItem,
} from '@/components/DetailFormCompoments/DetailContentItem';
import { getQueryParams } from 'web-common-modules/utils/params';

const Item = DetailContentItem;
interface PRPOS extends DrawerProps {
  id?: number | string;
}

const Detail: React.FC<PRPOS> = (props) => {
  // const { id, ...rest } = props;
  const id = getQueryParams()?.id;
  const [dataSource, setDataSource] = useState([{}]);
  const [info, setInfo] = useState({ deptName: '', assistantName: '', businessInfoList: [] });
  useEffect(() => {
    if (id) {
      businessInfo({ id: id })
        .then((res) => {
          if (res?.res?.code === '200') {
            const result = res?.res?.result;
            setInfo(result);
            setDataSource(result.operatorLogList);
          } else {
            message.error(res?.res?.message);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);
  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText={'商务映射助理详情'}></DetailTitle>
        <SpinCard spinning={false}>
          <Card>
            <Title>基本信息</Title>
            <DetailContextBox>
              <Item label="事业部">
                <p>{info?.deptName || '-'}</p>
              </Item>
              <Item label="商务助理">
                <p>{info?.assistantName || '-'}</p>
              </Item>
              <Item label="商务">
                <p>
                  {info?.businessInfoList.map((item, index) => {
                    return (
                      item.businessName + (index < info?.businessInfoList.length - 1 ? '、' : '')
                    );
                  })}
                </p>
              </Item>
            </DetailContextBox>
          </Card>
          <Card>
            <Title>操作日志</Title>
            <Table
              dataSource={dataSource}
              scroll={{ y: 500 }}
              columns={detailColumns()}
              loading={false}
              pagination={false}
            ></Table>
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default Detail;
