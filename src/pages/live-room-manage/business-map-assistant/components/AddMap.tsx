import React, { useEffect, useState } from 'react';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import { FormComponentProps } from 'antd/es/form';
import { Form, Select, Input, Button, message, Modal } from 'antd';
import DrawerProxy from '@/components/Drawer';
import WithToggleModal from '@/components/WithToggleModal';
import { DrawerProps } from 'antd/es/drawer';
import { classNames } from '@/utils/moduleUtils';
import { deptList } from '@/services/yml/business/sales-details/index';
import {
  businessAdd,
  businessInfo,
  businessEdit,
} from '@/services/yml/business/sales-details/index';
import UserList from './UserList';
const { confirm } = Modal;
interface PRPOS extends DrawerProps, FormComponentProps {
  id?: number | string;
  getList?: () => void;
  reset: () => void;
}

const AddGroup: React.FC<PRPOS & WithToggleModalProps> = (props) => {
  const { id, reset, form, ...rest } = props;
  const { getFieldDecorator } = form;
  const onOk = () => {
    confirm({
      title: '请确认要提交当前内容吗？',
      content: '',
      onOk() {
        form.validateFields((err, value) => {
          if (!err) {
            // console.log('🚀 ~ file: AddRules.tsx:25 ~ okBtn ~ value:', value);
            const params = {
              id: id ? id : '',
              deptId: '',
              assistantId: '',
              assistantName: '',
              businessInfoRequestList: [],
            };
            params.deptId = value.deptId;
            params.assistantId = value.assistant.key;
            params.assistantName = value.assistant.label;
            params.businessInfoRequestList = [];
            value.business &&
              value.business.forEach((item) => {
                params.businessInfoRequestList.push({
                  businessId: item.key,
                  businessName: item.label,
                  id: item?.id || '',
                });
              });
            console.log('params', params);
            const url = id ? businessEdit : businessAdd;
            url(params)
              .then((res) => {
                console.log('新增商务映射结果', res);
                if (res?.res?.code === '200') {
                  message.success(id ? '修改成功' : '新增成功');
                  props.getList && props.getList();
                  props.onCancel && props.onCancel();
                } else {
                  message.error(res?.res?.message);
                }
              })
              .catch((err) => {
                console.log('新增商务映射错误', err);
              });
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };
  const [options, setOptions] = useState([]);

  const getDeptList = () => {
    deptList({})
      .then((res) => {
        console.log(res);
        if (res?.res?.code === '200') {
          setOptions(res?.res?.result);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  useEffect(() => {
    getDeptList();
    if (id) {
      businessInfo({ id: id })
        .then((res) => {
          console.log(res);
          if (res?.res?.code === '200') {
            const result = res?.res?.result;
            form.setFieldsValue({
              deptId: result.deptId,
              assistant: { key: result?.assistantId, label: result?.assistantName },
              business:
                result?.businessInfoList.length &&
                result?.businessInfoList.map((item) => {
                  return {
                    id: item.id,
                    key: item.businessId,
                    label: item.businessName,
                  };
                }),
            });
          } else {
            message.error(res?.res?.message);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);
  const footer = [
    <Button key="cancel" className="ml-20" onClick={props.onCancel}>
      取消
    </Button>,
    <Button key="submit" className="ml-20" type="primary" loading={false} onClick={() => onOk()}>
      确定
    </Button>,
  ];
  return (
    <Modal
      className={classNames('select-user-modal')}
      title={id ? '编辑商务映射助理关系维护' : '新增商务映射助理关系维护'}
      width={510}
      {...rest}
      footer={footer}
      onOk={onOk}
      maskClosable={false}
      // visible={visible}
      // onClose={props.onCancel}
    >
      <div>
        <Form labelAlign="right" labelCol={{ span: 6 }} wrapperCol={{ span: 17 }}>
          <Form.Item className="mb-0" label="事业部">
            {getFieldDecorator('deptId', {
              rules: [
                {
                  required: true,
                  message: '请选择事业部',
                },
              ],
            })(
              <Select
                style={{ width: 300 }}
                disabled={id ? true : false}
                showSearch
                placeholder="请选择"
                onSearch={() => {
                  // @TODO: 搜索
                }}
              >
                {options.map((item) => {
                  return <Select.Option value={item?.id}>{item?.deptName}</Select.Option>;
                })}
              </Select>,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="商务助理">
            {getFieldDecorator('assistant', {
              rules: [
                {
                  required: true,
                  message: '请选择商务助理',
                },
              ],
            })(<UserList style={{ width: 300 }} disabled={id ? true : false} />)}
          </Form.Item>
          <Form.Item className="mb-0" label="商务">
            {getFieldDecorator('business', {
              rules: [
                {
                  required: true,
                  message: '请选择商务',
                },
              ],
            })(<UserList style={{ width: 300 }} mode="multiple" />)}
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default WithToggleModal(Form.create()(AddGroup));
