import React from 'react';
import styles from '../index.module.less';
import { AddGroup } from '../components';
import { AuthWrapper, history } from 'qmkit';
import { status_map, status_color, STATUS_ENUM } from '../types';
import moment from 'moment';
import { Modal, Tag } from 'antd';
import PopoverRowText from '@/components/PopoverRowText/index';
import { PlantformName, PLATFORM_ENUM } from '../../../../../web_modules/types';

const { confirm } = Modal;
export interface BtnInterface {
  edit?: (id: string) => void;
  openStop?: (params: any) => void;
  onRefresh: () => void;
}

export const columns = (params: BtnInterface) => {
  return [
    {
      title: '项目组',
      key: 'projectGroupName',
      dataIndex: 'projectGroupName',
      width: 100,
      render: (projectGroupName: string) => (
        <div>{<PopoverRowText text={projectGroupName || '-'} />}</div>
      ),
    },
    {
      title: '事业部',
      key: 'departmentName',
      dataIndex: 'departmentName',
      width: 100,
      render: (departmentName: string) => (
        <div>{<PopoverRowText text={departmentName || '-'} />}</div>
      ),
    },
    {
      title: '平台',
      key: 'platformEnum',
      dataIndex: 'platformEnum',
      width: 100,
      render: (platformSource: PLATFORM_ENUM) => <div>{PlantformName[platformSource] || '-'}</div>,
    },
    {
      title: '选品组长',
      key: 'SELECTION_LEADER',
      dataIndex: 'SELECTION_LEADER',
      width: 100,
      render: (SELECTION_LEADER: any[] = []) => (
        <div>{<PopoverRowText text={SELECTION_LEADER.toString() || '-'} />}</div>
      ),
    },
    {
      title: '选品',
      key: 'SELECTION',
      dataIndex: 'SELECTION',
      width: 100,
      render: (SELECTION = []) => (
        <div>{<PopoverRowText text={SELECTION.toString() || '-'} />}</div>
      ),
    },
    {
      title: '商务组长',
      key: 'BUSINESS_LEADER',
      dataIndex: 'BUSINESS_LEADER',
      width: 100,
      render: (BUSINESS_LEADER = []) => (
        <div>{<PopoverRowText text={BUSINESS_LEADER.toString() || '-'} />}</div>
      ),
    },
    {
      title: '商务',
      key: 'BUSINESS',
      dataIndex: 'BUSINESS',
      width: 100,
      render: (BUSINESS = []) => <div>{<PopoverRowText text={BUSINESS.toString() || '-'} />}</div>,
    },
    {
      title: '状态',
      key: 'projectGroupStatusEnum',
      dataIndex: 'projectGroupStatusEnum',
      width: 100,
      render: (projectGroupStatusEnum: STATUS_ENUM) => (
        <Tag color={status_color[projectGroupStatusEnum]}>
          {status_map[projectGroupStatusEnum] || '-'}
        </Tag>

        // <div style={{ display: 'flex', alignItems: 'center' }}>
        //   <span
        //     className={styles['status-round']}
        //     style={{ background: status_color[projectGroupStatusEnum] }}
        //   ></span>
        //   <span>{status_map[projectGroupStatusEnum]}</span>
        // </div>
      ),
    },
    {
      title: '创建时间',
      key: 'gmtCreated',
      dataIndex: 'gmtCreated',
      width: 100,
      render: (gmtCreated: string) => (
        <div>{gmtCreated ? moment(gmtCreated).format('YYYY-MM- HH:mm:ss') : '-'}</div>
      ),
    },
    {
      title: '修改时间',
      key: 'gmtModified',
      dataIndex: 'gmtModified',
      width: 100,
      render: (gmtModified: string) => (
        <div>{gmtModified ? moment(gmtModified).format('YYYY-MM- HH:mm:ss') : '-'}</div>
      ),
    },
    {
      title: '操作',
      key: 'other',
      width: 150,
      fixed: 'right',
      render: (records: any) => {
        const text = records.projectGroupStatusEnum === 'ENABLE' ? '禁用' : '开启';
        return (
          <div>
            <AuthWrapper functionName="f_project_team_group_edit">
              <AddGroup id={records.projectGroupId} reset={params.onRefresh}>
                <a style={{ marginRight: '4px' }}>编辑</a>
              </AddGroup>
            </AuthWrapper>
            <AuthWrapper functionName="f_project_team_group_open_close">
              <a
                onClick={() => {
                  confirm({
                    title: '提示',
                    content: `您确认要${text}?`,
                    onOk() {
                      params.openStop &&
                        params.openStop({
                          id: records.projectGroupId,
                          projectGroupStatusEnum:
                            records.projectGroupStatusEnum === 'ENABLE' ? 'DISABLE' : 'ENABLE',
                        });
                    },
                  });
                }}
              >
                {text}
              </a>
            </AuthWrapper>
            <a
              style={{ marginLeft: '4px' }}
              onClick={() => history.push(`/project-team-detail?id=${records.projectGroupId}`)}
            >
              详情
            </a>
          </div>
        );
      },
    },
  ];
};

export const detailColumns = () => {
  return [
    {
      title: '操作内容',
      key: 'operateContent',
      dataIndex: 'operateContent',
      width: 200,
      render: (operateContent: string) => <div>{operateContent || '-'}</div>,
    },
    {
      title: '操作人',
      key: 'operatorName',
      dataIndex: 'operatorName',
      render: (operator: string) => <div>{operator || '-'}</div>,
    },
    {
      title: '操作时间',
      key: 'operatorTime',
      dataIndex: 'operatorTime',
      render: (operatorTime: string) => <div>{operatorTime || '-'}</div>,
    },
  ];
};
