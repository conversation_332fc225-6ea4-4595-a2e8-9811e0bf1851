import React, { useEffect, useState } from 'react';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import WithToggleModal from '@/components/WithToggleModal';
import { FormComponentProps } from 'antd/es/form';
import { Modal, Form, Select, Input, Button, message } from 'antd';
import DrawerProxy from '@/components/Drawer';
import { DrawerProps } from 'antd/es/drawer';
import { classNames } from '@/utils/moduleUtils';
import {
  getAllDeptList,
  GetAllDeptListResult,
  addProjectGroup,
  editProjectGroup,
  projectGroupDetail,
} from '@/services/yml/live-room-manage';
import { useRequest } from 'ahooks';
import { handleResponse } from '@/utils/response';
import { usePerson } from '../../hooks';
import { OmitJDPlantformList, PLATFORM_ENUM } from '../../../../../web_modules/types';

interface PRPOS extends DrawerProps, FormComponentProps {
  id?: number | string;
  reset: () => void;
}

const { confirm } = Modal;

const AddGroup: React.FC<PRPOS & WithToggleModalProps> = (props) => {
  const { id, reset, form, ...rest } = props;
  const { getFieldDecorator } = form;
  const [departmentList, setDepartmentId] = useState<GetAllDeptListResult>([]);
  const [changeValue, setChangeValue] = useState<boolean>(false);

  const { run: addRun, loading: addLoading } = useRequest(addProjectGroup, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        // console.log('🚀 ~ file: AddGroup.tsx:32 ~ handleResponse ~ res:', res);
        message.success('新增成功');
        props.onCancel && props.onCancel();
        reset && reset();
      });
    },
  });
  const { run: detailRun, loading: detailLoading } = useRequest(projectGroupDetail, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        const { departmentId, departmentName, employInfoMap, projectGroupName, platformEnum } =
          res.result;

        form.setFieldsValue({
          department: { key: departmentId, label: departmentName },
          projectGroupName,
          platformEnum,
        });
        if (employInfoMap) {
          const list = {
            BUSINESS_LEADER: formatList(employInfoMap.BUSINESS_LEADER as any),
            BUSINESS: formatList(employInfoMap.BUSINESS as any),
            SELECTION_LEADER: formatList(employInfoMap.SELECTION_LEADER as any),
            SELECTION: formatList(employInfoMap.SELECTION as any),
          };
          form.setFieldsValue(list);
        }
      });
    },
  });

  const formatList = (list = []) => {
    return list.map((item: any) => ({
      key: item.employId,
      label: item.employName,
    }));
  };
  const keyToEmploy = (list = []) => {
    return list.map((item: any) => ({
      employId: item.key,
      employName: item.label,
    }));
  };
  const { run: editRun, loading: editLoading } = useRequest(editProjectGroup, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        // console.log('🚀 ~ file: AddGroup.tsx:32 ~ handleResponse ~ res:', res);
        message.success('编辑成功');
        props.onCancel && props.onCancel();
        reset && reset();
      });
    },
  });

  const onOk = () => {
    form.validateFields((err, values) => {
      if (err) return;
      const params = formateParam(values);
      confirm({
        title: '提示',
        content: '请确认要提交当前内容吗？',
        onOk() {
          if (id) {
            editRun({ ...params, id });
            return;
          }
          addRun(params);
        },
        onCancel() {
          // console.log('Cancel');
        },
      });
    });
  };

  const formateParam = (values: any) => {
    const {
      department,
      BUSINESS,
      BUSINESS_LEADER,
      SELECTION,
      SELECTION_LEADER,
      projectGroupName,
      platformEnum,
    } = values;
    const params: any = {
      departmentId: department.key,
      departmentName: department.label,
      projectGroupName,
      platformEnum,
      employeeInfoMap: {
        BUSINESS: keyToEmploy(BUSINESS),
        BUSINESS_LEADER: keyToEmploy(BUSINESS_LEADER),
        SELECTION: keyToEmploy(SELECTION),
        SELECTION_LEADER: keyToEmploy(SELECTION_LEADER),
      },
    };
    return params;
  };

  const handleCancel = () => {
    if (changeValue) {
      confirm({
        title: '提示',
        content: '取消后，新填写的内容不会生效，请确认要取消吗？',
        onOk() {
          props.onCancel && props.onCancel();
        },
        onCancel() {
          // console.log('Cancel');
        },
      });
    } else {
      props.onCancel && props.onCancel();
    }

    // if (id && changeValue) {
    //   // console.log('编辑');

    //   return;
    // }
    // props.onCancel && props.onCancel();
  };
  const footer = [
    <Button key="cancel" className="ml-20" onClick={handleCancel}>
      取消
    </Button>,
    <Button
      key="submit"
      className="ml-20"
      type="primary"
      loading={addLoading || editLoading}
      onClick={() => onOk()}
    >
      确定
    </Button>,
  ];

  // 获取所有事业部
  const { run: departmentRun, loading: departmentLoading } = useRequest(getAllDeptList, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        const { result } = res;
        setDepartmentId(result);
      });
    },
  });

  useEffect(() => {
    departmentRun({});
    if (id) {
      detailRun({ id: id as string });
    }
  }, []);

  // 获取人员接口
  const { onPopupScroll, onSearch, onChange, list, loading } = usePerson(2000, () => {
    setChangeValue(true);
  });

  return (
    <Modal
      className={classNames('select-user-modal')}
      title={id ? '编辑项目组' : '新增项目组'}
      width={510}
      {...rest}
      footer={footer}
      onOk={onOk}
      maskClosable={false}
      // visible={visible}
      onClose={handleCancel}
    >
      <div>
        <Form labelAlign="right" labelCol={{ span: 6 }} wrapperCol={{ span: 17 }}>
          <Form.Item className="mb-0" label="所属事业部">
            {getFieldDecorator('department', {
              rules: [
                {
                  required: true,
                  message: '请选择所属事业部',
                },
              ],
            })(
              <Select
                style={{ width: 300 }}
                labelInValue={true}
                placeholder="请选择所属事业部"
                loading={departmentLoading}
                onChange={() => {
                  setChangeValue(true);
                }}
              >
                {departmentList.map((item) => (
                  <Select.Option key={item.id} value={item.id}>
                    {item.deptName}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="平台">
            {getFieldDecorator('platformEnum', {
              rules: [
                {
                  required: true,
                  message: '请选择平台',
                },
              ],
            })(
              <Select style={{ width: 300 }}>
                <Select.Option value={PLATFORM_ENUM.ALL}>全部</Select.Option>
                {OmitJDPlantformList.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="项目组名称">
            {getFieldDecorator('projectGroupName', {
              rules: [
                {
                  required: true,
                  message: '请输入项目组名称',
                },
              ],
            })(
              <Input
                style={{ width: 300 }}
                onChange={() => {
                  setChangeValue(true);
                }}
                placeholder="请输入项目组名称"
                maxLength={20}
              />,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="选品组长">
            {getFieldDecorator('SELECTION_LEADER', {
              initialValue: [],
            })(
              <Select
                allowClear
                style={{ width: 300 }}
                mode="multiple"
                showSearch
                placeholder="请选择"
                loading={loading}
                defaultActiveFirstOption={false}
                showArrow={false}
                filterOption={false}
                onSearch={onSearch}
                onPopupScroll={onPopupScroll}
                onChange={onChange}
                labelInValue={true}
                onBlur={() => onChange('')}
                maxTagCount={15}
                optionLabelProp="label"
              >
                {list.map((item: any) => (
                  <Select.Option
                    label={item.employeeName}
                    key={item.employeeId}
                    value={item.employeeId}
                  >
                    <span>
                      {item.employeeName}-{item.employeeMobile}
                    </span>
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="选品">
            {getFieldDecorator('SELECTION', {
              initialValue: [],
            })(
              <Select
                allowClear
                mode="multiple"
                showSearch
                style={{ width: 300 }}
                placeholder="请选择"
                loading={loading}
                defaultActiveFirstOption={false}
                showArrow={false}
                filterOption={false}
                onSearch={onSearch}
                onPopupScroll={onPopupScroll}
                onChange={onChange}
                labelInValue={true}
                onBlur={() => onChange('')}
                maxTagCount={15}
                optionLabelProp="label"
              >
                {list.map((item: any) => (
                  <Select.Option
                    label={item.employeeName}
                    key={item.employeeId}
                    value={item.employeeId}
                  >
                    <span>
                      {item.employeeName}-{item.employeeMobile}
                    </span>
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="商务组长">
            {getFieldDecorator('BUSINESS_LEADER', {
              initialValue: [],
            })(
              <Select
                allowClear
                mode="multiple"
                showSearch
                style={{ width: 300 }}
                placeholder="请选择"
                loading={loading}
                defaultActiveFirstOption={false}
                showArrow={false}
                filterOption={false}
                onSearch={onSearch}
                onPopupScroll={onPopupScroll}
                onChange={onChange}
                labelInValue={true}
                onBlur={() => onChange('')}
                maxTagCount={15}
                optionLabelProp="label"
              >
                {list.map((item: any) => (
                  <Select.Option
                    label={item.employeeName}
                    key={item.employeeId}
                    value={item.employeeId}
                  >
                    <span>
                      {item.employeeName}-{item.employeeMobile}
                    </span>
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="商务">
            {getFieldDecorator('BUSINESS', {
              initialValue: [],
            })(
              <Select
                allowClear
                mode="multiple"
                style={{ width: 300 }}
                showSearch
                placeholder="请选择"
                loading={loading}
                defaultActiveFirstOption={false}
                showArrow={false}
                filterOption={false}
                onSearch={onSearch}
                onPopupScroll={onPopupScroll}
                onChange={onChange}
                labelInValue={true}
                onBlur={() => onChange('')}
                maxTagCount={15}
                optionLabelProp="label"
              >
                {list.map((item: any) => (
                  <Select.Option
                    label={item.employeeName}
                    key={item.employeeId}
                    value={item.employeeId}
                  >
                    <span>
                      {item.employeeName}-{item.employeeMobile}
                    </span>
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default WithToggleModal(Form.create()(AddGroup));
