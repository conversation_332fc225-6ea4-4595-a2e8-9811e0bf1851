import React, { useEffect, useState } from 'react';

import { DrawerProps } from 'antd/es/drawer';

import { Descriptions, Table, Spin } from 'antd';
import { detailColumns } from '../dataSource';
import { projectGroupDetail, operateLog } from '@/services/yml/live-room-manage';
import { useRequest } from 'ahooks';
import { handleResponse } from '@/utils/response';
import { status_map, STATUS_ENUM } from '../types';
import moment from 'moment';
import { PageLayout } from 'web-common-modules/components';
import {
  DetailContentLayout,
  DetailTitle,
  SpinCard,
  Card,
  Title,
} from '@/components/DetailFormCompoments';
import {
  DetailContextBox,
  DetailContentItem,
} from '@/components/DetailFormCompoments/DetailContentItem';
import { getQueryParams } from 'web-common-modules/utils/params';
import { PlantformName, PLATFORM_ENUM } from '../../../../../web_modules/types';

const Item = DetailContentItem;

interface PRPOS extends DrawerProps {
  id?: number | string;
}

const Detail: React.FC<PRPOS> = (props) => {
  // const { id, ...rest } = props;
  const id = getQueryParams()?.id;
  const [dataSource, setDataSource] = useState([{}]);
  const [detail, setDetail] = useState<any>(null);
  const { run: operateLogRun, loading: operateLogLoading } = useRequest(operateLog, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        console.log('🚀 ~ file: Detail.tsx:27 ~ handleResponse ~ res:', res);
        const { result } = res;
        setDataSource(result || []);
      });
    },
  });
  const { run: detailRun, loading: detailLoading } = useRequest(projectGroupDetail, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        const {
          departmentId,
          departmentName,
          employInfoMap,
          projectGroupName,
          projectGroupStatusEnum,
          ...value
        } = res.result;
        setDetail({
          ...value,
          departmentId,
          departmentName,
          projectGroupName,
          projectGroupStatusEnum,
          SELECTION_LEADER: formatName(employInfoMap?.SELECTION_LEADER as any),
          SELECTION: formatName(employInfoMap?.SELECTION as any),
          BUSINESS_LEADER: formatName(employInfoMap?.BUSINESS_LEADER as any),
          BUSINESS: formatName(employInfoMap?.BUSINESS as any),
        });
      });
    },
  });
  const formatName = (list = []) => {
    const nameList = list.map((item: any) => item.employName);
    return nameList.length ? nameList.toString() : '-';
  };

  useEffect(() => {
    detailRun({ id });
    operateLogRun({ id });
  }, []);
  return (
    <PageLayout>
      <DetailContentLayout>
        <DetailTitle titleText={'项目组详情'}></DetailTitle>
        <SpinCard spinning={detailLoading}>
          <Card>
            <Title>项目组信息</Title>
            <DetailContextBox>
              <Item label="所属事业部">
                <p>{detail?.departmentName || '-'}</p>
              </Item>
              <Item label="平台">
                <p>{PlantformName[detail?.platformEnum as PLATFORM_ENUM] || '-'}</p>
              </Item>
              <Item label="项目组名称">
                <p>{detail?.projectGroupName || '-'}</p>
              </Item>
              <Item label="选品组长">
                <p>{detail?.SELECTION_LEADER || '-'}</p>
              </Item>
              <Item label="选品">
                <p>{detail?.SELECTION || '-'}</p>
              </Item>
              <Item label="商务组长">
                <p>{detail?.BUSINESS_LEADER || '-'}</p>
              </Item>
              <Item label="商务">
                <p>{detail?.BUSINESS || '-'}</p>
              </Item>
              <Item label="状态">
                <p>
                  {detail?.projectGroupStatusEnum
                    ? status_map[detail?.projectGroupStatusEnum as STATUS_ENUM]
                    : '-'}
                </p>
              </Item>
              <Item label="创建时间">
                <p>
                  {detail?.gmtCreated
                    ? moment(detail?.gmtCreated).format('YYYY-MM- HH:mm:ss')
                    : '-'}
                </p>
              </Item>
              <Item label="修改时间">
                <p>
                  {detail?.gmtModified
                    ? moment(detail?.gmtModified).format('YYYY-MM- HH:mm:ss')
                    : '-'}
                </p>
              </Item>
            </DetailContextBox>
          </Card>
          <Card>
            <Title>操作日志</Title>
            <Table
              dataSource={dataSource}
              columns={detailColumns()}
              loading={operateLogLoading}
              pagination={false}
            ></Table>
          </Card>
        </SpinCard>
      </DetailContentLayout>
    </PageLayout>
  );
};

export default Detail;
