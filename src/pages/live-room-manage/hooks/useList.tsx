import { useRequest } from 'ahooks';
import { pageQuery } from '@/pages/system-code/services/yml';
import { pageParameterQuery } from '@/pages/system-parameter/services/yml';
import {
  queryApprovalManagementList,
  queryApprovalManagementRelationList,
  projectGroupList,
} from '@/services/yml/live-room-manage';
import { useEffect, useState } from 'react';
import usePagination from '@/hooks/usePagination';
import { selectionProcessKanbanPage } from '../../selection-flow-board/services/yml';
import { handleResponse } from '@/utils/response';
import { qualificationTypePage } from '@/services/yml/legal-audit-type';
import { qualificationRulePage } from '@/services/yml/legal-audit-rules';
import { pageQueryList } from '../../report-sheet/services/yml';
import { liveRoomList } from '../../open-off-live/Services/yml';
import { captainInfoPage, captainInfoActivityGoods } from '../../dy-head-plan/services/yml/index';
import { getPromotionLinkListApi } from '../../dy-head-link/services/yml';
import { guaranteedPageQuery } from '@/services/yml/quality-assurance-cooperation';
import { liveGoodsBoardSourcePageQuery } from '@/pages/goOnBoard/services';
import { legalWhitelistPage } from '@/pages/qualification-whitelist/services/yml';
// import { supplierQualificationPage } from '../../certification-audit/service';
import { automatedAuditRulesPage } from '../../qualification-audit-result/automated-audit-rules/business-automated-audit-rules/service';
import { goodsAutomatedAuditRulesPage } from '@/pages/qualification-audit-result/automated-audit-rules/goods-automated-audit-rules/services';
import {
  supplierQualificationPage,
  brandQualificationPage,
  goodsQualificationPage,
} from '../../certification-audit/service';
import { brandAutomatedAuditRulesPage } from '@/pages/qualification-audit-result/automated-audit-rules/brand-automated-audit-rules/service';
import { qualityInspectionRulePageQuery } from '@/pages/quality-test-rules/services/yml';
import { qualityInspectionTaskPageQuery } from '@/pages/quality-inspection-task/services/yml';
import { spuBrandPage } from '@/pages/goods-brand-store/services/index';
import { blackShopPage } from '@/pages/black-list/services';
import { employeePage } from '@/pages/setting/employee-list/fetchApi';
import { financePageList } from '@/pages/expend-contract/services';
import { qualificationReviewTaskPage } from '@/pages/review-task/services/yml';
import { spokenScriptAuditPage } from '@/pages/mouth-audit/service';
import { selectionRoundQuotaConfigPage } from '@/pages/session-limit-config/services';
import { shopBrandQualificationPage } from '@/pages/shop-brand-qualification/services';
import { sensitiveWordsConfigPageQuery } from '@/pages/sensitive-words-data/services';
import { spuSensitivePage } from '@/pages/setting/qualification-whitelist-category-rules/services';
import { bpStatistics } from '@/pages/choiceList-data-board/services/index';
import { specialAuditExclusiveQuotaAccountList } from '@/pages/complianceStar/services';
import { releaseRatePage } from '@/pages/choiceList-data-board/QuotaRelease/services';
import { luxuryBrandApprovalRecordPage } from '@/pages/compliance-audit-logs/services/yml/index';
import { promptConfigurationPage } from '@/pages/prompt-word-config/services';
import { specialAuditQuotaWarnNoticeConfigPage } from '@/pages/setting/special-approval-quota/services';
import { businessOpportunitySessionRelPage } from '@/pages/opportunity-management/services';
import { selectionProcessKanbanBusinessOpportunityPage } from '@/pages/opportunity-management/services';
import { businessOpportunitySelectionRoundRelPage } from '@/pages/opportunity-management/services';
import {
  workOrderCountCustomerComplaint,
  workOrderCountQuestionClassification,
  workOrderCountClose,
  workOrderCountCustomerService,
  workOrderCountItem,
  workOrderCountShop,
} from '@/pages/ticket-data-board/services';
import { getAttendanceList } from '@/pages/attendance-manage/services';
import { businessOpportunityLeadPage } from '@/pages/business-leads/service';

export enum APIKEY {
  SYSTEM_PARAMETER = 'system-parameter',
  SYSTEM_CODE = 'system_code',
  SELECTION_FLOW_BOARD = 'selection_flow_board',
  APPROVAL = 'approval',
  CONFIG_APPROVAL = 'config_approval',
  PROJECT_TEAM = 'project_team',
  AUDIT_TYPE = 'audit-type',
  AUDIT_RULES = 'audit-rules',
  APPLY_BILL = 'apply_bill',
  OPEN_OFF_LIVE = 'open_off_live',
  DY_HEAD_PLAN = 'dy_head_plan',
  DY_HEAD_PLAN_DETAIL = 'dy_head_plan_detail',
  DY_HEAD_LINK = 'dy_head_link',
  QUALITY_COOP = 'quality_coop',
  LIVE_GOODS_BOARD = 'live_goods_board',
  LEGAL_WHITE = 'legal_white',
  AUTOMATED_AUDIT_RULES = 'automated_audit_rules',
  CERTIFICATION_AUDIT_LIST = 'certification_audit_list',
  BRAND_AUTOMATED_AUDIT_RULES_LIST = 'brand_automated_audit_rules_page',
  QUALITY_TEST_RULES = 'quality_test_rules',
  QUALITY_INSPECTION_TASK = 'quality_inspection_task',
  GOODS_BRAND_STORE = 'goods_brand_store',
  BLACK_SHOP_LIST = 'black_shop_list',
  BRAND_QUALIFICATION_AUDIT = 'brand_qualification_audit',
  EMPLOYEE_PAGE = 'employee_page',
  CONTRACTS_INFO = 'contracts_info',
  REVIEW_TASK = 'REVIEW_TASK',
  SPOKEN_SCRIPT_AUDIT_PAGE = 'spoken_script_audit_page',
  SELECTION_ROUND_QUOTA_CONFIG = 'selection_round_quota_config',
  GOODS_AUTOMATED_AUDIT_RULES_PAGE = 'goods_automated_audit_rules_page',
  SHOP_BRAND_QUALIFICATION = 'shop_brand_qualification',
  SENSITIVE_WORDS_DATA = 'sensitive_words_data',
  SPU_SENSITIVE_PAGE = 'spu_sensitive_page',
  GOODS_QUALIFICATION = 'goods_qualification',
  PASS_RATE = 'pass_rate',
  COMPLIANCE_STAR_LIST = 'COMPLIANCE_STAR_LIST',
  QUOTA_RELEASE = 'QUOTA_RELEASE',
  LUXURY_BRAND_APPROVAL_RECORD_PAGE = 'LUXURY_BRAND_APPROVAL_RECORD_PAGE',
  PROMPT_WORD_CONFIG = 'prompt_word_config',
  ALERT_NOTICE_CONFIG = 'alert_notice_config',
  BUSINESS_OPPORTUNITY_SESSION_REL_PAGE = 'business_opportunity_session_rel_page',
  SELECTION_PROCESS_KANBAN_LIVE_ROOM_LIST = 'selection_process_kanban_live_room_list',
  BUSINESS_OPPORTUNITY_SELECTION_ROUND_REL_PAGE = 'business_opportunity_selection_round_rel_page',
  WORK_ORDER_COUNT_CUSTOMER_COMPLAINT = 'WORK_ORDER_COUNT_CUSTOMER_COMPLAINT',
  WORK_ORDER_COUNT_QUESTION_CLASSIFICATION = 'WORK_ORDER_COUNT_QUESTION_CLASSIFICATION',
  WORK_ORDER_COUNT_CLOSE = 'WORK_ORDER_COUNT_CLOSE',
  WORK_ORDER_COUNT_CUSTOMER_SERVICE = 'WORK_ORDER_COUNT_CUSTOMER_SERVICE',
  WORK_ORDER_COUNT_ITEM = 'WORK_ORDER_COUNT_ITEM',
  WORK_ORDER_COUNT_SHOP = 'WORK_ORDER_COUNT_SHOP',
  EMPLOYEE_ATTENDANCE = 'EMPLOYEE_ATTENDANCE',
  BUSINESS_OPPORTUNITY_LEAD_PAGE = 'BUSINESS_OPPORTUNITY_LEAD_PAGE',
}

const apiMap = {
  [APIKEY.SYSTEM_PARAMETER]: pageParameterQuery,
  [APIKEY.SYSTEM_CODE]: pageQuery,
  [APIKEY.SELECTION_FLOW_BOARD]: selectionProcessKanbanPage,
  [APIKEY.APPROVAL]: queryApprovalManagementList,
  [APIKEY.CONFIG_APPROVAL]: queryApprovalManagementRelationList,
  [APIKEY.PROJECT_TEAM]: projectGroupList,
  [APIKEY.AUDIT_TYPE]: qualificationTypePage,
  [APIKEY.AUDIT_RULES]: qualificationRulePage,
  [APIKEY.APPLY_BILL]: pageQueryList,
  [APIKEY.OPEN_OFF_LIVE]: liveRoomList,
  [APIKEY.DY_HEAD_PLAN]: captainInfoPage,
  [APIKEY.DY_HEAD_PLAN_DETAIL]: captainInfoActivityGoods,
  [APIKEY.DY_HEAD_LINK]: getPromotionLinkListApi,
  [APIKEY.QUALITY_COOP]: guaranteedPageQuery,
  [APIKEY.LIVE_GOODS_BOARD]: liveGoodsBoardSourcePageQuery,
  [APIKEY.LEGAL_WHITE]: legalWhitelistPage,
  [APIKEY.AUTOMATED_AUDIT_RULES]: automatedAuditRulesPage,
  [APIKEY.CERTIFICATION_AUDIT_LIST]: supplierQualificationPage,
  [APIKEY.BRAND_AUTOMATED_AUDIT_RULES_LIST]: brandAutomatedAuditRulesPage,
  [APIKEY.QUALITY_TEST_RULES]: qualityInspectionRulePageQuery,
  [APIKEY.QUALITY_INSPECTION_TASK]: qualityInspectionTaskPageQuery,
  [APIKEY.GOODS_BRAND_STORE]: spuBrandPage,
  [APIKEY.BLACK_SHOP_LIST]: blackShopPage,
  [APIKEY.BRAND_QUALIFICATION_AUDIT]: brandQualificationPage,
  [APIKEY.EMPLOYEE_PAGE]: employeePage,
  [APIKEY.CONTRACTS_INFO]: financePageList,
  [APIKEY.REVIEW_TASK]: qualificationReviewTaskPage,
  [APIKEY.SPOKEN_SCRIPT_AUDIT_PAGE]: spokenScriptAuditPage,
  [APIKEY.SELECTION_ROUND_QUOTA_CONFIG]: selectionRoundQuotaConfigPage,
  [APIKEY.GOODS_AUTOMATED_AUDIT_RULES_PAGE]: goodsAutomatedAuditRulesPage,
  [APIKEY.SHOP_BRAND_QUALIFICATION]: shopBrandQualificationPage,
  [APIKEY.SENSITIVE_WORDS_DATA]: sensitiveWordsConfigPageQuery,
  [APIKEY.SPU_SENSITIVE_PAGE]: spuSensitivePage,
  [APIKEY.GOODS_QUALIFICATION]: goodsQualificationPage,
  [APIKEY.PASS_RATE]: bpStatistics,
  [APIKEY.COMPLIANCE_STAR_LIST]: specialAuditExclusiveQuotaAccountList,
  [APIKEY.QUOTA_RELEASE]: releaseRatePage,
  [APIKEY.LUXURY_BRAND_APPROVAL_RECORD_PAGE]: luxuryBrandApprovalRecordPage,
  [APIKEY.PROMPT_WORD_CONFIG]: promptConfigurationPage,
  [APIKEY.ALERT_NOTICE_CONFIG]: specialAuditQuotaWarnNoticeConfigPage,
  [APIKEY.BUSINESS_OPPORTUNITY_SESSION_REL_PAGE]: businessOpportunitySessionRelPage,
  [APIKEY.SELECTION_PROCESS_KANBAN_LIVE_ROOM_LIST]: selectionProcessKanbanBusinessOpportunityPage,
  [APIKEY.BUSINESS_OPPORTUNITY_SELECTION_ROUND_REL_PAGE]: businessOpportunitySelectionRoundRelPage,
  [APIKEY.WORK_ORDER_COUNT_CUSTOMER_COMPLAINT]: workOrderCountCustomerComplaint,
  [APIKEY.WORK_ORDER_COUNT_QUESTION_CLASSIFICATION]: workOrderCountQuestionClassification,
  [APIKEY.WORK_ORDER_COUNT_CLOSE]: workOrderCountClose,
  [APIKEY.WORK_ORDER_COUNT_CUSTOMER_SERVICE]: workOrderCountCustomerService,
  [APIKEY.WORK_ORDER_COUNT_ITEM]: workOrderCountItem,
  [APIKEY.WORK_ORDER_COUNT_SHOP]: workOrderCountShop,
  [APIKEY.EMPLOYEE_ATTENDANCE]: getAttendanceList,
  [APIKEY.BUSINESS_OPPORTUNITY_LEAD_PAGE]: businessOpportunityLeadPage,
};

export const useList = <Req, Res>(
  type: APIKEY,
  formatList: undefined | ((value: any) => any) = undefined,
  size?: number,
  subCurrent?: boolean,
) => {
  // 列表数据
  const [dataSource, setDataSource] = useState<Res>();
  // 保存搜索数据
  const [condition, setCondition] = useState<Req>();
  const { pagination, setPagination } = usePagination({
    current: 1,
    size: size || 20,
  });

  const { loading, run } = useRequest(apiMap[type], {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        const { records = [], total, current, size } = res.result;
        setPagination({
          total,
          current: subCurrent ? current + 1 : current,
          size,
        });
        if (formatList) {
          setDataSource(formatList(records) || []);
          return;
        }
        setDataSource((records as Res) || []);
      });
    },
  });

  // 分页修改
  const onPageChange = (current: number, size: number) => {
    setPagination({ current, size, total: pagination.total });
    run({ ...condition, current: subCurrent ? current - 1 : current, size });
  };

  // 搜索
  const onSearch = (value: Req) => {
    console.log('value', value);
    setCondition(value);
    setPagination({
      current: 1,
      size: pagination.size,
    });
    run({ ...value, current: subCurrent ? 0 : 1, size: pagination.size });
  };

  // 刷新当前页面
  const onRefresh = () => {
    run({
      ...condition,
      current: subCurrent ? pagination.current - 1 : pagination.current,
      size: pagination.size,
    });
  };

  // useEffect(() => {
  //   run({ current: pagination.current, size: pagination.size });
  // }, []);

  return {
    dataSource,
    setDataSource,
    condition,
    // setCondition,
    pagination,
    // setPagination,
    loading,
    // run,
    onPageChange,
    onSearch,
    onRefresh,
  };
};

// 2025-05-15zhouby -> cursor ai结尾共生成10行代码
