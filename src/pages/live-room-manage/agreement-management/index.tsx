import React, { useEffect, useRef, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import PaginationProxy from '@/common/constants/Pagination';
import { SearchForm, AddMap } from './components';
import { options, columns } from './dataSource';
import { AuthWrapper } from 'qmkit';
import { Button, message } from 'antd';
import { Table } from 'antd';
import styles from './index.module.less';
import { agreementList } from '@/services/yml/agreement/index';
import PreviewPDF from '@/components/PreviewPDF';
import { useTableHeight } from '@/common/constants/hooks/index';
const BusinessMapAssistant = () => {
  const [dataSource, setDataSource] = useState([{}]);
  const [loading, setLoading] = useState(false);
  const pdfRef = useRef(null);
  const getList = (value) => {
    // return;
    setLoading(true);
    agreementList({ ...value })
      .then((res) => {
        if (res?.res?.code === '200') {
          const result = res?.res?.result;
          setDataSource(result?.records);
          setPagination({
            total: result.total,
            current: result.current,
            size: result.size,
          });
          setLoading(false);
        } else {
          message.error(res?.res?.message);
        }
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      });
  };
  const [pagination, setPagination] = useState({ current: 1, size: 20, total: 0 });
  const [formData, setFormData] = useState({});
  const onPageChange = (current: number, size: number) => {
    console.log(current, size);
    setPagination({
      ...pagination,
      current: current,
      size: size,
    });
    getList({ ...pagination, ...formData, current, size });
  };
  useEffect(() => {
    getList({ ...pagination, ...formData });
  }, []);
  const [resourceId, setResourceId] = useState();
  const onOpen = (id) => {
    setResourceId(id);
    pdfRef.current && pdfRef.current?.onOpen();
  };
  const { getHeight, tableHeight } = useTableHeight(75);
  return (
    <PageLayout>
      <div className={`${styles.publishFeeContainer} ${styles['publish-fee-page']} vh46Px`}>
        <div className="formHeight">
          <SearchForm
            options={options}
            onSearch={(value) => {
              console.log(value);
              setFormData({ ...formData, ...value });
              getList({ ...pagination, ...formData, ...value });

              //@TODO: 搜索之后获取列表
            }}
          />
          <div>
            {/* f_business_map_assistant_add f_agreement_add */}
            <AuthWrapper functionName="f_agreement_add">
              <AddMap
                getList={() => {
                  getList({ ...pagination, ...formData });
                }}
              >
                <Button type="primary" icon="plus">
                  新建协议
                </Button>
              </AddMap>
            </AuthWrapper>
          </div>
        </div>
        <div className={styles['list-table']} style={{ flex: 1 }}>
          <Table
            className={styles.tabel}
            rowKey={'id'}
            loading={loading}
            pagination={false}
            dataSource={dataSource}
            columns={columns({ onOpen })}
            scroll={{ y: tableHeight, x: '100%' }}
          ></Table>
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <PaginationProxy
            current={pagination?.current}
            pageSize={pagination?.size}
            total={pagination?.total}
            // @ts-ignore
            onChange={onPageChange}
            valueType="flatten"
          />
        </div>
        <PreviewPDF type={'look'} id={resourceId} onRef={pdfRef}></PreviewPDF>
      </div>
    </PageLayout>
  );
};

export default BusinessMapAssistant;
