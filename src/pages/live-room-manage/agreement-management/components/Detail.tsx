import React, { useState, useEffect } from 'react';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import DrawerProxy from '@/components/Drawer';
import WithToggleModal from '@/components/WithToggleModal';
import { DrawerProps } from 'antd/es/drawer';
import { classNames } from '@/utils/moduleUtils';
import { Descriptions, Table, message } from 'antd';
import styles from '../index.module.less';
import { detailColumns } from '../dataSource';
// import { businessInfo } from '@/services/yml/business/sales-details/index';
let businessInfo;
interface PRPOS extends DrawerProps {
  id?: number | string;
}

const Detail: React.FC<PRPOS & WithToggleModalProps> = (props) => {
  const { id, ...rest } = props;
  const [dataSource, setDataSource] = useState([{}]);
  const [info, setInfo] = useState({ deptName: '', assistantName: '', businessInfoList: [] });
  useEffect(() => {
    if (id) {
      businessInfo({ id: id })
        .then((res) => {
          console.log(res);
          if (res?.res?.code === '200') {
            const result = res?.res?.result;
            console.log(result);
            setInfo(result);
            setDataSource(result.operatorLogList);
          } else {
            message.error(res?.res?.message);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, []);
  return (
    <DrawerProxy
      className={classNames('select-user-modal')}
      title="商务映射助理详情"
      width={610}
      {...rest}
      footer={null}
    >
      <div>
        <Descriptions column={2} title="基本信息">
          <Descriptions.Item className="pb-24" label="事业部">
            {info?.deptName}
          </Descriptions.Item>
          <Descriptions.Item className="pb-24" label="商务助理">
            {info?.assistantName}
          </Descriptions.Item>
          <Descriptions.Item className="pb-24" label="商务" span={2}>
            {info?.businessInfoList.map((item) => {
              return item.businessName + '、';
            })}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </DrawerProxy>
  );
};

export default WithToggleModal(Detail);
