import React, { useEffect, useState } from 'react';
import { WithToggleModalProps } from '@/components/WithToggleModal';
import { FormComponentProps } from 'antd/es/form';
import { Form, Select, Input, Button, message, Modal, Alert } from 'antd';
import DrawerProxy from '@/components/Drawer';
import WithToggleModal from '@/components/WithToggleModal';
import { DrawerProps } from 'antd/es/drawer';
import { classNames } from '@/utils/moduleUtils';

import OSSUpload from '@/components/OSSUpload';
import { agetInstitutionList, addAgreementList } from '@/services/yml/agreement/index';
import { debounce } from 'lodash';
// import {
//   businessAdd,
//   businessInfo,
//   businessEdit,
// } from '@/services/yml/business/sales-details/index';
// let businessAdd, businessInfo, businessEdit;
export const styles = {
  box: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  } as any,
  plus: {
    color: '#999',
    fontSize: '28px',
  },
};
const { confirm } = Modal;
interface PRPOS extends DrawerProps, FormComponentProps {
  id?: number | string;
  getList?: () => void;
  reset: () => void;
}

const AddGroup: React.FC<PRPOS & WithToggleModalProps> = (props) => {
  const { id, reset, form, ...rest } = props;
  const { getFieldDecorator } = form;
  const onOk = () => {
    setLoading(true);
    // confirm({
    //   title: '新填写的信息将不会被保存，确认要取消吗？',
    //   content: '',
    //   onOk() {},
    //   onCancel() {
    //     console.log('Cancel');}
    //   }）
    form.validateFields((err, value) => {
      if (!err) {
        const params = {
          agreementType: 0,
          fileName: '',

          promptText: '',
          resourceId: '',
        };

        const arr = value.institutionId.map((i) => {
          return { institutionId: i.key, institutionName: i.label };
        });
        console.log('value', arr);
        params.fileName = imgList[0]?.name;
        params.resourceId = imgList[0]?.resourceId;
        params.promptText = value.promptText;
        params.institutionList = arr;
        const url = addAgreementList;
        url(params)
          .then((res) => {
            setLoading(false);
            if (res?.res?.code === '200') {
              message.success('新增成功');
              props.getList && props.getList();
              props.onCancel && props.onCancel();
            } else {
              message.error(res?.res?.message);
            }
          })
          .catch((err) => {
            console.log('新增错误', err);
            setLoading(false);
          });
      } else {
        setLoading(false);
      }
    });
  };
  const onCancel = () => {
    const value = form.getFieldsValue();
    let show = false;
    Object.values(value).forEach((i) => {
      if (i) {
        show = true;
      }
    });
    if (show) {
      confirm({
        title: '新填写的信息将不会被保存，确认要取消吗？',
        content: '',
        onOk() {
          props.onCancel && props.onCancel();
        },
        onCancel() {},
      });
    } else {
      props.onCancel && props.onCancel();
    }
  };

  const [options, setOptions] = useState([]);
  const getAgetInstitutionList = () => {
    agetInstitutionList({}).then((res: any) => {
      if (res?.res?.code === '200') {
        setOptions(res.res.result);
      }
    });
  };
  useEffect(() => {
    getAgetInstitutionList();
  }, []);
  const [loading, setLoading] = useState(false);
  const [imgList, setImgList] = useState([]);
  const footer = [
    <Button key="cancel" className="ml-20" onClick={() => onCancel()}>
      取消
    </Button>,
    <Button
      key="submit"
      className="ml-20"
      type="primary"
      loading={loading}
      onClick={debounce(() => onOk(), 500)}
    >
      确定
    </Button>,
  ];
  return (
    <Modal
      className={classNames('select-user-modal')}
      title={'协议配置'}
      width={500}
      {...rest}
      footer={footer}
      onOk={onOk}
      maskClosable={false}
      confirmLoading={loading}
      // visible={visible}
      // onClose={props.onCancel}
    >
      <div>
        <Form labelAlign="right" labelCol={{ span: 6 }} wrapperCol={{ span: 17 }}>
          <Form.Item className="mb-0" label="机构主体">
            {getFieldDecorator('institutionId', {
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Select
                disabled={id ? true : false}
                mode={'multiple'}
                showSearch
                style={{ width: 300 }}
                placeholder="请选择"
                labelInValue={true}
              >
                {options.map((item: any) => {
                  return (
                    <Select.Option value={item?.institutionId}>
                      {item?.institutionName}
                    </Select.Option>
                  );
                })}
              </Select>,
            )}
          </Form.Item>

          <Form.Item className="mb-0" label="协议类型">
            新媒体营销服务协议
          </Form.Item>
          <Form.Item className="mb-0" label="引导提示语">
            {getFieldDecorator('promptText', {
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <Input.TextArea
                autoSize={{ minRows: 1, maxRows: 10 }}
                placeholder="引导提示语句会在协议弹出时展示出，请认真填写"
                maxLength={200}
                style={{ width: 300 }}
                allowClear
              ></Input.TextArea>,
            )}
          </Form.Item>
          <Form.Item className="mb-0" label="合同协议">
            {getFieldDecorator('resourceId', {
              rules: [
                {
                  required: true,
                  message: '请选择',
                },
              ],
            })(
              <OSSUpload
                dataSource={imgList}
                maxLen={1}
                typeCode="SPU_IMG"
                onChange={(list) => {
                  // console.log('list', list);
                  setImgList(list);
                }}
                maxSize={10 * 1024 * 1024}
                accept={'.pdf'}
                isImage
              ></OSSUpload>,
            )}
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default WithToggleModal(Form.create()(AddGroup));
