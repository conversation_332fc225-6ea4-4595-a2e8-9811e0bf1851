import { useSetState } from 'ahooks';
import { Button, message, Modal, Spin } from 'antd';
import Form, { FormComponentProps } from 'antd/lib/form';
import React, { useEffect, useMemo, useState } from 'react';
import CreateForm from './CreateForm';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  getCompanyAdd,
  getCompanyDetail,
  GetCompanyDetailResult,
  getCompanyEdit,
} from '../../services';
import CreateList from './CreateList';
import PageLayout from '@/components/PageLayout';
import { Card, FormBottomCard, FormContentLayout } from '@/components/DetailFormCompoments';
import { getQueryParams } from '@/pages/anchor-information/utils/utils';
import { useCloseAndJump } from '@/hooks/useCloseAndJumpToPage';
import { history } from 'qmkit';

type PropsType = FormComponentProps<FormType>;
type ModalState = {
  loading?: boolean;
  detail?: GetCompanyDetailResult;
};
type FormType = {
  companyAddress?: string /*公司地址*/;
  corporateHierarchy?: number /*公司层级（1=主公司，2=分公司）*/;
  isLiveAgency?: number /*是否直播机构（1=是否，0=否）*/;
  // liveAgencyId?:string/*直播机构id*/,
  liveAgencyInfo?: { key: string; label: string } | undefined;
  name?: string /*公司名称*/;
  // pid?:string/*上级公司id*/,
  pidInfo?: { key: string; label: string } | undefined;
  remark?: string /*备注*/;
  taxRate?: string /*税率（单位%）*/;
  uscc?: string /*统一社会信用代码*/;
  addBankAccountList?: any[] /*新增银行账户列表*/;
};
const CreateModal: React.FC<PropsType> = ({ form }) => {
  const { delRoutetag } = useCloseAndJump();

  const [modalState, setState] = useSetState<ModalState>({ loading: false });
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedKeys, setSelecteKeys] = useState([]);

  const id = useMemo(() => getQueryParams().id, []);
  const type = useMemo(() => getQueryParams().type as 'edit' | 'create', []);
  const getDetail = async () => {
    setState((state) => ({ ...state, loading: true }));
    const result = await responseWithResultAsync({
      request: getCompanyDetail,
      params: { id },
    });
    setState((state) => ({ ...state, loading: false }));
    return result;
  };

  const handleEditShow = async () => {
    const detail = await getDetail();
    if (detail) {
      setState((state) => ({ ...state, detail }));
      initForm(detail);
      setSelecteKeys([]);
    }
  };
  const handleCancel = () => {
    setState((state) => ({ ...state, visible: false }));
    form.resetFields();
    delRoutetag();
    history.goBack();
  };
  const handleCreate = async (values: FormType) => {
    setState((state) => ({ ...state, loading: true }));
    const params = {
      ...values,
      taxRate: values?.taxRate?.toFixed(2),
      liveAgencyId: values?.liveAgencyInfo?.key,
      liveAgencyName: values?.liveAgencyInfo?.label,
      pid: values?.pidInfo?.key,
      parentName: values?.pidInfo?.label,
    };
    const result = await responseWithResultAsync({
      request: getCompanyAdd,
      params,
    });
    setState((state) => ({ ...state, loading: false }));
    return result;
  };
  const handleEdit = async (values: FormType) => {
    setState((state) => ({ ...state, loading: true }));
    const bankList = [];
    dataSource?.map((item: any, index: number) => {
      if (item.id.includes('.')) {
        bankList.push({ ...values.addBankAccountList[index] });
      }
    });
    const params = {
      ...values,
      taxRate: values?.taxRate?.toFixed(2),
      liveAgencyId: values?.liveAgencyInfo?.key,
      liveAgencyName: values?.liveAgencyInfo?.label,
      pid: values?.pidInfo?.key,
      parentName: values?.pidInfo?.label,
      addBankAccountList: bankList,
      id,
    };
    const result = await responseWithResultAsync({
      request: getCompanyEdit,
      params,
    });
    setState((state) => ({ ...state, loading: false }));
    return result;
  };
  const handleOk = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const result = type === 'create' ? await handleCreate(values) : await handleEdit(values);
      if (result) {
        message.success('操作成功');
        handleCancel();
      }
    });
  };
  const initForm = (detail: GetCompanyDetailResult) => {
    setState((state) => ({ ...state, detail: detail }));
    form.setFieldsValue({
      ...detail,
      pidInfo: { key: detail?.pid, label: detail?.parentName },
      // liveAgencyInfo: { key: detail?.liveAgencyId, label: detail?.liveAgencyName },
    });
    // 回显时由于直播机构名称是隐藏的直接赋值会找不到元素，加延时赋值
    if (detail?.isLiveAgency && detail?.liveAgencyId) {
      setTimeout(() => {
        form.setFieldsValue({
          liveAgencyInfo: { key: detail?.liveAgencyId, label: detail?.liveAgencyName },
        });
      }, 300);
    }
    // 给列表赋值
    setDataSource(detail?.bankAccountVOList);
  };
  useEffect(() => {
    if (type === 'edit' && id) {
      handleEditShow();
    }
  }, [type, id]);
  return (
    <PageLayout>
      <FormContentLayout>
        <Spin spinning={modalState.loading}>
          <Card title="公司信息">
            <CreateForm form={form} type={type} />
          </Card>
          <Card title="账户信息">
            <CreateList
              form={form}
              type={type}
              dataSource={dataSource}
              setDataSource={setDataSource}
              handleEditShow={handleEditShow}
              selectedKeys={selectedKeys}
              setSelecteKeys={setSelecteKeys}
            />
          </Card>
        </Spin>
        <FormBottomCard>
          <Button type="primary" onClick={handleOk}>
            保存
          </Button>
          <Button onClick={handleCancel}>取消</Button>
        </FormBottomCard>
      </FormContentLayout>
    </PageLayout>
  );
};

export default Form.create<PropsType>()(CreateModal);
