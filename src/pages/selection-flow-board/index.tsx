/*
 * @Author: zhouby
 * @Date: 2024-03-12 11:14:47
 * @LastEditTime: 2025-08-21 14:59:38
 * @Description: 选品流程看板
 */
import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import styles from './index.module.less';
import PageLayout from '@/components/PageLayout';
import style from '../../styles/index.module.less';
import {
  Table,
  Modal,
  Select,
  Icon,
  Tabs,
  Button,
  message,
  Dropdown,
  Menu,
  Popover,
  Form,
} from 'antd';
import { handlePrecision } from '@/common/common';
import {
  SearchForm,
  ExportModal,
  ImportBox,
  BatchResult,
  MiddleIndex,
  ChooseModal,
  AddPayVoucherModal,
  ConfirmPayVoucher,
  MarkVoucher,
  ApproveModal,
  PersonModal,
  ReturnSampleRegister,
  LimitInventory,
  ImportLimitInventoryBox,
  BpModal,
  SpecialApprovalGoods,
  AIConfig,
  AICreate,
  EditLiveStream,
} from './components';
import {
  useSearch,
  useTable,
  useBatch,
  useSingleBtn,
  useList,
  useSpecialApprovalGoods,
} from './hooks';
import download from '@/assets/download.png';
import { AuthWrapper, checkAuth, history, OneAuthWrapper } from 'qmkit';
import { CODE_ENUM, useTableHeight } from '@/common/constants/hooks/index';
import PaginationProxy from '@/common/constants/Pagination';
import { useSetState } from 'ahooks';
import { formatParams } from './utils';
// import { useList, APIKEY } from '../live-room-manage/hooks';
import {
  LiveGoodsInfoResult,
  SelectionProcessKanbanPageRequest,
  SelectionProcessKanbanPageResult,
  liveGoodsInfo,
  nonLiveGoodsInfo,
  auditWithdraw,
  auditWithdrawRequest,
} from './services/yml';
import moment from 'moment';
import GoodsInfoEdit from '@/components/GoodsInfoEdit/index';
import LegalCheckDrawer from '@/pages/choice-list-new/components/LegalCheckDrawer';
// import { ReasonList } from '@/pages/choice-list-new/components/choiceListRight/List/type';
import BatchConfirmModal from './components/BatchConfirmModal';
import BatchHighRiskModal, { ListInfoType } from './components/BatchHighRiskModal';
import { useSettingTable } from '@/common/constants/hooks/index';
import BatchEditOfflineCommission from './components/BatchEditOfflineCommission';
import SelectionClaim from './components/SelectionClaim';
import { useCode } from '@/common/constants/hooks/index';
import BatchUploadSpec from './components/BatchUploadSpec';
import LinkBatchConfirm from './components/LinkBatchConfirm';
import { debounce } from 'lodash';
import LoadingWrapper from '@/components/LoadingWrapper';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import { queryTalkLinkNum, QueryTalkLinkNumRequest } from '../live-calendar/services';
import { useTalkLinkNum } from '../live-calendar/utils/hook';
const { TabPane } = Tabs;

const sortMap = {
  ascend: 'ASC',
  descend: 'DESC',
};

const SelectionFlowBoard = ({ form }: any) => {
  const [tabsValue, setTabsValue] = useState('live');
  const { codeList, codeEnum } = useCode(CODE_ENUM.SELECTION_LABEL);

  const { options, talentList, list, serviceTypeList, groupList } = useSearch(
    tabsValue,
    codeList,
    form,
  );
  const { getHeight, tableHeight } = useTableHeight(60);
  const [tabLoading, setTabLoading] = useState<boolean>(false);
  const {
    dataSource,
    dataSummary,
    pagination,
    onPageChange,
    onSearch,
    onRefresh,
    loading,
    condition,
    unLiveLoading,
  } = useList(tabsValue);

  // console.log('codeList', codeList, codeEnum);
  // 详情和列表中单个按钮操作粘贴之前的场次货盘
  // 开始
  const [goodsInfo, setGoodsInfo] = useState({});
  const goodsInfoRef = useRef<{
    onOpen: (key?: string) => void;
    [key: string]: any;
  }>(null);
  const chooseModalRef = useRef<{
    onOpen: (key?: string) => void;
    [key: string]: any;
  }>(null);
  const [detailLoading, setDetailLoading] = useState(false);
  const [addVisible, setAddVisible] = useState(false);
  const [confirrmPayVoucherVisible, setConfirrmPayVoucherVisible] = useState(false);
  const [markVoucherVisible, setMarkVoucherVisible] = useState(false);
  const [ApproveVisible, setApproveVisible] = useState(false);
  // 退样登记
  const [returnSampleRegisterVisible, setReturnSampleRegisterVisible] = useState(false);
  // 限制库存
  const [limitInventoryVisible, setLimitInventoryVisible] = useState(false);
  const openEditDetail = (value: any, type: string) => {
    setDetailLoading(true);
    const api = tabsValue === 'live' ? liveGoodsInfo : nonLiveGoodsInfo;
    api({ id: value.id })
      .then(({ res }) => {
        if (res?.success) {
          setGoodsInfo(res?.result);
          setTimeout(() => {
            if (type === 'edit') {
              goodsInfoRef.current?.onOpen('edit');
            } else if (type === 'info') {
              goodsInfoRef.current?.onOpen();
            } else {
              chooseModalRef.current?.onOpen();
            }
          }, 100);
        }
      })
      .finally(() => {
        setDetailLoading(false);
      });
  };
  const {
    handleOk,
    openSingle,
    singleVisible,
    setSingleVisible,
    roleInfo,
    setRoleInfo,
    reason,
    setReason,
  } = useSingleBtn(setGoodsInfo, goodsInfo, onRefresh);
  const closeAddVisible = () => {
    setAddVisible(false);
  };
  const handleAddModal = useCallback((value: any) => {
    setGoodsInfo(value);
    setAddVisible(true);
  }, []);
  const closeConfirrmPayVoucherVisible = useCallback(() => {
    setConfirrmPayVoucherVisible(false);
  }, []);
  const handleCloseConfirrmPayVoucher = useCallback((value: any) => {
    setGoodsInfo(value);
    setConfirrmPayVoucherVisible(true);
  }, []);
  const closeMarkVoucherVisible = useCallback(() => {
    setMarkVoucherVisible(false);
  }, []);
  const handleOpenMarkVoucher = useCallback((value: any) => {
    setGoodsInfo(value);
    setMarkVoucherVisible(true);
  }, []);
  const closeApproveVisible = useCallback(() => {
    setApproveVisible(false);
  }, []);
  const handleOpenApproveVisible = useCallback((value: any) => {
    setGoodsInfo(value);
    setApproveVisible(true);
  }, []);
  // 退样登记
  const closeReturnSampleRegisterVisible = useCallback(() => {
    setReturnSampleRegisterVisible(false);
  }, []);
  const handleOpenReturnSampleRegister = useCallback((value: any) => {
    setGoodsInfo(value);
    setReturnSampleRegisterVisible(true);
  }, []);
  const handleCancelHighRiskAudit = debounce(async (value: any) => {
    console.log('特批撤回', value);
    try {
      const params: auditWithdrawRequest = {
        id: value.specialAuditId,
      };
      const { res } = await auditWithdraw(params);
      if (res?.code === '200') {
        console.log('特批撤回res', res);
        onSearch(searchParams);
        message.success(res.message);
      } else {
        message.error(res.message);
      }
    } catch (e) {
      console.log('特批撤回接口报错', e);
    }
  }, 300);
  // 限制库存
  const closeLimitInventoryVisible = useCallback(() => {
    setLimitInventoryVisible(false);
  }, []);
  const handleOpenLimitInventory = useCallback((value: any) => {
    setGoodsInfo(value);
    setLimitInventoryVisible(true);
  }, []);

  const handleSearchOld = () => {
    onSearch(searchParams);
  };
  // 结束

  const [legalInfo, setLegalInfo] = useSetState({
    info: {},
    visible: false,
  });

  const { specialApprovalGoodsVisible, setSpecialApprovalGoodsVisible, openSpecialApprovalGoods } =
    useSpecialApprovalGoods();

  const handleOpenSpecialApprovalGoods = (value: any) => {
    openSpecialApprovalGoods();
    setGoodsInfo(value);
  };

  const {
    columns,
    rowSelection,
    selectedKeys,
    selectedFullKeys,
    setSelectedKeys,
    setSelectedFullKeys,
    getLoading,
    droppedProductRepeatLoading,
    clearSelectKeys,
    setSortedInfo,
    sortedInfo,
  } = useTable(
    onRefresh,
    openEditDetail,
    openSingle,
    handleAddModal,
    handleCloseConfirrmPayVoucher,
    handleOpenMarkVoucher,
    handleOpenApproveVisible,
    setLegalInfo,
    tabsValue,
    handleSearchOld,
    codeEnum,
    handleOpenReturnSampleRegister,
    handleOpenLimitInventory,
    handleCancelHighRiskAudit,
    handleOpenSpecialApprovalGoods,
  );
  const [optionsColumn, setOptionsColumn] = useState([]);
  // const [optionsColumnLive, setOptionsColumnLive] = useState([]);
  // const { run: runLive, optionsOrigin: optionsOriginLive } = useSettingTable({
  //   name: 'selection-flow-board-table-live',
  //   options: columns,
  //   getTableHeight: getHeight,
  // });
  const { run, optionsOrigin } = useSettingTable({
    name: tabsValue === 'live' ? 'selection-flow-board-table-live' : 'selection-flow-board-table',
    options: columns,
    getTableHeight: getHeight,
  });
  useEffect(() => {
    setOptionsColumn(optionsOrigin);
  }, [optionsOrigin]);
  useEffect(() => {
    run();
  }, [condition]);
  const [isComponents, setIsComponents] = useState(false);
  const [searchParams, setSearchParams] = useState({});
  const handleSearch = (value: any) => {
    const params = formatParams(value, tabsValue);
    // console.log('🚀 ~ handleSearch ~ params:', params);
    onSearch(params);
    setSearchParams(params);
    getNum(params);
  };
  const { batchParams, msgVisible, setMsgVisible, setBatch } = useBatch();
  useEffect(() => {
    // console.log('tabsValue',tabsValue)
    if (tabsValue === 'live') {
      onSearch({
        liveDateStartTime: moment().format('YYYY-MM-DD'),
        liveDateEndTime: moment().add(30, 'day').format('YYYY-MM-DD'),
      });
    } else {
      onSearch({});
    }
    run();
  }, [tabsValue]);

  const onSelectRefresh = () => {
    setSelectedKeys([]);
    setSelectedFullKeys([]);
    onRefresh();
  };
  const { codeList: ReasonList, getEnumList } = useCode(CODE_ENUM.DROP_PRODUCT_REASON, {
    wait: true,
    able: true,
  });
  useEffect(() => {
    singleVisible && getEnumList();
  }, [singleVisible]);
  useEffect(() => {
    if (loading || unLiveLoading) {
      setTabLoading(true);
    } else {
      setTimeout(() => {
        setTabLoading(false);
      }, 300);
    }
  }, [loading, unLiveLoading]);

  const AIMenu = (
    <div>
      <div>
        <AuthWrapper functionName="f_selection_flow_board_category_vehicle_configuration">
          <AIConfig onRefresh={onRefresh}>
            <p style={{ padding: '4px 0px', cursor: 'pointer' }}>AI类目车配置</p>
          </AIConfig>
        </AuthWrapper>
      </div>
      <div>
        <AuthWrapper functionName="f_selection_flow_board_category_vehicle_create">
          <AICreate
            onRefresh={onRefresh}
            type="AI_GENERATE"
            condition={condition}
            // selectedFullKeys={selectedFullKeys}
            selectedKeys={selectedKeys}
          >
            <p style={{ padding: '4px 0px', cursor: 'pointer' }}>AI生成类目车</p>
          </AICreate>
        </AuthWrapper>
      </div>
    </div>
  );
  const talkLinkCondition = useMemo(
    () => ({
      ...condition,
      platformSource: talentList?.find((item) => item.id === condition?.liveRoomId?.[0])?.platform,
    }),
    [condition, talentList],
  );
  const { talkLinkNum, showLinkNumAndTalkNum, getNum } = useTalkLinkNum(talkLinkCondition);

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    console.log('🚀 ~ handleTableChange ~ sorter:', sorter);
    setSortedInfo(sorter);
    run();
    onSearch({
      ...condition,
      sortField: sorter?.order ? 'PLATFORM_SHOP_NAME' : undefined,
      sortType: sorter?.order ? sortMap[sorter?.order] : undefined,
    });
  };

  return (
    <PageLayout className={styles.publishFeeManageContainer} routePath="/selection-flow-board">
      <div
        className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
        style={{ display: 'flex', flexDirection: 'column', paddingTop: '0px' }}
      >
        <div className="formHeight" style={{ paddingTop: '0px' }}>
          <LoadingWrapper loading={tabLoading}>
            <Tabs
              defaultActiveKey={tabsValue}
              style={{ paddingBottom: '16px' }}
              onChange={(e: any) => {
                setTabLoading(true);
                setTabsValue(e);
                setSelectedKeys([]);
                setSelectedFullKeys([]);
                setSortedInfo(undefined);
                // onRefresh();
              }}
              animated={false}
            >
              <TabPane tab="直播流程" key="live"></TabPane>
              <TabPane tab="非直播流程" key="unlive"></TabPane>
            </Tabs>
          </LoadingWrapper>
          <SearchForm
            options={options}
            onSearch={handleSearch}
            getTableHeight={getHeight}
            tabsValue={tabsValue}
            clearSelectKeys={clearSelectKeys}
            codeList={codeList}
            talentList={talentList}
            list={list}
            serviceTypeList={serviceTypeList}
            groupList={groupList}
            setSortedInfo={setSortedInfo}
            sortedInfo={sortedInfo}
            form={form}
          ></SearchForm>
          <div className={style.btnGroup} style={{ marginBottom: '16px' }}>
            <AuthWrapper functionName="f_selection_flow_board_export">
              <ExportModal condition={condition} ids={selectedKeys} tabsValue={tabsValue}>
                <Button className="mr-8 mb-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  <img
                    src={download}
                    style={{
                      width: '14px',
                      height: '14px',
                      verticalAlign: 'sub',
                      marginRight: '6px',
                      marginTop: '-2px',
                    }}
                  />
                  <span>导出</span>
                </Button>
              </ExportModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_selection_flow_board_export_logs">
              <Button
                className="mr-8 mb-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={() => {
                  history.push(
                    `/export-list-selected-goods-all-flow-board?configCode=${
                      tabsValue === 'live' ? 'FLOW_BOARD_EXPORT' : 'NON_LIVE_SELECTION_ROUND_EXPORT'
                    }`,
                  );
                }}
              >
                <span>导出记录</span>
              </Button>
            </AuthWrapper>
            <ImportBox onRefresh={onRefresh} tabsValue={tabsValue}></ImportBox>
            {tabsValue === 'live' ? (
              <>
                <AuthWrapper functionName="f_selection_flow_board_confirm">
                  {/* <Button
                className="mr-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={() => {
                  setIsComponents(false);
                  setBatch('confirm', [...selectedFullKeys]);
                }}
              >
                <span>确认</span>
              </Button> */}
                  <BatchConfirmModal
                    selectedFullKeys={
                      selectedFullKeys as SelectionProcessKanbanPageResult['records']
                    }
                    openSingle={openSingle}
                    onSearch={onRefresh}
                    clearSelectedKeys={clearSelectKeys}
                  />
                </AuthWrapper>
                <AuthWrapper functionName="f_selection_flow_board_cancel">
                  <Button
                    className="mr-8"
                    style={{ borderColor: '#999999', color: '#444444' }}
                    onClick={() => {
                      setIsComponents(false);
                      setBatch('cancel', [...selectedFullKeys]);
                    }}
                  >
                    <span>取消</span>
                  </Button>
                </AuthWrapper>
                {/* 选品通过 */}
                <AuthWrapper functionName="f_selection_flow_board_ch_confirm">
                  <Button
                    className="mr-8"
                    style={{ borderColor: '#999999', color: '#444444' }}
                    onClick={() => {
                      setIsComponents(false);
                      setBatch('chConfirm', [...selectedFullKeys]);
                    }}
                  >
                    <span>通过</span>
                  </Button>
                </AuthWrapper>
                {/* 运营通过 */}
                <AuthWrapper functionName="f_selection_flow_board_op_confirm">
                  <Button
                    className="mr-8"
                    style={{ borderColor: '#999999', color: '#444444' }}
                    onClick={() => {
                      setIsComponents(false);
                      setBatch('opConfirm', [...selectedFullKeys]);
                    }}
                  >
                    <span>通过</span>
                  </Button>
                </AuthWrapper>
                <AuthWrapper functionName="f_selection_flow_board_del">
                  <Button
                    className="mr-8"
                    style={{ borderColor: '#999999', color: '#444444' }}
                    onClick={() => {
                      setIsComponents(true);
                      setBatch('droppedProduct', [...selectedFullKeys]);
                    }}
                  >
                    <span>掉品</span>
                  </Button>
                </AuthWrapper>
                <AuthWrapper functionName="f_selection_flow_board_repeat_drop">
                  <Button
                    className="mr-8"
                    style={{ borderColor: '#999999', color: '#444444' }}
                    onClick={() => {
                      setIsComponents(false);
                      setBatch('repeatDrop', [...selectedFullKeys]);
                    }}
                  >
                    <span>复播</span>
                  </Button>
                </AuthWrapper>
                <AuthWrapper functionName="f_selection_flow_board_person">
                  {selectedFullKeys?.length ? (
                    <PersonModal list={selectedFullKeys} onRefresh={onRefresh}>
                      <Button className="mr-8" style={{ borderColor: '#999999', color: '#444444' }}>
                        <span>人员归属</span>
                      </Button>
                    </PersonModal>
                  ) : (
                    <></>
                  )}
                </AuthWrapper>
                <AuthWrapper functionName="f_batch_edit_offline_commission">
                  {!!selectedFullKeys?.length && (
                    <BatchEditOfflineCommission
                      selectedFullKeys={
                        selectedFullKeys as SelectionProcessKanbanPageResult['records']
                      }
                      onSearch={onRefresh}
                      clearSelectedKeys={clearSelectKeys}
                    />
                  )}
                </AuthWrapper>
                <AuthWrapper functionName="f_selection_flow_board_ch_claim">
                  <SelectionClaim
                    selectedFullKeys={
                      selectedFullKeys as SelectionProcessKanbanPageResult['records']
                    }
                    onSearch={onRefresh}
                    clearSelectedKeys={() => {
                      clearSelectKeys();
                    }}
                  />
                </AuthWrapper>
                {checkAuth('f_selection_flow_board_batch_high_risk_audit') &&
                  (checkAuth('f_selection_flow_board_online_high_risk_audit') ||
                    checkAuth('f_selection_flow_board_high_risk_audit')) && (
                    <BatchHighRiskModal
                      selectedFullKeys={selectedFullKeys as ListInfoType[]}
                      onSearch={onRefresh}
                      clearSelectedKeys={() => {
                        clearSelectKeys();
                      }}
                    />
                  )}
                <AuthWrapper functionName="f_selection_flow_board_supplement_orders">
                  <Button
                    className="mr-8"
                    style={{ borderColor: '#999999', color: '#444444' }}
                    onClick={() => {
                      setIsComponents(false);
                      setBatch('batchSupplementCooperationOrders', [...selectedFullKeys]);
                    }}
                  >
                    <span>补充合作订单</span>
                  </Button>
                </AuthWrapper>
                <LinkBatchConfirm
                  selectedFullKeys={selectedFullKeys as ListInfoType[]}
                  onSearch={onRefresh}
                  clearSelectedKeys={() => {
                    clearSelectKeys();
                  }}
                />
                <ImportLimitInventoryBox
                  onRefresh={onRefresh}
                  tabsValue={tabsValue}
                ></ImportLimitInventoryBox>
                <OneAuthWrapper functionName="f_selection_flow_board_category_vehicle_configuration,f_selection_flow_board_category_vehicle_create">
                  <Popover
                    content={AIMenu}
                    title="AI类目车"
                    trigger="click"
                    overlayStyle={{ zIndex: 999 }}
                  >
                    <Button className="mr-8" style={{ borderColor: '#999999', color: '#444444' }}>
                      AI类目车
                    </Button>
                  </Popover>
                </OneAuthWrapper>
                <EditLiveStream
                  selectedFullKeys={selectedFullKeys as ListInfoType[]}
                  onSearch={onRefresh}
                  clearSelectedKeys={() => {
                    clearSelectKeys();
                  }}
                  selectedKeys={selectedKeys}
                />
              </>
            ) : (
              <>
                <AuthWrapper functionName="f_selection_flow_board_bpBySelf">
                  {selectedFullKeys?.length ? (
                    <BpModal list={selectedFullKeys} onRefresh={onRefresh}>
                      <Button className="mr-8" style={{ borderColor: '#999999', color: '#444444' }}>
                        <span>商务归属</span>
                      </Button>
                    </BpModal>
                  ) : (
                    <></>
                  )}
                </AuthWrapper>
              </>
            )}
            {tabsValue === 'unlive' ? (
              <AuthWrapper functionName="f_selection_flow_board_un_live_del">
                <Button
                  className="mr-8"
                  style={{ borderColor: '#999999', color: '#444444' }}
                  onClick={() => {
                    setIsComponents(false);
                    setBatch('unDroppedProduct', [...selectedFullKeys]);
                  }}
                >
                  <span>掉品</span>
                </Button>
              </AuthWrapper>
            ) : (
              <></>
            )}

            <AuthWrapper functionName="f_selection_up_caimai">
              <BatchUploadSpec
                selectedFullKeys={selectedFullKeys as ListInfoType[]}
                onSearch={onRefresh}
                clearSelectedKeys={() => {
                  clearSelectKeys();
                }}
              />
            </AuthWrapper>
          </div>
        </div>
        <div style={{ flex: 1 }}>
          {optionsColumn.length ? (
            <Table
              columns={optionsColumn}
              pagination={false}
              dataSource={dataSource as any[]}
              rowKey={(record: { [key: string]: any }) => `${record.id}`}
              rowClassName={(record, i) => (i % 2 === 1 ? styles.even : styles.odd)}
              scroll={{ y: tableHeight, x: '100%' }}
              rowSelection={rowSelection}
              loading={
                loading ||
                getLoading ||
                detailLoading ||
                droppedProductRepeatLoading ||
                unLiveLoading
              }
              onChange={handleTableChange}
            />
          ) : null}
        </div>
        <div className={style['pagination-box'] + ' pageHeight'}>
          <PaginationProxy
            {...pagination}
            onChange={({ current, size }: any) => {
              // console.log('🚀 ~ SelectionFlowBoard ~ res:', res);
              onPageChange(current, size);
              clearSelectKeys();
            }}
            valueType="merge"
          />
        </div>

        {tabsValue === 'live' ? (
          <div style={{ position: 'absolute', bottom: '20px', zIndex: '100', display: 'flex' }}>
            <AuthWrapper functionName="f_selection_flow_board_bottom_total">
              <section>
                <p className={styles.totalP}>
                  总计:
                  <span className={styles.totalSpan}>
                    &yen;{handlePrecision(dataSummary?.sumTotal, 2, 1) || 0}
                  </span>
                </p>
                <p className={styles.totalP}>
                  基础服务费合计:
                  <span className={styles.totalSpan}>
                    &yen;{handlePrecision(dataSummary?.brandFeeTotal, 2, 1) || 0}
                  </span>
                </p>
                <p className={styles.totalP}>
                  切片费合计:
                  <span className={styles.totalSpan}>
                    &yen;{handlePrecision(dataSummary?.sectionFeeTotal, 2, 1) || 0}
                  </span>
                </p>
                <p className={styles.totalP}>
                  资源位费用合计:
                  <span className={styles.totalSpan}>
                    &yen;{handlePrecision(dataSummary?.resourceBitCostTotal, 2, 1) || 0}
                  </span>
                </p>
              </section>
            </AuthWrapper>
            {showLinkNumAndTalkNum && (
              <section>
                <p className={styles.totalP}>
                  讲解坑位:
                  <span className={styles.totalSpan}>{talkLinkNum?.talkNum ?? '-'}</span>
                </p>
                <p className={styles.totalP}>
                  挂链坑位:
                  <span className={styles.totalSpan}>{talkLinkNum?.linkNum ?? '-'}</span>
                </p>
              </section>
            )}
          </div>
        ) : (
          <></>
        )}

        <MiddleIndex
          msgVisible={msgVisible}
          setMsgVisible={setMsgVisible}
          {...batchParams}
          concurrentNumber={50}
          onRefresh={onSelectRefresh}
          isComponents={isComponents}
        ></MiddleIndex>
        {/* 以下是从场次货盘粘贴的代码 */}
        <GoodsInfoEdit
          info={goodsInfo}
          onRef={goodsInfoRef}
          search={onRefresh}
          entry="changci"
          liveRoundId={(goodsInfo as LiveGoodsInfoResult).liveRoundInfo?.liveRoundId}
          tabsValue={tabsValue}
          onDetail={openEditDetail}
          pageTag="xuanpinkanban"
        />
        <ChooseModal
          liveRoundId={(goodsInfo as LiveGoodsInfoResult).liveRoundInfo?.liveRoundId}
          codeList={codeList}
          info={goodsInfo}
          onRef={chooseModalRef}
          search={onRefresh}
        />
        <Modal
          width={500}
          visible={singleVisible}
          // onClick={(e) => {
          //   // e.stopPropagation();
          //   // e.preventDefault();
          // }}
          onOk={handleOk}
          onCancel={() => {
            setSingleVisible(false);
          }}
        >
          <div
            // onClick={(e) => {
            //   e.stopPropagation();
            //   e.preventDefault();
            // }}
            style={{ display: 'flex' }}
          >
            <Icon
              type="exclamation-circle"
              className="iconStyle"
              style={{ color: roleInfo?.color, marginRight: 16 }}
            />
            <div>
              <h3> {roleInfo?.title}</h3> <br />
              {roleInfo?.info}
              {roleInfo?.type === 'del' && (
                <div style={{ marginTop: '12px' }}>
                  掉品原因：
                  <Select
                    value={reason}
                    style={{ width: 200 }}
                    dropdownMatchSelectWidth={false}
                    onChange={(e) => {
                      setReason(e);
                    }}
                    labelInValue
                  >
                    {ReasonList.map((i, index) => {
                      return (
                        <Select.Option value={i.value} key={index}>
                          {i.label}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </div>
              )}
            </div>
          </div>
        </Modal>
        <AddPayVoucherModal
          info={goodsInfo}
          onGetList={onRefresh}
          onCancel={closeAddVisible}
          visible={addVisible}
          onOk={closeAddVisible}
        ></AddPayVoucherModal>
        <ConfirmPayVoucher
          info={goodsInfo}
          tabsValue={tabsValue}
          onGetList={onRefresh}
          onCancel={closeConfirrmPayVoucherVisible}
          visible={confirrmPayVoucherVisible}
          onOk={closeConfirrmPayVoucherVisible}
        ></ConfirmPayVoucher>
        <MarkVoucher
          info={goodsInfo}
          onGetList={onRefresh}
          onCancel={closeMarkVoucherVisible}
          visible={markVoucherVisible}
          onOk={closeMarkVoucherVisible}
        ></MarkVoucher>
        <ApproveModal
          info={goodsInfo}
          onGetList={onRefresh}
          onCancel={closeApproveVisible}
          visible={ApproveVisible}
          onOk={closeApproveVisible}
        />
        <LegalCheckDrawer
          info={legalInfo?.info}
          visible={legalInfo?.visible}
          handleClose={() => {
            setLegalInfo({
              visible: false,
              info: {},
            });
          }}
          onGetList={onRefresh}
        />
        <ReturnSampleRegister
          info={goodsInfo}
          onGetList={onRefresh}
          onCancel={closeReturnSampleRegisterVisible}
          visible={returnSampleRegisterVisible}
          onOk={closeReturnSampleRegisterVisible}
        ></ReturnSampleRegister>
        <LimitInventory
          info={goodsInfo}
          onGetList={onRefresh}
          onCancel={closeLimitInventoryVisible}
          visible={limitInventoryVisible}
          onOk={closeLimitInventoryVisible}
        ></LimitInventory>
        <SpecialApprovalGoods
          onRefresh={onRefresh}
          visible={specialApprovalGoodsVisible}
          handleClose={() => {
            setSpecialApprovalGoodsVisible(false);
          }}
          info={goodsInfo}
        />
      </div>
    </PageLayout>
  );
};

export default Form.create<any>()(SelectionFlowBoard);
