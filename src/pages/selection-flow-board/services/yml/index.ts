import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

async function gql<T>(
  gqlDocument: string,
  operationName: string,
  sha256Hash: string,
  accessAuth: string,
  variables: {},
  path: string,
): Promise<ResponseWithResult<T>> {
  let requestBody = undefined;

  // 非线上环境不启用缓存
  if (Const.NODE_SERVER_ENV !== 'production') {
    requestBody = {
      variables: variables,
      extensions: {
        persistedQuery: {
          version: 1,
        },
      },
      operationName: operationName,
      query: gqlDocument,
    };
  } else {
    requestBody = {
      variables: variables,
      extensions: {
        persistedQuery: {
          version: 1,
        },
      },
      operationName: operationName,
    };
  }

  let response = await Fetch<T>(path, {
    method: 'POST',
    body: JSON.stringify(requestBody),
    headers: { accessAuth: accessAuth },
  });

  if ('errors' in response.res) {
    // @ts-ignore
    if (response.res.errors[0].message === 'PersistedQueryNotFound') {
      // 二次请求
      let requestBody = {
        variables: variables,
        extensions: {
          persistedQuery: {
            version: 1,
          },
        },
        operationName: operationName,
        query: gqlDocument,
      };
      response = await Fetch<T>(path, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { accessAuth: accessAuth },
      });
    }
  }
  // 封装返回结果
  if ('errors' in response.res) {
    return {
      success: false,
      // @ts-ignore
      message: response.res.errors[0].message,
      code: '400',
      // @ts-ignore
      result: response.res.errors[0].message,
    };
  } else {
    return {
      success: true,
      message: '',
      code: '200',
      // @ts-ignore
      result: response.res.data,
    };
  }
}
export const QueryGoodsBrandListDocument = `query _public_platform_spuBrandByName($name: String) {
  spuBrandByName(name: $name) {
    code
    englishName
    id
    logo
    name
    status
  }
}
`;

export type QueryGoodsBrandListVariables = {
  name?: string;
};

export type QueryGoodsBrandListResult = {
  spuBrandByName: Array<{
    /**品牌的编码 */ code: string;
    /**简拼 */
    englishName: string;
    /**null */
    id: string;
    /**logo */
    logo: string;
    /**null */
    name: string;
    /**启用状态：0 已启用  1：停用 */
    status: string;
  }>;
};

//queryGoodsBrandList:/gql/public/platform/spuBrandByName/b60ed801ca7d20fb2a4d8116c1a4ef555c83492636676b4026da03eeb377ab26
const queryGoodsBrandListHash = 'b60ed801ca7d20fb2a4d8116c1a4ef555c83492636676b4026da03eeb377ab26';
export async function queryGoodsBrandList(
  variables: QueryGoodsBrandListVariables,
): Promise<ResponseWithResult<QueryGoodsBrandListResult>> {
  return gql<QueryGoodsBrandListResult>(
    QueryGoodsBrandListDocument,
    '_public_platform_spuBrandByName',
    queryGoodsBrandListHash,
    getSign(
      '/gql/public/platform/spuBrandByName/b60ed801ca7d20fb2a4d8116c1a4ef555c83492636676b4026da03eeb377ab26',
    ),
    variables,
    '/gql/public/platform/spuBrandByName/b60ed801ca7d20fb2a4d8116c1a4ef555c83492636676b4026da03eeb377ab26',
  );
}

export type GetAllDeptListRequest = {
  deptPlatformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台 DY:抖音 TB：淘宝 JD:京东 PDD:拼多多 KS:快手[PlatformEnum]*/;
  isVisible?: boolean /*是否需要权限*/;
  liveRoomName?: string /*直播间名称*/;
};

export type GetAllDeptListResult = Array<{
  deptName?: string /*事业部名称*/;
  deptNo?: string /*事业部编号*/;
  id?: string /*id*/;
  status?: number /*是否启用 1：是 0：否*/;
}>;

/**
 *获取所有事业部
 */
export const getAllDeptList = (params: GetAllDeptListRequest) => {
  return Fetch<ResponseWithResult<GetAllDeptListResult>>('/iasm/public/dept/getDeptList', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/dept/getDeptList') },
  });
};

export type SelectionProcessKanbanPageRequest = {
  bpId?: string /*商务ID*/;
  brandAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*品牌资质结果[QualificationAuditStateEnum]*/;
  brandFeeMax?: string /*基础服务费查询区间 最大值*/;
  brandFeeMin?: string /*基础服务费查询区间 最小值*/;
  brandId?: string /*品牌ID*/;
  cooperationOrderId?: Array<string> /*合作订单id*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部ID*/;
  frameworkGoods?: boolean /*框架商品*/;
  goodsAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*商品资质结果[QualificationAuditStateEnum]*/;
  groupId?: Array<string> /*项目组ID*/;
  guaranteeProportionFlag?: boolean /*是否保比*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  guaranteeQuantityGoods?: boolean /*保量商品*/;
  isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalStatus?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*资质风险等级[SelectionQualificationRiskLevel]*/;
  linkCheckStatusList?: Array<string> /*链接确认状态*/;
  linkCheckStatusNullFlag?: boolean /*链接确认状态为空*/;
  linkFlag?: number /*是否有链接 1-有 0-无*/;
  liveDateEndTime?: string /*直播结束时间*/;
  liveDateStartTime?: string /*直播开始时间*/;
  livePlatformSpuIds?: Array<string> /*上播商品ID*/;
  liveRoomId?: Array<string> /*直播间ID*/;
  liveRoundId?: Array<string> /*直播场次ID*/;
  liveServiceTypeIds?: Array<string> /*直播服务类型IDs*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxCommissionRate?: string /*线上佣金比例-最高值*/;
  maxCommissionRateOffline?: string /*线下佣金比例-最高值*/;
  maxFavorableRate?: string /*最高商品好评率*/;
  maxSectionFee?: string /*切片费-最高值*/;
  maxShopPoints?: string /*最高店铺体验分*/;
  minCommissionRate?: string /*线上佣金比例-最低值*/;
  minCommissionRateOffline?: string /*线下佣金比例-最低值*/;
  minFavorableRate?: string /*最低商品好评率*/;
  minSectionFee?: string /*切片费-最低值*/;
  minShopPoints?: string /*最低店铺体验分*/;
  no?: Array<string> /*选品流程编号*/;
  notUploadFlag?: boolean /*是否只显示未上传付款凭证数据*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*运营审核状态[SelectionReviewStatus]*/;
  paymentVoucherStatus?:
    | 'WAIT_CONFIRM'
    | 'CONFIRMED'
    | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
  platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*来源平台[PlatformEnum]*/;
  platformSpuId?: Array<string> /*平台商品ID*/;
  processAttribution?: string /*流程归属*/;
  qualificationSupplementFlag?:
    | 'SUPPLEMENT_WAIT_AUDIT'
    | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
  qualifyInspectionStatus?:
    | 'NEEDLESS_INSPECTION'
    | 'NON_INSPECTION'
    | 'INSPECTING'
    | 'PASSED_INSPECTION'
    | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionLabel?: string /*选品标签*/;
  selectionStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*选品审核状态[SelectionReviewStatus]*/;
  serviceAgreementSigned?: boolean /*是否签署协议*/;
  size?: number /*分页大小*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  spokenIsComplete?: boolean /*口播稿是否完整*/;
  spuName?: Array<string> /*商品名称*/;
  spuNo?: Array<string> /*商品编号*/;
  status?: Array<
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED'
  > /*选品阶段[SelectionRoundStatus]*/;
  supplierAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*商家资质结果[QualificationAuditStateEnum]*/;
  supplierId?: string /*商家ID*/;
  talentId?: string /*达人Id*/;
};

export type SelectionProcessKanbanPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    allowQualityScoreAudit?: boolean /*是否允许质量分OA审核 true 是 false 否*/;
    auditDetailMap?: {
      [key: string]: {
        auditOpinion?: string /*审核意见*/;
        auditState?:
          | 'PASS'
          | 'NO_PASS'
          | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
        bizType?:
          | 'SUPPLIER'
          | 'BRAND'
          | 'GOODS'
          | 'SHOP'
          | 'BP_BRAND'
          | 'BP_GOODS'
          | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
        expirationDate?: string /*法务审核有效期*/;
        isBrandWhitelisted?: boolean /*是否白名单B*/;
        itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
        remark?: string /*备注*/;
      };
    } /*法务审核明细, Key是资质项目版本ID[QualificationBizTypeEnum]*/;
    avgSales?: string /*场均(支付)*/;
    avgSalesForFifteenDays?: string /*场均(T15)*/;
    bestSignCompanyStatus?:
      | 'WAIT_CHECK'
      | 'CHECKING'
      | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
    bpCancelReason?: string /*商务取消原因*/;
    bpId?: string /*商务ID*/;
    bpManagerName?: string /*商务经理*/;
    bpName?: string /*商务组长*/;
    bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[SelectionBpStatus]*/;
    brandFee?: string /*基础服务费，坑位费*/;
    brandFeePlatformServiceRate?: string /*基础服务费*/;
    brandName?: string /*品牌名称*/;
    captainPromotionLink?: {
      captainId?: string;
      creator?: string;
      doudianGoodsId?: string;
      doudianId?: string;
      doudianName?: string;
      effectEndTime?: string;
      effectStartTime?: string;
      errorDetail?: string;
      gmtCreated?: string;
      gmtModified?: string;
      id?: string;
      liveTime?: string;
      modifier?: string;
      operatorName?: string;
      promotionLink?: string;
      promotionLinkId?: string;
      reason?: number;
      sellPrice?: string;
      serviceFeeRate?: string;
      source?: number;
      spuId?: string;
      spuImg?: string;
      spuName?: string;
      spuNo?: string;
      status?: number;
      talentCommissionRate?: string;
      type?: number;
    } /*抖音推广链接*/;
    cateName?: string /*类目*/;
    commissionRate?: string /*线上佣金*/;
    commissionRateOffline?: string /*线下佣金*/;
    complianceAuditor?: string /*合规审核人ID*/;
    complianceAuditorName?: string /*合规审核人名称*/;
    complianceStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    coopFrameworkId?: string /*合作框架id*/;
    coopFrameworkType?: number /*合作框架类型,1保gmv模式-按主体,2保gmv模式-指定品牌*/;
    cooperationGuaranteed?: {
      accountNo?: string /*机构银行账号*/;
      achievedCalculationType?:
        | 'BY_PAYMENT'
        | 'BY_SETTLEMENT' /*达成计算方式(按支付-BY_PAYMENT,按结算-BY_SETTLEMENT)[AchievedCalculationTypeEnum]*/;
      agreedRefundFlag?: number /*是否约定退款*/;
      appointReceiptInstitutionId?: string /*指定收款机构id*/;
      appointReceiptInstitutionName?: string /*指定收款机构名称*/;
      bankName?: string /*机构开户行*/;
      bestSignContractId?: string /*上上签合同ID*/;
      bpId?: string /*商务id*/;
      bpName?: string /*商务名称*/;
      brandFee?: string /*基础佣金（原固定服务费）*/;
      brandFeeLatestPaymentTime?: string /*基础佣金最晚支付日期*/;
      brandIds?: Array<string> /*品牌id*/;
      brandInfo?: string /*品牌信息*/;
      brandInfoDTOS?: Array<{
        brandId?: string /*品牌id*/;
        brandName?: string /*品牌名称*/;
      }> /*品牌信息*/;
      commissionRate?: string /*线上佣金比例*/;
      contractApproveStatus?:
        | 'INIT'
        | 'WAIT_PENDING'
        | 'PENDING'
        | 'PASS'
        | 'REJECTED' /*合同审批状态（WAIT_PENDING：待审批；PENDING：审批中；PASS：已通过；REJECTED：已驳回）[ContractApproveStatusEnum]*/;
      contractInfo?: string /*合同信息*/;
      contractInfoList?: Array<string> /*合同信息*/;
      contractStatus?:
        | 'WAIT_START'
        | 'SIGNING'
        | 'SIGNED'
        | 'WITHDRAWAL' /*合同状态（WAIT_START:待开始；SIGNING:签署中；SIGNED:已签署）[ContractStatusEnum]*/;
      contractType?:
        | 'ON_LINE'
        | 'OFF_LINE' /*合同类型（ON_LINE：线上合同；OFF_LINE：线下合同）[GuaranteedContractTypeEnum]*/;
      coopActualEndTime?: string /*合作实际结束时间*/;
      coopAdvanceEndTime?: string /*合作提前结束时间*/;
      coopEndTime?: string /*合作结束时间*/;
      coopExtendEndTime?: string /*保量延长期*/;
      coopManualEndTime?: string /*合作手动结束时间*/;
      coopStartTime?: string /*合作开始时间*/;
      creator?: string /*创建人*/;
      creatorName?: string /*创建人名称*/;
      delFlag?: number /*删除标记*/;
      deptId?: string /*事业部id(已废弃)*/;
      deptInfo?: string /*事业部信息*/;
      deptInfoDTOS?: Array<{
        deptId?: string /*事业部id*/;
        deptName?: string /*事业部名称*/;
      }> /*事业部信息*/;
      description?: string /*合同描述*/;
      estimatedSettlementGmv?: string /*保量实际GMV(原名：预估结算gmv)*/;
      estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
      finishedGmv?: string /*保量预估GMV(原名：已完成目标gmv（原名：已完成保量gmv）)*/;
      finishedRate?: string /*保量预估GMV比例*/;
      gmtCreated?: string /*创建时间*/;
      gmtModified?: string /*更新时间*/;
      goodsKeyWordInfoDTOS?: Array<{
        brandInfoDTOS?: Array<{
          brandId?: string /*品牌id*/;
          brandName?: string /*品牌名称*/;
        }> /*品牌信息*/;
        goodsKeyWord?: string /*商品关键词*/;
        shopInfoDTOS?: Array<{
          shopId?: string /*店铺id*/;
          shopName?: string /*店铺名称*/;
        }> /*店铺信息*/;
      }> /*商品关键字信息*/;
      goodsKeywordInfo?: string /*商品关键字信息*/;
      guaranteeBrandFeeRate?: string /*保量基础佣金*/;
      guaranteeQuantityId?: string /*保量合作id(已废弃)*/;
      guaranteedGmv?: string /*目标gmv（原保量gmv）*/;
      guaranteedGmvAdvanceAchievedRule?:
        | 'AUTO_FINISH'
        | 'GO_ON' /*目标gmv提前达成计算规则(合作自动终止；乙方继续为甲方提供营销服务，对超出目标GMV部分的销售额，由甲方向乙方支付额外的激励佣金，激励佣金=超出的销售额/（目标GMV/基础佣金总额）)[GuaranteedGmvAdvanceAchievedRuleEnum]*/;
      guaranteedGmvFailAchievedRule?:
        | 'B_REFUNDS_UNUSED_BASIC_COMMISSION_TO_A'
        | 'EXTEND_COOPERATION_PERIOD'
        | 'UNTIL_ACHIEVEMENT_GOAL_EXTEND_COOPERATION_PERIOD' /*目标gmv未能达成计算规则（乙方向甲方退还未消耗的基础佣金；延长合作期限；合作期限延长至目标gmv达成之日）[GuaranteedGmvFailAchievedRuleEnum]*/;
      id?: string /*保量合同id*/;
      institutionId?: string /*机构id（弃用）*/;
      institutionIds?: Array<string> /*机构id集合*/;
      institutionInfoDTOS?: Array<{
        institutionId?: string /*机构id*/;
        institutionName?: string /*机构名称*/;
        institutionOrganizationName?: string /*机构实名主体名称*/;
        institutionRelNameVersion?: string /*机构实名快照版本号*/;
      }> /*机构信息*/;
      institutionName?: string /*机构名称（弃用）*/;
      institutionOrganizationName?: string /*机构实名主体名称（弃用）*/;
      institutionRelNameVersion?: string /*机构实名快照版本号（弃用）*/;
      invoiceContent?:
        | 'MODERN_SERVICE_TECHNOLOGY_SERVICE_FEE'
        | 'MODERN_SERVICE_SERVICE_FEE' /*发票内容[InvoiceContentTypeEnum]*/;
      invoiceRate?: string /*发票税率*/;
      isWhitelist?: boolean /*是否白名单（0：否；1：是）*/;
      liveRoomInfo?: string /*直播间信息*/;
      liveRoomInfos?: Array<string> /*直播间id*/;
      liveRoomPossessType?:
        | 'ALL'
        | 'INCLUDE' /*直播间拥有类型（ALL:全部；INCLUDE：包含)[LiveRoomPossessTypeEnum]*/;
      mediaSigningStatus?:
        | 'SIGNED'
        | 'UNSIGNED' /*新媒体服务协议签署状态(SIGNED：已签署；UNSIGNED：未签署)；已废弃[MediaSigningStatusEnum]*/;
      modifier?: string /*更新人*/;
      modifierName?: string /*更新人名称*/;
      name?: string /*保量合同名称*/;
      no?: string /*保量合同编号*/;
      oaRequestId?: string /*oa请求ID「审批流」*/;
      originalCoopGuaranteedId?: string /*原保量合作记录主键ID*/;
      originalCoopGuaranteedNo?: string /*原保量合作记录编号*/;
      payDurationType?:
        | 'T_PAY'
        | 'T_ZERO'
        | 'T_TWO'
        | 'T_FIFTEEN'
        | 'T_SEVEN'
        | 'BY_SETTLEMENT' /*支付天数（T_PAY:支付时；T+0:支付24h内；T+2:支付48h内）[PayDurationTypeEnum]*/;
      paymentAmountTotal?: string /*累计回款金额*/;
      paymentType?:
        | 'SELF_FUNDED'
        | 'THIRD_PARTY' /*付款方式（自行支付/代付）[ContractPaymentTypeEnum]*/;
      platform?: string /*平台*/;
      relVOS?: Array<{
        brandId?: string /*品牌id*/;
        cooperationGuaranteedId?: string /*保量合作记录主键id*/;
        cooperationGuaranteedNo?: string /*保量合作记录编号*/;
        creator?: string /*创建人*/;
        gmtCreated?: string;
        gmtModified?: string;
        id?: string /*id*/;
        legalName?: string /*供应商主体名称*/;
        liveDate?: string /*直播时间*/;
        liveRoomId?: string /*直播间id*/;
        liveRoomName?: string /*直播间名称*/;
        modifier?: string /*更新人*/;
        openId?: string /*直播间open_id*/;
        platformId?: string /*平台id*/;
        platformSource?: string /*商品来源平台*/;
        platformSpuId?: string /*平台商品ID*/;
        relFlag?: number /*关联标记（是否关联， 0 否  1 是）*/;
        selectionNo?: string /*场次货盘编号*/;
        shopId?: string /*店铺id*/;
        spuName?: string /*spu名称*/;
        spuNo?: string /*spu编号*/;
        supplierId?: string /*商家id*/;
        talentId?: string /*达人id*/;
      }> /*关联关系*/;
      settlementIntervalType?:
        | 'MONTH'
        | 'QUARTER'
        | 'YEAR' /*结算周期[ContractSettlementIntervalTypeEnum]*/;
      shopInfo?: string /*店铺信息*/;
      shopInfoDTOS?: Array<{
        shopId?: string /*店铺id*/;
        shopName?: string /*店铺名称*/;
      }> /*店铺信息*/;
      status?:
        | 'DRAFT'
        | 'WAIT_START'
        | 'IN_COOPERATION'
        | 'FINISHED'
        | 'WITHDRAWAL'
        | 'TERMINATE' /*合作状态（DRAFT：暂存；WAIT_START:待开始；IN_COOPERATION:合作中；FINISHED:已结束）[GuaranteedStatusEnum]*/;
      stimulateCommission?: string /*激励佣金*/;
      stimulateCommissionRate?: string /*激励佣金比例*/;
      supplierCompanyNameInfo?: string /*商家主体名称信息*/;
      supplierCompanyNameInfos?: Array<string> /*商家主体名称信息*/;
      supplierInfo?: string /*商家信息*/;
      supplierInfoDTOS?: Array<{
        contactAddress?: string /*联系地址*/;
        contactMail?: string /*联系邮箱*/;
        contactMobile?: string /*联系电话*/;
        contactName?: string /*联系人*/;
        supplierCompanyName?: string /*商家主体名称*/;
        supplierId?: string /*商家id*/;
      }> /*商家信息*/;
      thirdPartyPayerName?: string /*代付方名称*/;
      thirdPartyPayerUscc?: string /*代付方统一社会信用代码*/;
      type?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'SHOP'
        | 'SHOP_BRAND'
        | 'GOODS_KEYWORD' /*保量类型（SUPPLIER：按商家主体；BRAND：按品牌；SHOP：按店铺；SHOP_BRAND：按店铺品牌；GOODS_KEYWORD：商品关键字）[GuaranteedTypeEnum]*/;
      version?: number /*版本号*/;
    } /*保量信息*/;
    cooperationMode?:
      | 'COLONEL'
      | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
    cooperationOrderId?: string /*合作订单id*/;
    deptId?: string /*事业部ID*/;
    dropProductReason?: string /*掉品原因*/;
    dropProductReasonType?: string /*掉品原因类型*/;
    favorableRate?: string /*好评率*/;
    frameworkCoopModel?: Array<
      'GUARANTEED_GMV_MODE' | 'GUARANTEED_LIVE_ROUND_MODE' | 'GUARANTEED_SLICE_MODE'
    > /*合作模式多选：1保 GMV模式，2保场次排期模式，3前两种多选[CooperationFrameworkCoopModelEnum]*/;
    frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
    frameworkRoundFlag?: boolean /*是否计入年框场次*/;
    giftLogisticsNo?: string /*赠品快递单号*/;
    gmtCreated?: string /*创建时间*/;
    gmtCreatedTime?: string /*流程创建时间*/;
    gmvCommissionRate?: string /*GMV模式会有：年框GMV总分佣比例*/;
    gmvPlatformCommissionRate?: string /*GMV模式会有：年框GMV平台分佣比例*/;
    goodsQualityScore?: string /*商品质量分*/;
    goodsScore?: string /*商品体验分*/;
    groupId?: string /*项目组id*/;
    guaranteeProportionFlag?: boolean /*是否保比*/;
    guaranteeProportionId?: string /*保比id*/;
    guaranteeQuantityFlag?: boolean /*是否保量*/;
    guaranteeQuantityId?: string /*保量id*/;
    hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
    historySumSales?: string /*历史累计(支付)*/;
    historySumSalesForFifteenDays?: string /*历史累计(T15)*/;
    id?: string;
    image?: string /*商品图片*/;
    isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
    isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
    labelList?: string /*标签*/;
    legalAuditor?: string /*法务审核人ID*/;
    legalAuditorName?: string /*法务审核人名称*/;
    legalStatus?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*资质风险[SelectionQualificationRiskLevel]*/;
    linkBak?: string /*商品参考链接*/;
    linkCheckRemark?: string /*链接确认备注*/;
    linkCheckStatus?:
      | 'WAIT_CHECK'
      | 'NO_PROMOTION'
      | 'CAN_PROMOTION' /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
    linkValidityEffectiveTime?: string /*上播链接有效时间*/;
    linkValidityEndTime?: string /*上播链接结束时间*/;
    linkValidityStartTime?: string /*上播链接开始时间*/;
    liveDate?: string /*场次日期*/;
    liveDateTime?: string /*直播日期*/;
    livePlatformSpuId?: string /*上播商品id*/;
    liveRoomId?: string /*直播间id*/;
    liveRoundId?: string /*场次*/;
    liveRoundName?: string /*直播间名称*/;
    liveServiceType?: string /*直播服务类型*/;
    logisticsContent?: {
      deliveryCycle?: string /* 发货周期, 必填，xx天内发货，默认2天*/;
      deliveryMode?: 'SPOT' | 'PRESALE' /* 发货模式, 必填，现货/预售[DeliveryModeEnum]*/;
      giftDeliveryMode?: boolean /* 主品赠品发货情况, 赠品是否随主品一起发货，true：是，false：否*/;
      giftLogisticsNo?: string /* 赠品寄样单号，如果选择 主赠分别发货 显示本字段*/;
      logisticsNo?: string /* 主品寄样单号*/;
    } /*物流信息，json对象*/;
    logisticsNo?: string /*主品快递单号*/;
    logisticsScore?: string /*物流体验分*/;
    lowCommissionAuditStatus?:
      | 'NONE'
      | 'INIT'
      | 'PENDING'
      | 'APPROVED'
      | 'REJECTED'
      | 'CANCELED'
      | 'DELETED'
      | 'REVERTED'
      | 'OVERTIME_CLOSE'
      | 'OVERTIME_RECOVER' /*低佣审核状态[LowCommissionAuditStatusEnum]*/;
    lowCommissionAuditTime?: string /*低佣审核时间*/;
    lowCommissionAuditor?: string /*低佣审核人*/;
    lowCommissionAuditorName?: string /*低佣审核姓名*/;
    lowCommissionFlowNo?: string /*低佣审核流程编号*/;
    luckyProductFlag?: boolean /*是否福袋商品*/;
    luxuryReviewStatus?:
      | 'NO_NEED_REVIEW'
      | 'PENDING_REVIEW'
      | 'REJECTED'
      | 'APPROVED' /*高奢商品资质复查状态[LuxuryReviewStatusEnum]*/;
    maxPrice?: string /*最大价 */;
    mdyConfirmLive?: boolean /*明道云确认上播状态,0:否,1:是*/;
    minPrice?: string /*最小价*/;
    needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
    no?: string /*选品编号*/;
    operatorAuditor?: string /*运营审核人ID*/;
    operatorAuditorName?: string /*运营审核人名称*/;
    operatorStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    paymentCooperationId?: string /*合作订单付款订单id*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentVoucherId?: string /*付款凭证id*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    performanceState?: string /*履约状态*/;
    platformShopName?: string /*店铺名称*/;
    platformSource?:
      | 'DY'
      | 'JD'
      | 'TB'
      | 'PDD'
      | 'KS'
      | 'WECHAT_VIDEO'
      | 'BAIDU'
      | 'RED' /*商品来源`平台`[PlatformEnum]*/;
    platformSpuId?: string /*平台商品id*/;
    platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
    preSaleStock?: number /*预售库存*/;
    projectName?: string /*项目组*/;
    promotionLink?: string /*上播链接*/;
    promotionLinkId?: string /*推广链接id*/;
    qualificationRisk?: string /*资质风险等级*/;
    qualificationSupplementFlag?:
      | 'SUPPLEMENT_WAIT_AUDIT'
      | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
    qualifyInspectionStatus?:
      | 'NEEDLESS_INSPECTION'
      | 'NON_INSPECTION'
      | 'INSPECTING'
      | 'PASSED_INSPECTION'
      | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
    qualityScoreAuditStatus?:
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT' /*质量分审核状态[QualityScoreAuditStatusEnum]*/;
    roundTotalFees?: string /*保场次排期模式会有：场次费用*/;
    saasProductId?: string /*SaaS商品id*/;
    sectionFee?: string /*切片费*/;
    selectionAuditor?: string /*选品负责人ID*/;
    selectionAuditorName?: string /*选品负责人名称*/;
    selectionLabel?: string /*选品标签*/;
    selectionStage?: string /*选品阶段*/;
    selectionStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    sellingPoints?: string /*商品主要卖点*/;
    serviceAgreementId?: string /*服务协议id*/;
    serviceScore?: string /*商家服务分*/;
    shopPoints?: string /*店铺分*/;
    skuPrice?: string /*到手价*/;
    skus?: string /*sku信息，json对象*/;
    soldSalesVolume?: string /*已售销量*/;
    specQualificationVersion?: string /*采买链路资质版本ID, 无论是否采买均绑定一个采买链路版本, 用于品牌类型切换处理*/;
    specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
    specialAuditId?: string /*资质特批Id*/;
    specialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'WITHDRAW' /*资质特批状态[SpecialAuditStatus]*/;
    specialAuditType?: 'ONLINE' | 'OFFLINE' | 'HIGH_SPECIAL' /*特批类型[SpecialAuditTypeEnum]*/;
    spokenType?: 'PRIMARY_VERSION' | 'FINAL_VERSION' /*口播稿类型[SpokenScriptTypeEnum]*/;
    spuFocus?: string /*重点展示需求*/;
    spuId?: string /*spu id*/;
    spuName?: string /*商品名称*/;
    spuNo?: string /*spu编号*/;
    standardFavorableRate?: string /*标准好评率*/;
    standardGoodsScore?: string /*标准商品体验分*/;
    standardLogisticsScore?: string /*标准物流体验分*/;
    standardServiceScore?: string /*标准商家服务分*/;
    standardStore?: string /*标准店铺分*/;
    status?:
      | 'BP_CONFIRMING'
      | 'WAIT_AUDIT'
      | 'WAIT_LIVE'
      | 'ABORT_LIVE'
      | 'ABORT_WAIT_LIVE'
      | 'COMPLETED_LIVE'
      | 'CANCEL'
      | 'INVITING'
      | 'LOSE_EFFICACY'
      | 'TB_ORDERED' /*选品阶段[SelectionRoundStatus]*/;
    strategyFail?: string /*定向策略失败原因*/;
    suggestCommission?: string /*建议佣金*/;
    supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
    supplierBodySpecialAuditStatus?:
      | 'WAIT_AUDIT'
      | 'CONFIRMING'
      | 'PASS'
      | 'REJECT'
      | 'INVALID' /*主体特批状态[SupplierBodySpecialAuditStatusEnum]*/;
    supplierId?: string /*商家id*/;
    supplierOperatorId?: string /*商家员工ID*/;
    supplierOrgId?: string /*商家主体快照ID*/;
    supplierOrgName?: string /*商家主体名称*/;
    supplierReason?: string /*商家驳回原因*/;
    supplierStatus?:
      | 'INIT'
      | 'PASS'
      | 'AUTO_PASS'
      | 'REJECT'
      | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
    totalCommission?: string /*总佣金*/;
    totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
    version?: number /*版本号*/;
    yearFrameBrandFee?: string /*年框品牌费*/;
    yearFrameCommissionRate?: string /*年框佣金*/;
    yearFrameType?: string /*年框履约类型*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询选品流程看板
 */
export const selectionProcessKanbanPage = (params: SelectionProcessKanbanPageRequest) => {
  return Fetch<ResponseWithResult<SelectionProcessKanbanPageResult>>(
    '/iasm/public/selectionProcessKanban/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selectionProcessKanban/page') },
    },
  );
};

export type NonLiveSelectionPageRequest = {
  bpId?: string /*商务ID*/;
  brandId?: string /*品牌ID*/;
  cooperationOrderNoList?: Array<string> /*合作订单编号*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部ID*/;
  isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalStatus?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*资质风险等级[SelectionQualificationRiskLevel]*/;
  liveDateEndTime?: string /*合作结束时间*/;
  liveDateStartTime?: string /*合作开始时间*/;
  liveRoomId?: Array<string> /*直播间ID*/;
  maxCommissionRate?: string /*线上佣金比例-最高值*/;
  maxFavorableRate?: string /*最高商品好评率*/;
  maxShopPoints?: string /*最高店铺体验分*/;
  minCommissionRate?: string /*线上佣金比例-最低值*/;
  minFavorableRate?: string /*最低商品好评率*/;
  minShopPoints?: string /*最低店铺体验分*/;
  no?: Array<string> /*非直播选品流程编号*/;
  platformShopName?: string /*平台店铺名称*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*商品来源平台[PlatformEnum]*/;
  platformSpuId?: Array<string> /*平台商品ID*/;
  processAttribution?: string /*流程归属*/;
  qualifyInspectionStatus?:
    | 'NEEDLESS_INSPECTION'
    | 'NON_INSPECTION'
    | 'INSPECTING'
    | 'PASSED_INSPECTION'
    | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
  serviceTypeList?: Array<
    'LIVE' | 'SHOP_WINDOW' | 'VIDEO_CLIP' | 'SLICE' | 'TB_CUSTOMER'
  > /*业务类型,SHOP_WINDOW-橱窗,VIDEO_CLIP-短视频,SLICE-直播视频分发[ServiceTypeEnum]*/;
  size?: number /*分页大小*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  spuName?: Array<string> /*商品名称*/;
  spuNo?: Array<string> /*商品编号*/;
  status?: Array<
    'WAIT_AUDIT' | 'IN_COOPERATE' | 'END_COOPERATE' | 'LOSE_EFFICACY' | 'DROP_PRODUCT'
  > /*选品阶段[NonLiveSelectionRoundStatus]*/;
  supplierId?: string /*商家ID*/;
};

export type NonLiveSelectionPageResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    afterSaleContent?: {
      freshCompensation?: string /*（生鲜）赔付标准 文本 行业大类为生鲜时展示该字段；提示：请填写赔付标准和有效期，例如：确认收货后30个工作日内，坏果坏1赔1，超过50%全赔*/;
      freshIsCold?: boolean /* （生鲜）是否冷链发货 true：是，false：否*/;
      installationService?: string /* （数码家电）售后附加服务 文本 行业大类为数码家电时展示该字段；提示：免人工安装费用，额外收取xx元材料费，支持延迟发货*/;
      insuranceTime?: string /* （数码家电）质保时长  文本 行业大类为数码家电时展示该字段*/;
      modes?: Array<
        'NONE' | 'NO_REASON' | 'FREE_FREIGHT_INSURANCE' | 'OTHER'
      > /* 售后服务 多选数组 必填，选项：无、7天无理由、赠送运费险、其他[AfterSaleTypeEnum]*/;
      others?: string /* 其他售后服务文本 必填，售后服务选其他时，必填该字段*/;
      productionDate?: string /* 生产日期 文本 行业大类为食品饮料、美妆护肤则展示，例如：晚于2023年6月*/;
      shelfLife?: string /* 产品保质期 文本 行业大类为食品饮料、美妆护肤则展示，例如：6个月、避光保存7天*/;
    } /*售后信息，json对象*/;
    applyBillType?: string /*类型 正向 FORWARD 逆向 REVERSE 平台型商家(自动生成)报名单 PLATFORM_SUPPLIER_FORWARD*/;
    applyChannel?: string /*报名渠道，枚举项：招商计划；货盘邀请链接；选品邀请链接*/;
    applyRecordId?: string /*报名记录ID*/;
    applyRecordNo?: string /*报名记录编号*/;
    bpAssistantIds?: string /*商务助理ID, 英文逗号分割*/;
    bpId?: string /*商务ID*/;
    bpName?: string /*商务名称*/;
    bpStatus?:
      | 'INIT'
      | 'PASS'
      | 'REJECT'
      | 'CANCEL' /*商务审核状态，INIT:待审核、PASS:通过、REJECT:驳回、CANCEL:已取消[NonLiveSelectionBpStatus]*/;
    brandFee?: string /*基础服务费，坑位费*/;
    brandFeeTechRate?: string /*基础服务费平台服务费率*/;
    brandId?: string /*品牌ID*/;
    brandName?: string /*品牌名称*/;
    cateId?: string /*类目ID*/;
    cateName?: string /*类目名称*/;
    commentGood?: string /*商品好评数*/;
    commentTotal?: string /*商品评价数*/;
    commissionRate?: string /*线上佣金比例*/;
    commissionRateOffline?: string /*线下佣金比例*/;
    commissionTechRate?: string /*佣金平台服务费率*/;
    coopMode?: string /*合作方式，枚举项：线上合作、线下合作*/;
    coopOrderId?: string /*合订单id*/;
    coopOrderNo?: string /*合订单订单号*/;
    cooperationMode?:
      | 'COLONEL'
      | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
    creator?: string /*创建人*/;
    deptId?: string /*事业部ID*/;
    discountContent?: {
      giftInfos?: Array<{
        condition?:
          | 'IMMEDIATE'
          | 'REQUIREMENTS' /*赠送条件，下单即送、指定条件赠送[GiftConditionTypeEnum]*/;
        conditionRemark?: string /* 赠送条件备注*/;
        giftType?: 'ALL' | 'PART' /*赠品生效规格[GiftTypeEnum]*/;
        image?: string /*赠品图片*/;
        link?: string /*赠品链接*/;
        name?: string /*赠品名称*/;
        onSale?: boolean /*是否在售*/;
        price?: string /*赠品价格*/;
        skuNoList?: Array<string> /*赠品生效的sku编号*/;
      }> /*赠品信息*/;
      remark?: string /* 优惠备注*/;
      type?: Array<
        'FULL_REDUCTION' | 'IMMEDIATE_REDUCTION' | 'COUPON' | 'OTHER' | 'NONE'
      > /* 优惠方式, 必填，选项：满减、立减、优惠券、其他、无（该选项和其他选项互斥，选了无则不允许选其他选项[DiscountTypeEnum]*/;
    } /*优惠信息，json对象*/;
    endTime?: string /*合作结束时间*/;
    favorableRate?: string /*好评率*/;
    generationMethod?: string /*生成方式*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    groupId?: string /*项目组ID*/;
    hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
    highRiskGrant?: boolean /*高风险特批 0：否 1：是*/;
    id?: string;
    image?: string /*商品主图链接*/;
    institutionId?: string /*机构id*/;
    investmentId?: string /*招商计划ID*/;
    investmentNo?: string /*招商计划编号*/;
    investmentTitle?: string /*招商计划名称*/;
    isGuarantee?: boolean /*是否担保交易 0：不担保 1：担保*/;
    isPriceProtection?: boolean /*商品价格保护 0：否 1：是*/;
    isSampleProvided?: boolean /*样品提供*/;
    isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
    legalAuditor?: string /*法务审核人ID*/;
    legalAuditorName?: string /*法务审核人名称*/;
    legalReason?: string /*法务驳回原因*/;
    legalStatus?:
      | 'QUALIFIED'
      | 'HIGH'
      | 'HIGH_SPECIAL'
      | 'MIDDLE'
      | 'LOW'
      | 'PASS'
      | 'NONE' /*法务审核状态，INIT:待审核、PASS:通过、REJECT:驳回[NonLiveSelectionQualificationRiskLevel]*/;
    link?: string /*商品参考链接*/;
    linkBak?: string /*商品参考链接（手动补充）*/;
    linkValidityEndTime?: string /*上播链接有效期结束时间*/;
    linkValidityStartTime?: string /*上播链接有效期开始时间*/;
    liveRoomId?: string /*直播间ID*/;
    liveRoomName?: string /*直播间名称*/;
    logisticsContent?: {
      deliveryCycle?: string /* 发货周期, 必填，xx天内发货，默认2天*/;
      deliveryMode?: 'SPOT' | 'PRESALE' /* 发货模式, 必填，现货/预售[DeliveryModeEnum]*/;
      giftDeliveryMode?: boolean /* 主品赠品发货情况, 赠品是否随主品一起发货，true：是，false：否*/;
      giftLogisticsNo?: string /* 赠品寄样单号，如果选择 主赠分别发货 显示本字段*/;
      logisticsNo?: string /* 主品寄样单号*/;
    } /*物流信息，json对象*/;
    maxPrice?: string /*最大价 */;
    minPrice?: string /*最小价*/;
    modifier?: string /*修改人*/;
    no?: string /*编号, CCHP+TB*/;
    noQualificationRequired?: string /*是否免资质, 0不免除，1免除*/;
    platformShopId?: string /*平台店铺ID(抖店/快手等店铺ID)*/;
    platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
    platformSource?: string /*商品来源平台*/;
    platformSpuId?: string /*平台商品ID*/;
    platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
    promotionLink?: string /*推广链接*/;
    promotionLinkId?: string /*推广链接id*/;
    qualificationAuditId?: string /*资质审核id*/;
    qualificationItemAudit?: string /*资质项审核集合, 示例：[{item_version_id, biz_type, audit_state}]*/;
    qualificationSupplementFlag?:
      | 'SUPPLEMENT_WAIT_AUDIT'
      | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
    qualifyInspectionStatus?:
      | 'NEEDLESS_INSPECTION'
      | 'NON_INSPECTION'
      | 'INSPECTING'
      | 'PASSED_INSPECTION'
      | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
    relateQuaFlag?: boolean /*关联资质通过商品 0：否 1：是*/;
    sectionFee?: string /*切片费*/;
    sellingPoints?: string /*商品主要卖点*/;
    serviceAgreementId?: string /*服务协议id*/;
    serviceType?:
      | 'LIVE'
      | 'SHOP_WINDOW'
      | 'VIDEO_CLIP'
      | 'SLICE'
      | 'TB_CUSTOMER' /*服务类型[ServiceTypeEnum]*/;
    shelfLife?: string /*食品保质期*/;
    shopId?: string /*店铺ID*/;
    shopPoints?: string /*店铺分*/;
    skus?: Array<{
      cashAmount?: string /*商家返现金额*/;
      hisHighestPrice?: string /*历史最高价*/;
      hisLowestPrice?: string /*历史最低价*/;
      id?: string /*sku id*/;
      image?: string /*sku图片*/;
      name?: string /*sku名称，规格名称*/;
      price?: string /*日常价格*/;
      purchasePrice?: string /*采购价（含税）*/;
      salePrice?: string /*直播价格*/;
      skuNo?: string /*sku编号*/;
      stock?: string /*库存*/;
      taxRate?: string /*税率（%）*/;
    }> /*sku信息，json对象*/;
    sourceOrderNo?: string /*来源单据编码*/;
    sourceOrderType?: string /*来源单据类型*/;
    specQualificationIds?: string /*采买链路资质资源IDS*/;
    specQualificationNotice?: boolean /*采买链路待补充发送通知, 发送失败为 0, 发送成功为 1*/;
    specQualificationVersion?: string /*采买链路资质版本ID, 无论是否采买均绑定一个采买链路版本, 用于品牌类型切换处理*/;
    specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
    spuFocus?: string /*重点展示需求*/;
    spuId?: string /*spu id*/;
    spuName?: string /*spu名称*/;
    spuNo?: string /*spu编号*/;
    spuSource?: string /*商品来源：SUPPLIER_SPU、CAPTAIN_ACTIVITY_SPU、KUAISHOU_CAPTAIN_ACTIVITY_SPU ...*/;
    standardCateId?: string /*行业大类ID*/;
    standardCateName?: string /*行业大类名称*/;
    standardFavorableRate?: string /*标准好评率*/;
    standardStore?: string /*标准店铺分*/;
    startTime?: string /*合作开始时间*/;
    status?:
      | 'WAIT_AUDIT'
      | 'IN_COOPERATE'
      | 'END_COOPERATE'
      | 'LOSE_EFFICACY'
      | 'DROP_PRODUCT' /*场次货盘状态[NonLiveSelectionRoundStatus]*/;
    strategyFail?: string /*定向策略失败原因*/;
    supplierId?: string /*商家ID*/;
    supplierOperatorId?: string /*商家员工ID*/;
    supplierOrgId?: string /*商家主体快照ID*/;
    supplierOrgName?: string /*商家主体名称*/;
    supplySource?: string /*来源，国产、跨境、进口*/;
    talentId?: string /*达人ID*/;
    talentNo?: string /*达人编号*/;
    totalCommission?: string /*总佣金*/;
    version?: string /*版本号更新时校验*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *分页查询非直播选品流程
 */
export const nonLiveSelectionPage = (params: NonLiveSelectionPageRequest) => {
  return Fetch<ResponseWithResult<NonLiveSelectionPageResult>>(
    '/iasm/public/nonLiveSelection/page',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/page') },
    },
  );
};

export type ProjectGroupListRequest = {
  current?: number /*当前页码,从1开始*/;
  departmentName?: string /*事业部*/;
  deptId?: string /*事业部id*/;
  employId?: string /*员工id*/;
  projectGroupName?: string /*项目组名称*/;
  projectGroupStatusEnum?: 'ENABLE' | 'DISABLE' /*状态[ProjectGroupStatusEnum]*/;
  size?: number /*分页大小*/;
};

export type ProjectGroupListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    departmentId?: string /*事业部id(主键id)*/;
    departmentName?: string /*事业部名称*/;
    employeeInfoList?: Array<{
      employId?: string /*员工id*/;
      employName?: string /*员工姓名*/;
      projectGroupEmployeeTypeEnum?:
        | 'SELECTION_LEADER'
        | 'SELECTION'
        | 'BUSINESS_LEADER'
        | 'BUSINESS' /*员工类型[ProjectGroupEmployeeTypeEnum]*/;
    }> /*项目组员工信息列表*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    platformEnum?:
      | 'DY'
      | 'JD'
      | 'TB'
      | 'PDD'
      | 'KS'
      | 'WECHAT_VIDEO'
      | 'BAIDU'
      | 'RED' /*平台类型[PlatformEnum]*/;
    projectGroupId?: string /*项目组id*/;
    projectGroupName?: string /*项目组名称*/;
    projectGroupStatusEnum?: 'ENABLE' | 'DISABLE' /*状态[ProjectGroupStatusEnum]*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *项目组列表
 */
export const projectGroupList = (params: ProjectGroupListRequest) => {
  return Fetch<ResponseWithResult<ProjectGroupListResult>>(
    '/iasm/public/projectGroupManagement/projectGroupList',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/projectGroupManagement/projectGroupList') },
    },
  );
};

export type EmployeeExtConfigQueryRequest = {
  bizType?: string /*业务类型*/;
  exportTag?: string /*导出标签*/;
};

export type EmployeeExtConfigQueryResult = string;

/**
 *用户表头配置查询
 */
export const employeeExtConfigQuery = (params: EmployeeExtConfigQueryRequest) => {
  return Fetch<ResponseWithResult<EmployeeExtConfigQueryResult>>(
    '/iasm/public/employeeExtConfig/query',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/employeeExtConfig/query') },
    },
  );
};

export type EmployeeExtConfigSetRequest = {
  bizType?: string /*业务类型*/;
  extConfig?: string /*拓展配置*/;
};

export type EmployeeExtConfigSetResult = boolean;

/**
 *用户表头配置新增
 */
export const employeeExtConfigSet = (params: EmployeeExtConfigSetRequest) => {
  return Fetch<ResponseWithResult<EmployeeExtConfigSetResult>>(
    '/iasm/public/employeeExtConfig/set',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/employeeExtConfig/set') },
    },
  );
};

export type SelectionProcessKanbanExportRequest = {
  bpId?: string /*商务ID*/;
  brandAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*品牌资质结果[QualificationAuditStateEnum]*/;
  brandFeeMax?: string /*基础服务费查询区间 最大值*/;
  brandFeeMin?: string /*基础服务费查询区间 最小值*/;
  brandId?: string /*品牌ID*/;
  cooperationOrderId?: Array<string> /*合作订单id*/;
  deptId?: string /*事业部ID*/;
  dynamicHead?: string /*流程看板动态表头*/;
  exportTag?: string /*导出标签*/;
  goodsAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*商品资质结果[QualificationAuditStateEnum]*/;
  groupId?: Array<string> /*项目组ID*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  ids?: Array<string> /*流程看板IDS*/;
  isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalStatus?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*资质风险等级[SelectionQualificationRiskLevel]*/;
  linkCheckStatusList?: Array<
    'WAIT_CHECK' | 'NO_PROMOTION' | 'CAN_PROMOTION'
  > /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
  linkFlag?: number /*是否有链接 1-有 0-无*/;
  liveDateEndTime?: string /*直播结束时间*/;
  liveDateStartTime?: string /*直播开始时间*/;
  liveRoomId?: Array<string> /*直播间ID*/;
  liveRoundId?: Array<string> /*直播场次ID*/;
  liveServiceTypeIds?: Array<string> /*直播服务类型IDs*/;
  maxCommissionRate?: string /*线上佣金比例-最高值*/;
  maxFavorableRate?: string /*最高商品好评率*/;
  maxSectionFee?: string /*切片费-最高值*/;
  maxShopPoints?: string /*最高店铺体验分*/;
  minCommissionRate?: string /*线上佣金比例-最低值*/;
  minFavorableRate?: string /*最低商品好评率*/;
  minSectionFee?: string /*切片费-最低值*/;
  minShopPoints?: string /*最低店铺体验分*/;
  no?: Array<string> /*选品流程编号*/;
  notUploadFlag?: boolean /*是否只显示未上传付款凭证数据*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*运营审核状态[SelectionReviewStatus]*/;
  paymentVoucherStatus?:
    | 'WAIT_CONFIRM'
    | 'CONFIRMED'
    | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
  platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*来源平台[PlatformEnum]*/;
  platformSpuId?: Array<string> /*平台商品ID*/;
  processAttribution?: string /*流程归属*/;
  qualificationSupplementFlag?:
    | 'SUPPLEMENT_WAIT_AUDIT'
    | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionLabel?: string /*选品标签*/;
  selectionStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*选品审核状态[SelectionReviewStatus]*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  spuName?: Array<string> /*商品名称*/;
  spuNo?: Array<string> /*商品编号*/;
  status?: Array<
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED'
  > /*选品阶段[SelectionRoundStatus]*/;
  supplierAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*商家资质结果[QualificationAuditStateEnum]*/;
  supplierId?: string /*商家ID*/;
  talentId?: string /*达人Id*/;
};

export type SelectionProcessKanbanExportResult = string;

/**
 *流程看板详情信息导出
 */
export const selectionProcessKanbanExport = (params: SelectionProcessKanbanExportRequest) => {
  return Fetch<ResponseWithResult<SelectionProcessKanbanExportResult>>(
    '/iasm/public/selectionProcessKanban/export',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selectionProcessKanban/export') },
    },
  );
};

export type NonSelectionProcessKanbanExportRequest = {
  bpId?: string /*商务ID*/;
  brandId?: string /*品牌ID*/;
  cooperationOrderNoList?: Array<string> /*合作订单编号*/;
  current?: number;
  deptId?: string /*事业部ID*/;
  dynamicHead?: string /*流程看板动态表头*/;
  ids?: Array<string> /*非直播Ids*/;
  isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalStatus?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*资质风险等级[SelectionQualificationRiskLevel]*/;
  liveDateEndTime?: string /*合作结束时间*/;
  liveDateStartTime?: string /*合作开始时间*/;
  liveRoomId?: Array<string> /*直播间ID*/;
  maxFavorableRate?: string /*最高商品好评率*/;
  maxShopPoints?: string /*最高店铺体验分*/;
  minFavorableRate?: string /*最低商品好评率*/;
  minShopPoints?: string /*最低店铺体验分*/;
  no?: Array<string> /*非直播选品流程编号*/;
  platformShopName?: string /*平台店铺名称*/;
  platformSpuId?: Array<string> /*平台商品ID*/;
  processAttribution?: string /*流程归属*/;
  serviceTypeList?: Array<
    'LIVE' | 'SHOP_WINDOW' | 'VIDEO_CLIP' | 'SLICE' | 'TB_CUSTOMER'
  > /*业务类型,SHOP_WINDOW-橱窗,VIDEO_CLIP-短视频,SLICE-直播视频分发[ServiceTypeEnum]*/;
  size?: number;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  spuName?: Array<string> /*商品名称*/;
  spuNo?: Array<string> /*商品编号*/;
  status?: Array<
    'WAIT_AUDIT' | 'IN_COOPERATE' | 'END_COOPERATE' | 'LOSE_EFFICACY' | 'DROP_PRODUCT'
  > /*选品阶段[NonLiveSelectionRoundStatus]*/;
  supplierId?: string /*商家ID*/;
};

export type NonSelectionProcessKanbanExportResult = string;

/**
 *非直播选品流程导出
 */
export const nonSelectionProcessKanbanExport = (params: NonSelectionProcessKanbanExportRequest) => {
  return Fetch<ResponseWithResult<NonSelectionProcessKanbanExportResult>>(
    '/iasm/public/nonLiveSelection/export',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/export') },
    },
  );
};

export type BpConfirmRequest = {
  frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
  frameworkRoundFlag?: boolean /*是否计入年框场次*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  id?: string /*id*/;
  liveServiceTypeId?: string /*直播服务类型id*/;
  roundTotalFees?: string /*年框佣金*/;
  version?: number /*版本号*/;
};

export type BpConfirmResult = boolean;

/**
 *商务确认
 */
export const bpConfirm = (params: BpConfirmRequest) => {
  return Fetch<ResponseWithResult<BpConfirmResult>>('/iasm/public/selection/bpConfirm', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/bpConfirm') },
  });
};

export type BatchBpConfirmRequest = {
  bpConfirmRequests?: Array<{
    frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
    frameworkRoundFlag?: boolean /*是否计入年框场次*/;
    guaranteeQuantityFlag?: boolean /*是否保量*/;
    id?: string /*id*/;
    liveServiceTypeId?: string /*直播服务类型id*/;
    roundTotalFees?: string /*年框佣金*/;
    version?: number /*版本号*/;
  }>;
};

export type BatchBpConfirmResult = {
  failCount?: number /*失败数量*/;
  failResultModels?: Array<{
    failNo?: string /*场次货盘编号编号*/;
    failReason?: string /*错误原因*/;
  }> /*失败内容*/;
  successCount?: number /*成功数量*/;
  successResultModels?: Array<{
    successNo?: string /*场次货盘编号编号*/;
  }> /*成功内容*/;
  totalCount?: number /*总数*/;
};

/**
 *场次货盘商务批量确认
 */
export const batchBpConfirm = (params: BatchBpConfirmRequest) => {
  return Fetch<ResponseWithResult<BatchBpConfirmResult>>(
    '/iasm/public/selectionProcessKanban/batchBpConfirm',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selectionProcessKanban/batchBpConfirm') },
    },
  );
};

export type SelectionImportLinksRequest = {
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[PlatformEnum]*/;
  resourceId?: string /*导入资源文件id*/;
};

export type SelectionImportLinksResult = {
  errorList?: Array<{
    errorNo?: string /*错误编号*/;
    errorReason?: string /*错误原因*/;
  }> /*失败内容*/;
  failCount?: number /*失败数量*/;
  successCount?: number /*成功数量*/;
  totalCount?: number /*总数*/;
};

/**
 *导入链接
 */
export const selectionImportLinks = (params: SelectionImportLinksRequest) => {
  return Fetch<ResponseWithResult<SelectionImportLinksResult>>(
    '/iasm/public/selection/importLinks',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/importLinks') },
    },
  );
};

export type NonSelectionImportLinksRequest = {
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[PlatformEnum]*/;
  resourceId?: string /*导入资源文件id*/;
};

export type NonSelectionImportLinksResult = {
  errorList?: Array<{
    errorNo?: string /*错误编号*/;
    errorReason?: string /*错误原因*/;
  }> /*失败内容*/;
  failCount?: number /*失败数量*/;
  successCount?: number /*成功数量*/;
  totalCount?: number /*总数*/;
};

/**
 *非直播导入链接
 */
export const nonSelectionImportLinks = (params: NonSelectionImportLinksRequest) => {
  return Fetch<ResponseWithResult<NonSelectionImportLinksResult>>(
    '/iasm/public/nonLiveSelection/importLinks',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/importLinks') },
    },
  );
};

export type SelectionBpCancelRequest = {
  id?: string /*id*/;
  reason?: string /*原因*/;
  version?: number /*版本号*/;
};

export type SelectionBpCancelResult = boolean;

/**
 *商务取消
 */
export const selectionBpCancel = (params: SelectionBpCancelRequest) => {
  return Fetch<ResponseWithResult<SelectionBpCancelResult>>('/iasm/public/selection/bpCancel', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/bpCancel') },
  });
};

export type SelectionConfirmRequest = {
  id?: string /*id*/;
  version?: number /*版本号*/;
};

export type SelectionConfirmResult = boolean;

/**
 *选品确认
 */
export const selectionConfirm = (params: SelectionConfirmRequest) => {
  return Fetch<ResponseWithResult<SelectionConfirmResult>>(
    '/iasm/public/selection/selectionConfirm',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/selectionConfirm') },
    },
  );
};

export type OperatorConfirmRequest = {
  id?: string /*id*/;
  version?: number /*版本号*/;
};

export type OperatorConfirmResult = boolean;

/**
 *运营确认
 */
export const operatorConfirm = (params: OperatorConfirmRequest) => {
  return Fetch<ResponseWithResult<OperatorConfirmResult>>(
    '/iasm/public/selection/operatorConfirm',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/operatorConfirm') },
    },
  );
};

export type DroppedProductRequest = {
  dropProductReasonType?: string /*掉品原因类型*/;
  id?: string /*id*/;
  reason?: string /*原因*/;
  version?: number /*版本号*/;
};

export type DroppedProductResult = boolean;

/**
 *运营选品商务总监掉品
 */
export const droppedProduct = (params: DroppedProductRequest) => {
  return Fetch<ResponseWithResult<DroppedProductResult>>('/iasm/public/selection/droppedProduct', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/droppedProduct') },
  });
};

export type DroppedProductRepeatRequest = {
  id?: string /*id*/;
};

export type DroppedProductRepeatResult = boolean;

/**
 *掉品复播
 */
export const droppedProductRepeat = (params: DroppedProductRepeatRequest) => {
  return Fetch<ResponseWithResult<DroppedProductRepeatResult>>(
    '/iasm/public/selection/droppedProductRepeat',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/droppedProductRepeat') },
    },
  );
};

export type AddSuppLinkRequest = {
  id?: string /*场次货盘ID*/;
  ids?: Array<string> /*场次货盘ID*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  livePlatformSpuId?: string /*上播商品id*/;
  livestreamLink?: string /*上播链接*/;
};

export type AddSuppLinkResult = boolean;

/**
 *补充链接
 */
export const addSuppLink = (params: AddSuppLinkRequest) => {
  return Fetch<ResponseWithResult<AddSuppLinkResult>>('/iasm/public/selection/addLinks', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/addLinks') },
  });
};

export type NonAddSuppLinkRequest = {
  id?: string /*非直播ID*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  livestreamLink?: string /*上播链接*/;
};

export type NonAddSuppLinkResult = boolean;

/**
 *非直播补充链接
 */
export const nonAddSuppLink = (params: NonAddSuppLinkRequest) => {
  return Fetch<ResponseWithResult<NonAddSuppLinkResult>>('/iasm/public/nonLiveSelection/addLinks', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/addLinks') },
  });
};

export type GetOneLinkRequest = {
  id?: string /*业务ID*/;
};

export type GetOneLinkResult = {
  autoGetLinks?: boolean /*调用淘宝接口是否调用成功*/;
  msg?: string /*单个获取上播链接返回结果*/;
};

/**
 *请求链接
 */
export const getOneLink = (params: GetOneLinkRequest) => {
  return Fetch<ResponseWithResult<GetOneLinkResult>>('/iasm/public/selection/getOneLink', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/getOneLink') },
  });
};

export type LiveGoodsInfoRequest = {
  id?: string /*业务ID*/;
};

export type LiveGoodsInfoResult = {
  afterSaleContent?: string /*售后信息，json对象*/;
  applyBillType?:
    | 'FORWARD'
    | 'REVERSE'
    | 'PLATFORM_SUPPLIER_FORWARD' /*类型 正向 FORWARD 逆向 REVERSE 平台型商家(自动生成)报名单 PLATFORM_SUPPLIER_FORWARD[ApplyTypeEnum]*/;
  applyChannel?:
    | 'INVESTMENT_PLAN_DIRECT'
    | 'INVESTMENT_PLAN_INVITE'
    | 'INVESTMENT_PLAN_LIVE_ROUND_INVITE' /*报名渠道，枚举项：招商计划；货盘邀请链接；选品邀请链接[ApplyChannelEnum]*/;
  applyRecordId?: string /*报名记录ID*/;
  applyRecordNo?: string /*报名记录编号*/;
  auditDetailMap?: {
    [key: string]: {
      auditOpinion?: string /*审核意见*/;
      auditState?:
        | 'PASS'
        | 'NO_PASS'
        | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
      bizType?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'GOODS'
        | 'SHOP'
        | 'BP_BRAND'
        | 'BP_GOODS'
        | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
      expirationDate?: string /*法务审核有效期*/;
      isBrandWhitelisted?: boolean /*是否白名单B*/;
      itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
      remark?: string /*备注*/;
    };
  } /*法务审核明细, Key是资质项目版本ID[QualificationBizTypeEnum]*/;
  bestSignCompanyStatus?:
    | 'WAIT_CHECK'
    | 'CHECKING'
    | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
  bpAssistantIds?: string /*商务助理ID, 英文逗号分割*/;
  bpId?: string /*商务ID*/;
  bpName?: string /*商务名称*/;
  bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[SelectionBpStatus]*/;
  brandFee?: string /*基础服务费，坑位费*/;
  brandFeeTechRate?: string /*基础服务费平台服务费率*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  captainPromotionLink?: {
    captainId?: string;
    creator?: string;
    doudianGoodsId?: string;
    doudianId?: string;
    doudianName?: string;
    effectEndTime?: string;
    effectStartTime?: string;
    errorDetail?: string;
    gmtCreated?: string;
    gmtModified?: string;
    id?: string;
    liveTime?: string;
    modifier?: string;
    operatorName?: string;
    promotionLink?: string;
    promotionLinkId?: string;
    reason?: number;
    sellPrice?: string;
    serviceFeeRate?: string;
    source?: number;
    spuId?: string;
    spuImg?: string;
    spuName?: string;
    spuNo?: string;
    status?: number;
    talentCommissionRate?: string;
    type?: number;
  } /*抖音推广链接*/;
  cateId?: string /*类目ID*/;
  cateName?: string /*类目名称*/;
  cateNamePath?: string /*类目全路径名称*/;
  categoryCar?: string /*类目车*/;
  comment?: string /*备注*/;
  commentGood?: string /*商品好评数*/;
  commentTotal?: string /*商品评价数*/;
  commissionRate?: string /*线上佣金比例*/;
  commissionRateOffline?: string /*线下佣金比例*/;
  commissionTechRate?: string /*佣金平台服务费率*/;
  complianceAuditor?: string /*合规审核人ID*/;
  complianceAuditorName?: string /*合规审核人名称*/;
  complianceNo?: string /*合规审批编号*/;
  complianceStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  coopFrameworkId?: string /*合作框架id*/;
  coopMode?: string /*合作方式，枚举项：线上合作、线下合作*/;
  coopOrderNo?: string /*合作订单号*/;
  cooperationGuaranteed?: {
    accountNo?: string /*机构银行账号*/;
    achievedCalculationType?:
      | 'BY_PAYMENT'
      | 'BY_SETTLEMENT' /*达成计算方式(按支付-BY_PAYMENT,按结算-BY_SETTLEMENT)[AchievedCalculationTypeEnum]*/;
    agreedRefundFlag?: number /*是否约定退款*/;
    appointReceiptInstitutionId?: string /*指定收款机构id*/;
    appointReceiptInstitutionName?: string /*指定收款机构名称*/;
    bankName?: string /*机构开户行*/;
    bestSignContractId?: string /*上上签合同ID*/;
    bpId?: string /*商务id*/;
    bpName?: string /*商务名称*/;
    brandFee?: string /*基础佣金（原固定服务费）*/;
    brandFeeLatestPaymentTime?: string /*基础佣金最晚支付日期*/;
    brandIds?: Array<string> /*品牌id*/;
    brandInfo?: string /*品牌信息*/;
    brandInfoDTOS?: Array<{
      brandId?: string /*品牌id*/;
      brandName?: string /*品牌名称*/;
    }> /*品牌信息*/;
    commissionRate?: string /*线上佣金比例*/;
    contractApproveStatus?:
      | 'INIT'
      | 'WAIT_PENDING'
      | 'PENDING'
      | 'PASS'
      | 'REJECTED' /*合同审批状态（WAIT_PENDING：待审批；PENDING：审批中；PASS：已通过；REJECTED：已驳回）[ContractApproveStatusEnum]*/;
    contractInfo?: string /*合同信息*/;
    contractInfoList?: Array<string> /*合同信息*/;
    contractStatus?:
      | 'WAIT_START'
      | 'SIGNING'
      | 'SIGNED'
      | 'WITHDRAWAL' /*合同状态（WAIT_START:待开始；SIGNING:签署中；SIGNED:已签署）[ContractStatusEnum]*/;
    contractType?:
      | 'ON_LINE'
      | 'OFF_LINE' /*合同类型（ON_LINE：线上合同；OFF_LINE：线下合同）[GuaranteedContractTypeEnum]*/;
    coopActualEndTime?: string /*合作实际结束时间*/;
    coopAdvanceEndTime?: string /*合作提前结束时间*/;
    coopEndTime?: string /*合作结束时间*/;
    coopExtendEndTime?: string /*保量延长期*/;
    coopManualEndTime?: string /*合作手动结束时间*/;
    coopStartTime?: string /*合作开始时间*/;
    creator?: string /*创建人*/;
    creatorName?: string /*创建人名称*/;
    delFlag?: number /*删除标记*/;
    deptId?: string /*事业部id(已废弃)*/;
    deptInfo?: string /*事业部信息*/;
    deptInfoDTOS?: Array<{
      deptId?: string /*事业部id*/;
      deptName?: string /*事业部名称*/;
    }> /*事业部信息*/;
    description?: string /*合同描述*/;
    estimatedSettlementGmv?: string /*保量实际GMV(原名：预估结算gmv)*/;
    estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
    finishedGmv?: string /*保量预估GMV(原名：已完成目标gmv（原名：已完成保量gmv）)*/;
    finishedRate?: string /*保量预估GMV比例*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    goodsKeyWordInfoDTOS?: Array<{
      brandInfoDTOS?: Array<{
        brandId?: string /*品牌id*/;
        brandName?: string /*品牌名称*/;
      }> /*品牌信息*/;
      goodsKeyWord?: string /*商品关键词*/;
      shopInfoDTOS?: Array<{
        shopId?: string /*店铺id*/;
        shopName?: string /*店铺名称*/;
      }> /*店铺信息*/;
    }> /*商品关键字信息*/;
    goodsKeywordInfo?: string /*商品关键字信息*/;
    guaranteeBrandFeeRate?: string /*保量基础佣金*/;
    guaranteeQuantityId?: string /*保量合作id(已废弃)*/;
    guaranteedGmv?: string /*目标gmv（原保量gmv）*/;
    guaranteedGmvAdvanceAchievedRule?:
      | 'AUTO_FINISH'
      | 'GO_ON' /*目标gmv提前达成计算规则(合作自动终止；乙方继续为甲方提供营销服务，对超出目标GMV部分的销售额，由甲方向乙方支付额外的激励佣金，激励佣金=超出的销售额/（目标GMV/基础佣金总额）)[GuaranteedGmvAdvanceAchievedRuleEnum]*/;
    guaranteedGmvFailAchievedRule?:
      | 'B_REFUNDS_UNUSED_BASIC_COMMISSION_TO_A'
      | 'EXTEND_COOPERATION_PERIOD'
      | 'UNTIL_ACHIEVEMENT_GOAL_EXTEND_COOPERATION_PERIOD' /*目标gmv未能达成计算规则（乙方向甲方退还未消耗的基础佣金；延长合作期限；合作期限延长至目标gmv达成之日）[GuaranteedGmvFailAchievedRuleEnum]*/;
    id?: string /*保量合同id*/;
    institutionId?: string /*机构id（弃用）*/;
    institutionIds?: Array<string> /*机构id集合*/;
    institutionInfoDTOS?: Array<{
      institutionId?: string /*机构id*/;
      institutionName?: string /*机构名称*/;
      institutionOrganizationName?: string /*机构实名主体名称*/;
      institutionRelNameVersion?: string /*机构实名快照版本号*/;
    }> /*机构信息*/;
    institutionName?: string /*机构名称（弃用）*/;
    institutionOrganizationName?: string /*机构实名主体名称（弃用）*/;
    institutionRelNameVersion?: string /*机构实名快照版本号（弃用）*/;
    invoiceContent?:
      | 'MODERN_SERVICE_TECHNOLOGY_SERVICE_FEE'
      | 'MODERN_SERVICE_SERVICE_FEE' /*发票内容[InvoiceContentTypeEnum]*/;
    invoiceRate?: string /*发票税率*/;
    isWhitelist?: boolean /*是否白名单（0：否；1：是）*/;
    liveRoomInfo?: string /*直播间信息*/;
    liveRoomInfos?: Array<string> /*直播间id*/;
    liveRoomPossessType?:
      | 'ALL'
      | 'INCLUDE' /*直播间拥有类型（ALL:全部；INCLUDE：包含)[LiveRoomPossessTypeEnum]*/;
    mediaSigningStatus?:
      | 'SIGNED'
      | 'UNSIGNED' /*新媒体服务协议签署状态(SIGNED：已签署；UNSIGNED：未签署)；已废弃[MediaSigningStatusEnum]*/;
    modifier?: string /*更新人*/;
    modifierName?: string /*更新人名称*/;
    name?: string /*保量合同名称*/;
    no?: string /*保量合同编号*/;
    oaRequestId?: string /*oa请求ID「审批流」*/;
    originalCoopGuaranteedId?: string /*原保量合作记录主键ID*/;
    originalCoopGuaranteedNo?: string /*原保量合作记录编号*/;
    payDurationType?:
      | 'T_PAY'
      | 'T_ZERO'
      | 'T_TWO'
      | 'T_FIFTEEN'
      | 'T_SEVEN'
      | 'BY_SETTLEMENT' /*支付天数（T_PAY:支付时；T+0:支付24h内；T+2:支付48h内）[PayDurationTypeEnum]*/;
    paymentAmountTotal?: string /*累计回款金额*/;
    paymentType?:
      | 'SELF_FUNDED'
      | 'THIRD_PARTY' /*付款方式（自行支付/代付）[ContractPaymentTypeEnum]*/;
    platform?: string /*平台*/;
    relVOS?: Array<{
      brandId?: string /*品牌id*/;
      cooperationGuaranteedId?: string /*保量合作记录主键id*/;
      cooperationGuaranteedNo?: string /*保量合作记录编号*/;
      creator?: string /*创建人*/;
      gmtCreated?: string;
      gmtModified?: string;
      id?: string /*id*/;
      legalName?: string /*供应商主体名称*/;
      liveDate?: string /*直播时间*/;
      liveRoomId?: string /*直播间id*/;
      liveRoomName?: string /*直播间名称*/;
      modifier?: string /*更新人*/;
      openId?: string /*直播间open_id*/;
      platformId?: string /*平台id*/;
      platformSource?: string /*商品来源平台*/;
      platformSpuId?: string /*平台商品ID*/;
      relFlag?: number /*关联标记（是否关联， 0 否  1 是）*/;
      selectionNo?: string /*场次货盘编号*/;
      shopId?: string /*店铺id*/;
      spuName?: string /*spu名称*/;
      spuNo?: string /*spu编号*/;
      supplierId?: string /*商家id*/;
      talentId?: string /*达人id*/;
    }> /*关联关系*/;
    settlementIntervalType?:
      | 'MONTH'
      | 'QUARTER'
      | 'YEAR' /*结算周期[ContractSettlementIntervalTypeEnum]*/;
    shopInfo?: string /*店铺信息*/;
    shopInfoDTOS?: Array<{
      shopId?: string /*店铺id*/;
      shopName?: string /*店铺名称*/;
    }> /*店铺信息*/;
    status?:
      | 'DRAFT'
      | 'WAIT_START'
      | 'IN_COOPERATION'
      | 'FINISHED'
      | 'WITHDRAWAL'
      | 'TERMINATE' /*合作状态（DRAFT：暂存；WAIT_START:待开始；IN_COOPERATION:合作中；FINISHED:已结束）[GuaranteedStatusEnum]*/;
    stimulateCommission?: string /*激励佣金*/;
    stimulateCommissionRate?: string /*激励佣金比例*/;
    supplierCompanyNameInfo?: string /*商家主体名称信息*/;
    supplierCompanyNameInfos?: Array<string> /*商家主体名称信息*/;
    supplierInfo?: string /*商家信息*/;
    supplierInfoDTOS?: Array<{
      contactAddress?: string /*联系地址*/;
      contactMail?: string /*联系邮箱*/;
      contactMobile?: string /*联系电话*/;
      contactName?: string /*联系人*/;
      supplierCompanyName?: string /*商家主体名称*/;
      supplierId?: string /*商家id*/;
    }> /*商家信息*/;
    thirdPartyPayerName?: string /*代付方名称*/;
    thirdPartyPayerUscc?: string /*代付方统一社会信用代码*/;
    type?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'SHOP'
      | 'SHOP_BRAND'
      | 'GOODS_KEYWORD' /*保量类型（SUPPLIER：按商家主体；BRAND：按品牌；SHOP：按店铺；SHOP_BRAND：按店铺品牌；GOODS_KEYWORD：商品关键字）[GuaranteedTypeEnum]*/;
    version?: number /*版本号*/;
  } /*保量信息*/;
  cooperationMode?: string /*合作模式:COLONEL-团长,DIRECT-定向*/;
  cooperationOrderStatus?:
    | 'TO_BE_ADD'
    | 'TO_BE_FULFILL_AGREEMENT'
    | 'FULFILL_AGREEMENT'
    | 'CLOSED'
    | 'AGREEMENT_CONFIRMED' /*履约状态[CooperationOrderStatusEnum]*/;
  creator?: string /*创建人*/;
  depositAmount?: string /*定金金额*/;
  deptId?: string /*事业部ID*/;
  discountContent?: string /*优惠信息，json对象*/;
  dropProductReason?: string /*掉品原因*/;
  estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
  favorableRate?: string /*好评率*/;
  favorableRateRefreshTime?: string /*好评率刷新时间*/;
  finishedRate?: string /*保量预估GMV比例*/;
  firstCateParentId?: string /*一级类目id*/;
  frameworkCoopModel?: Array<
    'GUARANTEED_GMV_MODE' | 'GUARANTEED_LIVE_ROUND_MODE' | 'GUARANTEED_SLICE_MODE'
  > /*合作模式多选：1保 GMV模式，2保场次排期模式，3前两种多选[CooperationFrameworkCoopModelEnum]*/;
  frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
  frameworkRoundFlag?: boolean /*是否计入年框场次*/;
  generationMethod?: string /*生成方式*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*更新时间*/;
  gmvCommissionRate?: string /*年框GMV总分佣比例*/;
  gmvPlatformCommissionRate?: string /*年框GMV平台分佣比例*/;
  goodsQualityScore?: string /*商品质量分*/;
  goodsQualityScoreApprovalProcessNo?: string /*商品质量分审批流程编号*/;
  goodsQualityScoreProcessUid?: string /*商品质量分OA流程id*/;
  groupId?: string /*项目组ID*/;
  groupName?: string /*项目组名称*/;
  guaranteeBrandFeeRate?: string /*保量基础佣金*/;
  guaranteeProportionFlag?: boolean /*是否保比*/;
  guaranteeProportionId?: string /*保比记录id*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  guaranteeQuantityId?: string /*保量id*/;
  hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
  highRiskGrant?: boolean /*高风险特批 0：否 1：是*/;
  id?: string /*场次货盘ID*/;
  image?: string /*商品主图链接*/;
  institutionId?: string /*机构id*/;
  interestPoints?: string /*利益点*/;
  investmentId?: string /*招商计划ID*/;
  investmentNo?: string /*招商计划编号*/;
  investmentTitle?: string /*招商计划名称*/;
  isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
  isGuarantee?: boolean /*是否担保交易 false：不担保 true：担保*/;
  isPriceProtection?: boolean /*商品价格保护 0：否 1：是*/;
  isSampleProvided?: boolean /*样品提供*/;
  labelConfigInfo?: Array<{
    labelName?: string /*标签名称*/;
    labelOptionId?: string /*标签选项配置id*/;
    labelOptionName?: string /*标签选项名称*/;
  }> /*标签配置信息*/;
  labelList?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*id*/;
    labelGroupName?: string /*标签名称*/;
    labelOptionList?: Array<{
      id?: string /*id*/;
      labelOption?: string /*标签内容名称*/;
    }> /*标签内容*/;
    requiredFlag?: number /* 0-非必填 1-必填*/;
    status?: 'ENABLE' | 'DISABLE' /*ENABLE-启用 UNABLE-禁用[LabelStatusEnum]*/;
  }> /*标签*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalAuditorName?: string /*法务审核人名称*/;
  legalReason?: string /*法务驳回原因*/;
  legalStatus?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*法务审核状态[SelectionQualificationRiskLevel]*/;
  link?: string /*商品参考链接*/;
  linkBak?: string /*商品参考链接（手动补充）*/;
  linkCheckRemark?: string /*链接确认备注*/;
  linkCheckStatus?:
    | 'WAIT_CHECK'
    | 'NO_PROMOTION'
    | 'CAN_PROMOTION' /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  liveDate?: string /*场次ID*/;
  liveMaxPrice?: string /*最大价 */;
  liveMinPrice?: string /*最小价*/;
  livePlatformSpuId?: string /*上播商品id*/;
  liveRoomId?: string /*直播间id*/;
  liveRoundId?: string /*场次ID*/;
  liveRoundInfo?: {
    anchorType?:
      | 'PRIMARY_ANCHOR'
      | 'SECONDARY_ANCHOR'
      | 'TALENT_ANCHOR' /*主播类型[AnchorTypeEnum]*/;
    liveDate?: string /*直播日期*/;
    liveEndTime?: string /*直播结束时间*/;
    liveRoomId?: string /*直播间Id*/;
    liveRoomImg?: string /*直播间头像*/;
    liveRoomName?: string /*直播间名称*/;
    liveRoundId?: string /*直播场次ID*/;
    liveRoundName?: string /*直播场次名称*/;
    liveStartTime?: string /*直播开始时间*/;
    subject?: string /*场次主题*/;
  } /*场次信息*/;
  liveServiceType?: string /*直播服务类型(讲解类型)*/;
  liveServiceTypeId?: string /*直播服务类型ID*/;
  logisticsContent?: string /*物流信息，json对象*/;
  lowCommissionAuditStatus?:
    | 'NONE'
    | 'INIT'
    | 'PENDING'
    | 'APPROVED'
    | 'REJECTED'
    | 'CANCELED'
    | 'DELETED'
    | 'REVERTED'
    | 'OVERTIME_CLOSE'
    | 'OVERTIME_RECOVER' /*低佣审核状态[LowCommissionAuditStatusEnum]*/;
  lowCommissionAuditTime?: string /*低佣审核时间*/;
  lowCommissionAuditor?: string /*低佣审核人*/;
  lowCommissionAuditorName?: string /*低佣审核姓名*/;
  lowCommissionFlowNo?: string /*低佣审核流程编号*/;
  lowCommissionUniqueCode?: string /*低佣请求id*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxPrice?: string /*最大价 */;
  minPrice?: string /*最小价*/;
  needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
  ninetyDayLiveCount?: string /*90天已播场次数*/;
  ninetyDayLiveZeroCount?: string /*90天内销量=0的场次数*/;
  ninetyDayLiveZeroRate?: string /*低效品占比*/;
  no?: string /*编号, CCHP+TB*/;
  noQualificationRequired?: string /*是否免资质, 0不免除，1免除*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorAuditorName?: string /*运营审核人名称*/;
  operatorReason?: string /*运营驳回原因*/;
  operatorStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  orderProcessStatus?: string /*单据处理状态 生成中： Generating ，已生成： generated ，待关闭 Wait_Close */;
  paymentOrderStatus?:
    | 'NO_PAYMENT_REQUIRED'
    | 'PENDING_PAYMENT'
    | 'PAYING'
    | 'PARTIAL_PAYMENT'
    | 'PAYMENT_FAIL'
    | 'FULL_PAYMENT'
    | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
  paymentVoucher?: {
    comment?: string /*备注*/;
    confirmedDate?: string /*确认时间*/;
    confirmedName?: string /*财务名称*/;
    confirmedPerson?: string /*确认人*/;
    cooperationOrderId?: string /*合作订单id*/;
    gmtCreated?: string /*创建时间*/;
    id?: string;
    ourMainCompany?: string /*我方收款主体*/;
    paymentAmount?: string /*回款总金额*/;
    paymentDate?: string /*付款日期*/;
    paymentFileList?: Array<string> /*付款凭证文件*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderId?: string /*合作订单付款订单id*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentReason?: string /*付款凭证问题原因*/;
    paymentStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态 待确认 已确认 有问题[PaymentVoucherStatus]*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    supplierMainCompany?: string /*商家付款主体*/;
  } /*付款凭证信息*/;
  platformShopId?: string /*平台店铺ID(抖店/快手等店铺ID)*/;
  platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*商品来源平台[PlatformEnum]*/;
  platformSpuId?: string /*平台商品ID*/;
  platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
  playBackStatus?: 'PLAYED' | 'NOT_PLAYER' /*上播状态[PlayBackStatusEnum]*/;
  preCreateCoopOrder?: boolean /*是否需要前置生成合作订单*/;
  preSaleStock?: number /*预售库存*/;
  price?: string /*日常价*/;
  promotionLink?: string /*推广链接*/;
  promotionLinkBak?: string /*推广链接(手动补充)*/;
  promotionLinkId?: string /*推广链接id*/;
  qualityScoreAuditStatus?:
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT' /*质量分审核状态[QualityScoreAuditStatusEnum]*/;
  qualityScoreAuditor?: string /*商品质量分审批人*/;
  reasonDetail?: string /*详细原因*/;
  reasonType?:
    | 'BREAK_PRICE_REDUCE_COMMISSION'
    | 'BRAND_FEE_AFFECTS'
    | 'SUPPLEMENT_LIVE'
    | 'QUANTITY_GUARANTEED'
    | 'HIS_COMMERCIAL_CLAUSE_REPORTING_ERROR'
    | 'BRAND_REDUCE_COMMISSION'
    | 'FRAMEWORK_COOPERATION'
    | 'WELFARE_GOODS'
    | 'OTHER_LESS_THAN_LUO'
    | 'OTHER' /*低佣原因[LowCommissionReasonTypeEnum]*/;
  receiveSampleRegister?: string /*收样登记*/;
  resourceUrls?: Array<{
    resourceId?: string /*链接附件id*/;
    resourceUrl?: string /*链接附件url*/;
  }> /*链接附件列表*/;
  returnSampleRegister?: string /*退样登记*/;
  reverseDemandId?: string /*反需人员Id*/;
  reverseDemandName?: string /*反需人员姓名*/;
  reverseDemandTag?: boolean /*反需标签,0:否,1:是*/;
  roundTotalFees?: string /*场次费用*/;
  salePrice?: string /*到手价 */;
  sectionFee?: string /*切片费*/;
  selectGoodsPoolNo?: string /*选品池编号*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionAuditorName?: string /*选品审核人名称*/;
  selectionLabel?: string /*选品标签*/;
  selectionReason?: string /*选品驳回原因*/;
  selectionRemark?: string /*选品备注*/;
  selectionRoundAdjustRecordList?: Array<{
    adjustReason?: string /*调整原因*/;
    adjustSource?: 'COOP_ORDER_ADJUST' /*记录来源：COOP_ORDER_ADJUST:合作调整单[SelectionRoundAdjustRecordAdjustSource]*/;
    adjustType?:
      | 'ADJUST_SERVICE_FEE'
      | 'ADJUST_CHANGE_BINDING' /*调整类型ADJUST_SERVICE_FEE：调整基础服务费，ADJUST_CHANGE_BINDING：调整解绑支付单[SelectionRoundAdjustRecordAdjustType]*/;
    afterBrandFee?: string /*调整后基础服务费*/;
    approvalProcessNo?: string /*审批流程编号*/;
    beforeBrandFee?: string /*调整前基础服务费*/;
    bizOrderId?: string /*来源单据ID*/;
    coopOrderId?: string /*合作订单主键*/;
    coopOrderNo?: string /*合作订单号*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键ID*/;
    modifier?: string /*修改人*/;
    requestUniqueCode?: string /*请求唯一编号*/;
    selectionRoundId?: string /*场次货盘ID*/;
    selectionRoundNo?: string /*场次货盘编号*/;
    status?:
      | 'INIT'
      | 'PASS'
      | 'REJECT' /*审核状态，INIT：待审核、PASS：通过、REJECT：驳回[SelectionRoundAdjustRecordStatus]*/;
    targetCoopOrderId?: string /*目标合作订单id*/;
    targetCoopOrderNo?: string /*目标合作订单号*/;
    targetSelectionRoundId?: string /*目标场次货盘ID*/;
    targetSelectionRoundNo?: string /*目标场次货盘编号*/;
  }> /*商务条款变更信息*/;
  selectionStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  sellingPoints?: string /*商品主要卖点*/;
  serviceAgreementId?: string /*服务协议id*/;
  shelfLife?: string /*食品保质期*/;
  shopId?: string /*店铺ID*/;
  shopPoints?: string /*店铺分*/;
  shopPointsRefreshTime?: string /*店铺分刷新时间*/;
  skus?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    purchasePrice?: string /*采购价（含税）*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
    taxRate?: string /*税率（%）*/;
  }> /*sku信息*/;
  soldSalesVolume?: string /*已售销量*/;
  sourceOrderNo?: string /*来源单据编码*/;
  sourceOrderType?: string /*来源单据类型*/;
  specQualificationVersion?: string /*采买链路资质版本ID, 无论是否采买均绑定一个采买链路版本, 用于品牌类型切换处理*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  specialAuditApprovalProcessNo?: string /*资质特批-审批流程编号*/;
  specialAuditId?: string /*资质特批Id*/;
  specialAuditProcessUid?: string /*资质特批Uid*/;
  specialAuditStatus?:
    | 'WAIT_AUDIT'
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT'
    | 'WITHDRAW' /*资质特批状态[SpecialAuditStatus]*/;
  specialAuditType?: 'ONLINE' | 'OFFLINE' | 'HIGH_SPECIAL' /*特批类型[SpecialAuditTypeEnum]*/;
  specialAuditorRecordList?: Array<{
    auditTime?: string /*审批时间*/;
    auditor?: string /*审批人名称*/;
    auditorId?: string /*审批人ID*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    level?: string /*审批级别*/;
    modifier?: string /*修改人*/;
    rejectReason?: string /*驳回原因*/;
    roleType?:
      | 'BUSINESS'
      | 'SELECTION'
      | 'OPERATE'
      | 'MIDDLE_GROUND'
      | 'LEGAL'
      | 'ANCHOR' /*角色类型[RoleTypeEnum]*/;
    status?: string /*状态*/;
  }> /*资质特批审核人记录*/;
  spuFocus?: string /*重点展示需求*/;
  spuFocusResources?: Array<{
    resourceId?: string /*链接附件id*/;
    resourceUrl?: string /*链接附件url*/;
  }> /*重点展示需求附件*/;
  spuId?: string /*spu id*/;
  spuName?: string /*spu名称*/;
  spuNo?: string /*spu编号*/;
  spuSource?: string /*商品来源：SUPPLIER_SPU、CAPTAIN_ACTIVITY_SPU、KUAISHOU_CAPTAIN_ACTIVITY_SPU ...*/;
  standardCateId?: string /*行业大类ID*/;
  standardCateName?: string /*行业大类名称*/;
  standardFavorableRate?: string /*标准好评率*/;
  standardStore?: string /*标准店铺分*/;
  status?:
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED' /*场次货盘状态[SelectionRoundStatus]*/;
  suggestCommission?: string /*建议佣金*/;
  supplierBodySpecialAuditApprovalProcessNo?: string /*主体特批-审批流程编号*/;
  supplierBodySpecialAuditId?: string /*主体特批Id*/;
  supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
  supplierBodySpecialAuditStatus?:
    | 'WAIT_AUDIT'
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT'
    | 'INVALID' /*主体特批状态[SupplierBodySpecialAuditStatusEnum]*/;
  supplierBodySpecialAuditorRecordList?: Array<{
    auditTime?: string /*审批时间*/;
    auditor?: string /*审批人名称*/;
    auditorId?: string /*审批人ID*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    level?: string /*审批级别*/;
    modifier?: string /*修改人*/;
    rejectReason?: string /*驳回原因*/;
    roleType?:
      | 'BUSINESS'
      | 'SELECTION'
      | 'OPERATE'
      | 'MIDDLE_GROUND'
      | 'LEGAL'
      | 'ANCHOR' /*角色类型[RoleTypeEnum]*/;
    status?: string /*状态*/;
  }> /*主体特批审核人记录*/;
  supplierBodySpecialProcessUid?: string /*主体特批流程id*/;
  supplierId?: string /*商家ID*/;
  supplierOrgId?: string /*商家主体快照ID*/;
  supplierOrgName?: string /*商家主体名称*/;
  supplierStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  supplySource?:
    | 'HOME_BRED'
    | 'CROSS_BORDER'
    | 'IMPORT' /*来源，国产、跨境、进口[SupplySourceEnum]*/;
  talentId?: string /*达人ID*/;
  talentNo?: string /*达人编号*/;
  totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
  totalCommissionRate?: string /*总佣金*/;
  version?: number /*版本号*/;
};

/**
 *场次货盘详情
 */
export const liveGoodsInfo = (params: LiveGoodsInfoRequest) => {
  return Fetch<ResponseWithResult<LiveGoodsInfoResult>>('/iasm/public/selection/detail', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/detail') },
  });
};

export type IsSignAgreementRequest = {
  id?: string /*业务ID*/;
};

export type IsSignAgreementResult = boolean;

/**
 *校验并保存服务协议
 */
export const isSignAgreement = (params: IsSignAgreementRequest) => {
  return Fetch<ResponseWithResult<IsSignAgreementResult>>(
    '/iasm/public/selection/checkAndSaveServiceAgreement',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/checkAndSaveServiceAgreement') },
    },
  );
};

export type LiveRoomListRequest = {
  authorizeStatus?: 'DONE_AUTH' | 'WAIT_AUTH' | 'EXPIRE_AUTH' /*抖音授权状态[AuthorizeStatusEnum]*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部id*/;
  enableStatus?: 'DISABLE' | 'ENABLE' /*启用状态[EnableStatusEnum]*/;
  id?: string /*直播间id*/;
  institutionId?: string /*机构id*/;
  institutionName?: string /*机构名称*/;
  lateUpdateEndTime?: string /*最近更名结束时间*/;
  lateUpdateStartTime?: string /*最近更名开始时间*/;
  liveRoomName?: string /*直播间名称*/;
  platformEnum?:
    | 'DY'
    | 'TB'
    | 'KS'
    | 'JD'
    | 'PDD'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台[SourceTypeEnum]*/;
  size?: number /*分页大小*/;
  talentNo?: string /*达人编号*/;
};

export type LiveRoomListResult = {
  current?: number /*当前页码*/;
  hasNext?: boolean /*是否有下一页,true:有;false:无*/;
  pages?: number /*总页数*/;
  records?: Array<{
    account?: string /*抖币账号*/;
    authorId?: string /*直播间authorId*/;
    authorizeStatus?: number /*抖音授权状态*/;
    avatar?: string /*头像*/;
    brandFeeTechRate?: string /*基础服务费分佣比例*/;
    buId?: string /*事业部id*/;
    buName?: string /*事业部名称*/;
    businessLine?: string /*业务线*/;
    businessLineId?: string /*业务线id（系统代码code）*/;
    buyinId?: string /*百应ID*/;
    commissionTechRate?: string /*佣金率*/;
    cooperationMode?:
      | 'COLONEL'
      | 'DIRECT' /*合作模式:COLONEL-团长,DIRECT-定向[CooperationModeEnum]*/;
    createTime?: string /*创建时间*/;
    enableStatus?: number /*启用状态*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*id*/;
    institutionId?: string /*机构id*/;
    institutionName?: string /*机构名称*/;
    institutionNo?: string /*机构编号*/;
    name?: string /*直播间名称*/;
    openId?: string /*直播间openId*/;
    platform?: string /*平台*/;
    platformId?: string /*平台达人id*/;
    preCreateId?: string /*预创建记录主键id*/;
    projectManagerIdList?: Array<string> /*项目经理*/;
    projectManagerIds?: string /*项目经理*/;
    projectManagers?: Array<{
      accountDisableReason?: string /*账号禁用原因*/;
      accountName?: string /*账户名*/;
      accountState?: number /*账户状态  0：启用   1：禁用  2：离职*/;
      accountType?: number /*账号类型 0 b2b账号 1 s2b平台端账号 2 商家端账号 3供应商端账号*/;
      birthday?: string /*生日*/;
      bizRoleType?:
        | 'BUSINESS'
        | 'SELECTION'
        | 'OPERATE'
        | 'MIDDLE_GROUND'
        | 'LEGAL'
        | 'ANCHOR' /*业务角色[RoleTypeEnum]*/;
      companyInfoId?: string /*公司信息ID*/;
      createTime?: string /*创建时间*/;
      email?: string /*邮箱*/;
      employeeId?: string /*员工信息ID*/;
      employeeMobile?: string /*员工手机号*/;
      employeeName?: string /*员工姓名*/;
      feishuOpenId?: string /*飞书员工 open id*/;
      feishuUserId?: string /*飞书员工 userId*/;
      isMasterAccount?: number /*是否主账号 0、否 1、是*/;
      jobNo?: string /*工号*/;
      loginAccount?: string /*登录账号*/;
      position?: string /*职位*/;
      sex?: boolean /*性别，0：保密，1：男，2：女*/;
      tenantId?: string /*租户id,平台端是0*/;
      unionId?: string /*微信unionId,这个字段实际上要从user中获取*/;
    }> /*项目经理*/;
    sortingNum?: number /*排序权重*/;
    talentCompany?: string /*达人公司*/;
    talentId?: string /*达人id*/;
    talentNo?: string /*达人编号*/;
    updateTime?: string /*更新时间*/;
  }> /*结果列表*/;
  size?: number /*分页大小*/;
  total?: number /*总条数*/;
};

/**
 *直播间列表
 */
export const liveRoomList = (params: LiveRoomListRequest) => {
  return Fetch<ResponseWithResult<LiveRoomListResult>>('/iasm/public/liveRoom/list', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/liveRoom/list') },
  });
};

export type LiveRoomListAuthRequest = {
  deptId?: string /*事业部id*/;
  deptIds?: Array<string> /*事业部id批量查询*/;
  institutionIds?: Array<string> /*机构id批量查询*/;
  keyword?: string /*直播间名称/达人编号关键字*/;
  platformEnum?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*类型[PlatformEnum]*/;
};

export type LiveRoomListAuthResult = Array<{
  avatar?: string /*头像*/;
  buId?: string /*事业部id*/;
  buName?: string /*事业部名称*/;
  id?: string /*id*/;
  institutionId?: string /*达人所属机构ID*/;
  name?: string /*直播间名称*/;
  openId?: string /*直播间openId*/;
  platform?: string /*平台*/;
  sortingNum?: number /*排序权重*/;
  talentId?: string /*达人id*/;
  talentNo?: string /*达人编号*/;
}>;

/**
 *直播间列表(权限)
 */
export const liveRoomListAuth = (params: LiveRoomListAuthRequest) => {
  return Fetch<ResponseWithResult<LiveRoomListAuthResult>>(
    '/iasm/public/liveRoom/listVisibleLiveRoom',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/liveRoom/listVisibleLiveRoom') },
    },
  );
};

export type NonLiveGoodsInfoRequest = {
  id?: string /*业务ID*/;
};

export type NonLiveGoodsInfoResult = {
  afterSaleContent?: {
    freshCompensation?: string /*（生鲜）赔付标准 文本 行业大类为生鲜时展示该字段；提示：请填写赔付标准和有效期，例如：确认收货后30个工作日内，坏果坏1赔1，超过50%全赔*/;
    freshIsCold?: boolean /* （生鲜）是否冷链发货 true：是，false：否*/;
    installationService?: string /* （数码家电）售后附加服务 文本 行业大类为数码家电时展示该字段；提示：免人工安装费用，额外收取xx元材料费，支持延迟发货*/;
    insuranceTime?: string /* （数码家电）质保时长  文本 行业大类为数码家电时展示该字段*/;
    modes?: Array<
      'NONE' | 'NO_REASON' | 'FREE_FREIGHT_INSURANCE' | 'OTHER'
    > /* 售后服务 多选数组 必填，选项：无、7天无理由、赠送运费险、其他[AfterSaleTypeEnum]*/;
    others?: string /* 其他售后服务文本 必填，售后服务选其他时，必填该字段*/;
    productionDate?: string /* 生产日期 文本 行业大类为食品饮料、美妆护肤则展示，例如：晚于2023年6月*/;
    shelfLife?: string /* 产品保质期 文本 行业大类为食品饮料、美妆护肤则展示，例如：6个月、避光保存7天*/;
  } /*售后信息*/;
  applyChannel?: string /*报名渠道，枚举项：招商计划；货盘邀请链接；选品邀请链接*/;
  auditDetailMap?: {
    [key: string]: {
      auditOpinion?: string /*审核意见*/;
      auditState?:
        | 'PASS'
        | 'NO_PASS'
        | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
      bizType?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'GOODS'
        | 'SHOP'
        | 'BP_BRAND'
        | 'BP_GOODS'
        | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
      expirationDate?: string /*法务审核有效期*/;
      isBrandWhitelisted?: boolean /*是否白名单B*/;
      itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
      remark?: string /*备注*/;
    };
  } /*法务审核明细, Key是资质项目版本ID[QualificationBizTypeEnum]*/;
  bestSignCompanyStatus?:
    | 'WAIT_CHECK'
    | 'CHECKING'
    | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
  bpAssistantIds?: string /*商务助理ID, 英文逗号分割*/;
  bpId?: string /*商务ID*/;
  bpName?: string /*商务名称*/;
  bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[NonLiveSelectionBpStatus]*/;
  brandFee?: string /*基础服务费，坑位费*/;
  brandFeeTechRate?: string /*基础服务费平台服务费率*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  captainPromotionLink?: {
    captainId?: string;
    creator?: string;
    doudianGoodsId?: string;
    doudianId?: string;
    doudianName?: string;
    effectEndTime?: string;
    effectStartTime?: string;
    errorDetail?: string;
    gmtCreated?: string;
    gmtModified?: string;
    id?: string;
    liveTime?: string;
    modifier?: string;
    operatorName?: string;
    promotionLink?: string;
    promotionLinkId?: string;
    reason?: number;
    sellPrice?: string;
    serviceFeeRate?: string;
    source?: number;
    spuId?: string;
    spuImg?: string;
    spuName?: string;
    spuNo?: string;
    status?: number;
    talentCommissionRate?: string;
    type?: number;
  } /*抖音推广链接*/;
  cateId?: string /*类目ID*/;
  cateName?: string /*类目名称*/;
  cateNamePath?: string /*类目全路径名称*/;
  commentGood?: string /*商品好评数*/;
  commentTotal?: string /*商品评价数*/;
  commissionRate?: string /*线上佣金比例*/;
  commissionRateOffline?: string /*线下佣金比例*/;
  commissionTechRate?: string /*佣金平台服务费率*/;
  coopMode?: string /*合作方式，枚举项：线上合作、线下合作*/;
  coopOrderNo?: string /*合作订单号*/;
  cooperationOrderStatus?:
    | 'TALENT_CONFIRMING'
    | 'END_OF_COOPERATION'
    | 'IN_COOPERATION'
    | 'CLOSED' /*履约状态[NonLiveCooperationOrderStatusEnum]*/;
  creator?: string /*创建人*/;
  deptId?: string /*事业部ID*/;
  discountContent?: {
    giftInfos?: Array<{
      condition?:
        | 'IMMEDIATE'
        | 'REQUIREMENTS' /*赠送条件，下单即送、指定条件赠送[GiftConditionTypeEnum]*/;
      conditionRemark?: string /* 赠送条件备注*/;
      giftType?: 'ALL' | 'PART' /*赠品生效规格[GiftTypeEnum]*/;
      image?: string /*赠品图片*/;
      link?: string /*赠品链接*/;
      name?: string /*赠品名称*/;
      onSale?: boolean /*是否在售*/;
      price?: string /*赠品价格*/;
      skuNoList?: Array<string> /*赠品生效的sku编号*/;
    }> /*赠品信息*/;
    remark?: string /* 优惠备注*/;
    type?: Array<
      'FULL_REDUCTION' | 'IMMEDIATE_REDUCTION' | 'COUPON' | 'OTHER' | 'NONE'
    > /* 优惠方式, 必填，选项：满减、立减、优惠券、其他、无（该选项和其他选项互斥，选了无则不允许选其他选项[DiscountTypeEnum]*/;
  } /*优惠信息*/;
  endTime?: string /*合作结束时间*/;
  favorableRate?: string /*好评率*/;
  generationMethod?: string /*生成方式*/;
  gmtCreated?: string /*创建时间*/;
  groupId?: string /*项目组ID*/;
  groupName?: string /*项目组名称*/;
  hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
  highRiskGrant?: boolean /*高风险特批 false：否 true：是*/;
  id?: string /*id*/;
  image?: string /*商品主图链接*/;
  institutionId?: string /*机构id*/;
  interestPoints?: string /*利益点*/;
  investmentNo?: string /*招商计划编号*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalAuditorName?: string /*法务审核人名称*/;
  legalReason?: string /*法务驳回原因*/;
  legalStatus?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*法务审核状态，INIT:待审核、PASS:通过、REJECT:驳回[NonLiveSelectionQualificationRiskLevel]*/;
  link?: string /*商品参考链接*/;
  linkBak?: string /*商品参考链接（手动补充）*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  liveMaxPrice?: string /*最大价 */;
  liveMinPrice?: string /*最小价*/;
  liveRoomId?: string /*直播间ID*/;
  liveRoomName?: string /*直播间名称*/;
  logisticsContent?: {
    deliveryCycle?: string /* 发货周期, 必填，xx天内发货，默认2天*/;
    deliveryMode?: 'SPOT' | 'PRESALE' /* 发货模式, 必填，现货/预售[DeliveryModeEnum]*/;
    giftDeliveryMode?: boolean /* 主品赠品发货情况, 赠品是否随主品一起发货，true：是，false：否*/;
    giftLogisticsNo?: string /* 赠品寄样单号，如果选择 主赠分别发货 显示本字段*/;
    logisticsNo?: string /* 主品寄样单号*/;
  } /*物流信息*/;
  maxPrice?: string /*最大价 */;
  minPrice?: string /*最小价*/;
  no?: string /*编号, CCHP+TB*/;
  noQualificationRequired?: string /*是否免资质, 0不免除，1免除*/;
  paymentOrderStatus?:
    | 'NO_PAYMENT_REQUIRED'
    | 'PENDING_PAYMENT'
    | 'PAYING'
    | 'PARTIAL_PAYMENT'
    | 'PAYMENT_FAIL'
    | 'FULL_PAYMENT'
    | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
  paymentVoucher?: {
    comment?: string /*备注*/;
    confirmedDate?: string /*确认时间*/;
    confirmedName?: string /*财务名称*/;
    confirmedPerson?: string /*确认人*/;
    cooperationOrderId?: string /*合作订单id*/;
    gmtCreated?: string /*创建时间*/;
    id?: string;
    ourMainCompany?: string /*我方收款主体*/;
    paymentAmount?: string /*回款总金额*/;
    paymentDate?: string /*付款日期*/;
    paymentFileList?: Array<string> /*付款凭证文件*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderId?: string /*合作订单付款订单id*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentReason?: string /*付款凭证问题原因*/;
    paymentStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态 待确认 已确认 有问题[PaymentVoucherStatus]*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    supplierMainCompany?: string /*商家付款主体*/;
  } /*付款凭证信息*/;
  platformShopId?: string /*平台店铺ID(抖店/快手等店铺ID)*/;
  platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
  platformSource?: string /*商品来源平台*/;
  platformSpuId?: string /*平台商品ID*/;
  platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
  promotionLink?: string /*推广链接*/;
  promotionLinkId?: string /*推广链接id*/;
  sectionFee?: string /*切片费*/;
  sellingPoints?: string /*商品主要卖点*/;
  serviceAgreementId?: string /*服务协议id*/;
  serviceType?:
    | 'LIVE'
    | 'SHOP_WINDOW'
    | 'VIDEO_CLIP'
    | 'SLICE'
    | 'TB_CUSTOMER' /*服务类型[ServiceTypeEnum]*/;
  shelfLife?: string /*食品保质期*/;
  shopId?: string /*店铺ID*/;
  shopPoints?: string /*店铺分*/;
  skus?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    purchasePrice?: string /*采购价（含税）*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
    taxRate?: string /*税率（%）*/;
  }> /*sku信息*/;
  sourceOrderNo?: string /*来源单据编码*/;
  sourceOrderType?: string /*来源单据类型*/;
  spuFocus?: string /*重点展示需求*/;
  spuId?: string /*spu id*/;
  spuName?: string /*spu名称*/;
  spuNo?: string /*spu编号*/;
  spuSource?: string /*商品来源：SUPPLIER_SPU、CAPTAIN_ACTIVITY_SPU、KUAISHOU_CAPTAIN_ACTIVITY_SPU ...*/;
  standardCateId?: string /*行业大类ID*/;
  standardCateName?: string /*行业大类名称*/;
  standardFavorableRate?: string /*标准好评率*/;
  standardStore?: string /*标准店铺分*/;
  startTime?: string /*合作开始时间*/;
  status?:
    | 'WAIT_AUDIT'
    | 'IN_COOPERATE'
    | 'END_COOPERATE'
    | 'LOSE_EFFICACY'
    | 'DROP_PRODUCT' /*场次货盘状态[NonLiveSelectionRoundStatus]*/;
  supplierId?: string /*商家ID*/;
  supplierOrgId?: string /*商家主体快照ID*/;
  supplierOrgName?: string /*商家主体名称*/;
  supplySource?: string /*来源，国产、跨境、进口*/;
  talentId?: string /*达人ID*/;
  talentNo?: string /*达人编号*/;
  totalCommissionRate?: string /*总佣金*/;
  version?: number /*版本号*/;
};

/**
 *非直播流程详情
 */
export const nonLiveGoodsInfo = (params: NonLiveGoodsInfoRequest) => {
  return Fetch<ResponseWithResult<NonLiveGoodsInfoResult>>('/iasm/public/nonLiveSelection/detail', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/detail') },
  });
};

export type CheckLinkStatusRequest = {
  id?: string /*id*/;
  linkCheckRemark?: string /*链接确认备注*/;
  linkCheckStatus?:
    | 'WAIT_CHECK'
    | 'NO_PROMOTION'
    | 'CAN_PROMOTION' /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
  resourceIds?: Array<string> /*链接附件id列表*/;
};

export type CheckLinkStatusResult = boolean;

/**
 *确认链接状态
 */
export const checkLinkStatus = (params: CheckLinkStatusRequest) => {
  return Fetch<ResponseWithResult<CheckLinkStatusResult>>(
    '/iasm/public/selectionProcessKanban/checkLinkStatus',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selectionProcessKanban/checkLinkStatus') },
    },
  );
};

export type CheckLinkStatusDetailRequest = {
  id?: string /*业务ID*/;
};

export type CheckLinkStatusDetailResult = {
  afterSaleContent?: string /*售后信息，json对象*/;
  applyBillType?:
    | 'FORWARD'
    | 'REVERSE'
    | 'PLATFORM_SUPPLIER_FORWARD' /*类型 正向 FORWARD 逆向 REVERSE 平台型商家(自动生成)报名单 PLATFORM_SUPPLIER_FORWARD[ApplyTypeEnum]*/;
  applyChannel?:
    | 'INVESTMENT_PLAN_DIRECT'
    | 'INVESTMENT_PLAN_INVITE'
    | 'INVESTMENT_PLAN_LIVE_ROUND_INVITE' /*报名渠道，枚举项：招商计划；货盘邀请链接；选品邀请链接[ApplyChannelEnum]*/;
  applyRecordId?: string /*报名记录ID*/;
  applyRecordNo?: string /*报名记录编号*/;
  auditDetailMap?: {
    [key: string]: {
      auditOpinion?: string /*审核意见*/;
      auditState?:
        | 'PASS'
        | 'NO_PASS'
        | 'NONE' /*审核状态:合格-PASS,不合格-NO_PASS,未处理-NONE[QualificationAuditStateEnum]*/;
      bizType?:
        | 'SUPPLIER'
        | 'BRAND'
        | 'GOODS'
        | 'SHOP'
        | 'BP_BRAND'
        | 'BP_GOODS'
        | 'BP_SHOP' /*资质业务类型:商家,店铺品牌,商品[QualificationBizTypeEnum]*/;
      expirationDate?: string /*法务审核有效期*/;
      isBrandWhitelisted?: boolean /*是否白名单B*/;
      itemVersionId?: string /*资质项版本ID, 版本ID一致则可复用*/;
      remark?: string /*备注*/;
    };
  } /*法务审核明细, Key是资质项目版本ID[QualificationBizTypeEnum]*/;
  bestSignCompanyStatus?:
    | 'WAIT_CHECK'
    | 'CHECKING'
    | 'CHECKED' /*商家认证状态[BestSignCompanyStatusEnum]*/;
  bpAssistantIds?: string /*商务助理ID, 英文逗号分割*/;
  bpId?: string /*商务ID*/;
  bpName?: string /*商务名称*/;
  bpStatus?: 'INIT' | 'PASS' | 'REJECT' | 'CANCEL' /*商务状态[SelectionBpStatus]*/;
  brandFee?: string /*基础服务费，坑位费*/;
  brandFeeTechRate?: string /*基础服务费平台服务费率*/;
  brandId?: string /*品牌ID*/;
  brandName?: string /*品牌名称*/;
  captainPromotionLink?: {
    captainId?: string;
    creator?: string;
    doudianGoodsId?: string;
    doudianId?: string;
    doudianName?: string;
    effectEndTime?: string;
    effectStartTime?: string;
    errorDetail?: string;
    gmtCreated?: string;
    gmtModified?: string;
    id?: string;
    liveTime?: string;
    modifier?: string;
    operatorName?: string;
    promotionLink?: string;
    promotionLinkId?: string;
    reason?: number;
    sellPrice?: string;
    serviceFeeRate?: string;
    source?: number;
    spuId?: string;
    spuImg?: string;
    spuName?: string;
    spuNo?: string;
    status?: number;
    talentCommissionRate?: string;
    type?: number;
  } /*抖音推广链接*/;
  cateId?: string /*类目ID*/;
  cateName?: string /*类目名称*/;
  cateNamePath?: string /*类目全路径名称*/;
  categoryCar?: string /*类目车*/;
  comment?: string /*备注*/;
  commentGood?: string /*商品好评数*/;
  commentTotal?: string /*商品评价数*/;
  commissionRate?: string /*线上佣金比例*/;
  commissionRateOffline?: string /*线下佣金比例*/;
  commissionTechRate?: string /*佣金平台服务费率*/;
  complianceAuditor?: string /*合规审核人ID*/;
  complianceAuditorName?: string /*合规审核人名称*/;
  complianceNo?: string /*合规审批编号*/;
  complianceStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*合规审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  coopFrameworkId?: string /*合作框架id*/;
  coopMode?: string /*合作方式，枚举项：线上合作、线下合作*/;
  coopOrderNo?: string /*合作订单号*/;
  cooperationGuaranteed?: {
    accountNo?: string /*机构银行账号*/;
    achievedCalculationType?:
      | 'BY_PAYMENT'
      | 'BY_SETTLEMENT' /*达成计算方式(按支付-BY_PAYMENT,按结算-BY_SETTLEMENT)[AchievedCalculationTypeEnum]*/;
    agreedRefundFlag?: number /*是否约定退款*/;
    appointReceiptInstitutionId?: string /*指定收款机构id*/;
    appointReceiptInstitutionName?: string /*指定收款机构名称*/;
    bankName?: string /*机构开户行*/;
    bestSignContractId?: string /*上上签合同ID*/;
    bpId?: string /*商务id*/;
    bpName?: string /*商务名称*/;
    brandFee?: string /*基础佣金（原固定服务费）*/;
    brandFeeLatestPaymentTime?: string /*基础佣金最晚支付日期*/;
    brandIds?: Array<string> /*品牌id*/;
    brandInfo?: string /*品牌信息*/;
    brandInfoDTOS?: Array<{
      brandId?: string /*品牌id*/;
      brandName?: string /*品牌名称*/;
    }> /*品牌信息*/;
    commissionRate?: string /*线上佣金比例*/;
    contractApproveStatus?:
      | 'INIT'
      | 'WAIT_PENDING'
      | 'PENDING'
      | 'PASS'
      | 'REJECTED' /*合同审批状态（WAIT_PENDING：待审批；PENDING：审批中；PASS：已通过；REJECTED：已驳回）[ContractApproveStatusEnum]*/;
    contractInfo?: string /*合同信息*/;
    contractInfoList?: Array<string> /*合同信息*/;
    contractStatus?:
      | 'WAIT_START'
      | 'SIGNING'
      | 'SIGNED'
      | 'WITHDRAWAL' /*合同状态（WAIT_START:待开始；SIGNING:签署中；SIGNED:已签署）[ContractStatusEnum]*/;
    contractType?:
      | 'ON_LINE'
      | 'OFF_LINE' /*合同类型（ON_LINE：线上合同；OFF_LINE：线下合同）[GuaranteedContractTypeEnum]*/;
    coopActualEndTime?: string /*合作实际结束时间*/;
    coopAdvanceEndTime?: string /*合作提前结束时间*/;
    coopEndTime?: string /*合作结束时间*/;
    coopExtendEndTime?: string /*保量延长期*/;
    coopManualEndTime?: string /*合作手动结束时间*/;
    coopStartTime?: string /*合作开始时间*/;
    creator?: string /*创建人*/;
    creatorName?: string /*创建人名称*/;
    delFlag?: number /*删除标记*/;
    deptId?: string /*事业部id(已废弃)*/;
    deptInfo?: string /*事业部信息*/;
    deptInfoDTOS?: Array<{
      deptId?: string /*事业部id*/;
      deptName?: string /*事业部名称*/;
    }> /*事业部信息*/;
    description?: string /*合同描述*/;
    estimatedSettlementGmv?: string /*保量实际GMV(原名：预估结算gmv)*/;
    estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
    finishedGmv?: string /*保量预估GMV(原名：已完成目标gmv（原名：已完成保量gmv）)*/;
    finishedRate?: string /*保量预估GMV比例*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    goodsKeyWordInfoDTOS?: Array<{
      brandInfoDTOS?: Array<{
        brandId?: string /*品牌id*/;
        brandName?: string /*品牌名称*/;
      }> /*品牌信息*/;
      goodsKeyWord?: string /*商品关键词*/;
      shopInfoDTOS?: Array<{
        shopId?: string /*店铺id*/;
        shopName?: string /*店铺名称*/;
      }> /*店铺信息*/;
    }> /*商品关键字信息*/;
    goodsKeywordInfo?: string /*商品关键字信息*/;
    guaranteeBrandFeeRate?: string /*保量基础佣金*/;
    guaranteeQuantityId?: string /*保量合作id(已废弃)*/;
    guaranteedGmv?: string /*目标gmv（原保量gmv）*/;
    guaranteedGmvAdvanceAchievedRule?:
      | 'AUTO_FINISH'
      | 'GO_ON' /*目标gmv提前达成计算规则(合作自动终止；乙方继续为甲方提供营销服务，对超出目标GMV部分的销售额，由甲方向乙方支付额外的激励佣金，激励佣金=超出的销售额/（目标GMV/基础佣金总额）)[GuaranteedGmvAdvanceAchievedRuleEnum]*/;
    guaranteedGmvFailAchievedRule?:
      | 'B_REFUNDS_UNUSED_BASIC_COMMISSION_TO_A'
      | 'EXTEND_COOPERATION_PERIOD'
      | 'UNTIL_ACHIEVEMENT_GOAL_EXTEND_COOPERATION_PERIOD' /*目标gmv未能达成计算规则（乙方向甲方退还未消耗的基础佣金；延长合作期限；合作期限延长至目标gmv达成之日）[GuaranteedGmvFailAchievedRuleEnum]*/;
    id?: string /*保量合同id*/;
    institutionId?: string /*机构id（弃用）*/;
    institutionIds?: Array<string> /*机构id集合*/;
    institutionInfoDTOS?: Array<{
      institutionId?: string /*机构id*/;
      institutionName?: string /*机构名称*/;
      institutionOrganizationName?: string /*机构实名主体名称*/;
      institutionRelNameVersion?: string /*机构实名快照版本号*/;
    }> /*机构信息*/;
    institutionName?: string /*机构名称（弃用）*/;
    institutionOrganizationName?: string /*机构实名主体名称（弃用）*/;
    institutionRelNameVersion?: string /*机构实名快照版本号（弃用）*/;
    invoiceContent?:
      | 'MODERN_SERVICE_TECHNOLOGY_SERVICE_FEE'
      | 'MODERN_SERVICE_SERVICE_FEE' /*发票内容[InvoiceContentTypeEnum]*/;
    invoiceRate?: string /*发票税率*/;
    isWhitelist?: boolean /*是否白名单（0：否；1：是）*/;
    liveRoomInfo?: string /*直播间信息*/;
    liveRoomInfos?: Array<string> /*直播间id*/;
    liveRoomPossessType?:
      | 'ALL'
      | 'INCLUDE' /*直播间拥有类型（ALL:全部；INCLUDE：包含)[LiveRoomPossessTypeEnum]*/;
    mediaSigningStatus?:
      | 'SIGNED'
      | 'UNSIGNED' /*新媒体服务协议签署状态(SIGNED：已签署；UNSIGNED：未签署)；已废弃[MediaSigningStatusEnum]*/;
    modifier?: string /*更新人*/;
    modifierName?: string /*更新人名称*/;
    name?: string /*保量合同名称*/;
    no?: string /*保量合同编号*/;
    oaRequestId?: string /*oa请求ID「审批流」*/;
    originalCoopGuaranteedId?: string /*原保量合作记录主键ID*/;
    originalCoopGuaranteedNo?: string /*原保量合作记录编号*/;
    payDurationType?:
      | 'T_PAY'
      | 'T_ZERO'
      | 'T_TWO'
      | 'T_FIFTEEN'
      | 'T_SEVEN'
      | 'BY_SETTLEMENT' /*支付天数（T_PAY:支付时；T+0:支付24h内；T+2:支付48h内）[PayDurationTypeEnum]*/;
    paymentAmountTotal?: string /*累计回款金额*/;
    paymentType?:
      | 'SELF_FUNDED'
      | 'THIRD_PARTY' /*付款方式（自行支付/代付）[ContractPaymentTypeEnum]*/;
    platform?: string /*平台*/;
    relVOS?: Array<{
      brandId?: string /*品牌id*/;
      cooperationGuaranteedId?: string /*保量合作记录主键id*/;
      cooperationGuaranteedNo?: string /*保量合作记录编号*/;
      creator?: string /*创建人*/;
      gmtCreated?: string;
      gmtModified?: string;
      id?: string /*id*/;
      legalName?: string /*供应商主体名称*/;
      liveDate?: string /*直播时间*/;
      liveRoomId?: string /*直播间id*/;
      liveRoomName?: string /*直播间名称*/;
      modifier?: string /*更新人*/;
      openId?: string /*直播间open_id*/;
      platformId?: string /*平台id*/;
      platformSource?: string /*商品来源平台*/;
      platformSpuId?: string /*平台商品ID*/;
      relFlag?: number /*关联标记（是否关联， 0 否  1 是）*/;
      selectionNo?: string /*场次货盘编号*/;
      shopId?: string /*店铺id*/;
      spuName?: string /*spu名称*/;
      spuNo?: string /*spu编号*/;
      supplierId?: string /*商家id*/;
      talentId?: string /*达人id*/;
    }> /*关联关系*/;
    settlementIntervalType?:
      | 'MONTH'
      | 'QUARTER'
      | 'YEAR' /*结算周期[ContractSettlementIntervalTypeEnum]*/;
    shopInfo?: string /*店铺信息*/;
    shopInfoDTOS?: Array<{
      shopId?: string /*店铺id*/;
      shopName?: string /*店铺名称*/;
    }> /*店铺信息*/;
    status?:
      | 'DRAFT'
      | 'WAIT_START'
      | 'IN_COOPERATION'
      | 'FINISHED'
      | 'WITHDRAWAL'
      | 'TERMINATE' /*合作状态（DRAFT：暂存；WAIT_START:待开始；IN_COOPERATION:合作中；FINISHED:已结束）[GuaranteedStatusEnum]*/;
    stimulateCommission?: string /*激励佣金*/;
    stimulateCommissionRate?: string /*激励佣金比例*/;
    supplierCompanyNameInfo?: string /*商家主体名称信息*/;
    supplierCompanyNameInfos?: Array<string> /*商家主体名称信息*/;
    supplierInfo?: string /*商家信息*/;
    supplierInfoDTOS?: Array<{
      contactAddress?: string /*联系地址*/;
      contactMail?: string /*联系邮箱*/;
      contactMobile?: string /*联系电话*/;
      contactName?: string /*联系人*/;
      supplierCompanyName?: string /*商家主体名称*/;
      supplierId?: string /*商家id*/;
    }> /*商家信息*/;
    thirdPartyPayerName?: string /*代付方名称*/;
    thirdPartyPayerUscc?: string /*代付方统一社会信用代码*/;
    type?:
      | 'SUPPLIER'
      | 'BRAND'
      | 'SHOP'
      | 'SHOP_BRAND'
      | 'GOODS_KEYWORD' /*保量类型（SUPPLIER：按商家主体；BRAND：按品牌；SHOP：按店铺；SHOP_BRAND：按店铺品牌；GOODS_KEYWORD：商品关键字）[GuaranteedTypeEnum]*/;
    version?: number /*版本号*/;
  } /*保量信息*/;
  cooperationMode?: string /*合作模式:COLONEL-团长,DIRECT-定向*/;
  cooperationOrderStatus?:
    | 'TO_BE_ADD'
    | 'TO_BE_FULFILL_AGREEMENT'
    | 'FULFILL_AGREEMENT'
    | 'CLOSED'
    | 'AGREEMENT_CONFIRMED' /*履约状态[CooperationOrderStatusEnum]*/;
  creator?: string /*创建人*/;
  depositAmount?: string /*定金金额*/;
  deptId?: string /*事业部ID*/;
  discountContent?: string /*优惠信息，json对象*/;
  dropProductReason?: string /*掉品原因*/;
  estimatedSettlementGmvRate?: string /*保量实际GMV比例*/;
  favorableRate?: string /*好评率*/;
  favorableRateRefreshTime?: string /*好评率刷新时间*/;
  finishedRate?: string /*保量预估GMV比例*/;
  firstCateParentId?: string /*一级类目id*/;
  frameworkCoopModel?: Array<
    'GUARANTEED_GMV_MODE' | 'GUARANTEED_LIVE_ROUND_MODE' | 'GUARANTEED_SLICE_MODE'
  > /*合作模式多选：1保 GMV模式，2保场次排期模式，3前两种多选[CooperationFrameworkCoopModelEnum]*/;
  frameworkGmvFlag?: boolean /*是否计入年框gmv*/;
  frameworkRoundFlag?: boolean /*是否计入年框场次*/;
  generationMethod?: string /*生成方式*/;
  gmtCreated?: string /*创建时间*/;
  gmtModified?: string /*更新时间*/;
  gmvCommissionRate?: string /*年框GMV总分佣比例*/;
  gmvPlatformCommissionRate?: string /*年框GMV平台分佣比例*/;
  goodsQualityScore?: string /*商品质量分*/;
  goodsQualityScoreApprovalProcessNo?: string /*商品质量分审批流程编号*/;
  goodsQualityScoreProcessUid?: string /*商品质量分OA流程id*/;
  groupId?: string /*项目组ID*/;
  groupName?: string /*项目组名称*/;
  guaranteeBrandFeeRate?: string /*保量基础佣金*/;
  guaranteeProportionFlag?: boolean /*是否保比*/;
  guaranteeProportionId?: string /*保比记录id*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  guaranteeQuantityId?: string /*保量id*/;
  hideCommissionFlag?: boolean /*隐藏佣金及基础服务费*/;
  highRiskGrant?: boolean /*高风险特批 0：否 1：是*/;
  id?: string /*场次货盘ID*/;
  image?: string /*商品主图链接*/;
  institutionId?: string /*机构id*/;
  interestPoints?: string /*利益点*/;
  investmentId?: string /*招商计划ID*/;
  investmentNo?: string /*招商计划编号*/;
  investmentTitle?: string /*招商计划名称*/;
  isDisplayQualityScore?: boolean /*是否展示商品质量分 true 是 false 否*/;
  isGuarantee?: boolean /*是否担保交易 false：不担保 true：担保*/;
  isPriceProtection?: boolean /*商品价格保护 0：否 1：是*/;
  isSampleProvided?: boolean /*样品提供*/;
  labelConfigInfo?: Array<{
    labelName?: string /*标签名称*/;
    labelOptionId?: string /*标签选项配置id*/;
    labelOptionName?: string /*标签选项名称*/;
  }> /*标签配置信息*/;
  labelList?: Array<{
    chooseMethod?:
      | 'RADIO'
      | 'CHECKBOX' /*自定义标签选择方式 RADIO-单选 CHECK_BOX-多选[LabelChooseMethodEnum]*/;
    id?: string /*id*/;
    labelGroupName?: string /*标签名称*/;
    labelOptionList?: Array<{
      id?: string /*id*/;
      labelOption?: string /*标签内容名称*/;
    }> /*标签内容*/;
    requiredFlag?: number /* 0-非必填 1-必填*/;
    status?: 'ENABLE' | 'DISABLE' /*ENABLE-启用 UNABLE-禁用[LabelStatusEnum]*/;
  }> /*标签*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalAuditorName?: string /*法务审核人名称*/;
  legalReason?: string /*法务驳回原因*/;
  legalStatus?:
    | 'QUALIFIED'
    | 'HIGH'
    | 'HIGH_SPECIAL'
    | 'MIDDLE'
    | 'LOW'
    | 'PASS'
    | 'NONE' /*法务审核状态[SelectionQualificationRiskLevel]*/;
  link?: string /*商品参考链接*/;
  linkBak?: string /*商品参考链接（手动补充）*/;
  linkCheckRemark?: string /*链接确认备注*/;
  linkCheckStatus?:
    | 'WAIT_CHECK'
    | 'NO_PROMOTION'
    | 'CAN_PROMOTION' /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  liveDate?: string /*场次ID*/;
  liveMaxPrice?: string /*最大价 */;
  liveMinPrice?: string /*最小价*/;
  livePlatformSpuId?: string /*上播商品id*/;
  liveRoomId?: string /*直播间id*/;
  liveRoundId?: string /*场次ID*/;
  liveRoundInfo?: {
    anchorType?:
      | 'PRIMARY_ANCHOR'
      | 'SECONDARY_ANCHOR'
      | 'TALENT_ANCHOR' /*主播类型[AnchorTypeEnum]*/;
    liveDate?: string /*直播日期*/;
    liveEndTime?: string /*直播结束时间*/;
    liveRoomId?: string /*直播间Id*/;
    liveRoomImg?: string /*直播间头像*/;
    liveRoomName?: string /*直播间名称*/;
    liveRoundId?: string /*直播场次ID*/;
    liveRoundName?: string /*直播场次名称*/;
    liveStartTime?: string /*直播开始时间*/;
    subject?: string /*场次主题*/;
  } /*场次信息*/;
  liveServiceType?: string /*直播服务类型(讲解类型)*/;
  liveServiceTypeId?: string /*直播服务类型ID*/;
  logisticsContent?: string /*物流信息，json对象*/;
  lowCommissionAuditStatus?:
    | 'NONE'
    | 'INIT'
    | 'PENDING'
    | 'APPROVED'
    | 'REJECTED'
    | 'CANCELED'
    | 'DELETED'
    | 'REVERTED'
    | 'OVERTIME_CLOSE'
    | 'OVERTIME_RECOVER' /*低佣审核状态[LowCommissionAuditStatusEnum]*/;
  lowCommissionAuditTime?: string /*低佣审核时间*/;
  lowCommissionAuditor?: string /*低佣审核人*/;
  lowCommissionAuditorName?: string /*低佣审核姓名*/;
  lowCommissionFlowNo?: string /*低佣审核流程编号*/;
  lowCommissionUniqueCode?: string /*低佣请求id*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxPrice?: string /*最大价 */;
  minPrice?: string /*最小价*/;
  needSupplierBodySpecialAudit?: boolean /*是否命中主体特批规则*/;
  ninetyDayLiveCount?: string /*90天已播场次数*/;
  ninetyDayLiveZeroCount?: string /*90天内销量=0的场次数*/;
  ninetyDayLiveZeroRate?: string /*低效品占比*/;
  no?: string /*编号, CCHP+TB*/;
  noQualificationRequired?: string /*是否免资质, 0不免除，1免除*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorAuditorName?: string /*运营审核人名称*/;
  operatorReason?: string /*运营驳回原因*/;
  operatorStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*运营审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  orderProcessStatus?: string /*单据处理状态 生成中： Generating ，已生成： generated ，待关闭 Wait_Close */;
  paymentOrderStatus?:
    | 'NO_PAYMENT_REQUIRED'
    | 'PENDING_PAYMENT'
    | 'PAYING'
    | 'PARTIAL_PAYMENT'
    | 'PAYMENT_FAIL'
    | 'FULL_PAYMENT'
    | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
  paymentVoucher?: {
    comment?: string /*备注*/;
    confirmedDate?: string /*确认时间*/;
    confirmedName?: string /*财务名称*/;
    confirmedPerson?: string /*确认人*/;
    cooperationOrderId?: string /*合作订单id*/;
    gmtCreated?: string /*创建时间*/;
    id?: string;
    ourMainCompany?: string /*我方收款主体*/;
    paymentAmount?: string /*回款总金额*/;
    paymentDate?: string /*付款日期*/;
    paymentFileList?: Array<string> /*付款凭证文件*/;
    paymentMethod?:
      | 'ONLINE_BANK_TRANSFER'
      | 'OFFLINE_BANK_TRANSFER'
      | 'OFFLINE_VOUCHER'
      | 'COOP_FRAMEWORK_PAY'
      | 'ADVANCE_PAYMENT_PAY' /*付款方式[PaymentMethodEnum]*/;
    paymentOrderId?: string /*合作订单付款订单id*/;
    paymentOrderStatus?:
      | 'NO_PAYMENT_REQUIRED'
      | 'PENDING_PAYMENT'
      | 'PAYING'
      | 'PARTIAL_PAYMENT'
      | 'PAYMENT_FAIL'
      | 'FULL_PAYMENT'
      | 'SETTLED' /*付款状态[PaymentOrderStatusEnum]*/;
    paymentReason?: string /*付款凭证问题原因*/;
    paymentStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态 待确认 已确认 有问题[PaymentVoucherStatus]*/;
    paymentVoucherStatus?:
      | 'WAIT_CONFIRM'
      | 'CONFIRMED'
      | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
    supplierMainCompany?: string /*商家付款主体*/;
  } /*付款凭证信息*/;
  platformShopId?: string /*平台店铺ID(抖店/快手等店铺ID)*/;
  platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*商品来源平台[PlatformEnum]*/;
  platformSpuId?: string /*平台商品ID*/;
  platformSpuIdBak?: string /*平台商品ID（手动补充）*/;
  playBackStatus?: 'PLAYED' | 'NOT_PLAYER' /*上播状态[PlayBackStatusEnum]*/;
  preCreateCoopOrder?: boolean /*是否需要前置生成合作订单*/;
  preSaleStock?: number /*预售库存*/;
  price?: string /*日常价*/;
  promotionLink?: string /*推广链接*/;
  promotionLinkBak?: string /*推广链接(手动补充)*/;
  promotionLinkId?: string /*推广链接id*/;
  qualityScoreAuditStatus?:
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT' /*质量分审核状态[QualityScoreAuditStatusEnum]*/;
  qualityScoreAuditor?: string /*商品质量分审批人*/;
  reasonDetail?: string /*详细原因*/;
  reasonType?:
    | 'BREAK_PRICE_REDUCE_COMMISSION'
    | 'BRAND_FEE_AFFECTS'
    | 'SUPPLEMENT_LIVE'
    | 'QUANTITY_GUARANTEED'
    | 'HIS_COMMERCIAL_CLAUSE_REPORTING_ERROR'
    | 'BRAND_REDUCE_COMMISSION'
    | 'FRAMEWORK_COOPERATION'
    | 'WELFARE_GOODS'
    | 'OTHER_LESS_THAN_LUO'
    | 'OTHER' /*低佣原因[LowCommissionReasonTypeEnum]*/;
  receiveSampleRegister?: string /*收样登记*/;
  resourceUrls?: Array<{
    resourceId?: string /*链接附件id*/;
    resourceUrl?: string /*链接附件url*/;
  }> /*链接附件列表*/;
  returnSampleRegister?: string /*退样登记*/;
  reverseDemandId?: string /*反需人员Id*/;
  reverseDemandName?: string /*反需人员姓名*/;
  reverseDemandTag?: boolean /*反需标签,0:否,1:是*/;
  roundTotalFees?: string /*场次费用*/;
  salePrice?: string /*到手价 */;
  sectionFee?: string /*切片费*/;
  selectGoodsPoolNo?: string /*选品池编号*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionAuditorName?: string /*选品审核人名称*/;
  selectionLabel?: string /*选品标签*/;
  selectionReason?: string /*选品驳回原因*/;
  selectionRemark?: string /*选品备注*/;
  selectionRoundAdjustRecordList?: Array<{
    adjustReason?: string /*调整原因*/;
    adjustSource?: 'COOP_ORDER_ADJUST' /*记录来源：COOP_ORDER_ADJUST:合作调整单[SelectionRoundAdjustRecordAdjustSource]*/;
    adjustType?:
      | 'ADJUST_SERVICE_FEE'
      | 'ADJUST_CHANGE_BINDING' /*调整类型ADJUST_SERVICE_FEE：调整基础服务费，ADJUST_CHANGE_BINDING：调整解绑支付单[SelectionRoundAdjustRecordAdjustType]*/;
    afterBrandFee?: string /*调整后基础服务费*/;
    approvalProcessNo?: string /*审批流程编号*/;
    beforeBrandFee?: string /*调整前基础服务费*/;
    bizOrderId?: string /*来源单据ID*/;
    coopOrderId?: string /*合作订单主键*/;
    coopOrderNo?: string /*合作订单号*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    id?: string /*主键ID*/;
    modifier?: string /*修改人*/;
    requestUniqueCode?: string /*请求唯一编号*/;
    selectionRoundId?: string /*场次货盘ID*/;
    selectionRoundNo?: string /*场次货盘编号*/;
    status?:
      | 'INIT'
      | 'PASS'
      | 'REJECT' /*审核状态，INIT：待审核、PASS：通过、REJECT：驳回[SelectionRoundAdjustRecordStatus]*/;
    targetCoopOrderId?: string /*目标合作订单id*/;
    targetCoopOrderNo?: string /*目标合作订单号*/;
    targetSelectionRoundId?: string /*目标场次货盘ID*/;
    targetSelectionRoundNo?: string /*目标场次货盘编号*/;
  }> /*商务条款变更信息*/;
  selectionStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*选品审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  sellingPoints?: string /*商品主要卖点*/;
  serviceAgreementId?: string /*服务协议id*/;
  shelfLife?: string /*食品保质期*/;
  shopId?: string /*店铺ID*/;
  shopPoints?: string /*店铺分*/;
  shopPointsRefreshTime?: string /*店铺分刷新时间*/;
  skus?: Array<{
    cashAmount?: string /*商家返现金额*/;
    hisHighestPrice?: string /*历史最高价*/;
    hisLowestPrice?: string /*历史最低价*/;
    id?: string /*sku id*/;
    image?: string /*sku图片*/;
    name?: string /*sku名称，规格名称*/;
    price?: string /*日常价格*/;
    purchasePrice?: string /*采购价（含税）*/;
    salePrice?: string /*直播价格*/;
    skuNo?: string /*sku编号*/;
    stock?: string /*库存*/;
    taxRate?: string /*税率（%）*/;
  }> /*sku信息*/;
  soldSalesVolume?: string /*已售销量*/;
  sourceOrderNo?: string /*来源单据编码*/;
  sourceOrderType?: string /*来源单据类型*/;
  specQualificationVersion?: string /*采买链路资质版本ID, 无论是否采买均绑定一个采买链路版本, 用于品牌类型切换处理*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  specialAuditApprovalProcessNo?: string /*资质特批-审批流程编号*/;
  specialAuditId?: string /*资质特批Id*/;
  specialAuditProcessUid?: string /*资质特批Uid*/;
  specialAuditStatus?:
    | 'WAIT_AUDIT'
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT'
    | 'WITHDRAW' /*资质特批状态[SpecialAuditStatus]*/;
  specialAuditType?: 'ONLINE' | 'OFFLINE' | 'HIGH_SPECIAL' /*特批类型[SpecialAuditTypeEnum]*/;
  specialAuditorRecordList?: Array<{
    auditTime?: string /*审批时间*/;
    auditor?: string /*审批人名称*/;
    auditorId?: string /*审批人ID*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    level?: string /*审批级别*/;
    modifier?: string /*修改人*/;
    rejectReason?: string /*驳回原因*/;
    roleType?:
      | 'BUSINESS'
      | 'SELECTION'
      | 'OPERATE'
      | 'MIDDLE_GROUND'
      | 'LEGAL'
      | 'ANCHOR' /*角色类型[RoleTypeEnum]*/;
    status?: string /*状态*/;
  }> /*资质特批审核人记录*/;
  spuFocus?: string /*重点展示需求*/;
  spuFocusResources?: Array<{
    resourceId?: string /*链接附件id*/;
    resourceUrl?: string /*链接附件url*/;
  }> /*重点展示需求附件*/;
  spuId?: string /*spu id*/;
  spuName?: string /*spu名称*/;
  spuNo?: string /*spu编号*/;
  spuSource?: string /*商品来源：SUPPLIER_SPU、CAPTAIN_ACTIVITY_SPU、KUAISHOU_CAPTAIN_ACTIVITY_SPU ...*/;
  standardCateId?: string /*行业大类ID*/;
  standardCateName?: string /*行业大类名称*/;
  standardFavorableRate?: string /*标准好评率*/;
  standardStore?: string /*标准店铺分*/;
  status?:
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED' /*场次货盘状态[SelectionRoundStatus]*/;
  suggestCommission?: string /*建议佣金*/;
  supplierBodySpecialAuditApprovalProcessNo?: string /*主体特批-审批流程编号*/;
  supplierBodySpecialAuditId?: string /*主体特批Id*/;
  supplierBodySpecialAuditRemark?: string /*主体特批原因*/;
  supplierBodySpecialAuditStatus?:
    | 'WAIT_AUDIT'
    | 'CONFIRMING'
    | 'PASS'
    | 'REJECT'
    | 'INVALID' /*主体特批状态[SupplierBodySpecialAuditStatusEnum]*/;
  supplierBodySpecialAuditorRecordList?: Array<{
    auditTime?: string /*审批时间*/;
    auditor?: string /*审批人名称*/;
    auditorId?: string /*审批人ID*/;
    creator?: string /*创建人*/;
    gmtCreated?: string /*创建时间*/;
    gmtModified?: string /*更新时间*/;
    level?: string /*审批级别*/;
    modifier?: string /*修改人*/;
    rejectReason?: string /*驳回原因*/;
    roleType?:
      | 'BUSINESS'
      | 'SELECTION'
      | 'OPERATE'
      | 'MIDDLE_GROUND'
      | 'LEGAL'
      | 'ANCHOR' /*角色类型[RoleTypeEnum]*/;
    status?: string /*状态*/;
  }> /*主体特批审核人记录*/;
  supplierBodySpecialProcessUid?: string /*主体特批流程id*/;
  supplierId?: string /*商家ID*/;
  supplierOrgId?: string /*商家主体快照ID*/;
  supplierOrgName?: string /*商家主体名称*/;
  supplierStatus?:
    | 'INIT'
    | 'PASS'
    | 'AUTO_PASS'
    | 'REJECT'
    | 'SKIP' /*商家审核状态，INIT:待审核、PASS:通过、REJECT:驳回[SelectionReviewStatus]*/;
  supplySource?:
    | 'HOME_BRED'
    | 'CROSS_BORDER'
    | 'IMPORT' /*来源，国产、跨境、进口[SupplySourceEnum]*/;
  talentId?: string /*达人ID*/;
  talentNo?: string /*达人编号*/;
  totalCommissionContainGuaranteed?: string /*总佣金(含保量)*/;
  totalCommissionRate?: string /*总佣金*/;
  version?: number /*版本号*/;
};

/**
 *场次货盘详情
 */
export const checkLinkStatusDetail = (params: CheckLinkStatusDetailRequest) => {
  return Fetch<ResponseWithResult<CheckLinkStatusDetailResult>>('/iasm/public/selection/detail', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/selection/detail') },
  });
};

export type BatchUpCommissionRateOfflineRequest = {
  commissionRateOffline?: string /*线下佣金*/;
  ids?: Array<string> /*场次货盘id集合*/;
};

export type BatchUpCommissionRateOfflineResult = boolean;

/**
 *批量修改线下佣金
 */
export const batchUpCommissionRateOffline = (params: BatchUpCommissionRateOfflineRequest) => {
  return Fetch<ResponseWithResult<BatchUpCommissionRateOfflineResult>>(
    '/iasm/public/selectionProcessKanban/batchUpCommissionRateOffline',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/iasm/public/selectionProcessKanban/batchUpCommissionRateOffline'),
      },
    },
  );
};

export type UpdateAuditorClaimsRequest = {
  forceClaims?: boolean /*是否强制认领,true:存在选品仍然认领,false:存在已被认领的数据会提示*/;
  ids?: Array<string> /*选品池id集合*/;
  selectionAuditor?: string /*选品人员id*/;
};

export type UpdateAuditorClaimsResult = boolean;

/**
 *选品认领
 */
export const updateAuditorClaims = (params: UpdateAuditorClaimsRequest) => {
  return Fetch<ResponseWithResult<UpdateAuditorClaimsResult>>(
    '/iasm/public/selectionProcessKanban/claimsSelectionAuditor',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/iasm/public/selectionProcessKanban/claimsSelectionAuditor'),
      },
    },
  );
};

export type SummaryInfoRequest = {
  bpId?: string /*商务ID*/;
  brandAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*品牌资质结果[QualificationAuditStateEnum]*/;
  brandFeeMax?: string /*基础服务费查询区间 最大值*/;
  brandFeeMin?: string /*基础服务费查询区间 最小值*/;
  brandId?: string /*品牌ID*/;
  cooperationOrderId?: Array<string> /*合作订单id*/;
  current?: number /*当前页码,从1开始*/;
  deptId?: string /*事业部ID*/;
  frameworkGoods?: boolean /*框架商品*/;
  goodsAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*商品资质结果[QualificationAuditStateEnum]*/;
  groupId?: Array<string> /*项目组ID*/;
  guaranteeProportionFlag?: boolean /*是否保比*/;
  guaranteeQuantityFlag?: boolean /*是否保量*/;
  guaranteeQuantityGoods?: boolean /*保量商品*/;
  isSpecQualificationEmpty?: boolean /*是否采买链路资质为空*/;
  legalAuditor?: string /*法务审核人ID*/;
  legalStatus?: Array<
    'QUALIFIED' | 'HIGH' | 'HIGH_SPECIAL' | 'MIDDLE' | 'LOW' | 'PASS' | 'NONE'
  > /*资质风险等级[SelectionQualificationRiskLevel]*/;
  linkCheckStatusList?: Array<string> /*链接确认状态*/;
  linkCheckStatusNullFlag?: boolean /*链接确认状态为空*/;
  linkFlag?: number /*是否有链接 1-有 0-无*/;
  liveDateEndTime?: string /*直播结束时间*/;
  liveDateStartTime?: string /*直播开始时间*/;
  livePlatformSpuIds?: Array<string> /*上播商品ID*/;
  liveRoomId?: Array<string> /*直播间ID*/;
  liveRoundId?: Array<string> /*直播场次ID*/;
  liveServiceTypeIds?: Array<string> /*直播服务类型IDs*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxCommissionRate?: string /*线上佣金比例-最高值*/;
  maxCommissionRateOffline?: string /*线下佣金比例-最高值*/;
  maxFavorableRate?: string /*最高商品好评率*/;
  maxSectionFee?: string /*切片费-最高值*/;
  maxShopPoints?: string /*最高店铺体验分*/;
  minCommissionRate?: string /*线上佣金比例-最低值*/;
  minCommissionRateOffline?: string /*线下佣金比例-最低值*/;
  minFavorableRate?: string /*最低商品好评率*/;
  minSectionFee?: string /*切片费-最低值*/;
  minShopPoints?: string /*最低店铺体验分*/;
  no?: Array<string> /*选品流程编号*/;
  notUploadFlag?: boolean /*是否只显示未上传付款凭证数据*/;
  operatorAuditor?: string /*运营审核人ID*/;
  operatorStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*运营审核状态[SelectionReviewStatus]*/;
  paymentVoucherStatus?:
    | 'WAIT_CONFIRM'
    | 'CONFIRMED'
    | 'HAVE_PROBLEM' /*付款凭证状态[PaymentVoucherStatus]*/;
  platformShopName?: string /*平台店铺名称(抖店/快手等店铺名称)*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*来源平台[PlatformEnum]*/;
  platformSpuId?: Array<string> /*平台商品ID*/;
  processAttribution?: string /*流程归属*/;
  qualificationSupplementFlag?:
    | 'SUPPLEMENT_WAIT_AUDIT'
    | 'DEFAULT' /*资质补充状态[QualificationSupplementFlagEnum]*/;
  qualifyInspectionStatus?:
    | 'NEEDLESS_INSPECTION'
    | 'NON_INSPECTION'
    | 'INSPECTING'
    | 'PASSED_INSPECTION'
    | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
  selectionAuditor?: string /*选品审核人ID*/;
  selectionLabel?: string /*选品标签*/;
  selectionStatus?: Array<
    'INIT' | 'PASS' | 'AUTO_PASS' | 'REJECT' | 'SKIP'
  > /*选品审核状态[SelectionReviewStatus]*/;
  serviceAgreementSigned?: boolean /*是否签署协议*/;
  size?: number /*分页大小*/;
  specVersion?: boolean /*是否特殊资质版本, 即采买链路资质*/;
  spokenIsComplete?: boolean /*口播稿是否完整*/;
  spuName?: Array<string> /*商品名称*/;
  spuNo?: Array<string> /*商品编号*/;
  status?: Array<
    | 'BP_CONFIRMING'
    | 'WAIT_AUDIT'
    | 'WAIT_LIVE'
    | 'ABORT_LIVE'
    | 'ABORT_WAIT_LIVE'
    | 'COMPLETED_LIVE'
    | 'CANCEL'
    | 'INVITING'
    | 'LOSE_EFFICACY'
    | 'TB_ORDERED'
  > /*选品阶段[SelectionRoundStatus]*/;
  supplierAuditStateSet?: Array<
    'PASS' | 'NO_PASS' | 'NONE'
  > /*商家资质结果[QualificationAuditStateEnum]*/;
  supplierId?: string /*商家ID*/;
  talentId?: string /*达人Id*/;
};

export type SummaryInfoResult = {
  brandFeeTotal?: string /*基础服务费*/;
  sectionFeeTotal?: string /*切片费*/;
  sumTotal?: string /*合计*/;
};

/**
 *选品看板统计数据
 */
export const summaryInfo = (params: SummaryInfoRequest) => {
  return Fetch<ResponseWithResult<SummaryInfoResult>>(
    '/iasm/public/selectionProcessKanban/summary',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selectionProcessKanban/summary') },
    },
  );
};

export type QueryProcessForAddLinkRequest = {
  brandFee?: string /*基础服务费，坑位费*/;
  commissionRate?: string /*线上佣金比例*/;
  id?: string /*选品流程id*/;
  limitSize?: number /*限制数量,默认为 50*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  liveRoomId?: string /*直播间id*/;
  platformSpuId?: string /*平台商品ID*/;
};

export type QueryProcessForAddLinkResult = Array<{
  id?: string /*选品流程ID*/;
  liveDate?: string /*直播日期*/;
  liveRoomName?: string /*直播间*/;
  no?: string /*选品流程编号*/;
  spuName?: string /*商品名称*/;
}>;

/**
 *选品看板-补充链接同商品查询
 */
export const queryProcessForAddLink = (params: QueryProcessForAddLinkRequest) => {
  return Fetch<ResponseWithResult<QueryProcessForAddLinkResult>>(
    '/iasm/public/selection/queryProcessForAddLink',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/queryProcessForAddLink') },
    },
  );
};

export type BatchUpdateLinkRequest = {
  ids?: Array<string> /*选品流程ids*/;
  linkValidityEndTime?: string /*上播链接结束时间*/;
  linkValidityStartTime?: string /*上播链接开始时间*/;
  livePlatformSpuId?: string /*上播商品id*/;
  livestreamLink?: string /*上播链接*/;
};

export type BatchUpdateLinkResult = boolean;

/**
 *选品看板-批量更新链接
 */
export const batchUpdateLink = (params: BatchUpdateLinkRequest) => {
  return Fetch<ResponseWithResult<BatchUpdateLinkResult>>(
    '/iasm/public/selection/batchUpdateLink',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/batchUpdateLink') },
    },
  );
};

export type BatchSupplementCooperationOrdersRequest = {
  id?: string /*业务ID*/;
  isBefriends?: boolean /*是否为交个朋友*/;
};

export type BatchSupplementCooperationOrdersResult = any;

/**
 *补充合作订单
 */
export const batchSupplementCooperationOrders = (
  params: BatchSupplementCooperationOrdersRequest,
) => {
  return Fetch<ResponseWithResult<BatchSupplementCooperationOrdersResult>>(
    '/agreement/public/cooperation/supCooperOrder',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/agreement/public/cooperation/supCooperOrder') },
    },
  );
};

export type UploadBatchBpSpecQualificationRequest = {
  specResourceIds?: Array<string> /*采买链路资质资源集合*/;
  specVersionIds?: Array<string> /*采买链路资质版本IDS*/;
};

export type UploadBatchBpSpecQualificationResult = boolean;

/**
 *商务批量上传采买资质
 */
export const uploadBatchBpSpecQualification = (params: UploadBatchBpSpecQualificationRequest) => {
  return Fetch<ResponseWithResult<UploadBatchBpSpecQualificationResult>>(
    '/pim/public/qualificationVersion/uploadBatchBpSpecQualification',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        accessAuth: getSign('/pim/public/qualificationVersion/uploadBatchBpSpecQualification'),
      },
    },
  );
};

export type ReturnSampleRegisterRequest = {
  id?: string /*id*/;
  returnSampleRegister?: string /*退样登记*/;
};

export type ReturnSampleRegisterResult = boolean;

/**
 *选品看板-退样登记
 */
export const returnSampleRegister = (params: ReturnSampleRegisterRequest) => {
  return Fetch<ResponseWithResult<ReturnSampleRegisterResult>>(
    '/iasm/public/selection/returnSampleRegister',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/returnSampleRegister') },
    },
  );
};

export type BatchCheckLinkStatusRequest = {
  requestList?: Array<{
    id?: string /*id*/;
    linkCheckRemark?: string /*链接确认备注*/;
    linkCheckStatus?:
      | 'WAIT_CHECK'
      | 'NO_PROMOTION'
      | 'CAN_PROMOTION' /*链接确认状态[LinkPromotionCheckStatusEnum]*/;
    resourceIds?: Array<string> /*链接附件id列表*/;
  }> /*批量确认链接状态请求参数*/;
};

export type BatchCheckLinkStatusResult = {
  failCount?: number /*失败数量*/;
  failReasonList?: Array<{
    failReason?: string /*失败原因*/;
    no?: string /*选品流程编号*/;
  }> /*标签内容*/;
  successCount?: number /*成功数量*/;
  totalCount?: number /*总数*/;
};

/**
 *批量确认链接状态
 */
export const batchCheckLinkStatus = (params: BatchCheckLinkStatusRequest) => {
  return Fetch<ResponseWithResult<BatchCheckLinkStatusResult>>(
    '/iasm/public/selectionProcessKanban/batchCheckLinkStatus',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selectionProcessKanban/batchCheckLinkStatus') },
    },
  );
};

export type AuditWithdrawRequest = {
  id?: string /*资质特批id*/;
};

export type AuditWithdrawResult = any;

/**
 *交个朋友端-资质特批详情撤回
 */
export const auditWithdraw = (params: AuditWithdrawRequest) => {
  return Fetch<ResponseWithResult<AuditWithdrawResult>>(
    '/pim/public/befriends/qualificationSpecialAudit/withdraw',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/befriends/qualificationSpecialAudit/withdraw') },
    },
  );
};

export type UpdatePreSaleStockRequest = {
  id?: string /*场次货盘ID*/;
  preSaleStock?: number /*预售库存*/;
};

export type UpdatePreSaleStockResult = boolean;

/**
 *设置预售库存
 */
export const updatePreSaleStock = (params: UpdatePreSaleStockRequest) => {
  return Fetch<ResponseWithResult<UpdatePreSaleStockResult>>(
    '/iasm/public/selection/updatePreSaleStock',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/updatePreSaleStock') },
    },
  );
};

export type ImportPreSaleStockRequest = {
  resourceId?: string /*导入预售库存文件资源ID*/;
};

export type ImportPreSaleStockResult = {
  failList?: Array<{
    reason?: string /*失败原因*/;
    rowNum?: number /*Excel记录行号*/;
    selectionRoundNo?: string /*选品编号*/;
  }> /*失败信息*/;
  failNum?: number /*失败数*/;
  finished?: boolean /*导入完成*/;
  successNum?: number /*成功数*/;
};

/**
 *导入预售库存
 */
export const importPreSaleStock = (params: ImportPreSaleStockRequest) => {
  return Fetch<ResponseWithResult<ImportPreSaleStockResult>>(
    '/iasm/public/selection/importPreSaleStock',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/importPreSaleStock') },
    },
  );
};

export type ChangeBusinessOwnerRequest = {
  bpId?: string /*商务负责人*/;
  ids?: Array<string> /*主键id集合*/;
};

export type ChangeBusinessOwnerResult = boolean;

/**
 *变更商务归属
 */
export const changeBusinessOwner = (params: ChangeBusinessOwnerRequest) => {
  return Fetch<ResponseWithResult<ChangeBusinessOwnerResult>>(
    '/iasm/public/nonLiveSelection/changeBusinessOwner',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/nonLiveSelection/changeBusinessOwner') },
    },
  );
};

export type UserTagAddRequest = {
  bizType?: string /*业务类型*/;
  exportTag?: string /*导出标签名称*/;
  extConfig?: string /*拓展配置*/;
};

export type UserTagAddResult = boolean;

/**
 *用户表头配置标签新增
 */
export const userTagAdd = (params: UserTagAddRequest) => {
  return Fetch<ResponseWithResult<UserTagAddResult>>('/iasm/public/employeeExtConfig/saveTag', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/employeeExtConfig/saveTag') },
  });
};

export type UserTagQueryRequest = {
  bizType?: string /*业务类型*/;
  exportTag?: string /*导出标签*/;
};

export type UserTagQueryResult = {
  bizType?: string /*业务码*/;
  employeeId?: string /*用户id*/;
  exportTag?: string /*导出标签*/;
  exportTagList?: Array<string> /*用户拥有的全部导出标签*/;
  extConfig?: string /*表头字段*/;
  id?: string /*id*/;
};

/**
 *用户表头配置标签查询
 */
export const userTagQuery = (params: UserTagQueryRequest) => {
  return Fetch<ResponseWithResult<UserTagQueryResult>>('/iasm/public/employeeExtConfig/queryTag', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/employeeExtConfig/queryTag') },
  });
};

export type UserTagDelRequest = {
  bizType?: string /*业务类型*/;
  exportTag?: string /*导出标签名称*/;
};

export type UserTagDelResult = boolean;

/**
 *用户表头配置标签删除
 */
export const userTagDel = (params: UserTagDelRequest) => {
  return Fetch<ResponseWithResult<UserTagDelResult>>('/iasm/public/employeeExtConfig/delTag', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/employeeExtConfig/delTag') },
  });
};

export type UserTagEditRequest = {
  bizType?: string /*业务类型*/;
  extConfig?: string /*拓展配置*/;
  newExportTag?: string /*导出标签名称-更新后*/;
  oldExportTag?: string /*导出标签名称-更新前*/;
};

export type UserTagEditResult = boolean;

/**
 *用户表头配置标签更新
 */
export const userTagEdit = (params: UserTagEditRequest) => {
  return Fetch<ResponseWithResult<UserTagEditResult>>('/iasm/public/employeeExtConfig/updateTag', {
    method: 'POST',
    body: JSON.stringify(params),
    headers: { accessAuth: getSign('/iasm/public/employeeExtConfig/updateTag') },
  });
};

export type UserTagCheckRequest = {
  bizType?: string /*业务类型*/;
  newExportTag?: string /*导出标签名称-更新后*/;
  oldExportTag?: string /*导出标签名称-更新前*/;
};

export type UserTagCheckResult = boolean;

/**
 *用户表头配置标签重复性校验
 */
export const userTagCheck = (params: UserTagCheckRequest) => {
  return Fetch<ResponseWithResult<UserTagCheckResult>>(
    '/iasm/public/employeeExtConfig/checkVerify',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/employeeExtConfig/checkVerify') },
    },
  );
};

export type QualityScoreApprovalRequest = {
  goodsQualityScore?: string /*商品质量分*/;
  id?: string /*场次货盘id*/;
  platformSource?:
    | 'DY'
    | 'JD'
    | 'TB'
    | 'PDD'
    | 'KS'
    | 'WECHAT_VIDEO'
    | 'BAIDU'
    | 'RED' /*平台来源[PlatformEnum]*/;
  platformSpuId?: string /*商品id*/;
  reason?: string /*申请原因*/;
  resourceIds?: Array<string> /*附件id集合*/;
  spuName?: string /*商品名称*/;
};

export type QualityScoreApprovalResult = any;

/**
 *商品质量分发起OA审批
 */
export const qualityScoreApproval = (params: QualityScoreApprovalRequest) => {
  return Fetch<ResponseWithResult<QualityScoreApprovalResult>>(
    '/iasm/public/selection/qualityScoreApproval',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selection/qualityScoreApproval') },
    },
  );
};

export type HumanChangeRequest = {
  bpId?: string /*商务id*/;
  ids?: Array<string> /*流程看板IDS*/;
  operatorAuditor?: string /*运营人员id*/;
  selectionAuditor?: string /*选品人员id*/;
};

export type HumanChangeResult = boolean;

/**
 *人员归属确认
 */
export const humanChange = (params: HumanChangeRequest) => {
  return Fetch<ResponseWithResult<HumanChangeResult>>(
    '/iasm/public/selectionProcessKanban/humanChange',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/iasm/public/selectionProcessKanban/humanChange') },
    },
  );
};

export type SelectGoodsPoolExportRequest = {
  bjdNos?: string /*报名单号*/;
  bpIds?: Array<string> /*商务ID*/;
  brandId?: string /*品牌ID*/;
  current?: number /*当前页码,从1开始*/;
  deptIds?: Array<string> /*事业部ID*/;
  dynamicHead?: string /*选品池导出动态表头*/;
  endGmtCreated?: string /*创建时间-结束*/;
  explosiveAuditStatusList?: Array<
    'WAIT_SUBMIT' | 'ALREADY_SUBMIT' | 'REFUSED' | 'PASS'
  > /*爆品池审核状态[ExplosiveAuditStatusEnum]*/;
  explosiveBpId?: string /*爆品池归属商务Id*/;
  explosiveDeptId?: string /*爆品来源事业部id*/;
  explosiveFlag?: number /*是否爆品池*/;
  explosiveLiveRoomId?: string /*爆品直播间id*/;
  explosiveOperatorAuditor?: string /*爆品池归属运营*/;
  explosiveTypeList?: Array<
    'HISTORY_EXPLOSIVE' | 'PRE_EXPLOSIVE' | 'PRE_EXPLOSIVE_CAN_APPLY' | 'EXPLOSIVE'
  > /*爆品池类型标签[ExplosiveTypeEnum]*/;
  exportIds?: Array<string> /*选品池IDS*/;
  id?: Array<string> /*选品池id*/;
  isMyResponsible?: boolean /*我负责的,true为我负责的,默认为false*/;
  liveRoomIds?: Array<string> /*直播间ids*/;
  luckyProductFlag?: boolean /*是否福袋商品*/;
  maxBrandFee?: string /*基础服务费基础服务费,最大值*/;
  maxSalePrice?: string /*最高到手价*/;
  minBrandFee?: string /*基础服务费基础服务费,最小值*/;
  minSalePrice?: string /*最低到手价*/;
  platformSource?: string /*商品来源平台,抖音:DY,淘宝:TB*/;
  platformSpuIds?: Array<string> /*平台商品ID*/;
  qualifyInspectionStatus?:
    | 'NEEDLESS_INSPECTION'
    | 'NON_INSPECTION'
    | 'INSPECTING'
    | 'PASSED_INSPECTION'
    | 'FAILED_INSPECTION' /*质检状态[QualifyInspectionStatusEnum]*/;
  queryStr?: string /*平台商品id或商品名称*/;
  queryType?: number /*查询类型 1-全量 2-与我相关*/;
  shopName?: string /*店铺名称*/;
  size?: number /*分页大小*/;
  sortField?: string /*排序字段,创建时间:gmt_created,累计上播次数:historySumPlayTimes,历史累计销售额:historySumSales,场均销售额:avgSales,基础服务费:brandFee,报名记录创建时间:batchApplyRecordCreatedTime,总佣金:totalCommission,历史累计销量：historicalTotalSales近三天销售额: threeDaysSales近七天销售额: sevenDaysSales近十四天销售额: fourteenDaysSales近一个月销售额: oneDaysSales*/;
  spokenIsComplete?: boolean /*口播稿是否完整*/;
  spuNames?: Array<string> /*批量商品名称*/;
  spuNos?: Array<string> /*spu编号*/;
  standardCateIds?: Array<string> /*行业大类IDS*/;
  startGmtCreated?: string /*创建时间-开始*/;
  status?:
    | 'SELECTING'
    | 'DISABLED'
    | 'INVALID' /*状态,SELECTING:选品中,INVALID:已失效,DISABLED:已禁用[SelectGoodsStatusEnum]*/;
  supplierGoodsSource?:
    | 'SELF_APPLY_SUPPLIER_GOODS'
    | 'PLATFORM_SUPPLIER_GOODS' /*商家商品来源,SELF_APPLY_SUPPLIER_GOODS:自主报名商品,PLATFORM_SUPPLIER_GOODS:平台型商家商品[SupplierGoodsSourceEnum]*/;
  supplierId?: string /*商家ID*/;
  unclaimed?: boolean /*未认领的, 默认为false*/;
};

export type SelectGoodsPoolExportResult = string;

/**
 *选品池详情信息导出
 */
export const selectGoodsPoolExport = (params: SelectGoodsPoolExportRequest) => {
  return Fetch<ResponseWithResult<SelectGoodsPoolExportResult>>(
    '/pim/public/selectGoodsPool/export',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/pim/public/selectGoodsPool/export') },
    },
  );
};
