const signMap = {
  '/gql/public/platform/spuBrandByName/b60ed801ca7d20fb2a4d8116c1a4ef555c83492636676b4026da03eeb377ab26':
    'f_selection_flow_board_list.x1.xxxx1',
  '/iasm/public/dept/getDeptList': 'f_selection_flow_board_list.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/page': 'f_selection_flow_board_list.x1.xxxx1',
  '/iasm/public/nonLiveSelection/page': 'f_selection_flow_board_list.x1.xxxx1',
  '/iasm/public/projectGroupManagement/projectGroupList': 'f_selection_flow_board_list.x1.xxxx1',
  '/iasm/public/employeeExtConfig/query': 'f_selection_flow_board_export.x1.xxxx1',
  '/iasm/public/employeeExtConfig/set': 'f_selection_flow_board_export.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/export': 'f_selection_flow_board_export.x1.xxxx1',
  '/iasm/public/nonLiveSelection/export': 'f_selection_flow_board_export.x1.xxxx1',
  '/iasm/public/selection/bpConfirm': 'f_selection_flow_board_confirm.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/batchBpConfirm': 'f_selection_flow_board_confirm.x1.xxxx1',
  '/iasm/public/selection/importLinks': 'f_selection_flow_board_import_link.x1.xxxx1',
  '/iasm/public/nonLiveSelection/importLinks': 'f_selection_flow_board_import_link.x1.xxxx1',
  '/iasm/public/selection/bpCancel': 'f_selection_flow_board_cancel.x1.xxxx1',
  '/iasm/public/selection/selectionConfirm': 'f_selection_flow_board_ch_confirm.x1.xxxx1',
  '/iasm/public/selection/operatorConfirm': 'f_selection_flow_board_op_confirm.x1.xxxx1',
  '/iasm/public/selection/droppedProduct': 'f_selection_flow_board_del.x1.xxxx1',
  '/iasm/public/selection/droppedProductRepeat': 'f_selection_flow_board_repeat_drop.x1.xxxx1',
  '/iasm/public/selection/addLinks': 'f_selection_flow_board_require_link.x1.xxxx1',
  '/iasm/public/nonLiveSelection/addLinks': 'f_selection_flow_board_require_link.x1.xxxx1',
  '/iasm/public/selection/getOneLink': 'f_selection_flow_board_get_require_link.x1.xxxx1',
  '/iasm/public/selection/detail': 'basic.x1.xxxx1',
  '/iasm/public/selection/checkAndSaveServiceAgreement': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/list': 'basic.x1.xxxx1',
  '/iasm/public/liveRoom/listVisibleLiveRoom': 'basic.x1.xxxx1',
  '/iasm/public/nonLiveSelection/detail': 'basic.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/checkLinkStatus': 'basic.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/batchUpCommissionRateOffline': 'basic.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/claimsSelectionAuditor': 'basic.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/summary': 'basic.x1.xxxx1',
  '/iasm/public/selection/queryProcessForAddLink': 'basic.x1.xxxx1',
  '/iasm/public/selection/batchUpdateLink': 'basic.x1.xxxx1',
  '/agreement/public/cooperation/supCooperOrder': 'basic.x1.xxxx1',
  '/pim/public/qualificationVersion/uploadBatchBpSpecQualification': 'basic.x1.xxxx1',
  '/iasm/public/selection/returnSampleRegister': 'basic.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/batchCheckLinkStatus': 'basic.x1.xxxx1',
  '/pim/public/befriends/qualificationSpecialAudit/withdraw': 'basic.x1.xxxx1',
  '/iasm/public/selection/updatePreSaleStock': 'basic.x1.xxxx1',
  '/iasm/public/selection/importPreSaleStock': 'basic.x1.xxxx1',
  '/iasm/public/nonLiveSelection/changeBusinessOwner': 'basic.x1.xxxx1',
  '/iasm/public/employeeExtConfig/saveTag': 'basic.x1.xxxx1',
  '/iasm/public/employeeExtConfig/queryTag': 'basic.x1.xxxx1',
  '/iasm/public/employeeExtConfig/delTag': 'basic.x1.xxxx1',
  '/iasm/public/employeeExtConfig/updateTag': 'basic.x1.xxxx1',
  '/iasm/public/employeeExtConfig/checkVerify': 'basic.x1.xxxx1',
  '/iasm/public/selection/qualityScoreApproval': 'basic.x1.xxxx1',
  '/iasm/public/selectionProcessKanban/humanChange': 'f_selection_flow_board_person.x1.xxxx1',
  '/pim/public/selectGoodsPool/export': 'f_goods-assorting_export_logs.x1.xxxx1',
};

export function getSign(path: string): string {
  return signMap[path];
}
