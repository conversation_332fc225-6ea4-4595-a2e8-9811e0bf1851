import moment from 'moment';
import { isInvalidValue, isNullOrUndefined } from 'web-common-modules/utils/type';
// 将字符串按照空格分隔去掉所有""
export const formatString = (value: string | undefined | null) => {
  if (!value) {
    return undefined;
  }
  const formatValue = value.split(' ');
  const delEmpt = formatValue?.filter((item) => !!item);
  return delEmpt || [];
};

export const formatParams = (value: any, tabsValue: string) => {
  // ascription 流程归属占不做处理
  const {
    cooperationOrderId,
    ascription,
    liveTime,
    no,
    spuNo,
    status,
    person,
    brandFee,
    sectionFee,
    platformSpuId,
    spuName,
    favorableRate,
    shopPoints,
    commissionRate,
    commissionRateOffline,
    livePlatformSpuIds,
    cateId,
    ...otherParams
  } = value;
  // console.log('🚀 ~ formatParams ~ favorableRate:', favorableRate, shopPoints);
  const params = {
    ...otherParams,
    // cooperationOrderId: formatString(cooperationOrderId),
    no: formatString(no),
    spuNo: formatString(spuNo),
    liveDateStartTime: undefined,
    liveDateEndTime: undefined,
    bpId: undefined,
    operatorAuditor: undefined,
    selectionAuditor: undefined,
    status: [],
    platformSpuId: formatString(platformSpuId),
    ascription,
    brandFeeMin: brandFee?.min,
    brandFeeMax: brandFee?.max,
    minSectionFee: sectionFee?.min,
    maxSectionFee: sectionFee?.max,
    spuName: formatString(spuName),
  };

  if (liveTime?.startDate) {
    const { startDate, endDate } = liveTime;
    params.liveDateStartTime = moment(startDate).format('YYYY-MM-DD');
    params.liveDateEndTime = moment(endDate).format('YYYY-MM-DD');
  }
  if (person && person?.length) {
    const [key, value] = person;
    params[key] = value;
  }
  if (status && status?.length && status.includes('ABORT_LIVE')) {
    params.status = [...status, 'ABORT_WAIT_LIVE'];
  } else {
    params.status = status || [];
  }
  if (favorableRate) {
    const [minFavorableRate, maxFavorableRate] = favorableRate;
    params.minFavorableRate = !isInvalidValue(minFavorableRate)
      ? (minFavorableRate / 100).toFixed(4)
      : undefined;
    params.maxFavorableRate = !isInvalidValue(maxFavorableRate)
      ? (maxFavorableRate / 100).toFixed(4)
      : undefined;
  }
  if (commissionRate) {
    const [minCommissionRate, maxCommissionRate] = commissionRate;
    params.minCommissionRate = !isInvalidValue(minCommissionRate)
      ? (minCommissionRate / 100).toFixed(4)
      : undefined;
    params.maxCommissionRate = !isInvalidValue(maxCommissionRate)
      ? (maxCommissionRate / 100).toFixed(4)
      : undefined;
  }
  if (shopPoints) {
    const [minShopPoints, maxShopPoints] = shopPoints;
    params.minShopPoints = minShopPoints;
    params.maxShopPoints = maxShopPoints;
  }
  // 线下佣金格式化
  if (commissionRateOffline) {
    const [minCommissionRateOffline, maxCommissionRateOffline] = commissionRateOffline;
    params.minCommissionRateOffline = !isInvalidValue(minCommissionRateOffline)
      ? (minCommissionRateOffline / 100).toFixed(4)
      : undefined;
    params.maxCommissionRateOffline = !isInvalidValue(maxCommissionRateOffline)
      ? (maxCommissionRateOffline / 100).toFixed(4)
      : undefined;
  }
  if (tabsValue === 'live') {
    params.cooperationOrderId = formatString(cooperationOrderId);
    params.livePlatformSpuIds = formatString(livePlatformSpuIds);
  } else {
    params.cooperationOrderNoList = formatString(cooperationOrderId);
  }
  if (cateId?.length) {
    params.cateId = cateId[cateId.length - 1];
  }
  return params;
};

export const formatBatchApiParams = (list: any, keyMap: any, otherParams: any = {}) => {
  const kesy = Object.keys(keyMap || {});
  const res = list?.map((item: any) => {
    const param = kesy?.reduce((acc: any, cur: any) => {
      return {
        ...acc,
        ...otherParams,
        ...{
          [keyMap[cur]]: item[cur],
        },
      };
    }, {});
    return param;
  });
  return res;
};

/**
 * arrs 请求数据源数组
 * limit 是每次并行发起多少个请求
 * handleFn 接口
 */
export function limitQueueFn(arrs: any, limit: number, handleFn: any) {
  // 完成任务数
  let runningIndex = 0; // 这是正在执行的下标
  let finishedCount = 0; // 已完成的任务数量
  const result = new Array(arrs.length).fill(null); // 建立一个空数组， 存储结果

  return new Promise((resolveFn, rejectFn) => {
    // 如果数组为空，直接返回空结果
    if (arrs.length === 0) {
      return resolveFn([]);
    }

    // 第一次的时候 一次性执行 limit 个任务，但不超过数组长度
    for (let i = 0; i < Math.min(limit, arrs.length); i++) {
      run();
    }

    // 执行一个任务
    function run() {
      // 保存当前任务的索引，确保结果放在正确的位置
      const currentIndex = runningIndex;
      // 获取当前任务的值
      const value = arrs[currentIndex];
      // 增加运行索引，为下一个任务做准备
      runningIndex++;

      // 构造待执行任务 当该任务完成后 如果还有待完成的任务 继续执行任务
      Promise.resolve()
        .then(() => handleFn(value))
        .then(({ res }) => {
          // 使用保存的索引确保结果放在正确的位置
          result[currentIndex] = { ...res, id: arrs[currentIndex]?.id };
          finishedCount++;

          // 如果还有任务未启动，启动新任务
          if (runningIndex < arrs.length) {
            run();
          }

          // 如果所有任务都已完成，返回结果
          if (finishedCount === arrs.length) {
            resolveFn(result);
          }
        })
        .catch((error) => {
          console.error('Task failed:', error);
          // 即使任务失败，也标记为已完成
          result[currentIndex] = {
            success: false,
            message: error.message || '操作失败',
            id: arrs[currentIndex]?.id,
          };
          finishedCount++;

          // 如果还有任务未启动，启动新任务
          if (runningIndex < arrs.length) {
            run();
          }

          // 如果所有任务都已完成，返回结果
          if (finishedCount === arrs.length) {
            resolveFn(result);
          }
        });
    }
  });
}

export const numberToColor = (num: number, initColor = '#666666') => {
  if (isNullOrUndefined(num) || !num) {
    return initColor;
  }

  if (Number(num) < 9) {
    return '#EE0000';
  }

  if (Number(num) >= 9 && Number(num) <= 9.39) {
    return '#FAAD14';
  }

  if (Number(num) >= 9.4 && Number(num) <= 10) {
    return '#52C41A';
  }

  return initColor;
};
