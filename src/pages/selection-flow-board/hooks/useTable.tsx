import React, { useMemo, useState } from 'react';
import styles from '@/styles/index.module.less';
import style from '../index.module.less';
import { Popover, Icon, Tag, message, Modal } from 'antd';
import { newRenderPlatformSourceLogo } from '@/common/constants/platform';
import { AuthWrapper, checkAuth } from 'qmkit';
import {
  SelectionProcessKanbanPageResult,
  getOneLink,
  droppedProductRepeat,
} from '../services/yml';
import moment from 'moment';
import { LowCommissionAuditStatusEnum } from '../dataSource/index';
import {
  // LEVEL_NAME,
  // LEVEL,
  FlowStatus_ChoiceList as FlowStatus_ChoiceList_Enum,
  SELECT_STEP_COLOR,
  SELECT_STEP_NAME,
  STATUS_PERSON_LIST,
  STATUS_PERSON,
  SERVER_TYPE_NAME,
  SERVER_TYPE,
  UN_SELECT_STEP_NAME,
  UN_SELECT_STEP_COLOR,
  UN_FlowStatus_ChoiceList,
  lINKCHECKSTATUS_STEP_COLOR,
} from '../types';
import { SupplementLink } from '../components';
import { copyText } from '@/utils/moduleUtils';
import { useRequest } from 'ahooks';
import IDIMG from '@/assets/id.png';
// import UNBS from '@/assets/unbs.png';
import JingGao from '@/assets/jinggao.png';
import YBS from '@/assets/ybs.png';
import BatchConfirmModal from '../components/BatchConfirmModal';
import { LinkPromotionCheckStatusEnum } from './useSearch';
import Space from '@/components/Space/index';
import EditLinkStatus from '../components/EditLinkStatus';
import SesstingIconTable from '@/components/SesstingIconTable/index';
import HighRisk from '@/pages/choice-list-new/components/choiceListRight/components/HighRisk';
import {
  ApproveStatus,
  QualificationRiskLevelEnum,
} from '@/pages/specil-approval-record/highrisk-approval-records/hooks';
import { IconStatus } from '@/components/GoodsInfoEdit/leftCard';
import {
  allRiskLevelColor,
  RISK_LEVEL_TEXT,
} from '@/pages/audit/legal-audit-queue/utils/getRiskLevel';
import { ApproveIconStatus } from '@/pages/choice-list-new/components/choiceListRight/List/goodsCard';
import { numberContrast } from '@/utils/formatTime';
import BatchSupplementLink from '../components/BatchSupplementLink';
import { ColumnProps } from 'antd/lib/table';
import SubjectSpecilApprovalModal from '@/pages/choice-list-new/components/choiceListRight/components/SubjectSpecilApprovalModal';
import PopoverRowText from '@/components/PopoverRowText/index';

import {
  UALITY_INSPECTION_STATUS_COLOR,
  UALITY_INSPECTION_STATUS_ENUM,
  UALITY_INSPECTION_STATUS_NAME,
} from '../../../../web_modules/types';
import { LEVEL, LEVEL_NAME, LEVEL_COLOR } from '../../../../web_modules/types';
import { formatFee } from '@/pages/anchor-information/utils/getColumns';
import { numberToColor } from '@/pages/selection-flow-board/utils';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import {
  GOODS_GRADES_STATE_NAME,
  GOODS_GRADES_STATE_ENUM,
  COMPLIANCESTATUS_NAME,
  COMPLIANCESTATUS_COLOR,
  COMPLIANCESTATUS_ICON,
  LUXURY_REVIEW_STATUS_NAME,
  LUXURY_REVIEW_STATUS_ENUM,
} from '@/pages/choice-list-new/tools';
import { PAY_DAY_ABBREVIATE, PAY_DAY } from '@/pages/quality-assurance-cooperation/types';
import {
  CooperationModeColorEnum,
  CooperationModeEnum,
} from '@/pages/open-off-live/dataSource/table';

export type SelectionProcessKanbanPage =
  Required<SelectionProcessKanbanPageResult>['records'][number];
export const selectorCheck = (selectionAuditor?: string) => {
  const userId = JSON.parse(localStorage.getItem('jgpy-crm@loginInfo') || '{}')
    .employeeId as string;
  const isSameselectionAuditor = selectionAuditor === userId;
  return isSameselectionAuditor || !selectionAuditor;
};

export const useTable = (
  onRefresh: any,
  openEditDetail: any,
  openSingle: any,
  handleAddModal: any,
  handleCloseConfirrmPayVoucher: any,
  handleOpenMarkVoucher: any,
  handleOpenApproveVisible: any,
  setLegalInfo: any,
  tabsValue: string,
  onSearchOld: any,
  codeEnum: Record<string, string> | undefined,
  handleOpenReturnSampleRegister: any,
  handleOpenLimitInventory: any,
  handleCancelHighRiskAudit: any, //注意顺序
  openSpecialApprovalGoods: any,
) => {
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [selectedFullKeys, setSelectedFullKeys] = useState([]);
  const [sortedInfo, setSortedInfo] = useState<any>();
  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedKeys(selectedRowKeys);
      setSelectedFullKeys(selectedRows);
    },
    columnWidth: 20,
    selectedRowKeys: selectedKeys,
  };
  const AddPayVoucherBtn = (info: SelectionProcessKanbanPage) => {
    if (!info) {
      return false;
    }
    if (!Object.keys(info || {}).length) {
      return false;
    }
    if (['CONFIRMED'].includes(info?.paymentVoucherStatus as string)) {
      return false;
    }
    if (
      !!info?.brandFee &&
      (!info?.paymentOrderStatus ||
        ['PENDING_PAYMENT', 'PAYING'].includes(info?.paymentOrderStatus))
    ) {
      return true;
    }
    if (!!info?.sectionFee) {
      return true;
    }
    if (
      !!info?.brandFee &&
      ['FULL_PAYMENT', 'SETTLED'].includes(info?.paymentOrderStatus as string) &&
      ['OFFLINE_VOUCHER'].includes(info?.paymentMethod as string)
    ) {
      return true;
    }
  };
  // 掉品复播
  const { run: droppedProductRepeatRun, loading: droppedProductRepeatLoading } = useRequest(
    droppedProductRepeat,
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (res.code === '200') {
          message.success('操作成功');
          onRefresh && onRefresh();
        } else {
          message.warning(res.message || '网络异常');
        }
      },
    },
  );
  const { run: getRun, loading: getLoading } = useRequest(getOneLink, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        if (!res?.result?.autoGetLinks) {
          message.warning(res?.result?.msg);
          return;
        }
        message.success('请求成功');
        onRefresh && onRefresh();
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });
  const handleReplay = (records: SelectionProcessKanbanPage) => {
    Modal.confirm({
      icon: (
        <Icon
          type="exclamation-circle"
          style={{ color: '#FAAD14', fontSize: '16px', marginTop: '4px' }}
        />
      ),
      title: '提示',
      content: '您确认要复播此产品吗?',
      onOk: () => {
        droppedProductRepeatRun({ id: records?.id });
      },
    });
  };
  const columns = useMemo(() => {
    // console.log('sortedInfo:', sortedInfo, '---------->');
    const initHeader = [
      {
        title: (
          <SesstingIconTable
            bizType={
              tabsValue === 'live'
                ? 'selection-flow-board-table-live'
                : 'selection-flow-board-table'
            }
          ></SesstingIconTable>
        ),
        label: '#',
        align: 'center',
        draggable: false,
        key: 'number',
        className: styles['table-number'],
        render: (text: any, red: SelectionProcessKanbanPage, index: any) => {
          return <span>{index + 1}</span>;
        },
        width: 30,
      },
      {
        title: '图片',
        draggable: true,
        key: 'image',
        dataIndex: 'image',
        width: 80,
        render: (image: string) => (
          <>
            {image ? (
              <img src={image} className={styles['table-img']} style={{ objectFit: 'cover' }} />
            ) : (
              '-'
            )}
          </>
        ),
      },
      {
        title: '商品信息',
        draggable: true,
        key: 'goodsMsg',
        dataIndex: 'goodsMsg',
        width: 204,
        render: (_: any, records: SelectionProcessKanbanPage) => {
          return (
            <div>
              <div className={style['goods-name']}>
                {/* 显示详情 */}
                <Popover
                  title="商品名称"
                  content={
                    <>
                      {records?.spuName || '-'}
                      <Icon
                        onClick={() => {
                          copyText(records?.spuName || '');
                        }}
                        type="copy"
                        style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                      />
                    </>
                  }
                >
                  <p
                    className={style['goods-name-line']}
                    style={{ marginRight: '4px' }}
                    onClick={() => {
                      openEditDetail(records, 'info');
                    }}
                  >
                    【{records?.brandName || '-'}】{records?.spuName || '-'}
                  </p>
                </Popover>
                <Popover
                  title="商品信息"
                  content={
                    <div>
                      <p>
                        商品编号: {records?.spuNo || '-'}
                        <Icon
                          onClick={() => {
                            copyText(records?.spuNo || '');
                          }}
                          type="copy"
                          style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                        />
                      </p>
                      <p>
                        平台商品ID: {records?.platformSpuId || '-'}
                        <Icon
                          onClick={() => {
                            copyText(records?.platformSpuId || '');
                          }}
                          type="copy"
                          style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                        />
                      </p>
                      <p>
                        选品编号: {records?.no || '-'}
                        <Icon
                          onClick={() => {
                            copyText(records?.no || '');
                          }}
                          type="copy"
                          style={{ color: '#204eff', marginLeft: '4px', cursor: 'pointer' }}
                        />
                      </p>
                    </div>
                  }
                >
                  <img
                    src={IDIMG}
                    style={{ width: '16px', height: '16px', marginLeft: '4px' }}
                    alt=""
                  />
                  {/* <span
                  className={`iconfont icon-id ${style['id-style']}`}
                  style={{
                    fontSize: '16px',
                    position: 'relative',
                    color: '#52C41A',
                    marginLeft: '4px',
                  }}
                ></span> */}
                  {/* <div className={}>ID</div> */}
                </Popover>
              </div>
              <div style={{ color: '#EE0000' }}>
                {records?.minPrice === records?.maxPrice
                  ? `¥${records?.minPrice}`
                  : `¥${records?.minPrice} - ${records?.maxPrice}`}
              </div>
              <div className={style['company']}>
                {/* @TODO: icon */}
                {tabsValue === 'live' ? (
                  <Popover
                    content={
                      records?.serviceAgreementId
                        ? '已签署'
                        : '【未签署】商家未签署协议时不会触发法务审核流程，请商务尽快联系商家在合同管理中签署协议'
                    }
                  >
                    {records?.serviceAgreementId ? (
                      <img
                        src={YBS}
                        style={{
                          width: '16px',
                          height: '16px',
                          marginRight: '4px',
                        }}
                      />
                    ) : (
                      <img
                        src={JingGao}
                        style={{
                          width: '16px',
                          height: '16px',
                          marginRight: '4px',
                        }}
                      />
                    )}
                  </Popover>
                ) : (
                  <></>
                )}

                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    window.open(`/provider-detail/${records?.supplierId}`);
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  {records?.supplierOrgName}
                </div>
              </div>
            </div>
          );
        },
      },
    ];
    const storeMsg = [
      {
        title: '店铺信息',
        draggable: true,
        key: 'shopMsg',
        dataIndex: 'shopMsg',
        width: 170,
        sorter: tabsValue === 'live' ? true : false,
        sortOrder: sortedInfo?.columnKey === 'shopMsg' ? sortedInfo?.order : false,
        render: (_: any, records: SelectionProcessKanbanPage) => {
          return (
            <div>
              {records?.platformSource === 'DY' && tabsValue === 'live' ? (
                <Popover
                  content={
                    <div>
                      <article>
                        商品体验分:{' '}
                        <span
                          className={
                            records?.goodsScore &&
                            records?.standardGoodsScore &&
                            Number(records?.goodsScore) < Number(records?.standardGoodsScore)
                              ? style['error-number']
                              : ''
                          }
                        >
                          {records?.goodsScore === undefined || records?.goodsScore === null
                            ? '-'
                            : records?.goodsScore}
                        </span>
                      </article>
                      <article>
                        物流体验分:{' '}
                        <span
                          className={
                            records?.logisticsScore &&
                            records?.standardLogisticsScore &&
                            Number(records?.logisticsScore) <
                              Number(records?.standardLogisticsScore)
                              ? style['error-number']
                              : ''
                          }
                        >
                          {records?.logisticsScore === undefined || records?.logisticsScore === null
                            ? '-'
                            : records?.logisticsScore}
                        </span>
                      </article>
                      <article>
                        服务体验分:
                        <span
                          className={
                            records?.logisticsScore &&
                            records?.standardLogisticsScore &&
                            Number(records?.logisticsScore) <
                              Number(records?.standardLogisticsScore)
                              ? style['table-warning']
                              : ''
                          }
                        >
                          {records?.serviceScore === undefined || records?.serviceScore === null
                            ? '-'
                            : records?.serviceScore}
                        </span>
                      </article>
                    </div>
                  }
                >
                  <div style={{ fontWeight: 'bold' }}>{records?.platformShopName}</div>
                </Popover>
              ) : (
                <div style={{ fontWeight: 'bold' }}>{records?.platformShopName}</div>
              )}
              {/* table-warning */}
              {/* 低于80标记红色 */}
              {records?.platformSource === 'DY' && (
                <div>
                  商品好评率:
                  <span
                    className={
                      numberContrast({
                        num1: records?.standardFavorableRate,
                        num2: records?.favorableRate,
                      })
                        ? style['error-number']
                        : ''
                    }
                  >
                    {records?.favorableRate
                      ? `${(Number(records?.favorableRate) * 100).toFixed(2)}%`
                      : 0}
                  </span>
                </div>
              )}

              {/* 低于92%标记红色 */}
              {records.platformSource === 'DY' && (
                <div>
                  店铺体验分:
                  <span
                    className={
                      numberContrast({ num1: records?.standardStore, num2: records?.shopPoints })
                        ? style['error-number']
                        : ''
                    }
                  >
                    {records?.shopPoints || 0}
                  </span>
                </div>
              )}
              {tabsValue === 'live' && records?.isDisplayQualityScore ? (
                <div>
                  商品质量分{' '}
                  <span style={{ color: numberToColor(records?.goodsQualityScore, '#333333') }}>
                    {records?.goodsQualityScore}
                  </span>
                </div>
              ) : null}
            </div>
          );
        },
      },
    ];
    const endInit = [
      {
        title: '是否采买链路',
        draggable: true,
        key: 'specVersion',
        dataIndex: 'specVersion',
        width: 100,
        render: (specVersion: boolean) => (specVersion ? '是' : '否'),
      },
      {
        title: '采买资质状态',
        draggable: true,
        key: 'isSpecQualificationEmpty',
        dataIndex: 'isSpecQualificationEmpty',
        width: 100,
        render: (isSpec: boolean) => (isSpec ? '未上传' : '已上传'),
      },
      {
        title: '资质风险等级',
        draggable: true,
        key: 'legalStatus',
        dataIndex: 'legalStatus',
        render: (legalStatus: LEVEL, records: SelectionProcessKanbanPage) => {
          return (
            <>
              <p
                style={{
                  color: LEVEL_COLOR[records.legalStatus as keyof typeof LEVEL_COLOR],
                  marginRight: '8px',
                }}
              >
                {LEVEL_NAME[records.legalStatus as keyof typeof LEVEL_NAME] || '-'}
              </p>
              <div className="auditFw">
                {
                  IconStatus[
                    records?.auditDetailMap?.SUPPLIER?.auditState as keyof typeof IconStatus
                  ]?.icon
                }
                商家
              </div>
              <div className="auditFw">
                {
                  IconStatus[records?.auditDetailMap?.BRAND?.auditState as keyof typeof IconStatus]
                    ?.icon
                }
                品牌
              </div>
              <div>
                {
                  IconStatus[records?.auditDetailMap?.GOODS?.auditState as keyof typeof IconStatus]
                    ?.icon
                }
                商品
              </div>
            </>
          );
        },
        width: 100,
      },
      {
        title: '资质补充状态',
        draggable: true,
        kay: 'qualificationSupplementFlag',
        dataIndex: 'qualificationSupplementFlag',
        render: (qualificationSupplementFlag: string) => {
          return (
            <>{qualificationSupplementFlag === 'SUPPLEMENT_WAIT_AUDIT' ? '补充待审核' : '-'}</>
          );
        },
        width: 100,
      },
      {
        title: '质检状态',
        draggable: true,
        key: 'qualifyInspectionStatus',
        dataIndex: 'qualifyInspectionStatus',
        width: 100,
        render: (_: UALITY_INSPECTION_STATUS_ENUM) => {
          if (!_) {
            return <></>;
          }
          if (_ !== UALITY_INSPECTION_STATUS_ENUM.NO_INSPECTION) {
            return (
              <Tag color={UALITY_INSPECTION_STATUS_COLOR[_]}>
                {UALITY_INSPECTION_STATUS_NAME[_]}
              </Tag>
            );
          }
          return (
            <Tag
              style={{
                background: '#FFFBE6',
                borderColor: '#FFE58F',
                color: '#FAAD14',
              }}
            >
              {UALITY_INSPECTION_STATUS_NAME[_]}
            </Tag>
          );
        },
      },
      {
        title: '商务条件',
        draggable: true,
        key: 'business',
        dataIndex: 'business',
        width: 170,
        render: (_: any, records: SelectionProcessKanbanPage) => {
          return (
            <div>
              {tabsValue === 'live' ? (
                <p>
                  总佣金(含保量):
                  {records?.hideCommissionFlag ? (
                    ' *** %'
                  ) : (
                    <>
                      {records?.totalCommissionContainGuaranteed
                        ? `${(Number(records?.totalCommissionContainGuaranteed) * 100).toFixed(2)}%`
                        : '0.00%'}
                    </>
                  )}
                </p>
              ) : (
                <></>
              )}
              <p>
                线上佣金:
                {records?.hideCommissionFlag ? (
                  ' *** %'
                ) : (
                  <>
                    {records?.commissionRate
                      ? `${(Number(records?.commissionRate) * 100).toFixed(2)}%`
                      : '0.00%'}
                  </>
                )}
              </p>
              {/* 为0时不在列表页做展示  */}
              {records?.commissionRateOffline ? (
                <p>
                  线下佣金:
                  {records?.hideCommissionFlag ? ( // 根据后端返回字段判断显示隐藏
                    ' *** %'
                  ) : (
                    <>{`${(Number(records?.commissionRateOffline) * 100).toFixed(2)}%`}</>
                  )}
                </p>
              ) : (
                <p>线下佣金: 0.00%</p>
              )}
              {tabsValue === 'live' ? (
                <>
                  <p>
                    保量基础佣金:
                    {records?.hideCommissionFlag ? (
                      ' *** %'
                    ) : (
                      <>
                        <span>
                          {isNullOrUndefined(
                            (records as any)?.cooperationGuaranteed?.guaranteeBrandFeeRate,
                          )
                            ? '-'
                            : `${(
                                (records as any)?.cooperationGuaranteed?.guaranteeBrandFeeRate * 100
                              ).toFixed(2)}%`}
                        </span>
                        <span>
                          {(records as any)?.cooperationGuaranteed?.payDurationType &&
                            `(${
                              PAY_DAY_ABBREVIATE[
                                (records as any)?.cooperationGuaranteed?.payDurationType as PAY_DAY
                              ]
                            })`}
                        </span>
                      </>
                    )}
                  </p>
                  <p>
                    基础服务费:
                    {records?.hideCommissionFlag ? ' *** ' : <>{records?.brandFee || 0}</>}
                  </p>
                  {/* 为0时不在列表页做展示 */}
                  {records?.sectionFee ? (
                    <p>
                      切片费:
                      {records?.hideCommissionFlag ? ' *** ' : <>{records?.sectionFee || 0}</>}
                    </p>
                  ) : (
                    <></>
                  )}
                </>
              ) : (
                <></>
              )}
            </div>
          );
        },
      },

      {
        title: '人员归属',
        draggable: true,
        key: 'person',
        dataIndex: 'person',
        width: 100,
        render: (_: any, records: SelectionProcessKanbanPage) => {
          return (
            <div>
              {records?.projectName ? (
                <p style={{ fontWeight: 500 }}>{records?.projectName}</p>
              ) : (
                <></>
              )}
              {tabsValue === 'live' ? (
                <>
                  {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                    records?.bpStatus as STATUS_PERSON,
                  ) ? (
                    <p>商务: {records?.bpName || '-'}</p>
                  ) : (
                    <></>
                  )}
                  {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                    records?.operatorStatus as STATUS_PERSON,
                  ) ? (
                    <p>运营: {records?.operatorAuditorName || '-'}</p>
                  ) : (
                    <></>
                  )}
                  {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                    records?.selectionStatus as STATUS_PERSON,
                  ) ? (
                    <p>选品: {records?.selectionAuditorName || '-'}</p>
                  ) : (
                    <></>
                  )}
                  {![LEVEL.AUTO_PASS, LEVEL.SKIP].includes(records?.legalStatus as LEVEL) ? (
                    <p>法务: {records?.legalAuditorName || '-'}</p>
                  ) : (
                    <></>
                  )}
                </>
              ) : (
                <>
                  {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                    records?.bpStatus as STATUS_PERSON,
                  ) ? (
                    <p>商务: {records?.bpName || '-'}</p>
                  ) : (
                    <></>
                  )}
                  {![LEVEL.AUTO_PASS, LEVEL.SKIP].includes(records?.legalStatus as LEVEL) ? (
                    <p>法务: {records?.legalAuditorName || '-'}</p>
                  ) : (
                    <></>
                  )}
                </>
              )}
            </div>
          );
        },
      },

      {
        title: '选品阶段',
        draggable: true,
        key: 'step',
        dataIndex: 'step',
        width: 170,
        render: (_: any, records: SelectionProcessKanbanPage) => {
          return (
            <div>
              <div>
                {tabsValue !== 'live' ? (
                  <>
                    {/* {UN_SELECT_STEP_NAME[records?.status as UN_FlowStatus_ChoiceList]} */}
                    <Tag color={UN_SELECT_STEP_COLOR[records?.status as UN_FlowStatus_ChoiceList]}>
                      {UN_SELECT_STEP_NAME[records?.status as UN_FlowStatus_ChoiceList]}
                    </Tag>
                  </>
                ) : (
                  <></>
                )}
                {tabsValue === 'live' ? (
                  <>
                    <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                      {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                        records?.bpStatus as STATUS_PERSON,
                      ) ? (
                        <span style={{ width: '70px' }}>
                          商务: {STATUS_PERSON_LIST[records?.bpStatus as STATUS_PERSON] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                      {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                        records?.operatorStatus as STATUS_PERSON,
                      ) ? (
                        <span style={{ width: '70px' }}>
                          运营:{' '}
                          {STATUS_PERSON_LIST[records?.operatorStatus as STATUS_PERSON] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                      {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                        records?.selectionStatus as STATUS_PERSON,
                      ) ? (
                        <span style={{ width: '70px' }}>
                          选品:{' '}
                          {STATUS_PERSON_LIST[records?.selectionStatus as STATUS_PERSON] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                      {![LEVEL.AUTO_PASS, LEVEL.SKIP].includes(records?.legalStatus as LEVEL) ? (
                        <span style={{ width: '70px' }}>
                          法务: {LEVEL_NAME[records?.legalStatus as LEVEL] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                      {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                        records?.supplierStatus as STATUS_PERSON,
                      ) ? (
                        <span style={{ width: '70px' }}>
                          商家:{' '}
                          {STATUS_PERSON_LIST[records?.supplierStatus as STATUS_PERSON] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                      {records?.complianceStatus ? (
                        <span style={{ width: '70px' }}>
                          合规:{' '}
                          {COMPLIANCESTATUS_NAME[
                            records?.complianceStatus as COMPLIANCESTATUS_ENUM
                          ] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                      {records?.luxuryReviewStatus &&
                      records?.luxuryReviewStatus !== LUXURY_REVIEW_STATUS_ENUM.NO_NEED_REVIEW ? (
                        <span style={{ width: '70px' }}>
                          复查:{' '}
                          {LUXURY_REVIEW_STATUS_NAME[
                            records?.luxuryReviewStatus as LUXURY_REVIEW_STATUS_ENUM
                          ] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}

                      {records?.lowCommissionAuditStatus !== 'NONE' && (
                        <span style={{ width: '70px' }}>
                          低佣:
                          {
                            LowCommissionAuditStatusEnum[
                              records?.lowCommissionAuditStatus as keyof typeof LowCommissionAuditStatusEnum
                            ]
                          }
                          {records?.lowCommissionAuditorName
                            ? ' ' + records?.lowCommissionAuditorName
                            : ''}
                        </span>
                      )}
                      {records?.legalStatus === 'HIGH' && records?.specialAuditStatus && (
                        <div className="iconPGroup">
                          <span>资质特批：</span>
                          <span style={{ marginRight: '11px' }}>
                            {
                              ApproveStatus[
                                records.specialAuditStatus as keyof typeof ApproveStatus
                              ]
                            }
                          </span>
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                      {![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP].includes(
                        records?.bpStatus as STATUS_PERSON,
                      ) ? (
                        <span style={{ width: '70px' }}>
                          商务: {STATUS_PERSON_LIST[records?.bpStatus as STATUS_PERSON] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                      {![LEVEL.AUTO_PASS, LEVEL.SKIP].includes(records?.legalStatus as LEVEL) ? (
                        <span style={{ width: '70px' }}>
                          法务: {LEVEL_NAME[records?.legalStatus as LEVEL] || '-'}
                        </span>
                      ) : (
                        <></>
                      )}
                    </div>
                  </>
                )}
                {/* 审批中或者已通过显示 */}
                {tabsValue === 'live' &&
                [
                  GOODS_GRADES_STATE_ENUM.CONFIRMING,
                  GOODS_GRADES_STATE_ENUM.PASS,
                  GOODS_GRADES_STATE_ENUM.REJECT,
                ].includes(records?.qualityScoreAuditStatus) ? (
                  <div className="iconPGroup">
                    <span>商品分：</span>
                    <span style={{ marginRight: '11px' }}>
                      {
                        GOODS_GRADES_STATE_NAME[
                          records?.qualityScoreAuditStatus as GOODS_GRADES_STATE_ENUM
                        ]
                      }
                    </span>
                  </div>
                ) : null}
              </div>
            </div>
          );
        },
      },

      {
        title: '创建时间',
        draggable: true,
        key: 'gmtCreated',
        dataIndex: 'gmtCreated',
        render: (gmtCreated: string) => {
          return <>{gmtCreated ? moment(gmtCreated).format('YYYY-MM-DD HH:mm:ss') : '-'}</>;
        },
        width: 140,
      },
      {
        title: '上播链接',
        draggable: false,
        key: 'promotionLink',
        dataIndex: 'promotionLink',
        width: 120,
        fixed: 'right',
        render: (promotionLink: string, records: SelectionProcessKanbanPage) => {
          return (
            <>
              <p>
                <section>
                  {records?.cooperationMode && records.platformSource === 'DY' ? (
                    <Tag color={CooperationModeColorEnum[records.cooperationMode]}>
                      {CooperationModeEnum[records.cooperationMode]}
                    </Tag>
                  ) : null}
                </section>
                {promotionLink ? (
                  <Popover
                    content={
                      <div
                        style={{
                          width: '300px',
                          height: 'auto',

                          wordBreak: 'break-all',
                        }}
                      >
                        上播链接：{promotionLink}
                        <AuthWrapper functionName="f_selection_flow_board_copy_link">
                          <Icon
                            onClick={() => {
                              copyText(promotionLink || '');
                            }}
                            style={{ marginLeft: '8px', color: '#204EFF' }}
                            type="copy"
                          />
                        </AuthWrapper>
                      </div>
                    }
                  >
                    <p style={{ display: 'flex', color: '#204EFF', alignItems: 'center' }}>
                      <div
                        className="one-line-ellipsis"
                        onClick={() => {
                          window.open(promotionLink, '_blank');
                        }}
                      >
                        {promotionLink}
                      </div>
                    </p>
                  </Popover>
                ) : records?.cooperationMode === 'DIRECT' && records.platformSource === 'DY' ? (
                  <section style={{ display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: 'red' }}>创建失败</span>
                    <Popover content={<div>{records?.strategyFail}</div>}>
                      <Icon
                        type="question-circle"
                        theme="filled"
                        style={{ fontSize: '12px', marginLeft: '4px' }}
                      />
                    </Popover>
                  </section>
                ) : (
                  '-'
                )}
              </p>
              <div>
                {/* 非直播的补充链接换接口 */}
                <AuthWrapper functionName="f_selection_flow_board_require_link">
                  {['BP_CONFIRMING', 'CANCEL'].indexOf(records?.status as string) === -1 && (
                    // <SupplementLink info={records} onRefresh={onRefresh} tabsValue={tabsValue}>
                    //   <a>补充链接</a>
                    // </SupplementLink>
                    <BatchSupplementLink
                      buttonType="text"
                      info={records}
                      onRefresh={onRefresh}
                      tabsValue={tabsValue}
                    />
                  )}
                </AuthWrapper>
              </div>
              {/* <div>
                <AuthWrapper functionName="f_selection_flow_board_copy_link">
                  {records?.promotionLink ? (
                    <a
                      onClick={() => {
                        copyText(records?.promotionLink as string);
                      }}
                    >
                      复制链接
                    </a>
                  ) : (
                    <></>
                  )}
                </AuthWrapper>
              </div> */}
              <div>
                <AuthWrapper functionName="f_selection_flow_board_get_require_link">
                  {!records?.promotionLink &&
                  !!records?.totalCommission &&
                  records?.platformSource === 'TB' &&
                  [
                    FlowStatus_ChoiceList_Enum.WAIT_AUDIT,
                    FlowStatus_ChoiceList_Enum.WAIT_LIVE,
                  ].includes(records?.status as any) ? (
                    <a
                      onClick={() => {
                        getRun({ id: records?.id });
                      }}
                    >
                      请求链接
                    </a>
                  ) : (
                    <></>
                  )}
                </AuthWrapper>
              </div>
            </>
          );
        },
      },
      {
        title: '操作',
        draggable: false,
        key: 'other',
        dataIndex: 'other',
        width: 180,
        fixed: 'right',
        render: (_: string, records: SelectionProcessKanbanPage) => {
          return (
            <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              {tabsValue === 'live' ? (
                <>
                  {/* 商务确认 */}
                  <AuthWrapper functionName="f_selection_flow_board_confirm">
                    {records?.status === 'BP_CONFIRMING' ? (
                      // <a
                      //   style={{ marginRight: '6px' }}
                      //   onClick={() => {
                      //     openSingle(records, 'businessConfirm');
                      //   }}
                      // >
                      //   确认
                      // </a>
                      <BatchConfirmModal
                        single={true}
                        selectedFullKeys={[records] as SelectionProcessKanbanPageResult['records']}
                        openSingle={openSingle}
                        onSearch={onRefresh}
                      />
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  {/* 选品编辑 */}
                  <AuthWrapper functionName="f_selection_flow_board_ch_edit">
                    {['WAIT_AUDIT', 'WAIT_LIVE'].indexOf(records?.status as string) >= 0 ? (
                      <a
                        onClick={() => {
                          openEditDetail(records, 'editCh');
                        }}
                        style={{ marginRight: '6px' }}
                      >
                        编辑
                      </a>
                    ) : (
                      <></>
                    )}
                    {/* {records?.status === 'WAIT_AUDIT' ? (
                      <a
                        onClick={() => {
                          openEditDetail(records, 'editCh');
                        }}
                        style={{ marginRight: '6px' }}
                      >
                        编辑
                      </a>
                    ) : (
                      <></>
                    )} */}
                  </AuthWrapper>
                  {/* 选品通过 */}
                  <AuthWrapper functionName="f_selection_flow_board_ch_confirm">
                    {records?.status === 'WAIT_AUDIT' &&
                    ![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP, STATUS_PERSON.PASS].includes(
                      records?.selectionStatus as STATUS_PERSON,
                    ) ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          if (!selectorCheck(records.selectionAuditor)) {
                            message.error('被选品人员认领后的商品仅能由该认领人进行选品审核操作');
                            return;
                          }
                          openSingle(records, 'chooseConfirm');
                        }}
                      >
                        通过
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  {/* 运营通过 */}
                  <AuthWrapper functionName="f_selection_flow_board_op_confirm">
                    {records?.status === 'WAIT_AUDIT' &&
                    ![STATUS_PERSON.AUTO_PASS, STATUS_PERSON.SKIP, STATUS_PERSON.PASS].includes(
                      records?.operatorStatus as STATUS_PERSON,
                    ) ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          openSingle(records, 'opConfirm');
                        }}
                      >
                        通过
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  {/* 商务编辑  */}
                  <AuthWrapper functionName="f_selection_flow_board_bp_edit">
                    {records?.status === 'BP_CONFIRMING' ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          openEditDetail(records, 'edit');
                        }}
                      >
                        编辑
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  {/* 商务取消  INVITING BP_CONFIRMING*/}
                  <AuthWrapper functionName="f_selection_flow_board_cancel">
                    {[
                      FlowStatus_ChoiceList_Enum.INVITING,
                      FlowStatus_ChoiceList_Enum.BP_CONFIRMING,
                    ].includes(records?.status as FlowStatus_ChoiceList_Enum) ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          openSingle(records, 'businessCancle');
                        }}
                      >
                        取消
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  <AuthWrapper functionName="f_selection_flow_board_repeat_drop">
                    {/* ABORT_LIVE ABORT_WAIT_LIVE */}
                    {[
                      FlowStatus_ChoiceList_Enum.ABORT_LIVE,
                      FlowStatus_ChoiceList_Enum.ABORT_WAIT_LIVE,
                    ].includes(records?.status as FlowStatus_ChoiceList_Enum) ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          handleReplay(records);
                        }}
                      >
                        复播
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  <AuthWrapper functionName="f_selection_flow_board_del">
                    {/* WAIT_AUDIT WAIT_LIVE */}
                    {[
                      FlowStatus_ChoiceList_Enum.WAIT_AUDIT,
                      FlowStatus_ChoiceList_Enum.WAIT_LIVE,
                    ].includes(records?.status as FlowStatus_ChoiceList_Enum) ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          openSingle(records, 'del');
                        }}
                      >
                        掉品
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  <AuthWrapper functionName="f_selection_flow_board_add_pay_credential">
                    {[
                      FlowStatus_ChoiceList_Enum.WAIT_AUDIT,
                      FlowStatus_ChoiceList_Enum.WAIT_LIVE,
                      FlowStatus_ChoiceList_Enum.COMPLETED_LIVE,
                    ].includes(records?.status as FlowStatus_ChoiceList_Enum) &&
                    AddPayVoucherBtn(records) ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          handleAddModal(records);
                        }}
                      >
                        补充付款凭证
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  <AuthWrapper functionName="f_selection_flow_board_confirm_pay_certificate">
                    {records?.status === 'COMPLETED_LIVE' &&
                    records?.paymentVoucherStatus &&
                    records?.paymentVoucherStatus === 'WAIT_CONFIRM' ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          handleCloseConfirrmPayVoucher(records);
                        }}
                      >
                        确认付款凭证
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  <AuthWrapper functionName="f_selection_flow_board_mark_certificate_error">
                    {records?.status === 'COMPLETED_LIVE' &&
                    records?.paymentVoucherStatus &&
                    records?.paymentVoucherStatus === 'WAIT_CONFIRM' ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          handleOpenMarkVoucher(records);
                        }}
                      >
                        标记问题
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  <AuthWrapper functionName="f_selection_flow_board_high_risk_audit_with_draw">
                    {records?.legalStatus === 'HIGH' &&
                      records?.specialAuditStatus === 'CONFIRMING' && (
                        <a
                          style={{ marginRight: '6px' }}
                          onClick={() => {
                            handleCancelHighRiskAudit(records);
                          }}
                        >
                          特批撤回
                        </a>
                      )}
                  </AuthWrapper>
                  {/* 选品流程资质特批按钮 */}
                  {records?.status === 'WAIT_AUDIT' &&
                    records?.legalStatus === 'HIGH' &&
                    (!records?.specialAuditStatus ||
                      records?.specialAuditStatus === 'REJECT' ||
                      records?.specialAuditStatus === 'WITHDRAW') &&
                    // records?.specialAuditStatus !== 'PASS' &&
                    // records?.specialAuditStatus !== 'CONFIRMING' &&
                    (checkAuth('f_selection_flow_board_online_high_risk_audit') ||
                      checkAuth('f_selection_flow_board_high_risk_audit')) && (
                      <section style={{ marginRight: '6px' }}>
                        <HighRisk
                          onRefresh={onRefresh}
                          info={records as any}
                          isSelectTable={true}
                        />
                      </section>
                    )}
                  {/* {records?.status === 'WAIT_AUDIT' &&
                    ['AUTO_PASS', 'PASS', 'SKIP'].includes(records?.selectionStatus as string) &&
                    ['AUTO_PASS', 'PASS', 'SKIP'].includes(records?.supplierStatus as string) &&
                    ['AUTO_PASS', 'PASS', 'SKIP'].includes(records?.operatorStatus as string) &&
                    records?.legalStatus === 'HIGH' ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          handleOpenApproveVisible(records);
                        }}
                      >
                        高风险特批
                      </a>
                    ) : (
                      <></>
                    )} */}
                  {/* 退样登记，只有待直播、已播显示 */}
                  <AuthWrapper functionName="f_selection_flow_board_return_sample_register">
                    {['WAIT_LIVE', 'COMPLETED_LIVE'].indexOf(records?.status as string) >= 0 ? (
                      <a
                        onClick={() => {
                          handleOpenReturnSampleRegister(records);
                        }}
                        style={{ marginRight: '6px' }}
                      >
                        退样登记
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                  {/* 限制库存 */}
                  <AuthWrapper functionName="f_selection_flow_board_Limit_Inventory">
                    <a
                      onClick={() => {
                        handleOpenLimitInventory(records);
                      }}
                      style={{ marginRight: '6px' }}
                    >
                      限制库存
                    </a>
                  </AuthWrapper>
                  {/* 商品分特批 杭州抖音事业部并且商品质量分低于9分的显示 */}
                  {records?.allowQualityScoreAudit && (
                    <AuthWrapper functionName="f_selection_flow_board_special_approval_goods">
                      <a
                        onClick={() => {
                          // handleOpenLimitInventory(records);
                          openSpecialApprovalGoods(records);
                        }}
                        style={{ marginRight: '6px' }}
                      >
                        商品分特批
                      </a>
                    </AuthWrapper>
                  )}
                </>
              ) : (
                <>
                  <AuthWrapper functionName="f_selection_flow_board_un_live_del">
                    {/* WAIT_AUDIT WAIT_LIVE */}
                    {[
                      UN_FlowStatus_ChoiceList.WAIT_AUDIT,
                      UN_FlowStatus_ChoiceList.IN_COOPERATE,
                    ].includes(records?.status as UN_FlowStatus_ChoiceList) ? (
                      <a
                        style={{ marginRight: '6px' }}
                        onClick={() => {
                          openSingle(records, 'unDel');
                        }}
                      >
                        掉品
                      </a>
                    ) : (
                      <></>
                    )}
                  </AuthWrapper>
                </>
              )}

              <AuthWrapper functionName="f_selection_flow_board_look_legal">
                <a
                  onClick={() => {
                    setLegalInfo({
                      visible: true,
                      info: records,
                    });
                  }}
                  style={{ marginRight: '6px' }}
                >
                  查看资质
                </a>
              </AuthWrapper>
              <AuthWrapper functionName="f_selection_subject_special_approval">
                <SubjectSpecilApprovalModal info={records} onSearch={onRefresh} />
              </AuthWrapper>
            </div>
          );
        },
      },
    ];

    return tabsValue === 'live'
      ? [
          ...initHeader,
          {
            title: '场次信息',
            draggable: true,
            key: 'session',
            dataIndex: 'session',
            width: 200,
            render: (_: any, records: SelectionProcessKanbanPage) => {
              return (
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    {newRenderPlatformSourceLogo({
                      platform: records?.platformSource as any,
                      width: 14,
                    })}
                  </div>

                  {/* <div
              style={{
                display: 'flex',
                alignItems: 'center',
                fontSize: '12px',
                lineHeight: '20px',
              }}
            >
              <span>
                {records?.liveDate ? moment(records?.liveDate).format('YYYY-MM-DD') : '-'}
              </span>
            </div> */}
                  <div>{records?.liveRoundName || '-'}</div>
                  {/* <PopoverRowText text={records?.liveRoundName} /> */}
                </div>
              );
            },
          },
          ...storeMsg,
          {
            title: '销售数据',
            draggable: true,
            key: 'history',
            dataIndex: 'history',
            width: 200,
            render: (_: any, records: OperatorBoardList) => (
              <div>
                <p>
                  历史累计(支付)：¥
                  {records?.historySumSales ? formatFee(records?.historySumSales) : 0}
                </p>
                <p>场均(支付)：¥{records?.avgSales ? formatFee(records?.avgSales) : 0}</p>
                <p>
                  历史累计(T15)：¥
                  {records?.historySumSalesForFifteenDays
                    ? formatFee(records?.historySumSalesForFifteenDays)
                    : 0}
                </p>
                <p>
                  场均(T15)：¥
                  {records?.avgSalesForFifteenDays ? formatFee(records?.avgSalesForFifteenDays) : 0}
                </p>
              </div>
            ),
          },
          {
            title: '限制库存',
            draggable: true,
            key: 'preSaleStock',
            dataIndex: 'preSaleStock',
            width: 100,
            render: (_: any) => {
              return _ ? _ : '-';
            },
          },
          {
            title: '选品标签',
            draggable: true,
            key: 'selectionLabel',
            dataIndex: 'selectionLabel',
            width: 100,
            render: (_: any) => {
              return _ ? _ : '-';
            },
          },
          {
            title: '流程状态',
            draggable: true,
            key: 'status',
            dataIndex: 'status',
            width: 100,
            render: (_: any) => {
              return _ ? (
                <Tag color={SELECT_STEP_COLOR[_ as keyof typeof FlowStatus_ChoiceList_Enum]}>
                  {SELECT_STEP_NAME[_ as keyof typeof FlowStatus_ChoiceList_Enum]}
                </Tag>
              ) : (
                <></>
              );
            },
          },
          {
            title: '口播稿类型',
            draggable: true,
            key: 'spokenType',
            dataIndex: 'spokenType',
            width: 100,
            render: (_: string) => {
              return _ === 'PRIMARY_VERSION' ? (
                <Tag color="orange">初版口播稿</Tag>
              ) : _ === 'FINAL_VERSION' ? (
                <Tag color="green">终版口播稿</Tag>
              ) : (
                '-'
              );
            },
          },
          {
            title: '直播服务类型',
            draggable: true,
            key: 'liveServiceType',
            dataIndex: 'liveServiceType',
            width: 100,
            render: (liveServiceType: string) => {
              return (
                <p style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                  {liveServiceType ?? '-'}
                </p>
              );
            },
          },
          {
            title: '链接确认状态',
            dataIndex: 'linkCheckStatus',
            draggable: true,
            key: 'linkCheckStatus',
            width: 100,
            render: (
              linkCheckStatus: 'WAIT_CHECK' | 'CAN_PROMOTION' | 'NO_PROMOTION',
              record: SelectionProcessKanbanPage,
            ) => {
              return record.platformSource === 'TB' ? (
                <Space>
                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <div>
                      {linkCheckStatus ? (
                        <Tag
                          style={{ marginBottom: '8px' }}
                          color={lINKCHECKSTATUS_STEP_COLOR[linkCheckStatus]}
                        >
                          {LinkPromotionCheckStatusEnum[linkCheckStatus]}
                        </Tag>
                      ) : (
                        '-'
                      )}
                      {record?.promotionLink && (
                        <AuthWrapper functionName="f_link_edit_live">
                          <EditLinkStatus id={record?.id} onRefresh={onRefresh} />
                        </AuthWrapper>
                      )}
                    </div>
                    <Popover
                      content={
                        <p style={{ width: '300px', wordBreak: 'break-all' }}>
                          {record?.linkCheckRemark || '-'}
                        </p>
                      }
                    >
                      <div className="two-line-ellipsis">备注：{record.linkCheckRemark || '-'}</div>
                    </Popover>
                  </div>
                </Space>
              ) : (
                '-'
              );
            },
          },
          ...endInit,
        ]
      : [
          ...initHeader,
          {
            title: '服务类型',
            draggable: true,
            key: 'serviceType',
            dataIndex: 'serviceType',
            width: 90,
            render: (_: SERVER_TYPE) => (_ ? SERVER_TYPE_NAME[_] : '-'),
          },
          {
            title: '合作信息',
            draggable: true,
            key: 'session',
            dataIndex: 'session',
            width: 140,
            render: (_: any, records: any) => {
              return (
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    {newRenderPlatformSourceLogo({ platform: records?.platformSource, width: 14 })}
                  </div>

                  {/* <div
              style={{
                display: 'flex',
                alignItems: 'center',
                fontSize: '12px',
                lineHeight: '20px',
              }}
            >
              <span>
                {records?.liveDate ? moment(records?.liveDate).format('YYYY-MM-DD') : '-'}
              </span>
            </div> */}
                  <div>
                    {records?.startTime}~{records?.endTime}-{records?.liveRoomName}
                  </div>
                </div>
              );
            },
          },
          ...storeMsg,
          ...endInit,
        ];
  }, [
    tabsValue,
    onRefresh,
    openEditDetail,
    openSingle,
    handleAddModal,
    handleCloseConfirrmPayVoucher,
    handleOpenMarkVoucher,
    handleOpenApproveVisible,
    setLegalInfo,
    handleOpenReturnSampleRegister,
    handleCancelHighRiskAudit,
    handleOpenLimitInventory,
    openSpecialApprovalGoods,
    sortedInfo,
  ]);

  const clearSelectKeys = () => {
    setSelectedFullKeys([]);
    setSelectedKeys([]);
  };
  return {
    columns,
    rowSelection,
    selectedKeys,
    selectedFullKeys,
    setSelectedKeys,
    setSelectedFullKeys,
    getLoading,
    droppedProductRepeatLoading,
    clearSelectKeys,
    setSortedInfo,
    sortedInfo,
  };
};
