import React, { useMemo, useEffect } from 'react';
import { Input, Select, Spin, DatePicker, Radio } from 'antd';
import { Cascader, BatchInput, NumberInterval } from '../components';
import { useBrandList, useSearchRoomList, useDepartment, useTalent } from './index';
import moment from 'moment';
import {
  SELECT_STEP_LIST,
  LEVEL_LIST,
  PAY_STATUS_LIST,
  SERVER_TYPE_LIST,
  UN_SELECT_STEP_LIST,
  SELECT_REVIEW_STATUS,
} from '../types';
import { useSupplierName } from './useSupplierName';
import {
  plantformListAll,
  QUALIFICATION_REPLENISHMENT_STATUS_LIST,
  UALITY_INSPECTION_STATUS_LIST,
} from '../../../../web_modules/types';
import { useServiceType } from './useServiceType';
import debounce from 'lodash/debounce';
import PriceRangeInput from '@/components/PriceRangeInput/PriceRangeInput';
import SingleSelect from '@/components/SingleSelect';
import {
  trueStatusList,
  paymentVoucherStatusList,
  qualificationStatusList,
} from '@/pages/choice-list-new/components/choiceListRight/SearchForm/constant';
import { useGroup } from './useGroup';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import DateRangeSelect from '@/components/DateRangeSelect';
import BrandSelect from '@/components/BrandSelect';
import { cooperationPaymentStatusList } from '@/common/constants/moduleConstant';
import Category from 'web-common-modules/components/Category';
import { PlantformName, PLATFORM_ENUM } from '../../../../web_modules/types';

const { Option } = Select;
const { RangePicker } = DatePicker;
export enum LinkPromotionCheckStatusEnum {
  'WAIT_CHECK' = '待确认',
  'NO_PROMOTION' = '不可上播',
  'CAN_PROMOTION' = '可上播',
  'NULL_VALUE' = '空',
}
export const useSearch = (
  tabsValue: string,
  codeList: Array<Record<string, string>>,
  form: any,
) => {
  const { getBrandListLoading, brandList, onSearchBrand, handleBrandChange } = useBrandList();
  const { liveList, liveAllRoomLoading, handleSearch, handleChane, liveAllRoomRun } =
    useSearchRoomList();
  const { departmentListLoading, departmentList } = useDepartment();
  const {
    list,
    loading: getSupplierNameLoading,
    debounce: supplierNameDebounce,
    reset,
  } = useSupplierName();
  const {
    list: talentList,
    loading: talentLoading,
    debounce: talentDebounce,
    reset: talentReset,
    deptChangeGetList,
  } = useTalent();

  const {
    handleGroupChange,
    groupList,
    loading: groupLoading,
    onSearchGroup,
    handleGroupBlur,
    onSearchGroupByPlatformAndDept,
  } = useGroup();
  const {
    loading: serviceTypeLoading,
    list: serviceTypeList,
    onSearch: searchServiceTypeList,
  } = useServiceType();
  const handleSearchServiceTypeList = debounce((serviceTypeName?: string) => {
    searchServiceTypeList({ serviceTypeName, size: 100, current: 1, liveRoomIds: liveRoomId });
  }, 500);
  const { codeList: selectList } = useCode(CODE_ENUM.SELECTION_LABEL);
  const { platformSource, deptId, liveRoomId } = form.getFieldsValue();

  useEffect(() => {
    if (platformSource && deptId) {
      onSearchGroupByPlatformAndDept([platformSource, 'ALL'], deptId);
    }
  }, [platformSource, deptId]);

  useEffect(() => {
    if (liveRoomId?.length) {
      searchServiceTypeList({ size: 100, current: 1, liveRoomIds: liveRoomId });
    }
  }, [liveRoomId]);

  const options = useMemo(() => {
    const init = {
      // 待改变
      spuName: {
        label: '商品名称',
        draggable: false,
        renderNode: <BatchInput label="商品名称" />,
      },
      spuNo: {
        label: '商品编号',
        draggable: true,
        renderNode: <BatchInput label="商品编号" />,
      },

      platformSpuId: {
        label: '平台商品ID',
        draggable: true,
        renderNode: <BatchInput label="平台商品ID" />,
      },
      legalStatus: {
        label: '资质风险等级',
        draggable: true,
        hocOptions: {},
        renderNode: (
          <Select allowClear mode="multiple" placeholder="全部" filterOption={false}>
            {LEVEL_LIST.map((item) => (
              <Option key={item.value} value={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
        ),
      },
      liveRoomId: {
        label: '直播间',
        draggable: true,
        renderNode: (
          <Select
            placeholder="请输入"
            allowClear
            loading={talentLoading}
            showSearch
            filterOption={false}
            optionFilterProp="children"
            dropdownMatchSelectWidth={false}
            onSearch={(value) => {
              talentDebounce(value);
            }}
            onChange={(val) => {
              if (!val) {
                talentReset();
              }
              form.setFieldsValue({
                liveServiceTypeIds: undefined,
              });
            }}
            onBlur={() => {
              talentReset();
            }}
            defaultActiveFirstOption={false}
            mode="multiple"
            maxTagCount={1}
          >
            {talentList?.map((item) => {
              // const { companyInfo } = item;
              return (
                <Option value={item?.id} key={item?.id}>
                  {item?.name}-{item?.talentNo}
                </Option>
              );
            })}
          </Select>
        ),
      },

      brandId: {
        label: '品牌',
        draggable: false,
        renderNode: <BrandSelect></BrandSelect>,
      },
      liveTime: {
        label: tabsValue === 'live' ? '直播日期' : '合作日期',
        draggable: true,
        hocOptions: {
          // initialValue: [moment().startOf('day'), moment().add(30, 'day').endOf('day')],
        },
        renderNode: (
          <DateRangeSelect
            defaultValue={tabsValue === 'live' ? 'nowNext30Days' : ''}
            dateFormat="YYYY-MM-DD"
          ></DateRangeSelect>
        ),
      },
      supplierId: {
        label: '商家名称',
        draggable: true,
        renderNode: (
          <Select
            placeholder="请输入"
            allowClear
            loading={getSupplierNameLoading}
            showSearch
            filterOption={false}
            dropdownMatchSelectWidth={false}
            onSearch={(value) => {
              supplierNameDebounce(value);
            }}
            onChange={(val) => {
              if (!val) {
                reset();
              }
            }}
            defaultActiveFirstOption={false}
          >
            {list?.map((item) => {
              const { companyInfo } = item;
              return (
                <Option value={item?.id} key={item?.id}>
                  {item?.storeName}-{companyInfo?.companyCode}
                </Option>
              );
            })}
          </Select>
        ),
      },
      no: {
        label: '选品流程',
        draggable: false,
        renderNode: <BatchInput label="选品流程" />,
      },
      cooperationOrderId: {
        label: '合作订单',
        draggable: true,
        renderNode: <BatchInput label="合作订单" />,
      },
      processAttribution: {
        label: '流程归属',
        draggable: true,
        // renderNode: (
        //   <Radio.Group>
        //     <Radio value={'MINE'}>我的</Radio>
        //   </Radio.Group>
        // ),
        renderNode: (
          <Select allowClear placeholder="全部">
            <Option value={'MINE'}>我的</Option>
          </Select>
        ),
      },

      deptId: {
        label: '事业部',
        draggable: true,
        renderNode: (
          <Select
            allowClear
            loading={departmentListLoading}
            placeholder="全部"
            filterOption={false}
            onChange={(value: any) => {
              if (value) {
                deptChangeGetList({
                  deptId: value,
                });
              } else {
                talentReset();
              }
              form.setFieldsValue({
                groupId: undefined,
              });
            }}
          >
            {departmentList?.map((item) => (
              <Option value={item.id} key={item.id}>
                {item.deptName}
              </Option>
            ))}
          </Select>
        ),
      },
      platformShopName: {
        label: '店铺名称',
        draggable: true,
        renderNode: <Input placeholder="请输入店铺名称" maxLength={100} />,
      },
      favorableRate: {
        label: '商品好评率(%)',
        draggable: true,
        hocOptions: {
          rules: [
            {
              validator: (rule: any, value: any, callback: any) => {
                if (!value) {
                  return callback();
                }
                const [min, max] = value;
                if (!min && !max) {
                  return callback();
                }
                if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                  return callback('请输入0-100的数字');
                }
                if ((min && Number(min) > 100) || (max && Number(max) > 100)) {
                  return callback('请输入0-100的数字');
                }
                if (min && max && Number(min) > Number(max)) {
                  return callback('最低值应该小于最高值');
                }
                callback();
              },
            },
          ],
        },
        renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
      },
      shopPoints: {
        label: '店铺体验分',
        draggable: true,
        hocOptions: {
          rules: [
            {
              validator: (rule: any, value: any, callback: any) => {
                if (!value) {
                  return callback();
                }
                const [min, max] = value;
                if (!min && !max) {
                  return callback();
                }
                if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                  return callback('请输入0-100的数字');
                }
                if ((min && Number(min) > 100) || (max && Number(max) > 100)) {
                  return callback('请输入0-100的数字');
                }
                if (min && max && Number(min) > Number(max)) {
                  return callback('最低值应该小于最高值');
                }
                callback();
              },
            },
          ],
        },
        renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
      },
      specVersion: {
        label: '是否采买链路',
        draggable: true,
        // renderNode: (
        //   <Radio.Group>
        //     <Radio value={'MINE'}>我的</Radio>
        //   </Radio.Group>
        // ),
        renderNode: (
          <Select allowClear placeholder="全部">
            <Option value={true as any}>是</Option>
            <Option value={false as any}>否</Option>
          </Select>
        ),
      },
      isSpecQualificationEmpty: {
        label: '采买资质状态',
        draggable: true,
        // renderNode: (
        //   <Radio.Group>
        //     <Radio value={'MINE'}>我的</Radio>
        //   </Radio.Group>
        // ),
        renderNode: (
          <Select allowClear placeholder="全部">
            <Option value={false as any}>已上传</Option>
            <Option value={true as any}>未上传</Option>
          </Select>
        ),
      },
      qualifyInspectionStatus: {
        label: '质检状态',
        span: 1,
        draggable: true,
        renderNode: (
          <Select allowClear placeholder="请选择质检状态">
            {UALITY_INSPECTION_STATUS_LIST?.map((item) => (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        ),
      },
    };

    const businessStatus = {
      serviceTypeList: {
        label: '业务类型',
        renderNode: (
          <Select allowClear placeholder="请选择业务类型" mode="multiple" filterOption={false}>
            {SERVER_TYPE_LIST?.map((item) => (
              <Option key={item.value} value={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
        ),
      },
    };

    const guaranteeSearch = {
      guaranteeProportionFlag: {
        label: '是否保比',
        draggable: true,
        hocOptions: {},
        renderNode: (
          <Select allowClear placeholder="请选择">
            <Option key={true} value={true}>
              是
            </Option>
            <Option key={false} value={false}>
              否
            </Option>
          </Select>
        ),
      },
      guaranteeQuantityFlag: {
        label: '是否保量',
        draggable: true,
        hocOptions: {},
        renderNode: (
          <Select allowClear placeholder="请选择">
            <Option key={true} value={true}>
              是
            </Option>
            <Option key={false} value={false}>
              否
            </Option>
          </Select>
        ),
      },
    };
    return tabsValue === 'live'
      ? {
          ...init,
          // ...payStatus,
          status: {
            label: '选品阶段',
            draggable: true,
            hocOptions: {},
            renderNode: (
              <Select allowClear mode="multiple" placeholder="全部" filterOption={false}>
                {SELECT_STEP_LIST.map((item) => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            ),
          },
          /**
           * operatorAuditor 运营
           * selectionAuditor 选品
           * bpId 商务
           */
          person: {
            label: '人员归属',
            draggable: true,
            renderNode: <Cascader placeholder="全部" allowClear />,
          },
          brandFee: {
            label: '基础服务费',
            span: 1,
            draggable: true,
            renderNode: <PriceRangeInput></PriceRangeInput>,
          },
          platformSource: {
            label: '平台',
            draggable: true,
            renderNode: (
              <Select
                allowClear
                placeholder="全部"
                onChange={(value) => {
                  form.setFieldsValue({
                    cateIds: undefined,
                    groupId: undefined,
                  });
                }}
              >
                {plantformListAll?.map((item) => (
                  <Option value={item.value} key={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            ),
          },
          cateId: {
            label: '商品类目',
            draggable: true,
            renderNode: (
              <Category
                style={{ width: '100%' }}
                filterOption={false}
                maxTagCount={2}
                allowClear
                mode="multiple"
                placeholder="请先选择平台类型"
                source={
                  platformSource === 'TB'
                    ? 'TAOBAO'
                    : platformSource === 'KS'
                    ? 'KSXD'
                    : platformSource
                }
                width="100%"
                defaultApi={false}
              ></Category>
            ),
          },
          selectionLabel: {
            label: '选品标签',
            draggable: true,
            renderNode: <SingleSelect options={selectList}></SingleSelect>,
          },

          linkCheckStatusList: {
            label: '链接确认状态',
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="全部" mode="multiple">
                {Object.entries(LinkPromotionCheckStatusEnum).map((key) => (
                  <Option value={key[0]} key={key[0]}>
                    {key[1]}
                  </Option>
                ))}
              </Select>
            ),
          },
          // selectionGroup: {
          //   label: '选品组',
          //   renderNode: (
          //     <Select
          //       allowClear
          //       placeholder="请选择"
          //       mode="multiple"
          //       loading={serviceTypeLoading}
          //       onSearch={handleSearchServiceTypeList}
          //       filterOption={false}
          //       showSearch
          //       onChange={(val) => {
          //         if (!val?.length) {
          //           handleSearchServiceTypeList();
          //         }
          //       }}
          //       defaultActiveFirstOption={false}
          //     >
          //       {serviceTypeList?.map((item) => (
          //         <Option value={item.id} key={item.name}>
          //           {item.name}
          //         </Option>
          //       ))}
          //     </Select>
          //   ),
          // },
          liveServiceTypeIds: {
            label: '直播服务类型',
            draggable: true,
            renderNode: (
              <Select
                allowClear
                placeholder={liveRoomId?.length ? '请选择' : '请先选择直播间'}
                mode="multiple"
                loading={serviceTypeLoading}
                onSearch={handleSearchServiceTypeList}
                filterOption={false}
                showSearch
                onChange={(val) => {
                  if (!val?.length) {
                    handleSearchServiceTypeList();
                  }
                }}
                defaultActiveFirstOption={false}
                notFoundContent={liveRoomId?.length ? null : '请先选择直播间后选择直播服务类型'}
              >
                {(liveRoomId?.length ? serviceTypeList : [])?.map((item) => (
                  <Option value={item.id} key={item.name}>
                    {item.name}
                  </Option>
                ))}
              </Select>
            ),
          },
          notUploadFlag: {
            label: '付款凭证',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear>
                {trueStatusList.map((item, index) => {
                  return (
                    <Option key={index} value={item.value}>
                      {item.name}
                    </Option>
                  );
                })}
              </Select>
            ),
          },
          paymentVoucherStatus: {
            label: '付款凭证状态',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear>
                {paymentVoucherStatusList.map((item, index) => {
                  return (
                    <Option key={index} value={item.value}>
                      {item.name}
                    </Option>
                  );
                })}
              </Select>
            ),
          },
          // paymentOrderStatus: {
          //   label: '订单付款状态',
          //   span: 1,
          //   draggable: true,
          //   renderNode: (
          //     <Select allowClear>
          //       {cooperationPaymentStatusList.map((item, index) => {
          //         return (
          //           <Option key={index} value={item.value}>
          //             {item.label}
          //           </Option>
          //         );
          //       })}
          //     </Select>
          //   ),
          // },
          groupId: {
            label: '项目组',
            draggable: true,
            renderNode: (
              <Select
                allowClear
                placeholder={
                  platformSource && deptId ? '请选择项目组' : '请选择事业部和平台后再选择项目组'
                }
                filterOption={false}
                onSearch={onSearchGroup}
                mode="multiple"
                showSearch
                onChange={handleGroupChange}
                loading={groupLoading}
                onBlur={handleGroupBlur}
                notFoundContent={
                  platformSource && deptId ? null : '请选择事业部和平台后再选择项目组'
                }
              >
                {(platformSource && deptId ? groupList : [])?.map((item) => (
                  <Option key={item?.projectGroupId} value={item?.projectGroupId}>
                    {item?.projectGroupName}-{PlantformName[item?.platformEnum as PLATFORM_ENUM]}
                  </Option>
                ))}
              </Select>
            ),
          },
          supplierAuditStateSet: {
            label: '商家资质结果',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear mode="multiple" maxTagCount={1}>
                {qualificationStatusList.map((item, index) => {
                  return (
                    <Option key={index} value={item.value}>
                      {item.name}
                    </Option>
                  );
                })}
              </Select>
            ),
          },
          brandAuditStateSet: {
            label: '品牌资质结果',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear mode="multiple" maxTagCount={1}>
                {qualificationStatusList.map((item, index) => {
                  return (
                    <Option key={index} value={item.value}>
                      {item.name}
                    </Option>
                  );
                })}
              </Select>
            ),
          },
          goodsAuditStateSet: {
            label: '商品资质结果',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear mode="multiple" maxTagCount={1}>
                {qualificationStatusList.map((item, index) => {
                  return (
                    <Option key={index} value={item.value}>
                      {item.name}
                    </Option>
                  );
                })}
              </Select>
            ),
          },
          // 资质补充状态
          qualificationSupplementFlag: {
            label: '资质补充状态',
            draggable: true,
            span: 1,
            renderNode: (
              <Select allowClear placeholder="全部">
                {QUALIFICATION_REPLENISHMENT_STATUS_LIST.map((item) => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            ),
          },
          selectionStatus: {
            label: '选品审核状态',
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="全部">
                {SELECT_REVIEW_STATUS?.map((item) => (
                  <Option value={item.value} key={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            ),
          },
          operatorStatus: {
            label: '运营审核状态',
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="全部">
                {SELECT_REVIEW_STATUS?.map((item) => (
                  <Option value={item.value} key={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            ),
          },
          commissionRate: {
            label: '线上佣金(%)',
            span: 1,
            draggable: true,
            hocOptions: {
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (!value) {
                      return callback();
                    }
                    const [min, max] = value;
                    if (!min && !max) {
                      return callback();
                    }
                    if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                      return callback('请输入0-100的数字');
                    }
                    if ((min && Number(min) > 100) || (max && Number(max) > 100)) {
                      return callback('请输入0-100的数字');
                    }
                    if (min && max && Number(min) > Number(max)) {
                      return callback('最低值应该小于最高值');
                    }
                    callback();
                  },
                },
              ],
            },
            renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
          },
          sectionFee: {
            label: '切片费',
            span: 1,
            draggable: true,
            renderNode: <PriceRangeInput></PriceRangeInput>,
          },
          commissionRateOffline: {
            label: '线下佣金（%）',
            span: 1,
            draggable: true,
            hocOptions: {
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (!value) {
                      return callback();
                    }
                    const [min, max] = value;
                    if (!min && !max) {
                      return callback();
                    }
                    if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                      return callback('请输入0-100的数字');
                    }
                    if ((min && Number(min) > 100) || (max && Number(max) > 100)) {
                      return callback('请输入0-100的数字');
                    }
                    if (min && max && Number(min) > Number(max)) {
                      return callback('最低值应该小于最高值');
                    }
                    callback();
                  },
                },
              ],
            },
            renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
          },
          linkFlag: {
            label: '有无链接',
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="全部">
                <Option value={1}>有链接</Option>
                <Option value={0}>无链接</Option>
              </Select>
            ),
          },
          frameworkGoods: {
            label: '框架商品',
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="全部">
                <Option value={1}>是</Option>
                <Option value={0}>否</Option>
              </Select>
            ),
          },
          livePlatformSpuIds: {
            label: '上播商品ID',
            draggable: false,
            renderNode: <BatchInput label="上播商品ID" />,
          },
          // guaranteeQuantityGoods: {
          //   label: '保量商品',
          //   draggable: true,
          //   renderNode: (
          //     <Select allowClear placeholder="全部">
          //       <Option value={1}>是</Option>
          //       <Option value={0}>否</Option>
          //     </Select>
          //   ),
          // },
          // isPromiseRatio: {
          //   label: '是否保比',
          //   draggable: true,
          //   renderNode: (
          //     <Select allowClear placeholder="全部">
          //       <Option value={1}>是</Option>
          //       <Option value={0}>否</Option>
          //     </Select>
          //   ),
          // },
          ...guaranteeSearch,
          luckyProductFlag: {
            label: '是否福袋商品',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="请选择是否福袋商品">
                <Option value={true}>是</Option>
                <Option value={false}>否</Option>
              </Select>
            ),
          },
          spokenIsComplete: {
            label: '手卡是否完整',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="请选择手卡是否完整">
                <Option value={true}>是</Option>
                <Option value={false}>否</Option>
              </Select>
            ),
          },
          serviceAgreementSigned: {
            label: '是否签署协议',
            span: 1,
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="请选择是否签署协议">
                <Option value={true}>是</Option>
                <Option value={false}>否</Option>
              </Select>
            ),
          },
        }
      : {
          ...init,
          ...businessStatus,
          //UN_SELECT_STEP_LIST
          status: {
            label: '选品阶段',
            hocOptions: {},
            renderNode: (
              <Select allowClear mode="multiple" placeholder="全部" filterOption={false}>
                {UN_SELECT_STEP_LIST.map((item) => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            ),
          },
          /**
           * operatorAuditor 运营
           * selectionAuditor 选品
           * bpId 商务
           */
          person: {
            label: '人员归属',
            renderNode: (
              <Cascader
                placeholder="全部"
                allowClear
                cList={[
                  {
                    employeeId: 'bpId',
                    employeeName: '商务',
                    bizRoleType: 'BUSINESS',
                  },
                ]}
              />
            ),
          },
          commissionRate: {
            label: '线上佣金(%)',
            span: 1,
            draggable: true,
            hocOptions: {
              rules: [
                {
                  validator: (rule: any, value: any, callback: any) => {
                    if (!value) {
                      return callback();
                    }
                    const [min, max] = value;
                    if (!min && !max) {
                      return callback();
                    }
                    if ((min && Number(min) < 0) || (max && Number(max) < 0)) {
                      return callback('请输入0-100的数字');
                    }
                    if ((min && Number(min) > 100) || (max && Number(max) > 100)) {
                      return callback('请输入0-100的数字');
                    }
                    if (min && max && Number(min) > Number(max)) {
                      return callback('最低值应该小于最高值');
                    }
                    callback();
                  },
                },
              ],
            },
            renderNode: <NumberInterval minPlaceholder="最低" maxPlaceholder="最高" />,
          },
          platformSource: {
            label: '平台',
            draggable: true,
            renderNode: (
              <Select allowClear placeholder="全部">
                <Option value={'DY'} key={'DY'}>
                  抖音
                </Option>
                <Option value={'TB'} key={'DY'}>
                  淘宝
                </Option>
                <Option value={'WECHAT_VIDEO'} key={'DY'}>
                  视频号
                </Option>
              </Select>
            ),
          },
        };
  }, [
    tabsValue,
    talentList,
    list,
    getBrandListLoading,
    brandList,
    onSearchBrand,
    handleBrandChange,
    departmentListLoading,
    departmentList,
    getSupplierNameLoading,
    supplierNameDebounce,
    reset,
    talentLoading,
    talentReset,
    serviceTypeList,
    groupList,
    deptChangeGetList,
    platformSource, // 添加platformSource依赖，确保platformSource改变时重新计算options
  ]);

  return { options, talentList, list, serviceTypeList, groupList }; // 此处增加talentList是为了传给searchForm用来监听渲染直播间下拉，否则直播间下拉不会更新
};
