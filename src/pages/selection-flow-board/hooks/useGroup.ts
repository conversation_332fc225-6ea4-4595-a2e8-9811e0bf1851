import { useEffect, useState } from 'react';
import { projectGroupList } from '../services/yml/index';
import { useRequest } from 'ahooks';
import { handleResponse } from '@/utils/response';
import { debounce } from 'lodash';
import { message } from 'antd';

export const useGroup = () => {
  const [groupList, setGroupList] = useState<any[]>([]);
  const [condition, setCondition] = useState({});
  const { loading, run } = useRequest(projectGroupList, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res).then((res) => {
        if (res?.success) {
          const { records } = res?.result || {};
          setGroupList(records || []);
        } else {
          message.warning(res?.message || '网络异常');
        }
        // setGroupList(res?.result?.spuBrandByName || []);
      });
    },
  });

  useEffect(() => {
    run({ current: 1, size: 50, projectGroupStatusEnum: 'ENABLE' });
  }, []);

  const handleGroupChange = (v: string) => {
    if (!v) {
      run({ current: 1, size: 50, projectGroupStatusEnum: 'ENABLE', ...condition });
    }
  };

  const onSearchGroup = debounce((name: string) => {
    run({
      current: 1,
      size: 50,
      projectGroupName: name,
      projectGroupStatusEnum: 'ENABLE',
      ...condition,
    });
  }, 500);
  const onSearchGroupByDept = debounce((departmentName: string) => {
    run({ current: 1, size: 50, departmentName, projectGroupStatusEnum: 'ENABLE' });
  }, 500);
  const onSearchGroupByDeptId = debounce((deptId: string) => {
    run({ current: 1, size: 50, deptId, projectGroupStatusEnum: 'ENABLE' });
  }, 500);
  const handleGroupBlur = () => {
    run({ current: 1, size: 50, projectGroupStatusEnum: 'ENABLE', ...condition });
  };

  // 支持平台和事业部筛选
  const onSearchGroupByPlatformAndDept = debounce((platformSource: string, deptId: string) => {
    setCondition({ platformSource, deptId });
    run({ current: 1, size: 50, platformSource, deptId, projectGroupStatusEnum: 'ENABLE' });
  }, 500);

  return {
    handleGroupChange,
    groupList,
    loading,
    onSearchGroup,
    onSearchGroupByDept,
    handleGroupBlur,
    onSearchGroupByDeptId,
    getList: run,
    onSearchGroupByPlatformAndDept,
  };
};
