import { useRequest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import usePagination from '@/hooks/usePagination';
import {
  selectionProcessKanbanPage,
  nonLiveSelectionPage,
  SelectionProcessKanbanPageResult,
  NonLiveSelectionPageResult,
  NonLiveSelectionPageRequest,
  SelectionProcessKanbanPageRequest,
  summaryInfo,
} from '../services/yml';
import { handleResponse } from '@/utils/response';

type Res =
  | Required<SelectionProcessKanbanPageResult>['records']
  | Required<NonLiveSelectionPageResult>['records'];

type Req = NonLiveSelectionPageRequest | SelectionProcessKanbanPageRequest;
export const useList = (
  tabsValue: string,
  formatList: undefined | ((value: any) => any) = undefined,
  size?: number,
  subCurrent?: boolean,
) => {
  // 列表数据
  const [dataSource, setDataSource] = useState<Res>();
  // 列表合计
  const [dataSummary, setDataSummary] = useState<Res>();
  // 保存搜索数据
  const [condition, setCondition] = useState<Req>();
  const { pagination, setPagination } = usePagination({
    current: 1,
    size: size || 20,
  });

  const { run: summaryRun } = useRequest(summaryInfo, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res)
        .then((res) => {
          setDataSummary(res.result || {});
        })
        .catch(() => {
          setDataSummary({});
        });
    },
  });

  const { loading, run, cancel } = useRequest(selectionProcessKanbanPage, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res)
        .then((res) => {
          const { records = [], total, current, size } = res.result;
          setPagination({
            total,
            current: subCurrent ? current + 1 : current,
            // size,
          });
          if (formatList) {
            setDataSource(formatList(records) || []);
            return;
          }
          setDataSource((records as Res) || []);
        })
        .catch(() => {
          setDataSource([]);
        });
    },
  });
  const {
    loading: unLiveLoading,
    run: unLiveRun,
    cancel: cancelUnLive,
  } = useRequest(nonLiveSelectionPage, {
    manual: true,
    onSuccess: ({ res }) => {
      handleResponse(res)
        .then((res) => {
          const { records = [], total, current, size } = res.result;
          setPagination({
            total,
            current: subCurrent ? current + 1 : current,
            size,
          });
          if (formatList) {
            setDataSource(formatList(records) || []);
            return;
          }
          setDataSource((records as Res) || []);
        })
        .catch(() => {
          setDataSource([]);
        });
    },
  });

  // 分页修改
  const onPageChange = (current: number, size: number) => {
    setPagination({ current, size, total: pagination.total });
    const params = { ...condition, current: subCurrent ? current - 1 : current, size };
    tabsValue === 'live' ? run(params) : unLiveRun(params);
  };

  // 搜索
  const onSearch = (value: Req) => {
    // 当选品审核状态和运营审核状态为：1通过时需要查出通过/自动通过/跳过的单据，2未审核时查出未审核的单据
    const paramsValue = {
      ...value,
      selectionStatus:
        value.selectionStatus === 1
          ? ['PASS', 'AUTO_PASS', 'SKIP']
          : value.selectionStatus === 2
          ? ['INIT']
          : [],
      operatorStatus:
        value.operatorStatus === 1
          ? ['PASS', 'AUTO_PASS', 'SKIP']
          : value.operatorStatus === 2
          ? ['INIT']
          : [],
    };
    setCondition(paramsValue);
    setPagination({
      current: 1,
      size: pagination.size,
    });
    const params = { ...paramsValue, current: subCurrent ? 0 : 1, size: pagination.size };
    cancel();
    cancelUnLive();
    if (tabsValue === 'live') {
      run(params);
      summaryRun(params);
    } else {
      unLiveRun(params);
    }
  };
  const paginationRef = useRef(pagination);
  // 刷新当前页面
  const onRefresh = () => {
    console.log('paginationRef', paginationRef.current);
    const params = {
      ...condition,
      current: subCurrent ? paginationRef.current.current - 1 : paginationRef.current.current,
      size: paginationRef.current.size,
    };
    tabsValue === 'live' ? run(params) : unLiveRun(params);
  };

  // useEffect(() => {
  //   run({ current: pagination.current, size: pagination.size });
  // }, []);
  useEffect(() => {
    paginationRef.current = pagination;
  }, [pagination]);

  return {
    dataSource,
    dataSummary,
    // setDataSource,
    condition,
    // setCondition,
    pagination,
    // setPagination,
    loading,
    // run,
    onPageChange,
    onSearch,
    onRefresh,
    unLiveLoading,
  };
};
