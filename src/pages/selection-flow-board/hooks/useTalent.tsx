import { useState, useEffect } from 'react';
import { useRequest, useDebounceFn } from 'ahooks';
import { Const } from 'qmkit';
import { message } from 'antd';
import { liveRoomListAuth, LiveRoomListAuthResult } from '../services/yml';

export function useTalent(deptId?: string) {
  const [list, setList] = useState<LiveRoomListAuthResult>([]);

  useEffect(() => {
    debounce();
  }, []);

  const { run: debounce } = useDebounceFn(
    (val?) => {
      if (val) {
        run({
          keyword: val,
          deptId,
        });
        return;
      }
      run({
        keyword: '',
        deptId,
      });
    },
    {
      wait: 500,
    },
  );

  const { loading, run } = useRequest(
    (params?) =>
      liveRoomListAuth({
        ...params,
        current: 1,
        size: 50,
        deptId,
      }),
    {
      manual: true,
      onSuccess: ({ res }) => {
        if (res?.success) {
          // console.log('res', res);
          const records = res?.result || [];
          setList(records);
          return;
        }
        message.error(res?.message || Const.ERR_MESSAGE);
      },
    },
  );

  const deptChangeGetList = (params: any) => {
    run(params);
  };

  return {
    loading,
    list,
    debounce,
    reset: debounce,
    deptChangeGetList,
  };
}
