import React, { useEffect } from 'react';
import { Modal, Form, Icon, Input, Tooltip, message } from 'antd';
// import WithToggleModal from '@/components/WithToggleModal';
import { ModalProps } from 'antd/lib/modal';
('antd');
import style from '@/styles/index.module.less';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import { numberToColor } from '../utils';
import FileUploadSouceId from '@/components/FileUploadSouceId';
import { useRequest } from 'ahooks';
import { qualityScoreApproval } from '../services/yml';

interface IProps extends ModalProps {
  form: WrappedFormUtils;
  onRefresh: any;
  handleClose: any;
  info: any;
  [key: string]: any;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 16,
  },
};

const SpecialApprovalGoods = (props: IProps) => {
  const { form, visible, handleClose, info, onRefresh } = props;

  const { getFieldDecorator } = form;

  const { run, loading } = useRequest(qualityScoreApproval, {
    manual: true,
    onSuccess({ res }) {
      if (!res.success) {
        message.error(res.message || '网络异常');
        return;
      }
      message.success('操作成功');
      handleClose?.();
      onRefresh?.();
    },
  });

  const handleOK = () => {
    form.validateFields((err, values) => {
      if (err) {
        return;
      }
      const { resourceIds, reason } = values;
      run({
        reason,
        resourceIds: resourceIds?.map((item: any) => item?.resourceId),
        spuName: info?.spuName,
        platformSpuId: info?.platformSpuId,
        goodsQualityScore: info?.goodsQualityScore,
        deptId: info?.deptId,
        platformSource: info?.platformSource,
        id: info?.id,
      });
    });
  };

  useEffect(() => {
    // if (visible && info) {
    //   console.log('🚀 ~ useEffect ~ info:', info);
    //   const { spuName, spuId, goodsQualityScore } = info;

    // }
    if (!visible) {
      form.resetFields();
    }
  }, [visible, info]);

  return (
    <Modal
      title={<>商品分特批</>}
      visible={visible}
      width={500}
      className={style['modal-sty']}
      maskClosable={false}
      onCancel={handleClose}
      onOk={handleOK}
      okButtonProps={{
        loading,
      }}
    >
      <Form labelAlign="right">
        <Form.Item label="商品名称" {...formItemLayout} style={{ marginBottom: '10px' }}>
          {info?.spuName || '-'}
        </Form.Item>
        <Form.Item label="商品ID" {...formItemLayout} style={{ marginBottom: '10px' }}>
          {info?.platformSpuId}
        </Form.Item>
        <Form.Item label="商品质量分" {...formItemLayout} style={{ marginBottom: '10px' }}>
          <span style={{ color: numberToColor(info?.goodsQualityScore) }}>
            {info?.goodsQualityScore}
          </span>
        </Form.Item>
        <Form.Item label="申请原因" required {...formItemLayout} style={{ marginBottom: '10px' }}>
          {getFieldDecorator('reason', {
            rules: [
              {
                required: true,
                message: '请填写申请原因',
              },
            ],
          })(<Input.TextArea placeholder="请填写" maxLength={300} />)}
        </Form.Item>
        <Form.Item label="附件" required {...formItemLayout} style={{ marginBottom: '10px' }}>
          {getFieldDecorator('resourceIds', {
            rules: [
              {
                required: true,
                message: '请选择',
              },
            ],
          })(
            <FileUploadSouceId
              typeCode="QUALIFICATION_SAMPLE"
              isImage
              maxSize={20 * 1024 * 1024}
              multiple
              accept={'.jpg,.jpeg,.png,.pdf'}
              maxLen={10}
            />,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create<IProps>()(SpecialApprovalGoods);
