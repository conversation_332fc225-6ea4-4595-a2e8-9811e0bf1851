import React, { useCallback, useEffect, useState } from 'react';
import { FormComponentProps } from 'antd/es/form';
import SearchFormComponent, { searchItem } from '@/pages/report-sheet/components/SearchForm';
import SearchFormComponentSet, { searchItemSet } from '@/components/SearchForm/index';
import { Form } from 'antd';
import moment from 'moment';
import { useSettingData } from '@/common/constants/hooks/index';
import SaveSearchTag from './SaveSearchTag';

interface IProps extends FormComponentProps {
  onSearch: (value: any) => void;
  options: Record<string, searchItem>;
  getTableHeight?: any;
  tabsValue: string;
  clearSelectKeys: () => void;
  codeList: Array<Record<string, string>>;
  talentList?: any;
  list?: any;
  serviceTypeList?: any;
  groupList?: any;
  setSortedInfo: (sorter: any) => void;
  sortedInfo: any;
}

const sortMap = {
  ascend: 'ASC',
  descend: 'DESC',
};

const SearchForm: React.FC<IProps> = ({
  form,
  onSearch,
  options,
  getTableHeight,
  tabsValue,
  clearSelectKeys,
  talentList,
  list,
  serviceTypeList,
  groupList,
  setSortedInfo,
  sortedInfo,
}) => {
  const { deptId, platformSource, liveRoomId } = form.getFieldsValue();

  const [saveParams, setSaveParams] = useState();
  const onSubmit = useCallback(
    (init?: boolean) => {
      clearSelectKeys();
      form.validateFields((err, values) => {
        // console.log(values, '------>');
        // onSearch(values);
        // sortedInfo?.order ? 'PLATFORM_SHOP_NAME' : undefined,
        //sortType: sortedInfo?.order ? sortMap[sortedInfo.order] : undefined,
        if (!err) {
          const params = { ...values };
          // if (init) {
          params.sortField = undefined;
          params.sortType = undefined;
          // } else {
          //   params.sortField = sortedInfo?.order ? 'PLATFORM_SHOP_NAME' : undefined;
          //   params.sortType = sortedInfo?.order ? sortMap[sortedInfo.order] : undefined;
          // }
          setSortedInfo(undefined);
          onSearch(params);
          setSaveParams(values);
        }
      });
    },
    [onSearch, form],
  );

  const onReset = () => {
    form.resetFields();
    clearSelectKeys();

    onSubmit(true);
  };
  const { run, optionsOrigin } = useSettingData({
    name: 'selection-flow-board-form-live',
    options: options,
    getTableHeight,
  });

  useEffect(() => {
    form.resetFields();
    if (tabsValue === 'live') {
      form.setFieldsValue({
        liveTime: {
          startDate: moment().startOf('day'),
          endDate: moment().add(30, 'day').endOf('day'),
        },
      });
    }
  }, [tabsValue]);
  useEffect(() => {
    if (tabsValue === 'live') {
      run();
    }
  }, [talentList, list, serviceTypeList, groupList, platformSource, liveRoomId]);

  useEffect(() => {
    form.setFieldsValue({
      liveRoomId: undefined,
    });
    if (deptId) {
      run();
    }
  }, [deptId]);

  return (
    <div>
      {tabsValue === 'live' ? (
        <SearchFormComponentSet
          onSearch={onSubmit}
          onReset={onReset}
          needMore={true}
          form={form}
          showRow={2}
          rowShowNum={4}
          options={optionsOrigin}
          getTableHeight={getTableHeight}
          loading={false}
          bizType="selection-flow-board-form-live"
        >
          <SaveSearchTag type="live-search-tag" saveParams={saveParams} searchForm={form} />
        </SearchFormComponentSet>
      ) : (
        <SearchFormComponent
          form={form}
          options={options}
          loading={false}
          onSearch={onSubmit}
          onReset={onReset}
          needMore
          showRow={2}
          getTableHeight={getTableHeight}
        >
          <SaveSearchTag type="un-live-search-tag" saveParams={saveParams} searchForm={form} />
        </SearchFormComponent>
      )}
    </div>
  );
};

// export default Form.create<IProps>()(SearchForm);
export default SearchForm;
