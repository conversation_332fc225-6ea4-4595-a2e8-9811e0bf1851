import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Select, Spin, Icon } from 'antd';
import { debounce } from 'lodash';

import { SelectProps } from 'antd/es/select';
import styles from './index.module.less';

import { useRequest } from 'ahooks';
import { handleResponse } from '@/utils/response';

import {
  getEmployeePageByRole,
  GetEmployeePageByRoleRequest,
  GetEmployeePageByRoleResult,
} from '@/services/yml/employee-list';
import useEmployee from '@/pages/goods-assorting/hooks/useEmployee';

type ListItemType = {
  employeeId: 'bpId' | 'operatorAuditor' | 'selectionAuditor';
  employeeName: '商务' | '运营' | '选品';
  bizRoleType: GetEmployeePageByRoleRequest['bizRoleType'];
};
enum EmployeeKey {
  'bpId' = 'BUSINESS',
  'operatorAuditor' = 'OPERATE',
  'selectionAuditor' = 'SELECTION',
}
const defaultList: ListItemType[] = [
  {
    employeeId: 'bpId',
    employeeName: '商务',
    bizRoleType: 'BUSINESS',
  },
  {
    employeeId: 'operatorAuditor',
    employeeName: '运营',
    bizRoleType: 'OPERATE',
  },
  {
    employeeId: 'selectionAuditor',
    employeeName: '选品',
    bizRoleType: 'SELECTION',
  },
];
export interface IProps extends Omit<SelectProps, 'onChange'> {
  // getQueryByParentId?: (val: any) => Promise<any>;
  // getSearchByName?: (val: any) => Promise<any>;
  // source?: 'DY' | 'KSXD';
  value?: string[];
  onChange?: (val: string[]) => void;
  showType?: 'light' | 'normal';
  width?: 'auto' | number;
  mustLastLevel?: boolean;
  status?: number;
  getSelectList?: (val: any) => void;
  needName?: boolean;
  cList?: any[];
}

const Cascader = (props: IProps) => {
  const {
    value,
    onChange,
    placeholder,
    className = '',
    showType = 'normal',
    width = 200,
    mustLastLevel = false,
    status,
    getSelectList,
    needName = false,
    cList = defaultList,
  } = props;
  const [selectList, setSelectList] = useState<ListItemType[]>([]); // 选中类目
  const [cascaderList, setCascaderList] = useState<any>([defaultList]);
  const { list, setList, onSearch, loading } = useEmployee();

  const { run } = useRequest(
    (params?: GetEmployeePageByRoleRequest) => {
      return getEmployeePageByRole({
        // 过滤停用
        accountState: 0,
        current: 1,
        size: 100,
        ...params,
      });
    },
    {
      manual: true,
      debounceInterval: 200,
      onSuccess: (res) => {
        handleResponse(res).then((res) => {
          const { records = [] } = res?.result?.employeeByPage;
          cascaderList[1] = [...records];
          setCascaderList([...cascaderList]);
        });
      },
    },
  );
  const handleSearch = debounce(async (v: string) => {
    console.log('search', v);
    setSearchValue(v);
    if (selectList?.length || value?.length) {
      // run({ employeeName: v, bizRoleType: EmployeeKey[value[0]] });

      const result = await onSearch({
        employeeName: v,
        bizRoleType: EmployeeKey[selectList[0]?.employeeId ?? value[0]],
      });
      cascaderList[1] = result?.records ?? [];
      setCascaderList([...cascaderList]);
    }
  }, 500);
  useEffect(() => {
    if (value?.length && EmployeeKey[value[0]]) {
      onSearch({ bizRoleType: EmployeeKey[value[0]] });
    } else {
      onSearch();
    }
  }, []);

  useEffect(() => {
    // console.log('🚀 ~ Cascader ~ value:', value);
    if (!value || !value?.length) {
      setSelectList([]);
    }
  }, [value]);
  const [searchValue, setSearchValue] = useState();
  const clickSelect = useCallback(
    async (obj: ListItemType, index: number) => {
      // console.log('obj', obj);
      // console.log('index', index, searchValue);

      if (selectList[index]) {
        selectList.splice(index);
        //  categoryList.splice(index + 1);
      }
      selectList[index] = obj;
      if (index === 0) {
        const result = await onSearch({
          bizRoleType: obj.bizRoleType,
          employeeName: searchValue,
        });
        cascaderList[1] = result?.records ?? [];
        setCascaderList([...cascaderList]);
      }
      setSelectList([...selectList]);
    },
    [selectList, cascaderList, list],
  );

  const getCategoryList = () => {
    return cascaderList?.map((level: any, index: number) => {
      return (
        <div className={`${styles['category-item']}`} key={index}>
          <ul>
            {level?.map((obj) => {
              return (
                <li
                  className={`flex-between ${
                    selectList[index]?.employeeId === obj.employeeId ? styles['select'] : ''
                  }`}
                  key={obj.employeeId}
                  onMouseDown={(e) => {
                    // setFlag(false);
                    // console.log('一级', value);
                    clickSelect(obj, index);

                    if (index === 0) {
                      e.preventDefault();
                    }
                  }}
                >
                  <span>{obj.employeeName}</span>
                  {index === 0 && <Icon type="right" />}
                </li>
              );
            })}
          </ul>
        </div>
      );
    });
  };

  const selectValue = useMemo(() => {
    return selectList?.map((item) => item.employeeName)?.join('/') || undefined;
  }, [selectList]);

  const handleChange = (value: string) => {
    if (!value) {
      setSelectList([]);
      setCascaderList([[...cList]]);
      onSearch();
    }
  };

  const handleFocus = (value) => {
    // setSelectList([]);
    setCascaderList([[...cList]]);
  };

  const handleBlur = () => {
    const curSelect = [...selectList];
    onChange && onChange(curSelect?.map((item) => item?.employeeId));
    // setSelectList([]);
    // setCascaderList([[...defaultList]]);
  };

  return (
    <div className={`${styles['category-l']}`}>
      <Select
        loading={loading}
        defaultActiveFirstOption={false}
        showSearch
        placeholder={placeholder || '请选择类目'}
        notFoundContent={loading ? <Spin size="small" /> : null}
        filterOption={false}
        onSearch={handleSearch}
        dropdownRender={(menu) => {
          return <div className={`${styles['category-box']}`}>{getCategoryList()}</div>;
        }}
        value={selectValue}
        allowClear
        onChange={handleChange}
        onBlur={handleBlur}
        onFocus={handleFocus}
      ></Select>
    </div>
  );
};

export default Cascader;
