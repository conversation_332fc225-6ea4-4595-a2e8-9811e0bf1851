import { Form, Modal, Spin, message, Button } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  batchBpConfirm,
  BatchBpConfirmRequest,
  BatchBpConfirmResult,
  bpConfirm,
  BpConfirmRequest,
  SelectionProcessKanbanPageResult,
} from '../../services/yml';
import { getServiceTypeBySelectionRoundIds, GetServiceTypeResult } from '@/services/yml/choose';
import { WrappedFormUtils } from 'antd/lib/form/Form';
import Result from '../Result';
import {
  guaranteedDetail,
  guaranteedDetailList,
  GuaranteedDetailResult,
  guaranteedRelDetailList,
  GuaranteedRelDetailListResult,
} from '@/services/yml/quality-assurance-cooperation';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import BoardFrameworkSelectFormTable from '@/pages/choice-list-new/components/choiceListRight/components/BoardFrameworkSelectFormTable';
import styles from './index.module.less';
import { useRequest } from 'ahooks';

interface BatchConfirmModalProps {
  selectedFullKeys: SelectionProcessKanbanPageResult['records'];
  form: WrappedFormUtils;
  single?: boolean;
  openSingle?: (
    record:
      | NonNullable<BatchConfirmModalProps['selectedFullKeys']>[number]
      | NonNullable<BatchConfirmModalProps['selectedFullKeys']>[number],
    type: 'businessConfirm',
  ) => void;
  onSearch?: () => void;
  clearSelectedKeys?: () => void;
}

type TableColumnsType = NonNullable<BatchConfirmModalProps['selectedFullKeys']>[number] & {
  childrenMsg: GetServiceTypeResult;
};
const BatchConfirmModal: React.FC<BatchConfirmModalProps> = ({
  selectedFullKeys,
  form,
  single,
  openSingle,
  onSearch,
  clearSelectedKeys,
}) => {
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState<TableColumnsType[]>([]);
  const [guaranteedDetailArray, setGuaranteedDetailArray] = useState<GuaranteedDetailResult[]>([]);
  const [guaranteedRelDetail, setGuaranteedRelDetail] = useState<GuaranteedRelDetailListResult>({});
  const { run: guaranteedRelDetailListRun, loading: guaranteedRelDetailListLoading } = useRequest(
    guaranteedRelDetailList,
    {
      manual: true,
      onSuccess({ res }) {
        if (res?.success) {
          setGuaranteedRelDetail(res?.result || {});
        } else {
          message.warning(res?.message || '网络异常');
        }
      },
    },
  );
  const filterList = useMemo(() => {
    return selectedFullKeys?.filter((item) => item?.status === 'BP_CONFIRMING') ?? [];
    // return selectedFullKeys
  }, [selectedFullKeys, visible]);

  const handleShowModal = async () => {
    const filterList = selectedFullKeys?.filter((item) => item?.status === 'BP_CONFIRMING') ?? [];
    form.resetFields();
    if (!selectedFullKeys?.length) {
      message.warning('请选择要操作的数据');
      return;
    }
    if (selectedFullKeys?.length && selectedFullKeys.length > 50) {
      message.warning('最多批量操作50条数据');
      return;
    }

    if (filterList.length) {
      // console.log(filterList);
      const fList = filterList?.filter((i) => i?.guaranteeQuantityId);
      const params = fList.map((item) => item.guaranteeQuantityId!);
      const detailListResult = await responseWithResultAsync({
        request: guaranteedDetailList,
        params: { ids: params },
      });
      detailListResult && setGuaranteedDetailArray(detailListResult);
    }

    // 新增保量判断
    if (filterList.length) {
      const filterHasId = filterList.reduce((acc: any[], cur: any) => {
        if (cur?.guaranteeQuantityId) {
          return [
            ...acc,
            {
              cooperationGuaranteedId: cur?.guaranteeQuantityId,
              selectionNo: cur?.no,
            },
          ];
        }
        return [...acc];
      }, []);
      filterHasId?.length && guaranteedRelDetailListRun({ detailRequests: filterHasId });
    }

    // const result = await batchChildrenMsgList();
    const ids = [...new Set(filterList?.map((item) => item.id!))];
    if (ids.length) {
      const result = await responseWithResultAsync({
        request: getServiceTypeBySelectionRoundIds,
        params: { ids },
      });
      const data: TableColumnsType[] = filterList.map((item) => {
        const required = result && item.id && result[item.id];
        return {
          ...item,
          childrenMsg:
            result && item.id && result[item.id] && result[item.id] !== null
              ? (result[item.id] as GetServiceTypeResult)
              : undefined,
        };
      });
      setDataSource(data);
      if (
        !data.find((item) => !!item.childrenMsg) &&
        !data.find((item) => item.guaranteeQuantityId) &&
        !data.find((item) => item.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE')) &&
        !data.find((item) => item.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE')) &&
        filterList.length < 2
      ) {
        openSingle &&
          openSingle(
            filterList[0].id ? filterList[0] : selectedFullKeys.length ? selectedFullKeys[0] : {},
            'businessConfirm',
          );
        return;
      }
    }
    setVisible(true);
  };
  const handleCancel = () => {
    setVisible(false);
  };
  function removeAndGetNumber(str: string) {
    // 使用正则表达式匹配数字
    const numberMatch = str.match(/\d+/);

    // 如果没有找到数字，返回null
    if (!numberMatch) {
      return null;
    }

    // 获取匹配到的数字
    const number = parseInt(numberMatch[0], 10);

    // 去除字符串中的数字
    const newStr = str.replace(/\d+/, '');

    // 返回去除数字后的字符串和数字
    return {
      newStr: newStr,
      number: number,
    };
  }
  const [selectIndexList, setSelectIndexList] = useState<number[]>([]);
  const handleSelectList = (list: number[]) => {
    setSelectIndexList(list);
  };
  const [confirmLoading, setLoading] = useState(false);
  const [successNumber, setSuccessNumber] = useState(0);
  const [filedList, setFiledList] = useState<
    {
      errorReason: string;
      errorNo: string;
    }[]
  >([]);
  const [resultVisible, setResultVisible] = useState(false);
  const handleOk = () => {
    if (!filterList?.length) {
      handleCancel();
      return;
    }
    form.validateFields(async (err, values) => {
      const valuesList: BpConfirmRequest[] = [];
      if (err) {
        return;
      }
      setLoading(true);
      if (filterList.length && tableShow) {
        Object.keys(values).forEach((key) => {
          const result = removeAndGetNumber(key);
          const formKey = result!.newStr;
          const index = result!.number;
          console.log(formKey, index);
          valuesList[index] = !valuesList[index] ? {} : valuesList[index];
          valuesList[index][formKey] =
            formKey === 'liveServiceTypeId'
              ? values[formKey + index]?.key
              : values[formKey + index];
          valuesList[index].id = dataSource[index].id;
          valuesList[index].version = dataSource[index].version;
        });
      }
      if (!tableShow) {
        filterList.forEach((item, index) => {
          valuesList[index] = !valuesList[index] ? {} : valuesList[index];
          valuesList[index].id = item.id;
          valuesList[index].version = item.version;
        });
      }
      setResultVisible(true);
      setVisible(false);
      console.log(valuesList);
      console.log(filterList);
      if (filterList.length > 1) {
        const requresParams: BpConfirmRequest[] = filterList.map((item) => {
          const findValue = valuesList.find((i) => i?.id === item?.id);
          if (findValue?.id) {
            return findValue;
          } else {
            return {
              id: item?.id ?? undefined,
              version: item?.version ?? undefined,
            };
          }
        });
        console.log(requresParams);
        const result = await responseWithResultAsync({
          request: batchBpConfirm,
          params: {
            bpConfirmRequests: requresParams,
            // valuesList.length > 1
            // ? (valuesList.filter(
            //   (item) => item.id,
            // ) as BatchBpConfirmRequest['bpConfirmRequests'])
            // : undefined,
          },
        });
        if (result && filterList.length > 1) {
          setSuccessNumber(result.successCount as number);
          if (result.failCount) {
            setFiledList(
              result.failResultModels!.map((item) => {
                return {
                  errorReason: item.failReason!,
                  errorNo: item.failNo!,
                };
              }),
            );
          }
          onSearch && onSearch();
        }
      } else {
        const result = await responseWithResultAsync({
          request: bpConfirm,
          params: valuesList[0],
        });
        setSuccessNumber(result ? 1 : 0);
        result && onSearch && onSearch();
      }
      clearSelectedKeys && clearSelectedKeys();
      setLoading(false);
    });
  };
  const modalWidth = useMemo(() => {
    const frameworkCoopModelLiveWidth = dataSource.find((item) =>
      item.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE'),
    )
      ? 200
      : 0;
    const frameworkCoopModelGMV = dataSource.find((item) =>
      item.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE'),
    )
      ? 200
      : 0;
    const guaranteeQuantityWidth = dataSource.find((item) => item.guaranteeQuantityId) ? 340 : 0;
    return 780 + frameworkCoopModelLiveWidth + frameworkCoopModelGMV + guaranteeQuantityWidth;
  }, [dataSource, visible]);
  const tableShow = useMemo(() => {
    return (
      (dataSource.find((item) => item.guaranteeQuantityId) ||
        dataSource.find((item) => item.frameworkCoopModel?.includes('GUARANTEED_GMV_MODE')) ||
        dataSource.find((item) =>
          item.frameworkCoopModel?.includes('GUARANTEED_LIVE_ROUND_MODE'),
        ) ||
        dataSource.find((item) => !!item?.childrenMsg)) &&
      visible
    );
  }, [dataSource, visible]);
  return (
    <>
      <Modal
        visible={visible}
        title={filterList.length < 2 ? '确认提交' : '批量确认'}
        onCancel={handleCancel}
        width={modalWidth}
        // width={1300}
        onOk={handleOk}
        confirmLoading={confirmLoading}
      >
        {!single && (
          <p
            style={{
              fontFamily: 'PingFangSC, PingFang SC',
              fontWeight: 500,
              fontSize: '16px',
              color: 'rgba(0,0,0,0.85)',
              fontStyle: 'normal',
              marginBottom: '16px',
            }}
          >
            {`当前选中数据${selectedFullKeys?.length ?? 0}条, 其中有${
              filterList.length
            }条流程可批量确认, 请确认是否操作？`}
          </p>
        )}
        {!!filterList.length && tableShow && (
          <BoardFrameworkSelectFormTable
            form={form}
            goodsInfoList={dataSource}
            onSelect={handleSelectList}
            guaranteedDetailArray={guaranteedDetailArray}
            guaranteedRelDetailListLoading={guaranteedRelDetailListLoading}
            guaranteedRelDetail={guaranteedRelDetail}
          />
        )}
      </Modal>
      <Modal
        title="批量确认"
        visible={resultVisible}
        onOk={() => {
          setResultVisible(false);
          setFiledList([]);
          setSuccessNumber(0);
        }}
        onCancel={() => {
          setResultVisible(false);
          setFiledList([]);
          setSuccessNumber(0);
        }}
      >
        <Spin spinning={confirmLoading}>
          <Result successNumber={successNumber} fail={filedList} />
        </Spin>
      </Modal>
      {single ? (
        <a
          style={{ marginRight: '6px' }}
          onClick={() => {
            handleShowModal();
          }}
        >
          确认
        </a>
      ) : (
        <Button
          className="mr-8 mb-8"
          style={{ borderColor: '#999999', color: '#444444' }}
          onClick={() => {
            handleShowModal();
          }}
        >
          <span>确认</span>
        </Button>
      )}
    </>
  );
};

export default Form.create<BatchConfirmModalProps>()(BatchConfirmModal);
