/*
 * @Author: zhouby
 * @Date: 2024-03-21 10:39:49
 * @LastEditTime: 2025-03-17 16:31:27
 * @Description: 运营流程看板
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.less';
import PageLayout from '@/components/PageLayout';
import style from '../../styles/index.module.less';
import { Table, Modal, Button, Icon, message } from 'antd';
import { useSearch, useTable, useList, APIKEY, useBatchCreateInterestPoints } from './hooks';
import { SearchForm, ExportModal } from './components';
import { useTableHeight } from '@/common/constants/hooks/index';
import { formatParams } from './utils';
import moment from 'moment';
import { AuthWrapper, history } from 'qmkit';
import download from '@/assets/download.png';
import {
  OperatorBoardListRequest,
  OperatorBoardListResult,
  liveGoodsInfo,
  boardExport,
  playbackEditBatch,
  PlaybackEditBatchRequest,
  saveOperatorSerialNo,
} from './services/yml';
import GoodsInfoEdit from '@/components/GoodsInfoEdit/index';
// import ExportModal from '@/components/ExportModal';
import { copyText } from '@/utils/moduleUtils';
import { PROCESS_STATUS } from '../../../web_modules/types';
import { useRequest } from 'ahooks';
import ImportInterestPointsBox from './components/ImportInterestPointsBox';
import { useTalkLinkNum } from '../live-calendar/utils/hook';

const OperationProcessBoard = () => {
  const { confirm } = Modal;
  const { options, liveList } = useSearch();
  const [init, setInit] = useState(false);
  const { getHeight, tableHeight } = useTableHeight(90);
  const { dataSource, onSearch, onRefresh, loading, condition, totalEstimateGmv } = useList<
    OperatorBoardListRequest,
    OperatorBoardListResult
  >(APIKEY.APPROVAL);
  const [liveRoomId, setLiveRoomId] = useState();
  // 119交个朋友淘宝直播间id是119
  const handleSearch = (value: any) => {
    setIsInput(false);
    const params = formatParams(value);
    setLiveRoomId(params?.liveRoomId);
    onSearch(params);
    localStorage.setItem('OPERATION_PROCESS_BOARD_LIVE', value?.liveRoomId);
    getNum(params);
  };
  useEffect(() => {
    if (!init && liveList?.length) {
      setInit(true);
      onSearch({
        liveDate: moment().format('YYYY-MM-DD'),
        liveRoomId: localStorage.getItem('OPERATION_PROCESS_BOARD_LIVE') || liveList[0].id,
      });
      setLiveRoomId(localStorage.getItem('OPERATION_PROCESS_BOARD_LIVE') || liveList[0].id);
    }
  }, [init, liveList]);
  // 粘贴场次货盘代码
  // 开始
  const goodsInfoRef = useRef<{
    onOpen: (key?: string) => void;
    [key: string]: any;
  }>(null);
  const [detailLoading, setDetailLoading] = useState(false);
  const [goodsInfo, setGoodsInfo] = useState({});
  const openEditDetail = (value: any, type: string) => {
    setDetailLoading(true);
    liveGoodsInfo({ id: value.selectionId })
      .then(({ res }) => {
        if (res?.success) {
          setGoodsInfo(res?.result);
          setTimeout(() => {
            if (type === 'edit') {
              goodsInfoRef.current?.onOpen('edit');
            } else if (type === 'info') {
              goodsInfoRef.current?.onOpen();
            }
          }, 100);
        }
      })
      .finally(() => {
        setDetailLoading(false);
      });
  };
  // 结束
  const {
    columns,
    rowSelection,
    selectedRows,
    tableList,
    isInput,
    setIsInput,
    inputErr,
    selectedKeys,
  } = useTable(openEditDetail, dataSource, onRefresh);

  const copyAll = useCallback(() => {
    const filterArr = selectedRows.filter(
      (item: Required<OperatorBoardListResult>[number]) =>
        item?.promotionLink &&
        [PROCESS_STATUS.WAIT_LIVE, PROCESS_STATUS.TB_ORDERED].includes(
          item?.selectionRoundStatus as PROCESS_STATUS,
        ) &&
        (item?.platformSource !== 'TB' || item?.linkCheckStatus === 'CAN_PROMOTION'),
    );
    if (!filterArr?.length) {
      message.warning('没有可复制的链接');
      return;
    }
    let str = '';
    filterArr.forEach((item: Required<OperatorBoardListResult>[number], index: number) => {
      if (index === 0) {
        str = str + item?.promotionLink;
      } else {
        str = str + '\n\n' + item?.promotionLink;
      }
    });
    copyText(str);
  }, [selectedRows]);

  const { run: saveOperatorSerialNoRun, loading: saveOperatorSerialNoLoading } = useRequest(
    saveOperatorSerialNo,
    {
      manual: true,
      onSuccess({ res }) {
        if (res?.success) {
          setIsInput(false);
          onRefresh();
          message.success('保存成功');
        } else {
          message.warning(res?.message || '网络异常');
        }
      },
    },
  );

  // 保存排序
  const handleSaveSort = () => {
    if (inputErr) {
      message.warning('请填写正确的序号');
      return;
    }
    // console.log(tableList);
    const params = tableList?.map((item: OperatorBoardListResult[number], index: number) => ({
      operatorSerialNo: index + 1,
      selectionId: item?.selectionId,
    }));
    saveOperatorSerialNoRun({ saveRequests: params });
  };
  //已上播。/不可上播
  const handleBroadcast = useCallback(
    (type: string) => {
      // console.log('勾选数据', selectedRows);
      if (selectedRows.length > 50) {
        confirm({
          content: '当前选中数据超出单次50条上限,请重新选择!',
        });
      } else {
        const playedArr = selectedRows.filter((i) => i?.playBackStatus !== 'PLAYED');
        const notPlayedArr = selectedRows.filter((i) => i?.playBackStatus !== 'NOT_PLAYER');
        // console.log('selectedRows.length - playedArr.length', selectedRows.length, notPlayedArr);
        if (type === 'PLAYED') {
          confirm({
            content: `当前选中数据${selectedRows.length}条,其中有${playedArr.length}条流程可批量标记上播,请确认是否操作。`,
            onOk() {
              if (playedArr.length) {
                const ids = playedArr.map((i) => i.selectionId);
                handleBroadcastReq({ ids, playBackStatus: type });
              }
            },
          });
        } else if (type === 'NOT_PLAYER') {
          confirm({
            content: `当前选中数据${selectedRows.length}条,其中有${notPlayedArr.length}条流程可批量标记不可上播,请确认是否操作。`,
            onOk() {
              if (notPlayedArr.length) {
                const ids = notPlayedArr.map((i) => i.selectionId);
                handleBroadcastReq({ ids, playBackStatus: type });
              }
            },
          });
        }
      }
    },
    [selectedRows],
  );
  const handleBroadcastReq = async (params: PlaybackEditBatchRequest) => {
    const { res } = await playbackEditBatch(params);
    console.log('res', res);
    if (res.code === '200') {
      message.success(res.message);
    } else {
      message.error(res.message);
    }
    onRefresh();
    // clearSelecte();
  };

  const { batchCreateInterestPointsRun, batchCreateInterestPointsLoading } =
    useBatchCreateInterestPoints(onRefresh);

  const handlePoint = () => {
    // if (selectedRows.length > 50) {
    //   confirm({
    //     content: '当前选中数据超出单次50条上限,请重新选择!',
    //   });
    //   return;
    // }
    const interestPointsList = selectedRows.filter((item) => !item?.interestPoints);

    confirm({
      title: `当前选中数据${selectedRows.length}条,其中有${interestPointsList?.length}条商品利益点为空,可操作批量生成利益点,请确认是否操作?`,
      onOk() {
        if (!interestPointsList?.length) {
          return;
        }
        const spuIdList = interestPointsList?.map((item) => item?.spuId);
        batchCreateInterestPointsRun({ spuIdList });
      },
    });
  };
  const talkLinkCondition = useMemo(
    () => ({
      deptId: liveList?.find((item) => item?.id === liveRoomId)?.buId,
      liveRoomId: liveRoomId ? [liveRoomId] : null,
      liveDateStartTime: condition?.liveDate,
      liveDateEndTime: condition?.liveDate,
      platformSource: liveList?.find((item) => item?.id === liveRoomId)?.platform,
    }),
    [condition, liveList],
  );
  const { talkLinkNum, showLinkNumAndTalkNum, getNum } = useTalkLinkNum(talkLinkCondition);
  return (
    <PageLayout className={styles.publishFeeManageContainer} routePath="/operation-process-board">
      <div
        className={`${style.publishFeeContainer} ${style['publish-fee-page']}`}
        style={{ display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            options={options}
            onSearch={handleSearch}
            getTableHeight={getHeight}
          ></SearchForm>
          <div className={style.btnGroup} style={{ marginBottom: '16px' }}>
            <AuthWrapper functionName="f_operation_process_board_export">
              <ExportModal condition={condition} ids={selectedKeys} requestFunc={boardExport}>
                <Button className="mr-8" style={{ borderColor: '#999999', color: '#444444' }}>
                  <img
                    src={download}
                    style={{
                      width: '14px',
                      height: '14px',
                      verticalAlign: 'sub',
                      marginRight: '6px',
                      marginTop: '-2px',
                    }}
                  />
                  <span>导出</span>
                </Button>
              </ExportModal>
            </AuthWrapper>
            <AuthWrapper functionName="f_operation_process_board_export_logs">
              <Button
                className="mr-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={() => {
                  history.push(
                    `/export-list-selected-goods-all?configCode=OPERATOR_BOARD_DY_EXPORT_CODE`,
                  );
                }}
              >
                <span>导出记录</span>
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_operation_process_board_copy_link">
              <Button
                className="mr-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={copyAll}
              >
                <span>复制链接</span>
              </Button>
            </AuthWrapper>
            {isInput ? (
              <Button
                className="mr-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={handleSaveSort}
              >
                <span>保存排序</span>
              </Button>
            ) : (
              <></>
            )}
            <AuthWrapper functionName="f_operation_already_broadcast">
              {selectedRows.length > 0 && (
                <Button
                  className="mr-8"
                  style={{ borderColor: '#999999', color: '#444444' }}
                  onClick={() => {
                    handleBroadcast('PLAYED');
                  }}
                >
                  <span>已上播</span>
                </Button>
              )}
            </AuthWrapper>
            <AuthWrapper functionName="f_operation_cant_broadcast">
              {selectedRows.length > 0 && (
                <Button
                  className="mr-8"
                  style={{ borderColor: '#999999', color: '#444444' }}
                  onClick={() => {
                    handleBroadcast('NOT_PLAYER');
                  }}
                >
                  <span>不可上播</span>
                </Button>
              )}
            </AuthWrapper>
            <ImportInterestPointsBox onRefresh={onRefresh} />
            <AuthWrapper functionName="f_operation_process_board_point">
              <Button
                className="mr-8"
                style={{ borderColor: '#999999', color: '#444444' }}
                onClick={handlePoint}
              >
                <span>生成利益点</span>
              </Button>
            </AuthWrapper>
          </div>
        </div>
        <div style={{ flex: 1 }}>
          <Table
            columns={columns}
            pagination={false}
            dataSource={tableList as any[]}
            rowKey={(record: { [key: string]: any }) => `${record.selectionId}`}
            rowClassName={(record, i) => (i % 2 === 1 ? styles.even : styles.odd)}
            scroll={{ y: tableHeight, x: '100%' }}
            rowSelection={rowSelection}
            loading={
              loading ||
              detailLoading ||
              saveOperatorSerialNoLoading ||
              batchCreateInterestPointsLoading
            }
          />
        </div>
        {/* <div className={style['pagination-box'] + ' pageHeight'}></div> */}
        {/* 以下是从场次货盘粘贴的代码 */}
        <GoodsInfoEdit info={goodsInfo} onRef={goodsInfoRef} search={onRefresh} entry="changci" />
        <section style={{ display: 'flex' }}>
          <div className={styles.totalGmv}>
            {[119].includes(liveRoomId ? Number(liveRoomId) : 0) && (
              <>
                预估GMV总计: <span className={styles.totalGmvNum}>¥{totalEstimateGmv || 0}</span>
              </>
            )}
            {showLinkNumAndTalkNum && (
              <>
                讲解坑位: <span className={styles.totalGmvNum}>{talkLinkNum?.talkNum ?? '-'}</span>
                挂链坑位: <span className={styles.totalGmvNum}>{talkLinkNum?.linkNum ?? '-'}</span>
              </>
            )}
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default OperationProcessBoard;
