import { CHECK_ITME } from '../types';
export const exportList: Array<CHECK_ITME[]> = [
  [
    {
      code: 'selectionFlowNo',
      name: '选品流程编码',
    },
    {
      code: 'selectionStatus',
      name: '选品阶段',
    },
    {
      code: 'liveServiceType',
      name: '讲解类型',
    },
    {
      code: 'chargeGroup',
      name: '负责组',
    },
    {
      code: 'materialInfo',
      name: '定品信息',
    },
    {
      code: 'brandFee',
      name: '基础服务费',
    },
    {
      code: 'selectRedEnvelopes',
      name: '严选红包',
    },
    {
      code: 'link',
      name: '商品参考链接',
    },
    {
      code: 'promotionLink',
      name: '上播链接',
    },
    {
      code: 'linkValidityTime',
      name: '链接有效期',
    },
    {
      code: 'priceDistance',
      name: '到手价',
    },
    {
      code: 'giftInfos',
      name: '赠品',
    },
    {
      code: 'totalCommissionContainGuaranteed',
      name: '总佣金(含保量)',
    },
    {
      code: 'commissionRate',
      name: '线上佣金率',
    },
    {
      code: 'commissionRateOffline',
      name: '线下佣金率',
    },
    {
      code: 'guaranteeBrandFeeRate',
      name: '保量基础佣金',
    },
    {
      code: 'logisticsNo',
      name: '主品快递单号',
    },
    {
      code: 'giftLogisticsNo',
      name: '赠品快递单号',
    },
    {
      code: 'remark',
      name: '备注',
    },
    {
      code: 'skuModels',
      name: 'SKU信息',
    },
    {
      code: 'discountContentJson',
      name: '优惠信息',
    },
    {
      code: 'deliveryModeStr',
      name: '发货模式',
    },
    {
      code: 'deliveryCycle',
      name: '发货周期',
    },
    {
      code: 'giftDeliveryModeStr',
      name: '赠品随主品发货',
    },
    {
      code: 'freshIsColdStr',
      name: '是否冷链发货',
    },
    {
      code: 'modesStr',
      name: '售后服务',
    },
    {
      code: 'others',
      name: '其他售后服务',
    },
    {
      code: 'productionDate',
      name: '生产日期',
    },
    {
      code: 'shelfLife',
      name: '产品保质期',
    },
    {
      code: 'freshCompensation',
      name: '赔付标准',
    },
    {
      code: 'insuranceTime',
      name: '质保时长',
    },
    {
      code: 'installationService',
      name: '售后附加服务',
    },
    {
      code: 'interestPoints',
      name: '利益点',
    },
    {
      code: 'linkCheckStatus',
      name: '链接确认状态',
    },
    {
      code: 'linkCheckRemark',
      name: '链接确认状态备注',
    },
    {
      code: 'handCard',
      name: '手卡',
    },
    {
      code: 'key',
      name: 'Keynote',
    },
    {
      code: 'depositAmount',
      name: '定金金额',
    },
    {
      code: 'historySumSales',
      name: '历史累计(支付)',
    },
    {
      code: 'avgSales',
      name: '场均(支付)',
    },
    {
      code: 'historySumSalesForFifteenDays',
      name: '历史累计(T15)',
    },
    {
      code: 'avgSalesForFifteenDays',
      name: '场均(T15)',
    },
    {
      code: 'interestPointsGenerateType',
      name: '生成类型',
    },
    {
      code: 'spuFocus',
      name: '重点展示需求',
    },
    {
      code: 'spuFocusResourceListStr',
      name: '重点展示需求(附件)',
    },
  ],
];

export const exportKey = () => {
  const orgList = exportList;
  // 拉平
  const list = orgList?.reduce((pre: any[], cur: any[]) => [...pre, ...cur], []);
  const keys = list?.map((item) => item.code);
  return keys;
};

export const exportMap = () => {
  const orgList = exportList;
  // 拉平
  const list = orgList?.reduce((pre: any[], cur: any[]) => [...pre, ...cur], []);
  const map = list?.reduce((pre: any, cur: any) => ({ ...pre, ...{ [cur.code]: cur } }), {});
  return map;
};
