import React, { useEffect, useState, useRef } from 'react';
import { <PERSON>dal, Spin, Button, message } from 'antd';
import styles from './index.less';
import html2canvas from 'html2canvas';
import ComQRcode from '@/pages/paas-notice-list/compents/comQRcode';
import ApproveList from '@/pages/paas-notice-list/compents/approveList';
import SettlementDetailCom from '@/pages/paas-notice-list/compents/detailCompents/settlementDetailCom';

interface PrintSettlementModalProps {
  visible: boolean;
  id: string;
  taskId?: string;
  sourceNo?: string;
  approvalNo?: string;
  onCancel: () => void;
}

const PrintSettlementModal: React.FC<PrintSettlementModalProps> = ({
  visible,
  id,
  taskId,
  sourceNo,
  approvalNo,
  onCancel,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [settlementType, setSettlementType] = useState<string>('');
  const printContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (visible && id) {
      // 这里可以添加额外的数据加载逻辑
      setLoading(false);
    }
  }, [visible, id]);

  // 根据结算类型生成标题
  const getPageTitle = () => {
    if (settlementType === '3') {
      return '朋友云-付款单(预付核销)审批流程';
    }
    return '朋友云-对公付款/员工报销审批流程';
  };

  // 处理打印功能
  const handlePrint = () => {
    if (!printContainerRef.current) return;

    // 显示加载提示
    const loadingMessage = document.createElement('div');
    loadingMessage.style.position = 'fixed';
    loadingMessage.style.top = '50%';
    loadingMessage.style.left = '50%';
    loadingMessage.style.transform = 'translate(-50%, -50%)';
    loadingMessage.style.padding = '20px';
    loadingMessage.style.background = 'rgba(0, 0, 0, 0.7)';
    loadingMessage.style.color = 'white';
    loadingMessage.style.borderRadius = '4px';
    loadingMessage.style.zIndex = '9999';
    loadingMessage.textContent = '正在准备打印内容，请稍候...';
    document.body.appendChild(loadingMessage);

    // 创建打印窗口，但先不显示内容
    const printWindow = window.open('about:blank', '_blank');
    if (!printWindow) {
      alert('请允许弹出窗口以便进行打印');
      document.body.removeChild(loadingMessage);
      return;
    }

    // 显示加载中页面
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>付款结算单打印 - 加载中</title>
          <style>
            body {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              margin: 0;
              background-color: #f0f2f5;
              font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            }
            .loading {
              text-align: center;
            }
            .loading-text {
              margin-top: 20px;
              font-size: 16px;
              color: #1890ff;
            }
            .spinner {
              display: inline-block;
              width: 50px;
              height: 50px;
              border: 3px solid rgba(24, 144, 255, 0.2);
              border-radius: 50%;
              border-top-color: #1890ff;
              animation: spin 1s ease-in-out infinite;
            }
            @keyframes spin {
              to { transform: rotate(360deg); }
            }
          </style>
        </head>
        <body>
          <div class="loading">
            <div class="spinner"></div>
            <div class="loading-text">正在生成打印预览，请稍候...</div>
          </div>
        </body>
      </html>
    `);

    // 延迟执行以确保提示显示
    setTimeout(() => {
      // 使用html2canvas将内容转换为图片
      html2canvas(printContainerRef.current!, {
        scale: 2, // 提高图片清晰度
        useCORS: true, // 允许加载跨域图片
        allowTaint: true, // 允许污染画布
        backgroundColor: '#ffffff', // 设置背景色为白色
        logging: false, // 关闭日志
        onclone: (documentClone) => {
          // 克隆的文档中找到打印容器
          const printContainer = documentClone.getElementById('print-container');
          if (printContainer) {
            // 确保打印容器在克隆文档中可见且样式正确
            printContainer.style.display = 'block';
            printContainer.style.width = '100%';
            printContainer.style.maxWidth = '800px';
            printContainer.style.margin = '0 auto';
            printContainer.style.padding = '20px';
            printContainer.style.overflow = 'visible';

            // 设置容器内所有元素可见
            const allElements = printContainer.querySelectorAll('*');
            allElements.forEach((el) => {
              const element = el as HTMLElement;
              if (element.style) {
                element.style.display = '';
                element.style.visibility = 'visible';
              }
            });
          }
          return documentClone;
        },
      })
        .then((canvas) => {
          // 移除加载提示
          document.body.removeChild(loadingMessage);

          // 设置打印窗口内容
          printWindow.document.open();
          printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>${getPageTitle()}</title>
              <style>
                body {
                  margin: 0;
                  padding: 20px;
                  display: flex;
                  justify-content: center;
                  background-color: #f0f2f5;
                  font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
                }
                .print-container {
                  background-color: white;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                  width: 100%;
                  max-width: 800px;
                }
                img {
                  display: block;
                  width: 100%;
                }
                .print-header {
                  padding: 16px;
                  background-color: white;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  border-bottom: 1px solid #e8e8e8;
                }
                .print-title {
                  font-size: 16px;
                  font-weight: bold;
                  color: #333;
                  margin: 0;
                }
                .print-button {
                  background-color: #1890ff;
                  color: white;
                  border: none;
                  padding: 6px 16px;
                  border-radius: 2px;
                  cursor: pointer;
                  font-size: 14px;
                  transition: background-color 0.3s;
                }
                .print-button:hover {
                  background-color: #40a9ff;
                }
                @media print {
                  body {
                    background-color: white;
                    padding: 0;
                  }
                  .print-container {
                    box-shadow: none;
                    max-width: 100%;
                  }
                  .print-header {
                    display: none;
                  }
                }
              </style>
            </head>
            <body>
              <div class="print-container">
                <div class="print-header">
                  <h1 class="print-title">打印预览</h1>
                  <button class="print-button" onclick="window.print()">打印</button>
                </div>
                <img src="${canvas.toDataURL('image/png')}" alt="付款结算单" />
              </div>
            </body>
          </html>
        `);

          printWindow.document.close();
        })
        .catch((error) => {
          console.error('生成打印图片时出错：', error);
          document.body.removeChild(loadingMessage);
          if (printWindow) {
            printWindow.close();
          }
          message.error('生成打印内容时出错，请重试');
        });
    }, 100);
  };

  const modalFooter = (
    <div className={styles.modalFooter}>
      <Button onClick={onCancel} style={{ marginRight: 8 }}>
        取消
      </Button>
      <Button type="primary" onClick={handlePrint}>
        打印
      </Button>
    </div>
  );

  return (
    <Modal
      title="付款结算单打印预览"
      visible={visible}
      onCancel={onCancel}
      width={1200}
      bodyStyle={{ padding: '24px', maxHeight: '70vh', overflow: 'auto' }}
      footer={modalFooter}
      destroyOnClose
      centered
    >
      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin tip="正在加载单据数据..." size="large" />
        </div>
      ) : (
        <div id="print-container" className={styles.printContainer} ref={printContainerRef}>
          <div className={styles.documentHeader}>
            {/* 二维码生成组件 */}
            <ComQRcode id={approvalNo || ''} />
            {/* 中间模版内容 */}
            <h1 className={styles.pageTitle}>{getPageTitle()}</h1>
            <div className={styles.flowInfo}>
              <div className={styles.flowItem}>
                <span>流程编号:</span>
                <span>{approvalNo}</span>
              </div>
            </div>
          </div>

          {/* 结算单详情内容 */}
          <div className={styles.settlementContent}>
            <SettlementDetailCom
              id={id}
              isPrintMode={true}
              onDataLoaded={(data) => setSettlementType(data?.basicInfo?.settlementType || '')}
            />
          </div>

          {/* 审批记录 */}
          <div className={styles.approvalSection}>
            <div className={styles.approvalTitle}>审批记录</div>
            <ApproveList id={id} taskId={taskId} />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default PrintSettlementModal;
