// 结算单打印样式
.modalFooter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 0 0 0;
  border-top: 1px solid #e8e8e8;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #fafafa;
  border-radius: 4px;
}

.printContainer {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;

  // 打印时的样式调整
  @media print {
    box-shadow: none;
    border-radius: 0;
  }
}

.documentHeader {
  position: relative;
  padding: 24px;
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  text-align: center;

  // 二维码样式调整
  :global(.qrcode-container) {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 10;
  }
}

.pageTitle {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 16px 0;
  text-align: center;
  line-height: 1.4;
}

.flowInfo {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.flowItem {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;

  span:first-child {
    font-weight: 500;
    margin-right: 8px;
    color: #333;
  }

  span:last-child {
    color: #1890ff;
    font-weight: 500;
  }
}

.settlementContent {
  padding: 0;
  background-color: white;

  // 重置结算单详情组件内部样式
  :global(.ant-card) {
    margin-bottom: 16px;
    box-shadow: none;
    border: 1px solid #e8e8e8;

    .ant-card-head {
      background-color: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      padding: 0 16px;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        padding: 12px 0;
      }
    }

    .ant-card-body {
      padding: 16px;
    }
  }

  // 表格样式调整
  :global(.ant-table) {
    font-size: 12px;

    .ant-table-thead > tr > th {
      background-color: #fafafa;
      border-bottom: 1px solid #e8e8e8;
      padding: 8px;
      font-weight: 600;
      color: #333;
    }

    .ant-table-tbody > tr > td {
      padding: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }

  // 详情项样式调整
  :global(.detail-content-item) {
    margin-bottom: 8px;

    .detail-label {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .detail-value {
      color: #666;
      word-break: break-all;
    }
  }
}

.approvalSection {
  margin-top: 24px;
  padding: 20px;
  background-color: white;
  border-top: 1px solid #e8e8e8;

  .approvalTitle {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #1890ff;
    display: inline-block;
  }

  // 审批记录组件样式调整
  :global(.approval-list) {
    .ant-timeline {
      margin-top: 16px;

      .ant-timeline-item {
        padding-bottom: 16px;

        .ant-timeline-item-content {
          font-size: 14px;

          .approval-step {
            background-color: #f9f9f9;
            padding: 12px;
            border-radius: 4px;
            border-left: 3px solid #1890ff;

            .step-title {
              font-weight: 600;
              color: #333;
              margin-bottom: 8px;
            }

            .step-info {
              color: #666;
              font-size: 12px;

              .info-item {
                margin-right: 16px;
                display: inline-block;

                .info-label {
                  font-weight: 500;
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 打印时的特殊样式
@media print {
  .modalFooter {
    display: none;
  }

  .loadingContainer {
    display: none;
  }

  .printContainer {
    margin: 0;
    padding: 0;
    box-shadow: none;
    border-radius: 0;
    page-break-inside: avoid;
  }

  .documentHeader {
    background-color: white;
    border-bottom: 2px solid #333;
    page-break-inside: avoid;
  }

  .pageTitle {
    font-size: 20px;
    color: #000;
  }

  .flowInfo {
    margin-top: 12px;
  }

  .flowItem {
    font-size: 12px;
    color: #000;

    span:first-child,
    span:last-child {
      color: #000;
    }
  }

  .settlementContent {
    :global(.ant-card) {
      border: 1px solid #333;
      page-break-inside: avoid;

      .ant-card-head {
        background-color: #f0f0f0;
        border-bottom: 1px solid #333;

        .ant-card-head-title {
          color: #000;
        }
      }
    }

    :global(.ant-table) {
      .ant-table-thead > tr > th {
        background-color: #f0f0f0;
        border-bottom: 1px solid #333;
        color: #000;
      }

      .ant-table-tbody > tr > td {
        border-bottom: 1px solid #ddd;
        color: #000;
      }
    }
  }

  .approvalSection {
    background-color: white;
    border-top: 2px solid #333;
    page-break-inside: avoid;

    .approvalTitle {
      color: #000;
      border-bottom: 2px solid #333;
    }

    :global(.approval-list) {
      .ant-timeline-item-content {
        .approval-step {
          background-color: #f9f9f9;
          border-left: 3px solid #333;

          .step-title {
            color: #000;
          }

          .step-info {
            color: #333;

            .info-item {
              .info-label {
                color: #000;
              }
            }
          }
        }
      }
    }
  }
}
