import React, { useEffect, useState, useRef } from 'react';
import { Modal, Spin, Tag, Typography, Button, message, Table } from 'antd';
import { getAdvancePaymentDetail } from '@/services/finance';
import styles from './PrintModal.less';
import { Card, Title } from '@/components/DetailFormCompoments';
import {
  DetailContextBox,
  DetailContentItem as Item,
} from '@/components/DetailFormCompoments/DetailContentItem';
import html2canvas from 'html2canvas';
import moment from 'moment';
import ComQRcode from '@/pages/paas-notice-list/compents/comQRcode';
import ApproveList from '@/pages/paas-notice-list/compents/approveList';
import { useDeptList } from '@/hooks/useDeptList';
import { BUSSINESS_TYPE } from '@/pages/summary-costs/type';
import { CODE_ENUM, useCode } from '@/common/constants/hooks';
import { INVOICE_TYPE_MANAGEMENT } from '@/pages/supplier-list/types';

const { Text } = Typography;

// 类型定义，避免使用GetAdvancePaymentDetailResult可能引起的类型错误
interface ApiResponse {
  res?: {
    result?: any;
  };
}

// ai生成
interface AdvancePaymentDetailProps {
  title?: string;
  applicant?: string;
  applyDate?: string;
  costAttributionCompany?: string;
  costAttributionDepartment?: string;
  businessType?: string;
  costType?: string;
  businessLine?: string;
  affiliatedType?: string;
  attachment?: string;
  attachmentSize?: string;
  remark?: string;
  settlementType?: string;
  bankAccountNumber?: string;
  openingBank?: string;
  paymentRequestAmount?: string | number;
  invoicePayment?: string;
  contractNumber?: string;
  serialNumber?: string;
  flowNumber?: string;
  // 添加API返回的字段
  expenseCompanyName?: string;
  expenseDepartmentName?: string;
  deptName?: string;
  remarks?: string;
  settlementEntityName?: string;
  payeeBankAccount?: string;
  bankName?: string;
  applicationAmount?: number | string;
  invoiceDelivery?: string;
  expenseType?: string;
  invoiceInfoList?: Array<{
    id?: string;
    invoiceNum?: string;
    invoiceDate?: string;
    invoiceType?: string;
    buyer?: string;
    priceTaxTotalFigure?: number;
    taxRate?: string | number;
    noTaxAmountTotal?: number;
    taxAmountTotal?: number;
  }>;
}
// ********-飞虎ai结尾共生成37行代码

interface PrintModalProps {
  visible: boolean;
  id: string;
  taskId?: string;
  sourceNo?: string;
  approvalNo?: string;
  onCancel: () => void;
}

const PrintModal: React.FC<PrintModalProps> = ({
  visible,
  id,
  taskId,
  sourceNo,
  approvalNo,
  onCancel,
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [detail, setDetail] = useState<AdvancePaymentDetailProps>({});
  const printContainerRef = useRef<HTMLDivElement>(null);
  const { deptList } = useDeptList();

  // ai生成
  // 将useCode移到组件顶层，并监听businessType变化
  const [costTypeCode, setCostTypeCode] = useState<string>('');
  const { codeList, getEnumList } = useCode(costTypeCode, { able: true });

  // 当业务类型变化时更新费用种类的枚举列表
  useEffect(() => {
    if (detail.businessType) {
      // 根据预付款单现有逻辑，charge_type_1和charge_type_4是现场费用-抖币和抽奖及打赏-福袋写死的
      if (detail.businessType == 'charge_type_1') {
        codeList.push({ value: 'DOU_CURRENCY', label: '现场费用-抖币' });
      } else if (detail.businessType === 'charge_type_4') {
        codeList.push(
          { value: 'LUCKY_BAG', label: '抽奖及打赏-福袋' },
          { value: 'LIVE_ROOM_LOTTERY', label: '抽奖及打赏-直播间-实物' },
        );
      } else {
        setCostTypeCode(detail.businessType);
      }
    }
  }, [detail.businessType]);
  // ********-飞虎ai结尾共生成9行代码

  useEffect(() => {
    if (visible && id) {
      fetchData();
    }
  }, [visible, id]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = (await getAdvancePaymentDetail({ no: sourceNo })) as unknown as ApiResponse;

      // 根据实际接口响应结构处理数据
      let result;
      if (response && typeof response === 'object') {
        // 尝试不同的数据结构模式
        result = response?.res?.result; // 返回结果
      }

      if (result) {
        // 获取部门名称
        const deptName = deptList.find((item) => item.value === String(result.deptId))?.label;

        // 将API返回的数据映射到组件需要的数据结构
        setDetail({
          title: `预付款申请单-${result.applicant}-${moment(result.gmtCreated).format(
            'YYYY-MM-DD HH:mm:ss',
          )}-${deptName}-${result.settlementEntityName}-${formatAmount(
            result?.applicationAmount,
          )}-${result.no}`, // 标题
          ...result,
          deptName: deptName, // 业务线可能需要映射
        });
      } else {
        console.error('获取预付款单详情异常:', response);
      }
    } catch (error) {
      console.error('获取预付款单详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // ai生成
  /**
   * 格式化金额，每三位加逗号，保留两位小数
   * @param value 需要格式化的金额，可以是数字或字符串
   * @returns 格式化后的金额字符串
   */
  const formatAmount = (value: string | number | undefined): string => {
    if (value === undefined || value === null) {
      return '-';
    }

    // 将输入转换为数字
    const numValue = typeof value === 'string' ? parseFloat(value) : value;

    // 检查是否为有效数字
    if (isNaN(numValue)) {
      return '-';
    }

    // 格式化数字：保留两位小数，添加千位分隔符
    return numValue.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };
  // ********-飞虎ai结尾共生成22行代码

  // 处理打印功能
  const handlePrint = () => {
    if (!printContainerRef.current) return;

    // ai生成
    // 显示加载提示
    const loadingMessage = document.createElement('div');
    loadingMessage.style.position = 'fixed';
    loadingMessage.style.top = '50%';
    loadingMessage.style.left = '50%';
    loadingMessage.style.transform = 'translate(-50%, -50%)';
    loadingMessage.style.padding = '20px';
    loadingMessage.style.background = 'rgba(0, 0, 0, 0.7)';
    loadingMessage.style.color = 'white';
    loadingMessage.style.borderRadius = '4px';
    loadingMessage.style.zIndex = '9999';
    loadingMessage.textContent = '正在准备打印内容，请稍候...';
    document.body.appendChild(loadingMessage);

    // 创建打印窗口，但先不显示内容
    const printWindow = window.open('about:blank', '_blank');
    if (!printWindow) {
      alert('请允许弹出窗口以便进行打印');
      document.body.removeChild(loadingMessage);
      return;
    }

    // 显示加载中页面
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>预付款单打印 - 加载中</title>
          <style>
            body {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              margin: 0;
              background-color: #f0f2f5;
              font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            }
            .loading {
              text-align: center;
            }
            .loading-text {
              margin-top: 20px;
              font-size: 16px;
              color: #1890ff;
            }
            .spinner {
              display: inline-block;
              width: 50px;
              height: 50px;
              border: 3px solid rgba(24, 144, 255, 0.2);
              border-radius: 50%;
              border-top-color: #1890ff;
              animation: spin 1s ease-in-out infinite;
            }
            @keyframes spin {
              to { transform: rotate(360deg); }
            }
          </style>
        </head>
        <body>
          <div class="loading">
            <div class="spinner"></div>
            <div class="loading-text">正在生成打印预览，请稍候...</div>
          </div>
        </body>
      </html>
    `);

    // 延迟执行以确保提示显示
    setTimeout(() => {
      // 使用html2canvas将内容转换为图片
      html2canvas(printContainerRef.current!, {
        scale: 2, // 提高图片清晰度
        useCORS: true, // 允许加载跨域图片
        allowTaint: true, // 允许污染画布
        backgroundColor: '#ffffff', // 设置背景色为白色
        logging: false, // 关闭日志
        onclone: (documentClone) => {
          // 克隆的文档中找到打印容器
          const printContainer = documentClone.getElementById('print-container');
          if (printContainer) {
            // 确保打印容器在克隆文档中可见且样式正确
            printContainer.style.display = 'block';
            printContainer.style.width = '100%';
            printContainer.style.maxWidth = '800px';
            printContainer.style.margin = '0 auto';
            printContainer.style.padding = '20px';
            printContainer.style.overflow = 'visible';

            // 设置容器内所有元素可见
            const allElements = printContainer.querySelectorAll('*');
            allElements.forEach((el) => {
              const element = el as HTMLElement;
              if (element.style) {
                element.style.display = '';
                element.style.visibility = 'visible';
              }
            });
          }
          return documentClone;
        },
      })
        .then((canvas) => {
          // 移除加载提示
          document.body.removeChild(loadingMessage);

          // 设置打印窗口内容
          printWindow.document.open();
          printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>预付款单审批打印</title>
              <style>
                body {
                  margin: 0;
                  padding: 20px;
                  display: flex;
                  justify-content: center;
                  background-color: #f0f2f5;
                  font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
                }
                .print-container {
                  background-color: white;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                  width: 100%;
                  max-width: 800px;
                }
                img {
                  display: block;
                  width: 100%;
                }
                .print-header {
                  padding: 16px;
                  background-color: white;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  border-bottom: 1px solid #e8e8e8;
                }
                .print-title {
                  font-size: 16px;
                  font-weight: bold;
                  color: #333;
                  margin: 0;
                }
                .print-button {
                  background-color: #1890ff;
                  color: white;
                  border: none;
                  padding: 6px 16px;
                  border-radius: 2px;
                  cursor: pointer;
                  font-size: 14px;
                  transition: background-color 0.3s;
                }
                .print-button:hover {
                  background-color: #40a9ff;
                }
                @media print {
                  body {
                    background-color: white;
                    padding: 0;
                  }
                  .print-container {
                    box-shadow: none;
                    max-width: 100%;
                  }
                  .print-header {
                    display: none;
                  }
                }
              </style>
            </head>
            <body>
              <div class="print-container">
                <div class="print-header">
                  <h1 class="print-title">打印预览</h1>
                  <button class="print-button" onclick="window.print()">打印</button>
                </div>
                <img src="${canvas.toDataURL('image/png')}" alt="预付款单" />
              </div>
            </body>
          </html>
        `);

          printWindow.document.close();
        })
        .catch((error) => {
          console.error('生成打印图片时出错：', error);
          document.body.removeChild(loadingMessage);
          if (printWindow) {
            printWindow.close();
          }
          // alert('生成打印内容时出错，请重试');
          message.error('生成打印内容时出错，请重试');
        });
    }, 100);
    // ********-飞虎ai结尾共生成179行代码
  };

  const modalFooter = (
    <div className={styles.modalFooter}>
      <Button onClick={onCancel} style={{ marginRight: 8 }}>
        取消
      </Button>
      <Button type="primary" onClick={handlePrint}>
        打印
      </Button>
    </div>
  );

  return (
    <Modal
      title="预付款单打印预览"
      visible={visible}
      onCancel={onCancel}
      width={900}
      bodyStyle={{ padding: '24px', maxHeight: '70vh', overflow: 'auto' }}
      footer={modalFooter}
      destroyOnClose
      centered
    >
      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin tip="正在加载单据数据..." size="large" />
        </div>
      ) : (
        <div id="print-container" className={styles.printContainer} ref={printContainerRef}>
          <div className={styles.documentHeader}>
            {/* 二维码生成组件 */}
            <ComQRcode id={approvalNo || ''} />
            {/* 中间模版内容 */}
            <h1 className={styles.pageTitle}>朋友云-预付款单审批流程</h1>
            <div className={styles.flowInfo}>
              <div className={styles.flowItem}>
                <span>流程编号:</span>
                <span>{approvalNo}</span>
              </div>
            </div>
          </div>

          <Card>
            <Title>基础信息</Title>
            <DetailContextBox>
              <Item label="标题" span={4}>
                <p>{detail.title}</p>
              </Item>
              <Item label="申请人" span={2}>
                <p>{detail.applicant || '-'}</p>
              </Item>
              <Item label="申请日期" span={2}>
                <p>{moment(detail.applyDate).format('YYYY-MM-DD HH:mm:ss')}</p>
              </Item>
              <Item label="费用所属公司" span={2}>
                <p>{detail.expenseCompanyName || '-'}</p>
              </Item>
              <Item label="费用所属部门" span={2}>
                <p>{detail.expenseDepartmentName || '-'}</p>
              </Item>
              <Item label="业务类型" span={2}>
                <p>
                  {detail.businessType
                    ? BUSSINESS_TYPE[detail.businessType as keyof typeof BUSSINESS_TYPE] || '-'
                    : '-'}
                </p>
              </Item>
              <Item label="费用种类" span={2}>
                <p>
                  {/* ai生成 */}
                  {codeList && codeList.length > 0
                    ? codeList.find((item) => item.value === detail.expenseType)?.label || '-'
                    : '-'}
                  {/* ********-飞虎ai结尾共生成3行代码 */}
                </p>
              </Item>
              <Item label="业务线" span={2}>
                <p>{detail.deptName || '-'}</p>
              </Item>
              <Item label="备注" span={4}>
                <p>{detail.remarks}</p>
              </Item>
            </DetailContextBox>
          </Card>

          <Card>
            <Title>付款信息</Title>
            <DetailContextBox>
              <Item label="结算主体" span={2}>
                <p>{detail.settlementEntityName || '-'}</p>
              </Item>
              <Item label="银行账号" span={2}>
                <p>{detail.payeeBankAccount || '-'}</p>
              </Item>
              <Item label="开户银行" span={2}>
                <p>{detail.bankName || '-'}</p>
              </Item>
              <Item label="付款申请金额" span={2}>
                <p>
                  {/* ai生成 */}
                  {formatAmount(detail.applicationAmount)}
                  {/* ********-飞虎ai结尾共生成1行代码 */}
                </p>
              </Item>
              <Item label="发票交付" span={2}>
                <p>
                  {detail.invoiceDelivery === 'PAYMENT_BEFORE_INVOICE'
                    ? '先款后票'
                    : detail.invoiceDelivery === 'INVOICE_BEFORE_PAYMENT'
                    ? '先票后款'
                    : '无发票'}
                </p>
              </Item>
            </DetailContextBox>
          </Card>

          <Card>
            <Title>发票信息</Title>
            <Table
              dataSource={detail.invoiceInfoList}
              rowKey={(record, index) => record.id || String(index)}
              pagination={false}
              size="small"
              bordered
              style={{ marginTop: '12px' }}
              columns={[
                {
                  title: '#',
                  dataIndex: 'index',
                  key: 'index',
                  align: 'center',
                  width: 60,
                  render: (_, __, index) => index + 1,
                },
                {
                  title: '发票号码',
                  dataIndex: 'invoiceNum',
                  key: 'invoiceNum',
                  align: 'center',
                  render: (text) => text || '-',
                },
                {
                  title: '开票日期',
                  dataIndex: 'invoiceDate',
                  key: 'invoiceDate',
                  align: 'center',
                  render: (text) => text || '-',
                },
                {
                  title: '发票类型',
                  dataIndex: 'invoiceType',
                  key: 'invoiceType',
                  align: 'center',
                  render: (text) =>
                    INVOICE_TYPE_MANAGEMENT[text as keyof typeof INVOICE_TYPE_MANAGEMENT] || '-',
                },
                {
                  title: '购买方',
                  dataIndex: 'buyer',
                  key: 'buyer',
                  align: 'center',
                  render: (text) => text || '-',
                },
                {
                  title: '含税金额',
                  dataIndex: 'priceTaxTotalFigure',
                  key: 'priceTaxTotalFigure',
                  align: 'right',
                  render: (text) => text || '-',
                },
                {
                  title: '税率',
                  dataIndex: 'taxRate',
                  key: 'taxRate',
                  align: 'center',
                  render: (text) => text || '-',
                },
                {
                  title: '不含税金额',
                  dataIndex: 'noTaxAmountTotal',
                  key: 'noTaxAmountTotal',
                  align: 'right',
                  render: (text) => text || '-',
                },
                {
                  title: '可抵扣税额',
                  dataIndex: 'deductibleTaxAmount',
                  key: 'deductibleTaxAmount',
                  align: 'right',
                  render: (text) => text || '-',
                },
              ]}
            />
          </Card>

          <Card>
            <Title>审批记录</Title>
            <div className={styles.emptyData}>
              {/* 审批记录组件 */}
              <ApproveList id={id} taskId={taskId} />
            </div>
          </Card>
        </div>
      )}
    </Modal>
  );
};

export default PrintModal;
