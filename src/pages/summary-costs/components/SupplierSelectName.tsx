import React, { useEffect, useRef, useState } from 'react';
import { Button, Row, Col, Input, Select } from 'antd';
import { getSupplierList } from '@/services/gql/supplier-list/list';
import { liveroomList, getSupplier } from '@/services/yml/goods-assorting/index';

const SupplierSelectName = (props) => {
  const { value, onChange, mode } = props;
  const [dataList, setDataList] = useState([]); //直播间列表
  const getList = (name) => {
    const params = {
      size: 10,
      current: 1,
      name,
    };
    getSupplier({ ...params }).then((res) => {
      // console.log(res);
      if (res?.res?.code === '200') {
        const resData = res?.res?.result;
        resData?.length > 0 ? setDataList([...resData]) : setDataList([]);
      } else {
        setDataList([]);
      }
    });
  };
  useEffect(() => {
    getList('');
  }, []);

  return (
    <Select
      value={value}
      allowClear
      showSearch
      mode={mode}
      onSearch={getList}
      onChange={onChange}
      maxTagCount={mode === 'multiple' ? 1 : undefined}
      placeholder="请选择"
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      notFoundContent={null}
    >
      {dataList?.map((i, index) => {
        return (
          <Option value={i?.supplierCompanyCode} key={index}>
            {i?.supplierCompanyCode}
          </Option>
        );
      })}
    </Select>
  );
};
export default SupplierSelectName;
