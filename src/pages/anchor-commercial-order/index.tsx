import { Button, Form, message, Table, Modal, Icon } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { AuthWrapper } from 'qmkit';
import React, { useEffect, useState } from 'react';
import PageLayout from '@/components/PageLayout/index';
import PaginationProxy from '@/common/constants/Pagination';
import styles from './index.module.less';
import { useTableHeight } from '@/common/constants/hooks/index';
import SearchForm from './components/SearchForm';
import { getColumnsCommercialOrder } from './utils/getColumns';
import { useList } from './utils/hook';
import { history } from 'qmkit';
import { SearchFormType } from './utils/type';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
import {
  liveBusinessOrderDel,
  liveBusinessOrderSubmit,
  liveBusinessOrderForceVoid,
  liveBusinessOrderPushCost,
  LiveBusinessOrderBatchPushCostResult,
  LiveBusinessOrderBatchPushCostRequest,
  liveBusinessOrderBatchPushCost,
  liveBusinessOrderExport,
  LiveBusinessOrderExportRequest,
} from './services';
import {
  updateCostInfoReject,
  UpdateCostInfoRejectRequest,
} from '@/pages/anchor-duration-statistics/services/index';
import ResShowInfo from './components/ResShowInfo';
import { useLocation } from 'react-router-dom';
import { handlePrecision } from '@/common/common';
import { SORT_ENUM } from '../anchor-duration-statistics/utils/enum';

const AnchorInformation: React.FC<FormComponentProps<SearchFormType>> = ({ form }) => {
  const { list, loading, getList, pagination, dataSummary } = useList(form);
  const { confirm } = Modal;
  const location = useLocation();
  useEffect(() => {
    getList({});
    setSelectedKeys([]);
    setSelectedFullKeys([]);
  }, [location]);

  const onReset = () => {
    form.resetFields();
    setSelectedKeys([]);
    setSelectedFullKeys([]);
    getList({
      current: 1,
      size: 20,
    });
  };
  const { getHeight, tableHeight } = useTableHeight(80);

  const handleGoAdd = () => {
    history.push(`/anchor-commercial-order-add?type=create`);
  };
  const handleGoCopy = (value?: Record<string, any>) => {
    console.log('value', value);
    history.push({
      pathname: '/anchor-commercial-order-add',
      state: value,
    });
  };
  const handleGoEdit = (id?: string) => {
    history.push(`/anchor-commercial-order-edit?type=edit&id=${id}`);
  };
  const handleGoDetail = (value?: string) => {
    history.push(`/anchor-commercial-order-detail?no=${value}`);
  };
  const handleDelete = async (id?: string) => {
    const result = await responseWithResultAsync({
      request: liveBusinessOrderDel,
      params: { id },
    });
    if (result) {
      message.success('操作成功');
      setSelectedKeys([]);
      setSelectedFullKeys([]);
      getList({});
    }
  };
  //作废
  const handleVoidForce = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveBusinessOrderForceVoid,
      params: { id },
    });
    if (result) {
      getList({});
      setSelectedKeys([]);
      setSelectedFullKeys([]);
      message.success('操作成功');
    }
  };
  //推送成本
  const handlePushCost = async (id: string) => {
    const result = await responseWithResultAsync({
      request: liveBusinessOrderPushCost,
      params: { id },
    });
    if (result) {
      getList({});
      setSelectedKeys([]);
      setSelectedFullKeys([]);
      message.success('操作成功');
    }
  };
  const [selectedKeys, setSelectedKeys] = useState<Array<string>>([]);
  const [selectedFullKeys, setSelectedFullKeys] = useState<Array<any>>([]);
  const rowSelection = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedKeys(selectedRowKeys);
      setSelectedFullKeys(selectedRows);
    },
    columnWidth: 80,
    selectedRowKeys: selectedKeys,
  };
  const [batchResInfo, setBatchResInfo] = useState<LiveBusinessOrderBatchPushCostResult | null>();

  const batchAction = (value: 'pushCost' | 'reject') => {
    const type = {
      pushCost: '批量推送成本',
      reject: '批量提交结果',
    };
    setBatchResInfo(null);
    if (selectedFullKeys.length) {
      const recordCodeList: Array<string> = [];
      selectedFullKeys.forEach((i) => {
        if (
          (i.status === 1 ||
            (i?.status && [2].includes(i.status) && i.costStatus === 'AUDIT_REJECT')) &&
          value === 'pushCost'
        ) {
          recordCodeList.push(i.id);
        }
        if (i.costStatus === 'AUDIT_PASS' && value === 'reject') {
          recordCodeList.push(i.recordCode);
        }
      });

      confirm({
        title: `当前选中数据${selectedFullKeys.length}条，其中${recordCodeList.length}条流程可${type[value]}，请确认是否操作`,
        icon: <Icon type="exclamation-circle" theme="filled" style={{ color: '#FAAD14' }} />,
        onOk() {
          if (recordCodeList.length) {
            if (value === 'pushCost') {
              batchPushCost(recordCodeList);
            }
            if (value === 'reject') {
              batchReject(recordCodeList);
            }
          }
        },
        onCancel() {
          console.log('Cancel');
        },
      });
    } else {
      message.warn('请选择数据');
    }
  };

  const batchPushCost = async (value: Array<string>) => {
    try {
      const params: LiveBusinessOrderBatchPushCostRequest = { idList: value };
      const { res } = await liveBusinessOrderBatchPushCost(params);
      if (res.code === '200') {
        setBatchResInfo(res?.result);
        setSelectedFullKeys([]);
        setSelectedKeys([]);
        getList({});
      }
    } catch {
      console.log('批量提交');
    }
  };
  const exportList = async (value: { idList: Array<string> }) => {
    console.log('导出参数', value, form.getFieldsValue());
    const idsList = value?.idList && value?.idList.length ? value?.idList : undefined;
    const params: LiveBusinessOrderExportRequest = {
      ...form.getFieldsValue(),
      idsList,
    };
    const { res } = await liveBusinessOrderExport(params);

    if (res.code === '200') {
      message.success('导出成功');
    } else {
      message.error(res.message);
    }
  };
  const batchReject = async (value: Array<string>) => {
    try {
      const params: UpdateCostInfoRejectRequest = {
        recordCodeList: value,
        expenseTypeEnum: 'BUSINESS_ORDER',
      };
      const { res } = await updateCostInfoReject(params);
      if (res.code === '200') {
        setBatchResInfo(res?.result);
        setSelectedFullKeys([]);
        setSelectedKeys([]);
        getList({ current: 1 });
      }
    } catch {
      console.log('批量提交');
    }
  };

  return (
    <PageLayout
      className={styles['cooperation-report-contain']}
      routePath="/anchor-commercial-order"
    >
      <div
        className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
        style={{ height: 'calc(100vh - 50px)', display: 'flex', flexDirection: 'column' }}
      >
        <div className="formHeight">
          <SearchForm
            form={form}
            loading={loading}
            onSearch={() => {
              setSelectedKeys([]);
              setSelectedFullKeys([]);
              getList({ current: 1 });
            }}
            onReset={onReset}
          />

          <div className="flex items-center mb-16">
            <AuthWrapper functionName="f_anchor_commercial_order_create">
              <Button type="primary" icon="plus" onClick={handleGoAdd}>
                商单
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anchor_commercial_push_cost">
              <Button
                className="ml-8"
                onClick={() => {
                  batchAction('pushCost');
                }}
              >
                推送成本
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anchor_commercial_export">
              <Button
                className="ml-8"
                onClick={() => {
                  exportList({ idList: selectedKeys });
                }}
              >
                <span className="iconfont icon-daochu"></span>
                导出
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anchor_commercial_export_list">
              <Button
                className="ml-8"
                onClick={() => {
                  history.push(
                    '/export-list-anchor-commercial-order?configCode=LIVE_BUSINESS_ORDER_EXPORT',
                  );
                }}
              >
                导出记录
              </Button>
            </AuthWrapper>
            <AuthWrapper functionName="f_anchor_commercial_reject">
              <Button
                className="ml-8"
                onClick={() => {
                  batchAction('reject');
                }}
              >
                审核驳回
              </Button>
            </AuthWrapper>
          </div>
        </div>
        <div className={styles.boardTable} style={{ flex: 1 }}>
          <AuthWrapper functionName="f_anchor_commercial_order_list">
            <Table
              rowKey="id"
              loading={loading}
              columns={getColumnsCommercialOrder({
                goEdit: handleGoEdit,
                goDetail: handleGoDetail,
                onDelete: handleDelete,
                onVoidForce: handleVoidForce,
                onPushCost: handlePushCost,
                onCopy: handleGoCopy,
              })}
              dataSource={list}
              pagination={false}
              rowSelection={rowSelection}
              scroll={{ y: tableHeight, x: '100%' }}
              onChange={(pagination, filters, sorter) => {
                const sortValue = SORT_ENUM?.[sorter.order] ?? undefined;
                getList({ realLiveDateSortRule: sortValue }, true);
              }}
            />
          </AuthWrapper>
        </div>
        <div className={styles['pagination-box'] + ' pageHeight'}>
          <AuthWrapper functionName="f_anchor_commercial_order_list">
            <PaginationProxy
              current={pagination?.current}
              pageSize={pagination?.size}
              total={pagination?.total}
              // @ts-ignore
              onChange={(current, size) => {
                getList({
                  current,
                  size,
                });
              }}
              valueType="flatten"
            />
          </AuthWrapper>
        </div>
        <div style={{ position: 'absolute', bottom: '20px', zIndex: '100' }}>
          <p className={styles.totalP}>
            结算金额:
            <span className={styles.totalSpan}>
              &yen;
              {dataSummary?.settlementAmount
                ? handlePrecision(dataSummary?.settlementAmount, 2, 1) + '元'
                : 0}
            </span>
          </p>
        </div>
        <ResShowInfo info={batchResInfo}></ResShowInfo>
      </div>
    </PageLayout>
  );
};

export default Form.create()(AnchorInformation);
