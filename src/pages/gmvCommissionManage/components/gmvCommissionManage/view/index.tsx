import {
  Button,
  Form,
  message,
  Popover,
  Spin,
  Table,
  Popconfirm,
  Tooltip,
  Icon,
  Input,
  Modal,
} from 'antd';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
// import { getTableHeight } from '@/common/common';
import { EbPay } from 'web-common-modules/components';
import SearchForm from './SearchForm';
import { useMount, useRequest, useSetState } from 'ahooks';
import { Const, history, AuthWrapper, OneAuthWrapper } from 'qmkit';
import { FormComponentProps } from 'antd/es/form';
import moment from 'moment';
import { isNullOrUndefined } from '@/utils/moduleUtils';
import { dealDecimalToPercent } from '@/utils/util';
import { DownLoadBillNode } from '../downLoadBill';
import styles from './index.module.less';
import { gmvCommissionFee } from '../service';
import { useEbPay } from './EBPayhook';
import { defaultRender } from '@/utils/string';
import { CooperationModeMap } from 'web-common-modules/biz/BusinessCooperation/constants';

import {
  batchExportApiForOp,
  batchExportApiForJg,
  cooperationFrameworkBillClose,
} from '../yml/index';
import {
  BIZ_ORDER_TYPE,
  CooperationFrameworkBillStatusEnum,
  PAY_TYPE,
  EBPAY_TYPE,
  SYS_TYPE,
  FromType,
  PaymentStatus,
} from '@/common/constants/moduleConstant';
import RebateBillModal from './components/RebateBillModal';
import ExportModal from '@/components/ExportModal';
import { classNames } from '@/utils/moduleUtils';
import { orderBatchToGMV, orderBatchToLive } from '../yml';
import { ImportBillModal } from './components/index';
import { totalPay } from './helper';
import {
  cooperationPlatformSubmit,
  livePlatformSubmit,
  getListCooperationFrameworkBillBySimplePage,
} from '../yml/index';

import Decimal from 'decimal.js';
// import PaginationProxy from './components/Pagination';
import PaginationProxy from '@/common/constants/Pagination';
import download from '@/assets/download.png';
import { PlantformName } from '../../../../../../web_modules/types';
import { useTableHeight } from '@/common/constants/hooks/index';
import PopoverRowText from '@/components/PopoverRowText/index';
interface IProps extends FormComponentProps {
  location?: {
    state: { [key: string]: any };
  };
  needCreateRebate: boolean;
  needBatchExport: boolean;
  isPay?: boolean;
  status?: string;
  type: string;
}

enum BillType {
  SUBMIT = 'SUBMIT', // 待提交
  PAYED = 'PAYED', //商家已付款
  UNPAY = 'UNPAY', // 待付款
  REJECT = 'REJECT', //已驳回
  CLOSE = 'CLOSE', //已关闭
}

const billTypeMap = {
  [CooperationFrameworkBillStatusEnum.IN_PROCESS]: BillType.SUBMIT,
  false: BillType.UNPAY,
  true: BillType.PAYED,
  [CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT]: BillType.REJECT,
  [CooperationFrameworkBillStatusEnum.CLOSE]: BillType.CLOSE,
};

// const { confirm } = Modal;

const SudoInputBtn = ({ text, className, onSuccess }: any) => {
  const [show, setShow] = useState(false);
  const [value, setValue] = useState<string>();
  const onOk = () => {
    setData([]);
    const arr = value ? value.split('\n').filter((i) => i) : undefined;
    // console.log(arr);
    if (arr) {
      if (arr.length > 50) {
        message.warn('最多输入50条');
      } else {
        if (text === '年框订单') {
          onSuccessLive(arr);
        } else {
          onSuccessGmv(arr);
        }
      }
    }

    // onSuccess(arr)?.then(() => {
    //   setShow(false);
    // });
  };
  const onSuccessGmv = (value: string[]) => {
    return orderBatchToLive({ coopOrderNoList: value })
      .then(({ res }) => {
        console.log('gmv', res);
        // checkResponse(res);
        if (res?.code === '200') {
          setShow(false);
          setResShow(true);
          // message.success('操作成功');

          setSuccessNum(res?.result?.successCount);
          setFailNum(res?.result?.failCount);
          setData(res.result?.failResultModels);
        } else {
          message.error(res?.message);
        }
      })
      .catch((err) => {
        message.error(err.message);
        return Promise.reject();
      });
  };
  const onSuccessLive = (value: string[]) => {
    return orderBatchToGMV({ coopOrderNoList: value })
      .then(({ res }) => {
        console.log('nian', res);
        if (res?.code === '200') {
          setShow(false);
          setResShow(true);
          // message.success('操作成功');
          setSuccessNum(res?.result?.successCount);
          setFailNum(res?.result?.failCount);
          setData(res.result?.failResultModels);
        } else {
          message.error(res?.message);
        }
      })
      .catch((err) => {
        message.error(err.message);
        return Promise.reject();
      });
  };
  useEffect(() => {
    if (show) {
      setValue('');
    }
  }, [show]);
  const [resShow, setResShow] = useState(false);
  const [data, setData] = useState([]);
  const [successNum, setSuccessNum] = useState(0);
  const [failNum, setFailNum] = useState(0);
  const columns = [
    {
      title: '合作订单',
      dataIndex: 'coopOrderNo',
      key: 'coopOrderNo',
      render: (t) => t || '-',
      width: 120,
    },
    {
      title: '失败原因',
      dataIndex: 'failReason',
      key: 'failReason',
      render: (t) => t || '-',
      width: 120,
    },
  ];
  return (
    <span className={classNames(className, 'flex flex-row items-center')}>
      {!show && (
        <Button style={{ borderColor: '#999999', color: '#444444' }} onClick={() => setShow(true)}>
          转{text}
        </Button>
      )}
      <Modal width={500} title={text} visible={show} onOk={onOk} onCancel={() => setShow(false)}>
        <p
          style={{
            height: '20px',
            fontWeight: '400',
            fontSize: '12px',
            color: '#666666',
            lineHeight: '20px',
            textAlign: 'left',
            marginTop: '-12px',
            marginBottom: '6px',
          }}
        >
          多个{text}换行输入即可
        </p>
        <Input.TextArea
          rows={6}
          allowClear
          value={value}
          onChange={(e) => {
            const data = e.target.value;
            setValue(data);
          }}
        />
      </Modal>
      <Modal
        title={
          text === '年框订单' ? '普通订单批量转年框订单操作结果' : '年框订单批量转普通订单操作结果'
        }
        visible={resShow}
        onOk={() => setResShow(false)}
        onCancel={() => setResShow(false)}
        cancelText={''}
      >
        <p
          style={{
            height: '20px',
            fontWeight: '400',
            fontSize: '14px',
            color: '#666666',
            lineHeight: '20px',
            textAlign: 'left',
            marginBottom: '6px',
          }}
        >
          <Icon
            type="check-circle"
            style={{ color: '#52C41A', marginRight: '6px' }}
            theme="filled"
          />
          {successNum}条转
          {text}订单成功
        </p>
        <p
          style={{
            height: '20px',
            fontWeight: '400',
            fontSize: '14px',
            color: '#666666',
            lineHeight: '20px',
            textAlign: 'left',
            marginBottom: '16px',
          }}
        >
          <Icon
            type="close-circle"
            style={{ color: '#EE0000', marginRight: '6px' }}
            theme="filled"
          />
          {failNum}条转
          {text}订单失败
        </p>
        {failNum > 0 && (
          <div>
            <p
              style={{
                height: '20px',
                fontWeight: '500',
                fontSize: '14px',

                lineHeight: '20px',
                textAlign: 'left',
                marginBottom: '4px',
              }}
            >
              失败明细
            </p>
            <Table
              columns={columns}
              pagination={false}
              dataSource={data as any[]}
              scroll={{ y: 250, x: '100%' }}
            ></Table>
          </div>
        )}
      </Modal>
    </span>
  );
};

const CooperationBill: React.FC<IProps> = forwardRef(
  ({ form, needCreateRebate, needBatchExport, location, ...restProps }, onRef) => {
    const [datasource, setDatasource] = useState<any[]>([]);
    const [pagination, setPagination] = useSetState({
      current: 1,
      size: 20,
      total: 0,
    });
    const [commisionFee, setCommisionFee] = useState();
    const [selectedRows, setSelectedRows] = useState([]);
    const [selectedRowsKeys, setSelectedRowsKeys] = useState([]);
    const [params, setParams] = useState({});
    const [allPay, setAllPay] = useState(false);
    // isPay 和 status 替换掉父组件传入的isPay和status
    const [isPay, setIsPay] = useState<boolean | undefined>(true);
    const [status, setStatus] = useState<string | undefined>(undefined);
    const [billType, setBillType] = useState<BillType>(BillType.PAYED);
    const [saveStatus, setSaveStatus] = useState<string>('true');

    const handleBillingStatusChange = (value: string) => {
      setSaveStatus(value);
      // 改变status和isPay
      if (value === 'true' || value === 'false') {
        setStatus(undefined);
        setIsPay(value === 'true' ? true : false);
        return;
      }
      setIsPay(undefined);
      setStatus(value);
    };

    // 付款方式
    const getPayType = () => {
      return ['production', 'gray'].includes(Const.NODE_SERVER_ENV)
        ? [PAY_TYPE.EBANK_B2B, PAY_TYPE.OFF_LINE_BANK_TRANSFER]
        : [PAY_TYPE.EBANK_B2B, PAY_TYPE.EBANK_B2C, PAY_TYPE.OFF_LINE_BANK_TRANSFER];
    };

    const onSelectChange = (selectedRowKeys, selectedRows) => {
      setSelectedRows(selectedRows);
      setSelectedRowsKeys(selectedRowKeys);
      restProps?.setRowSelectedkeys && restProps?.setRowSelectedkeys(selectedRows);
    };

    /**
     * 通过GraphQL获取列表五种状态
     * 待提交 paid：false， status：2
     * 待付款 isPay： false
     * 商家已付款 isPay: true
     * 已驳回 status：10
     * 已关闭 status：20
     */
    const { loading: loading, run: getListRun } = useRequest(
      getListCooperationFrameworkBillBySimplePage,
      {
        manual: true,
        onSuccess: ({ res }) => {
          if (res?.success) {
            const { records, total, current, size } = res.result || {};
            setDatasource(records || []);
            setPagination({
              total,
              current,
              size,
            });

            return;
          }
          message.error(res?.message || Const.ERR_MESSAGE);
        },
      },
    );
    //总费用
    const getCommission = async () => {
      const { res } = await gmvCommissionFee(
        [SYS_TYPE.OPERATE, SYS_TYPE.SUPPLIER].includes(Const.SYS_TYPE)
          ? { paid: isPay }
          : { paid: true, isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined },
        FromType.OPERATE,
      );
      if (res.success) {
        setCommisionFee(res.result);
      }
    };
    useEffect(() => {
      if (typeof isPay !== 'boolean') return;
      getCommission();
    }, []);

    const initParam = () => {
      const res = {};

      if ([SYS_TYPE.ORGANIZATION, SYS_TYPE.TALENT].includes(Const.SYS_TYPE)) {
        res.paid = true;
        return res;
      }
      res.paid = isPay ?? undefined;
      res.status = status;
      return res;
    };
    const onSearch = (pages?: any, isPayStatus?: any) => {
      const { current, size } = pagination;
      const {
        paymentTime,
        createdTime,
        liveTime,
        rejectTime,
        statisticsTime,
        closeTime,
        modelTypes = [],
        platform,
        supplierIds,
        talentIds,
        billNo,
      } = form.getFieldsValue();
      let paymentTimeEnd, paymentTimeStart;
      let gmtCreatedStart, gmtCreatedEnd;
      let liveTimeStart,
        liveTimeEnd,
        rejectTimeStart,
        rejectTimeEnd,
        statisticsTimeStart,
        statisticsTimeEnd,
        closeTimeStart,
        closeTimeEnd;

      if (paymentTime?.length >= 2) {
        paymentTimeStart = paymentTime[0].format('YYYY-MM-DD 00:00:00');
        paymentTimeEnd = paymentTime[1].format('YYYY-MM-DD 23:59:59');
        paymentTimeEnd = Date.parse(paymentTimeEnd); // 转为时间戳形式
        paymentTimeStart = Date.parse(paymentTimeStart); // 转为时间戳形式
      }
      if (createdTime?.length >= 2) {
        gmtCreatedStart = createdTime[0].format('YYYY-MM-DD 00:00:00');
        gmtCreatedEnd = createdTime[1].format('YYYY-MM-DD 23:59:59');
        gmtCreatedEnd = Date.parse(gmtCreatedEnd); // 转为时间戳形式
        gmtCreatedStart = Date.parse(gmtCreatedStart); // 转为时间戳形式
      }
      if (liveTime?.length >= 2) {
        liveTimeStart = liveTime[0].format('YYYY-MM-DD 00:00:00');
        liveTimeEnd = liveTime[1].format('YYYY-MM-DD 23:59:59');
        liveTimeEnd = Date.parse(liveTimeEnd); // 转为时间戳形式
        liveTimeStart = Date.parse(liveTimeStart); // 转为时间戳形式
      }

      if (rejectTime?.length >= 2) {
        rejectTimeStart = rejectTime[0].format('YYYY-MM-DD 00:00:00');
        rejectTimeEnd = rejectTime[1].format('YYYY-MM-DD 23:59:59');
        rejectTimeEnd = Date.parse(rejectTimeEnd); // 转为时间戳形式
        rejectTimeStart = Date.parse(rejectTimeStart); // 转为时间戳形式
      }

      if (statisticsTime?.length >= 2) {
        statisticsTimeStart = statisticsTime[0].format('YYYY-MM-DD 00:00:00');
        statisticsTimeEnd = statisticsTime[1].format('YYYY-MM-DD 23:59:59');
        statisticsTimeEnd = Date.parse(statisticsTimeEnd); // 转为时间戳形式
        statisticsTimeStart = Date.parse(statisticsTimeStart); // 转为时间戳形式
      }
      if (closeTime?.length >= 2) {
        closeTimeStart = closeTime[0].format('YYYY-MM-DD 00:00:00');
        closeTimeEnd = closeTime[1].format('YYYY-MM-DD 23:59:59');
        closeTimeEnd = Date.parse(closeTimeEnd); // 转为时间戳形式
        closeTimeStart = Date.parse(closeTimeStart); // 转为时间戳形式
      }
      const { status, otherForm } = form.getFieldsValue();
      const isPayStatusParam = isPayStatus || initParam();

      const finParams = {
        //cluePoolType:TalentCluePoolType.PRIVATE_RESOURCE.key,
        // ctxEmployeeId:util.getLoginData()?.employeeId,
        // institutionId:util.getLoginData()?.storeId,
        current: pages?.current || current,
        size: pages?.size || size,
        ...isPayStatusParam,
        paymentTimeEnd,
        paymentTimeStart,
        gmtCreatedEnd,
        gmtCreatedStart,
        liveTimeStart,
        liveTimeEnd,
        rejectTimeStart,
        rejectTimeEnd,
        statisticsTimeStart,
        statisticsTimeEnd,
        closeTimeStart,
        closeTimeEnd,
        ...otherForm,
        modelTypes: modelTypes.filter((item) => !!item),
        platform: platform || null,
        supplierIds: supplierIds ? [supplierIds] : null,
        talentIds: talentIds ? [talentIds] : null,
        isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
        billNo,
      };
      getListRun(finParams);
      setParams(finParams);
      getCommission();
      if (!isPayStatus) {
        setBillType(billTypeMap[saveStatus as 'true']);
      } else {
        setBillType(billTypeMap['true' as const]);
        setSaveStatus('true');
      }
    };
    const onReset = useCallback(() => {
      form.resetFields();
      const { current, size } = pagination;
      handleBillingStatusChange('true');
      // setBillType(billTypeMap['true' as const]);
      // setSaveStatus('true');
      if (current === 1 && size === 10) {
        onSearch(undefined, { paid: true });
        return;
      }

      setPagination({
        current: 1,
        size: 20,
      });
    }, [form, setPagination, pagination]);

    const refresh = () => {
      onSearch();
    };
    useImperativeHandle(onRef, () => {
      return {
        refresh: refresh,
        onPay: onPay,
      };
    });

    useMount(() => {
      const { current, size } = pagination;
      getListRun({ current, size });
    });

    const copDetailPath = {
      [SYS_TYPE.ORGANIZATION]: '/business/orderdetail/',
      [SYS_TYPE.SUPPLIER]: '/order-cooperation/',
      [SYS_TYPE.TALENT]: '/order-cooperation/',
      [SYS_TYPE.OPERATE]: '/order-cooperation/',
    };
    const sectionDetailPath = {
      [SYS_TYPE.ORGANIZATION]: '/order-cooperation/sectionDetail/',
      [SYS_TYPE.SUPPLIER]: '/order-cooperation/sectionDetail/',
      [SYS_TYPE.TALENT]: '/order-cooperation/sectionDetail/',
      [SYS_TYPE.OPERATE]: '/order-cooperation/sectionDetail/',
    };
    type PathKey = keyof typeof sectionDetailPath | keyof typeof copDetailPath;
    const {
      payVisable,
      onPayCancel,
      onPayok,
      orderNo,
      onCreateOrder,
      loadingInfo,
      setPayVisable,
      initTotalPay,
    } = useEbPay(
      () => refresh(),
      () => {},
    );

    const onPay = useCallback(
      (record) => {
        const selectedRowsNo = selectedRows.map((_) => ({
          billNo: _.billNo,
          billType: _.bizBillTypeEnum,
        }));
        setAllPay(false);
        const total = totalPay(selectedRows) || 0;
        // setTotalPay(total);
        onCreateOrder(selectedRowsNo, total);
      },
      [onCreateOrder, selectedRows],
    );
    // 继续付款
    const continuePay = (record: any) => {
      const selectedRowsNo = [record].map((_) => ({
        billNo: _.billNo,
        billType: _.bizBillTypeEnum,
      }));

      const total = totalPay([record]) || 0;
      // setTotalPay(total);
      onCreateOrder(selectedRowsNo, total);
    };
    const onPayTotal = useCallback(
      (record) => {
        // console.log('🚀 ~ file: index.tsx:367 ~ commisionFee:', commisionFee);
        setAllPay(true);
        onCreateOrder([], new Decimal(commisionFee));
      },
      [onCreateOrder, commisionFee],
    );
    useEffect(() => {
      onSearch();
      onSelectChange([], []);
      // !isPay && Const.SYS_TYPE === SYS_TYPE.SUPPLIER && onSelectChange([], []);
    }, [pagination.current, pagination.size]);

    //提交
    const handleSubmit = (type: string, record: any) => {
      // console.log('🚀 ~ file: index.tsx:361 ~ handleSubmit ~ record:', record);
      // console.log('🚀 ~ file: index.tsx:361 ~ handleSubmit ~ type:', type);
      const api = type === 'GMV' ? cooperationPlatformSubmit : livePlatformSubmit;
      api({
        billId: record.bizId,
        isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
      })
        .then(({ res }) => {
          if (res?.success) {
            message.success('提交成功');
            refresh();
          } else {
            message.error(res?.message || '接口错误');
          }
        })
        .catch((err) => {
          message.error(err || '接口错误');
        });
    };

    const closeBill = (params: any) => {
      Modal.confirm({
        title: '账单关闭后不可再恢复，请确认要关闭该账单吗？',
        icon: <Icon type="exclamation-circle" theme="filled" style={{ color: '#fda12a' }} />,
        onOk() {
          cooperationFrameworkBillClose(params).then(({ res }) => {
            if (!res?.success) {
              message.error(res?.message || '关闭账单失败');
              return;
            }
            message.success('关闭账单成功');
            refresh();
          });
        },
        onCancel() {
          // console.log('Cancel');
        },
      });
    };

    const handleSubmitBtn = (type: string, params: any) => {
      Modal.confirm({
        title: '提交后账单将生效，商家会看到账单，确认要提交吗？',
        icon: <Icon type="exclamation-circle" theme="filled" style={{ color: '#fda12a' }} />,
        onOk() {
          handleSubmit(type, params);
        },
        onCancel() {
          // console.log('Cancel');
        },
      });
    };

    const columns = [
      {
        title: '#',
        align: 'center',
        key: 'number',
        className: styles['table-number'],
        render: (text: any, red: any, index: any) => {
          return (
            <span>
              {((pagination.current < 1 ? 1 : pagination.current) - 1) * pagination.size +
                index +
                1}
            </span>
          );
        },
        width: 30,
      },
      {
        title: '账单编号',
        key: 'billNoother',
        width: 125,
        render: (_, record) => {
          return (
            <Popover placement="rightTop" title="账单编号" content={record?.billNo || '-'}>
              <p>{record?.billNo || '-'}</p>
            </Popover>
          );
        },
      },
      {
        title: '账单名称',
        key: 'billNo',
        render: (_, record) => (
          <>
            <div className={styles.listImg}>
              <p className={styles.billName}>{record?.billName || '-'}</p>
              {/* <Popover title="账单名称" content={record?.billName || '-'}>
                
              </Popover> */}
            </div>
          </>
        ),
        width: 250,
      },
      {
        title: '商品名称',
        key: 'spuName',
        dataIndex: 'spuName',
        render: (t: any) => <PopoverRowText text={t || '-'} />,
        width: 150,
      },
      {
        title: '账单类型',
        key: 'modelType',
        dataIndex: 'modelType',
        render: (t: any) => defaultRender(CooperationModeMap[t]),
        width: 114,
      },
      {
        title: '平台',
        key: 'platform',
        dataIndex: 'platform',
        render: (platform: string) => {
          return <>{platform ? PlantformName[platform] : '-'}</>;
        },
        width: 50,
      },
      {
        title: '有效GMV',
        key: 'totalFees',
        dataIndex: 'totalFees',
        align: 'right',
        render: (t: any, record: any) =>
          ['SECTION_ORDER', 'LIVE_ROUND_FRAMEWORK_BILL_ORDER', 'EXTRA_REWARD_BILL_ORDER'].includes(
            record?.bizBillTypeEnum,
          )
            ? '-'
            : `¥ ${Number(t).toFixed(2)}` || '-',
        width: 120,
      },
      {
        title: '基础服务费比例',
        key: 'gmvTalentCommissionRate',
        width: 100,
        align: 'right',
        render: (_, record) => {
          if (['SECTION_ORDER', 'EXTRA_REWARD_BILL_ORDER'].includes(record?.bizBillTypeEnum))
            return '-';

          if (Const.SYS_TYPE === SYS_TYPE.TALENT)
            return dealDecimalToPercent(Number(record.gmvTalentCommissionRate), 2) || '-';
          else if (Const.SYS_TYPE === SYS_TYPE.ORGANIZATION)
            return (
              dealDecimalToPercent(
                Number(record.gmvTalentCommissionRate) +
                  Number(record.gmvInstitutionCommissionRate),
                2,
              ) || '-'
            );
          else
            return (
              dealDecimalToPercent(
                Number(record?.gmvTalentCommissionRate) +
                  Number(record?.gmvInstitutionCommissionRate) +
                  Number(record?.gmvPlatformCommissionRate),
                2,
              ) || '-'
            );
        },
      },
      {
        title: '付款金额',
        key: 'gmvInstitutionCommissionFees',
        align: 'right',
        render: (_, record) => {
          if (Const.SYS_TYPE === SYS_TYPE.TALENT)
            return `¥ ${Number(record.gmvTalentCommissionFees).toFixed(2)}`;
          else if (Const.SYS_TYPE === SYS_TYPE.ORGANIZATION || Const.SYS_TYPE === SYS_TYPE.JGPY)
            return `¥ ${(
              Number(record.gmvInstitutionCommissionFees) + Number(record.gmvTalentCommissionFees)
            ).toFixed(2)}`;
          else if ([SYS_TYPE.SUPPLIER, SYS_TYPE.OPERATE].includes(Const.SYS_TYPE))
            return `¥ ${Number(record?.gmvCommissionFees).toFixed(2)}`;
        },
        width: 110,
      },
      {
        title: '创建时间',
        key: 'gmtCreated',
        dataIndex: 'gmtCreated',
        render: (t: any) =>
          isNullOrUndefined(t) ? '-' : moment(t).format(Const.TIME_FORMAT).toString(),
        width: 150,
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: (t: any, record) => {
          const flagColor =
            typeof isPay === 'boolean'
              ? !isPay
                ? '#597EF7'
                : 'lightgreen'
              : record.status === CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT
              ? '#F05959'
              : record.status === CooperationFrameworkBillStatusEnum.IN_PROCESS
              ? '#597EF7'
              : '#0DBC59';
          return (
            <>
              <span
                className={styles.columnStatus}
                style={{
                  backgroundColor: flagColor,
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  display: 'inline-block',
                  marginRight: 8,
                  marginBottom: 1,
                }}
              />
              <span className={styles.state}>
                {typeof isPay === 'boolean'
                  ? !isPay
                    ? '未付款'
                    : '已付款'
                  : record.status === CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT
                  ? '已驳回'
                  : record.status === CooperationFrameworkBillStatusEnum.IN_PROCESS
                  ? '待提交'
                  : '已关闭'}
              </span>
              {record?.rejectReason &&
                [
                  CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT,
                  CooperationFrameworkBillStatusEnum.CLOSE,
                ].includes(record.status) && (
                  <Tooltip overlayClassName="titleTooltip" title={record?.rejectReason || '-'}>
                    <Icon type="question-circle" className="ml-4" />
                  </Tooltip>
                )}
            </>
          );
        },
        width: 90,
      },
      {
        title:
          typeof isPay === 'boolean'
            ? '付款时间'
            : status === CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT
            ? '驳回时间'
            : '关闭时间',
        key:
          typeof isPay === 'boolean'
            ? !!isPay
              ? 'payTime'
              : 'expectedPayTime'
            : status === CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT
            ? 'rejectTime'
            : 'closeTime',
        dataIndex:
          typeof isPay === 'boolean'
            ? 'payTime'
            : status === CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT
            ? 'rejectTime'
            : 'closeTime',
        render: (t: any, record) => {
          if (isNullOrUndefined(t)) return '-';
          return moment(t).format(Const.TIME_FORMAT).toString();
        },
        width: 250,
      },
      {
        title: '操作',
        width: 200,
        fixed: 'right',
        key: 'id',
        render: (_, record) => {
          return (
            <div
              className={'flex'}
              style={{ alignItems: 'center', height: '19px', overflow: 'hidden' }}
            >
              {/* 下载账单 */}
              {/* <DownLoadBillNode
                record={record}
                isLiveRoundFrameworkBill={
                  record.bizBillTypeEnum === 'LIVE_ROUND_FRAMEWORK_BILL_ORDER'
                }
                requestBody={{ billId: record.bizId, liveId: record.liveId }}
                className="mr-10"
              /> */}
              {/* 关联订单 */}
              {!!record?.coopOrderNos?.length && (
                <OneAuthWrapper
                  functionName={
                    'f_sp_frame_coop_bill_orderlink,f_op_frame_coop_bill_orderlink,f_jigou_frame_coop_bill_orderlink'
                  }
                >
                  <Popover
                    title="关联订单"
                    content={
                      <div>
                        {record?.coopOrderNos?.map((_: number) => (
                          <p
                            key={_}
                            style={{ cursor: 'pointer' }}
                            onClick={() => {
                              history.push(
                                `${
                                  (record?.bizBillTypeEnum === BIZ_ORDER_TYPE.SECTION_ORDER
                                    ? sectionDetailPath
                                    : copDetailPath)[Const.SYS_TYPE as PathKey]
                                }${_}`,
                              );
                            }}
                          >{`${
                            record?.bizBillTypeEnum === BIZ_ORDER_TYPE.SECTION_ORDER
                              ? '切片订单'
                              : '合作订单'
                          }：${_}`}</p>
                        ))}
                      </div>
                    }
                  >
                    <a className="mr-8">关联订单</a>
                  </Popover>
                </OneAuthWrapper>
              )}
              {/**
               * @TODO: 平台端和商家端在不同状态下列表显示不同按钮
               * 待提交：   平台端：提交，下载账单。      商家端：-
               * 待付款：   平台端：关闭，下载账单。      商家端：付款，驳回，下载账单
               * 已付款：   平台端：下载账单。           商家端：下载账单
               * 商家驳回： 平台端：关闭，下载账单。       商家端：继续付款，下载账单
               * 已关闭：   平台端：下载账单。           商家端： 下载账单
               */}

              {/* 提交 需要二次确认提示框 f_tb_submit*/}
              {/* GMV 和 @TODO:推广平台是淘宝 */}
              {(Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) &&
                billType === 'SUBMIT' &&
                [1, 2].includes(record.modelType) &&
                record.platform === 'TAOBAO' && (
                  <AuthWrapper functionName={'f_tb_submit'}>
                    <a
                      type="link"
                      style={{ marginRight: '6px' }}
                      onClick={() => handleSubmitBtn('GMV', record)}
                    >
                      提交
                    </a>
                    {/* <Popconfirm
                      title="提交后账单将生效，商家会看到账单，确认要提交吗？"
                      onConfirm={() => handleSubmit('GMV', record)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <a type="link">提交</a>
                    </Popconfirm> */}
                  </AuthWrapper>
                )}
              {/* 场次模式 */}
              {(Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) &&
                billType === 'SUBMIT' &&
                record.platform === 'TAOBAO' &&
                record.bizBillTypeEnum === 'LIVE_ROUND_FRAMEWORK_BILL_ORDER' && (
                  <AuthWrapper functionName={'f_tb_submit'}>
                    {/* handleSubmitBtn */}
                    <a
                      type="link"
                      style={{ marginRight: '6px' }}
                      onClick={() => handleSubmitBtn('base', record)}
                    >
                      提交
                    </a>
                    {/* <Popconfirm
                      title="提交后账单将生效，商家会看到账单，确认要提交吗？"
                      onConfirm={() => handleSubmit('base', record)}
                      okText="确定"
                      cancelText="取消"
                    ></Popconfirm> */}
                  </AuthWrapper>
                )}
              {/* 关闭/关闭账单 GMV  */}
              {(Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) &&
                ['UNPAY', 'REJECT'].includes(billType) &&
                [1, 2].includes(record.modelType) &&
                record.platform === 'TAOBAO' &&
                ![
                  PaymentStatus['已付款：基础服务费'],
                  PaymentStatus['已结算：基础服务费'],
                  PaymentStatus['部分付款'],
                ].includes(Number(record.paymentStatus)) && (
                  <AuthWrapper functionName={'f_tb_coop_bill_cancel'}>
                    <a
                      style={{ margin: '0 6px 0 0' }}
                      onClick={() =>
                        closeBill({
                          billId: record.bizId,
                          isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
                        })
                      }
                    >
                      关闭账单
                    </a>
                    {/* <Popconfirm
                      title="账单关闭后不可再恢复，请确认要关闭该账单吗？"
                      onConfirm={() => {
                        cooperationFrameworkBillClose({
                          billId: record.bizId,
                          isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
                        }).then(({ res }) => {
                          if (!res?.success) {
                            message.error(res?.message || '关闭账单失败');
                            return;
                          }
                          message.success('关闭账单成功');
                          refresh();
                        });
                      }}
                      okText="确定"
                      cancelText="取消"
                    >
                      
                    </Popconfirm> */}
                  </AuthWrapper>
                )}
              {
                // 平台端 未付款 && 未驳回则显示驳回按钮  关闭账单  场次模式
                (Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) &&
                  ['UNPAY', 'REJECT'].includes(billType) &&
                  ['LIVE_ROUND_FRAMEWORK_BILL_ORDER'].includes(record.bizBillTypeEnum) &&
                  record.platform === 'TAOBAO' &&
                  ![
                    PaymentStatus['已付款：基础服务费'],
                    PaymentStatus['已结算：基础服务费'],
                    PaymentStatus['部分付款'],
                  ].includes(Number(record.paymentStatus)) && (
                    <AuthWrapper functionName={'f_tb_coop_bill_cancel'}>
                      <a
                        style={{ margin: '0 6px 0 0' }}
                        onClick={() =>
                          closeBill({
                            billId: record.bizId,
                            isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
                          })
                        }
                      >
                        关闭账单
                      </a>
                      {/* <Popconfirm
                        title="账单关闭后不可再恢复，请确认要关闭该账单吗？"
                        onConfirm={() => {
                          liveRoundFrameworkBillClose({
                            billId: record.bizId,
                            isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
                          }).then(({ res }) => {
                            if (!res?.success) {
                              message.error(res?.message || '关闭账单失败');
                              return;
                            }
                            message.success('关闭账单成功');
                            refresh();
                          });
                        }}
                        okText="确定"
                        cancelText="取消"
                      >
                        
                      </Popconfirm> */}
                    </AuthWrapper>
                  )
              }
              {(Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) &&
                ['UNPAY', 'REJECT'].includes(billType) &&
                [1, 2].includes(record.modelType) &&
                record.platform === 'DY' &&
                record.status === CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT &&
                ![
                  PaymentStatus['已付款：基础服务费'],
                  PaymentStatus['已结算：基础服务费'],
                  PaymentStatus['部分付款'],
                ].includes(Number(record.paymentStatus)) && (
                  <AuthWrapper functionName={'f_tb_coop_bill_cancel'}>
                    <a
                      style={{ margin: '0 6px 0px 0' }}
                      onClick={() =>
                        closeBill({
                          billId: record.bizId,
                          isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
                        })
                      }
                    >
                      关闭账单
                    </a>
                    {/* <Popconfirm
                      title="账单关闭后不可再恢复，请确认要关闭该账单吗？"
                      onConfirm={() => {
                        cooperationFrameworkBillClose({
                          billId: record.bizId,
                          isBefriends: Const.SYS_TYPE === SYS_TYPE.JGPY ? true : undefined,
                        }).then(({ res }) => {
                          if (!res?.success) {
                            message.error(res?.message || '关闭账单失败');
                            return;
                          }
                          message.success('关闭账单成功');
                          refresh();
                        });
                      }}
                      okText="确定"
                      cancelText="取消"
                    >
                      
                    </Popconfirm> */}
                  </AuthWrapper>
                )}
              {
                // 商家端 未付款 && 未驳回则显示驳回按钮  关闭账单  场次模式
                (Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) &&
                  ['LIVE_ROUND_FRAMEWORK_BILL_ORDER'].includes(record.bizBillTypeEnum) &&
                  record.platform === 'DY' &&
                  ![
                    PaymentStatus['已付款：基础服务费'],
                    PaymentStatus['已结算：基础服务费'],
                    PaymentStatus['部分付款'],
                  ].includes(Number(record.paymentStatus)) &&
                  record.status === CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT && (
                    <AuthWrapper functionName={'f_op_frame_coop_bill_cancel'}>
                      <a
                        style={{ margin: '0 6px 0px 0px' }}
                        onClick={() => closeBill({ billId: record.bizId })}
                      >
                        关闭账单
                      </a>
                      {/* <Popconfirm
                        title="账单关闭后不可再恢复，请确认要关闭该账单吗？"
                        onConfirm={() => {
                          liveRoundFrameworkBillClose({ billId: record.bizId }).then(({ res }) => {
                            if (!res?.success) {
                              message.error(res?.message || '关闭账单失败');
                              return;
                            }
                            message.success('关闭账单成功');
                            refresh();
                          });
                        }}
                        okText="确定"
                        cancelText="取消"
                      >
                        
                      </Popconfirm> */}
                    </AuthWrapper>
                  )
              }
              {/* 下载账单 只有在商家端待提交状态的时候不显示 */}
              {!(
                Const.SYS_TYPE === SYS_TYPE.SUPPLIER &&
                billType === 'SUBMIT' &&
                record.platform === 'TAOBAO'
              ) && (
                <DownLoadBillNode
                  record={record}
                  isLiveRoundFrameworkBill={
                    record.bizBillTypeEnum === 'LIVE_ROUND_FRAMEWORK_BILL_ORDER'
                  }
                  requestBody={{ billId: record.bizId, liveId: record.liveId }}
                />
              )}
            </div>
          );
        },
      },
    ];

    const SudoBtn = () => {
      // const onInitLive = (value: string) => {
      //   return initLiveRoundFrameworkBillApi({ coopOrderNo: value })
      //     .then(({ res }) => {
      //       checkResponse(res);
      //       message.success('操作成功');
      //     })
      //     .catch((err) => {
      //       message.error(err.message);
      //       return Promise.reject();
      //     });
      // };
      return (
        <AuthWrapper functionName="f_op_frame_coop_sudo">
          <SudoInputBtn className="ml-8" text={'年框订单'} />
          <SudoInputBtn className="ml-8" text={'普通订单'} />
          {/* <SudoInputBtn className="ml-8" text={'补场次账单'} onSuccess={onInitLive} /> */}
        </AuthWrapper>
      );
    };

    const onSearchTable = () => {
      onSearch({ current: 1 });
    };
    // const [tableHeight, setTableHeight] = useState(500);
    // const getHeight = () => {
    //   setTableHeight(getTableHeight(75));
    // };
    // useEffect(() => {
    //   setTimeout(() => {
    //     getHeight();
    //   }, 0);
    // }, []);
    const { getHeight, tableHeight } = useTableHeight(65);
    return (
      // <AuthWrapper functionName="f_jg_settle_manage_list">
      <div className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}>
        <Spin spinning={loading}>
          {/* 待付款和已付款状态时展示金额 */}
          {/* {['UNPAY', 'PAYED'].includes(billType) && (
            <div className={styles.cacuTotal}>
              <div className={styles.content}>
                <div className={styles.txt}>{`累计${isPay ? '已' : '待'}付款`}</div>
                <div className={styles.fee}>
                  {`¥ ${commisionFee || '-'}`}
                  {!isPay && Const.SYS_TYPE === SYS_TYPE.SUPPLIER && (
                    <Button
                      style={{ marginLeft: '15px', marginBottom: 4 }}
                      onClick={() => onPayTotal([])}
                    >
                      全部付款
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )} */}
          <div className="formHeight">
            <SearchForm
              onSearch={onSearchTable}
              loading={loading}
              onReset={onReset}
              form={form}
              from={Const.SYS_TYPE}
              isPay={isPay}
              status={status}
              handleBillingStatusChange={handleBillingStatusChange}
              getTableHeight={getHeight}
            />
            <div className={`mb-16 flex ${styles.btnGroup}`}>
              <div>
                {needCreateRebate && (
                  <RebateBillModal destroyOnClose={false}>
                    <Button
                      icon="plus"
                      type="primary"
                      className="mr-8"
                      access="f_op_frame_coop_rebate_bill_create"
                    >
                      新建账单
                    </Button>
                  </RebateBillModal>
                )}
                {needBatchExport && (
                  <ExportModal
                    condition={params}
                    requestFunc={
                      Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY
                        ? batchExportApiForOp
                        : batchExportApiForJg
                    }
                    content="导出范围为所筛选的结果中 类型为GMV模式-按主体、GMV模式-按品牌、场次模式，导出完成后可在导出记录中查看并下载"
                  >
                    <Button
                      className="mr-8"
                      access="f_op_frame_coop_batchExport,f_jg_frame_coop_batchExport"
                      style={{ borderColor: '#999999', color: '#444444' }}
                    >
                      <img
                        src={download}
                        style={{
                          width: '14px',
                          height: '14px',
                          verticalAlign: 'sub',
                          marginRight: '6px',
                        }}
                      />
                      批量导出查询账单
                    </Button>
                  </ExportModal>
                )}
              </div>
              {/**
               *  @TODO: 新增淘宝账单按钮
               * 1. 所有状态都显示
               * 2. 只有在平台端显示两个按钮
               * 3. 权限key f_tb_import_bill
               * 4. 现在新增交个朋友端也显示 */}
              {/* 新增按钮开始 */}
              {(Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) && (
                <>
                  <ImportBillModal>
                    <Button
                      className="mr-8"
                      access="f_tb_import_bill"
                      style={{ borderColor: '#999999', color: '#444444' }}
                    >
                      导入淘宝账单
                    </Button>
                  </ImportBillModal>
                  <Button
                    className="mr-8"
                    onClick={() => history.push(`/tb-bill-record`)}
                    access="f_tb_import_bill_record"
                    style={{ borderColor: '#999999', color: '#444444' }}
                    // access="f_cooperation_order_frame_bill_download_List,f_op_frame_coop_batchExport,f_jg_frame_coop_batchExport"
                  >
                    淘宝账单导入记录
                  </Button>
                </>
              )}
              {/* 新增按钮结束 */}
              <Button
                onClick={() =>
                  history.push(`/billExport-list?configCode=COOPERATION_FRAMEWORK_BILL`)
                }
                access="f_cooperation_order_frame_bill_download_List,f_op_frame_coop_batchExport,f_jg_frame_coop_batchExport"
                style={{ borderColor: '#999999', color: '#444444' }}
              >
                账单导出记录
              </Button>
              {(Const.SYS_TYPE === SYS_TYPE.OPERATE || Const.SYS_TYPE === SYS_TYPE.JGPY) && (
                <SudoBtn />
              )}
            </div>{' '}
          </div>
          <div style={{ flex: 1 }}>
            <Table
              columns={columns}
              dataSource={datasource}
              pagination={false}
              rowKey={(record) => `${record.id}`}
              rowClassName={(record, i) => (i % 2 === 1 ? styles.even : styles.odd)} // 重点是这个api
              scroll={{ y: tableHeight, x: '100%' }}
            />
          </div>
          <div className={styles['pagination-box'] + ' pageHeight'}>
            <PaginationProxy
              {...pagination}
              onChange={setPagination}
              valueType="merge"
              leftNode={
                <div className={styles['totalPay']}>
                  <div className={styles['totalPayName']}>累计已付款</div>
                  <div className={styles['totalPayMoney']}>{`¥ ${commisionFee || '-'}`}</div>
                </div>
              }
            />
          </div>
        </Spin>
        <EbPay
          allPay={allPay}
          coopFrameworkId={orderNo}
          needWarning={false}
          toRecharge={() => history.push('/business-cooperation/index')}
          payType={getPayType()}
          onPaySuccess={() => {
            // 关弹窗
            setPayVisable(false);
            // 刷列表
            onPayok();
          }}
          totalpay={initTotalPay}
          renderPaySuccessContent={() => {
            return <div>你可以前往合作订单详情页查看 </div>;
          }}
          setVisible={setPayVisable}
          handleCancel={() => {
            onPayCancel();
          }}
          onPayFail={() => setPayVisable(false)}
          visible={payVisable}
          batchPay={true}
          ebpayType={EBPAY_TYPE.SURPLUS}
        />
      </div>
      // </AuthWrapper>
    );
  },
);

export default Form.create<IProps>()(CooperationBill);
