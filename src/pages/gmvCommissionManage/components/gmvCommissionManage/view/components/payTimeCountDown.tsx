import React, { useRef, useState, useEffect } from 'react';
import moment from 'moment';
import { OSSImagePre } from '@/common/constants/moduleConstant';
import { display } from 'html2canvas/dist/types/css/property-descriptors/display';

interface Iprops {
  start: string | number;
  onFinish: Function;
}
const formatWith2Num = (val) => {
  if (!val) val = 0;
  return `00${val}`.slice(-2);
};
const formatCountDown = (diff) => {
  const time = moment.duration(diff, 'seconds');
  const days = time.days() || 0;
  let hours = time.hours();
  let minutes = time.minutes();
  let seconds = time.seconds();
  hours = formatWith2Num(hours);
  minutes = formatWith2Num(minutes);
  seconds = formatWith2Num(seconds);
  return `${days}天${hours}时${minutes}分${seconds}秒`;
};

const PayTimeCountDown: React.FC<Iprops> = ({ start, onFinish }) => {
  const [timeRemaining, setTimeRemaining] = useState('');
  const intervalRef = useRef();

  const startCountdown = () => {
    const diff = moment(start).diff(moment(), 'seconds');
    if (diff <= 0) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      onFinish && onFinish();
      return;
    }

    const val = formatCountDown(diff);
    setTimeRemaining(val);
  };

  useEffect(() => {
    const diff = moment(start + 3600).diff(moment(), 'seconds');
    if (diff < 0) {
      setTimeRemaining('');
      return;
    }
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    intervalRef.current = setInterval(startCountdown, 1000);
    return () => {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    };
  }, [start]);
  if (!timeRemaining) return '-';
  return (
    <p style={{ display: 'flex', alignItems: 'center' }}>
      {' '}
      <img src={`${OSSImagePre}/icon/clock.png`} style={{ width: 14, marginRight: 4 }}></img>剩余
      <span style={{ color: '#ccb432', margin: '0 2px' }}>{timeRemaining}</span>自动确认
    </p>
  );
};

export default PayTimeCountDown;
