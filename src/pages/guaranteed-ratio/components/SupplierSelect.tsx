import React, { useEffect, useState } from 'react';

import { Select } from 'antd';

import { getSupplier, GetSupplierResult } from '@/services/yml/goods-assorting/index';
const { Option } = Select;
const SupplierSelect: React.FC<{ value?: string; onChange: (value: string) => void }> = ({
  value,
  onChange,
}) => {
  const [dataList, setDataList] = useState<GetSupplierResult>([]); //直播间列表
  const getList = (name: string) => {
    const params = {
      size: 10,
      current: 1,
      name,
    };
    getSupplier({ ...params }).then((res) => {
      // console.log(res);
      if (res?.res?.code === '200') {
        const resData = res?.res?.result;
        resData?.length > 0 ? setDataList([...resData]) : setDataList([]);
      } else {
        setDataList([]);
      }
    });
  };
  useEffect(() => {
    getList('');
  }, []);

  return (
    <Select
      value={value}
      allowClear
      showSearch
      onSearch={getList}
      onChange={onChange}
      placeholder="请选择"
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      notFoundContent={null}
    >
      {dataList?.map((i, index) => {
        return (
          <Option value={i?.supplierCompanyCode} key={index}>
            {i?.supplierCompanyCode}
          </Option>
        );
      })}
    </Select>
  );
};
export default SupplierSelect;
