import { useRequest } from 'ahooks';
import { Button, Form, message, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { AuthWrapper, Const, history } from 'qmkit';
import React, { useCallback, useEffect, useState } from 'react';
import { LayoutContentItem, TableProxy } from 'web-common-modules/components';
import PaginationProxy from '@/common/constants/Pagination';
import { handlePrecision, ADD_EDIT_DETAIL_UNION } from '@/common/common';

import { usePagination } from 'web-common-modules/hooks';
import styles from '../index.module.less';
import { getCoopListApi } from '@/services/yml/business-cooperation';
import { useTableHeight } from '@/common/constants/hooks/index';
import CargoSearchForm from '../components/CargoSearchForm';
import { getColumnsCargo } from '../utils/getColumns';
import { isNullOrUndefined } from 'web-common-modules/utils/type';
import { filterInvalid } from '@/utils/moduleUtils';
import { queryCargoRateList, QueryCargoRateListResult } from '../services/index';
import moment from 'moment';
import { platformEnum } from '@/pages/goods-assorting/constant';

const CargoRate: React.FC<FormComponentProps> = ({ form }) => {
  const [datasource, setDatasource] = useState<QueryCargoRateListResult[]>([]);
  const { pagination, setPagination, initSize, resetPagination } = usePagination({ size: 20 });

  const { loading: getListLoading, run: getListRun } = useRequest(queryCargoRateList, {
    manual: true,
    onSuccess: ({ res }) => {
      console.log('res,res', res);
      if (res?.success) {
        const { total, records, current, size } = res?.result || {};
        const arr = records?.map((item, i) => ({ ...item, index: i + 1 }));
        // arr?.forEach((i, index) => {
        //   i.index = index + 1;
        // });
        setDatasource(arr || []);
        setPagination({
          total: total || 0,
        });
        return;
      }
      message.error(res?.message || Const.ERR_MESSAGE);
    },
  });

  const getList = useCallback(
    (condition?) => {
      const { ...rest } = condition || {};
      const { current, size } = pagination;
      const { ...values } = form.getFieldsValue() || {};
      console.log('values', values);
      const params = {
        current,
        size,
        liveDate: values?.liveDate ? moment(values?.liveDate).format('YYYY-MM-DD') : undefined,
        liveRoomIds: values?.liveRoomIds ? [values?.liveRoomIds] : undefined,
        cateIds: values?.cateIds && values?.cateIds?.length ? [values?.cateIds[1]] : undefined,
        platformSource: values?.platformSource,
        deptId: values?.deptId,
        ...rest,
      };

      getListRun(filterInvalid(params));
    },
    [pagination, form],
  );

  useEffect(() => {
    setTimeout(() => {
      getList();
    }, 300);
  }, []);

  const onReset = useCallback(() => {
    const deptId = form.getFieldsValue().deptId;
    form.resetFields();
    form.setFieldsValue({ deptId: deptId });
    resetPagination();
    getList({
      current: 1,
      size: initSize,
    });
  }, [form, getList, resetPagination]);
  const { getHeight, tableHeight } = useTableHeight(125);

  return (
    // <LayoutContentItem>
    <div
      className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
      style={{ height: 'calc(100vh - 90px)', display: 'flex', flexDirection: 'column' }}
    >
      <div className="formHeight">
        <CargoSearchForm
          form={form}
          loading={getListLoading}
          onSearch={() => {
            getList({ current: 1 });
          }}
          onReset={onReset}
        />

        {/* <div className="flex items-center mb-16">
          <AuthWrapper functionName="f_choice_list_board_export">
            <Button
              style={{ marginRight: '8px' }}
              onClick={() => {
                exportList();
              }}
            >
              <span className="iconfont icon-daochu"></span>
              导出
            </Button>
          </AuthWrapper>
          <AuthWrapper functionName="f_choice_list_board_export_log">
            <Button
              onClick={() => {
                goExportList('BUSINESS_SUBMIT_RATE_EXPORT_CODE');
              }}
            >
              导出记录
            </Button>
          </AuthWrapper>
        </div> */}
      </div>
      <div className={styles.boardTable} style={{ flex: 1 }}>
        <Table
          rowKey="id"
          loading={getListLoading}
          columns={[...getColumnsCargo()]}
          dataSource={datasource}
          scroll={{ y: tableHeight, x: '100%' }}
          pagination={false}
        />
      </div>
      <div className={styles['pagination-box'] + ' pageHeight'}>
        <PaginationProxy
          current={pagination?.current}
          pageSize={pagination?.size}
          total={pagination?.total}
          onChange={(current, size) => {
            setPagination({
              current,
              size,
            });
            getList({
              current,
              size,
            });
          }}
          valueType="flatten"
        />
      </div>
    </div>
    // </LayoutContentItem>
  );
};

export default Form.create()(CargoRate);
