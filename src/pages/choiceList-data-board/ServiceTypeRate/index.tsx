import { useRequest } from 'ahooks';
import { Button, Form, message, Table } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { Const } from 'qmkit';
import React, { useCallback, useEffect, useState } from 'react';

import PaginationProxy from '@/common/constants/Pagination';

import usePagination from '@/hooks/usePagination';
import styles from '../index.module.less';

import { useTableHeight } from '@/common/constants/hooks/index';
import ServiceSearchForm from '../components/ServiceSearchForm';
import { getColumnsService } from '../utils/getColumns';

import { filterInvalid } from '@/utils/moduleUtils';
import moment from 'moment';
import {
  queryServiceRateList,
  QueryServiceRateListResult,
  QueryServiceRateListRequest,
} from '../services/index';

const ServiceTypeRate: React.FC<FormComponentProps> = ({ form }) => {
  const [datasource, setDatasource] = useState<QueryServiceRateListResult[]>([]);
  const { pagination, setPagination, initSize, resetPagination } = usePagination({ size: 20 });

  const { loading: getListLoading, run: getListRun } = useRequest(queryServiceRateList, {
    manual: true,
    onSuccess: ({ res }) => {
      if (res?.success) {
        const { total, records, current, size } = res?.result || {};
        const arr = records?.map((item, i) => ({ ...item, index: i + 1 }));
        // arr?.forEach((i, index) => {
        //   i.index = index + 1;
        // });
        setDatasource(arr || []);
        setPagination({
          total: total || 0,
        });
        return;
      }
      message.error(res?.message || Const.ERR_MESSAGE);
    },
  });

  const getList = useCallback(
    (condition?) => {
      const { ...rest } = condition || {};
      const { current, size } = pagination;
      const { ...values } = form.getFieldsValue() || {};
      const params: QueryServiceRateListRequest = {
        current,
        size,
        deptId: values?.deptId,
        liveDate: values?.liveDate ? moment(values?.liveDate).format('YYYY-MM-DD') : undefined,
        liveRoomIds: values?.liveRoomIds ? [values?.liveRoomIds] : undefined,
        liveServiceTypeIds: values?.liveServiceTypeIds || undefined,
        platformSource: values?.platformSource,
        ...rest,
      };

      getListRun(filterInvalid(params));
    },
    [pagination, form],
  );

  useEffect(() => {
    setTimeout(() => {
      getList();
    }, 300);
  }, []);

  const onReset = useCallback(() => {
    const deptId = form.getFieldsValue().deptId;
    form.resetFields();
    form.setFieldsValue({ deptId: deptId });
    resetPagination();
    getList({
      current: 1,
      size: initSize,
    });
  }, [form, getList, resetPagination]);
  const { getHeight, tableHeight } = useTableHeight(125);

  return (
    // <LayoutContentItem>
    <div
      className={`${styles.publishFeeContainer} ${styles['publish-fee-page']}`}
      style={{ height: 'calc(100vh - 90px)', display: 'flex', flexDirection: 'column' }}
    >
      <div className="formHeight">
        <ServiceSearchForm
          form={form}
          loading={getListLoading}
          onSearch={() => {
            getList({ current: 1 });
          }}
          onReset={onReset}
        />
      </div>
      <div className={styles.boardTable} style={{ flex: 1 }}>
        <Table
          rowKey="id"
          loading={getListLoading}
          columns={[...getColumnsService()]}
          dataSource={datasource}
          scroll={{ y: tableHeight, x: '100%' }}
          pagination={false}
        />
      </div>
      <div className={styles['pagination-box'] + ' pageHeight'}>
        <PaginationProxy
          current={pagination?.current}
          pageSize={pagination?.size}
          total={pagination?.total}
          onChange={(current, size) => {
            setPagination({
              current,
              size,
            });
            getList({
              current,
              size,
            });
          }}
          valueType="flatten"
        />
      </div>
    </div>
    // </LayoutContentItem>
  );
};

export default Form.create()(ServiceTypeRate);
