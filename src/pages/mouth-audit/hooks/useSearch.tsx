import React from 'react';
import BatchInput from '@/components/BatchInput';
import { Select, Input, DatePicker, InputNumber, Cascader } from 'antd';
import { CHECK_LIST, CLAIM_STATUS_LIST } from '../types';
import ChangePersonOption from 'web-common-modules/biz/user/ChargePersonOption';
import { useSearchRoomList } from '../hooks';
import { useDeptList } from '@/pages/goods-assorting/hooks/index';

export const useSearch = () => {
  const { liveList, liveAllRoomLoading, handleSearch, handleChane, liveAllRoomRun } =
    useSearchRoomList();
  const { deptList } = useDeptList();
  const options = {
    // selectionNo: {
    //   label: '选品编号',
    //   renderNode: <BatchInput clearable label="请填写选品编号" />,
    // },
    deptIdsPlatform: {
      label: '事业部-平台',
      span: 1,
      draggable: false,
      renderNode: <Cascader options={deptList} />,
    },
    status: {
      label: '审核状态',
      renderNode: (
        <Select allowClear placeholder="请选择">
          {CHECK_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    spuName: {
      label: '商品名称',
      renderNode: <Input placeholder="请输入" />,
    },

    selectGoodPoolIdList: {
      label: '选品池ID',
      renderNode: <BatchInput clearable label="选品池ID" />,
    },
    cateName: {
      label: '类目',
      renderNode: <Input placeholder="请输入" />,
    },
    creator: {
      label: '提交者',
      span: 1,
      draggable: true,
      renderNode: (
        <ChangePersonOption
          labelInValue={false}
          placeholder="请输入"
          accountState={false}
          showAccount={true}
        />
      ),
    },
    versionId: {
      label: '版本(V)',
      renderNode: <InputNumber min={1} placeholder="请输入" style={{ width: '100%' }} />,
    },
    liveRoomIdList: {
      label: '直播间',
      renderNode: (
        <Select
          loading={liveAllRoomLoading}
          placeholder="请输入直播间关键词"
          allowClear
          filterOption={false}
          onSearch={handleSearch}
          onChange={handleChane}
          showSearch
          mode="multiple"
          maxTagCount={1}
        >
          {liveList.map((item) => (
            <Select.Option key={item.id} value={item.id}>
              {item.name}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    owner: {
      label: '认领法务',
      renderNode: (
        <ChangePersonOption
          labelInValue={false}
          placeholder="请输入"
          accountState={false}
          showAccount={true}
        />
      ),
    },
    owned: {
      label: '认领状态',
      renderNode: (
        <Select allowClear placeholder="请选择">
          {CLAIM_STATUS_LIST?.map((item) => (
            <Select.Option value={item.value} key={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      ),
    },
  };
  return { options };
};
