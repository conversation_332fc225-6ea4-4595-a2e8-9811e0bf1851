export enum CHECK_TYPE {
  REVIEWING = 'WAIT_AUDIT',
  REVIEWED = 'AUDITED',
  IN_AUDIT = 'IN_AUDIT',
}

export const CHECK_NAME = {
  [CHECK_TYPE.REVIEWING]: '待审核',
  [CHECK_TYPE.REVIEWED]: '已审核',
  [CHECK_TYPE.IN_AUDIT]: '审核中',
};

export const CHECK_COLOR = {
  [CHECK_TYPE.REVIEWING]: {
    bg: '#FFFBE6',
    color: '#FAAD14',
  },
  [CHECK_TYPE.IN_AUDIT]: {
    bg: '#EDF4FF',
    color: '#204EFF',
  },
  [CHECK_TYPE.REVIEWED]: {
    bg: '#F6FFED',
    color: '#52C41A',
  },
};

export const CHECK_LIST = [
  {
    label: CHECK_NAME[CHECK_TYPE.REVIEWED],
    value: CHECK_TYPE.REVIEWED,
  },
  {
    label: CHECK_NAME[CHECK_TYPE.REVIEWING],
    value: CHECK_TYPE.REVIEWING,
  },
  {
    label: CHECK_NAME[CHECK_TYPE.IN_AUDIT],
    value: CHECK_TYPE.IN_AUDIT,
  },
];

export const AI_AUDIT_STATUS = {
  GENERATING: 'GENERATING',
  GENERATED: 'GENERATED',
  FAILED: 'FAILED',
};

export const AI_AUDIT_STATUS_NAME = {
  [AI_AUDIT_STATUS.GENERATING]: '生成中',
  [AI_AUDIT_STATUS.GENERATED]: '已生成',
  [AI_AUDIT_STATUS.FAILED]: '生成失败',
};

export const AI_AUDIT_STATUS_COLOR = {
  [AI_AUDIT_STATUS.GENERATING]: {
    bg: '#FFFBE6',
    color: '#FAAD14',
  },
  [AI_AUDIT_STATUS.GENERATED]: {
    bg: '#F6FFED',
    color: '#52C41A',
  },
  [AI_AUDIT_STATUS.FAILED]: {
    bg: '#FF4D4F',
    color: '#FFFFFF',
  },
};

//ai生成
export enum CLAIM_STATUS {
  CLAIMED = 1,
  UNCLAIMED = 0,
}

export const CLAIM_STATUS_NAME = {
  [CLAIM_STATUS.CLAIMED]: '已认领',
  [CLAIM_STATUS.UNCLAIMED]: '未认领',
};

export const CLAIM_STATUS_COLOR = {
  [CLAIM_STATUS.CLAIMED]: {
    bg: '#F6FFED',
    color: '#52C41A',
  },
  [CLAIM_STATUS.UNCLAIMED]: {
    bg: '#FFF1F0',
    color: '#F5222D',
  },
};

export const CLAIM_STATUS_LIST = [
  {
    label: CLAIM_STATUS_NAME[CLAIM_STATUS.CLAIMED],
    value: CLAIM_STATUS.CLAIMED,
  },
  {
    label: CLAIM_STATUS_NAME[CLAIM_STATUS.UNCLAIMED],
    value: CLAIM_STATUS.UNCLAIMED,
  },
];
// 2024-07-14zhouby -> cursor ai结尾共生成21行代码
