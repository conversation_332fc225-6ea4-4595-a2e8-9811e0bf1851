import React from 'react';
import { Button } from 'antd';
import { ColumnProps } from 'antd/es/table';
import moment from 'moment';
import { AuthWrapper } from 'qmkit';
import { LiveRoundIntervalTargetPageRequest, LiveRoundIntervalTargetPageResult } from './services';
import EditModal from './components/EditModal';
import { toDecimal } from '@/utils/string';
import PopoverRowText from '@/components/PopoverRowText/index';
import { history } from 'qmkit';
export type SessionTargetsListInfoType = NonNullable<
  LiveRoundIntervalTargetPageResult['records']
>[number];
export type EmployeeListType = { employeeId?: string; employeeName?: string }[];
export const getColumns = ({
  refresh,
}: {
  refresh: (params: LiveRoundIntervalTargetPageRequest) => void;
}) =>
  [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_, record, index) => {
        return <div>{index + 1}</div>;
      },
    },
    {
      title: '直播间',
      dataIndex: 'liveRoomName',
      key: 'liveRoomName',
      width: 180,
      render: (liveRoomName: string) => {
        return (
          <p>
            <PopoverRowText text={liveRoomName}></PopoverRowText>
          </p>
        );
      },
    },
    {
      title: '直播日期',
      dataIndex: 'liveDate',
      key: 'liveDate',
      width: 100,
      render: (_, records) => {
        return <p> {records?.liveDate ? moment(records.liveDate).format('YYYY-MM-DD') : '-'}</p>;
      },
    },
    {
      title: '项目组',
      dataIndex: 'groupName',
      key: 'groupName',
      width: 150,
    },
    {
      title: '场控',
      dataIndex: 'fieldControlEntity',
      key: 'fieldControlEntity',
      width: 150,
      render: (fieldControlEntity: EmployeeListType) =>
        fieldControlEntity?.length ? (
          <p>
            <PopoverRowText
              text={fieldControlEntity?.map((item) => item?.employeeName)?.join(',')}
            ></PopoverRowText>
          </p>
        ) : (
          '-'
        ),
    },
    {
      title: '商务',
      dataIndex: 'bpEntity',
      key: 'bpEntity',
      width: 150,
      render: (bpEntity: EmployeeListType) =>
        bpEntity?.length ? (
          <p>
            <PopoverRowText
              text={bpEntity?.map((item) => item?.employeeName)?.join(',')}
            ></PopoverRowText>
          </p>
        ) : (
          '-'
        ),
    },
    {
      title: '时间段',
      dataIndex: 'timePeriod',
      key: 'timePeriod',
      width: 150,
    },
    {
      title: '目标GMV（万元）',
      dataIndex: 'gmvGoal',
      key: 'gmvGoal',
      width: 150,
      render: (_, records) =>
        records?.gmvGoal === null ? '-' : toDecimal(Number(records?.gmvGoal) / 10000, 2),
    },
    {
      title: '目标佣金（万元）',
      dataIndex: 'commissionGoal',
      key: 'commissionGoal',
      width: 150,
      render: (_, records) =>
        records?.commissionGoal === null
          ? '-'
          : toDecimal(Number(records?.commissionGoal) / 10000, 2),
    },
    {
      title: '目标基础服务费（万元）',
      dataIndex: 'brandFeeGoal',
      key: 'brandFeeGoal',
      width: 150,
      render: (_, records) =>
        records?.brandFeeGoal === null ? '-' : toDecimal(Number(records?.brandFeeGoal) / 10000, 2),
    },
    {
      title: '目标毛利（万元）',
      dataIndex: 'targetGrossProfit',
      key: 'targetGrossProfit',
      width: 150,
      render: (targetGrossProfit) =>
        targetGrossProfit ? toDecimal(Number(targetGrossProfit) / 10000, 2) : '-',
    },
    {
      title: '短视频编导',
      dataIndex: 'videoClipDirectorEntity',
      key: 'videoClipDirectorEntity',
      width: 100,
      render: (videoClipDirectorEntity: EmployeeListType) =>
        videoClipDirectorEntity?.length ? (
          <PopoverRowText
            text={videoClipDirectorEntity?.map((item) => item?.employeeName)?.join(',')}
          ></PopoverRowText>
        ) : (
          '-'
        ),
    },
    {
      title: '投放人员',
      dataIndex: 'releasePersonnelEntity',
      key: 'releasePersonnelEntity',
      width: 100,
      render: (releasePersonnelEntity: EmployeeListType) =>
        releasePersonnelEntity?.length ? (
          <PopoverRowText
            text={releasePersonnelEntity?.map((item) => item?.employeeName)?.join(',')}
          ></PopoverRowText>
        ) : (
          '-'
        ),
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'creatorName',
      width: 100,
      render: (_: string) => _ || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreated',
      key: 'gmtCreated',
      width: 150,
      render: (gmtCreated: string) => (
        <p>{gmtCreated ? moment(gmtCreated).format('YYYY-MM-DD HH:mm:ss') : '-'}</p>
      ),
    },
    {
      title: '修改人',
      dataIndex: 'modifierName',
      key: 'modifierName',
      width: 100,
    },
    {
      title: '修改时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      width: 150,
      render: (gmtModified: string) => (
        <p>{gmtModified ? moment(gmtModified).format('YYYY-MM-DD HH:mm:ss') : '-'}</p>
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 100,
      fixed: 'right',
      render: (_, record, index) => (
        <div className="action-wrap">
          <AuthWrapper functionName="f_session_targets_edit">
            <a
              onClick={() => {
                history.push(`/session-targets-edit/?id=${record.id}`);
              }}
              type="primary"
            >
              编辑
            </a>
          </AuthWrapper>
        </div>
      ),
    },
  ] as ColumnProps<SessionTargetsListInfoType>[];

export const formatLiveIds = (list: any[]) => {
  const ids = new Set<string>();
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    ids.add(item?.liveRoomId as string);
  }
  // const idsArr = [...ids]
  return {
    isRes: ids.size > 1,
    list: [...ids],
  };
};
