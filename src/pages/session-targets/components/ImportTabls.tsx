import React, { useState } from 'react';
import WithToggleModal from '@/components/WithToggleModal';
import { ModalProps } from 'antd/lib/modal';
import { Modal, Button, Form, message } from 'antd';
import style from '@/styles/index.module.less';
import { DataSourceItem } from 'web-common-modules/components/OSSUpload';
import styles from '../index.module.less';
import OSSUpload from '@/pages/gmvCommissionManage/components/gmvCommissionManage/view/components/OSSUpload';
import { liveRoundIntervalTargetImportTargets } from '../services';
import { useRequest } from 'ahooks';

const formItemLayout = {
  labelCol: {
    span: 2,
    offset: 0,
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    span: 24,
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
interface IProps extends ModalProps {
  [key: string]: any;
}

const ImportTable: React.FC<IProps> = (props) => {
  const { visible, onCancel, form, setVisble, setImportResult, ...rest } = props;
  const [fileList, setFileList] = useState<Partial<DataSourceItem>[]>([]);
  const [uploadLoading, setUploadLoading] = useState<boolean>(false);
  const { getFieldDecorator } = form;
  const { run, loading } = useRequest(liveRoundIntervalTargetImportTargets, {
    manual: true,
    onSuccess({ res }) {
      if (res?.success) {
        message.success('导入成功');
        cancel();
        setVisble?.(true);
        setImportResult({
          loading: false,
          result: res?.result || null,
        });
      } else {
        message.warning(res?.message || '网络异常');
      }
    },
  });
  const cancel = (e?: any) => {
    form.resetFields();
    setFileList([]);
    onCancel && onCancel(e);
  };
  const downTemplate = () => {
    window.open(
      '//befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/import/iasm/%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%8C%BA%E9%97%B4%E7%9B%AE%E6%A0%87%E6%A8%A1%E6%9D%BF%20(7)%20.xlsx',
    );
  };
  const onConfirm = () => {
    form.validateFields((err: any, values: any) => {
      if (!!err) return;
      setImportResult({
        loading: true,
      });
      const [item] = fileList;
      const { resourceId } = item;
      run({ resourceId: resourceId as string });
    });
  };
  return (
    <Modal
      className={style['modal-style']}
      title="导入区间目标"
      maskClosable={false}
      width={500}
      visible={visible}
      {...rest}
      footer={
        <div>
          <Button onClick={cancel}>取消</Button>
          <Button
            htmlType="submit"
            className="ml-10"
            type="primary"
            onClick={onConfirm}
            loading={loading}
          >
            导入
          </Button>
        </div>
      }
      onCancel={cancel}
    >
      <div style={{ marginBottom: '2px' }}>
        下载
        <Button type="link" style={{ padding: '0px' }} onClick={downTemplate}>
          导入模板
        </Button>
        以查看所需格式示例
      </div>
      <Form className={styles['modal-form']}>
        <Form.Item label="附件" required {...formItemLayout} style={{ marginTop: '-2px' }}>
          {getFieldDecorator('fileList', {
            rules: [
              {
                validator: (_, value, callback) => {
                  if (!value?.length || value?.length === 0) {
                    callback('请上传文件');
                    return;
                  }
                  callback();
                },
              },
            ],
          })(
            <OSSUpload
              renderUpload={() => (
                <div className={style.importLine}>
                  <Button
                    className="h-28"
                    icon="upload"
                    style={{
                      display: 'block',
                    }}
                    loading={uploadLoading}
                  >
                    上传
                  </Button>
                  <span className={style.pro}>支持xls、xlsx格式</span>
                </div>
              )}
              uploadLoading={uploadLoading}
              setLoading={(val) => setUploadLoading(val)}
              disabled={false}
              accept=".xls,.xlsx"
              typeCode="COMMON_IMPORT"
              isImage={false}
              maxSize={10 * 1024 * 1024}
              maxLen={1}
              dataSource={fileList}
              onChange={(list: DataSourceItem[]) => {
                const dealList = list
                  .map((item) => ({
                    name: item?.name,
                    resourceId: item?.resourceId,
                  }))
                  .slice(-1);
                setFileList(dealList);
              }}
            />,
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(WithToggleModal(ImportTable));
