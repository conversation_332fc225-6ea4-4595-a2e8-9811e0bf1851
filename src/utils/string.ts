import _ from 'lodash';
import { ReactChild } from 'react';
import { isInvalidValue, isNullOrUndefined } from './moduleUtils.ts';

/**
 * 千分符保留两位小数
 * @param num Number
 * @param retain 保留的位数
 * @returns {string}
 */
const toThousands = (num, retain = 2) => {
  if (!num && num != 0) return '-';
  const str = Number(num).toFixed(retain);
  const reg = str.indexOf('.') > -1 ? /(\d)(?=(\d{3})+\.)/g : /(\d)(?=(?:\d{3})+$)/g;
  return str.replace(reg, function ($0, $1) {
    return $1 + ',';
  });
};

/**
 * 保留N位小数
 * @param num Number
 * @param precision 保留的位数
 * @returns {string}
 */
const toDecimal = (num, precision = 2) => {
  if (!num && num != 0) return '-';
  if (isNaN(num)) return '-';
  return Number(num).toFixed(precision);
};

/**
 * 数字转化为字符串
 * @param number Number
 * @param max 最大值
 * @param suffix 后缀
 * @param split 是否千分位分隔
 * @param precision 小数点后几位
 * @param tenThousandPrecision {undefined | number} 转化为万之后的小数点精确到几位
 * @returns {string}
 */
const numberToString = (
  number,
  { max = 10000, suffix = 'w', precision = 1, split = false, tenThousandPrecision = 2 } = {},
) => {
  if (!number || isNaN(Number(number))) return number;

  if (number < max) return split ? toThousands(number, precision) : _.round(number, precision);

  if (number > 100000000)
    return split
      ? toThousands(
          number / 100000000,
          !isNullOrUndefined(tenThousandPrecision) ? tenThousandPrecision : precision,
        ) + '亿'
      : _.round(
          number / 100000000,
          !isNullOrUndefined(tenThousandPrecision) ? tenThousandPrecision : precision,
        ) + '亿';

  return split
    ? toThousands(
        number / max,
        !isNullOrUndefined(tenThousandPrecision) ? tenThousandPrecision : precision,
      ) + suffix
    : _.round(
        number / max,
        !isNullOrUndefined(tenThousandPrecision) ? tenThousandPrecision : precision,
      ) + suffix;
};

/**
 * 金额显示
 * @param price 金额
 * @param toThousands 是否千分位展示
 * @param suffix 后缀
 * @param defaultValue 默认值
 * @returns {string}
 */
const priceRender = (
  price,
  { prefix = '¥', defaultValue = '-', split = true, retain = 2, suffix = '' } = {},
) => {
  if (!price && price != 0) return defaultValue;
  const priceStr = split ? toThousands(price, retain) : price;
  return `${prefix}${priceStr}${suffix}`;
};

/**
 * 默认展示
 * @param value 展示的内容
 * @param defaultValue 默认值
 * @returns {string}
 */
const defaultRender = (value: any, defaultValue: ReactChild | string = '-') => {
  if (!value && value !== 0) return defaultValue;
  return value;
};

/**
 * 判断是否有值
 * @param value
 * @returns {boolean}
 */
const isEmpty = (value: any) => {
  return (
    value === null ||
    value === undefined ||
    (typeof value === 'string' && value.trim() === '') ||
    (typeof value === 'object' && Object.keys(value).length < 1)
  );
};

// 转化为文件流
const base64ToBlob = (urlData, type) => {
  const arr = urlData.split(',');
  const mime = arr[0].match(/:(.*?);/)[1] || type;
  // 去掉url的头，并转化为byte
  const bytes = window.atob(arr[1]);
  // 处理异常,将ascii码小于0的转换为大于0
  const ab = new ArrayBuffer(bytes.length);
  // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
  const ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], {
    type: mime,
  });
};

/**
 * 佣金，后端返回小数，前端处理成百分比的格式
 */
const dealDecimalToPercent = (val?: string | number, precision = 0, noSymbol?: boolean): string => {
  return !isNullOrUndefined(val)
    ? (Number(val) * 100).toFixed(precision) + (noSymbol ? '' : '%')
    : '-';
};

/**
 * 更小的价格
 * @param num1 Number
 * @param num2 Number
 * @returns Number
 */
const lowerPrice = (num1: string | number, num2: string | number) => {
  if (isNaN(Number(num1)) || isNaN(Number(num2)) || num1 === null || num2 === null) {
    return num1 || num2 || 0;
  }
  return Number(num1) < Number(num2) ? Number(num1) : Number(num2);
};

/**
 * 更小的价格展示用
 * @param num1 Number
 * @param num2 Number
 * @returns string
 */
const lowerPriceShow = (num1: string | number, num2: string | number) => {
  return toDecimal(lowerPrice(num1, num2));
};

const subtract: (a: number | string, b: number | string, precision?: number) => number = (
  a,
  b,
  precision = 2,
) => {
  return Number(Number(Number(a) - Number(b)).toFixed(precision));
};

/**
 * 四舍五入，不保留后面的无效位
 * eg：5.400 -> 5.4
 */
const toDecimalRound = (num: number | string, precision = 2) => {
  if (!num && num !== 0) return '-';
  return _.round(Number(num), precision);
};

/**
 * 折扣
 * 9.992 -> 9.9折
 */
export const toDecimalFloor = (num: number | string, precision = 1) => {
  if (!num && num !== 0) return '-';
  return _.floor(Number(num), precision);
};

/**
 * 佣金，后端返回小数，前端处理成百分比的格式(不强制小数位)
 */
const dealDecimalRoundToPercent = (
  val?: string | number,
  precision = 0,
  obj?: { defaultValue?: string },
): string => {
  return !isInvalidValue(val)
    ? _.round(Number(val) * 100, precision) + '%'
    : obj?.defaultValue ?? '-';
};

const tenThousandNumberProcess = (num, max = 10000, precision = 2) => {
  if (!num && num != 0) return '-';
  num = Number(num);
  if (num > max) {
    return toThousands(num / 10000, precision) + 'w';
  } else {
    return toThousands(num, 0);
  }
};

const commonRender = (val: string) => val ?? '-';
const formatNumber = (num, precision = 0) => {
  if (num > 10000) {
    return toThousands(num / 10000, precision) + 'w';
  }
  if (num > 1000) {
    return toThousands(num / 1000, precision) + 'k';
  }
  return toThousands(num, precision);
};

const hashCode = (str: string) => {
  let hash = 0,
    i,
    chr;
  if (str.length === 0) return hash;
  for (i = 0; i < str.length; i++) {
    chr = str.charCodeAt(i);
    hash = (hash << 5) - hash + chr;
    hash |= 0; // Convert to 32bit integer
  }
  return hash;
};

const getGetCharLength = (s: string) => {
  if (!s?.length) return 0;
  let sTotal = 0;
  let eTotal = 0;
  for (const c of s) {
    if (c?.match(/[^\x00-\xff]/)) {
      sTotal++;
    } else {
      eTotal++;
    }
  }
  return sTotal * 2 + eTotal;
};

const percentRender = (number: number) => {
  return toDecimal(number * 100) + '%';
};
const formatMoney = (val?: string | number | null) => {
  if (val !== '' && val !== null && val !== undefined) {
    return toThousands(Number(val), 2);
  }
  return '-';
};
export {
  dealDecimalRoundToPercent,
  toDecimalRound,
  subtract,
  toThousands,
  dealDecimalToPercent,
  toDecimal,
  numberToString,
  priceRender,
  defaultRender,
  isEmpty,
  base64ToBlob,
  lowerPrice,
  lowerPriceShow,
  tenThousandNumberProcess,
  commonRender,
  formatNumber,
  hashCode,
  getGetCharLength,
  percentRender,
  formatMoney,
};
