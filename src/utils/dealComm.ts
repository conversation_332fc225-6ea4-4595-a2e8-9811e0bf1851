export const dealComm = (val1: any, val2: any) => {
  let commmoney = '';
  if (val1.indexOf('~') >= 0) {
    const arr = val1.split('~');
    const price0 = arr[0];
    const price1 = arr[1];
    const commmoney1 = Number((price0 * val2).toFixed(2));
    const commmoney2 = Number((price1 * val2).toFixed(2));
    commmoney = '¥' + commmoney1 + '~' + '¥' + commmoney2;
  } else {
    commmoney = '¥' + (val1 * val2).toFixed(2);
  }

  return commmoney;
};

export default dealComm;
