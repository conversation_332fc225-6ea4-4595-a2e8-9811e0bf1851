// 公共常量
import { SortOrder } from 'antd/es/table';
import { enumToArr, transformToList } from '@/common/common';
import VERSION from './version/version.json';

// ----------- 基础 ------------------
export const ANTD_PREFIX = 'jw';
export const ANTD_PREFIX_V2 = 'jw-v2';
export const ANTD_PREFIX_PRO = 'jw-pro';

// ----------- URL相关 ---------------
export const OSSHost = 'https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com';
export const OSSCrmImagePre =
  'https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images/jgpy-crm';
export const OSSImagePre = 'https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com/images';
export const OSS_STATIC_PREFIX = 'https://befriend-static-prod.oss-cn-hangzhou.aliyuncs.com';
export const LOGO_OLD = `${OSSImagePre}/logo/logo-old.png`;

// 精选联盟商品详情url
export const HAOHUO_URL_OLD = 'https://haohuo.jinritemai.com/views/product/detail?id=';
export const HAOHUO_URL_NEW = 'https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=';
export const HAOHUO_URL = HAOHUO_URL_NEW;

// ----------- 版本相关 ---------------
export enum SYS_TYPE {
  OPERATE = 'OPERATE',
  SUPPLIER = 'SUPPLIER',
  TALENT = 'TALENT',
  ORGANIZATION = 'ORGANIZATION',
  FINANCE = 'FINANCE',
  JGPY = 'JGPY',
  INVEST = 'INVEST',
}

export const SysNameMap: Record<SYS_TYPE, string> = {
  [SYS_TYPE.OPERATE]: '平台端',
  [SYS_TYPE.SUPPLIER]: '商家端',
  [SYS_TYPE.TALENT]: '达人端',
  [SYS_TYPE.ORGANIZATION]: '机构端',
  [SYS_TYPE.INVEST]: '尽微云-招商端',
};

export const VERSION_CONFIG = {
  [SYS_TYPE.OPERATE]: VERSION.version,
  [SYS_TYPE.INVEST]: VERSION.version,
  [SYS_TYPE.ORGANIZATION]: VERSION.version,
  [SYS_TYPE.SUPPLIER]: VERSION.version,
  [SYS_TYPE.TALENT]: VERSION.version,
};

export const COPY_VERSION = {
  [SYS_TYPE.OPERATE]: `PSS V${VERSION.version}`,
  [SYS_TYPE.ORGANIZATION]: `PSS V${VERSION.version}`,
  [SYS_TYPE.SUPPLIER]: `PSS V${VERSION.version}`,
  [SYS_TYPE.TALENT]: `PSS V${VERSION.version}`,
  [SYS_TYPE.INVEST]: `PSS V${VERSION.version}`,
};

// ----------- 日期相关 ------------------
export const MONTH_ZH = [
  '一',
  '二',
  '三',
  '四',
  '五',
  '六',
  '七',
  '八',
  '九',
  '十',
  '十一',
  '十二',
];

// ----------- 状态相关 ----------------

export enum EXPORT_STATE_ENUM {
  INIT = 'INIT',
  SEND_DATA_FINISH = 'SEND_DATA_FINISH',
  UPLOAD_FILE_FINISH = 'UPLOAD_FILE_FINISH',
  FAIL = 'FAIL',
  ERROR = 'ERROR',
}

export const EXPORT_STATE_MAP = {
  [EXPORT_STATE_ENUM.INIT]: '生成中',
  [EXPORT_STATE_ENUM.SEND_DATA_FINISH]: '生成中',
  [EXPORT_STATE_ENUM.UPLOAD_FILE_FINISH]: '已生成',
  [EXPORT_STATE_ENUM.FAIL]: '失败',
  [EXPORT_STATE_ENUM.ERROR]: '失败',
};

export enum PLATFORM_STATUS {
  IN_SALE = '上架中',
  DELETED = '已删除',
  SHELVE = '已下架',
}

export enum DyGoodsStatusEnum {
  IN_SALE = 'IN_SALE',
  DELETED = 'DELETED',
  SHELVE = 'SHELVE',
}

export enum SORT_ORDER {
  ASC = 'ASC',
  DESC = 'DESC',
}

export const SORT_ORDER_MAP = {
  ascend: SORT_ORDER.ASC,
  descend: SORT_ORDER.DESC,
};

export const SORT_ORDER_ORIGIN = {
  [SORT_ORDER.ASC]: 'ascend',
  [SORT_ORDER.DESC]: 'descend',
} as { [key: string]: SortOrder };

export enum LIVE_STATE_ENUM {
  UN_PLAY = 'UN_PLAY',
  PLAY_TODAY = 'PLAY_TODAY',
  PLAYED = 'PLAYED',
  BANNED = 'BANNED',
}
export const LIVE_STATE = {
  [LIVE_STATE_ENUM.UN_PLAY]: '未开播',
  [LIVE_STATE_ENUM.PLAY_TODAY]: '今日直播',
  [LIVE_STATE_ENUM.PLAYED]: '已结束',
  [LIVE_STATE_ENUM.BANNED]: '平台封禁',
};

export enum MENU_TYPE {
  USERCENTER = 'USERCENTER',
  NONE = 'NONE',
}

export enum HEADER_TYPE {
  NORMAL = 'NORMAL',
  SPECIAL = 'SPECIAL',
}

export enum BOOL_ENUM {
  YES = 'YES',
  NO = 'NO',
}
export const booleanMap = {
  [BOOL_ENUM.YES]: '是',
  [BOOL_ENUM.NO]: '否',
};
// form 表单，输入范围需要比较的类型
export enum FormFieldCompareType {
  // 大于
  GREATER = 'GREATER',
  // 小于
  LESS = 'LESS',
}

export enum OrderTypeEnum {
  LIVE = 1,
  GMV,
}

export const OrderTypes = [
  {
    value: OrderTypeEnum.LIVE,
    label: '普通类',
  },
  {
    value: OrderTypeEnum.GMV,
    label: '框架类',
  },
];

/*
 * @Author: Cupid.Z
 * @Date: 2021-12-30 18:50:48
 * @LastEditors: Cupid.Z
 * @LastEditTime: 2021-12-30 21:27:07
 * @Description: file content
 */

export enum AuditTypeEnum {
  CANCEL_COOP = 'CANCEL_COOP',
  ADJUST_SERVICE_FEE = 'ADJUST_SERVICE_FEE',
  ADJUST_LIVE_TIME = 'ADJUST_LIVE_TIME',
  ADJUST_CHANGE_BINDING = 'ADJUST_CHANGE_BINDING',
}
export enum AuditOrderStatusEnum {
  SUPPLIER_CONFIRMING = 'SUPPLIER_CONFIRMING',
  TALENT_CONFIRMING = 'TALENT_CONFIRMING',
  SUPPLIER_PASS = 'SUPPLIER_PASS',
  SUPPLIER_REJECT = 'SUPPLIER_REJECT',
  TALENT_PASS = 'TALENT_PASS',
  TALENT_REJECT = 'TALENT_REJECT',
  CANCEL = 'CANCEL',
}

export enum AdjustTypeEnum {
  CREATE = '创建',
  CANCEL = '取消',
  PASS = '通过',
  REJECT = '驳回',
  SYSTEM_CANCEL = '超过处理时效',
  SYSTEM_PASS = '系统默认通过',
  PAY_CANCEL_ADJUST = '付款取消调整',
  PAY_REJECT_ADJUST = '付款驳回调整',
}

export enum gmvPaymentStatus {
  /**
   * 无需付款
   */
  '无需付款' = 1,
  /**
   * 待付款
   */
  '待付款',
  /**
   * 付款中
   */
  '付款中',
  /**
   * 部分付款
   */
  '部分付款',
  '付款失败',
  '已付款',
  /**
   * 已结算
   */
  '已结算',
}

export const gmvPaymentStatusBadge = {
  [1]: 'green',
  [2]: 'blue',
  [3]: 'blue',
  [4]: 'green',
  [6]: 'green',
};

export enum PaymentStatus {
  /**
   * 无需付款
   */
  '无需付款' = 1,
  /**
   * 待付款
   */
  '待付款',
  /**
   * 付款中
   */
  '付款中',
  /**
   * 部分付款
   */
  '部分付款',
  '付款失败',
  '已付款：基础服务费',
  /**
   * 已结算
   */
  '已结算：基础服务费',
}

export const paymentStatusBadge = {
  [1]: 'green',
  [6]: 'green',
  [7]: 'green',
  [2]: 'blue',
  [3]: 'blue',
  [4]: 'blue',
  [5]: 'red',
};

export enum CooperationOrderStatus {
  /**
   * 待补充推广链接
   */
  TO_BE_ADD = 1,
  /**
   * 待履约
   */
  TO_BE_FULFILL_AGREEMENT = 2,
  /**
   * 已履约
   */
  FULFILL_AGREEMENT = 3,
  /**
   * 已关闭
   */
  CLOSED = 4,
  /**
   * "履约已确认"
   */
  AGREEMENT_CONFIRMED = 5,
}

export enum gmvCooperationOrderStatus {
  /**
   * 待补推广链接
   */
  TO_BE_ADD = 1,
  /**
   * 待履约
   */
  TO_BE_FULFILL_AGREEMENT,
  /**
   * 已履约
   */
  FULFILL_AGREEMENT,
  /**
   * 已关闭
   */
  CLOSED,
  /**
   * "履约已确认"
   */
  AGREEMENT_CONFIRMED,
}

export enum nonCoopOrderStatus {
  /**
   * 待达人确认
   */
  TALENT_CONFIRMING = 'TALENT_CONFIRMING',
  /**
   * 合作结束
   */
  END_OF_COOPERATION = 'END_OF_COOPERATION',
  /**
   * 合作中
   */
  IN_COOPERATION = 'IN_COOPERATION',
  /**
   * 已关闭
   */
  CLOSED = 'CLOSED',
}

export const nonCoopOrderStatusText = {
  [nonCoopOrderStatus.TALENT_CONFIRMING]: '待达人确认',
  [nonCoopOrderStatus.END_OF_COOPERATION]: '合作结束',
  [nonCoopOrderStatus.IN_COOPERATION]: '合作中',
  [nonCoopOrderStatus.CLOSED]: '已关闭',
};
export const gmvCooperationOrderStatusText = {
  [gmvCooperationOrderStatus.TO_BE_ADD]: '待补充推广链接',
  [gmvCooperationOrderStatus.TO_BE_FULFILL_AGREEMENT]: '待履约',
  [gmvCooperationOrderStatus.FULFILL_AGREEMENT]: '已履约',
  [gmvCooperationOrderStatus.CLOSED]: '已关闭',
  [gmvCooperationOrderStatus.AGREEMENT_CONFIRMED]: '履约已确认',
};

export enum cooperationFrameworkBillStatus {
  '初始化' = 1,
  '统计中',
  '已完成',
}

export enum OrderTabsEnum {
  ADJUST = 'ADJUST',
  COOPERATION = 'COOPERATION',
}

export const cooperationorderType = {
  1: '普通类',
  2: '框架类',
};

export enum ConfirmationType {
  AUTO_CONFIRMED = '自动确认',
  SUPPLIER_MANUAL_CONFIRMED = '商家手动确认',
}

export const CooperationOrderStatusText = {
  [CooperationOrderStatus.TO_BE_ADD]: '待补充推广链接',
  [CooperationOrderStatus.TO_BE_FULFILL_AGREEMENT]: '待履约',
  [CooperationOrderStatus.FULFILL_AGREEMENT]: '已履约',
  [CooperationOrderStatus.AGREEMENT_CONFIRMED]: '履约已确认',
  [CooperationOrderStatus.CLOSED]: '已关闭',
};
export const CooperationOrderStatusBadge = {
  [CooperationOrderStatus.TO_BE_ADD]: 'processing',
  [CooperationOrderStatus.TO_BE_FULFILL_AGREEMENT]: 'processing',
  [CooperationOrderStatus.FULFILL_AGREEMENT]: 'success',
  [CooperationOrderStatus.AGREEMENT_CONFIRMED]: 'success',
  [CooperationOrderStatus.CLOSED]: 'default',
};

export const FromType = {
  [SYS_TYPE.TALENT]: 'talent',
  [SYS_TYPE.SUPPLIER]: 'supplier',
  [SYS_TYPE.ORGANIZATION]: 'institution',
  [SYS_TYPE.OPERATE]: 'platform',
  [SYS_TYPE.JGPY]: 'befriends',
};

export enum SectionOrderStatusEnum {
  /**
   * 审核通过
   */
  PASS = 1,
  /**
   * 待履约
   */
  TO_BE_FULFILL_AGREEMENT,
  /**
   * 已履约
   */
  FULFILL_AGREEMENT,
  /**
   * "履约已确认"
   */
  AGREEMENT_CONFIRMED,
  // 待审核
  TO_BE_AUDIT,

  REJECTED,
}
export const SectionOrderStatuses = [
  {
    label: '待审核',
    value: SectionOrderStatusEnum.TO_BE_AUDIT,
  },
  {
    label: '已驳回',
    value: SectionOrderStatusEnum.REJECTED,
  },
  {
    label: '已通过',
    value: SectionOrderStatusEnum.PASS,
  },
  {
    label: '待履约',
    value: SectionOrderStatusEnum.TO_BE_FULFILL_AGREEMENT,
  },
  {
    label: '已履约',
    value: SectionOrderStatusEnum.FULFILL_AGREEMENT,
  },
  {
    label: '履约已确认',
    value: SectionOrderStatusEnum.AGREEMENT_CONFIRMED,
  },
];

export const SectionOrderStatusMap = {
  [SectionOrderStatusEnum.PASS]: {
    label: '已通过',
    color: 'success    ',
  },
  [SectionOrderStatusEnum.TO_BE_FULFILL_AGREEMENT]: {
    label: '待履约',
    color: 'processing',
  },
  [SectionOrderStatusEnum.FULFILL_AGREEMENT]: {
    label: '已履约',
    color: 'success',
  },
  [SectionOrderStatusEnum.AGREEMENT_CONFIRMED]: {
    label: '履约已确认',
    color: 'success',
  },
  [SectionOrderStatusEnum.TO_BE_AUDIT]: {
    label: '待审核',
    color: 'processing',
  },
  [SectionOrderStatusEnum.REJECTED]: {
    label: '已驳回',
    color: 'error',
  },
};

export enum SectionPaymentStatus {
  '无需付款' = 1,
  '待付款' = 2,
  /**
   * 付款中
   */
  '付款中' = 3,
  '已付款' = 6,
  /**
   * 已结算
   */
  '已结算' = 7,
}

export enum SectionOrderType {
  '授权服务费' = 1,
}

/*
 * @Author: Cupid.Z
 * @Date: 2021-12-30 18:50:48
 * @LastEditors: Cupid.Z
 * @LastEditTime: 2021-12-30 21:27:07
 * @Description: file content
 */

export const AuditTypeMap = {
  [AuditTypeEnum.CANCEL_COOP]: '关闭合作订单',
  [AuditTypeEnum.ADJUST_SERVICE_FEE]: '调整基础服务费',
  [AuditTypeEnum.ADJUST_LIVE_TIME]: '调整直播日期',
  [AuditTypeEnum.ADJUST_CHANGE_BINDING]: '调整_解绑支付单',
};
export const AuditOrderStatusMap = {
  [AuditOrderStatusEnum.SUPPLIER_CONFIRMING]: '待商家确认',
  [AuditOrderStatusEnum.TALENT_CONFIRMING]: '待达人确认',
  [AuditOrderStatusEnum.SUPPLIER_PASS]: '商家通过',
  [AuditOrderStatusEnum.SUPPLIER_REJECT]: '商家驳回',
  [AuditOrderStatusEnum.TALENT_PASS]: '达人通过',
  [AuditOrderStatusEnum.TALENT_REJECT]: '达人驳回',
  [AuditOrderStatusEnum.CANCEL]: '已取消',
};

// 合作订单支付状态
export enum cooperationPaymentStatusEnum {
  NO_PAYMENT_REQUIRED = 1, // 无需付款
  PENDING_PAYMENT = 2, // 待付款
  PAYING = 3, // 付款中
  PARTIAL_PAYMENT = 4, // 部分付款
  PAYMENT_FAIL = 5, // 付款失败"
  FULL_PAYMENT = 6, // 固定服务费已付款
  SETTLED = 7, // 固定服务费已结算
}

export const cooperationPaymentStatusText = {
  [cooperationPaymentStatusEnum.NO_PAYMENT_REQUIRED]: '无需付款',
  [cooperationPaymentStatusEnum.PENDING_PAYMENT]: '待付款', // 待付款
  [cooperationPaymentStatusEnum.PAYING]: '付款中', // 付款中
  [cooperationPaymentStatusEnum.PARTIAL_PAYMENT]: '部分付款', // 部分付款
  [cooperationPaymentStatusEnum.PAYMENT_FAIL]: '付款失败', // 付款失败"
  [cooperationPaymentStatusEnum.FULL_PAYMENT]: '已付款：基础服务费', // 固定服务费已付款
  [cooperationPaymentStatusEnum.SETTLED]: '已结算：基础服务费', // 固定服务费已结算
};

export enum CooperationFrameworkBillStatusEnum {
  INIT = '1',
  IN_PROCESS = '2',
  FINISH = '3',
  SUPPLIER_REJECT = '10',
  CLOSE = '20',
  SUBMIT = '30', // @TODO: 待提交暂定
}
export const CooperationFrameworkBillStatusMap = {
  [CooperationFrameworkBillStatusEnum.INIT]: '初始化',
  [CooperationFrameworkBillStatusEnum.IN_PROCESS]: '统计中',
  [CooperationFrameworkBillStatusEnum.FINISH]: '已完成',
  [CooperationFrameworkBillStatusEnum.SUPPLIER_REJECT]: '商家驳回',
  [CooperationFrameworkBillStatusEnum.CLOSE]: '已关闭',
};

export enum IncrementServiceTypeEnum {
  '授权服务费' = 1,
}

export const IncrementServiceTypes = [IncrementServiceTypeEnum['授权服务费']];

export enum PaymentMethodEnum {
  '网银支付' = 0,
  '汇款支付',
  '线下凭证',
  '框架支付',
  '预付款单支付',
}
export const PaymentMethods = [
  PaymentMethodEnum['网银支付'],
  PaymentMethodEnum['汇款支付'],
  PaymentMethodEnum['线下凭证'],
  PaymentMethodEnum['框架支付'],
  PaymentMethodEnum['预付款单支付'],
];

export const CooperationFrameworkBillStatuses = transformToList(CooperationFrameworkBillStatusMap);

export const auditTypeList = transformToList(AuditTypeMap);
export const auditOrderStatusList = transformToList(AuditOrderStatusMap);
export enum GoodsResource {
  DOUYIN_SUPPLIER_SPU = 'DOUYIN_SUPPLIER_SPU',
  // KUAISHOU_SUPPLIER_SPU = 'KUAISHOU_SUPPLIER_SPU',
  TAOBAO_SUPPLIER_SPU = 'TAOBAO_SUPPLIER_SPU',
  VIDEO_SUPPLIER_SPU = 'VIDEO_SUPPLIER_SPU',
  BAIDU_SUPPLIER_SPU = 'BAIDU_SUPPLIER_SPU',
  JD_SUPPLIER_SPU = 'JD_SUPPLIER_SPU',
  KS_SUPPLIER_SPU = 'KS_SUPPLIER_SPU',
  KUAISHOU_SUPPLIER_SPU = 'KUAISHOU_SUPPLIER_SPU',
  RED_SUPPLIER_SPU = 'RED_SUPPLIER_SPU',
}
export const GoodsResourceMap = {
  '': '全部',
  [GoodsResource.DOUYIN_SUPPLIER_SPU]: '抖音',
  [GoodsResource.TAOBAO_SUPPLIER_SPU]: '淘宝',
  // [GoodsResource.KUAISHOU_SUPPLIER_SPU]: '快手',
  [GoodsResource.VIDEO_SUPPLIER_SPU]: '视频号',
  [GoodsResource.BAIDU_SUPPLIER_SPU]: '百度',
  [GoodsResource.JD_SUPPLIER_SPU]: '京东',
  [GoodsResource.KUAISHOU_SUPPLIER_SPU]: '快手',
  [GoodsResource.RED_SUPPLIER_SPU]: '小红书',
};

export const GoodsResourceIcon = {
  [GoodsResource.DOUYIN_SUPPLIER_SPU]: `${OSSImagePre}/icon/icn-dy.png`,
  // [GoodsResource.KUAISHOU_SUPPLIER_SPU]: `${OSSImagePre}/icon/icn-ks.png`,
  [GoodsResource.TAOBAO_SUPPLIER_SPU]: `${OSSImagePre}/icon/icn-tb.png`,
  [GoodsResource.VIDEO_SUPPLIER_SPU]: `${OSSImagePre}/logo/video.png`,
  [GoodsResource.RED_SUPPLIER_SPU]: `${OSSImagePre}/icon/icon-xhs.png`,
  [GoodsResource.BAIDU_SUPPLIER_SPU]: `${OSSImagePre}/icon/icn-baidu.png`,
  [GoodsResource.JD_SUPPLIER_SPU]: `${OSSImagePre}/icon/icn-jd.png`,
  [GoodsResource.KUAISHOU_SUPPLIER_SPU]: `${OSSImagePre}/icon/icn-ks.png`,
  [GoodsResource.RED_SUPPLIER_SPU]: `${OSSImagePre}/icon/icon-xhs.png`,
};

export const goodsResourceList = transformToList(GoodsResourceMap);
export enum BIZ_ORDER_TYPE {
  COOPERATION_ORDER = 'COOPERATION_ORDER',
  SAMPLE_ORDER = 'SAMPLE_ORDER',
  SECTION_ORDER = 'SECTION_ORDER',
}
export enum PAY_TYPE {
  EBANK = 'EBANK',
  EBANK_B2B = 'EBANK_B2B',
  EBANK_B2C = 'EBANK_B2C',
  OFF_LINE_BANK_TRANSFER = 'OFF_LINE_BANK_TRANSFER',
  QR_CODE = 'QR_CODE',
  ALL = 'ALL',
}

export enum EBPAY_TYPE {
  SURPLUS = 'SURPLUS',
  NORMAL = 'NORMAL',
  NORMAL_USE_PREPAY = 'NORMAL_USE_PREPAY',
}
export enum ChannelEnum {
  DY = 'DY',
  TT = 'TT',
  XG = 'XG',
  HS = 'HS',
  WECHAT_VIDEO = 'WECHAT_VIDEO',
  BAIDU = 'BAIDU',
  JD = 'JD',
  KS = 'KS',
  RED = 'RED',
}

export const ChannelImgStatus = {
  [ChannelEnum.DY]: `${OSSImagePre}/icon/icn-dy.png`,
  [ChannelEnum.TT]: `${OSSImagePre}/icon/icn-tt.png`,
  [ChannelEnum.XG]: `${OSSImagePre}/icon/icn-xg.png`,
  [ChannelEnum.HS]: `${OSSImagePre}/icon/icn-hs.png`,
  [ChannelEnum.WECHAT_VIDEO]: `${OSSImagePre}/logo/video.png`,
  [ChannelEnum.RED]: `${OSSImagePre}/icon/icon-xhs.png`,
  [ChannelEnum.BAIDU]: `${OSSImagePre}/icon/icn-baidu.png`,
  [ChannelEnum.JD]: `${OSSImagePre}/icon/icn-jd.png`,
  [ChannelEnum.KS]: `${OSSImagePre}/icon/icn-ks.png`,
  [ChannelEnum.RED]: `${OSSImagePre}/icon/icon-xhs.png`,
};
export enum PlatformSource {
  // 抖音
  TIC_TOK = 'TIC_TOK',
  // 快手
  // KWAI = 'KWAI',
  // 尽微好物
  JINWEI = 'JINWEI',
  // 视频号
  VIDEO = 'VIDEO',
  // 淘宝
  TAOBAO = 'TAOBAO',
  // 天猫
  T_MALL = 'T_MALL',
  WECHAT_VIDEO = 'WECHAT_VIDEO',
  RED = 'RED', // 小红书
  // 百度
  BAIDU = 'BAIDU',
  // 京东
  JD = 'JD',
  KS = 'KS',
  RED = 'RED',
}

export const PlatformSourceIcon = {
  [PlatformSource.TIC_TOK]: `${OSSImagePre}/icon/icn-dy.png`,
  // [PlatformSource.KWAI]: `${OSSImagePre}/icon/icn-ks.png`,
  [PlatformSource.TAOBAO]: `${OSSImagePre}/icon/icn-tb.png`,
  [PlatformSource.T_MALL]: `${OSSImagePre}/icon/icn-tm.png`,
  [PlatformSource.WECHAT_VIDEO]: `${OSSImagePre}/logo/video.png`,
  [PlatformSource.RED]: `${OSSImagePre}/icon/icon-xhs.png`,
  [PlatformSource.BAIDU]: `${OSSImagePre}/icon/icn-baidu.png`,
  [PlatformSource.JD]: `${OSSImagePre}/icon/icn-jd.png`,
  [PlatformSource.KS]: `${OSSImagePre}/icon/icn-ks.png`,
  [PlatformSource.RED]: `${OSSImagePre}/icon/icon-xhs.png`,
};
export enum PolymerizationEnum {
  DY = 'DY',
  TB = 'TB',
  KS = 'KS',
  JD = 'JD',
  TM = 'TM',
}

export const PolymerizationStatus = {
  [PolymerizationEnum.DY]: `${OSSImagePre}/icon/icn-dy.png`,
  [PolymerizationEnum.TB]: `${OSSImagePre}/icon/icn-tb.png`,
  [PolymerizationEnum.KS]: `${OSSImagePre}/icon/icn-ks.png`,
  [PolymerizationEnum.JD]: `${OSSImagePre}/icon/icn-jd.png`,
  [PolymerizationEnum.TM]: `${OSSImagePre}/icon/icn-tm.png`,
};

// 子项目移动进来
export enum FlowStatus {
  INVITING = '邀约中',
  BP_CONFIRMING = '待商务确认',
  WAIT_AUDIT = '审核中',
  WAIT_LIVE = '待直播',
  ABORT_LIVE = '已掉品',
  // ABORT_WAIT_LIVE = '已掉品',
  CANCEL = '已取消',
  COMPLETED_LIVE = '已播', //已播状态
  LOSE_EFFICACY = '已失效', //已播状态
}
export enum FlowStatusEnumAll {
  INVITING = '邀约中',
  BP_CONFIRMING = '待商务确认',
  WAIT_AUDIT = '审核中',
  WAIT_LIVE = '待直播',
  ABORT_LIVE = '已掉品',
  ABORT_WAIT_LIVE = '已掉品',
  CANCEL = '已取消',
  COMPLETED_LIVE = '已播', //已播状态
  LOSE_EFFICACY = '已失效', //已播状态
  INVALID = '已失效',
  SELECTING = '选品中',
  DISABLED = '已禁用',
  IN_COOPERATE = '合作中',
  END_COOPERATE = '合作结束',
}
export enum FlowStatusColor {
  BP_CONFIRMING = 'orange',
  WAIT_AUDIT = 'blue',
  WAIT_LIVE = 'green',
  ABORT_LIVE = 'red',
  ABORT_WAIT_LIVE = 'red',
  CANCEL = 'red',
  COMPLETED_LIVE = 'purple', //已播状态
  INVITING = 'orange',
  LOSE_EFFICACY = 'black',
  INVALID = 'blue',
  SELECTING = 'green',
  DISABLED = 'red',
  IN_COOPERATE = 'green',
  END_COOPERATE = 'purple',
}

export enum FlowStatus_ChoiceList {
  INVITING = '邀约中',
  BP_CONFIRMING = '待商务确认',
  WAIT_AUDIT = '审核中',
  WAIT_LIVE = '待直播',
  ABORT_LIVE = '已掉品',
  ABORT_WAIT_LIVE = '已掉品',
  CANCEL = '已取消',
  COMPLETED_LIVE = '已播', //已播状态
  LOSE_EFFICACY = '已失效', //已播状态
}
export enum FlowStatusColor_ChoiceList {
  BP_CONFIRMING = 'orange',
  WAIT_AUDIT = 'blue',
  WAIT_LIVE = 'green',
  ABORT_LIVE = 'red',
  ABORT_WAIT_LIVE = 'red',
  CANCEL = 'red',
  COMPLETED_LIVE = 'purple', //已播状态
  INVITING = 'orange',
  LOSE_EFFICACY = 'black',
}

export const cooperationPaymentStatusList = Object.entries(cooperationPaymentStatusText).map(
  ([value, label]) => ({
    value,
    label,
  }),
);
