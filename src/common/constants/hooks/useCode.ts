import { useState, useEffect } from 'react';
import { systemCodeSearch } from '@/pages/system-code/services/yml/index';
import { orderBy } from 'lodash';
import { responseWithResultAsync } from '@/components/GoodsInfoEdit/component/LeftCardLeftForm';
export enum CODE_ENUM {
  SELECTION_LABEL = 'SelectionLabelEnum',
  DROP_PRODUCT_REASON = 'DROP_PRODUCT_REASON_TYPE',
  BRAND_BLACK_TYPE = 'BRAND_BLACK_TYPE',
  SPU_BLACK_TYPE = 'SPU_BLACK_TYPE',
  LOW_COMMISSION_REASON_TYPE = 'LowCommissionReasonTypeEnum',
  SUPPLIER_BLACK_TYPE = 'SUPPLIER_BLACK_TYPE',
  TAX_RATE = 'TAX_RATE',
  GOODS_ASSORTING_DISABLE_REASON = 'GOODS_ASSORTING_DISABLE_REASON',
  GOODS_ASSORTING_ENABLE_REASON = 'GOODS_ASSORTING_ENABLE_REASON',
  BUSINESS_ORDER_ORG = 'business_order_org',
  SHOP_BLACK_TYPE = 'SHOP_BLACK_TYPE',
  FIELD_COST = 'charge_type_1',
  PUBLICITY_EXPENSES = 'charge_type_2',
  TRAFFIC_DELIVERY = 'charge_type_3',
  LUCKY_DRAW_AND_REWARD = 'charge_type_4',
  ARTIST_COST = 'charge_type_5',
  ARTIST_SHARING = 'charge_type_6',
  GOODS_SUBSIDY = 'charge_type_7',
  SHORT_VIDEO_COST = 'charge_type_8',
  WORK_ORDER_FEEDBACK_CHANNEL = 'WORK_ORDER_FEEDBACK_CHANNEL',
  WORK_ORDER_PROBLEM_CLASSIFICATION = 'WORK_ORDER_PROBLEM_CLASSIFICATION',
  WORK_ORDER_USER_CLASSIFICATION = 'WORK_ORDER_USER_CLASSIFICATION', //用户分类
  WORK_ORDER_VIOLATION_CLASSIFICATION = 'WORK_ORDER_VIOLATION_CLASSIFICATION', //违规分类
  FUNDING_PARTY = 'FUNDING_PARTY',
  SENSITIVE_WORDS_SCOPE_CONFIG = 'SENSITIVE_WORDS_SCOPE_CONFIG',
  WORK_ORDER_USER_SOURCE = 'WORK_ORDER_USER_SOURCE', //用户来源
  INVOICE_TYPE = 'INVOICE_TYPE',
  BUSINESS_UNIT = 'BUSINESS_UNIT', //业务线
  COMMISSION_RANGE = 'COMMISSION_RANGE',
  BRAND_FEE_RANGE = 'BRAND_FEE_RANGE',
  COOPERATION_MODE = 'COOPERATION_MODE',
  SOURCE_CHANNEL = 'SOURCE_CHANNEL',
  HANGZONG_ROUNDTAG = 'HANGZONG_ROUNDTAG',
  WAREHOUSE_AREA_TYPE = 'warehouse_area_type', //库区类型
  WAREHOUSE_LOCATION_TYPE = 'warehouse_location_type', //库位类型
  INBOUND_TYPE = 'inbound_type', //入库类型
  OUTBOUND_TYPE = 'outbound_type', //出库类型
  ROUND_TAG = 'ROUND_TAG',
  VALIDITY_PERIOD = 'VALIDITY_PERIOD', // 低佣生效期限
  COMMON_AUDIT_OPINION = 'COMMON_AUDIT_OPINION',
  BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE = 'BUSINESS_OPPORTUNITY_LEAD_BRAND_TYPE',
  BUSINESS_OPPORTUNITY_LEAD_BRAND_FINANCING_STATUS = 'BUSINESS_OPPORTUNITY_LEAD_BRAND_FINANCING_STATUS',
  BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE = 'BUSINESS_OPPORTUNITY_LEAD_TAOBAO_SHOP_SCORE',
  BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE = 'BUSINESS_OPPORTUNITY_LEAD_DOUYIN_SHOP_SCORE',
  COOPERATION_TYPE = 'COOPERATION_TYPE',
}

// 拉平数组转成Tree结构
const flatToTree = (list: any[], firstName: string) => {
  // 创建映射表，用于快速查找子节点
  const childrenMap = new Map<string, any[]>();
  // 创建已访问集合，防止死循环
  const visited = new Set<string>();

  // 初始化映射表
  list.forEach((item) => {
    const parentKey = item.superCodeValue;
    if (!childrenMap.has(parentKey)) {
      childrenMap.set(parentKey, []);
    }
    childrenMap.get(parentKey)!.push(item);
  });

  // 递归构建树形结构
  const buildTree = (parentCode: string): any[] => {
    // 防止死循环：如果已经访问过这个节点，返回空数组
    if (visited.has(parentCode)) {
      return [];
    }

    // 标记当前节点为已访问
    visited.add(parentCode);

    const children = childrenMap.get(parentCode) || [];
    return children.map((child) => {
      const { codeValue, valueName } = child;
      return {
        ...child,
        label: valueName,
        value: codeValue,
        children: buildTree(child.codeValue),
      };
    });
  };

  // 从根节点开始构建
  return buildTree(firstName);
};
// 2024-12-19 zhouby -> cursor ai结尾共生成 25 行代码

export const useCode = (
  code: string,
  options?: { wait?: boolean; able?: boolean; isGetMultilevel?: boolean },
) => {
  const [codeList, setCodeList] = useState<
    Array<{ value: string | undefined; label: string | undefined }>
  >([]);
  const [codeEnum, setCodeEnum] = useState<Record<string, any>>();
  const getEnumList = async () => {
    if (!code) {
      setCodeList([]);
      return;
    }
    const result = await responseWithResultAsync({
      request: systemCodeSearch,
      params: { codeType: code as string, isGetMultilevel: options?.isGetMultilevel },
    });
    if (result) {
      const obj: Record<string, any> = {};

      const resultArr = result.systemCodeValueModels?.map((item) => ({
        ...item,
        status: result.status == 'DISABLE' ? 'DISABLE' : item.status,
      }));
      const filterArr = orderBy(resultArr ?? [], ['sort'], ['desc'])?.filter(
        (item) => item.status === 'ENABLE',
      );
      const noFilterArr = orderBy(resultArr ?? [], ['sort'], ['desc']);

      if (options?.isGetMultilevel) {
        const allList = options?.able ? filterArr : noFilterArr;
        //这是一个拉平的数组
        const nameMap = allList?.reduce((acc: any, item: any) => {
          acc[item?.codeValue] = item?.valueName;
          return acc;
        }, {});
        // 将拉平数字转成Tree结构
        const treeList = flatToTree(allList, result?.codeType as string);
        console.log('🚀 ~ getEnumList ~ treeList:', treeList);

        setCodeEnum(nameMap);
        setCodeList([...treeList]);
        return;
      }
      const arr = (options?.able ? filterArr : noFilterArr).map((i) => {
        obj[i.codeValue as string] = i.valueName;
        return {
          label: i.valueName,
          value: i.codeValue,
        };
      });
      setCodeEnum(obj);
      setCodeList([...arr]);
    }
  };
  useEffect(() => {
    // setTimeout(() => {
    !options?.wait && getEnumList();
    // },);
  }, [code]);
  return {
    codeList,
    codeEnum,
    getEnumList,
  };
};
