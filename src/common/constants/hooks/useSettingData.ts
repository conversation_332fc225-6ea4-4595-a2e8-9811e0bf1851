import { useState, useEffect, useRef } from 'react';
import { setConfig, getConfig } from '@/services/yml/common/index';
type SettingData = {
  name: string;
  options: any; // 这里的 any 可以根据实际情况替换为具体的类型
  getTableHeight?: any; // 这里的 any 可以根据实际情况替换为具体的类型
};
export const useSettingData = ({ name, options, getTableHeight }: SettingData) => {
  const bizType = name;

  const [optionsOrigin, setOptionsOrigin] = useState<
    Array<{ name: string; index: number; isShow: boolean }>
  >([]);
  const oldOptions = useRef([]);

  useEffect(() => {
    // console.log('options', options);
    if (options && bizType) {
      run();
    }
  }, []);
  useEffect(() => {
    if (options && bizType) {
      const arrs = Object.entries(options).map((item, index) => {
        return {
          ...item[1],
          name: item[1]?.label,
          key: item[0],
          index: index,
          isShow: true,
          draggable: item[1]?.draggable,
        };
      });
      oldOptions.current = [...arrs];
    }
  }, [options]);
  const setConfigInfo = (value: { bizType: string; extConfig: string }) => {
    // debugger;
    const defaultArr = JSON.parse(value.extConfig)?.filter((i) => i.draggable === false);
    const draggableArr = JSON.parse(value.extConfig)?.filter((i) => i.draggable);
    const resArr = defaultArr?.concat(draggableArr);

    // return;
    setConfig({ bizType: value.bizType, extConfig: JSON.stringify(resArr) }).then(({ res }) => {
      if (res.code === '200') {
        getConfigInfo({ bizType: value.bizType });
      }
    });
  };
  const getConfigInfo = (value: { bizType: string; arr?: Array<any> }) => {
    getConfig({ bizType: value.bizType }).then(({ res }) => {
      if (res.code === '200') {
        const result = res.result;
        if (result) {
          const newOptions = [...JSON.parse(result)];
          const newKeys = newOptions.map((i) => i.key);
          const oldKeys = oldOptions.current.map((i) => i.key);
          let isChange = false;
          newOptions.forEach((item, index) => {
            if (item.hocOptions) {
              delete item.hocOptions;
            }
            //如果代码里有
            if (oldKeys.indexOf(item.key) > -1) {
              // 当传入draggable改变时更新接口中的值
              if (oldOptions.current[oldKeys.indexOf(item.key)].draggable !== item.draggable) {
                isChange = true;
              }
              // 当传入label改变时更新接口中的值
              if (oldOptions.current[oldKeys.indexOf(item.key)].label !== item.name) {
                isChange = true;
              }
              newOptions.splice(index, 1, {
                ...item,
                draggable: oldOptions.current[oldKeys.indexOf(item.key)]?.draggable,
                name: oldOptions.current[oldKeys.indexOf(item.key)]?.label,
              });
              // item.draggable = oldOptions.current[ol dKeys.indexOf(item.key)]?.draggable;
            } else {
              isChange = true;
              newOptions.splice(index, 1);
            }
          });
          // console.log('newOptionssChange', isChange);
          oldOptions.current.forEach((item, index) => {
            if (newKeys.indexOf(item.key) > -1) {
            } else {
              newOptions.push({ ...oldOptions.current[[index]] });
            }
          });

          if (isChange) {
            const arr = newOptions.map((i) => {
              const obj = i;
              // 此处应该删除多余的属性
              delete obj.hocOptions;
              delete obj.renderNode;
              delete obj.formItemOptions;
              return obj;
            });
            // return;
            setConfigInfo({ bizType: value.bizType, extConfig: JSON.stringify(arr) });
          } else {
            const arr: Array<any> = [];
            newOptions.forEach((item) => {
              oldOptions.current.forEach((i) => {
                if (i.key === item.key) {
                  arr.push({
                    ...i,
                    ...item,
                  });
                }
              });
            });
            setOptionsOrigin([...arr]);
          }
        } else {
          setConfigInfo({ bizType: value.bizType, extConfig: JSON.stringify(value.arr) });
        }
        getTableHeight();
      }
    });
  };
  const run = () => {
    // console.log('options', options);
    const arr = Object.entries(options).map((item, index) => {
      return {
        // ...item[1],
        name: item[1]?.label,
        key: item[0],
        index: index,
        isShow: true,
        draggable: item[1]?.draggable,
      };
    });
    setTimeout(() => {
      getConfigInfo({ bizType, arr });
    }, 100);
  };

  return {
    optionsOrigin,
    run,
  };
};
