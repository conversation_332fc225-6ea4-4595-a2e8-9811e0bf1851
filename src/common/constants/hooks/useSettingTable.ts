import { useState, useEffect, useRef } from 'react';
import { setConfig, getConfig } from '@/services/yml/common/index';
import { use } from 'echarts';
type SettingData = {
  name: string;
  options: any; // 这里的 any 可以根据实际情况替换为具体的类型
  getTableHeight?: any; // 这里的 any 可以根据实际情况替换为具体的类型
};
export const useSettingTable = ({ name, options, getTableHeight }: SettingData) => {
  const bizType = name;

  const [optionsOrigin, setOptionsOrigin] = useState<
    Array<{ name: string; index: number; isShow: boolean }>
  >([]);
  const oldOptions = useRef([]);

  useEffect(() => {
    if (options && bizType) {
      const arrs = options.map((item, index) => {
        return {
          ...item,
          name: item?.label || item?.title,
          index: index,
          key: item.key,
          isShow: true,
          draggable: item?.draggable,
        };
      });
      oldOptions.current = [...arrs];
    }
  }, [options]);
  const setConfigInfo = (value: { bizType: string; extConfig: string }) => {
    setConfig({ bizType: value.bizType, extConfig: value.extConfig }).then(({ res }) => {
      if (res.code === '200') {
        setTimeout(() => {
          getConfigInfo({ bizType: value.bizType });
        }, 300);
      }
    });
  };
  const getConfigInfo = (value: { bizType: string; arr?: Array<any> }) => {
    getConfig({ bizType: value.bizType }).then(({ res }) => {
      if (res.code === '200') {
        const result = res.result ? [...JSON.parse(res.result)] : [];

        if (result && result.length) {
          const newOptions = result;
          const newKeys = newOptions.map((i) => i.key);
          const oldKeys = oldOptions.current.map((i) => i.key);

          let isChange = false;
          newOptions.forEach((item, index) => {
            //如果代码里有
            if (oldKeys.indexOf(item.key) > -1) {
              if (oldOptions.current[oldKeys.indexOf(item.key)].draggable !== item.draggable) {
                isChange = true;
              }
              newOptions.splice(index, 1, {
                ...item,
                draggable: oldOptions.current[oldKeys.indexOf(item.key)]?.draggable,
              });
            } else {
              isChange = true;
              newOptions.splice(index, 1);
            }
          });
          oldOptions.current.forEach((item, index) => {
            if (newKeys.indexOf(item.key) > -1) {
            } else {
              isChange = true;

              newOptions.splice(index, 0, { ...oldOptions.current[index] });
            }
          });

          if (isChange) {
            const arr = newOptions.map((i) => {
              const obj = i;
              delete obj.title;
              return obj;
            });
            JSON.stringify(arr);
            setTimeout(() => {
              setConfigInfo({ bizType: value.bizType, extConfig: JSON.stringify(arr) });
            }, 100);
          } else {
            const arr: Array<any> = [];
            newOptions.forEach((item) => {
              oldOptions.current.forEach((i) => {
                if (i.key === item.key) {
                  arr.push({
                    ...i,
                    ...item,
                  });
                }
              });
            });

            const resArr = arr.filter((i) => i.isShow);

            setOptionsOrigin([...resArr]);
          }
        } else {
          setTimeout(() => {
            setConfigInfo({ bizType: value.bizType, extConfig: JSON.stringify(value.arr) });
          }, 100);
        }
        getTableHeight();
      }
    });
  };
  const run = () => {
    const arr = options.map((item, index) => {
      return {
        name: item?.label || item?.title,
        key: item?.key,
        index: index,
        isShow: true,
        draggable: item?.draggable,
      };
    });
    setTimeout(() => {
      if (arr) {
        getConfigInfo({ bizType, arr });
      }
    }, 100);
  };
  return {
    optionsOrigin,
    run,
  };
};
