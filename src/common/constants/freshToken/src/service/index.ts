import { getSign } from './index.yml.sign';
import { Fetch, Const } from 'qmkit';

interface ResponseWithResult<T> {
  success: boolean;
  message: string;
  code: string;
  result: T;
}

export type VerifyRolesChangedRequest = any;

export type VerifyRolesChangedResult = boolean;

/**
 *验证员工角色是否发生变化
 */
export const verifyRolesChanged = (params: VerifyRolesChangedRequest) => {
  return Fetch<ResponseWithResult<VerifyRolesChangedResult>>(
    '/user/public/employee/verifyRolesChanged',
    {
      method: 'POST',
      body: JSON.stringify(params),
      headers: { accessAuth: getSign('/user/public/employee/verifyRolesChanged') },
    },
  );
};
