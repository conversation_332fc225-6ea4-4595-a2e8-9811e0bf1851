import { getFunctions } from '@/services/user';
import { setLocalFunctions } from 'qmkit/util';
import { verifyRolesChanged } from './service';

interface ChangeDetectionResponse {
  isChange: boolean;
  [key: string]: any;
}

interface ChangeDetectionOptions {
  interval?: number; // 轮询间隔，默认30秒
  checkChangeUrl?: string; // 检测变化的接口地址
  getPermissionUrl?: string; // 获取权限的接口地址
  onPermissionChange?: () => void; // 权限变化时的回调
  onError?: (error: any) => void; // 错误回调
}

class ChangeDetectionPolling {
  private timer: number | null = null;
  private isRunning = false;
  private isRequesting = false; // 标记是否有请求正在进行
  private options: Required<ChangeDetectionOptions>;

  constructor(options: ChangeDetectionOptions = {}) {
    this.options = {
      interval: 60000, // 默认30秒
      checkChangeUrl: '/api/check-changes', // 默认检测接口
      getPermissionUrl: '/api/get-permissions', // 默认权限接口
      onPermissionChange: () => {
        // 默认刷新页面
        window.location.reload();
      },
      onError: (error) => {
        console.error('ChangeDetectionPolling error:', error);
      },
      ...options,
    };
  }

  /**
   * 启动轮询
   */
  public start(): void {
    if (this.isRunning) {
      console.warn('ChangeDetectionPolling is already running');
      return;
    }

    this.isRunning = true;
    this.poll();
  }

  /**
   * 停止轮询
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    this.isRequesting = false; // 重置请求状态
    if (this.timer) {
      window.clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 检查是否正在运行
   */
  public isActive(): boolean {
    return this.isRunning;
  }

  /**
   * 检查是否有请求正在进行
   */
  public isRequestInProgress(): boolean {
    return this.isRequesting;
  }

  /**
   * 执行轮询逻辑
   */
  private async poll(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    // 如果上一次请求还在进行中，跳过本次轮询
    if (this.isRequesting) {
      console.log('上一次请求还在进行中，跳过本次轮询');
      // 延迟一段时间后再次尝试
      this.timer = window.setTimeout(() => {
        this.poll();
      }, 5000); // 5秒后重试
      return;
    }

    try {
      this.isRequesting = true;
      await this.checkForChanges();
    } catch (error) {
      this.options.onError(error);
    } finally {
      this.isRequesting = false;
      // 设置下一次轮询
      if (this.isRunning) {
        this.timer = window.setTimeout(() => {
          this.poll();
        }, this.options.interval);
      }
    }
  }

  /**
   * 检测变化
   */
  private async checkForChanges(): Promise<void> {
    if (!window.token) {
      return;
    }
    try {
      const { res } = await verifyRolesChanged({});
      if (res.success && res?.result) {
        await this.updatePermissions();
      }
    } catch (error) {
      throw new Error(`检测变化失败: ${error}`);
    }
    // try {
    //   const response = await fetch(this.options.checkChangeUrl, {
    //     method: 'GET',
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //     credentials: 'include', // 包含cookies
    //   });

    //   if (!response.ok) {
    //     throw new Error(`HTTP error! status: ${response.status}`);
    //   }

    //   const data: ChangeDetectionResponse = await response.json();

    //   if (data.isChange) {
    //     console.log('检测到变化，开始更新权限...');
    //     await this.updatePermissions();
    //   }
    // } catch (error) {
    //   throw new Error(`检测变化失败: ${error}`);
    // }
  }

  /**
   * 更新权限
   */
  private async updatePermissions(): Promise<void> {
    if (!window.token) {
      return;
    }
    try {
      const { res } = await getFunctions();
      if (res.success) {
        const functions = (res?.result || []).filter((item) => item?.authNm);
        setLocalFunctions(functions.map((item) => item?.authNm));
        this.options.onPermissionChange();
      }
    } catch (error) {
      throw new Error(`更新权限失败: ${error}`);
    }
    // try {
    //   const response = await fetch(this.options.getPermissionUrl, {
    //     method: 'GET',
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //     credentials: 'include',
    //   });

    //   if (!response.ok) {
    //     throw new Error(`HTTP error! status: ${response.status}`);
    //   }

    //   const permissionData = await response.json();
    //   console.log('权限更新成功:', permissionData);

    //   // 调用权限变化回调
    //   this.options.onPermissionChange();
    // } catch (error) {
    //   throw new Error(`更新权限失败: ${error}`);
    // }
  }

  /**
   * 更新配置
   */
  public updateOptions(newOptions: Partial<ChangeDetectionOptions>): void {
    this.options = {
      ...this.options,
      ...newOptions,
    };
  }

  /**
   * 销毁实例
   */
  public destroy(): void {
    this.stop();
  }
}

export default ChangeDetectionPolling;
export type { ChangeDetectionResponse, ChangeDetectionOptions };
// 2024-12-19 zhouby -> cursor ai结尾共生成 108 行代码
