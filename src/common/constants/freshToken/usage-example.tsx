//ai生成
import React from 'react';
import { ChangeDetectionPolling } from './index';

// 在 mainlayout 组件中使用示例 - Class 写法
export default class MainLayoutExample extends React.Component<{
  location: {
    pathname: string;
  };
}> {
  private polling: ChangeDetectionPolling | null = null;

  constructor(props: any) {
    super(props);
    this.state = {
      // 你的状态
    };
  }

  componentDidMount() {
    // 创建轮询实例
    this.polling = new ChangeDetectionPolling({
      interval: 30000, // 30秒轮询一次
      // checkChangeUrl: '/api/user/check-permission-change', // 检测权限变化的接口
      // getPermissionUrl: '/api/user/get-permissions', // 获取权限的接口
      onPermissionChange: () => {
        // 权限变化时的处理逻辑
        console.log('权限发生变化，刷新页面');
        // 可以在这里添加一些提示信息
        // message.info('权限已更新，页面将刷新');
        window.location.reload();
      },
      onError: (error) => {
        console.error('权限检测轮询出错:', error);
        // 可以在这里添加错误处理逻辑
        // message.error('权限检测失败');
      },
    });

    // 启动轮询
    this.polling.start();
  }

  componentWillUnmount() {
    // 组件卸载时清理轮询
    if (this.polling) {
      this.polling.stop();
      this.polling.destroy();
      this.polling = null;
    }
  }

  render() {
    return (
      <div>
        {/* 你的 mainlayout 内容 */}
        <h1>Main Layout</h1>
        <p>权限检测轮询已启动，每30秒检测一次权限变化</p>
      </div>
    );
  }
}
// 2024-12-19 zhouby -> cursor ai结尾共生成 42 行代码
