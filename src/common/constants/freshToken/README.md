# ChangeDetectionPolling 使用说明

## 概述

`ChangeDetectionPolling` 是一个用于定期检测用户权限变化的轮询类。当检测到权限变化时，会自动调用权限接口并刷新页面。

## 功能特性

- 🔄 定期轮询检测接口
- 🎯 条件触发权限更新
- 🧹 自动清理资源
- ⚙️ 可配置轮询间隔和接口地址
- 🚨 错误处理和回调
- 🛡️ 请求状态管理（防止重复请求）

## 使用方法

### 1. 基本使用

```typescript
import { ChangeDetectionPolling } from '@/common/constants/freshToken';

const polling = new ChangeDetectionPolling({
  interval: 30000, // 30秒轮询一次
  checkChangeUrl: '/api/user/check-permission-change',
  getPermissionUrl: '/api/user/get-permissions',
  onPermissionChange: () => {
    window.location.reload();
  },
});

// 启动轮询
polling.start();

// 停止轮询
polling.stop();

// 销毁实例
polling.destroy();
```

### 2. 在 React 组件中使用

#### Function 组件写法

```typescript
import React, { useEffect, useRef } from 'react';
import { ChangeDetectionPolling } from '@/common/constants/freshToken';

const MainLayout: React.FC = () => {
  const pollingRef = useRef<ChangeDetectionPolling | null>(null);

  useEffect(() => {
    pollingRef.current = new ChangeDetectionPolling({
      interval: 30000,
      checkChangeUrl: '/api/user/check-permission-change',
      getPermissionUrl: '/api/user/get-permissions',
      onPermissionChange: () => {
        window.location.reload();
      },
    });

    pollingRef.current.start();

    return () => {
      if (pollingRef.current) {
        pollingRef.current.destroy();
      }
    };
  }, []);

  return <div>Main Layout Content</div>;
};
```

#### Class 组件写法

```typescript
import React from 'react';
import { ChangeDetectionPolling } from '@/common/constants/freshToken';

export default class MainLayout extends React.Component<{
  location: {
    pathname: string;
  };
}> {
  private polling: ChangeDetectionPolling | null = null;

  constructor(props: any) {
    super(props);
    this.state = {
      // 你的状态
    };
  }

  componentDidMount() {
    this.polling = new ChangeDetectionPolling({
      interval: 30000,
      checkChangeUrl: '/api/user/check-permission-change',
      getPermissionUrl: '/api/user/get-permissions',
      onPermissionChange: () => {
        window.location.reload();
      },
    });

    this.polling.start();
  }

  componentWillUnmount() {
    if (this.polling) {
      this.polling.stop();
      this.polling.destroy();
      this.polling = null;
    }
  }

  render() {
    return <div>Main Layout Content</div>;
  }
}
```

## 配置选项

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| `interval` | `number` | `30000` | 轮询间隔（毫秒） |
| `checkChangeUrl` | `string` | `'/api/check-changes'` | 检测变化的接口地址 |
| `getPermissionUrl` | `string` | `'/api/get-permissions'` | 获取权限的接口地址 |
| `onPermissionChange` | `() => void` | `() => window.location.reload()` | 权限变化时的回调 |
| `onError` | `(error: any) => void` | `console.error` | 错误处理回调 |

## 接口要求

### 检测变化接口 (`checkChangeUrl`)

接口需要返回包含 `isChange` 字段的 JSON 响应：

```json
{
  "isChange": true,
  "message": "权限已发生变化"
}
```

- `isChange: true` - 检测到变化，将调用权限接口
- `isChange: false` - 无变化，继续轮询

### 权限接口 (`getPermissionUrl`)

获取用户最新权限信息的接口，返回格式不限。

## 注意事项

1. **自动清理**：在 React 组件中使用时，务必在 `useEffect` 的清理函数中调用 `destroy()` 方法
2. **错误处理**：建议配置 `onError` 回调来处理网络错误等异常情况
3. **轮询间隔**：根据业务需求合理设置轮询间隔，避免过于频繁的请求
4. **接口地址**：确保接口地址正确且有相应的权限
5. **请求状态管理**：系统会自动检测上一次请求是否完成，如果还在进行中会跳过本次轮询并在 5 秒后重试

## 示例文件

- `ChangeDetectionPolling.ts` - 核心实现
- `usage-example.tsx` - 使用示例
- `index.ts` - 导出文件
