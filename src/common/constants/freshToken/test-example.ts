//ai生成
import { ChangeDetectionPolling } from './index';

// 测试请求状态管理功能
const testRequestStateManagement = () => {
  const polling = new ChangeDetectionPolling({
    interval: 10000, // 10秒轮询一次
    checkChangeUrl: '/api/test/check-changes',
    getPermissionUrl: '/api/test/get-permissions',
    onPermissionChange: () => {
      console.log('权限发生变化，刷新页面');
      window.location.reload();
    },
    onError: (error) => {
      console.error('轮询出错:', error);
    },
  });

  // 启动轮询
  polling.start();

  // 监控请求状态
  const statusInterval = setInterval(() => {
    console.log('轮询状态:', {
      isActive: polling.isActive(),
      isRequestInProgress: polling.isRequestInProgress(),
    });
  }, 2000);

  // 5分钟后停止测试
  setTimeout(() => {
    polling.stop();
    clearInterval(statusInterval);
    console.log('测试结束');
  }, 5 * 60 * 1000);

  return polling;
};

export default testRequestStateManagement;
// 2024-12-19 zhouby -> cursor ai结尾共生成 35 行代码
