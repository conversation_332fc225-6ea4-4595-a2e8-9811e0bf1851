.pagination-style {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  padding-bottom: 8px;
  :global {
    .pagination-proxy {
      width: unset !important;
    }
    .ant-pagination {
      width: unset !important;
      flex: 1 !important;
      margin-top: 8px;
    }
    .pagination-proxy .ant-pagination-total-text {
      flex: none !important;
    }
    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-jump-prev,
    .ant-pagination-jump-next {
      height: 30px;
      min-width: 30px;
      line-height: 30px;
      min-width: 30px;
      font-size: 12px;
      background: #f0f4f7 !important;
      border: none;
    }
    .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination-next .ant-pagination-item-link {
      background: #f0f4f7 !important;
      border: none;
    }
    .ant-pagination-item {
      height: 30px;
      width: auto;
      line-height: 30px;
      min-width: 30px;
      font-size: 12px;
      background: #f0f4f7;
      border: none;
    }
    .ant-select-selection--single {
      height: 30px;
      font-size: 12px;
      background: #f0f4f7;
      border: none;
    }
    .ant-pagination-options-quick-jumper {
      height: 30px;
      font-size: 12px;
      border: none;
    }
    .ant-pagination-options-quick-jumper input {
      background: #f0f4f7;
      height: 30px;
    }
    .ant-pagination-item-active {
      background-color: #204eff;
      color: #ffffff;
    }
    .ant-pagination-item-active a {
      color: #ffffff !important;
    }
  }
}
